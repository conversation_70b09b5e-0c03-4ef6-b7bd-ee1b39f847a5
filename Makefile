# Usage:
# make protos # Generates Go files for protobuffers
# make protos-lite-jar # Generates Jar for front-end protos and host it on local maven
#
# Src: https://stackoverflow.com/a/10858332
# Check that given variables are set and all have non-empty values,
# die with an error otherwise.
#
# Params:
#   1. Variable name(s) to test.
#   2. (optional) Error message to print.
check_defined = \
    $(strip $(foreach 1,$1, \
        $(call __check_defined,$1,$(strip $(value 2)))))
__check_defined = \
    $(if $(value $1),, \
      $(error Undefined $1$(if $2, ($2))))

UNAME_S := $(shell uname -s)
ifeq ($(UNAME_S),Linux)
	OSFLAG = linux
endif
ifeq ($(UNAME_S),Darwin)
	OSFLAG = osx
endif

# Support M1 macs
UNAME_M := $(shell uname -m)
ifeq ($(UNAME_M),arm64)
	ARCH = aarch_64
else
	ARCH = x86_64
endif

# install path for M1 and intel macs
# Check if the architecture is ARM (Apple Silicon)
UNAME_P := $(shell uname -p)
ifeq ($(UNAME_P),arm)
	INSTALL_PATH = "/opt/homebrew"
else
	INSTALL_PATH = "/usr/local"
endif


# great stuff here for `make help` @ https://gist.github.com/prwhite/8168133
# COLORS for help sections
GREEN  := $(shell tput -Txterm setaf 2)
YELLOW := $(shell tput -Txterm setaf 3)
WHITE  := $(shell tput -Txterm setaf 7)
RESET  := $(shell tput -Txterm sgr0)

TARGET_MAX_CHAR_NUM=20
GOPATH ?= $(shell go env GOPATH)
CURDIR ?= $(shell pwd)
export PATH := $(GOPATH)/bin:$(PATH)
ENVOY_PROTO_VALIDATE_PATH = ${GOPATH}/pkg/mod/github.com/envoyproxy/protoc-gen-validate@v1.0.2
GOOGLE_API_PROTO_PATH = ${GOPATH}/pkg/mod/github.com/googleapis/googleapis@v0.0.0-20230801173531-078f670152ce
GO_PROTO_OUTPUT_ROOT =  ${GOPATH}/src
GAMMA_API_PATH = ${GO_PROTO_OUTPUT_ROOT}/github.com/epifi/gamma/api
BE_COMMON_API_PATH = ${GO_PROTO_OUTPUT_ROOT}/github.com/epifi/be-common/api
GRINGOTT_API_PATH = ${GO_PROTO_OUTPUT_ROOT}/github.com/epifi/gringott/api
WEB_API_PATH = ${GO_PROTO_OUTPUT_ROOT}/github.com/epifi/devdash-ui/client/src

# Python protos implementation
PYTHON_VERSION?=python3.8
PYTHON_ENV_PATH?=$(value VIRTUAL_ENV)
PYTHON_GENERATE_GRPC_FUNCS?=0
PYTHON_PROTOC_GEN_REPO_VERSION?=v1.0.2
PYTHON_PROTOC_GEN_REPO_URL?=https://github.com/bufbuild/protoc-gen-validate
GOOGLE_API_PROTO_PATH_PYTHON?=$(PYTHON_ENV_PATH)/lib/$(PYTHON_VERSION)/site-packages
# Pb2 files would be generated at <below path>/api/
PYTHON_PROTO_OUTPUT_ROOT?=$(value PYTHON_PROTO_OUTPUT_ROOT)
# Path to close protoc-gen-validate Github repository
ENVOY_PROTO_VALIDATE_PATH_PYTHON=$(PYTHON_ENV_PATH)/repos

# Android proto code generation config
EPIFI_ANDROID_PATH ?= $(HOME)/android
GRPC_CODE_MODULE = $(EPIFI_ANDROID_PATH)/core/grpcgeneratedcode
PROTOC_JAVA_OUT_PATH = ${GRPC_CODE_MODULE}/src/main/java/
PROTOC_GRPC_JAVA_PLUGIN = $(CURDIR)/libs/grpc-java
PROTOC_GRPC_JAVA_PLUGIN_VERSION=1.66.0
PROTOC_GRPC_JAVA_PLUGIN_URL = https://repo1.maven.org/maven2/io/grpc/protoc-gen-grpc-java/${PROTOC_GRPC_JAVA_PLUGIN_VERSION}/protoc-gen-grpc-java-${PROTOC_GRPC_JAVA_PLUGIN_VERSION}-${OSFLAG}-${ARCH}.exe

## Show help
help:
	@echo ''
	@echo 'Usage:'
	@echo '  ${YELLOW}make${RESET} ${GREEN}<target>${RESET}'
	@echo ''
	@echo 'Targets:'
	@awk '/^[a-zA-Z\-\_0-9]+:/ { \
		helpMessage = match(lastLine, /^## (.*)/); \
		if (helpMessage) { \
			helpCommand = substr($$1, 0, index($$1, ":")-1); \
			helpMessage = substr(lastLine, RSTART + 3, RLENGTH); \
			printf "  ${YELLOW}%-$(TARGET_MAX_CHAR_NUM)s${RESET} ${GREEN}%s${RESET}\n", helpCommand, helpMessage; \
		} \
	} \
	{ lastLine = $$0 }' $(MAKEFILE_LIST)

# Checks if target is set
target:
	$(call check_defined, target, Build target)

# Checks if name is set
name:
	$(call check_defined, name, Revision name)

## Downloads dependencies
download:
	@echo "Downloading dependencies"
	go mod download

# Compiles the frontend lite protos and creates a JAR using maven script
# Note: This script deletes all the generated code, before generating the code.
compile-protos-java-lite:
	@echo 'EPIFI_ANDROID_PATH env variable needs to be set to android repo location'
	@echo 'PROTOC_JAVA_OUT_PATH=${PROTOC_JAVA_OUT_PATH}'
	wget -c ${PROTOC_GRPC_JAVA_PLUGIN_URL} -O ${PROTOC_GRPC_JAVA_PLUGIN}
	chmod +x ${PROTOC_GRPC_JAVA_PLUGIN}
	rm -rf ${PROTOC_JAVA_OUT_PATH}
	mkdir -p ${PROTOC_JAVA_OUT_PATH}
	find api/frontend/ -iname "*.proto" | xargs -t -L 1 protoc \
		-I . \
		-I ${GOPATH}/pkg/mod/ \
		-I ${ENVOY_PROTO_VALIDATE_PATH} \
		-I ${GOOGLE_API_PROTO_PATH} \
		--plugin=protoc-gen-grpc-java=${PROTOC_GRPC_JAVA_PLUGIN} \
		--java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		--grpc-java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		$1
	find api/types/ -iname "*.proto" | xargs -t -L 1 protoc \
		-I . \
		-I ${GOPATH}/pkg/mod/ \
		-I ${ENVOY_PROTO_VALIDATE_PATH} \
		-I ${GOOGLE_API_PROTO_PATH} \
		--plugin=protoc-gen-grpc-java=${PROTOC_GRPC_JAVA_PLUGIN} \
		--java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		--grpc-java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		$1
	find api/rpc/ -iname "*.proto" | xargs -t -L 1 protoc \
		-I . \
		-I ${GOPATH}/pkg/mod/ \
		-I ${ENVOY_PROTO_VALIDATE_PATH} \
		-I ${GOOGLE_API_PROTO_PATH} \
		--plugin=protoc-gen-grpc-java=${PROTOC_GRPC_JAVA_PLUGIN} \
		--java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		--grpc-java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		$1
	# TODO(pruthvi): Remove after account/account_type is moved into types/
	protoc \
		-I . \
		-I ${GOPATH}/pkg/mod/ \
		-I ${ENVOY_PROTO_VALIDATE_PATH} \
		-I ${GOOGLE_API_PROTO_PATH} \
		--plugin=protoc-gen-grpc-java=${PROTOC_GRPC_JAVA_PLUGIN} \
		--java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		--grpc-java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		api/accounts/account_type.proto
	go mod tidy

# Unlike `protos-lite-jar` which builds the entire frontend package
# here we can pass a path value which will be proto file or a directory
# with proto files and it will generate only those files.
# EG:
# make proto-lite-jar path=api/frontend/deeplink/
# make proto-lite-jar path=api/frontend/deeplink/deeplink.proto
# NOTE: This doesn't handle deletion of proto files as we cannot
# determine dir to delete in the destination dir.
proto-lite-jar: download-protos-dep
	@echo 'EPIFI_ANDROID_PATH env variable needs to be set to android repo location'
	@echo 'PROTOC_JAVA_OUT_PATH=${PROTOC_JAVA_OUT_PATH}'
	@echo 'path = ${path}'
	find $(path) -iname "*.proto" | xargs -t -L 1 protoc \
		-I . \
		-I ${GOPATH}/pkg/mod/ \
		-I ${ENVOY_PROTO_VALIDATE_PATH} \
		-I ${GOOGLE_API_PROTO_PATH} \
		--plugin=protoc-gen-grpc-java=${PROTOC_GRPC_JAVA_PLUGIN} \
		--java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		--grpc-java_out=lite:${PROTOC_JAVA_OUT_PATH} \
		$1
	go mod tidy

download-protos-dep:
	@INSTALL_PATH=${INSTALL_PATH} \
	OSFLAG=${OSFLAG} \
	ARCH=${ARCH} \
        bash ./scripts/install.sh download_proto_deps

download-protos-dep-python:
	@if [[ "$(shell protoc --version)" != "libprotoc 23.4" ]]; then \
		echo "Installing protoc 23.4..."; \
		wget -qO- https://github.com/protocolbuffers/protobuf/releases/download/v23.4/protoc-23.4-${OSFLAG}-${ARCH}.zip | tar xvz -C /tmp -; \
		rm -rf mv $(INSTALL_PATH)/include/google; \
		rm -rf mv $(INSTALL_PATH)/bin/protoc; \
		mv /tmp/bin/protoc $(INSTALL_PATH)/bin; \
		chmod 755 $(INSTALL_PATH)/bin/protoc; \
		mv /tmp/include/google $(INSTALL_PATH)/include; \
		echo "protoc v3.12.3 installed"; \
	fi;
	@echo "Downloading dependencies for proto compilation";
	@if [ ! -d "$(ENVOY_PROTO_VALIDATE_PATH_PYTHON)" ] ; then \
		git clone --branch $(PYTHON_PROTOC_GEN_REPO_VERSION) $(PYTHON_PROTOC_GEN_REPO_URL) $(ENVOY_PROTO_VALIDATE_PATH_PYTHON); \
	else \
		cd "$(ENVOY_PROTO_VALIDATE_PATH_PYTHON)"; \
		git fetch origin refs/tags/$(PYTHON_PROTOC_GEN_REPO_VERSION):refs/tags/$(PYTHON_PROTOC_GEN_REPO_VERSION); \
		git checkout $(PYTHON_PROTOC_GEN_REPO_VERSION); \
	fi; \
	cd $(CURDIR); \
	python3 -m pip install -r python_requirements.txt; \
	brew install protobuf grpc

# compile all protos
# Read more on why we're passing requireUnimplementedServers flag in -go-grpc_out in protoc command:
# https://github.com/grpc/grpc-go/blob/master/cmd/protoc-gen-go-grpc/README.md
compile-protos-go:
	@GO_PROTO_OUTPUT_ROOT=${GO_PROTO_OUTPUT_ROOT} \
   	GOPATH=${GOPATH} \
   	ENVOY_PROTO_VALIDATE_PATH=${ENVOY_PROTO_VALIDATE_PATH} \
   	GOOGLE_API_PROTO_PATH=${GOOGLE_API_PROTO_PATH} \
   	PR_NUMBER=$(PR_NUMBER) \
 	bash ./scripts/compile.sh compile_protos_go ${target}


compile-protos-python:
	@echo "Compiling protobufs."
	# Generates Google API validate proto
	find ${ENVOY_PROTO_VALIDATE_PATH_PYTHON}/validate -iname "*.proto" | xargs -t -L 1 protoc \
	-I . \
	-I $(ENVOY_PROTO_VALIDATE_PATH_PYTHON) \
	-I ${PYTHON_PROTO_OUTPUT_ROOT} \
	-I ${GOOGLE_API_PROTO_PATH_PYTHON} \
	--python_out=${PYTHON_PROTO_OUTPUT_ROOT} \
	`if [ ${PYTHON_GENERATE_GRPC_FUNCS} -eq 1 ]; then echo "--grpclib_python_out=${PYTHON_PROTO_OUTPUT_ROOT}"; fi` \
	$1
	# Generates pb2 files based on protos repo
	find . -iname "*.proto" | grep -E "api/${target}"  | xargs -t -L 1 protoc \
	-I . \
	-I $(ENVOY_PROTO_VALIDATE_PATH_PYTHON) \
	-I ${PYTHON_PROTO_OUTPUT_ROOT} \
	-I ${GOOGLE_API_PROTO_PATH_PYTHON} \
	--python_out=${PYTHON_PROTO_OUTPUT_ROOT} \
	`if [ ${PYTHON_GENERATE_GRPC_FUNCS} -eq 1 ]; then echo "--grpclib_python_out=${PYTHON_PROTO_OUTPUT_ROOT}"; fi` \
	$1

# generates mocks for service proto-definitions within respective api/ folders of gamma
generate-service-mocks-go:
	@GAMMA_API_PATH=${GAMMA_API_PATH} \
    GOPATH=${GOPATH} \
    PR_NUMBER=$(PR_NUMBER) \
    GO_PROTO_OUTPUT_ROOT=${GO_PROTO_OUTPUT_ROOT} \
    bash ./scripts/compile.sh generate_service_mocks_go ${target}

install-gen-queue-pb:
	export GOPRIVATE=github.com/epifi/*
	go install github.com/epifi/gamma/pkg/generator/gen_queue_pb@latest

gen-queue-pb:
	@if [ -d "${GAMMA_API_PATH}/${target}" ]; then \
		find ${GAMMA_API_PATH}/${target} -type d | xargs -t -L 1 -I {}\
		gen_queue_pb -dir={};\
  	else  gen_queue_pb -dir=${GAMMA_API_PATH}/`dirname ${target}` ;\
	fi;

# Downloads the ts-proto which is dependency for ts proto compilation
download-protos-dep-ts:
	npm list | grep ts-proto@1.144.1 || npm install ts-proto@1.144.1

# Downloads the js-proto which is dependency for ts proto compilation
download-protos-dep-js:
	npm install | brew list protoc-gen-grpc-web || brew install protoc-gen-grpc-web

# Compiles proto to typescript files
ts-proto-compile:
	@echo "Compiling protobufs."
	find . -iname "*.proto" | grep ./api/${target} | xargs -t -L 1 protoc \
		-I . \
		-I ${GO_PROTO_OUTPUT_ROOT} \
		-I ${GOPATH}/pkg/mod/ \
		-I ${ENVOY_PROTO_VALIDATE_PATH} \
		-I ${GOOGLE_API_PROTO_PATH} \
		--plugin=./node_modules/.bin/protoc-gen-ts_proto \
		--ts_proto_out=${WEB_API_PATH}/tsapi \
		--ts_proto_opt=outputClientImpl=grpc-web \
		--ts_proto_opt=esModuleInterop=true \
		--ts_proto_opt=env=browser \
		$1

# Compiles proto to javascript files
js-proto-compile:
	@echo "Compiling protobufs."
	find . -iname "*.proto" | grep /api/${target} | xargs -t -L 1 protoc \
		-I .\
		-I ${GO_PROTO_OUTPUT_ROOT} \
		-I ${GOPATH}/pkg/mod/ \
		-I ${ENVOY_PROTO_VALIDATE_PATH} \
		-I ${GOOGLE_API_PROTO_PATH} \
		--js_out=import_style=commonjs:${WEB_API_PATH} \
        --grpc-web_out=import_style=commonjs,mode=grpcweb:${WEB_API_PATH} \
		$1

# Compiles proto to json files
json-proto-compile:
	@echo "Compiling protobufs"
	@mkdir -p ${output_path}
	find . -iname "*.proto" | grep /api/${target} | xargs -t -L 1 protoc \
		-I . \
		-I ${GOPATH}/pkg/mod/ \
		-I ${ENVOY_PROTO_VALIDATE_PATH} \
		-I ${GOOGLE_API_PROTO_PATH} \
		--jsonschema_out=${output_path} \
		--jsonschema_opt=prefix_schema_files_with_package \
		--jsonschema_opt=all_fields_required \
		--jsonschema_opt=enums_as_strings_only \
		--proto_path=$1

# Generates proto schema in json files
json-proto-schema-gen:
	@echo "Compiling protobufs to JavaScript"
	@mkdir -p ${output_path}
	@PROTO_FILES=$$(find . -iname "*.proto" | grep /api/${target}); \
	if [ -n "$$PROTO_FILES" ]; then \
		echo "Found .proto files: $$PROTO_FILES"; \
		node ./scripts/proto_json_compiler/compile_proto_json.js ${output_path} $$PROTO_FILES; \
	else \
		echo "No .proto files found for target /api/${target}"; \
	fi

## download protos dependency and upgrade google libs (if required)
setup-tools: download-protos-dep upgrade-google-libs install-buf-plugins

## Compile protos for TS Eg: make protos-ts [target=service]
protos-ts: download-protos-dep-ts ts-proto-compile

## Compile protos for JS Eg: make protos-js [target=service]
protos-js: download-protos-dep-js js-proto-compile

## Compile protos for Go Ex: make protos [target=service]
protos: compile-protos-go generate-service-mocks-go

## Verifi service autogen is required in both repos gamma and gringott, manually we modify the go_package to point to the respective repo's autogen directory
## for the entire file tree of the verifi service, below command automates this.
## Usage instructions:
## Use make protos target to generate autogen in gamma repo
## Use make verifi-protos to generate autogen in gringott repo for all verifi services (including typesv2 files if required)
verifi-protos:
	time bash ./scripts/gen_verifi_protos.sh gen_verifi_protos

protos-python: download-protos-dep-python compile-protos-python

## Compiles lite protos & creates JAR in Android lib path
protos-lite-jar: download-protos-dep compile-protos-java-lite

## Deletes binary files generated by protoc
clean:
	find api/ -name '*.java' -delete
	find api/ -name '*.pb.validate.go' -delete
	find api/ -name '*.pb.go' -delete
	cd ${GRPC_CODE_MODULE} && rm -rf src/main/java/

## Run go clean and purge all caches
goclean:
	go clean -i -r -cache -testcache -modcache -x

run-gen-queue: install-gen-queue-pb gen-queue-pb

# if the file in include/google lib has go package stating with "github.com/golang/protobuf/ptypes" this rule will overwrite
# the libs in favour of new google.golang.org/protobuf/types libs
upgrade-google-libs:
	@INSTALL_PATH=${INSTALL_PATH} \
	OSFLAG=${OSFLAG} \
	ARCH=${ARCH} \
		bash ./scripts/install.sh upgrade_go_libs

# This path needs to the be set on the Machine, to point to the https://github.com/epiFi/qa-automation source code
# location. By default it points to the Home folder + Documents/projects/qa-automation
EPIFI_QA_AUTOMATION_PATH ?= $HOME/Documents/projects/qa-automation

# Compile protos for accessing CX Dev actions/rpcs in development envs. For example, to call scripts/db state changes
# from Mobile automation suite
protos-cx-dev-action:
	bash scripts/compile_cx_dev_action_protos.sh \
		${EPIFI_QA_AUTOMATION_PATH} \
		${PROTOC_GRPC_JAVA_PLUGIN} \
		${PROTOC_GRPC_JAVA_PLUGIN_URL} \
		${ENVOY_PROTO_VALIDATE_PATH} \
		${GOOGLE_API_PROTO_PATH}

download-protos-dep-java:
	@echo "Downloading dependencies for proto compilation";
	wget -c ${PROTOC_GRPC_JAVA_PLUGIN_URL} -O ${PROTOC_GRPC_JAVA_PLUGIN}
	chmod +x ${PROTOC_GRPC_JAVA_PLUGIN}

# Install Buf and Custom plugins for Buf (https://buf.build/docs/installation/)
install-buf-plugins:
	@echo "Installing buf plugins"
	@INSTALL_PATH=${INSTALL_PATH} \
	bash ./scripts/install.sh install_buf

# Run buf lint tool (TODO: on typesv2/common for now, we will add more rules across protos)
run-lint:
	buf lint --path api/typesv2/common
