Go memory stats:
{
  "Alloc": 752111960,
  "TotalAlloc": 25291115768,
  "Sys": 1165831720,
  "Lookups": 0,
  "Mallocs": 229756880,
  "Frees": 228238420,
  "HeapAlloc": 752111960,
  "HeapSys": 1137565696,
  "HeapIdle": 348561408,
  "HeapInuse": 789004288,
  "HeapReleased": 253485056,
  "HeapObjects": 1518460,
  "StackInuse": 7405568,
  "StackSys": 7405568,
  "MSpanInuse": 3756000,
  "MSpanSys": 6348480,
  "MCacheInuse": 14400,
  "MCacheSys": 15600,
  "BuckHashSys": 3600013,
  "GCSys": 8586792,
  "OtherSys": 2309571,
  "NextGC": 2131488240,
  "LastGC": 1756989249163019000,
  "PauseTotalNs": 283335447,
  "PauseNs": [
    21582,
    49084,
    161458,
    67458,
    75500,
    207541,
    1427542,
    2175333,
    1537083,
    1916250,
    16870917,
    32824166,
    2099208,
    658541,
    756916,
    1798542,
    938375,
    756209,
    890750,
    1179209,
    1225292,
    347916,
    900293,
    538916,
    325333,
    457542,
    3002584,
    3334500,
    622125,
    554917,
    2701125,
    571251,
    1547250,
    1110208,
    446292,
    2118041,
    1136374,
    1405500,
    1199291,
    463291,
    981250,
    839833,
    601042,
    882958,
    795999,
    1031958,
    644708,
    933083,
    380750,
    503291,
    401999,
    800416,
    10221916,
    429416,
    70793916,
    38716625,
    24391000,
    474666,
    467792,
    5060626,
    1190667,
    1505667,
    902375,
    613083,
    1072583,
    1197167,
    2398250,
    720000,
    839125,
    1946207,
    633459,
    371001,
    605291,
    319333,
    1729375,
    2099667,
    1274458,
    1362458,
    1208542,
    1564917,
    1623000,
    837875,
    1543084,
    746707,
    1149249,
    1289209,
    1756000,
    1063749,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "PauseEnd": [
    1756979782614609000,
    1756979782622539000,
    1756979782646079000,
    1756979782662468000,
    1756979782689114000,
    1756979782709676000,
    1756979808994607000,
    1756979844051380000,
    1756979846205346000,
    1756979932984645000,
    1756980053291751000,
    1756980173949548000,
    1756980294040691000,
    1756980363049313000,
    1756980483133129000,
    1756980603273982000,
    1756980723323779000,
    1756980843429732000,
    1756980963464862000,
    1756981083545151000,
    1756981203677170000,
    1756981323726632000,
    1756981443790842000,
    1756981563816445000,
    1756981683842162000,
    1756981803938889000,
    1756981924053526000,
    1756982044222590000,
    1756982164266256000,
    1756982284322140000,
    1756982404424010000,
    1756982524544809000,
    1756982644685004000,
    1756982764746764000,
    1756982884820317000,
    1756983004885715000,
    1756983124933262000,
    1756983244992639000,
    1756983365043206000,
    1756983485153747000,
    1756983605186472000,
    1756983725206623000,
    1756983845243173000,
    1756983965318854000,
    1756984085357751000,
    1756984205432527000,
    1756984325452738000,
    1756984445517583000,
    1756984565562120000,
    1756984685607839000,
    1756984805658150000,
    1756984925728123000,
    1756985045876787000,
    1756985165939135000,
    1756985286392324000,
    1756985407022247000,
    1756985527223535000,
    1756985647340064000,
    1756985767405623000,
    1756985887571902000,
    1756986007653959000,
    1756986127701848000,
    1756986247740126000,
    1756986367822435000,
    1756986487882566000,
    1756986607944929000,
    1756986728001066000,
    1756986848039130000,
    1756986968075188000,
    1756987088141613000,
    1756987208179371000,
    1756987328234182000,
    1756987448286544000,
    1756987568333930000,
    1756987688384608000,
    1756987808451841000,
    1756987928490272000,
    1756988048546917000,
    1756988168583165000,
    1756988288644623000,
    1756988408728396000,
    1756988528753541000,
    1756988648827422000,
    1756988768855102000,
    1756988888894459000,
    1756989008946870000,
    1756989129062016000,
    1756989249163019000,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "NumGC": 88,
  "NumForcedGC": 0,
  "GCCPUFraction": 0.00015365253236649366,
  "EnableGC": true,
  "DebugGC": false,
  "BySize": [
    {
      "Size": 0,
      "Mallocs": 0,
      "Frees": 0
    },
    {
      "Size": 8,
      "Mallocs": 3862943,
      "Frees": 3835126
    },
    {
      "Size": 16,
      "Mallocs": 66235268,
      "Frees": 65590371
    },
    {
      "Size": 24,
      "Mallocs": 4594548,
      "Frees": 4551774
    },
    {
      "Size": 32,
      "Mallocs": 8017008,
      "Frees": 7951947
    },
    {
      "Size": 48,
      "Mallocs": 16898215,
      "Frees": 16758386
    },
    {
      "Size": 64,
      "Mallocs": 47738914,
      "Frees": 47429858
    },
    {
      "Size": 80,
      "Mallocs": 2896530,
      "Frees": 2873615
    },
    {
      "Size": 96,
      "Mallocs": 5750839,
      "Frees": 5705144
    },
    {
      "Size": 112,
      "Mallocs": 4996171,
      "Frees": 4958375
    },
    {
      "Size": 128,
      "Mallocs": 1669991,
      "Frees": 1655432
    },
    {
      "Size": 144,
      "Mallocs": 512379,
      "Frees": 506220
    },
    {
      "Size": 160,
      "Mallocs": 1475117,
      "Frees": 1458886
    },
    {
      "Size": 176,
      "Mallocs": 362282,
      "Frees": 356940
    },
    {
      "Size": 192,
      "Mallocs": 756266,
      "Frees": 744178
    },
    {
      "Size": 208,
      "Mallocs": 388542,
      "Frees": 383174
    },
    {
      "Size": 224,
      "Mallocs": 352587,
      "Frees": 349448
    },
    {
      "Size": 240,
      "Mallocs": 6496703,
      "Frees": 6454725
    },
    {
      "Size": 256,
      "Mallocs": 1082113,
      "Frees": 1070939
    },
    {
      "Size": 288,
      "Mallocs": 787987,
      "Frees": 777999
    },
    {
      "Size": 320,
      "Mallocs": 366970,
      "Frees": 364001
    },
    {
      "Size": 352,
      "Mallocs": 191197,
      "Frees": 189113
    },
    {
      "Size": 384,
      "Mallocs": 138800,
      "Frees": 136984
    },
    {
      "Size": 416,
      "Mallocs": 82153,
      "Frees": 81132
    },
    {
      "Size": 448,
      "Mallocs": 76521,
      "Frees": 73085
    },
    {
      "Size": 480,
      "Mallocs": 946095,
      "Frees": 938905
    },
    {
      "Size": 512,
      "Mallocs": 113734,
      "Frees": 112695
    },
    {
      "Size": 576,
      "Mallocs": 223066,
      "Frees": 218100
    },
    {
      "Size": 640,
      "Mallocs": 75409,
      "Frees": 74871
    },
    {
      "Size": 704,
      "Mallocs": 344516,
      "Frees": 341392
    },
    {
      "Size": 768,
      "Mallocs": 169518,
      "Frees": 167710
    },
    {
      "Size": 896,
      "Mallocs": 194825,
      "Frees": 192313
    },
    {
      "Size": 1024,
      "Mallocs": 109549,
      "Frees": 108013
    },
    {
      "Size": 1152,
      "Mallocs": 140872,
      "Frees": 139280
    },
    {
      "Size": 1280,
      "Mallocs": 27099,
      "Frees": 26747
    },
    {
      "Size": 1408,
      "Mallocs": 371627,
      "Frees": 367792
    },
    {
      "Size": 1536,
      "Mallocs": 80807,
      "Frees": 80190
    },
    {
      "Size": 1792,
      "Mallocs": 70839,
      "Frees": 69959
    },
    {
      "Size": 2048,
      "Mallocs": 39845,
      "Frees": 36216
    },
    {
      "Size": 2304,
      "Mallocs": 59008,
      "Frees": 58573
    },
    {
      "Size": 2688,
      "Mallocs": 45745,
      "Frees": 45321
    },
    {
      "Size": 3072,
      "Mallocs": 16061,
      "Frees": 15229
    },
    {
      "Size": 3200,
      "Mallocs": 7000,
      "Frees": 6973
    },
    {
      "Size": 3456,
      "Mallocs": 4778,
      "Frees": 4698
    },
    {
      "Size": 4096,
      "Mallocs": 78079,
      "Frees": 70726
    },
    {
      "Size": 4864,
      "Mallocs": 59427,
      "Frees": 58983
    },
    {
      "Size": 5376,
      "Mallocs": 36479,
      "Frees": 36072
    },
    {
      "Size": 6144,
      "Mallocs": 2770,
      "Frees": 2646
    },
    {
      "Size": 6528,
      "Mallocs": 831,
      "Frees": 808
    },
    {
      "Size": 6784,
      "Mallocs": 212,
      "Frees": 200
    },
    {
      "Size": 6912,
      "Mallocs": 355,
      "Frees": 345
    },
    {
      "Size": 8192,
      "Mallocs": 31727,
      "Frees": 31289
    },
    {
      "Size": 9472,
      "Mallocs": 3177,
      "Frees": 3126
    },
    {
      "Size": 9728,
      "Mallocs": 1926,
      "Frees": 1911
    },
    {
      "Size": 10240,
      "Mallocs": 119,
      "Frees": 105
    },
    {
      "Size": 10880,
      "Mallocs": 102,
      "Frees": 94
    },
    {
      "Size": 12288,
      "Mallocs": 357,
      "Frees": 327
    },
    {
      "Size": 13568,
      "Mallocs": 738,
      "Frees": 715
    },
    {
      "Size": 14336,
      "Mallocs": 1339,
      "Frees": 1319
    },
    {
      "Size": 16384,
      "Mallocs": 2636,
      "Frees": 2569
    },
    {
      "Size": 18432,
      "Mallocs": 1178,
      "Frees": 1147
    }
  ]
}
----
Non-Go stats:
{
  "CGoAllocatedBytes": 138045584,
  "CGoTotalBytes": 174325760
}
