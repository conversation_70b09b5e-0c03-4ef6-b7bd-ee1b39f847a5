Go memory stats:
{
  "Alloc": 807723976,
  "TotalAlloc": 39952156480,
  "Sys": 1167439400,
  "Lookups": 0,
  "Mallocs": 374214839,
  "Frees": 371961866,
  "HeapAlloc": 807723976,
  "HeapSys": 1137229824,
  "HeapIdle": 313696256,
  "HeapInuse": 823533568,
  "HeapReleased": 183902208,
  "HeapObjects": 2252973,
  "StackInuse": 7700480,
  "StackSys": 7700480,
  "MSpanInuse": 4229600,
  "MSpanSys": 6887040,
  "MCacheInuse": 14400,
  "MCacheSys": 15600,
  "BuckHashSys": 4158477,
  "GCSys": 8810240,
  "OtherSys": 2637739,
  "NextGC": 2219888720,
  "LastGC": 1756995372543103000,
  "PauseTotalNs": 349107196,
  "PauseNs": [
    21582,
    49084,
    161458,
    67458,
    75500,
    207541,
    1427542,
    2175333,
    1537083,
    1916250,
    16870917,
    32824166,
    2099208,
    658541,
    756916,
    1798542,
    938375,
    756209,
    890750,
    1179209,
    1225292,
    347916,
    900293,
    538916,
    325333,
    457542,
    3002584,
    3334500,
    622125,
    554917,
    2701125,
    571251,
    1547250,
    1110208,
    446292,
    2118041,
    1136374,
    1405500,
    1199291,
    463291,
    981250,
    839833,
    601042,
    882958,
    795999,
    1031958,
    644708,
    933083,
    380750,
    503291,
    401999,
    800416,
    10221916,
    429416,
    70793916,
    38716625,
    24391000,
    474666,
    467792,
    5060626,
    1190667,
    1505667,
    902375,
    613083,
    1072583,
    1197167,
    2398250,
    720000,
    839125,
    1946207,
    633459,
    371001,
    605291,
    319333,
    1729375,
    2099667,
    1274458,
    1362458,
    1208542,
    1564917,
    1623000,
    837875,
    1543084,
    746707,
    1149249,
    1289209,
    1756000,
    1063749,
    824542,
    910667,
    1360500,
    1518999,
    1555791,
    1088291,
    910375,
    1107124,
    1054500,
    1282750,
    1373416,
    1254375,
    1329833,
    712042,
    1081833,
    1514125,
    248209,
    1271167,
    1243876,
    1137000,
    881500,
    3670292,
    1285708,
    1757750,
    793541,
    1079666,
    576167,
    1552792,
    1292708,
    1450125,
    1176334,
    1422542,
    735291,
    953707,
    1102500,
    919125,
    726750,
    1418333,
    1541167,
    1393501,
    1804542,
    2467417,
    1806167,
    1317874,
    865042,
    1008250,
    1943959,
    1495001,
    785500,
    904125,
    2864958,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "PauseEnd": [
    1756979782614609000,
    1756979782622539000,
    1756979782646079000,
    1756979782662468000,
    1756979782689114000,
    1756979782709676000,
    1756979808994607000,
    1756979844051380000,
    1756979846205346000,
    1756979932984645000,
    1756980053291751000,
    1756980173949548000,
    1756980294040691000,
    1756980363049313000,
    1756980483133129000,
    1756980603273982000,
    1756980723323779000,
    1756980843429732000,
    1756980963464862000,
    1756981083545151000,
    1756981203677170000,
    1756981323726632000,
    1756981443790842000,
    1756981563816445000,
    1756981683842162000,
    1756981803938889000,
    1756981924053526000,
    1756982044222590000,
    1756982164266256000,
    1756982284322140000,
    1756982404424010000,
    1756982524544809000,
    1756982644685004000,
    1756982764746764000,
    1756982884820317000,
    1756983004885715000,
    1756983124933262000,
    1756983244992639000,
    1756983365043206000,
    1756983485153747000,
    1756983605186472000,
    1756983725206623000,
    1756983845243173000,
    1756983965318854000,
    1756984085357751000,
    1756984205432527000,
    1756984325452738000,
    1756984445517583000,
    1756984565562120000,
    1756984685607839000,
    1756984805658150000,
    1756984925728123000,
    1756985045876787000,
    1756985165939135000,
    1756985286392324000,
    1756985407022247000,
    1756985527223535000,
    1756985647340064000,
    1756985767405623000,
    1756985887571902000,
    1756986007653959000,
    1756986127701848000,
    1756986247740126000,
    1756986367822435000,
    1756986487882566000,
    1756986607944929000,
    1756986728001066000,
    1756986848039130000,
    1756986968075188000,
    1756987088141613000,
    1756987208179371000,
    1756987328234182000,
    1756987448286544000,
    1756987568333930000,
    1756987688384608000,
    1756987808451841000,
    1756987928490272000,
    1756988048546917000,
    1756988168583165000,
    1756988288644623000,
    1756988408728396000,
    1756988528753541000,
    1756988648827422000,
    1756988768855102000,
    1756988888894459000,
    1756989008946870000,
    1756989129062016000,
    1756989249163019000,
    1756989369216341000,
    1756989489287851000,
    1756989609336830000,
    1756989729433213000,
    1756989849472580000,
    1756989969539989000,
    1756990089581059000,
    1756990209647574000,
    1756990329724524000,
    1756990449756347000,
    1756990569836621000,
    1756990689888007000,
    1756990809959909000,
    1756990930065125000,
    1756991050097723000,
    1756991170133434000,
    1756991290216689000,
    1756991410269554000,
    1756991530341939000,
    1756991650409488000,
    1756991770472586000,
    1756991890558071000,
    1756992010654542000,
    1756992130717106000,
    1756992250775593000,
    1756992370847053000,
    1756992490906706000,
    1756992610996238000,
    1756992731034230000,
    1756992851116724000,
    1756992971185029000,
    1756993091259032000,
    1756993211358871000,
    1756993331459309000,
    1756993451495168000,
    1756993571545193000,
    1756993691604676000,
    1756993811655404000,
    1756993931717109000,
    1756994051783699000,
    1756994171856860000,
    1756994291892723000,
    1756994411936079000,
    1756994532008501000,
    1756994652082658000,
    1756994772136970000,
    1756994892213763000,
    1756995012295835000,
    1756995132358201000,
    1756995252436405000,
    1756995372543103000,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "NumGC": 139,
  "NumForcedGC": 0,
  "GCCPUFraction": 0.00012909738439997122,
  "EnableGC": true,
  "DebugGC": false,
  "BySize": [
    {
      "Size": 0,
      "Mallocs": 0,
      "Frees": 0
    },
    {
      "Size": 8,
      "Mallocs": 6291239,
      "Frees": 6246365
    },
    {
      "Size": 16,
      "Mallocs": 108294524,
      "Frees": 107551211
    },
    {
      "Size": 24,
      "Mallocs": 7157109,
      "Frees": 7057312
    },
    {
      "Size": 32,
      "Mallocs": 12916080,
      "Frees": 12768464
    },
    {
      "Size": 48,
      "Mallocs": 27354798,
      "Frees": 27126965
    },
    {
      "Size": 64,
      "Mallocs": 78319210,
      "Frees": 77814800
    },
    {
      "Size": 80,
      "Mallocs": 4540401,
      "Frees": 4479332
    },
    {
      "Size": 96,
      "Mallocs": 9234931,
      "Frees": 9163231
    },
    {
      "Size": 112,
      "Mallocs": 8135885,
      "Frees": 8076330
    },
    {
      "Size": 128,
      "Mallocs": 2648644,
      "Frees": 2622437
    },
    {
      "Size": 144,
      "Mallocs": 778143,
      "Frees": 766628
    },
    {
      "Size": 160,
      "Mallocs": 2306186,
      "Frees": 2276787
    },
    {
      "Size": 176,
      "Mallocs": 535798,
      "Frees": 525329
    },
    {
      "Size": 192,
      "Mallocs": 1165069,
      "Frees": 1141351
    },
    {
      "Size": 208,
      "Mallocs": 577495,
      "Frees": 569181
    },
    {
      "Size": 224,
      "Mallocs": 493232,
      "Frees": 488735
    },
    {
      "Size": 240,
      "Mallocs": 10672967,
      "Frees": 10609855
    },
    {
      "Size": 256,
      "Mallocs": 1733912,
      "Frees": 1715289
    },
    {
      "Size": 288,
      "Mallocs": 1213769,
      "Frees": 1200326
    },
    {
      "Size": 320,
      "Mallocs": 550343,
      "Frees": 542431
    },
    {
      "Size": 352,
      "Mallocs": 293367,
      "Frees": 289439
    },
    {
      "Size": 384,
      "Mallocs": 208162,
      "Frees": 204912
    },
    {
      "Size": 416,
      "Mallocs": 123977,
      "Frees": 121084
    },
    {
      "Size": 448,
      "Mallocs": 118834,
      "Frees": 114252
    },
    {
      "Size": 480,
      "Mallocs": 1527853,
      "Frees": 1515008
    },
    {
      "Size": 512,
      "Mallocs": 172753,
      "Frees": 170285
    },
    {
      "Size": 576,
      "Mallocs": 358388,
      "Frees": 355323
    },
    {
      "Size": 640,
      "Mallocs": 112712,
      "Frees": 110548
    },
    {
      "Size": 704,
      "Mallocs": 548453,
      "Frees": 543568
    },
    {
      "Size": 768,
      "Mallocs": 250241,
      "Frees": 246115
    },
    {
      "Size": 896,
      "Mallocs": 291944,
      "Frees": 286727
    },
    {
      "Size": 1024,
      "Mallocs": 166579,
      "Frees": 164062
    },
    {
      "Size": 1152,
      "Mallocs": 206630,
      "Frees": 203033
    },
    {
      "Size": 1280,
      "Mallocs": 40988,
      "Frees": 40046
    },
    {
      "Size": 1408,
      "Mallocs": 607989,
      "Frees": 602608
    },
    {
      "Size": 1536,
      "Mallocs": 120743,
      "Frees": 119255
    },
    {
      "Size": 1792,
      "Mallocs": 103721,
      "Frees": 101739
    },
    {
      "Size": 2048,
      "Mallocs": 60145,
      "Frees": 59244
    },
    {
      "Size": 2304,
      "Mallocs": 89918,
      "Frees": 87984
    },
    {
      "Size": 2688,
      "Mallocs": 72074,
      "Frees": 70875
    },
    {
      "Size": 3072,
      "Mallocs": 29690,
      "Frees": 28529
    },
    {
      "Size": 3200,
      "Mallocs": 9193,
      "Frees": 8962
    },
    {
      "Size": 3456,
      "Mallocs": 9925,
      "Frees": 9770
    },
    {
      "Size": 4096,
      "Mallocs": 210575,
      "Frees": 209621
    },
    {
      "Size": 4864,
      "Mallocs": 96542,
      "Frees": 95815
    },
    {
      "Size": 5376,
      "Mallocs": 55630,
      "Frees": 54930
    },
    {
      "Size": 6144,
      "Mallocs": 4292,
      "Frees": 4145
    },
    {
      "Size": 6528,
      "Mallocs": 1131,
      "Frees": 1100
    },
    {
      "Size": 6784,
      "Mallocs": 323,
      "Frees": 318
    },
    {
      "Size": 6912,
      "Mallocs": 606,
      "Frees": 596
    },
    {
      "Size": 8192,
      "Mallocs": 48688,
      "Frees": 47854
    },
    {
      "Size": 9472,
      "Mallocs": 5178,
      "Frees": 5048
    },
    {
      "Size": 9728,
      "Mallocs": 3502,
      "Frees": 3468
    },
    {
      "Size": 10240,
      "Mallocs": 181,
      "Frees": 171
    },
    {
      "Size": 10880,
      "Mallocs": 302,
      "Frees": 282
    },
    {
      "Size": 12288,
      "Mallocs": 601,
      "Frees": 574
    },
    {
      "Size": 13568,
      "Mallocs": 992,
      "Frees": 964
    },
    {
      "Size": 14336,
      "Mallocs": 2168,
      "Frees": 2144
    },
    {
      "Size": 16384,
      "Mallocs": 4728,
      "Frees": 4615
    },
    {
      "Size": 18432,
      "Mallocs": 2002,
      "Frees": 1982
    }
  ]
}
----
Non-Go stats:
{
  "CGoAllocatedBytes": 138429120,
  "CGoTotalBytes": 172720128
}
