Go memory stats:
{
  "Alloc": 597476480,
  "TotalAlloc": 73288888552,
  "Sys": 1654704776,
  "Lookups": 0,
  "Mallocs": 668638756,
  "Frees": 668334898,
  "HeapAlloc": 597476480,
  "HeapSys": 1613185024,
  "HeapIdle": 918863872,
  "HeapInuse": 694321152,
  "HeapReleased": 396050432,
  "HeapObjects": 303858,
  "StackInuse": 5734400,
  "StackSys": 5734400,
  "MSpanInuse": 2672640,
  "MSpanSys": 14736960,
  "MCacheInuse": 14400,
  "MCacheSys": 15600,
  "BuckHashSys": 5532933,
  "GCSys": 12356112,
  "OtherSys": 3143747,
  "NextGC": 2397378480,
  "LastGC": 1757272777529918000,
  "PauseTotalNs": 472415861,
  "PauseNs": [
    21582,
    49084,
    161458,
    67458,
    75500,
    207541,
    1427542,
    2175333,
    1537083,
    1916250,
    16870917,
    32824166,
    2099208,
    658541,
    756916,
    1798542,
    938375,
    756209,
    890750,
    1179209,
    1225292,
    347916,
    900293,
    538916,
    325333,
    457542,
    3002584,
    3334500,
    622125,
    554917,
    2701125,
    571251,
    1547250,
    1110208,
    446292,
    2118041,
    1136374,
    1405500,
    1199291,
    463291,
    981250,
    839833,
    601042,
    882958,
    795999,
    1031958,
    644708,
    933083,
    380750,
    503291,
    401999,
    800416,
    10221916,
    429416,
    70793916,
    38716625,
    24391000,
    474666,
    467792,
    5060626,
    1190667,
    1505667,
    902375,
    613083,
    1072583,
    1197167,
    2398250,
    720000,
    839125,
    1946207,
    633459,
    371001,
    605291,
    319333,
    1729375,
    2099667,
    1274458,
    1362458,
    1208542,
    1564917,
    1623000,
    837875,
    1543084,
    746707,
    1149249,
    1289209,
    1756000,
    1063749,
    824542,
    910667,
    1360500,
    1518999,
    1555791,
    1088291,
    910375,
    1107124,
    1054500,
    1282750,
    1373416,
    1254375,
    1329833,
    712042,
    1081833,
    1514125,
    248209,
    1271167,
    1243876,
    1137000,
    881500,
    3670292,
    1285708,
    1757750,
    793541,
    1079666,
    576167,
    1552792,
    1292708,
    1450125,
    1176334,
    1422542,
    735291,
    953707,
    1102500,
    919125,
    726750,
    1418333,
    1541167,
    1393501,
    1804542,
    2467417,
    1806167,
    1317874,
    865042,
    1008250,
    1943959,
    1495001,
    785500,
    904125,
    2864958,
    1467750,
    1687751,
    1476126,
    1757499,
    1231667,
    1245583,
    649875,
    1779376,
    1237458,
    774459,
    1972707,
    572208,
    852499,
    1392125,
    735374,
    2019208,
    1521041,
    838792,
    1043917,
    8283791,
    2133083,
    1736875,
    1775708,
    858751,
    673084,
    2632625,
    774416,
    2248166,
    1349875,
    986458,
    1934250,
    1483876,
    1325416,
    715124,
    2067834,
    634750,
    2209875,
    1369959,
    1196959,
    851000,
    915166,
    749125,
    1308000,
    1109125,
    1412166,
    1209417,
    709958,
    1151959,
    847583,
    618208,
    1029417,
    1308792,
    1106750,
    1136917,
    718541,
    997250,
    1407958,
    1457043,
    1077458,
    2001125,
    950334,
    1653834,
    774709,
    918876,
    1234042,
    1428376,
    604166,
    1197917,
    757750,
    1385666,
    764750,
    1642042,
    724917,
    730125,
    1442083,
    1459918,
    1272792,
    1370083,
    1180749,
    1273834,
    1212999,
    1185750,
    1153041,
    697291,
    1148500,
    1801459,
    1047750,
    888666,
    1090375,
    1077041,
    649707,
    904458,
    860500,
    900916,
    600584,
    1553417,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "PauseEnd": [
    1756979782614609000,
    1756979782622539000,
    1756979782646079000,
    1756979782662468000,
    1756979782689114000,
    1756979782709676000,
    1756979808994607000,
    1756979844051380000,
    1756979846205346000,
    1756979932984645000,
    1756980053291751000,
    1756980173949548000,
    1756980294040691000,
    1756980363049313000,
    1756980483133129000,
    1756980603273982000,
    1756980723323779000,
    1756980843429732000,
    1756980963464862000,
    1756981083545151000,
    1756981203677170000,
    1756981323726632000,
    1756981443790842000,
    1756981563816445000,
    1756981683842162000,
    1756981803938889000,
    1756981924053526000,
    1756982044222590000,
    1756982164266256000,
    1756982284322140000,
    1756982404424010000,
    1756982524544809000,
    1756982644685004000,
    1756982764746764000,
    1756982884820317000,
    1756983004885715000,
    1756983124933262000,
    1756983244992639000,
    1756983365043206000,
    1756983485153747000,
    1756983605186472000,
    1756983725206623000,
    1756983845243173000,
    1756983965318854000,
    1756984085357751000,
    1756984205432527000,
    1756984325452738000,
    1756984445517583000,
    1756984565562120000,
    1756984685607839000,
    1756984805658150000,
    1756984925728123000,
    1756985045876787000,
    1756985165939135000,
    1756985286392324000,
    1756985407022247000,
    1756985527223535000,
    1756985647340064000,
    1756985767405623000,
    1756985887571902000,
    1756986007653959000,
    1756986127701848000,
    1756986247740126000,
    1756986367822435000,
    1756986487882566000,
    1756986607944929000,
    1756986728001066000,
    1756986848039130000,
    1756986968075188000,
    1756987088141613000,
    1756987208179371000,
    1756987328234182000,
    1756987448286544000,
    1756987568333930000,
    1756987688384608000,
    1756987808451841000,
    1756987928490272000,
    1756988048546917000,
    1756988168583165000,
    1756988288644623000,
    1756988408728396000,
    1756988528753541000,
    1756988648827422000,
    1756988768855102000,
    1756988888894459000,
    1756989008946870000,
    1756989129062016000,
    1756989249163019000,
    1756989369216341000,
    1756989489287851000,
    1756989609336830000,
    1756989729433213000,
    1756989849472580000,
    1756989969539989000,
    1756990089581059000,
    1756990209647574000,
    1756990329724524000,
    1756990449756347000,
    1756990569836621000,
    1756990689888007000,
    1756990809959909000,
    1756990930065125000,
    1756991050097723000,
    1756991170133434000,
    1756991290216689000,
    1756991410269554000,
    1756991530341939000,
    1756991650409488000,
    1756991770472586000,
    1756991890558071000,
    1756992010654542000,
    1756992130717106000,
    1756992250775593000,
    1756992370847053000,
    1756992490906706000,
    1756992610996238000,
    1756992731034230000,
    1756992851116724000,
    1756992971185029000,
    1756993091259032000,
    1756993211358871000,
    1756993331459309000,
    1756993451495168000,
    1756993571545193000,
    1756993691604676000,
    1756993811655404000,
    1756993931717109000,
    1756994051783699000,
    1756994171856860000,
    1756994291892723000,
    1756994411936079000,
    1756994532008501000,
    1756994652082658000,
    1756994772136970000,
    1756994892213763000,
    1756995012295835000,
    1756995132358201000,
    1756995252436405000,
    1756995372543103000,
    1756995492646502000,
    1756995612750606000,
    1756995732784570000,
    1756995852822474000,
    1756995972890336000,
    1756996092943355000,
    1756996213008957000,
    1756996333048671000,
    1756996453117332000,
    1756996573223216000,
    1756996693303780000,
    1756996813366524000,
    1757002704693343000,
    1757023773667036000,
    1757041699363652000,
    1757055218215133000,
    1757077928370363000,
    1757097865535024000,
    1757119238655860000,
    1757141013220548000,
    1757162503102737000,
    1757165392226622000,
    1757165510152864000,
    1757165630237494000,
    1757165750308817000,
    1757165870380233000,
    1757165990437966000,
    1757166110469908000,
    1757166230515316000,
    1757166350616798000,
    1757166470678919000,
    1757167564931334000,
    1757178180862373000,
    1757188492706542000,
    1757210042910096000,
    1757230564105527000,
    1757252398024362000,
    1757265814030475000,
    1757265934116618000,
    1757266054160186000,
    1757266174219652000,
    1757266294331592000,
    1757266414409181000,
    1757266534451245000,
    1757266654551547000,
    1757266774611241000,
    1757266894660106000,
    1757267014692582000,
    1757267134774053000,
    1757267254821542000,
    1757267374873495000,
    1757267494950475000,
    1757267614987475000,
    1757267735039350000,
    1757267855080597000,
    1757267975142838000,
    1757268095206965000,
    1757268215274784000,
    1757268335315579000,
    1757268455363402000,
    1757268575434088000,
    1757268695512458000,
    1757268815587887000,
    1757268935655651000,
    1757269055745250000,
    1757269175828972000,
    1757269295864304000,
    1757269415945100000,
    1757269536035498000,
    1757269656065459000,
    1757269776148649000,
    1757269896249276000,
    1757270016299832000,
    1757270136356003000,
    1757270256435118000,
    1757270376478921000,
    1757270496538102000,
    1757270616578358000,
    1757270736645521000,
    1757270856664962000,
    1757270976716077000,
    1757271096782873000,
    1757271216850845000,
    1757271336892856000,
    1757271456941979000,
    1757271577000742000,
    1757271697037881000,
    1757271817091857000,
    1757271937139061000,
    1757272057203640000,
    1757272177246144000,
    1757272297312850000,
    1757272417372299000,
    1757272537433568000,
    1757272657464134000,
    1757272777529918000,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0,
    0
  ],
  "NumGC": 235,
  "NumForcedGC": 0,
  "GCCPUFraction": 0.000110792361462292,
  "EnableGC": true,
  "DebugGC": false,
  "BySize": [
    {
      "Size": 0,
      "Mallocs": 0,
      "Frees": 0
    },
    {
      "Size": 8,
      "Mallocs": 11016772,
      "Frees": 11012846
    },
    {
      "Size": 16,
      "Mallocs": 196480112,
      "Frees": 196370703
    },
    {
      "Size": 24,
      "Mallocs": 13248427,
      "Frees": 13228745
    },
    {
      "Size": 32,
      "Mallocs": 22717716,
      "Frees": 22696484
    },
    {
      "Size": 48,
      "Mallocs": 48624700,
      "Frees": 48584261
    },
    {
      "Size": 64,
      "Mallocs": 140008082,
      "Frees": 139995960
    },
    {
      "Size": 80,
      "Mallocs": 9384650,
      "Frees": 9376235
    },
    {
      "Size": 96,
      "Mallocs": 16311577,
      "Frees": 16298742
    },
    {
      "Size": 112,
      "Mallocs": 14413782,
      "Frees": 14407119
    },
    {
      "Size": 128,
      "Mallocs": 4757032,
      "Frees": 4750776
    },
    {
      "Size": 144,
      "Mallocs": 1436868,
      "Frees": 1433317
    },
    {
      "Size": 160,
      "Mallocs": 4128210,
      "Frees": 4120192
    },
    {
      "Size": 176,
      "Mallocs": 998047,
      "Frees": 993565
    },
    {
      "Size": 192,
      "Mallocs": 2169353,
      "Frees": 2159974
    },
    {
      "Size": 208,
      "Mallocs": 1029286,
      "Frees": 1025643
    },
    {
      "Size": 224,
      "Mallocs": 946268,
      "Frees": 943703
    },
    {
      "Size": 240,
      "Mallocs": 18468013,
      "Frees": 18466887
    },
    {
      "Size": 256,
      "Mallocs": 3149279,
      "Frees": 3144232
    },
    {
      "Size": 288,
      "Mallocs": 2297697,
      "Frees": 2294685
    },
    {
      "Size": 320,
      "Mallocs": 1026012,
      "Frees": 1024479
    },
    {
      "Size": 352,
      "Mallocs": 528268,
      "Frees": 527278
    },
    {
      "Size": 384,
      "Mallocs": 393974,
      "Frees": 392600
    },
    {
      "Size": 416,
      "Mallocs": 223716,
      "Frees": 223015
    },
    {
      "Size": 448,
      "Mallocs": 224419,
      "Frees": 221314
    },
    {
      "Size": 480,
      "Mallocs": 2679419,
      "Frees": 2677881
    },
    {
      "Size": 512,
      "Mallocs": 340660,
      "Frees": 340253
    },
    {
      "Size": 576,
      "Mallocs": 621593,
      "Frees": 621026
    },
    {
      "Size": 640,
      "Mallocs": 210293,
      "Frees": 209959
    },
    {
      "Size": 704,
      "Mallocs": 969684,
      "Frees": 968635
    },
    {
      "Size": 768,
      "Mallocs": 463666,
      "Frees": 462714
    },
    {
      "Size": 896,
      "Mallocs": 551283,
      "Frees": 549681
    },
    {
      "Size": 1024,
      "Mallocs": 299245,
      "Frees": 298328
    },
    {
      "Size": 1152,
      "Mallocs": 383138,
      "Frees": 382213
    },
    {
      "Size": 1280,
      "Mallocs": 79858,
      "Frees": 79619
    },
    {
      "Size": 1408,
      "Mallocs": 1124153,
      "Frees": 1122625
    },
    {
      "Size": 1536,
      "Mallocs": 239012,
      "Frees": 238800
    },
    {
      "Size": 1792,
      "Mallocs": 194073,
      "Frees": 193429
    },
    {
      "Size": 2048,
      "Mallocs": 113782,
      "Frees": 113664
    },
    {
      "Size": 2304,
      "Mallocs": 192819,
      "Frees": 192535
    },
    {
      "Size": 2688,
      "Mallocs": 241617,
      "Frees": 241373
    },
    {
      "Size": 3072,
      "Mallocs": 47527,
      "Frees": 46738
    },
    {
      "Size": 3200,
      "Mallocs": 19857,
      "Frees": 19840
    },
    {
      "Size": 3456,
      "Mallocs": 17476,
      "Frees": 17433
    },
    {
      "Size": 4096,
      "Mallocs": 737377,
      "Frees": 736773
    },
    {
      "Size": 4864,
      "Mallocs": 167546,
      "Frees": 167478
    },
    {
      "Size": 5376,
      "Mallocs": 103332,
      "Frees": 103129
    },
    {
      "Size": 6144,
      "Mallocs": 8475,
      "Frees": 8364
    },
    {
      "Size": 6528,
      "Mallocs": 2970,
      "Frees": 2950
    },
    {
      "Size": 6784,
      "Mallocs": 1588,
      "Frees": 1585
    },
    {
      "Size": 6912,
      "Mallocs": 1226,
      "Frees": 1222
    },
    {
      "Size": 8192,
      "Mallocs": 95444,
      "Frees": 95045
    },
    {
      "Size": 9472,
      "Mallocs": 10459,
      "Frees": 10433
    },
    {
      "Size": 9728,
      "Mallocs": 6104,
      "Frees": 6077
    },
    {
      "Size": 10240,
      "Mallocs": 877,
      "Frees": 868
    },
    {
      "Size": 10880,
      "Mallocs": 729,
      "Frees": 719
    },
    {
      "Size": 12288,
      "Mallocs": 1662,
      "Frees": 1643
    },
    {
      "Size": 13568,
      "Mallocs": 2232,
      "Frees": 2217
    },
    {
      "Size": 14336,
      "Mallocs": 3856,
      "Frees": 3849
    },
    {
      "Size": 16384,
      "Mallocs": 12829,
      "Frees": 12682
    },
    {
      "Size": 18432,
      "Mallocs": 3663,
      "Frees": 3659
    }
  ]
}
----
Non-Go stats:
{
  "CGoAllocatedBytes": 138703616,
  "CGoTotalBytes": 169852928
}
