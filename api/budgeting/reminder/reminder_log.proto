syntax = "proto3";

package reminder;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/budgeting/reminder";
option java_package = "com.github.epifi.gamma.api.budgeting.reminder";

// FieldMask for ReminderLog messages
enum ReminderLogFieldMask {
  REMINDER_LOG_FIELD_MASK_UNSPECIFIED = 0;
  REMINDER_LOG_MASK_ID = 1;
  REMINDER_LOG_MASK_SUBSCRIPTION_ID = 2;
  REMINDER_LOG_MASK_ACTOR_ID = 3;
  REMINDER_LOG_MASK_YEAR = 4;
  REMINDER_LOG_MASK_MONTH = 5;
  REMINDER_LOG_MASK_COUNT = 6;
  REMINDER_LOG_MASK_CREATED_AT = 7;
  REMINDER_LOG_MASK_UPDATED_AT = 8;
  REMINDER_LOG_MASK_DELETED_AT = 9;
}

// A ReminderLog message represents the count of triggered reminders for a
// specific reminder subscription and month combination.
message ReminderLog {
  // unique identifier for the reminder log.
  string id = 1;

  // identifier of the reminder subscription.
  string reminder_subscription_id = 2;

  // identifier of the actor who triggered the reminder.
  string actor_id = 3;

  // year of the reminder log.
  int32 year = 4;

  // month of the reminder log.
  int32 month = 5;

  // count of triggered reminders in the specified month and reminder subscription.
  int32 count = 6;

  // standard timestamp fields
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  google.protobuf.Timestamp deleted_at = 9;
}
