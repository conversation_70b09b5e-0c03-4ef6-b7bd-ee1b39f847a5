syntax = "proto3";

package api.datasharing;

import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/datasharing/data_req_params.proto";
import "api/typesv2/datasharing/data_sharing_record.proto";

option go_package = "github.com/epifi/gamma/api/datasharing";
option java_package = "com.github.epifi.gamma.api.datasharing";

// DataSharing service facilitates the process of sharing data from one regulated entity to another.
// For example, sharing Account Aggregator (AA) data from Epifi Wealth with Epifi Tech
service DataSharing {
  // Returns a deeplink to a screen for exchanging specific data,
  // asking user consents and other actions required before sharing data with the caller.
  // Example: To share AA data from Epifi Wealth with Epifi Tech,
  // the screen may ask the user to consent to sharing their AA data with Epifi Tech.
  // Currently, after data is shared, the next action is determined based on the client type
  // and request ID. In the future, clients may provide a callback upfront for easier integration.
  rpc InitiateDataSharing (InitiateDataSharingRequest) returns (InitiateDataSharingResponse);

  // Retrieves the data of a user's connected accounts for a specific client request.
  // The client must provide a unique client request ID to track the data across requests.
  // Returns error codes if the necessary consents are not present.
  rpc DownloadData (DownloadDataRequest) returns (DownloadDataResponse);

  // Stores the data received from clients and associates it with the original GetData request
  // using the client request ID. After storing the data, it notifies relevant services based on
  // the client and returns the notification response.
  rpc UploadData (UploadDataRequest) returns (UploadDataResponse);
}

message InitiateDataSharingRequest {
  typesv2.datasharing.Client client = 1;

  typesv2.common.Owner client_ownership = 2;

  string client_req_id = 3;

  string actor_id = 4;

  typesv2.datasharing.DataType data_type = 5;

  api.typesv2.datasharing.DataRequestParams data_request_params = 6;
}

message InitiateDataSharingResponse {
  rpc.Status status = 1;

  // This deeplink enables clients to get necessary consents for the data type,
  // get data for a client req id and store that data.
  frontend.deeplink.Deeplink next_action_deeplink = 2;
}

message DownloadDataRequest {
  typesv2.datasharing.Client client = 1;

  typesv2.common.Owner client_ownership = 2;

  // Unique client request ID used to track the data shared
  string client_req_id = 3;

  string actor_id = 4;

  typesv2.datasharing.DataType data_type = 5;

  api.typesv2.datasharing.DataRequestParams data_request_params = 6;
}


message DownloadDataResponse {
  rpc.Status status = 1;

  typesv2.datasharing.DataSharingRecord data_sharing_record = 2;
}

message UploadDataRequest {
  typesv2.datasharing.DataSharingRecord data_sharing_record = 1;
}

message UploadDataResponse {
  rpc.Status status = 1;

  frontend.deeplink.Deeplink next_action = 2;
}
