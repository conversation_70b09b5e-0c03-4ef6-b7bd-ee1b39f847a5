syntax = "proto3";

package health_engine;

import "api/auth/device_registration_status.proto";
import "api/health_engine/enums.proto";
import "api/order/payment/accounting_entry_type.proto";
import "api/order/payment/payment_protocol.proto";
import "api/order/payment/transaction.proto";
import "api/rpc/status.proto";
import "api/typesv2/bank.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/psp.proto";
import "google/protobuf/timestamp.proto";


option go_package = "github.com/epifi/gamma/api/health_engine";
option java_package = "com.github.epifi.gamma.api.health_engine";

// Defines the GRPC service to perform various operations to process the data for health engine
service HealthEngineService {

  // RPC AddHealthMetric to add metrics into data store.
  // Health queries will be executed on these data to get health status.
  // Based on implementation of different MetricsData one or more metrics will be added to data store.
  // Check MetricsData for details of different metrics data.
  rpc AddHealthMetric (AddHealthMetricRequest) returns (AddHealthMetricResponse) {
  }

  // RPC to fetch the health status for the given request
  // Health status is fetched from redis and if record is not found for the given request, status ok is returned which means the given request is in healthy state
  rpc GetHealthStatus (GetHealthStatusRequest) returns (GetHealthStatusResponse) {}

  // OverrideHealthStatus is useful to override the health status of an entity configured by health engine
  // it will check for the current status, if it is the same, then it will update with the new status
  // for e.g. upi payments through federal bank is marked as unhealthy by health engine, but federal bank has communicated offline that it is stable now
  // in such cases, we want to override the health status
  rpc OverrideHealthStatus (OverrideHealthStatusRequest) returns (OverrideHealthStatusResponse) {}

  // GetDowntimePeriod retrieves the downtime period for a given metric.
  // It fetches the start and end times from the cache based on the provided metric data.
  rpc GetDowntimePeriod (GetDowntimePeriodRequest) returns (GetDowntimePeriodResponse) {}
}

message GetDowntimePeriodRequest {
  MatrixDataRequest matrix_data = 1;
}

message GetDowntimePeriodResponse {
  rpc.Status status = 1;
  // downtime period start timestamp
  google.protobuf.Timestamp start_time = 2;
  // downtime period end timestamp
  google.protobuf.Timestamp end_time = 3;
}

message AddHealthMetricRequest {
  MatrixDataRequest matrix_data = 1;
}
// TransactionMetric to capture transaction related components that need to be tracked for tracking health of payment components.
message TransactionMetric {
  // bank name involve in transaction.
  api.typesv2.Bank bank_name = 1;

  // represent the psp name involved in upi txn
  // will help to take decision for specific PSP
  api.typesv2.PSP_Handle psp_handle = 2;

  // status of the transaction
  order.payment.TransactionStatus status = 3;

  // payment protocol for the transaction
  order.payment.PaymentProtocol payment_protocol = 4;

  // type of transaction
  order.payment.AccountingEntryType txn_type = 5;
}

message DevRegistrationEndpointMetric {
  // Virtual mobile number to which SMS is sent
  api.typesv2.common.PhoneNumber phone_number = 1;

  auth.DevRegistrationFlow flow = 2;

  uint32 code = 3;
}

message CsisMetric {

}

message AddHealthMetricResponse {
  enum Status {
    OK = 0;
    // request parameters invalid
    INVALID_ARGUMENT = 3;
    // Internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}

message GetHealthStatusRequest {
  MatrixDataRequest matrix_data = 1;
  // actor id of the user which is requesting the health of the flow
  // actor id is used for (but not limited to)
  // 1. check if the users falls in the seed traffic bucket
  // NOTE: actor id is not mandatory and used in the above listed methods only
  string actor_id = 2;
}

message GetHealthStatusResponse {
  enum Status {
    // status ok represents healthy status
    OK = 0;
    // Internal server error
    INTERNAL = 13;
    // for the same request, there can be 2 health states (one at a given point of time)
    // WARNING_STATE or UNHEALTHY_STATE is returned when the respective alert has been triggered based upon the thresholds being configured
    // client can tweak their business logic based upon the health state
    WARNING_STATE = 100;

    UNHEALTHY_STATE = 101;
    // manual kill switch applied for the given request
    MANUAL_KILL_SWITCH_APPLIED = 102;
    // HEALTH_ENGINE_BY_PASSED depicts that the health of the entity was either in warning state or in unhealthy state due to automated kill switch but the seed traffic logic
    // used in the flow is allowing the request to go through. Specific status is returned instead of status OK because this can be used to analyse the working of the seed traffic logic.
    // NOTE: each flow onboarded to health engine can define its own seed traffic logic
    HEALTH_ENGINE_BY_PASSED = 103;
  }
  rpc.Status status = 1;
}


message OverrideHealthStatusRequest {
  MatrixDataRequest matrix_data = 1;
  // current status of health engine
  HealthStatus current_health_status = 2;
  // status to be overridden to, i.e. the new status to be set for the given MatrixDataRequest
  HealthStatus new_health_status = 3;
}

message OverrideHealthStatusResponse {
  enum Status {
    // to be returned when overriding is successful (irrespective of the value, i.e. healthy/unhealthy)
    OK = 0;
    // to be returned when overriding operation fails because of unexpected scenarios
    INTERNAL = 13;
    // request parameters invalid
    INVALID_ARGUMENT = 3;
    // to be returned when current health status passed in the request is not same as stored in the redis
    STATUS_MISMATCHED = 100;
  }

  rpc.Status status = 1;
}

// Different type of metrics that need to be tracked by health engine.
// Based on implementation of metrics component different type of data will get ingested in data store.
// As there high cardinality will lead to higher latency, and it might also lead to outage at metric data storage.
// Cardinality is multiple of different label values ingested.
message MatrixDataRequest {
  oneof MatrixData {
    TransactionMetric transaction_metric = 1;
    DevRegistrationEndpointMetric dev_registration_endpoint_metric = 2;
    CsisMetric csis_metric = 3;
  }
}
