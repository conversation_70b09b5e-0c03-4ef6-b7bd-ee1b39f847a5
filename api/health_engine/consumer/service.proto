//go:generate gen_queue_pb
syntax = "proto3";

package health_engine.consumer;

import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/health_engine/consumer";
option java_package = "com.github.epifi.gamma.api.health_engine.consumer";

service Consumer {
  // ProcessHealthStatus consumes message published by health engine webhook
  // and update redis with the given health state configured in the alert
  rpc ProcessHealthStatus(ProcessHealthStatusRequest) returns (ProcessHealthStatusResponse) {}
}

message ProcessHealthStatusRequest {
  // A set of all the common attributes to be contained in a queue consumer request
  queue.ConsumerRequestHeader request_header = 1;
  // key value pair added as labels in the alerts. for eg. "Bank": "federal"
  map<string, string> labels = 2;
  // key value pair added as annotations in the alerts. for eg. "ttl": "20m", "severity": "warning"
  map<string, string> annotations = 3;
  // time at which alert was trigerred. It is returned by the alertmanager itself in the webhook method
  google.protobuf.Timestamp alert_trigger_time = 4;
}
message ProcessHealthStatusResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}
