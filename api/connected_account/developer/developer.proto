// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package connected_account.developer;

import "api/connected_account/internal/account.proto";
import "api/connected_account/internal/bank.proto";
import "api/connected_account/internal/consent.proto";
import "api/connected_account/internal/data.proto";
import "api/connected_account/internal/transaction.proto";
import "api/connected_account/notification/notification.proto";

option go_package = "github.com/epifi/gamma/api/connected_account/developer";
option java_package = "com.github.epifi.gamma.api.connected_account.developer";

enum CaEntity {
  CA_ENTITY_UNSPECIFIED = 0;

  DATA_ATTEMPT_ENTITY = 1;

  CONSENT_ENTITY = 2;

  ACCOUNTS_ENTITY = 3;

  TRANSACTIONS_ENTITY = 4;

  BANK_PREFERENCE_ENTITY = 5;

  NOTIFICATION_ENTITY = 6;
}

message ConsentEntityActorIdResponse {
  repeated ConsentEntityResponse consent_entity_response = 1;
}

message ConsentEntityResponse {
  connected_account.ConsentRequest consent_request = 1;
  connected_account.Consent consent = 2;
  repeated connected_account.ConsentAccountMapping consent_account_mapping = 3;
}

message DataEntityResponseList {
  repeated DataEntityResponse data_entity_response_list = 4;
}

message DataEntityResponse {
  connected_account.DataFetchAttempt data_fetch_attempt = 1;
  repeated connected_account.DataProcessAttempt data_process_attempt_list = 2;
  repeated connected_account.BatchProcessTransaction batch_list = 3;
}

message AccountEntityResponse {
  message Account {
    connected_account.AaAccount aa_account = 1;
    connected_account.AaDepositAccount aa_deposit_account = 2;
    connected_account.AaRecurringDepositAccount aa_recurring_deposit_account = 3;
    connected_account.AaTermDepositAccount aa_term_deposit_account = 4;
    string error = 5;
    EquityAccountData equity_account_data = 6;
    EtfAccountData etf_account_data = 7;
    ReitAccountData reit_account_data = 8;
    InvitAccountData invit_account_data = 9;
    NpsAccountData nps_account_data = 10;
  }
  repeated Account accounts = 1;
}

message BankPreferenceEntityResponse {
  repeated connected_account.AaUserBankPreference aa_bank_preference = 1;
}

message NotificationEntityResponse {
  connected_account.notification.AaNotification aa_notification = 1;
}

message NotificationEntityResponseList {
  repeated NotificationEntityResponse notification_entity_response_list = 1;
}

message TransactionsEntityResponse {
  connected_account.AaTransaction aa_transaction = 1;
  connected_account.AaDepositTransaction aa_deposit_transaction = 2;
  connected_account.AaRecurringDepositTransaction aa_rd_transaction = 3;
  connected_account.AaTermDepositTransaction aa_td_transaction = 4;
  AaEquityTransaction aa_equity_transaction = 6;
  AaEtfTransaction aa_etf_transaction = 7;
  string error = 5;

}

message TransactionsEntityResponseList {
  repeated TransactionsEntityResponse aa_transaction_list = 1;
}

message EquityAccountData {
  connected_account.AaEquityAccount aa_equity_account = 1;
  repeated connected_account.AaEquityHolding aa_equity_holdings = 2;
}

message EtfAccountData {
  connected_account.AaEtfAccount aa_etf_account = 1;
  repeated connected_account.AaEtfHolding aa_etf_holdings = 2;
}

message ReitAccountData {
  connected_account.AaReitAccount aa_reit_account = 1;
  repeated connected_account.AaReitHolding aa_reit_holdings = 2;
}

message InvitAccountData {
  connected_account.AaInvitAccount aa_invit_account = 1;
  repeated connected_account.AaInvitHolding aa_invit_holdings = 2;
}

message NpsAccountData {
  connected_account.AaNpsAccount aa_nps_account = 1;
  repeated connected_account.AaNpsHolding aa_nps_holdings = 2;
}
