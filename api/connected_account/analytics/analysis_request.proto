//go:generate gen_sql -types=AnalysisRequest,Details
syntax = "proto3";

package connected_account.analytics;

import "api/connected_account/analytics/enums.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/employment_type.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/connected_account/analytics";
option java_package = "com.github.epifi.gamma.api.connected_account.analytics";

// AnalysisAttempt represents an individual attempt to analyze a user's data.
// This message captures the details of the analysis request and its current status.
message AnalysisRequest {
  // Unique identifier for the analysis attempt
  string id = 1;

  // uuid to be used for orchestrating workflow to fetch the analysis from vendor, same as the client request id sent from the caller
  // creating a unique identifier for the analysis request instead of using id due to future proofing and cleaner solution, reference: https://chatgpt.com/share/675bf7f7-cfd8-8012-af0a-3fdabad7f5b1
  string orch_id = 2;

  // Identifier of the user whose analysis is being requested
  string actor_id = 3;

  // Latest status of the analysis
  AttemptStatus status = 4;

  // details json w.r.t. the analysis request and response from vendor to store additional data
  Details details = 5;

  // ts to mark the analysis attempt workflow process completion. This is null if the analysis attempt is not completed and
  // only one non null entry is allowed at any moment
  google.protobuf.Timestamp completed_at = 7;

  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;
}

message Details {
  // Client enum for specified by the caller
  api.typesv2.common.Owner client = 1;
  // id returned from vendor which is used to track the analysis request
  // get it in fast analysis response and the same is used in detailed analysis and status requests
  string reference_id = 2;
  // ts to mark fast analysis successful completion from vendor and storing the response in s3
  google.protobuf.Timestamp l1_completed_at = 3;
  // ts to mark detailed analysis successful completion from vendor and storing the response in s3
  google.protobuf.Timestamp l2_completed_at = 4;

  bool is_initiated_with_data = 5;

  // user's employment type details as sent by client
  api.typesv2.EmploymentType employment_type = 6; // optional field
  // Name of user's organisation / employer
  string organisation_name = 7; // optional field
}
