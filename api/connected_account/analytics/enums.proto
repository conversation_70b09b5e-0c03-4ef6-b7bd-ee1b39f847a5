//go:generate gen_sql -types=Client,AttemptStatus
syntax = "proto3";

package connected_account.analytics;

option go_package = "github.com/epifi/gamma/api/connected_account/analytics";
option java_package = "com.github.epifi.gamma.api.connected_account.analytics";

// AttemptRequestStatus represents the status of the analysis request attempt process.
enum AttemptStatus {
  ATTEMPT_REQUEST_STATUS_UNSPECIFIED = 0;

  ATTEMPT_REQUEST_STATUS_CREATED = 1;

  ATTEMPT_REQUEST_STATUS_PROCESSING = 2;

  ATTEMPT_REQUEST_STATUS_SUCCESS = 3;

  ATTEMPT_REQUEST_STATUS_FAILED = 4;
}

enum AnalysisRequestFieldMask {
  ANALYSIS_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  ANALYSIS_REQUEST_FIELD_MASK_ORCH_ID = 1;
  ANALYSIS_REQUEST_FIELD_MASK_STATUS = 2;
  ANALYSIS_REQUEST_FIELD_MASK_DETAILS = 3;
  ANALYSIS_REQUEST_FIELD_MASK_COMPLETED_AT = 4;
}
