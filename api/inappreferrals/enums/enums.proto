//go:generate gen_sql -types=Component,ReferralsV1CodeShareActionType,ReferralsV1CodeShareMediaType,CollectedDataType,ReferralLinkVendor,ReferralsV1IconAnimationType,InAppReferralUnlockFieldMask,RefereeOnbStage,FiniteCodeChannel,FiniteCodeType,Trigger,Variant,ConfigStatus,ReferralCodeType,AppFeature
syntax = "proto3";

package inappreferral.enums;

option go_package = "github.com/epifi/gamma/api/inappreferral/enums";
option java_package = "com.github.epifi.gamma.api.inappreferral.enums";

enum Component {
  COMPONENT_UNSPECIFIED = 0;
  // for details related to home screen referrals widget.
  OLD_HOME_SCREEN_WIDGET = 1;
  // for details related to new home screen referrals widget.
  HOME_SCREEN_WIDGET_V1 = 2;
  // for details related to invite friends screen's "How to win rewards" section
  HOW_TO_WIN_REWARDS_WIDGET = 3;
  // for details related to "Share code" button
  INVITE_FRIENDS_SCREEN_SHARE_CODE_BUTTON = 4;

  // referrals v1 screen offer-info component
  V1_OFFER_INFO_COMPONENT = 5;
  // referrals v1 screen how-it-works component
  V1_HOW_IT_WORKS_COMPONENT = 6;
  // referrals v1 share-code scripts component
  V1_SHARE_CODE_COMPONENT = 7;
  // referrals v1 invite-contacts component
  V1_INVITE_CONTACTS_COMPONENT = 8;
  // referrals v1 stacked-rewards-info component
  V1_STACKED_REWARDS_INFO_COMPONENT = 9;
  // referrals v1 referee actions info component
  // this component/page shows the actions to be performed by the referee for the referrer to get rewards and the status of the rewards (earned, pending, etc.)
  // figma : https://www.figma.com/file/5TZH6I0awKw8DMtj3aMzrC/Referrals-%E2%80%A2-FFF?type=design&node-id=9465-15293&mode=design&t=XuZltBF9uLVVXSYy-4
  V1_REFEREE_ACTIONS_INFO_COMPONENT = 10;
  // referrals v1 weekly-earnings-info component
  // this component shows the earnings of the referrer week by week on each referral
  // figma: https://www.figma.com/design/5TZH6I0awKw8DMtj3aMzrC/Referrals-%E2%80%A2-FFF?node-id=11840-4543&t=tJN7ThO9mmWkoAmz-1
  V1_WEEKLY_EARNINGS_INFO_COMPONENT = 11;
  // referrals home screen v2 widget
  // it's the next version of HOME_SCREEN_WIDGET_V1
  HOME_SCREEN_WIDGET_V2 = 12;
  // details of the component shown post applying referral code
  CODE_APPLIED_COMPONENT = 13;
}

enum ReferralsV1CodeShareActionType {
  REFERRALS_V1_CODE_SHARE_ACTION_TYPE_UNSPECIFIED = 0;
  // share code on whatsapp
  WHATSAPP = 1;
  // share code via message app
  MESSAGE = 2;
  // copy share script to clipboard
  COPY_TO_CLIPBOARD = 3;
  // default share action, i.e. opens up all share options of the platform
  INVITE_FRIENDS = 4;
  // share code on instagram
  INSTAGRAM = 5;
  // share code on Twitter
  TWITTER = 6;
}

enum ReferralsV1CodeShareMediaType {
  // no media to be shared
  REFERRALS_V1_CODE_SHARE_MEDIA_TYPE_UNSPECIFIED = 0;
  // default media that client already has
  DEFAULT = 1;
  // custom media being sent from backend
  CUSTOM = 2;
}

enum CollectedDataType {
  COLLECTED_DATA_TYPE_UNSPECIFIED = 0;
  ORDER_UPDATE = 1;
  SAVINGS_ACCOUNT_STATE_UPDATE = 2;
}

// ReferralLinkVendor: enum tells about the vendor which was used for the generation of the referral link
enum ReferralLinkVendor {
  REFERRAL_LINK_VENDOR_UNSPECIFIED = 0;
  APPSFLYER_ONELINK = 1;
}

// ReferralsV1IconAnimationType: type of animation to be shown for the action type icon
// If no animation is required, unspecified should be passed
enum ReferralsV1IconAnimationType {
  // To be passed for no animation
  REFERRALS_V1_ICON_ANIMATION_TYPE_UNSPECIFIED = 0;
  // Animation type : <insert drive/figma link for animation>
  TYPE_ONE = 1;
}

enum InAppReferralUnlockFieldMask {
  IN_APP_REFERRAL_UNLOCK_UNSPECIFIED = 0;
  IS_UNLOCKED = 1;
  AVERAGE_EOD_BALANCE = 2;
  UNLOCKED_AT = 3;
}

// RefereeOnbStage represents the onboarding stage of a referee
enum RefereeOnbStage {
  REFEREE_ONB_STAGE_UNSPECIFIED = 0;

  // The default stage will be INSTALLED as soon as finite code is claimed be a referee
  INSTALLED = 1;

  // This will be set as soon as savings account is created
  ACCOUNT_CREATED = 2;

  // As soon as rewards are granted for this referrer-referee combo
  REWARD_GRANTED = 3;
}


// Finite code can be used to onboard on the app and be shared by one of the below channels.
enum FiniteCodeChannel {
  FINITE_CODE_CHANNEL_UNSPECIFIED = 0;
  IN_APP_REFERRAL = 1;
  INFLUENCER_REFERRAL = 2;
  CUSTOMER_SERVICE = 3;
  WAITLIST = 4;
  GPAY = 5;
  PHONEPE = 6;
  VANTAGE_CIRCLE = 7;
  TIMESPRIME = 8;
  CASHKARO = 9;
  TIMESPOINT = 10;
  ACQUISITION = 11;
  OFFLINE = 12;
  GROWTHX = 13;
  CLEARTAX = 14;
}

// Specifies the different types of Finite Code
enum FiniteCodeType {
  FINITE_CODE_TYPE_UNSPECIFIED = 0;
  REGULAR = 1;
  GOLDEN_TICKET = 2;
  GPAY_TYPE1 = 3;
  GPAY_TYPE2 = 4;
  GPAY_TYPE3 = 5;
  GPAY_TYPE4 = 6;
  GPAY_ONLINE = 17;
  PHONEPE_TYPE1 = 7;
  PHONEPE_TYPE2 = 8;
  PHONEPE_TYPE3 = 9;
  PHONEPE_TYPE4 = 10;
  PHONEPE_ONLINE = 18;
  VANTAGE_CIRCLE_TYPE1 = 11;
  TIMESPRIME_TYPE1 = 12;
  CASHKARO_TYPE1 = 13;
  TIMESPOINT_TYPE1 = 14;
  TRUECALLER_ADS = 15;
  EMAIL_MARKETING = 16;
  ZOMATO = 19;
  RADIO_INDIGO = 20;
  CULT_FIT_TV = 21;
  YOUTUBE = 22;
  PHONEPE_TYPE5 = 23;
  PHONEPE_TYPE6 = 24;
  AMAZON = 25;
  GROWTHX_TYPE1 = 26;
  CLEARTAX_TYPE1 = 27;
  // finite code type for campaign to onboard user on wealth analyser via some campaign, ex- share code on Linkedin
  WEALTH_ANALYSER_CAMPAIGN = 28;
  // regular finite code for direct-to-home (D2H) referrals
  D2H_REGULAR = 29;
}

// Trigger: what "action" leads to the kicking-off of the notification.
// Note: this doesn't guarantee that a notification will be sent for sure. It will go via it's handler checks
// and also the configured comms config for these triggers.
enum Trigger {
  TRIGGER_UNSPECIFIED = 0;
  // trigger associated with referral getting unlocked for a user
  REFERRAL_UNLOCKED_TRIGGER = 1;
  // trigger associated with performing a qualifying action by the referee to earn the referral reward
  PERFORM_QUALIFYING_ACTION_TRIGGER = 2;
  // trigger associated with referee account being created
  // this trigger covers all notifications to be sent to remind referrer only
  // in case required to remind referee, a different trigger must be created
  REFEREE_ACCOUNT_CREATED_REMIND_REFERRER_TRIGGER = 3;
}

// Variant: Another dimension to have variety for comms when other aspects (trigger, channel, type etc) are same.
enum Variant {
  VARIANT_UNSPECIFIED = 0;
  VARIANT_1 = 1;
  VARIANT_2 = 2;
  VARIANT_3 = 3;
  VARIANT_4 = 4;
  VARIANT_5 = 5;
  VARIANT_6 = 6;
  VARIANT_7 = 7;
  VARIANT_8 = 8;
  VARIANT_9 = 9;
  VARIANT_10 = 10;
}

// ConfigStatus: to cater for maker-checker model of the notification config, i.e. a single switch to handle config status
enum ConfigStatus {
  CONFIG_STATUS_UNSPECIFIED = 0;
  CREATED = 1;
  APPROVED = 2;
  ACTIVE = 3;
  INACTIVE = 4;
}

enum NotificationConfigInfoFieldMask {
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_UNSPECIFIED = 0;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_ID = 1;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_TRIGGER = 2;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_FINITE_CODE_CHANNEL = 3;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_FINITE_CODE_TYPE = 4;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_VARIANT = 5;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_SEGMENT_EXPRESSION = 6;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_CONTENT_INFO = 7;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_STATUS = 8;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_ACTIVE_FROM = 9;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_ACTIVE_TILL = 10;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_CREATED_BY = 11;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_CREATED_AT = 12;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_UPDATED_AT = 13;
  NOTIFICATION_CONFIG_INFO_FIELD_MASK_DELETED_AT = 14;
}

// enum to denote the type of referral code used while claiming
// the referral code can be a regular 10-digit alphanumeric code or phone number of their friend on FI
enum ReferralCodeType {
  REFERRAL_CODE_TYPE_UNSPECIFIED = 0;
  // 10 digit alphanumeric code
  REFERRAL_CODE_TYPE_FINITE_CODE = 1;
  // Phone number of referee
  REFERRAL_CODE_TYPE_PHONE_NUMBER = 2;
}

// AppFeature enum to denote App Features, ex- Savings Account, Credit Card, etc.
// ex usage: for showing a UI element based on the AppFeature enabled for the user
enum AppFeature {
  APP_FEATURE_UNSPECIFIED = 0;
  SAVINGS_ACCOUNT = 1;
  // d2h/FI-lite user, irrespective of the intent
  DIRECT_TO_HOME = 2;
  WEALTH_ANALYSER = 3;
}

// enum to denote the type of visual elements which can rendered on the screen, ex- image, lottie etc
enum VisualElementType {
  VISUAL_ELEMENT_TYPE_UNSPECIFIED = 0;
  IMAGE = 1;
  LOTTIE = 2;
}
