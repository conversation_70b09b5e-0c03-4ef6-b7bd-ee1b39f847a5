syntax = "proto3";

package season;

import "api/rewards/reward.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/inappreferral/season";
option java_package = "com.github.epifi.gamma.api.inappreferral.season";

message Season {
  // unique id of the season
  string id = 1;
  // reward offer group with which this season is linked for rewards orchestration
  string rewardOfferGroupId = 2;

  message DisplayDetails {
    // which season is it, i.e. season 1/2/3
    uint32 season_number = 1;
    // title of the season, i.e. "SUMMER BONANZA"
    string title = 2;
    // season image
    string image_url = 3;
    // season bg color
    string bg_color = 4;
    // season description
    string desc = 5;

    message FeatureDetails {
      string image_url = 1;
      string desc = 2;
    }
    // feature details of the particular season
    repeated FeatureDetails feature_details = 6;

  }
  // display details of the season
  DisplayDetails display_details = 3;
  // describes the levels for a season and the associated referrals + rewards
  SeasonLevels season_levels = 4;

  // timestamp since the season will be active
  google.protobuf.Timestamp active_since = 10;
  // timestamp till when the season will be active
  google.protobuf.Timestamp active_till = 11;
  // timestamp since when the season will be displayed on the app
  google.protobuf.Timestamp display_since = 12;
  // timestamp till when the season will be displayed on the app
  google.protobuf.Timestamp display_till = 13;

  // created at
  google.protobuf.Timestamp created_at = 14;
  // updated_at
  google.protobuf.Timestamp updated_at = 15;
}

message SeasonLevels {
  // list of all the levels under this season
  repeated SeasonLevel levels = 1;
}

message SeasonLevel {
  // total rewards that can be given out in this level
  uint32 total_rewards = 1;
  // reward type of the rewards that can be given out in this level
  rewards.RewardType reward_type = 2;
  // value of each reward under this level
  uint32 reward_value = 3;

  message DisplayDetails {
    // title of the level
    string title = 1;
    // desc of the level
    string desc = 2;
  }
  // display details of the season level
  DisplayDetails display_details = 4;
}
