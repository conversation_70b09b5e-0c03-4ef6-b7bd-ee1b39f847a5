//go:generate gen_sql -types=ContentInfo,TriggerMetadata,ReferralUnlockedTriggerMetadata,PerformQualifyingActionTriggerMetadata
syntax = 'proto3';

package inappreferral.notification;

import "google/protobuf/timestamp.proto";
import "api/inappreferrals/enums/enums.proto";

option go_package = "github.com/epifi/gamma/api/inappreferral/notification";
option java_package = "com.github.epifi.gamma.api.inappreferral.notification";

message NotificationConfigInfo {
  // uuid of the config
  string id = 1;
  // trigger for the notification
  inappreferral.enums.Trigger trigger = 2;
  // finite code channel the notification config is associated with
  inappreferral.enums.FiniteCodeChannel finite_code_channel = 3;
  // finite code type the notification config is associated with
  inappreferral.enums.FiniteCodeType finite_code_type = 4;
  // variant, another dimension to have variety
  inappreferral.enums.Variant variant = 5;
  // segmentation support, for e.g. 'IsMember('a') && !IsMember('b')
  string segment_expression = 6;
  // content/payload info associated with the notification(s) to be sent via this config.
  // Note: it can have multiple content, i.e. in-app, system-tray etc. In that case, we send notification for each content
  ContentInfo content_info = 7;
  // status of the config
  inappreferral.enums.ConfigStatus status = 8;
  // config is active from [inclusive]
  google.protobuf.Timestamp active_from = 9;
  // config is active till (exclusive)
  google.protobuf.Timestamp active_till = 10;
  // for audit purposes
  string created_by = 11;

  // intentional gap in field numbers to cater for future use cases
  google.protobuf.Timestamp created_at = 20;
  google.protobuf.Timestamp updated_at = 21;
  google.protobuf.Timestamp deleted_at = 22;
}

// ContentInfo: required for creating the notification payload and associated attributes
message ContentInfo {
  // info required to generated in-app-notification payload.
  // Note: can be nil
  InAppNotificationContentInfo in_app_notification_content_info = 1;
  // info required to generated system tray push notification payload.
  // Note: can be nil
  SystemTrayNotificationContentInfo system_tray_notification_content_info = 2;
}

message TriggerMetadata {
  oneof metadata {
    ReferralUnlockedTriggerMetadata referral_unlocked_trigger_metadata = 1;
    PerformQualifyingActionTriggerMetadata perform_qualifying_action_trigger_metadata = 2;
    RefereeAccountCreatedTriggerMetadata referee_account_created_trigger_metadata = 3;
  }
}

message ReferralUnlockedTriggerMetadata {
  // finite code type for which referral is unlocked for the user
  enums.FiniteCodeType finite_code_type = 1;
}

message PerformQualifyingActionTriggerMetadata {
  // todo: add fields
}

message RefereeAccountCreatedTriggerMetadata {
  string referee_actor_id = 1;
}

message InAppNotificationContentInfo {
  string title = 1;
  string body = 2;
  string icon_url = 3;
}

message SystemTrayNotificationContentInfo {
  string title = 1;
  string body = 2;
  string icon_url = 3;
}
