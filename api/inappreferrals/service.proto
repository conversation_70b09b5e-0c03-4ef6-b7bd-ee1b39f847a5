syntax = "proto3";

package inappreferral;

import "api/inappreferrals/enums/enums.proto";
import "api/inappreferrals/finitecode.proto";
import "api/inappreferrals/finitecodeclaim.proto";
import "api/inappreferrals/notification/notification.proto";
import "api/inappreferrals/referrals_segmented_components.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/inappreferral";
option java_package = "com.github.epifi.gamma.api.inappreferral";

// Defines the GRPC service to perform various operations to process an in app referral.
service InAppReferral {
  // RPC to fetch referral details for an actorID
  // The RPC checks if an actorId onboarded the app via referrals or not.
  // If yes, the RPC in the response sends other details such as the FiniteCodeID used to onboard, the ReferrerActorId,
  // the type-channel of the finite code, etc.
  //
  // The RPC returns OK status response if the actor has onboarded via referral and referral details are found successfully
  // The RPC returns NOT_FOUND status response if actor hasn't onboarded via referral
  // All other status responses should be treated as internal error
  rpc GetReferralDetailsForActor (GetReferralDetailsForActorRequest) returns (GetReferralDetailsForActorResponse) {}

  // RPC to fetch finite code details for an actor.
  // The RPC will return a list of finite code details object each holding the code, and other related properties.
  rpc GetFiniteCodesForActor (GetFiniteCodesForActorRequest) returns (GetFiniteCodesForActorResponse) {}

  // RPC to fetch details of a finite code
  // The RPC will return if such a finite code exists, and if yes whether its claim limit has been exhausted or not
  rpc GetFiniteCodeByCode (GetFiniteCodeByCodeRequest) returns (GetFiniteCodeByCodeResponse) {}

  // GetRefereesForActor fetches a list of referees for an actor
  //
  // The List can be filtered with:
  // 1. Finite Code ID - if finite_code_id field is present, the list of referees fetched will all have onboarded via
  // this finite code.
  // The maximum number of referees returned is bounded by the page_size specified in the request.
  // The list of referees is returned in DESC order based on created_at timestamp.
  //
  // The referee list returned by the RPC includes the Referee name, the finite code they used to onboard, their
  // onboarding status, their profile image.
  rpc GetRefereesForActor (GetRefereesForActorRequest) returns (GetRefereesForActorResponse) {}

  // GetRefereesCountForActor fetches the number of referees for an actor
  rpc GetRefereesCountForActor (GetRefereesCountForActorRequest) returns (GetRefereesCountForActorResponse) {}

  // GenerateFiniteCodeForActor checks if an actor has done the needful qualifying action to generate a finite code.
  // If yes, the RPC generates the finite code, otherwise returns FAILED_PRECONDITION response.
  // A qualifying action can be as simple as a credit txn present in actor's savings account or SD creation, etc.
  rpc GenerateFiniteCodeForActor (GenerateFiniteCodeForActorRequest) returns (GenerateFiniteCodeForActorResponse) {}

  // RPC to claim a finite code.
  // The RPC checks if the finite code to be claimed is valid or not.
  // It also checks if the finite code hasn't exhausted yet, i.e. if claimed_count < claim_limit for a finite code.
  //
  // If any of the above checks fail, the RPC returns an error response, specific to the check that failed.
  //
  // If the above checks pass, the RPC creates a new finite code claim record and increments the claimed_count of the finite
  // code. Both these actions are performed in txn.
  rpc ClaimFiniteCode (ClaimFiniteCodeRequest) returns (ClaimFiniteCodeResponse) {}

  // RPC to get the finite code claimed by an actor either via the referral flow or via the waitlist flow.
  // If no such finite code exists, the RPC returns NOT_FOUND status response.
  rpc GetClaimedFiniteCodeForActor (GetClaimedFiniteCodeForActorRequest) returns (GetClaimedFiniteCodeForActorResponse) {}

  // RPC to fetch referral details for a referrer.
  // The below details can be fetched using the RPC:
  // 1. Referral eligibility for referrer
  // 2. If eligible, active finite codes for referrer of each channel and type.
  rpc GetReferralSummaryForReferrer (GetReferralSummaryForReferrerRequest) returns (GetReferralSummaryForReferrerResponse) {}

  // RPC to check if an actor is eligible for referral program or not
  //
  // The eligibility criteria for referral can differ based on requirements. As of now, the below two criteria are looked
  // into to consider an actor as eligible for referral.
  // 1. If it has been more than some duration since the actor onboarded (15 days as of now)
  // 2. If the avg eod balance of the actor (in all account types) exceeds some min balance
  rpc IsActorEligibleForReferral (IsActorEligibleForReferralRequest) returns (IsActorEligibleForReferralResponse) {}

  // UnlockInAppReferralForActor bypasses the requirements/constraints to unlock in-app referrals for an actor.
  // Note: Cannot be used in Production
  rpc UnlockInAppReferralForActor (UnlockInAppReferralForActorRequest) returns (UnlockInAppReferralForActorResponse) {}

  // RPC to fetch the referrals unlock date for actor
  rpc GetReferralsUnlockTimestampForActor (GetReferralsUnlockTimestampForActorRequest) returns (GetReferralsUnlockTimestampForActorResponse) {}

  // GetSegmentedReferralComponentsDetails will return details specific for a component (a renderable element on app) based on the segment that the user lies in.
  // It will only return the component details that are active at the moment the RPC is called. If no component details
  // exist for the requested component for any segment that the actor lies in, or the details have expired, we will send default values for the requested components.
  rpc GetSegmentedReferralComponentsDetails (GetSegmentedReferralComponentsDetailsRequest) returns (GetSegmentedReferralComponentsDetailsResponse) {}

  // CreateReferralsSegmentedComponent will create a new segmented component
  rpc CreateReferralsSegmentedComponent (CreateReferralsSegmentedComponentRequest) returns (CreateReferralsSegmentedComponentResponse) {};

  // ReadReferralsSegmentedComponent will return referrals segmented components
  rpc ReadReferralsSegmentedComponent (ReadReferralsSegmentedComponentRequest) returns (ReadReferralsSegmentedComponentResponse) {};

  // UpdateReferralsSegmentedComponent will update an existing segmented component with new values
  rpc UpdateReferralsSegmentedComponent (UpdateReferralsSegmentedComponentRequest) returns (UpdateReferralsSegmentedComponentResponse) {};

  // DeleteReferralsSegmentedComponent will delete a segmented component
  rpc DeleteReferralsSegmentedComponent (DeleteReferralsSegmentedComponentRequest) returns (DeleteReferralsSegmentedComponentResponse) {};

  // rpc to store/update referral link for the actor
  rpc StoreReferralLinkForActor (StoreReferralLinkForActorRequest) returns (StoreReferralLinkForActorResponse) {};

  // CreateNotificationConfig will create a new notification config
  // To be used by sherlock dev action only
  rpc CreateNotificationConfig (CreateNotificationConfigRequest) returns (CreateNotificationConfigResponse) {};

  // UpdateNotificationConfig will update an existing notification config with new values
  // To be used by sherlock dev action only
  rpc UpdateNotificationConfig (UpdateNotificationConfigRequest) returns (UpdateNotificationConfigResponse) {};

  // DeleteNotificationConfig will delete a notification config
  // To be used by sherlock dev action only
  rpc DeleteNotificationConfig (DeleteNotificationConfigRequest) returns (DeleteNotificationConfigResponse) {};
}

message GetReferralDetailsForActorRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // flag to explicitly fetch exception cases, for e.g. ACQUISITION+GOLDEN_TICKET is considered as non-referral.
  // thus, if the flag is set to `true`, it will return referral details by considering the actor as a referee.
  bool force_include_exception_case = 2;
}

message GetReferralDetailsForActorResponse {
  enum Status {
    // successfully claimed
    OK = 0;

    // no referral details found for actor
    NOT_FOUND = 5;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // flag to determine if the actorId onboarded via referral
  // the below flag is deprecated
  // instead, use the rpc status OK to figure out if the user has onboarded via referral or not.
  // onboarded_via_referral -> true = status -> OK (both are same)
  // for all other status responses, onboarded_via_referral will be false.
  bool onboarded_via_referral = 2 [deprecated = true];

  // If the user onboarded using finite code, this field will hold the referral details
  ReferralDetails referral_details = 3;

  message ReferralDetails {
    // finite code ID
    string finite_code_id = 1;
    // actor ID of the referrer who gave the finite code to the referee
    string referrer_actor_id = 2;
    // channel of the finite code
    inappreferral.enums.FiniteCodeChannel finite_code_channel = 3;
    // type of the finite code
    inappreferral.enums.FiniteCodeType finite_code_type = 4;
    // id of the finite code claim record in case the user onboarded using finite code
    string finite_code_claim_id = 5;
    // finite code of the referee
    string code = 6;
    // referral unlock date of the referrer
    google.protobuf.Timestamp referrer_referral_unlocked_at = 7;
    // Onboarding status of the referee
    inappreferral.enums.RefereeOnbStage referee_onboarding_status = 8;
    // time at which referral code was claimed(created at time for finite code claim)
    google.protobuf.Timestamp referral_code_claimed_at = 9;
    // actual code used while onboarding.
    // note: finite code and actual code used can differ as new types of referral codes get introduced, for e.g. phone number as referral code
    string code_used = 10;
  }
}

message GetFiniteCodesForActorRequest {
  // ActorId of the referrer for which finite code details need to be fetched.
  string actorID = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // [optional] filter for fetching finite codes of a specific types
  repeated enums.FiniteCodeType finite_code_types = 2;
}

message GetFiniteCodesForActorResponse {
  enum Status {
    // successfully fetched list of finite codes for actor
    OK = 0;

    // no finite codes available for the actorID
    NOT_FOUND = 5;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // List of finite codes for the actor
  repeated FiniteCode finite_codes = 2;
}

message GetFiniteCodeByCodeRequest {
  // Finite code whose details needs to be fetched
  string code = 1 [(validate.rules).string = {min_len: 10, max_len: 10}];
}

message GetFiniteCodeByCodeResponse {
  enum Status {
    // finite code exists
    OK = 0;

    // finite code doesn't exists
    RECORD_NOT_FOUND = 5;

    // finite code exists buts its limit has been exhausted, i.e. claimed_count >= claim_limit for the finite code.
    RESOURCE_EXHAUSTED = 8;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  FiniteCode finite_code = 2;
}

message GetRefereesForActorRequest {
  // ActorId of the referrer for which finite code details need to be fetched.
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // The finiteCode for which to fetch recent referrals. If empty, recent referrals will be fetched in general and not
  // limited to just one finite code.
  string finite_code_id = 2;

  // Page size determines the upper bound on the number of records returned in a particular response.
  // since, the number of referees can be huge for an actor,
  // page size helps in keeping the payload small
  // Page size must be in the range [1, 30]
  int32 page_size = 3 [(validate.rules).int32 = {gte: 1, lte: 40}];

  // page context to help server fetch the page
  rpc.PageContextRequest page_context = 4;
}

message GetRefereesForActorResponse {
  enum Status {
    // successfully fetched list of recent X referrals
    OK = 0;

    // No referrals currently exist for the actor. This status will also be returned if the actor doesn't have a finite code
    // to share yet.
    NOT_FOUND = 5;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  repeated Referee recent_referees = 2;

  // page context to help client fetch next or previous page
  rpc.PageContextResponse page_context = 3;
}

message GetRefereesCountForActorRequest {
  // ActorId of the referrer for which finite code details need to be fetched.
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // The finiteCode for which to fetch count of referrals. If empty, referrals count will be fetched in general and not
  // limited to just one finite code.
  string finite_code_id = 2;
}

message GetRefereesCountForActorResponse {
  enum Status {
    // successfully fetched count of referrals
    OK = 0;

    // No referrals currently exist for the actor. This status will also be returned if the actor doesn't have a finite code
    // to share yet.
    NOT_FOUND = 5;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  int64 referees_count = 2;
}

message GenerateFiniteCodeForActorRequest {
  // ActorId of the referrer for which finite code details need to be fetched.
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // [Mandatory] type of the finite code to be generated
  enums.FiniteCodeType finite_code_type = 2;
}

message GenerateFiniteCodeForActorResponse {
  enum Status {
    // successfully generated finite code for the actor
    OK = 0;

    // actor hasn't performed the needful qualifying action, finite code generation cannot happen.
    FAILED_PRECONDITION = 9;

    // internal server error
    INTERNAL = 13;

    // finite code for the actor has already been created
    ALREADY_EXISTS = 6;
  }

  rpc.Status status = 1;

  // In case finite code is generated, the finite code object.
  FiniteCode finite_code = 2;
}

message ClaimFiniteCodeRequest {
  // ActorId of the referee for which finite code needs to be claimed
  string referee_actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // Finite code that needs to be claimed
  string finite_code = 2 [(validate.rules).string = {min_len: 10, max_len: 10}];
}

message ClaimFiniteCodeResponse {
  enum Status {
    // successfully claimed the finite code for the actor
    OK = 0;

    // finite code doesn't exist and hence cannot be claimed
    NOT_FOUND = 5;

    // finite code limit exhausted, i.e. claimed_count >= claim_limit for the finite code.
    RESOURCE_EXHAUSTED = 8;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // Finite code claim record in case the finite code is claimed successfully.
  FiniteCodeClaim finite_code_claim = 2;
}

message GetClaimedFiniteCodeForActorRequest {
  // ActorId for which if finite code claimed needs to be checked
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message GetClaimedFiniteCodeForActorResponse {
  enum Status {
    OK = 0;

    // no finite code found for actor in any flow, i.e. waitlist and referral
    NOT_FOUND = 5;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // Finite code claimed by actor
  // In case the claimed finite code is via the waitlist channel, claimed_count and claim_limit both will be 1. Also,
  // the skip_employment_check_privilege will be true
  FiniteCode finite_code = 2;
}

message GetReferralSummaryForReferrerRequest {
  // Referrer ActorId for which referral summary is to be fetched
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message GetReferralSummaryForReferrerResponse {
  enum Status {
    OK = 0;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // flag to determine referrer's eligibility for referral program
  bool is_eligible_for_referral = 2;

  // list of active finite codes for the referrer.
  // this will be nil in case the referrer is not eligible for referral program.
  repeated FiniteCode finite_codes = 3;
}

message IsActorEligibleForReferralRequest {
  // ActorId of the referrer for which finite code details need to be fetched.
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // [Mandatory] type of the finite code for which eligibility to be checked
  enums.FiniteCodeType finite_code_type = 2;
}

message IsActorEligibleForReferralResponse {
  enum Status {
    // actor eligible for referral
    OK = 0;

    // actor hasn't performed the needful qualifying action, actor not eligible for referral
    FAILED_PRECONDITION = 9;

    // internal server error
    INTERNAL = 13;

    // actor has failed eligibility criteria as avg eod balance less than minimum required
    FAILED_PRECONDITION_AVG_EOD_BALANCE = 101;

    // actor has failed eligibility criteria as minimum duration post onboarding not completed
    FAILED_PRECONDITION_ONBOARDING_DURATION = 102;

    // no avg eod balance found in db
    NO_AVG_EOD_BALANCE_FOUND = 103;

    // debit card pin not yet set by the user
    DEBIT_CARD_PIN_NOT_SET = 104;
  }

  // status used to check the eligibility of actor
  rpc.Status status = 1;

  // avg eod balance for all accounts of the actor
  // Note: this field will be null if actor is eligible for referral, and shouldn't be relied on then.
  google.type.Money amount = 2;

  // number of days since user onboarded successfully
  // Note: this field will be 0 if actor is eligible for referral, and shouldn't be relied on then.
  int32 days_since_onboarding = 3;
}

message UnlockInAppReferralForActorRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // [Mandatory] type of the finite code which needs to be unlocked for the actor
  enums.FiniteCodeType finite_code_type = 2;
}

message UnlockInAppReferralForActorResponse {
  rpc.Status status = 1;
}

message GetReferralsUnlockTimestampForActorRequest {
  string actor_id = 1;
  // [Mandatory] type of the finite code for which unlock timestamp needs fetched for actor
  enums.FiniteCodeType finite_code_type = 2;
}

message GetReferralsUnlockTimestampForActorResponse {
  rpc.Status status = 1;

  // referrals unlock timestamp for actor
  google.protobuf.Timestamp referrals_unlock_timestamp = 2;
}

message GetSegmentedReferralComponentsDetailsRequest {
  string actor_id = 1;

  // list of components for which we want to fetch the details for the actor
  repeated inappreferral.enums.Component components = 2;
  // app feature for which the component details are to be fetched
  enums.AppFeature app_feature = 3;
}

message GetSegmentedReferralComponentsDetailsResponse {
  rpc.Status status = 1;

  // contains component to component details. uint32 corresponds to`Component` (defined in inappreferrals/referrals_segmented_components)
  map<uint32, ComponentDetails> component_to_component_details_map = 2;
}

message CreateReferralsSegmentedComponentRequest {
  // id of segment for which the component is being added
  string segment_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // component for which we're creating segmented details
  inappreferral.enums.Component component = 2 [(validate.rules).enum = {not_in: [0]}];

  // details of the component
  ComponentDetails component_details = 3 [(validate.rules).message.required = true];

  // timestamp since which the details will be active
  google.protobuf.Timestamp active_since = 4 [(validate.rules).timestamp.required = true];

  // timestamp upto which the details will be active
  google.protobuf.Timestamp active_till = 5 [(validate.rules).timestamp.required = true];

  // app feature for which the component is enabled
  enums.AppFeature app_feature = 6 [(validate.rules).enum = {not_in: [0]}];
  // variant of the component
  enums.Variant variant = 7;
}

message CreateReferralsSegmentedComponentResponse {
  rpc.Status status = 1;

  // created referrals-segmented-component
  ReferralsSegmentedComponent referrals_segmented_component = 2;
}


message ReadReferralsSegmentedComponentRequest {
  // list of ids which for which the referrals segmented components are to be read
  repeated string ids = 1 [(validate.rules).repeated.min_items = 1];
}

message ReadReferralsSegmentedComponentResponse {
  enum Status {
    // successfully claimed
    OK = 0;

    // no referral details found for actor
    NOT_FOUND = 5;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // read referrals-segmented-components
  repeated ReferralsSegmentedComponent referrals_segmented_components = 2;
}

message UpdateReferralsSegmentedComponentRequest {
  // id of referrals-segmented-component that we want to update
  string id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // details of the component that we want to update.
  // Note: can be nil if details aren't to be updated
  ComponentDetails component_details = 2;

  // new timestamp since which the details will be active.
  // Note: can be nil if timestamp update is not required
  google.protobuf.Timestamp active_since = 3;

  // new timestamp upto which the details will be active.
  // Note: can be nil if timestamp update is not required
  google.protobuf.Timestamp active_till = 4;
}

message UpdateReferralsSegmentedComponentResponse {
  rpc.Status status = 1;

  // updated referrals-segmented-component
  ReferralsSegmentedComponent referrals_segmented_component = 2;
}

message DeleteReferralsSegmentedComponentRequest {
  // id of referrals-segmented-component that we want to delete
  string id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message DeleteReferralsSegmentedComponentResponse {
  rpc.Status status = 1;
}

message StoreReferralLinkForActorRequest {
  // actor-id for whom the referral link is generated
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // referral link for the actor
  string referral_link = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // vendor from which the referral link is generated, for e.g. Appsflyer Onelink
  inappreferral.enums.ReferralLinkVendor referral_link_vendor = 4;
  // timestamp when the referral link was generated.
  // If not provided, `current timestamp` will be assumed.
  google.protobuf.Timestamp generated_at = 3;
}

message StoreReferralLinkForActorResponse {
  rpc.Status status = 1;
}

message CreateNotificationConfigRequest {
  // notification config to be created
  inappreferral.notification.NotificationConfigInfo notification_config = 1 [(validate.rules).message.required = true];
}

message CreateNotificationConfigResponse {
  enum Status {
    // actor eligible for referral
    OK = 0;

    // invalid argument
    INVALID_ARGUMENT = 3;

    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // created notification config
  inappreferral.notification.NotificationConfigInfo notification_config = 2;
}

message UpdateNotificationConfigRequest {
  // id of the notification config
  string notification_config_id = 1 [(validate.rules).string.min_len = 1];
  // updated notification config
  inappreferral.notification.NotificationConfigInfo notification_config = 2;
  // field(s) to be updated
  // put non zero validation here
  repeated inappreferral.enums.NotificationConfigInfoFieldMask notification_config_update_mask = 3 [(validate.rules).repeated.min_items = 1];
}

message UpdateNotificationConfigResponse {
  enum Status {
    // successfully updated notification config
    OK = 0;
    // invalid argument
    INVALID_ARGUMENT = 3;
    // notification config with reference id not found
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  // status
  rpc.Status status = 1;
  // updated notification config
  inappreferral.notification.NotificationConfigInfo notification_config = 2;
}

message DeleteNotificationConfigRequest {
  // id of the notification config
  string notification_config_id = 1 [(validate.rules).string.min_len = 1];
}

message DeleteNotificationConfigResponse {
  enum Status {
    // successfully updated notification config
    OK = 0;
    // invalid argument
    INVALID_ARGUMENT = 3;
    // notification config with reference id not found
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  // status
  rpc.Status status = 1;
}
