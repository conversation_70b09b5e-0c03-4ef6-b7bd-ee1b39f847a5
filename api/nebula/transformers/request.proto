syntax = "proto3";

package nebula.transformers;

option go_package = "github.com/epifi/gamma/api/nebula/transformers";

enum TransformerType {
  TRANSFORMER_TYPE_UNSPECIFIED = 0;
  TRANSFORMER_TYPE_EXCLUDE_COLUMN = 1;
  TRANSFORMER_TYPE_REMOVE_NULL_UNICODE = 2;
  TRANSFORMER_TYPE_FLATTEN_JSON = 3;
  TRANSFORMER_TYPE_EXCLUDE_JSON_FIELDS = 4;
}

message TransformerConfig {
  TransformerType type = 1;

  oneof params {
    ExcludeColumnConfig exclude_column_config = 2;
    FlattenJsonConfig flatten_json_config = 3;
    ExcludeJsonFields exclude_json_fields = 4;
  }
}

message TransformerConfigs {
  repeated TransformerConfig transformers = 1;
}

message ExcludeColumnConfig {
  repeated string excluded_columns = 1;
}

message FlattenJsonConfig {
  string column = 1;
  string computed_columns_prefix = 2;
}

message ExcludeJsonFields {
  string column = 1;
  repeated string fields = 2;
}
