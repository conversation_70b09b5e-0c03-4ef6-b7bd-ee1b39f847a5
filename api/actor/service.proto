syntax = "proto3";

package actor;

import "api/accounts/account_type.proto";
import "api/actor/actor_pi_resolution.proto";
import "api/order/payment/notification/ecs_enach_details.proto";
import "api/order/payment/payment_protocol.proto";
import "api/pay/category/transaction_category.proto";
import "api/payment_instruments/payment_instrument.proto";
import "api/rpc/status.proto";
import "api/timeline/service.proto";
import "api/timeline/timeline.proto";
import "api/typesv2/actor.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/transaction_transfer_type.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/struct.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/actor";
option java_package = "com.github.epifi.gamma.api.actor";

// Actor service enables the relation between an actor and user/merchant.
//
// It also stores the relation between actors,
// usually in the form of a payment transfer
//
// A relation (actor_from, actor_to, pi_to) or (actor_from, actor_to, pi_from)
// is unique across this service
service Actor {
  // Create an actor
  rpc CreateActor (CreateActorRequest) returns (CreateActorResponse) {}

  // Get an actor by id
  rpc GetActorById (GetActorByIdRequest) returns (GetActorByIdResponse) {}

  // Get an actor by entity id (user id or merchant id)
  rpc GetActorByEntityId (GetActorByEntityIdRequest) returns (GetActorByEntityIdResponse) {}

  // Create a relation between 2 actors via a PI
  rpc CreateActorPiResolution (CreateActorPiResolutionRequest) returns (CreateActorPiResolutionResponse) {}

  // Resolve actor_to given actor_from and pi_to
  rpc ResolveActorTo (ResolveActorToRequest) returns (ResolveActorToResponse) {}

  // Resolve actor_from given actor_to and pi_from
  rpc ResolveActorFrom (ResolveActorFromRequest) returns (ResolveActorFromResponse) {}

  // GetPIsOfActorTo returns the list of PIs (Payment Instruments) belonging to actor_to, as known by actor_from.
  //
  // Note: `Limit` Parameter is only used For `Actor_EXTERNAL_MERCHANT`, where we fetch the PIs associated with the merchant using the `getMerchantPis()` method.
  // For other actor types, the limit is ignored, and we attempt to fetch all PIs
  // If the limit is not provided or provided as 0, we will fetch all PIs.
  rpc GetPIsOfActorTo (GetPIsOfActorToRequest) returns (GetPIsOfActorToResponse) {}

  // GetEntityDetailsByActorId returns the entity level details like mobile number, name etc. for a given actor id
  rpc GetEntityDetailsByActorId (GetEntityDetailsByActorIdRequest) returns (GetEntityDetailsByActorIdResponse) {}

  // BlockActor blocks the other actor for logged in actor and optionally mark as spam.
  // The `is_spam` flag in request is used while blocking an actor. If the flag is set, the actor is marked as a spam
  // in the block_actors_map, spam count of all the associated payment instruments are incremented by 1.
  // Note: ALREADY_PROCESSED is returned even if actor is already marked as blocked
  // The client is expected to use ReportSpamForBlockedActor RPC to report spam for an already blocked actor
  rpc BlockActor (BlockActorRequest) returns (BlockActorResponse) {}

  // UnblockActor unblocks the other actor for the logged in actor and unset the spam flags if spam was reported.
  //
  // If the blocked actor was found to be marked as spam, spam count of all the associated payment instruments are
  // decremented by 1.
  // Note: ALREADY_PROCESSED is returned even if actor is already unblocked
  rpc UnblockActor (UnblockActorRequest) returns (UnblockActorResponse) {}

  // ReportSpamForBlockedActor reports spam for an actor who is already blocked and increments the spam count
  // of all the associated payment instruments by 1.
  // If actor is not already blocked then corresponding response code is returned
  // Note: In case the other actor is already reported as spam by logged in actor, the RPC won't perform
  // any operation and simply returns ALREADY_PROCESSED.
  rpc ReportSpamForBlockedActor (ReportSpamForBlockedActorRequest) returns (ReportSpamForBlockedActorResponse) {}

  // GetRelationshipWithActor returns other actor relationship (BLOCKED/UNBLOCKED/REPORTED) with the current
  // logged in actor.
  rpc GetRelationshipWithActor (GetRelationshipWithActorRequest) returns (GetRelationshipWithActorResponse) {}

  // GetEntityDetails returns list of entity level details like mobile number, name etc. for list of actor id's.
  // If any of the entity details are not found for an actor, remaining entity details for other actors are returned.
  // In this case it is upto the caller to decide if this is an error or not.
  rpc GetEntityDetails (GetEntityDetailsRequest) returns (GetEntityDetailsResponse) {}

  // RPC to soft delete an actor from DB.
  rpc DeleteActor (DeleteActorRequest) returns (DeleteActorResponse);

  // BatchHardDeleteActor does hard delete of actor from the database and publishes deletion event to
  // intimate respective stack holders.
  // The RPC works on all or none principle for all eligible entries (eligible = ids for which entry is present in the DB).
  // i.e. actor deletion and deletion event publishing is done in an atomic block
  // **NOTE**
  // The method only supports deletion on epiFi wealth related actors at the moment.
  rpc BatchHardDeleteActor (BatchHardDeleteActorRequest) returns (BatchHardDeleteActorResponse);

  // BatchHardDeleteActorPiResolution does hard delete of actor pi resolution entry
  // The RPC works on all or none principle for all eligible entries (eligible = ids for which entry is present in the DB).
  // i.e. all the eligible entries are deleted in a single atomic block
  // **NOTE**
  // The method only supports deletion on epiFi wealth related actors at the moment.
  rpc BatchHardDeleteActorPiResolution (BatchHardDeleteActorPiResolutionRequest) returns (BatchHardDeleteActorPiResolutionResponse);

  // GetPayButtonStatus will read the data from map if pay button should be enable or disable in this case
  // if actor will not present in map we will send Record Not found
  rpc GetActorCapabilities (GetActorCapabilitiesRequest) returns (GetActorCapabilitiesResponse);

  // GetActorsByEntityIds will get the actors for the corresponding entity ids
  // Note: Since we have limit on payload passed in rpc request and response ( 4MB), we should pass actor ids in limit
  rpc GetActorsByEntityIds (GetActorsByEntityIdsRequest) returns (GetActorsByEntityIdsResponse);

  // BulkFetchActorToPis will get list of PI Ids for requested list of actors
  // There can be multiple PIs for a given actor_from, actor_to mapping
  // since actor_to Ids is expected in request, BulkFetchActorToPIs will return a map of actor_to Id to list of PIs associated
  rpc BulkFetchActorToPIs (BulkFetchActorToPIsRequest) returns (BulkFetchActorToPIsResponse) {}

  // ResolveOtherActorPiAndTimeline is a generic rpc called when any type of transaction is done and used to resolve/create  :
  // 1. external actor pi details 2. timeline between the actors
  // it is a self serving rpc i.e. any type of transaction (eg. credit_card txn , upi txn etc.) can call this rpc , it just have to plug its type in the rpc
  rpc ResolveOtherActorPiAndTimeline (ResolveOtherActorPiAndTimelineRequest) returns (ResolveOtherActorPiAndTimelineResponse) {};
}

message BulkFetchActorToPIsRequest {
  // Represents an actor who initiates a payment.
  string actor_from = 1 [(validate.rules).string.min_len = 1];

  // Represents list of actors to whom the payment is being made.
  repeated string actor_to = 2 [(validate.rules).repeated = {min_items: 1, unique: true}];
}

message BulkFetchActorToPIsResponse {
  enum Status {
    // request was successful
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // No PIs found
    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;
  // since there can be multiple PIs for a given actor_from, actor_to mapping
  // actor_pi_mapping is a map of actor_to and list of PIs associated
  map<string, google.protobuf.ListValue> actor_pi_mapping = 2;
}

message CreateActorRequest {
  api.typesv2.Actor.Type type = 1 [(validate.rules).enum = {not_in: [0]}];

  // User ID or Merchant ID based on the type of actor
  // In case of external actors, this will be empty
  string entity_id = 2;

  // Name to be present if type is ex-user or ex-merchant since we're not creating ex-user
  // and ex-merchant entity
  string name = 3;

  // if ownership is coming in request, actor is created with requested ownership
  // if ownership field is empty => actor will be created with default(~`EPIFI_TECH`) ownership
  api.typesv2.common.Ownership ownership = 4;
}

message CreateActorResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  api.typesv2.Actor actor = 2;
}

message GetActorByIdRequest {
  string id = 1;
}

message GetActorByIdResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  api.typesv2.Actor actor = 2;
}

message GetActorByEntityIdRequest {
  api.typesv2.Actor.Type type = 1;

  // User ID or Merchant ID based on the type of actor
  // In case of external actors, this will be empty
  string entity_id = 2;
}

message GetActorByEntityIdResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  api.typesv2.Actor actor = 2;
}

// actor_from and actor_to is mandatory.
// Exactly one of pi_to and pi_from must be present.
message CreateActorPiResolutionRequest {
  // Reference to actor id from actor service
  // It represents an actor who initiates a payment.
  // If pi_to is present, it represents an internal actor who initiates a payment
  // else if pi_from is present, it represents an (internal/external) actor
  // from whom payment was received
  string actor_from = 1 [(validate.rules).string.min_len = 1];

  // Reference to actor id from actor service
  // It represents an actor who receives a payment.
  string actor_to = 2 [(validate.rules).string.min_len = 1];

  // Reference to PI id from PI service
  // For outbound transfer (actor_from, actor_to, pi_to), it holds no significance
  // For inbound transfer (actor_from, actor_to, pi_from), it represents the PI
  // from which money will be debited from.
  string pi_from = 3;

  // Reference to PI id from PI service
  // For outbound transfer (actor_from, actor_to, pi_to), it represents the PI
  // in which money will be credited to.
  // For inbound transfer (actor_from, actor_to, pi_from), it holds no significance
  string pi_to = 4;
}

message CreateActorPiResolutionResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  ActorPiResolution actor_pi_resolution = 2;
}

message ResolveActorToRequest {
  // Represents an actor who initiates a payment.
  string actor_from = 1 [(validate.rules).string.min_len = 1];

  // Represents the PI in which money will be credited to.
  string pi_to = 2 [(validate.rules).string.min_len = 1];

  // Name of the actor to whom money is being transferred
  string actor_to_name = 3 [(validate.rules).string.min_len = 1];

  // ownership for actor; if actor is created during resolve
  api.typesv2.common.Ownership ownership = 4;
}

message ResolveActorToResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;

  // Represents an actor who receives a payment
  string actor_to = 2;
}

message ResolveActorFromRequest {
  // Represents an actor who received the payment
  string actor_to = 1 [(validate.rules).string.min_len = 1];

  // Represents the PI from which money was debited from
  string pi_from = 2 [(validate.rules).string.min_len = 1];

  // Name of the actor from whom money was received
  string actor_from_name = 3 [(validate.rules).string.min_len = 1];

  // ownership of the actor to be created
  api.typesv2.common.Ownership ownership = 4;
}

message ResolveActorFromResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // Represents an actor from whom payment was received
  string actor_from = 2;
}

message GetPIsOfActorToRequest {
  // Represents an actor who initiates a payment.
  string actor_from = 1 [(validate.rules).string.min_len = 1];

  // Represents an actor to whom the payment is being made.
  string actor_to = 2 [(validate.rules).string.min_len = 1];

  // Limit on the number of PIs to be fetched. if limit is not provided or provided as 0, we will fetch all PIs.
  // Note: `Limit` Parameter is only used For `Actor_EXTERNAL_MERCHANT`.For other actor types, the limit is ignored, and we attempt to fetch all PIs
  int32 limit = 3;
}

message GetPIsOfActorToResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;

  // List of PIs that belongs to actor_to, that actor_from knows of
  repeated string pi_ids = 2;
}

message GetEntityDetailsByActorIdRequest {
  // actor id for which we need the details
  string actor_id = 1;
}

message GetEntityDetailsByActorIdResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    // actor does not exist
    RECORD_NOT_FOUND = 5;
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // User ID or Merchant ID based on the type of actor
  // In case of external actors, this will be empty
  string entity_id = 2;

  // name of the entity (user/merchant)
  api.typesv2.common.Name name = 3;

  // mobile number of the entity (user/merchant)
  api.typesv2.common.PhoneNumber mobile_number = 4;

  // url profile image of the entity (user/merchant)
  string profile_image_url = 5;

  // email id of the entity (user/merchant)
  string email_id = 6;

  message Merchant {
    map<string, bool> capabilities = 1;
  }

  // Idea is to add entities separately and let the caller take decisions based on type of actor
  // More entities like external user, user, internal merchant can be added need basis
  oneof entity {
    Merchant merchant = 7;
  }

  // legal name of the entity
  api.typesv2.common.Name legal_name = 8;

  api.typesv2.ActorType type = 9;
}

message BlockActorRequest {
  // actor id for the logged in actor
  string current_actor_id = 1 [(validate.rules).string.min_len = 1];

  // actor id to be blocked
  string other_actor_id = 2 [(validate.rules).string.min_len = 1];

  // tells whether to report the user as spam while blocking
  bool is_spam = 3;
}

message BlockActorResponse {
  enum Status {
    OK = 0;
    // invalid request arguments
    INVALID_ARGUMENT = 3;
    // internal server error
    INTERNAL = 13;
    // actor is already blocked
    ALREADY_PROCESSED = 50;
  }
  rpc.Status status = 1;
}

message UnblockActorRequest {
  // actor id for the logged in actor
  string current_actor_id = 1 [(validate.rules).string.min_len = 1];

  // actor id to be unblocked
  string other_actor_id = 2 [(validate.rules).string.min_len = 1];
}

message UnblockActorResponse {
  enum Status {
    OK = 0;
    // invalid request arguments
    INVALID_ARGUMENT = 3;
    // internal server error
    INTERNAL = 13;
    // actor is already unblocked
    ALREADY_PROCESSED = 50;
  }
  rpc.Status status = 1;
}

message ReportSpamForBlockedActorRequest {
  // actor id for the logged in actor
  string current_actor_id = 1 [(validate.rules).string.min_len = 1];

  // blocked actor's identifier
  string blocked_actor_id = 2 [(validate.rules).string.min_len = 1];
}

message ReportSpamForBlockedActorResponse {
  enum Status {
    OK = 0;
    // invalid request arguments
    INVALID_ARGUMENT = 3;
    // internal server error
    INTERNAL = 13;
    // actor already marked spam once
    ALREADY_PROCESSED = 50;
    // the actor is not blocked
    ACTOR_NOT_BLOCKED = 100;
  }
  rpc.Status status = 1;
}

message GetRelationshipWithActorRequest {
  // actor id for the logged in actor
  string current_actor_id = 1 [(validate.rules).string.min_len = 1];

  // actor id against whom query is being made
  string other_actor_id = 2 [(validate.rules).string.min_len = 1];
}

message GetRelationshipWithActorResponse {
  enum Status {
    OK = 0;
    // invalid request arguments
    INVALID_ARGUMENT = 3;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  enum Relationship {
    RELATION_UNSPECIFIED = 0;
    // other actor is blocked by current actor
    BLOCKED = 1;
    // other actor is not blocked by current actor
    NOT_BLOCKED = 2;
    // other actor is blocked and reported by current actor
    REPORTED = 3;
  }

  // relationship between other actor and current logged in actor
  Relationship relationship = 2;
}

message GetActorsByEntityIdsRequest {
  // List of actor id's
  repeated string entity_ids = 1;
}

message GetActorsByEntityIdsResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;

    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;

  repeated api.typesv2.Actor actors = 2;
}

message GetEntityDetailsRequest {
  // List of actor id's
  repeated string actor_ids = 1;

  // flag to determine if entity details for soft-deleted users should be fetched or not.
  bool fetch_soft_deleted_users = 2;
}

message GetEntityDetailsResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  message EntityDetail {
    // User ID or Merchant ID based on the type of actor
    // In case of external actors, this will be empty
    string entity_id = 1;

    // name of the entity (user/merchant)
    api.typesv2.common.Name name = 2;

    // mobile number of the entity (user/merchant)
    api.typesv2.common.PhoneNumber mobile_number = 3;

    // url profile image of the entity (user/merchant)
    string profile_image_url = 4;

    // email id of the entity (user/merchant)
    string email_id = 5;

    // actor id passed in the request
    string actor_id = 6;

    message Merchant {
      map<string, bool> capabilities = 1;
    }

    // Idea is to add entities separately and let the caller take decisions based on type of actor
    // More entities like external user, user, internal merchant can be added need basis
    oneof entity {
      Merchant merchant = 7;
    }
    api.typesv2.ActorType entity_type = 8;
  }
  // List of entity details. The order of the entity details will be random and is independent of request passed.
  repeated EntityDetail entity_details = 2;
}

message DeleteActorRequest {
  string actor_id = 1;
}

message DeleteActorResponse {
  rpc.Status status = 1;
}


message BatchHardDeleteActorRequest {
  repeated string actor_ids = 1;
}

message BatchHardDeleteActorResponse {
  enum Status {
    OK = 0;
    // internal error
    INTERNAL = 13;
    // if mentioned actor identifiers are already deleted/not present in the data base.
    ALREADY_PROCESSED = 50;
  }
  // status of the request
  rpc.Status status = 1;
}

message BatchHardDeleteActorPiResolutionRequest {
  repeated string actor_ids = 1;
}

message BatchHardDeleteActorPiResolutionResponse {
  enum Status {
    OK = 0;
    // internal error
    INTERNAL = 13;
    // if mentioned actor identifiers are already deleted/not present in the data base.
    ALREADY_PROCESSED = 50;
  }
  // status of the request
  rpc.Status status = 1;
}

message GetActorCapabilitiesRequest {
  string actor_id = 1;
}

message GetActorCapabilitiesResponse {
  enum Status {
    OK = 0;
    // internal error
    INTERNAL = 13;

    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;
  // value true for enable and false for disable
  map<int32, bool> value = 2;
}

// If we are using it for ATM merchant creation and resolution points to be noted are:-
// 1. we have to pass transaction_category as ATM_WITHDRAWAL OR ATM DEPOSIT
// 2.AtmAddress, MerchantDetails and OtherActorName should be same as evaluated through parser in vendor notification
// 3.pi_identifier can be passed as card or can be left blank as we are creating it within the RPC
message ResolveOtherActorPiAndTimelineRequest {
  // actor id of the internal user
  string primary_actor_id = 1;

  // payment protocol eg. UPI, NEFT etc.
  order.payment.PaymentProtocol payment_protocol = 2;

  // credit/debit transaction
  api.typesv2.TransactionTransferType event_type = 3 [(validate.rules).enum = {not_in: [0]}];

  // type of account involved for the internal actor
  accounts.Type account_type = 4;

  // Vendor bank where transaction occurred
  vendorgateway.Vendor partner_bank = 5;

  // set of fields to uniquely identify an account
  message AccountIdentifier {
    string account_number = 1;

    string ifsc_code = 2;

    // type of the pi for eg. BANK_ACCOUNT, PARTIAL_BANK_ACCOUNT, GENERIC
    paymentinstrument.PaymentInstrumentType pi_type = 3;

    // Type of the account
    .accounts.Type account_type = 4;
  }

  message CardTransactionIdentifier {
    // 15 digit identification number to uniquely identify the merchant
    string mid = 1;
    string other_actor_name = 2;
  }

  // pi identifier for the other actor
  // for credit pi belonging to the payer
  // for debit pi belonging to the payee
  oneof pi_identifier {
    // in case of upi transactions, vpa of the other actor
    string vpa = 6;

    // in case of non-upi account details of other actor
    AccountIdentifier account = 7;

    // if pi id is present, other pi identifiers will be ignored
    string pi_id = 8;

    // in case of card transactions (debit/credit card) , we will require either MID or other actor name to resolve the pi
    CardTransactionIdentifier card_transaction_identifier = 9;
  }
  // ownership of the other actor pi (keep entity segregation in mind while passing this)
  // will be assigned tech if not passed
  paymentinstrument.Ownership pi_ownership = 10;

  timeline.Ownership timeline_ownership = 11;
  // merchant details in case of card transactions (both credit and debit card)
  MerchantDetails merchant_details = 12;

  string other_actor_name = 13;

  // transaction category (eg. POS,ATM etc.)
  pay.category.TransactionCategory transaction_category = 14;

  // address of the ATM machine in case of  ATM withdrawal or cash deposit.
  string atm_address = 15;

  // Enum to identify whether the timeline will be created for transactions created via new inbound notification or for transactions
  // getting updated via transaction backfill workflow
  timeline.TimelineResolutionSource timeline_resolution_source = 16;

  // details regarding the ecs_enach mandate transactions
  // it can be related to chrarges for these mandates also
  order.payment.notification.EcsEnachMandateDetails ecs_enach_details = 17;
}

message ResolveOtherActorPiAndTimelineResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // other actor pi id
  string other_actor_pi_id = 2;
  // timeline id resolved between 2 actors
  string timeline_id = 3;
}


// merchant details
message MerchantDetails {
  string name = 1;
  // merchant location (can be empty)
  string location = 2;
  // mcc code of the merchant
  string mcc_code = 3;
  // geo code of the merchant (can be empty)
  string geo_code = 4;
  // required for populating the address of the merchant
  // city in which transaction was done
  string city = 5;
  // alpha-2 country code for the country in which transaction was made
  string country_code = 6;
  // 15 digit identification number to uniquely identify the merchant
  string mid = 7;

  // terminal id of the Merchant
  string tid = 8;
}
