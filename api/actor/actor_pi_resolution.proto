syntax = "proto3";

package actor;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/actor";
option java_package = "com.github.epifi.gamma.api.actor";

// ActorPiResolution represents a relation between 2 actors, usually in the context of a payment transfer.
//
// A message for outbound transfer (actor_from, actor_to, pi_to) represents actor_from added
// actor_to as beneficiary via pi_to with an intent to make an outbound transfer.
//
// A message for inbound transfer (actor_from, actor_to, pi_from) represents actor_to received
// an incoming txn from actor_from via pi_from.
//
// A specific timeline (actor_from, actor_to) will be constructed by collating
// multiple such messages.
message ActorPiResolution {
  string id = 1;

  // Reference to actor id from actor service
  // It represents an actor who initiates a payment.
  // If pi_to is present, it represents an internal actor who initiates a payment
  // else if pi_from is present, it represents an (internal/external) actor
  // from whom payment was received
  string actor_from = 2;

  // Reference to actor id from actor service
  // It represents an actor who receives a payment.
  string actor_to = 3;

  // Reference to PI id from PI service
  // For outbound transfer (actor_from, actor_to, pi_to), it holds no significance
  // For inbound transfer (actor_from, actor_to, pi_from), it represents the PI
  // from which money will be debited from.
  string pi_from = 4;

  // Reference to PI id from PI service
  // For outbound transfer (actor_from, actor_to, pi_to), it represents the PI
  // in which money will be credited to.
  // For inbound transfer (actor_from, actor_to, pi_from), it holds no significance
  string pi_to = 5;

  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;
}
