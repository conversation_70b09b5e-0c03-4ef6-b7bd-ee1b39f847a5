syntax = "proto3";

package pan;

import "api/frontend/deeplink/deeplink.proto";
import "api/pan/enums.proto";
import "api/pan/pan.proto";
import "api/pan/epan/epan_attempt_events.proto";
import "api/pan/epan/epan_attempts.proto";
import "api/pan/scanned_pan/scanned_pan_attempt.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/date.proto";
import "api/vendorgateway/pan/service.proto";
import "api/vendorgateway/vendor.proto";
import "api/vendors/wealth/nsdl.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "validate/validate.proto";
import "api/dynamic_elements/dynamic_elements.proto";
import "api/typesv2/polling_request_info.proto";

option go_package = "github.com/epifi/gamma/api/pan";
option java_package = "com.github.epifi.gamma.api.pan";

service Pan {
  // InitiateEPAN rpc is used to initiate a new request for EPAN data
  rpc InitiateEPAN (InitiateEPANRequest) returns (InitiateEPANResponse);

  // UpdateEPANInfo rpc is used to update the status of EPAN from clients
  rpc UpdateEPANInfo (UpdateEPANInfoRequest) returns (UpdateEPANInfoResponse);

  // GetEPANAttempt rpc is used to get the state and EPAN data if available
  rpc GetEPANAttempt (GetEPANAttemptRequest) returns (GetEPANAttemptResponse);

  // GetPANAadharLinkStatus RPC returns PAN - Aadhaar link status.
  // First finds the info from the DB, if data is not found, calls the Federal Bank APIs.
  // If PAN & Aadhaar are not linked, the info is refreshed after every x days.
  // Returns Not Found response if the data is not available.
  rpc GetPANAadharLinkStatus (GetPANAadharLinkStatusRequest) returns (GetPANAadharLinkStatusResponse);

  // RegisterEPAN rpc creates an attempt for epan using client request id
  // if attempt already exist for id it will return status already exist
  rpc RegisterEPAN (RegisterEPANRequest) returns (RegisterEPANResponse);

  // PurgeEPANData rpc soft deletes all records where updatedAt < gives x days
  // it purges all record irrespective of their state
  rpc PurgeEPANData (PurgeEPANDataRequest) returns (PurgeEPANDataResponse);

  // GetAllEPANAttempts return all epan attempts for given actor id
  // it will return deleted record as well
  // it has limit off 500, if provided limit > 500 it will return 500 records only
  rpc GetAllEPANAttempts (GetAllEPANAttemptsRequest) returns (GetAllEPANAttemptsResponse);

  // rpc validates pan number via NSDL VG API and returns pan name and aadhaar link status
  rpc NsdlPanInquiry (NsdlPanInquiryRequest) returns (NsdlPanInquiryResponse);

  // Validate takes a PAN number and returns the
  // status corresponding to PAN if they're active, expired, fake etc.
  // Currently supports for NSDL and FEDERAL_BANK vendors
  rpc ValidatePAN (ValidatePANRequest) returns (ValidatePANResponse) {}

  // UploadEPAN takes an epan pdf in bytes and password and returns status
  // it uses inhouse API for ePAN validation
  rpc UploadEPAN (UploadEPANRequest) returns (UploadEPANResponse) {}

  rpc UploadPANToS3 (UploadPANToS3Request) returns (UploadPANToS3Response) {}

  // FetchPANFromS3 API is used to fetch PAN Data from S3 url.
  rpc FetchPANFromS3 (FetchPANFromS3Request) returns (FetchPANFromS3Response) {}

  // GetEPANNextAction to fetch next action after epan flow is completed
  rpc GetEPANNextAction (GetEPANNextActionRequest) returns (GetEPANNextActionResponse) {}

  // UploadScannedPanDocument RPC will be used to store details from a scanned PAN document.
  rpc UploadScannedPanDocument (UploadScannedPanDocumentRequest) returns (UploadScannedPanDocumentResponse) {}

  // GetScannedPanDocument RPC fetches the document details that are captured from a scanned PAN document
  rpc GetScannedPanDocument (GetScannedPanDocumentRequest) returns (GetScannedPanDocumentResponse) {}

  // ValidateV2 takes a PAN number, associated name and dob and returns the
  // status corresponding to PAN if they're active, expired, fake etc. and if name / dob match with pan records.
  // It also stores the pan-aadhar seeding status in user intel.
  // Currently supports for NSDL and FEDERAL_BANK vendors
  rpc ValidateV2 (ValidateV2Request) returns (ValidateV2Response);

  // RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {}

  // RPC used by the Dynamic Elements service to callback on user action on a dynamic element
  // ActorId and ElementId are mandatory parameters in the Request
  // Response contains status code
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no element exists with the given ElementId
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the callback is registered successfully
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse) {}

  // GetNudges will be used to return map of nudges from PAN Service
  // ActorId is mandatory parameters in the Request
  rpc GetNudges (GetNudgesRequest) returns (GetNudgesResponse) {}

  // GetPanUpdateNextAction will be used to return next action for PAN Update flow
  rpc GetPanUpdateNextAction (GetPanUpdateNextActionRequest) returns (GetPanUpdateNextActionResponse) {}

  // Fetch Profile (name, dob) from PAN
  rpc FetchProfileFromPan (FetchProfileFromPanRequest) returns (FetchProfileFromPanResponse);
}

message FetchProfileFromPanRequest {
  vendorgateway.Vendor vendor = 1;
  string actor_id = 2;
  string pan = 3;
}

message FetchProfileFromPanResponse {
  rpc.Status status = 1;
  api.typesv2.common.Name pan_name = 2;
  api.typesv2.common.Date dob = 3;
}

message GetPanUpdateNextActionRequest {
  string actor_id = 1;
  PanUpdateClientState client_last_state = 2;
  api.typesv2.PollingRequestInfo polling_request_info = 3;
}

message GetPanUpdateNextActionResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
  api.typesv2.PollingResponseInfo polling_response_info = 3;
}

message GetNudgesRequest {
  string actor_id = 1;
}

message GetNudgesResponse {
  rpc.Status status = 1;
  // Key is string value of Nudge Type
  // api/pan/enums.proto
  map<string, Nudge> nudges = 2;
}

message UploadEPANRequest {
  // Binary data of the EPAN document in PDF format.
  bytes data = 1;
  // Password required to open the EPAN document.
  string password = 2;
  // client_request_id will be propagated by client that will sent in UPLOAD_EPAN_BOTTOM_SHEET screen options by backend
  string client_request_id = 3;
  string actor_id = 4;
}

message UploadEPANResponse {
  rpc.Status status = 1;
}

message ValidatePANRequest {
  string actor_id = 1;
  string pan_number = 2 [(validate.rules).string.len = 10];
  vendorgateway.Vendor vendor = 3;
}

message ValidatePANResponse {
  rpc.Status status = 1;
  vendorgateway.pan.Record record = 2;
}


message NsdlPanInquiryRequest {
  string pan_number = 1 [(validate.rules).string.min_len = 1];
}

message NsdlPanInquiryResponse {
  rpc.Status status = 1;
  // pan status of user
  vendors.wealth.NsdlPanStatus pan_status = 2;
  // aadhaar seeding status of user
  vendors.wealth.NsdlAadhaarStatus aadhaar_status = 3;
  api.typesv2.common.Name name = 4;
  string last_updated_date = 5;
  // name on pan card
  string pan_card_name = 6;
  string err_msg = 7;
}


message InitiateEPANRequest {
  string actor_id = 1;
  string client_request_id = 2;
}

message InitiateEPANResponse {
  enum Status {
    OK = 0;
    UPDATE_NOT_PERMISSIBLE = 101;
  }
  rpc.Status status = 1;
  string client_req_id = 2;
  string karza_token = 3;
  string pan_number = 4;
  google.type.Date dob = 5;
}

message UpdateEPANInfoRequest {
  string client_req_id = 1;
  epan.EPANData e_pan_data = 2;
  string event_name = 3;
  repeated epan.UpdateEPANInfoParamMask param_mask = 4;
  string actor_id = 5;
  epan.UpdateSource update_source = 6;
}

message UpdateEPANInfoResponse {
  enum Status {
    OK = 0;
    UPDATE_NOT_PERMISSIBLE = 101;
  }
  rpc.Status status = 1;
}

message GetEPANAttemptRequest {
  string actor_id = 1;
  string client_req_id = 2;
}

message GetEPANAttemptResponse {
  rpc.Status status = 1;
  epan.EPANAttempt e_pan_attempt = 2;
}

message GetPANAadharLinkStatusRequest {
  string actor_id = 1;
  // if force_cache set to true then will fetch the aadhaar seeding from db only,
  // there will not be any vendor call in case no record found or stale record
  // recommended value is false
  bool force_cache = 2;
}

message GetPANAadharLinkStatusResponse {
  enum Status {
    // pan aadhar link status is available
    OK = 0;
    // pan aadhar link status is unavailable from vendor
    RECORD_NOT_FOUND = 5;
    // invalid pan or pan is deactivated
    INVALID_PAN = 101;
  }

  rpc.Status status = 1;

  PANAadharLinkStatus pan_aadhar_link_status = 2;

  // time at which the pan aadhaar link status was last synced at
  google.protobuf.Timestamp synced_at = 3;
}

message RegisterEPANRequest {
  string actor_id = 1;
  // client req id should be unique for each attempt
  string client_req_id = 2;
}

message RegisterEPANResponse {
  rpc.Status status = 1;
}

message PurgeEPANDataRequest {
  google.protobuf.Timestamp expiry_time = 1;
}

message PurgeEPANDataResponse {
  rpc.Status status = 1;
}

message GetAllEPANAttemptsRequest {
  string actor_id = 1;
  int32 limit = 2;
}

message GetAllEPANAttemptsResponse {
  rpc.Status status = 1;
  repeated epan.EPANAttempt e_pan_attempts = 2;
}

message UploadPANToS3Request {
  PanType pan_type = 1;
  // pan_data in bytes format.
  bytes pan_data = 2;
  // filename for the s3 file being uploaded.
  string file_name = 3;
  // flow responsible for uploading the given PAN to S3.
  UploadPANFlow flow = 4;
}

message UploadPANToS3Response {
  rpc.Status status = 1;
  string s3_path_url = 2;
}

message GetEPANNextActionRequest {
  string actor_id = 1;
  // client_req_id sent by backend in UPLOAD_FILE screen options
  string client_req_id = 2;
  // blob contains next_action and flow related data, sent by backend in UPLOAD_FILE screen options
  // will be to and fro between client and server
  bytes blob = 3;
}

message GetEPANNextActionResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message FetchPANFromS3Request {
  string filepath = 1;
}

message FetchPANFromS3Response {
  rpc.Status status = 1;
  bytes pan_data = 2;
}

message UploadScannedPanDocumentRequest {
  string actor_id = 1;
  bytes pan_image = 2;
  string client_req_id = 3;
}

message UploadScannedPanDocumentResponse {
  enum Status {
    OK = 0;
    // PAN_DETAILS_VALIDATION_FAILED is returned when validation for extracted fields from the scanned pan image fails.
    PAN_DETAILS_VALIDATION_FAILED = 100;
  }
  rpc.Status status = 1;
}

message GetScannedPanDocumentRequest {
  oneof identifier {
    // fetch the latest scanned pan document details for the actor.
    string actor_id = 1;
    string client_req_id = 2;
  }
  bool want_image_in_base64 = 3;
}

message GetScannedPanDocumentResponse {
  rpc.Status status = 1;
  scannedpan.ScannedPanAttempt scanned_pan_attempt = 2;
}

message ValidateV2Request {
  vendorgateway.Vendor vendor = 1;
  string actor_id = 2;
  // pan number that needs to be validated
  string pan = 3;
  // name as written on the pan card
  string name = 4;
  // date of birth as in the pan records
  google.type.Date dob = 5;
}

message ValidateV2Response {
  rpc.Status status = 1;
  // pan_valid returns true if pan is existing and active
  api.typesv2.common.BooleanEnum pan_valid = 2;
  // name_match returns true if entered name matches with the records with the vendor
  api.typesv2.common.BooleanEnum name_match = 3;
  // dob_match returns true if dob entered matches with vendor's records
  api.typesv2.common.BooleanEnum dob_match = 4;
  // pan-aadhar seeding status
  PANAadharLinkStatus pan_aadhar_link_status = 5;
}
