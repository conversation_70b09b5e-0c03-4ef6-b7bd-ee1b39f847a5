//go:generate gen_sql -types=GuardianInformation,DataVerificationDetails
syntax = "proto3";

package pan.scannedpan;

import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/nominee.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/pan/scannedpan";
option java_package = "com.github.epifi.gamma.api.pan.scannedpan";

message ScannedPanAttempt {
  string actor_id = 1;
  string pan = 2;
  // request id associated with the scan attempt
  string client_req_id = 3;
  // name of the customer on the pan
  api.typesv2.common.Name pan_name = 4;
  // guardian information as per PAN
  GuardianInformation guardian_info = 5;
  // customer DOB as per PAN
  google.type.Date pan_dob = 6;
  // customer's image on the PAN
  api.typesv2.common.Image user_pan_image = 7;
  // date of issue of the PAN
  google.type.Date date_of_issue = 8;
  // customer's signature image on the PAN.
  api.typesv2.common.Image pan_sign_image = 9;
  // the scanned pan image.
  api.typesv2.common.Image pan_image = 10;
  DataVerificationDetails data_verification_details = 11;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  int64 deleted_at_unix = 15;
}

message GuardianInformation {
  api.typesv2.common.Name guardian_name = 1;
  api.typesv2.RelationType guardian_relation = 2;
}

message DataVerificationDetails {
  message ConfidenceValues {
    // confidence value that the document is a pan.
    double pan_confidence = 1;
    // confidence value that the extracted fields are valid.
    double extracted_field_confidence = 2;
  }
  ConfidenceValues confidence_values = 1;
}
