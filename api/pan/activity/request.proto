syntax = "proto3";

import "api/celestial/activity/header.proto";
import "api/pan/epan/epan_attempts.proto";

package pan.activity;

option go_package = "github.com/epifi/gamma/api/pan/activity";
option java_package = "com.github.epifi.gamma.api.pan.activity";

message EpanEventSyncRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message EpanEventSyncResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  epan.EPANState e_pan_state = 2;
  // identifier to indicate whether the epan download was successful or not
  epan.EPANSubState e_pan_sub_state = 3;
}

message EpanDataSyncRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message EpanDataSyncResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdateEpanAttemptRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message UpdateEpanAttemptResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}
