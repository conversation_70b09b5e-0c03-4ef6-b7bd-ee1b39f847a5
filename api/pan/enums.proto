syntax = "proto3";

package pan;

option go_package = "github.com/epifi/gamma/api/pan";
option java_package = "com.github.epifi.gamma.api.pan";

enum PANAadharLinkStatus {
  PAN_AADHAR_STATUS_UNSPECIFIED = 0;
  // Aadhaar Seeding is Successful.
  PAN_AADHAR_STATUS_LINKED = 1;
  // Aadhaar Seeding is Unsuccessful or <PERSON><PERSON><PERSON><PERSON> is not seeded.
  PAN_AADHAR_STATUS_NOT_LINKED = 2;
  // Aadhaar seeding Not applicable or the user is exempted from linking aadhaar
  PAN_AADHAAR_STATUS_NA_OR_EXEMPT = 3;
}

enum PanType {
  PAN_TYPE_UNSPECIFIED = 0;

  PAN_TYPE_PHYSICAL_PAN = 1;

  PAN_TYPE_EPAN = 2;
}

enum UploadPANFlow {
  UPLOAD_PAN_FLOW_UNSPECIFIED = 0;

  // pan document uploaded during Biometric KYC customer due diligence process.
  UPLOAD_PAN_FLOW_BKYC_CUSTOMER_DUE_DILIGENCE = 1;
  // pan document uploaded during VKYC for pre validations.
  UPLOAD_PAN_FLOW_VKYC = 2;
}

enum NudgeType {
  NUDGE_TYPE_UNSPECIFIED = 0;
  // Nudge to update PAN document.
  NUDGE_TYPE_PAN_UPDATE_PROFILE_TOP_BANNER = 1;
}

enum PanUpdateClientState {
  PAN_UPDATE_CLIENT_STATE_UNSPECIFIED = 0;
  PAN_UPDATE_CLIENT_STATE_INITIATED = 1;
  PAN_UPDATE_CLIENT_STATE_OPTIONS_SCREEN = 2;
  PAN_UPDATE_CLIENT_STATE_EPAN_ENTRY = 3;
  PAN_UPDATE_CLIENT_STATE_EPAN_VERIFICATION = 4;
  PAN_UPDATE_CLIENT_STATE_SCANNED_PAN_ENTRY = 5;
  PAN_UPDATE_CLIENT_STATE_SCANNED_PAN_VERIFICATION = 6;
  PAN_UPDATE_CLIENT_STATE_EKYC = 7;
  PAN_UPDATE_CLIENT_STATE_PROFILE_UPDATE = 8;
}
