//go:generate gen_sql -types=Metadata,PanUpdateStatus
syntax = "proto3";

package pan.panupdate;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/pan/panupdate";
option java_package = "com.github.epifi.gamma.api.pan.panupdate";

message PanUpdateAttempt {
  string id = 1;
  string actor_id = 2;
  string pan_number = 3;
  PanUpdateStatus status = 4;
  Metadata metadata = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  int64 deleted_at_unix = 8;
}

message Metadata {
  ProfileUpdateDetails profile_update_details = 1;
  // ekyc_client_req_id is ekycrrn
  string ekyc_client_req_id = 2;
  string epan_attempt_id = 3;
  string scan_pan_attempt_id = 4;
}

message ProfileUpdateDetails {
  string profile_update_started_at = 1;
  string client_req_id = 2;
}

enum PanUpdateStatus {
  PAN_UPDATE_STATUS_UNSPECIFIED = 0;
  PAN_UPDATE_STATUS_IN_PROGRESS = 1;
  PAN_UPDATE_STATUS_COMPLETED = 2;
  PAN_UPDATE_STATUS_FAILED = 3;
}

enum PanUpdateAttemptFieldMask {
  PAN_UPDATE_ATTEMPT_FIELD_MASK_UNSPECIFIED = 0;
  PAN_UPDATE_ATTEMPT_FIELD_MASK_STATUS = 1;
  PAN_UPDATE_ATTEMPT_FIELD_MASK_METADATA = 2;
}
