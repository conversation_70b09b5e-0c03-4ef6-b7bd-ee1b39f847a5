//go:generate gen_sql -types=AddressLines
syntax = "proto3";

package stockguardian.address;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/address";

message Address {
  string id = 1;
  // Unstructured address lines describing the lower levels of an address.
  AddressLines address_lines = 2;
  // Generally refers to the city/town portion of the address.
  // Examples: US city, IT comune, UK post town.
  // In regions of the world where localities are not well defined or do not fit
  // into this structure well, leave locality empty and use address_lines.
  string locality = 3;
  // Highest administrative subdivision which is used for postal
  // addresses of a country or region.
  // For example, this can be a state, a province, an oblast, or a prefecture.
  // Specifically, for Spain this is the province and not the autonomous
  // community (e.g. "Barcelona" and not "Catalonia").
  // Many countries don't use an administrative area in postal addresses. E.g.
  // in Switzerland this should be left unpopulated.
  string administrative_area = 4;
  // Optional. Postal code of the address. Not all countries use or require
  // postal codes to be present, but where they are used, they may trigger
  // additional validation with other parts of the address (e.g. state/zip
  // validation in the U.S.A.).
  string postal_code = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp deleted_at = 7;
}

message AddressLines {
  repeated string address_lines = 1;
}
