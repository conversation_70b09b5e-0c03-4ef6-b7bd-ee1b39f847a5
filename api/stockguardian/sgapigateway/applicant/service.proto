syntax = "proto3";

package stockguardian.sgapigateway.applicant;

import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/name.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapigateway/applicant";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgapigateway.applicant";

service ApplicantService {
  rpc GetApplicant (GetApplicantRequest) returns (GetApplicantResponse);
  rpc CreateApplicant (CreateApplicantRequest) returns (CreateApplicantResponse);
}

message GetApplicantRequest {
  ApplicantIdentifier identifier = 1;
}

message ApplicantIdentifier {
  message PrimaryDetails {
    string PAN = 1;
    api.typesv2.common.PhoneNumber phone_number = 2;
  }

  oneof Identifier {
    PrimaryDetails primary_details = 1;
    string applicant_id = 2;
  }
}

message GetApplicantResponse {
  rpc.Status status = 1;
  Applicant applicant = 2;
}

message CreateApplicantRequest {
  string client_request_id = 1;
  string PAN = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
  // customer's declared name passed by LSP to RE
  api.typesv2.common.Name name = 4;
  // email of the customer which was authenticated by LSP
  string email = 5;
}

message CreateApplicantResponse {
  rpc.Status status = 1;
  Applicant applicant = 2;
}

message Applicant {
  string id = 1;
  string client_request_id = 2;
  api.typesv2.common.PhoneNumber phone_number = 3;
  string PAN = 4;
}
