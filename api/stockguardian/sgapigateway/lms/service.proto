syntax = "proto3";

package stockguardian.sgapigateway.lms;

import "api/rpc/status.proto";
import "api/stockguardian/sgapigateway/lms/enums/enums.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgapigateway/lms";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgapigateway.lms";

service Lms {
  // CalculateLoanSchedule can be used for calculating loan schedule before creating the loan in the system.
  rpc CalculateLoanSchedule (CalculateLoanScheduleRequest) returns (CalculateLoanScheduleResponse);
  // GetLoanSchedule fetches the repayment schedule of an already disbursed loan.
  rpc GetLoanSchedule (GetLoanScheduleRequest) returns (GetLoanScheduleResponse);
  // GetLoanCancellationDetails returns cancellation amount if cancellation is allowed,
  // returns FAILED_PRECONDITION if cancellation is not allowed
  rpc GetLoanCancellationDetails (GetLoanCancellationDetailsRequest) returns (GetLoanCancellationDetailsResponse);
  // GetLoanForeclosureDetails returns foreclosure amount for a loan,
  // will be 0 if the loan is already closed
  rpc GetLoanForeclosureDetails (GetLoanForeclosureDetailsRequest) returns (GetLoanForeclosureDetailsResponse);
  // RecordLoanRepayment can be used to record repayments towards a loan in the LMS system,
  // based on the payment amount and date, the loan can either Foreclosed or a Regular repayment will be recorded
  // In case of cancellation:
  // returns FAILED_PRECONDITION if cancellation is not allowed
  // returns INVALID_ARGUMENT if the cancellation amount is not correct
  rpc RecordLoanRepayment (RecordLoanRepaymentRequest) returns (RecordLoanRepaymentResponse);
  // InitiateBatchRecurringPaymentExecution is a gateway API to internal lms service InitiateBatchRecurringPaymentExecution
  rpc InitiateBatchRecurringPaymentExecution (InitiateBatchRecurringPaymentExecutionRequest) returns (InitiateBatchRecurringPaymentExecutionResponse);
}

message InitiateBatchRecurringPaymentExecutionRequest {
  enums.RecurringPaymentExecutionType execution_type = 1;
  oneof batch_input {
    // input csv file containing loan accounts and corresponding due amounts need to be passed in case of finflux
    bytes input_file = 2;
  }
}

message InitiateBatchRecurringPaymentExecutionResponse {
  rpc.Status status = 1;
  // identifier that can be used to fetch the executions that are initiated in this batch
  string batch_id = 2;
}

message RecordLoanRepaymentRequest {
  // loan account identifier shared with LSP
  string loan_account_id = 1 [(validate.rules).string.min_len = 1];
  // date on which amount was credited to the NBFC pool account
  // this should be in IST
  google.type.Date transaction_date = 2 [(validate.rules).message.required = true];
  google.type.Money amount = 3 [(validate.rules).message.required = true];
  // Unique Transaction Reference number
  string transaction_utr = 4 [(validate.rules).string.min_len = 1];
  lms.enums.PaymentProtocol payment_protocol = 5 [(validate.rules).enum = {not_in: [0]}];
  // Denotes a unique request id with which the client initiated the payment posting request.
  string client_request_id = 6 [(validate.rules).string.min_len = 1];
  RepaymentType repayment_type = 7;
  enum RepaymentType {
    REPAYMENT_TYPE_UNSPECIFIED = 0;
    // this should be used in case user wants to cancel the loan
    // if not explicitly specified, repayment will be considered as a regular repayment and allocated towards EMIs based on allocation strategy
    REPAYMENT_TYPE_CANCELLATION = 1;
  }
}

message RecordLoanRepaymentResponse {
  rpc.Status status = 1;
}

message GetLoanForeclosureDetailsRequest {
  // loan account identifier shared with LSP
  string loan_account_id = 1 [(validate.rules).string.min_len = 1];
  // foreclosure amount will be calculated as on foreclosure_date
  // this should be in IST
  google.type.Date foreclosure_date = 2 [(validate.rules).message.required = true];
}

message GetLoanForeclosureDetailsResponse {
  rpc.Status status = 1;
  google.type.Money foreclosure_amount = 2;
  google.type.Money interest_amount = 3;
  google.type.Money principal_amount = 4;
  google.type.Money other_charges = 5;
  google.type.Money penalty_charges = 6;
  google.type.Money fee_charges = 7;
}

message GetLoanCancellationDetailsRequest {
  // loan account identifier shared with LSP
  string loan_account_id = 1 [(validate.rules).string.min_len = 1];
  // cancellation amount will be calculated as on cancellation_date
  // this should be in IST
  google.type.Date cancellation_date = 2 [(validate.rules).message.required = true];
}

message GetLoanCancellationDetailsResponse {
  rpc.Status status = 1;
  google.type.Money cancellation_amount = 2;
}

message CalculateLoanScheduleRequest {
  // identifier for the loan product
  string product_id = 1 [(validate.rules).string.min_len = 1];
  google.type.Money principal_amount = 2 [(validate.rules).message.required = true];
  // annualised interest rate
  double interest_rate = 3;
  // tenure in months
  int32 tenure_in_months = 4;
  double processing_fee_percentage_including_gst = 5;
  google.type.Date expected_disbursement_date = 6;
}

message CalculateLoanScheduleResponse {
  rpc.Status status = 1;
  google.type.Money total_repayment_expected = 2;
  google.type.Money net_disbursal_amount = 3;
  google.type.Money broken_period_interest = 4;
  double annual_percentage_rate = 5;
  google.type.Money total_interest_charged = 6;
  google.type.Money total_fee_charges_charged = 7;
  google.type.Money total_penalty_charges_charged = 8;
  google.type.Date loan_maturity_date = 9;
  repeated LoanInstallment installments = 10;
  google.type.Money charges_due_at_disbursement = 11;
  message LoanInstallment {
    google.type.Date due_date = 1;
    google.type.Money principal = 2;
    google.type.Money interest = 3;
    google.type.Money fees_charges = 4;
    google.type.Money penalty_charges = 5;
    google.type.Money total_due = 6;
    int32 installment_number = 7;
  }
}

message GetLoanScheduleRequest {
  oneof loan_identifier {
    option (validate.required) = true;
    // loan account identifier shared with LSP
    string loan_account_id = 1;
    // identifier used for communication between Application service and LSP
    string loan_application_id = 2;
  }
}

message GetLoanScheduleResponse {
  rpc.Status status = 1;

  message LoanInstallment {
    int32 installment_number = 1;
    google.type.Date due_date = 2;
    google.type.Money principal_due = 3;
    google.type.Money interest_due = 4;
    google.type.Money fees_charges_due = 5;
    google.type.Money penalty_charges_due = 6;
    google.type.Money principal_paid = 7;
    google.type.Money interest_paid = 8;
    google.type.Money fees_charges_paid = 9;
    google.type.Money penalty_charges_paid = 10;
    google.type.Money total_due = 11;
    google.type.Money total_paid = 12;
    // true if all the EMI components (P, I & C) are completely paid off
    bool is_complete = 13;
    // date when all the components of the emi (P, I & C) are cleared off
    google.type.Date obligations_met_on = 14;
  }
  repeated LoanInstallment loan_installments = 2;

  // denotes the outstanding amount summary of the given loan.
  OutstandingAmountSummary outstanding_amount_summary = 3;
  // denotes the total overpaid amount towards the given loan.
  google.type.Money total_overpaid_amount = 4;
  // can be used to show to the user
  // this will be same as the loan id that will be printed on all loan documents
  string customer_loan_id = 5;
  google.type.Money net_disbursal_amount = 6;
  google.type.Money total_repayment_expected = 7;
  google.type.Date disbursement_date = 8;
  // id to be used in all servicing APIs
  string loan_account_id = 9;

  message OutstandingAmountSummary {
    // denotes the total outstanding amount of the loan.
    google.type.Money total_outstanding_amount = 1;
    // denotes the principal component of the outstanding amount.
    google.type.Money principal_component = 2;
    // denotes the interest component of the outstanding amount.
    google.type.Money interest_component = 3;
    // denotes the fees and charges component of the outstanding amount.
    google.type.Money fees_charges_component = 4;
    // denotes the penalty charges component of the outstanding amount.
    google.type.Money penalty_charges_component = 5;
  }
}
