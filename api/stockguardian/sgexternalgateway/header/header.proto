syntax = "proto3";

package stockguardian.sgexternalgateway.header;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgexternalgateway/header";

// LoanHeader contains common header information expected in authenticated requests.
// This is typically populated automatically by an interceptor based on the provided auth token.
message LoanHeader {
  // Client ID associated with the authenticated partner making the request.
  string client_id = 1;
}
