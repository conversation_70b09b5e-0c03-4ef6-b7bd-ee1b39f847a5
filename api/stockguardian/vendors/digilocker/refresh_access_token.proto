syntax = "proto3";

package stockguardian.vendors.digilocker;

// Go package path for the generated code
option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/digilocker";


//Request message for refreshing an access token.
message RefreshAccessTokenRequest {
  // Refresh token to obtain a new access token
  string refresh_token = 1 [json_name = "refresh_token"];
  // grant type is method to refresh access token (e.g., 'refresh_token')
  string grant_type = 2 [json_name = "grant_type"];
  // Client ID for the application
  string client_id = 3 [json_name = "client_id"];
  // Client secret for the application
  string client_secret = 4 [json_name = "client_secret"];
  // Redirect URI used in OAuth flow to get authorization code
  string redirect_uri = 5 [json_name = "redirect_uri"];
}

// Response message containing refreshed access token details.
message RefreshAccessTokenResponse {
  // refreshed access token
  string access_token = 1 [json_name = "access_token"];
  // The duration in seconds for which the access token is valid.
  int32 expires_in = 2 [json_name = "expires_in"];
  // Type of token (e.g., 'bearer')
  string token_type = 3 [json_name = "token_type"];
  // The permissions associated with the token.
  string scope = 4 [json_name = "scope"];
  // The timestamp in UNIX format indicating when the user's consent expires.
  int64 consent_valid_till = 5 [json_name = "consent_valid_till"];
  // New refresh token
  string refresh_token = 6 [json_name = "refresh_token"];
  // DigiLocker Id of the user account.
  string digilocker_id = 7 [json_name = "digilockerid"];
  // Name of the user
  string name = 8 [json_name = "name"];
  // Date of birth of the user, format: DDMMYYYY
  string dob = 9 [json_name = "dob"];
  // Gender of the user, values: M, F, T
  string gender = 10 [json_name = "gender"];
  // Flag indicating whether e-Aadhaar is present for the user, values: Y, N.
  string e_aadhaar = 11 [json_name = "eaadhaar"];
  // DigiLocker account reference key
  string reference_key = 12 [json_name = "reference_key"];
}
