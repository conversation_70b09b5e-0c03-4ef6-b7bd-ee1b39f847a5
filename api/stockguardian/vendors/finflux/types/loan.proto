syntax = "proto3";

package stockguardian.vendors.finflux.types;

import "api/stockguardian/vendors/finflux/types/common.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/finflux/types";
option java_package = "com.github.epifi.gringott.api.stockguardian.vendors.finflux.types";

// Example JSON:
//  {
//        "principal": 10000.00,
//        "numberOfRepayments": 12,
//        "periodFrequencyType": {
//            "id": 2,
//            "code": "periodFrequencyType.months",
//            "value": "MONTHS"
//        },
//        "repayEvery": 1,
//        "interestRate": 20,
//        "calculatedEmiAmount": 926.00,
//        "principalGrace": 0,
//        "interestPaymentGrace": 0,
//        "interestFreeGrace": 0,
//        "brokenPeriodInterest": 0.00,
//        "emi": 926.00
//  }
message LoanTerms {
  double principal = 1;
  int32 number_of_repayments = 2 [json_name = "numberOfRepayments"];
  // e.g.: {"id": 2, "code": "periodFrequencyType.months", "value": "MONTHS"}
  Enum period_frequency_type = 3 [json_name = "periodFrequencyType"];
  int32 repay_every = 4 [json_name = "repayEvery"];
  double interest_rate = 5 [json_name = "interestRate"];
  double calculated_emi_amount = 6 [json_name = "calculatedEmiAmount"];
  double principal_grace = 7 [json_name = "principalGrace"];
  double interest_payment_grace = 8 [json_name = "interestPaymentGrace"];
  double interest_free_grace = 9 [json_name = "interestFreeGrace"];
  double broken_period_interest = 10 [json_name = "brokenPeriodInterest"];
  double emi = 11 [json_name = "emi"];
}

// Example JSON:
//    {
//        "id": 16,
//        "loanId": 16,
//        "isFldg": false,
//        "additionalInterestComputationType": {
//            "id": 4,
//            "code": "standard",
//            "value": "Standard"
//        },
//        "courseOnInterestPayment": 0,
//        "additionalGraceOnInterestPayment": 0,
//        "courseOnPrincipalPayment": 0,
//        "additionalGraceOnPrincipalPayment": 0,
//        "isInterestTypeConvertable": false
//    }
message LoanAdditionalDetails {
  // e.g. 16
  int32 id = 1 [json_name = "id"];
  // e.g. 16
  int32 loan_id = 2 [json_name = "loanId"];
  // e.g. false
  bool is_fldg = 3 [json_name = "isFldg"];
  // e.g.: {"id": 4, "code": "standard", "value": "Standard"}
  types.Enum additional_interest_computation_type = 4 [json_name = "additionalInterestComputationType"];
  // e.g. 0
  double course_on_interest_payment = 5 [json_name = "courseOnInterestPayment"];
  // e.g. 0
  double additional_grace_on_interest_payment = 6 [json_name = "additionalGraceOnInterestPayment"];
  // e.g. 0
  double course_on_principal_payment = 7 [json_name = "courseOnPrincipalPayment"];
  // e.g. 0
  double additional_grace_on_principal_payment = 8 [json_name = "additionalGraceOnPrincipalPayment"];
  // e.g. false
  bool is_interest_type_convertable = 9 [json_name = "isInterestTypeConvertable"];
}

// Example JSON:
//    {
//        "id": 300,
//        "code": "loanStatusType.active",
//        "value": "Active",
//        "pendingApproval": false,
//        "waitingForDisbursal": false,
//        "active": true,
//        "closedObligationsMet": false,
//        "closedWrittenOff": false,
//        "closedRescheduled": false,
//        "closed": false,
//        "overpaid": false,
//        "transferInProgress": false,
//        "transferOnHold": false,
//        "underTransfer": false
//    }
message LoanStatus {
  // e.g. 300
  int32 id = 1 [json_name = "id"];
  // e.g. "loanStatusType.active"
  string code = 2 [json_name = "code"];
  // e.g. "Active"
  string value = 3 [json_name = "value"];
  // e.g. false
  bool pending_approval = 4 [json_name = "pendingApproval"];
  // e.g. false
  bool waiting_for_disbursal = 5 [json_name = "waitingForDisbursal"];
  // e.g. true
  bool active = 6 [json_name = "active"];
  // e.g. false
  bool closed_obligations_met = 7 [json_name = "closedObligationsMet"];
  // e.g. false
  bool closed_written_off = 8 [json_name = "closedWrittenOff"];
  // e.g. false
  bool closed_rescheduled = 9 [json_name = "closedRescheduled"];
  // e.g. false
  bool closed = 10 [json_name = "closed"];
  // e.g. false
  bool overpaid = 11 [json_name = "overpaid"];
  // e.g. false
  bool transfer_in_progress = 12 [json_name = "transferInProgress"];
  // e.g. false
  bool transfer_on_hold = 13 [json_name = "transferOnHold"];
  // e.g. false
  bool under_transfer = 14 [json_name = "underTransfer"];
}

// Example JSON:
//    {
//        "submittedOnDate": [
//            2024,
//            2,
//            10
//        ],
//        "submittedByUsername": "post",
//        "submittedByFirstname": "post",
//        "submittedByLastname": "post",
//        "approvedOnDate": [
//            2024,
//            2,
//            10
//        ],
//        "approvedByUsername": "post",
//        "approvedByFirstname": "post",
//        "approvedByLastname": "post",
//        "expectedDisbursementDate": [
//            2024,
//            2,
//            10
//        ],
//        "actualDisbursementDate": [
//            2024,
//            2,
//            10
//        ],
//        "disbursedByUsername": "post",
//        "disbursedByFirstname": "post",
//        "disbursedByLastname": "post",
//        "expectedMaturityDate": [
//            2025,
//            2,
//            5
//        ]
//    }
message LoanTimeline {
  // e.g. [2024, 2, 10]
  repeated int32 submitted_on_date = 1 [json_name = "submittedOnDate"];
  // e.g. "post"
  string submitted_by_username = 2 [json_name = "submittedByUsername"];
  // e.g. "post"
  string submitted_by_firstname = 3 [json_name = "submittedByFirstname"];
  // e.g. "post"
  string submitted_by_lastname = 4 [json_name = "submittedByLastname"];
  // e.g. [2024, 2, 10]
  repeated int32 approved_on_date = 5 [json_name = "approvedOnDate"];
  // e.g. "post"
  string approved_by_username = 6 [json_name = "approvedByUsername"];
  // e.g. "post"
  string approved_by_firstname = 7 [json_name = "approvedByFirstname"];
  // e.g. "post"
  string approved_by_lastname = 8 [json_name = "approvedByLastname"];
  // e.g. [2024, 2, 10]
  repeated int32 expected_disbursement_date = 9 [json_name = "expectedDisbursementDate"];
  // e.g. [2024, 2, 10]
  repeated int32 actual_disbursement_date = 10 [json_name = "actualDisbursementDate"];
  // e.g. "post"
  string disbursed_by_username = 11 [json_name = "disbursedByUsername"];
  // e.g. "post"
  string disbursed_by_firstname = 12 [json_name = "disbursedByFirstname"];
  // e.g. "post"
  string disbursed_by_lastname = 13 [json_name = "disbursedByLastname"];
  // e.g. [2025, 2, 5]
  repeated int32 expected_maturity_date = 14 [json_name = "expectedMaturityDate"];
}

// Example JSON:
//       {
//        "currency": {
//            "code": "INR",
//            "name": "Indian Rupee",
//            "decimalPlaces": 2,
//            "inMultiplesOf": 0,
//            "displaySymbol": "₹",
//            "nameCode": "currency.INR",
//            "displayLabel": "Indian Rupee (₹)"
//        },
//        "principalDisbursed": 10000,
//        "principalPaid": 0,
//        "principalWrittenOff": 0,
//        "principalOutstanding": 10000,
//        "principalFromIncomePosting": 0,
//        "principalOverdue": 0,
//        "principalNetDisbursed": 9882,
//        "interestCharged": 1131.32,
//        "interestPaid": 0,
//        "interestWaived": 0,
//        "interestWrittenOff": 0,
//        "interestOutstanding": 1131.32,
//        "interestOverdue": 0,
//        "feeChargesCharged": 118,
//        "feeChargesDueAtDisbursementCharged": 118,
//        "feeChargesPaid": 118,
//        "feeChargesWaived": 0,
//        "feeChargesWrittenOff": 0,
//        "feeChargesOutstanding": 0,
//        "feeChargesOverdue": 0,
//        "penaltyChargesCharged": 0,
//        "penaltyChargesPaid": 0,
//        "penaltyChargesWaived": 0,
//        "penaltyChargesWrittenOff": 0,
//        "penaltyChargesOutstanding": 0,
//        "penaltyChargesOverdue": 0,
//        "totalExpectedRepayment": 11249.32,
//        "totalRepayment": 118,
//        "totalExpectedCostOfLoan": 1249.32,
//        "totalCostOfLoan": 118,
//        "totalWaived": 0,
//        "totalWrittenOff": 0,
//        "totalOutstanding": 11131.32,
//        "totalOverdue": 0,
//        "excessAmountPaid": 0,
//        "availableBalance": 0,
//        "upfrontInterestAvilable": 0,
//        "rebateApplied": 0,
//        "totalAdvanceEmiAmount": 0
//    }
message LoanSummary {
  // e.g.: {"code": "INR", "name": "Indian Rupee", "decimalPlaces": 2, "inMultiplesOf": 0, "displaySymbol": "₹", "nameCode": "currency.INR", "displayLabel": "Indian Rupee (₹)"}
  types.Currency currency = 1 [json_name = "currency"];
  // e.g. 10000
  double principal_disbursed = 2 [json_name = "principalDisbursed"];
  // e.g. 0
  double principal_paid = 3 [json_name = "principalPaid"];
  // e.g. 0
  double principal_written_off = 4 [json_name = "principalWrittenOff"];
  // e.g. 10000
  double principal_outstanding = 5 [json_name = "principalOutstanding"];
  // e.g. 0
  double principal_from_income_posting = 6 [json_name = "principalFromIncomePosting"];
  // e.g. 0
  double principal_overdue = 7 [json_name = "principalOverdue"];
  // e.g. 9882
  double principal_net_disbursed = 8 [json_name = "principalNetDisbursed"];
  // e.g. 1131.32
  double interest_charged = 9 [json_name = "interestCharged"];
  // e.g. 0
  double interest_paid = 10 [json_name = "interestPaid"];
  // e.g. 0
  double interest_waived = 11 [json_name = "interestWaived"];
  // e.g. 0
  double interest_written_off = 12 [json_name = "interestWrittenOff"];
  // e.g. 1131.32
  double interest_outstanding = 13 [json_name = "interestOutstanding"];
  // e.g. 0
  double interest_overdue = 14 [json_name = "interestOverdue"];
  // e.g. 118
  double fee_charges_charged = 15 [json_name = "feeChargesCharged"];
  // e.g. 118
  double fee_charges_due_at_disbursement_charged = 16 [json_name = "feeChargesDueAtDisbursementCharged"];
  // e.g. 118
  double fee_charges_paid = 17 [json_name = "feeChargesPaid"];
  // e.g. 0
  double fee_charges_waived = 18 [json_name = "feeChargesWaived"];
  // e.g. 0
  double fee_charges_written_off = 19 [json_name = "feeChargesWrittenOff"];
  // e.g. 0
  double fee_charges_outstanding = 20 [json_name = "feeChargesOutstanding"];
  // e.g. 0
  double fee_charges_overdue = 21 [json_name = "feeChargesOverdue"];
  // e.g. 0
  double penalty_charges_charged = 22 [json_name = "penaltyChargesCharged"];
  // e.g. 0
  double penalty_charges_paid = 23 [json_name = "penaltyChargesPaid"];
  // e.g. 0
  double penalty_charges_waived = 24 [json_name = "penaltyChargesWaived"];
  // e.g. 0
  double penalty_charges_written_off = 25 [json_name = "penaltyChargesWrittenOff"];
  // e.g. 0
  double penalty_charges_outstanding = 26 [json_name = "penaltyChargesOutstanding"];
  // e.g. 0
  double penalty_charges_overdue = 27 [json_name = "penaltyChargesOverdue"];
  // e.g. 11249.32
  double total_expected_repayment = 28 [json_name = "totalExpectedRepayment"];
  // e.g. 118
  double total_repayment = 29 [json_name = "totalRepayment"];
  // e.g. 1249.32
  double total_expected_cost_of_loan = 30 [json_name = "totalExpectedCostOfLoan"];
  // e.g. 118
  double total_cost_of_loan = 31 [json_name = "totalCostOfLoan"];
  // e.g. 0
  double total_waived = 32 [json_name = "totalWaived"];
  // e.g. 0
  double total_written_off = 33 [json_name = "totalWrittenOff"];
  // e.g. 11131.32
  double total_outstanding = 34 [json_name = "totalOutstanding"];
  // e.g. 0
  double total_overdue = 35 [json_name = "totalOverdue"];
  // e.g. 0
  double excess_amount_paid = 36 [json_name = "excessAmountPaid"];
  // e.g. 0
  double available_balance = 37 [json_name = "availableBalance"];
  // e.g. 0
  double upfront_interest_available = 38 [json_name = "upfrontInterestAvilable"];
  // e.g. 0
  double rebate_applied = 39 [json_name = "rebateApplied"];
  // e.g. 0
  double total_advance_emi_amount = 40 [json_name = "totalAdvanceEmiAmount"];
}
