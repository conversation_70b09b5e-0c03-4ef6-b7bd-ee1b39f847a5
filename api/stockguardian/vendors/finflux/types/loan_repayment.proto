syntax = "proto3";

package stockguardian.vendors.finflux.types;

import "api/stockguardian/vendors/finflux/types/common.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/finflux/types";
option java_package = "com.github.epifi.gringott.api.stockguardian.vendors.finflux.types";

// Example JSON:
//    {
//        "period": 11,
//        "fromDate": [
//            2024,
//            12,
//            5
//        ],
//        "dueDate": [
//            2025,
//            1,
//            5
//        ],
//        "daysInPeriod": 31,
//        "principalOriginalDue": 895.82,
//        "principalDue": 895.82,
//        "principalOutstanding": 895.82,
//        "principalLoanBalanceOutstanding": 915.27,
//        "interestOriginalDue": 30.18,
//        "interestDue": 30.18,
//        "interestOutstanding": 30.18,
//        "feeChargesDue": 0,
//        "penaltyChargesDue": 0,
//        "totalOriginalDueForPeriod": 926,
//        "totalDueForPeriod": 926,
//        "totalPaidForPeriod": 0,
//        "totalOutstandingForPeriod": 926,
//        "totalActualCostOfLoanForPeriod": 30.18,
//        "totalInstallmentAmountForPeriod": 926,
//        "recalculatedInterestComponent": false,
//        "loanRepaymentPeriodComputationDetails": {
//            "calculatedOnAmountMap": {
//                "2025-01-05": 1811.09
//            }
//        },
//        "interestAdjustedDueToGrace": 0,
//        "interestAccruable": 30.18
//  }
message LoanRepaymentPeriod {
  message ComputationDetails {
    map<string, double> calculated_on_amount_map = 1 [json_name = "calculatedOnAmountMap"];
  }

  int32 period = 1;
  repeated int32 from_date = 2 [json_name = "fromDate"];
  repeated int32 due_date = 3 [json_name = "dueDate"];
  int32 days_in_period = 4 [json_name = "daysInPeriod"];
  double principal_original_due = 5 [json_name = "principalOriginalDue"];
  double principal_due = 6 [json_name = "principalDue"];
  double principal_paid = 7 [json_name = "principalPaid"];
  double principal_waived = 8 [json_name = "principalWaived"];
  double principal_written_off = 9 [json_name = "principalWrittenOff"];
  double principal_outstanding = 10 [json_name = "principalOutstanding"];
  double principal_loan_balance_outstanding = 11 [json_name = "principalLoanBalanceOutstanding"];
  double interest_original_due = 12 [json_name = "interestOriginalDue"];
  double interest_due = 13 [json_name = "interestDue"];
  double interest_paid = 14 [json_name = "interestPaid"];
  double interest_waived = 15 [json_name = "interestWaived"];
  double interest_written_off = 16 [json_name = "interestWrittenOff"];
  double interest_outstanding = 17 [json_name = "interestOutstanding"];
  double fee_charges_original_due = 18 [json_name = "feeChargesOriginalDue"];
  double fee_charges_due = 19 [json_name = "feeChargesDue"];
  double fee_charges_paid = 20 [json_name = "feeChargesPaid"];
  double fee_charges_waived = 21 [json_name = "feeChargesWaived"];
  double fee_charges_written_off = 22 [json_name = "feeChargesWrittenOff"];
  double fee_charges_outstanding = 23 [json_name = "feeChargesOutstanding"];
  double penalty_charges_original_due = 24 [json_name = "penaltyChargesOriginalDue"];
  double penalty_charges_due = 25 [json_name = "penaltyChargesDue"];
  double penalty_charges_paid = 26 [json_name = "penaltyChargesPaid"];
  double penalty_charges_waived = 27 [json_name = "penaltyChargesWaived"];
  double penalty_charges_written_off = 28 [json_name = "penaltyChargesWrittenOff"];
  double penalty_charges_outstanding = 29 [json_name = "penaltyChargesOutstanding"];
  double total_original_due_for_period = 30 [json_name = "totalOriginalDueForPeriod"];
  double total_due_for_period = 31 [json_name = "totalDueForPeriod"];
  double total_paid_for_period = 32 [json_name = "totalPaidForPeriod"];
  double total_paid_in_advance_for_period = 33 [json_name = "totalPaidInAdvanceForPeriod"];
  double total_paid_late_for_period = 34 [json_name = "totalPaidLateForPeriod"];
  double total_waived_for_period = 35 [json_name = "totalWaivedForPeriod"];
  double total_written_off_for_period = 36 [json_name = "totalWrittenOffForPeriod"];
  double total_outstanding_for_period = 37 [json_name = "totalOutstandingForPeriod"];
  double total_actual_cost_of_loan_for_period = 38 [json_name = "totalActualCostOfLoanForPeriod"];
  double total_installment_amount_for_period = 39 [json_name = "totalInstallmentAmountForPeriod"];
  bool recalculated_interest_component = 40 [json_name = "recalculatedInterestComponent"];
  double advance_payment_amount = 41 [json_name = "advancePaymentAmount"];
  // applicable only for calculate loan schedule without loan id api
  ComputationDetails computation_details = 42 [json_name = "loanRepaymentPeriodComputationDetails"];
  double interest_adjusted_due_to_grace = 43 [json_name = "interestAdjustedDueToGrace"];
  double interest_accruable = 44 [json_name = "interestAccruable"];
  bool complete = 45 [json_name = "complete"];
  // date when all the components of the emi (P, I & C) are cleared off
  // applicable only for fetch loan schedule with loan id api
  repeated int32 obligations_met_on_date = 46 [json_name = "obligationsMetOnDate"];
}

// Example JSON:
//     {
//        "currency": {
//            "code": "INR",
//            "name": "temp",
//            "decimalPlaces": 2,
//            "inMultiplesOf": 0,
//            "nameCode": "tempcode",
//            "displayLabel": "temp [INR]"
//        },
//        "loanTermInDays": 361,
//        "totalPrincipalDisbursed": 10000,
//        "totalPrincipalExpected": 10000,
//        "totalPrincipalPaid": 0,
//        "totalInterestCharged": 1131.32,
//        "totalFeeChargesCharged": 118,
//        "totalPenaltyChargesCharged": 0,
//        "totalWaived": 0,
//        "totalWrittenOff": 0,
//        "totalRepaymentExpected": 11249.32,
//        "totalRepayment": 118,
//        "totalPaidInAdvance": 0,
//        "totalPaidLate": 0,
//        "totalOutstanding": 11131.32,
//        "totalAdvancePayment": 0,
//        "periods": [
//            {
//                "dueDate": [
//                    2024,
//                    2,
//                    10
//                ],
//                "principalDisbursed": 10000,
//                "principalLoanBalanceOutstanding": 10000,
//                "feeChargesDue": 118,
//                "feeChargesPaid": 118,
//                "totalOriginalDueForPeriod": 118,
//                "totalDueForPeriod": 118,
//                "totalPaidForPeriod": 118,
//                "totalActualCostOfLoanForPeriod": 118,
//                "recalculatedInterestComponent": false
//            },
//            {
//                "period": 1,
//                "fromDate": [
//                    2024,
//                    2,
//                    10
//                ],
//                "dueDate": [
//                    2024,
//                    3,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 24,
//                "principalOriginalDue": 764.89,
//                "principalDue": 764.89,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 764.89,
//                "principalLoanBalanceOutstanding": 9235.11,
//                "interestOriginalDue": 161.11,
//                "interestDue": 161.11,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 161.11,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 161.11,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 161.11
//            },
//            {
//                "period": 2,
//                "fromDate": [
//                    2024,
//                    3,
//                    5
//                ],
//                "dueDate": [
//                    2024,
//                    4,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 31,
//                "principalOriginalDue": 766.95,
//                "principalDue": 766.95,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 766.95,
//                "principalLoanBalanceOutstanding": 8468.16,
//                "interestOriginalDue": 159.05,
//                "interestDue": 159.05,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 159.05,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 159.05,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 159.05
//            },
//            {
//                "period": 3,
//                "fromDate": [
//                    2024,
//                    4,
//                    5
//                ],
//                "dueDate": [
//                    2024,
//                    5,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 30,
//                "principalOriginalDue": 784.86,
//                "principalDue": 784.86,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 784.86,
//                "principalLoanBalanceOutstanding": 7683.3,
//                "interestOriginalDue": 141.14,
//                "interestDue": 141.14,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 141.14,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 141.14,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 141.14
//            },
//            {
//                "period": 4,
//                "fromDate": [
//                    2024,
//                    5,
//                    5
//                ],
//                "dueDate": [
//                    2024,
//                    6,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 31,
//                "principalOriginalDue": 793.68,
//                "principalDue": 793.68,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 793.68,
//                "principalLoanBalanceOutstanding": 6889.62,
//                "interestOriginalDue": 132.32,
//                "interestDue": 132.32,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 132.32,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 132.32,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 132.32
//            },
//            {
//                "period": 5,
//                "fromDate": [
//                    2024,
//                    6,
//                    5
//                ],
//                "dueDate": [
//                    2024,
//                    7,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 30,
//                "principalOriginalDue": 811.17,
//                "principalDue": 811.17,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 811.17,
//                "principalLoanBalanceOutstanding": 6078.45,
//                "interestOriginalDue": 114.83,
//                "interestDue": 114.83,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 114.83,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 114.83,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 114.83
//            },
//            {
//                "period": 6,
//                "fromDate": [
//                    2024,
//                    7,
//                    5
//                ],
//                "dueDate": [
//                    2024,
//                    8,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 31,
//                "principalOriginalDue": 821.32,
//                "principalDue": 821.32,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 821.32,
//                "principalLoanBalanceOutstanding": 5257.13,
//                "interestOriginalDue": 104.68,
//                "interestDue": 104.68,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 104.68,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 104.68,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 104.68
//            },
//            {
//                "period": 7,
//                "fromDate": [
//                    2024,
//                    8,
//                    5
//                ],
//                "dueDate": [
//                    2024,
//                    9,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 31,
//                "principalOriginalDue": 835.46,
//                "principalDue": 835.46,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 835.46,
//                "principalLoanBalanceOutstanding": 4421.67,
//                "interestOriginalDue": 90.54,
//                "interestDue": 90.54,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 90.54,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 90.54,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 90.54
//            },
//            {
//                "period": 8,
//                "fromDate": [
//                    2024,
//                    9,
//                    5
//                ],
//                "dueDate": [
//                    2024,
//                    10,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 30,
//                "principalOriginalDue": 852.31,
//                "principalDue": 852.31,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 852.31,
//                "principalLoanBalanceOutstanding": 3569.36,
//                "interestOriginalDue": 73.69,
//                "interestDue": 73.69,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 73.69,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 73.69,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 73.69
//            },
//            {
//                "period": 9,
//                "fromDate": [
//                    2024,
//                    10,
//                    5
//                ],
//                "dueDate": [
//                    2024,
//                    11,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 31,
//                "principalOriginalDue": 864.53,
//                "principalDue": 864.53,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 864.53,
//                "principalLoanBalanceOutstanding": 2704.83,
//                "interestOriginalDue": 61.47,
//                "interestDue": 61.47,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 61.47,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 61.47,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 61.47
//            },
//            {
//                "period": 10,
//                "fromDate": [
//                    2024,
//                    11,
//                    5
//                ],
//                "dueDate": [
//                    2024,
//                    12,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 30,
//                "principalOriginalDue": 880.92,
//                "principalDue": 880.92,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 880.92,
//                "principalLoanBalanceOutstanding": 1823.91,
//                "interestOriginalDue": 45.08,
//                "interestDue": 45.08,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 45.08,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 45.08,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 45.08
//            },
//            {
//                "period": 11,
//                "fromDate": [
//                    2024,
//                    12,
//                    5
//                ],
//                "dueDate": [
//                    2025,
//                    1,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 31,
//                "principalOriginalDue": 894.59,
//                "principalDue": 894.59,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 894.59,
//                "principalLoanBalanceOutstanding": 929.32,
//                "interestOriginalDue": 31.41,
//                "interestDue": 31.41,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 31.41,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 926,
//                "totalDueForPeriod": 926,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 926,
//                "totalActualCostOfLoanForPeriod": 31.41,
//                "totalInstallmentAmountForPeriod": 926,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 31.41
//            },
//            {
//                "period": 12,
//                "fromDate": [
//                    2025,
//                    1,
//                    5
//                ],
//                "dueDate": [
//                    2025,
//                    2,
//                    5
//                ],
//                "complete": false,
//                "daysInPeriod": 31,
//                "principalOriginalDue": 929.32,
//                "principalDue": 929.32,
//                "principalPaid": 0,
//                "principalWrittenOff": 0,
//                "principalOutstanding": 929.32,
//                "principalLoanBalanceOutstanding": 0,
//                "interestOriginalDue": 16,
//                "interestDue": 16,
//                "interestPaid": 0,
//                "interestWaived": 0,
//                "interestWrittenOff": 0,
//                "interestOutstanding": 16,
//                "feeChargesDue": 0,
//                "feeChargesPaid": 0,
//                "feeChargesWaived": 0,
//                "feeChargesWrittenOff": 0,
//                "feeChargesOutstanding": 0,
//                "penaltyChargesDue": 0,
//                "penaltyChargesPaid": 0,
//                "penaltyChargesWaived": 0,
//                "penaltyChargesWrittenOff": 0,
//                "penaltyChargesOutstanding": 0,
//                "totalOriginalDueForPeriod": 945.32,
//                "totalDueForPeriod": 945.32,
//                "totalPaidForPeriod": 0,
//                "totalPaidInAdvanceForPeriod": 0,
//                "totalPaidLateForPeriod": 0,
//                "totalWaivedForPeriod": 0,
//                "totalWrittenOffForPeriod": 0,
//                "totalOutstandingForPeriod": 945.32,
//                "totalActualCostOfLoanForPeriod": 16,
//                "totalInstallmentAmountForPeriod": 945.32,
//                "recalculatedInterestComponent": false,
//                "advancePaymentAmount": 0,
//                "interestAdjustedDueToGrace": 0,
//                "interestAccruable": 16
//            }
//        ]
//    }
message LoanRepaymentSchedule {
  types.Currency currency = 1 [json_name = "currency"];
  // e.g.: 361
  int32 loan_term_in_days = 2 [json_name = "loanTermInDays"];
  // e.g.: 10000
  double total_principal_disbursed = 3 [json_name = "totalPrincipalDisbursed"];
  // e.g.: 10000
  double total_principal_expected = 4 [json_name = "totalPrincipalExpected"];
  // e.g.: 0
  double total_principal_paid = 5 [json_name = "totalPrincipalPaid"];
  // e.g.: 1131.32
  double total_interest_charged = 6 [json_name = "totalInterestCharged"];
  // e.g.: 118
  double total_fee_charges_charged = 7 [json_name = "totalFeeChargesCharged"];
  // e.g.: 0
  double total_penalty_charges_charged = 8 [json_name = "totalPenaltyChargesCharged"];
  // e.g.: 0
  double total_waived = 9 [json_name = "totalWaived"];
  // e.g.: 0
  double total_written_off = 10 [json_name = "totalWrittenOff"];
  // e.g.: 11249.32
  double total_repayment_expected = 11 [json_name = "totalRepaymentExpected"];
  // e.g.: 118
  double total_repayment = 12 [json_name = "totalRepayment"];
  // e.g.: 0
  double total_paid_in_advance = 13 [json_name = "totalPaidInAdvance"];
  // e.g.: 0
  double total_paid_late = 14 [json_name = "totalPaidLate"];
  // e.g.: 11131.32
  double total_outstanding = 15 [json_name = "totalOutstanding"];
  // e.g.: 0
  double total_advance_payment = 16 [json_name = "totalAdvancePayment"];
  repeated types.LoanRepaymentPeriod periods = 17 [json_name = "periods"];
}
