syntax = "proto3";

package stockguardian.vendors.finflux;

import "api/stockguardian/vendors/finflux/types/client.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/finflux";
option java_package = "com.github.epifi.gringott.api.stockguardian.vendors.finflux";

// Update Client Request
// {
//     "locale": "en",
//     "dateFormat": "dd-MM-yyyy",
//     "clientData": {
//         "firstName": "Puru",
//         "middleName": "John",
//         "lastName": "Kalia",
//         "dateOfBirth": "01-12-2001",
//         "gender": "Male",
//         "mobileNo": "9871190985",
//         "alternateMobileNo": "9822550365",
//         "email": "<EMAIL>",
//         "education": "Graduation",
//         "officeName": "Head Office"
//     },
//     "addressData": [
//         {
//             "addressType": [
//                 "Permanent Address"
//             ],
//             "addressLineOne": "123 Main St Apt 4A",
//             "addressLineTwo": "123 Main St Apt 4A",
//             "landmark": "Eiffel Tower",
//             "ownershipType": "own",
//             "postalCode": "560001"
//         }
//     ],
//     "familyDetailsData": [
//         {
//             "firstName": "John",
//             "lastName": "Doe",
//             "dateOfBirth": "01-01-1954",
//             "relationship": "Father",
//             "gender": "Male",
//             "documentTypeId": "PAN",
//             "documentKey": "**********"
//         }
//     ],
//     "clientIdentifierData": [
//         {
//             "documentType": "AADHAAR",
//             "documentKey": "************",
//             "issueDate": "12-10-2019",
//             "expiryDate": ""
//         }
//     ],
//     "bankDetailsData": [
//         {
//             "accountNumber": "*****************",
//             "accountType": "SAVINGSACCOUNT",
//             "ifscCode": "UBIN0558281",
//             "name": "aditya",
//             "supportedForRepayment": true,
//             "supportedForDisbursement": true
//         }
//     ],
//     "employmentDetailData": {
//         "employmentType": "SELF",
//         "monthlySalary": 56789.9,
//         "totalWorkExperience": 5,
//         "currentEmployerName": "Finflux",
//         "existingIncomeObligation": "997.000000"
//     }
// }
message UpdateClientRequest {
  string locale = 1 [json_name = "locale"];
  // (mandatory)
  string date_format = 2 [json_name = "dateFormat"];
  // (optional)
  types.Client client = 3 [json_name = "clientData"];
  // (optional) at least one address is required
  repeated types.Address addresses = 4 [json_name = "addressData"];
  // (optional)
  repeated types.FamilyMember family_members = 5 [json_name = "familyDetailsData"];
  // (optional)
  repeated types.ClientIdentifier identifiers = 6 [json_name = "clientIdentifierData"];
  // (optional)
  repeated types.BankAccount bank_accounts = 7 [json_name = "bankDetailsData"];
  // (optional)
  types.Employment employment = 8 [json_name = "employmentDetailData"];
}

message UpdateClientResponse {
  // unique identifier of the client
  int32 client_id = 1 [json_name = "clientId"];
}
