syntax = "proto3";

package stockguardian.vendors.finflux;

option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/finflux";
option java_package = "com.github.epifi.gringott.api.stockguardian.vendors.finflux";

/*
 * Generate Auth Token API - To get the access token to pass in the header for all other API calls
 * Request body is GenerateAuthTokenRequest
 * Response body is GenerateAuthTokenResponse
 */

message GenerateAuthTokenRequest {
  string client_id = 1 [json_name = "client_id"];
  string grant_type = 2 [json_name = "grant_type"];
  string username = 3 [json_name = "username"];
  string password = 4 [json_name = "password"];
  // "true" or "false"
  string is_password_encrypted = 5 [json_name = "isPasswordEncrypted"];
}

// Example JSON:
//  {
//      "access_token": "TlEjL-AV1zg3t3dL64mt3K2OnDQ",
//      "token_type": "bearer",
//      "refresh_token": "NcPY3w352Q_x3mj_tdNPAMEdwIg",
//      "expires_in": 31535999,
//      "scope": "access-token"
//  }
message GenerateAuthTokenResponse {
  string access_token = 1 [json_name = "access_token"];
  string token_type = 2 [json_name = "token_type"];
  string refresh_token = 3 [json_name = "refresh_token"];
  int32 expires_in_seconds = 4 [json_name = "expires_in"];
  string scope = 5 [json_name = "scope"];
}
