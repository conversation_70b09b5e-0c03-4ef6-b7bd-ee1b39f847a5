syntax = "proto3";

package stockguardian.vendors.finflux;

import "api/stockguardian/vendors/finflux/types/client.proto";
import "api/stockguardian/vendors/finflux/types/common.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/vendors/finflux";
option java_package = "com.github.epifi.gringott.api.stockguardian.vendors.finflux";

/*
 * Fetch Client API - To fetch a customer in the finflux system based on client id (or) external id
 * Request body is empty
 * Response body is FetchClientResponse
 */

// Example JSON:
//    {
//        "id": 13,
//        "accountNo": "*********",
//        "status": {
//          "id": 300,
//          "code": "clientStatusType.active",
//          "value": "Active"
//         },
//        "active": true,
//        "activationDate": [
//          2024,
//          2,
//          19
//        ],
//        "firstname": "<PERSON><PERSON>",
//        "middlename": "<PERSON>",
//        "lastname": "<PERSON><PERSON>",
//        "displayName": "<PERSON><PERSON> <PERSON>",
//        "mobileNo": "**********",
//        "alternateMobileNo": "**********",
//        "dateOfBirth": [
//          2001,
//          12,
//          1
//         ],
//        "gender": {
//          "id": 23,
//          "name": "Male",
//          "isActive": false,
//          "mandatory": false
//        },
//        "clientType": {
//          "isActive": false,
//          "mandatory": false
//        },
//        "clientClassification": {
//          "isActive": false,
//          "mandatory": false
//        },
//        "salutation": {
//          "isActive": false,
//          "mandatory": false
//        },
//        "nationality": {
//          "isActive": false,
//          "mandatory": false
//        },
//        "education": {
//          "id": 115,
//          "name": "Graduation",
//          "isActive": false,
//          "mandatory": false
//        },
//        "riskCategory": {
//           "isActive": false,
//           "mandatory": false
//         },
//        "emailId": "<EMAIL>",
//        "officeId": 1,
//        "officeName": "Head Office",
//        "timeline": {
//          "submittedOnDate": [
//            2024,
//            2,
//            19
//          ],
//          "submittedByUsername": "post",
//          "submittedByFirstname": "post",
//          "submittedByLastname": "post",
//          "activatedOnDate": [
//            2024,
//            2,
//            19
//           ],
//          "activatedByUsername": "post",
//          "activatedByFirstname": "post",
//          "activatedByLastname": "post"
//       },
//      "legalForm": {
//        "id": 1,
//        "code": "legalFormType.person",
//        "value": "PERSON"
//      },
//      "groups": [],
//      "clientNonPersonDetails": {
//        "constitution": {
//          "isActive": false,
//          "mandatory": false
//        },
//        "mainBusinessLine": {
//          "isActive": false,
//          "mandatory": false
//        }
//      },
//      "isLocked": false,
//      "isWorkflowEnabled": false,
//      "maritalStatus": {
//        "isActive": false,
//        "mandatory": false
//      },
//      "isVerified": false,
//      "isWorkflowEnableForBranch": false,
//      "clientReligion": {
//        "isActive": false,
//        "mandatory": false
//      },
//      "isMobileVerified": false,
//      "officeHierarchy": ".",
//      "isBlackListed": false,
//      "clientTags": []
//    }
message FetchClientResponse {
  int32 id = 1 [json_name = "id"];
  string account_no = 2 [json_name = "accountNo"];
  types.Enum status = 3 [json_name = "status"];
  bool active = 4 [json_name = "active"];
  repeated int32 activation_date = 5 [json_name = "activationDate"];
  string firstname = 6 [json_name = "firstname"];
  string middlename = 7 [json_name = "middlename"];
  string lastname = 8 [json_name = "lastname"];
  string display_name = 9 [json_name = "displayName"];
  string mobile_no = 10 [json_name = "mobileNo"];
  string alternate_mobile_no = 11 [json_name = "alternateMobileNo"];
  repeated int32 date_of_birth = 12 [json_name = "dateOfBirth"];
  types.Option gender = 13 [json_name = "gender"];
  types.Option education = 14 [json_name = "education"];
  string email_id = 15 [json_name = "emailId"];
  int32 office_id = 16 [json_name = "officeId"];
  string office_name = 17 [json_name = "officeName"];
  types.Timeline timeline = 18 [json_name = "timeline"];
  types.Enum legal_form = 19 [json_name = "legalForm"];
  reserved 20; // reserved for "groups" as structure is not known
  types.ClientNonPersonDetails client_non_person_details = 21 [json_name = "clientNonPersonDetails"];
  bool is_locked = 22 [json_name = "isLocked"];
  bool is_workflow_enabled = 23 [json_name = "isWorkflowEnabled"];
  types.Option marital_status = 24 [json_name = "maritalStatus"];
  bool is_verified = 25 [json_name = "isVerified"];
  bool is_workflow_enable_for_branch = 26 [json_name = "isWorkflowEnableForBranch"];
  types.Option client_religion = 27 [json_name = "clientReligion"];
  bool is_mobile_verified = 28 [json_name = "isMobileVerified"];
  string office_hierarchy = 29 [json_name = "officeHierarchy"];
  bool is_black_listed = 30 [json_name = "isBlackListed"];
  reserved 31; // reserved for "clientTags" as structure is not known
}
