//go:generate gen_sql -types=BatchSourceDetails
syntax = "proto3";

package stockguardian.lms;

import "api/stockguardian/lms/enums/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/lms";
option java_package = "com.github.epifi.gringott.api.stockguardian.lms";

message RecurringPaymentExecutionBatch {
  // Denotes a unique id for a recurring payment execution batch
  string id = 1;
  enums.RecurringPaymentExecutionType recurring_payment_execution_type = 2;
  // Details of the source of this batch
  BatchSourceDetails source_details = 3;

  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  google.protobuf.Timestamp deleted_at = 6;
}

message BatchSourceDetails {
  string input_file_s3_path = 1;
}
