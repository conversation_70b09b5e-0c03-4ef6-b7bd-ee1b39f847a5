//go:generate gen_queue_pb
syntax = "proto3";

package stockguardian.creditreport.consumer;

import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/creditreport/consumer";
option java_package = "com.github.epifi.gringott.api.stockguardian.creditreport/consumer";

service CreditReportConsumer{
    // consumer to flatten cibil report of user
    rpc ProcessCreditReportFlattening(ProcessCreditReportFlatteningRequest) returns (ProcessCreditReportFlatteningResponse);
}

message ProcessCreditReportFlatteningRequest{
    // A set of all the common attributes to be contained in a queue consumer response
    queue.ConsumerRequestHeader request_header = 1;
    // Raw report id of credit report that needs to be flattened
    string credit_report_id = 2;
}

message ProcessCreditReportFlatteningResponse{
    // A set of all the common attributes to be contained in a queue consumer response
    queue.ConsumerResponseHeader response_header = 1;
}
