syntax = "proto3";

package api.stockguardian.sgdigilocker;

import "google/protobuf/timestamp.proto";
import "api/stockguardian/sgdigilocker/common/user.proto";
import "api/stockguardian/sgdigilocker/common/enums.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgdigilocker";

// DigilockerUser contains the user details fetched from DigiLocker
message DigilockerUser {
  // Primary key, unique identifier for each user entry
  string id = 1;

  // User identifier for the flow from the source system
  string user_identifier = 2;

  // Product flow through which user details are fetched and stored
  common.ProductFlow flow = 3;

  // User profile details (name, dob, gender, etc.) fetched from DigiLocker
  common.UserProfile profile = 4;

  // Timestamp of when the user entry was created
  google.protobuf.Timestamp created_at = 7;

  // Timestamp of when the user entry was last updated
  google.protobuf.Timestamp updated_at = 8;

  // Timestamp of when the user entry was soft deleted (NULL if not deleted)
  google.protobuf.Timestamp deleted_at = 9;
}
