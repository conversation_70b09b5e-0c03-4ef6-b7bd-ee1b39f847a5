syntax = "proto3";

package api.stockguardian.sgdigilocker;

import "google/protobuf/timestamp.proto";
import "api/stockguardian/sgdigilocker/common/enums.proto";
import "api/stockguardian/sgdigilocker/common/document.proto";
import "api/stockguardian/sgdigilocker/common/vendor.proto";
import "api/typesv2/common/document_type.proto";
import "api/typesv2/common/document_format.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgdigilocker";

message DigilockerDocument {
  // Unique identifier for each document entry
  string id = 1;

  // User identifier for the product flow from the source system
  string user_identifier = 2;

  // Id passed by client while fetching the document from vendor
  string client_req_id = 3;

  // Product flow through which documents are fetched and stored
  common.ProductFlow flow = 4;

  // Document type enum. Ex- PAN, AADHAAR etc
  typesv2.common.DocumentType doc_type = 5;

  // Document format enum. Ex- XML, PDF etc
  typesv2.common.DocumentFormat  format = 6;

  // Storage location of the raw document. Ex- S3 bucket, path etc
  common.StorageLocation storage_location = 7;

  // Parsed content of the document.
  common.ParsedDoc parsed_data = 8;

  // Timestamp indicating when the document was fetched from the DigiLocker vendor
  google.protobuf.Timestamp fetched_at = 9;

  // Document metadata received from the DigiLocker vendor
  common.VendorMetadata vendor_metadata = 10;

  // Timestamp indicating when the document entry was created
  google.protobuf.Timestamp created_at = 11;

  // Timestamp indicating when the document entry was last updated
  google.protobuf.Timestamp updated_at = 12;

  // Timestamp indicating when the document entry was deleted
  google.protobuf.Timestamp deleted_at = 13;
}

message ExtendedDigilockerDocument {
  DigilockerDocument document = 1;
  // Pre-signed URL to download the document from the storage location
  // Expiry: 10 minutes
  string pre_signed_url = 2;
}
