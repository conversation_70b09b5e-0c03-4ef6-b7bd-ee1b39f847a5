syntax = "proto3";

package stockguardian.sgkyc;

import "api/rpc/status.proto";
import "api/stockguardian/sgkyc/internal/ckyc_download.proto";
import "api/stockguardian/sgkyc/internal/kyc_vendor_data.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/date.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/gender.proto";
import "api/stockguardian/sgkyc/enums.proto";
import "api/stockguardian/sgkyc/internal/okyc.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/phone_number.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgkyc";

service KycService {
  rpc StoreKYCVendorData (StoreKYCVendorDataRequest) returns (StoreKYCVendorDataResponse);
  // This RPC is used to fetch the common data that is present in all the different types of KYCs
  rpc GetKYCRecord (GetKYCRecordRequest) returns (GetKYCRecordResponse);
  // This RPC is used to fetch the data for all the types of KYCs that we were done as part of the application process
  rpc GetKYCDataForApplication (GetKYCDataForApplicationRequest) returns (GetKYCDataForApplicationResponse);
  // This RPC is used to fetch the data for all the types of KYCs that we were done as part of the application process
  rpc GetKYCDataForApplicationV2 (GetKYCDataForApplicationV2Request) returns (GetKYCDataForApplicationV2Response);
}

message StoreKYCVendorDataRequest {
  string reference_id = 1;
  .stockguardian.sgkyc.KYCVendorData vendor_data = 2;
}

message StoreKYCVendorDataResponse {
  rpc.Status status = 1;
}

message GetKYCRecordRequest {
  string reference_id = 1;
}

message GetKYCRecordResponse {
  rpc.Status status = 1;
  KYCRecord kyc_record = 2;
}

message KYCRecord {
  api.typesv2.common.Name name = 1;
  api.typesv2.common.Name father_name = 2;
  api.typesv2.common.Name mother_name = 3;
  api.typesv2.common.Date date_of_birth = 4;
  api.typesv2.common.PostalAddress communication_address = 5;
  api.typesv2.common.PostalAddress permanent_address = 6;
  api.typesv2.common.Gender gender = 7;
}

message GetKYCDataForApplicationRequest {
  // customer application id for which we need the kyc data for
  string application_id = 1;
  bool want_images_in_base64 = 2;
}

message GetKYCDataForApplicationResponse {
  rpc.Status status = 1;
  CKYCDownloadPayload ckyc_data = 2;
  OKYCPayload okyc_data = 3;
  KYCVendorDataType data_type = 4;
}

message GetKYCDataForApplicationV2Request {
  // customer application id for which we need the kyc data for
  string application_id = 1;
  bool want_images_in_base64 = 2;
}

message GetKYCDataForApplicationV2Response {
  rpc.Status status = 1;
  KYCVendorDataType data_type = 2;
  KycData kyc_data = 3;
}

message KycData {
  message PersonalDetails {
    api.typesv2.common.Name name = 1;
    api.typesv2.common.Date dob = 2;
    api.typesv2.common.Gender gender = 3;
    api.typesv2.common.PostalAddress correspondence_address = 4;
    api.typesv2.common.PostalAddress permanent_address = 5;
    api.typesv2.common.PhoneNumber mobile = 6;
    string pan = 7;
  }

  PersonalDetails personal_data = 1;
  // Identity data will be used for storing the identity proof details like PAN, Aadhar, Passport etc.
  repeated Identity identity_data = 2;
  // images_data will be used for storing the images of the identity proofs and other documents
  repeated ImageData images_data = 3;
  api.typesv2.common.Image user_image = 4;
  string raw_response = 5;
  string kyc_document_number = 6;
}
