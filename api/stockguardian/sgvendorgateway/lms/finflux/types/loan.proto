syntax = "proto3";

package stockguardian.sgvendorgateway.lms.finflux.types;

import "api/stockguardian/sgvendorgateway/lms/finflux/types/enum.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/lms/finflux/types";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgvendorgateway.lms.finflux.types";

message LoanSummary {
  google.type.Money principal_disbursed = 1;
  google.type.Money principal_paid = 2;
  google.type.Money principal_written_off = 3;
  google.type.Money principal_outstanding = 4;
  google.type.Money principal_from_income_posting = 5;
  google.type.Money principal_overdue = 6;
  google.type.Money principal_net_disbursed = 7;
  google.type.Money interest_charged = 8;
  google.type.Money interest_paid = 9;
  google.type.Money interest_waived = 10;
  google.type.Money interest_written_off = 11;
  google.type.Money interest_outstanding = 12;
  google.type.Money interest_overdue = 13;
  google.type.Money fee_charges_charged = 14;
  google.type.Money fee_charges_due_at_disbursement_charged = 15;
  google.type.Money fee_charges_paid = 16;
  google.type.Money fee_charges_waived = 17;
  google.type.Money fee_charges_written_off = 18;
  google.type.Money fee_charges_outstanding = 19;
  google.type.Money fee_charges_overdue = 20;
  google.type.Money penalty_charges_charged = 21;
  google.type.Money penalty_charges_paid = 22;
  google.type.Money penalty_charges_waived = 23;
  google.type.Money penalty_charges_written_off = 24;
  google.type.Money penalty_charges_outstanding = 25;
  google.type.Money penalty_charges_overdue = 26;
  google.type.Money total_expected_repayment = 27;
  google.type.Money total_repayment = 28;
  google.type.Money total_expected_cost_of_loan = 29;
  google.type.Money total_cost_of_loan = 30;
  google.type.Money total_waived = 31;
  google.type.Money total_written_off = 32;
  google.type.Money total_outstanding = 33;
  google.type.Money total_overdue = 34;
  google.type.Money excess_amount_paid = 35;
  google.type.Money available_balance = 36;
  google.type.Money upfront_interest_available = 37;
  google.type.Money rebate_applied = 38;
  google.type.Money total_advance_emi_amount = 39;
}

message LoanTimeline {
  google.type.Date submitted_on = 1;
  google.type.Date approved_on = 2;
  google.type.Date expected_disbursement_date = 3;
  // actual disbursed date
  google.type.Date disbursed_on = 4;
  google.type.Date expected_maturity_date = 5;
}

message LoanDetails {
  // unique identifier of the loan in Finflux
  string id = 1;
  // e.g. "*********"
  string account_number = 2;
  LoanStatus status = 3;
  // unique identifier of the customer in Finflux
  string client_id = 4;
  google.type.Money principal_amount = 5;
  google.type.Money approved_principal_amount = 6;
  google.type.Money proposed_principal_amount = 7;
  google.type.Money amount_paid_in_advance = 8;
  google.type.Money broken_period_interest = 9;
  // e.g. 12
  int32 term_frequency = 10;
  // e.g.: "month"
  FrequencyType term_frequency_type = 11;
  // e.g. 12
  int32 number_of_repayments = 12;
  // e.g. 1
  int32 repayment_every = 13;
  FrequencyType repayment_frequency_type = 14;
  // e.g. 20
  double interest_rate_per_period = 15;
  FrequencyType interest_rate_frequency_type = 16;
  // e.g. 20
  double annual_interest_rate = 17;
  int32 number_of_paid_repayments = 18;
  int32 number_of_due_repayments = 19;
  // declining balance or flat
  InterestType interest_type = 20;
  // e.g. 14
  double min_interest_rate_per_period = 21;
  // e.g. 30
  double max_interest_rate_per_period = 22;
  // e.g. 20
  double current_interest_rate = 23;
  bool is_cancellation_allowed = 24;
  LoanTimeline timeline = 25;
  LoanSummary summary = 26;
  google.type.Money fee_charged_at_disbursement = 27;
  bool in_arrears = 28;
  // is non-performing asset
  bool is_npa = 29;
  // e.g. 926
  google.type.Money calculated_emi_amount = 30;
  bool is_fldg = 31;
  // e.g. 12
  int32 actual_number_of_repayments = 32;
  google.type.Money total_repayment_expected = 33;
  PreDisbursementChargeSummaryDetail pre_disbursement_charge_summary_detail = 34;
}

message LoanRepaymentPeriod {
  // incremental serial number of the repayment
  int32 serial_number = 1;
  // start date of the repayment period
  google.type.Date from_date = 2;
  // end date of the repayment period
  google.type.Date due_date = 3;
  // number of days in the repayment period
  int32 days_in_period = 4;
  LoanComponentBreakdown principal_component = 5;
  LoanComponentBreakdown interest_component = 6;
  LoanComponentBreakdown fee_charges_component = 7;
  LoanComponentBreakdown penalty_charges_component = 8;
  // total of all components (principal, interest, fee, penalty)
  LoanComponentBreakdown total_component = 9;
  // total principal outstanding before this period
  google.type.Money principal_balance_outstanding_for_loan = 10;
  google.type.Money total_actual_cost_of_loan_for_period = 11;
  google.type.Money total_installment_amount_for_period = 12;
  google.type.Money interest_adjusted_due_to_grace = 13;
  google.type.Money interest_accruable = 14;
  // true if all the EMI components (P, I & C) are completely paid off
  bool is_complete = 15;
  // date when all the components of the emi (P, I & C) are cleared off
  google.type.Date obligations_met_on = 16;
}

// component level breakdown of a loan repayment period
// e.g.: principal component, interest component, fee component, penalty component
message LoanComponentBreakdown {
  google.type.Money original_due = 1;
  // total due amount for the EMI as on the request time
  google.type.Money due = 2;
  // total amount paid for this component
  // not applicable for loan calculation as loan is not taken yet
  google.type.Money paid = 3;
  // total amount waived for this component
  // not applicable for loan calculation as loan is not taken yet
  google.type.Money waived = 4;
  // not applicable for loan calculation as loan is not taken yet
  google.type.Money written_off = 5;
  // not applicable for loan calculation as loan is not taken yet
  google.type.Money outstanding = 6;
}

message PreDisbursementChargeSummaryDetail {
  google.type.Money pre_disbursement_fee_charged = 1;
  google.type.Money pre_disbursement_fee_charges_outstanding = 2;
  google.type.Money pre_disbursement_fee_charges_paid = 3;
  google.type.Money pre_disbursement_fee_charges_waived = 4;
  google.type.Money pre_disbursement_fee_charges_written_off = 5;
}
