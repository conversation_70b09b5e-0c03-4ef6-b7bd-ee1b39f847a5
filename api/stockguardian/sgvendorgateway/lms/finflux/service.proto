syntax = "proto3";

package stockguardian.sgvendorgateway.lms.finflux;

import "api/rpc/status.proto";
import "api/stockguardian/lms/enums/enums.proto";
import "api/stockguardian/sgvendorgateway/lms/finflux/types/client.proto";
import "api/stockguardian/sgvendorgateway/lms/finflux/types/enum.proto";
import "api/stockguardian/sgvendorgateway/lms/finflux/types/loan.proto";
import "api/stockguardian/vendors/finflux/types/product.proto";
import "api/typesv2/common/address.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/lms/finflux";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgvendorgateway.lms.finflux";

// TODO: improve comments for all RPCs
service Finflux {
  // CreateClient creates a new client (user) in the Finflux system
  rpc CreateClient (CreateClientRequest) returns (CreateClientResponse);
  // FetchClient fetches the details of a client (user) in the Finflux system by client_id or external_id
  rpc FetchClient (FetchClientRequest) returns (FetchClientResponse);
  // FetchBankAccounts fetches the bank accounts of a client (user) from the Finflux system
  rpc FetchBankAccounts (FetchBankAccountsRequest) returns (FetchBankAccountsResponse);
  // UpdateClient updates the details of an existing client (user) in the Finflux system.
  rpc UpdateClient (UpdateClientRequest) returns (UpdateClientResponse);
  // CreateLoan creates a new loan account in the Finflux system
  rpc CreateLoan (CreateLoanRequest) returns (CreateLoanResponse);
  // ApproveLoan approves a loan application in the Finflux system
  rpc ApproveLoan (ApproveLoanRequest) returns (ApproveLoanResponse);
  // DisburseLoan marks a loan as disbursed in the Finflux system
  rpc DisburseLoan (DisburseLoanRequest) returns (DisburseLoanResponse);
  // FetchLoanDetails fetches the details of a loan in the Finflux system by loan_id
  rpc FetchLoanDetails (FetchLoanDetailsRequest) returns (FetchLoanDetailsResponse);
  // CalculateLoanSchedule calculates the repayment schedule (without a loan id) in the Finflux system
  rpc CalculateLoanSchedule (CalculateLoanScheduleRequest) returns (CalculateLoanScheduleResponse);
  // FetchLoanSchedule fetches the repayment schedule of a loan in the Finflux system by loan_id
  rpc FetchLoanSchedule (FetchLoanScheduleRequest) returns (FetchLoanScheduleResponse);
  // RejectLoan rejects a loan application in the Finflux system and usually applicable when the
  // current state of loan is "Submitted and Pending approval" in the Finflux system
  rpc RejectLoan (RejectLoanRequest) returns (RejectLoanResponse);
  // PostLoanRepayment rpc is useful to post loan repayment information to Finflux LMS.
  rpc PostLoanRepayment (PostLoanRepaymentRequest) returns (PostLoanRepaymentResponse);
  // ForecloseLoan rpc is used to foreclose a loan account
  rpc ForecloseLoan (ForecloseLoanRequest) returns (ForecloseLoanResponse);
  // GetForeclosureDetails rpc is useful to fetch loan foreclosure details from Finflux LMS
  rpc GetForeclosureDetails (GetForeclosureDetailsRequest) returns (GetForeclosureDetailsResponse);
  // GetLoanCancellationDetails rpc is used to get loan cancellation amount
  rpc GetLoanCancellationDetails (GetLoanCancellationDetailsRequest) returns (GetLoanCancellationDetailsResponse);
  // CancelLoan rpc is used to cancel a loan that is within the cancellation window
  rpc CancelLoan (CancelLoanRequest) returns (CancelLoanResponse);
  // GetDocument is used to get documents like LAS,NOC etc.
  rpc GetDocument (GetDocumentRequest) returns (GetDocumentResponse);
  // AddCharges rpc is used to add charges to a loan account based on some event that is external to the partner LMS system
  rpc AddCharges (AddChargesRequest) returns (AddChargesResponse);
  // GetLoanReport rpc is used to get reports like - NPA, Repayment, Disbursement, RPS and Active Loans
  rpc GetReport (GetReportRequest) returns (GetReportResponse);
  // Pays a predisbursement charge for a loan
  rpc PreDisbursementChargePayment(PreDisbursementChargePaymentRequest) returns (PreDisbursementChargePaymentResponse);
}

message AddChargesRequest {
  string loan_id = 1 [(validate.rules).string.min_len = 1];
  google.type.Money amount = 2 [(validate.rules).message.required = true];
  types.ChargeType charge_type = 3 [(validate.rules).enum = {not_in: [0]}];
  // date on which the charge is due to be paid
  google.type.Date due_date = 4 [(validate.rules).message.required = true];
  // denotes the unique external id to be passed to Finflux LMS for idempotency
  string external_id = 5 [(validate.rules).string.min_len = 1];
}

message AddChargesResponse {
  rpc.Status status = 1;
}

message PostLoanRepaymentRequest {
  // amount repaid
  google.type.Money transaction_amount = 1 [(validate.rules).message.required = true];
  // unique identifier of payment at caller end
  string external_id = 2 [(validate.rules).string.min_len = 1];
  // denotes the repayment txn time.
  google.type.Date transaction_date = 3 [(validate.rules).message.required = true];
  string loan_id = 4 [(validate.rules).string.min_len = 1];
  // this would be used to pass appropriate payment type to Finflux while payment posting.
  .stockguardian.lms.enums.PaymentProtocol payment_protocol = 5;
  // we will use this for tracking the payment eg txn_utr
  string utr = 6;
  // This will be a remark related to repayment that will be passed to Finflux LMS
  string note = 7;
}

message PostLoanRepaymentResponse {
  rpc.Status status = 1;
}

message ForecloseLoanRequest {
  // amount repaid
  google.type.Money transaction_amount = 1 [(validate.rules).message.required = true];
  // denotes the unique external id which was passed to Finflux LMS for ForecloseLoan.
  string external_id = 2 [(validate.rules).string.min_len = 1];
  // denotes the repayment txn time.
  google.type.Date transaction_date = 3 [(validate.rules).message.required = true];
  // loan request id
  string loan_id = 4 [(validate.rules).string.min_len = 1];
  // this would be used to pass appropriate payment type to Finflux while payment posting.
  .stockguardian.lms.enums.PaymentProtocol payment_protocol = 5;
  // we will use this for tracking the payment eg txn_utr
  string utr = 6;
}

message ForecloseLoanResponse {
  rpc.Status status = 1;
}

message GetForeclosureDetailsRequest {
  // denotes the loan account against which the payment needs to be recorded in Finflux LMS,
  // we need to pass Finflux loan account id here.
  string loan_id = 1 [(validate.rules).string.min_len = 1];
  // denotes the date on which if foreclosure was tried then what should be the foreclosure amount.
  google.type.Date foreclosure_date = 2 [(validate.rules).message.required = true];
}

// https://drive.google.com/file/d/1B-iAYj6l-v2rO5hbNDmNtsglVLW4MjnR/view?usp=drive_link
message GetForeclosureDetailsResponse {
  rpc.Status status = 1;
  // amount is the total amount user has to pay to close the loan (this includes the excess amount as well)
  // net_foreclosure_amount will be 0 if loan is past maturity date but amount will be populated in all cases
  // since we have not configured excess payment for our products, amount always means foreclosure/closure amount for our use cases
  google.type.Money amount = 2;
  google.type.Money net_foreclosure_amount = 3;
  google.type.Money principal_portion = 4;
  google.type.Money interest_portion = 5;
  google.type.Money fee_charges_portion = 6;
  google.type.Money penalty_charges_portion = 7;
  google.type.Money outstanding_loan_balance = 8;
  // extra charges we have to pay in case we foreclose.
  google.type.Money foreclosure_charges = 9;
}

message GetLoanCancellationDetailsRequest {
  // denotes the loan account against which the payment needs to be recorded in Finflux LMS,
  // we need to pass Finflux loan account id here.
  string loan_id = 1 [(validate.rules).string.min_len = 1];
  // denotes the date on which cancellation details are requested
  google.type.Date cancellation_date = 2 [(validate.rules).message.required = true];
}

message GetLoanCancellationDetailsResponse {
  rpc.Status status = 1;
  google.type.Money loan_amount = 2;
  // cancellation_amount will include interest accrued till the cancellation date
  // interest to be waived off can be passed in the CancelLoanRequest if we do not want to collect interest in the cancellation period
  google.type.Money cancellation_amount = 3;
}

message CancelLoanRequest {
  // amount repaid
  google.type.Money transaction_amount = 1 [(validate.rules).message.required = true];
  // denotes the unique external id which was passed to Finflux LMS for ForecloseLoan.
  string external_id = 2 [(validate.rules).string.min_len = 1];
  // denotes the repayment txn time.
  google.type.Date transaction_date = 3 [(validate.rules).message.required = true];
  // loan request id
  string loan_id = 4 [(validate.rules).string.min_len = 1];
  // this would be used to pass appropriate payment type to Finflux while payment posting.
  .stockguardian.lms.enums.PaymentProtocol payment_protocol = 5;
  // interest to be waived off
  google.type.Money interest_waiver_amount = 6;
}

message CancelLoanResponse {
  rpc.Status status = 1;
}

message RejectLoanRequest {
  string loan_id = 1 [(validate.rules).string.min_len = 1];
  google.type.Date rejected_on = 2 [(validate.rules).message.required = true];
}

message RejectLoanResponse {
  rpc.Status status = 1;
  types.LoanStatus loan_status = 2;
  string loan_id = 3;
  string client_id = 4;
  google.type.Date rejected_on = 5;
  google.type.Date closed_on = 6;
}

message FetchLoanScheduleRequest {
  oneof identifier {
    option (validate.required) = true;
    // unique identifier of the loan in the Finflux system
    string loan_id = 1;
    // external id using which we've created the loan in the Finflux system
    string external_id = 2;
  }
}

message FetchLoanScheduleResponse {
  rpc.Status status = 1;
  types.LoanDetails loan = 2;
  repeated types.LoanRepaymentPeriod periods = 3;
  google.type.Money total_overpaid_amount = 4;
}

message CalculateLoanScheduleRequest {
  // Finflux's product unique identifier (this is generally unique for combination of lender and loan program)
  string product_id = 1 [(validate.rules).string.min_len = 1];
  google.type.Money principal_amount = 2 [(validate.rules).message.required = true];
  int32 number_of_installments = 3 [(validate.rules).int32.gte = 1];
  double interest_rate = 4;
  google.type.Date expected_disbursement_date = 5 [(validate.rules).message.required = true];
  // date when the loan application was submitted
  google.type.Date submitted_on = 6 [(validate.rules).message.required = true];
  // (inclusive of GST)
  // e.g.: 3.54 (for 3% processing fee and 18% GST on it)
  double processing_fee_percentage_including_gst = 7;
}

message CalculateLoanScheduleResponse {
  rpc.Status status = 1;
  google.type.Money total_principal_disbursed = 2;
  google.type.Money total_principal_expected = 3;
  google.type.Money total_principal_paid = 4;
  google.type.Money total_interest_charged = 5;
  google.type.Money total_fee_charges_charged = 6;
  google.type.Money total_penalty_charges_charged = 7;
  google.type.Money total_repayment_expected = 8;
  google.type.Money total_outstanding = 9;
  google.type.Money calculated_emi_amount = 10;
  repeated types.LoanRepaymentPeriod periods = 11;
  google.type.Money net_disbursal_amount = 12;
  google.type.Date expected_maturity_date = 13;
  double annual_percentage_rate = 14;
  int32 number_of_repayments = 15;
  types.FrequencyType repayment_frequency = 16;
  google.type.Money charges_due_at_disbursement = 17;
  google.type.Money broken_period_interest = 18;
}

message FetchLoanDetailsRequest {
  oneof identifier {
    option (validate.required) = true;
    // unique identifier of the loan in the Finflux system
    string loan_id = 1;
    // external id using which we've created the loan in the Finflux system
    string external_id = 2;
  }
}

message FetchLoanDetailsResponse {
  rpc.Status status = 1;
  types.LoanDetails loan = 2;
}

message CreateLoanRequest {
  // Finflux's product unique identifier (this is generally unique for combination of lender and loan program)
  string product_id = 1 [(validate.rules).string.min_len = 1];
  // unique identifier of the customer in the Finflux system
  string client_id = 2 [(validate.rules).string.min_len = 1];
  // vendor unique identifier of the loan at Fi
  string external_id = 3 [(validate.rules).string.min_len = 1];
  google.type.Money principal_amount = 4 [(validate.rules).message.required = true];
  google.type.Date submitted_on = 5 [(validate.rules).message.required = true];
  int32 number_of_repayments = 6 [(validate.rules).int32.gte = 1];
  // (inclusive of GST)
  // e.g.: 3.54 (for 3% processing fee and 18% GST on it)
  double processing_fee_percentage_including_gst = 7;
  // annual interest rate in percentage
  // e.g.: 9.0 (for 9% interest rate per annum)
  double interest_rate = 8;
  google.type.Date expected_disbursement_date = 9 [(validate.rules).message.required = true];
  // refers to the pool fund account from which the loan will be disbursed
  // This is required because all the report APIs take fund id as input and only provide the info of the loans that are part of that fund
  string fund_id = 10 [(validate.rules).string.min_len = 1];
  google.type.Money amount_for_upfront_collection = 11;
  // indicates whether to use flat overdue fee calculation
  bool is_flat_overdue_fee = 12;
}

message CreateLoanResponse {
  rpc.Status status = 1;
  // unique identifier of the loan in the Finflux system
  string loan_id = 2;
}

message DisburseLoanRequest {
  string loan_id = 1 [(validate.rules).string.min_len = 1];
  // it's not the disbursal amount, it's the loan amount excluding the processing charges etc.
  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];
  google.type.Date actual_disbursement_date = 3 [(validate.rules).message.required = true];
  // denotes the bank account id (whose details were already added in FF lms during client creation/update)
  // to which the loan amount is disbursed.
  int32 disbursement_bank_account_id = 4;
  // denotes the payment protocol of the disbursement transaction.
  .stockguardian.lms.enums.PaymentProtocol disbursement_txn_payment_protocol = 5;
  // denotes the UTR of the disbursement transaction
  string disbursement_utr = 6 ;
}

message DisburseLoanResponse {
  rpc.Status status = 1;
}

message ApproveLoanRequest {
  string loan_id = 1 [(validate.rules).string.min_len = 1];
  google.type.Date approved_on = 2 [(validate.rules).message.required = true];
  google.type.Money approved_loan_amount = 3 [(validate.rules).message.required = true];
  google.type.Date expected_disbursement_date = 4 [(validate.rules).message.required = true];
}

message ApproveLoanResponse {
  rpc.Status status = 1;
}

message CreateClientRequest {
  types.Client client = 1 [(validate.rules).message.required = true];
  api.typesv2.common.PostalAddress permanent_address = 2 [(validate.rules).message.required = true];
  api.typesv2.common.PostalAddress residential_address = 3 [(validate.rules).message.required = true];
  api.typesv2.common.PostalAddress kyc_address = 4 [(validate.rules).message.required = true];
}

message CreateClientResponse {
  rpc.Status status = 1;
  // unique identifier of the user in the Finflux system
  string client_id = 2;
}

message UpdateClientRequest {
  // denotes the identifier of the client (user) in the Finflux system
  string client_id = 1 [(validate.rules).string.min_len = 1];
  // denotes the user bank account details.
  BankAccountDetails bank_account_details = 2;

  message BankAccountDetails {
    string account_number = 1;
    string ifsc_code = 2;
    string account_holder_name = 3;
  }
}

message UpdateClientResponse {
  rpc.Status status = 1;
}

message FetchClientRequest {
  oneof identifier {
    option (validate.required) = true;
    // unique identifier of the customer in the Finflux system
    string client_id = 1;
    // vendor unique identifier of the user at Fi
    string external_id = 2;
  }
}

message FetchClientResponse {
  rpc.Status status = 1;
  types.Client client = 2;
}

message FetchBankAccountsRequest {
  // denotes the FF user whose bank accounts are to be fetched.
  string client_id = 1 [(validate.rules).string.min_len = 1];
}

message FetchBankAccountsResponse {
  rpc.Status status = 1;
  // denotes the bank account details of the user
  repeated BankAccountDetails bank_account_details = 2;

  message BankAccountDetails {
    int32 id = 1;
    string account_number = 2;
    string ifsc_code = 3;
  }
}

message GetDocumentRequest {
  // type of document we want to fetch eg; NOC, LAS etc.
  oneof RequestType {
    LoanAccountStatement loan_account_statement = 1;
    NoObjectionCertificate noc = 2;
  }
}

message LoanAccountStatement {
  // unique identifier of loan at LMS side.
  string loan_id = 1;
  // sets the end date for the report, meaning the report will include all transactions and account details up to this date.
  google.type.Date end_date = 5;
}

message NoObjectionCertificate {
  // unique identifier of loan at LMS side.
  string loan_id = 1;
}

message GetDocumentResponse {
  rpc.Status status = 1;
  // base64 of the returned document.
  string base64_document = 2;
}

message FetchProductResponse {
  rpc.Status status = 1;
  vendors.finflux.types.ProductDetails product_details = 2;
}

message GetReportRequest {
  types.ReportType report_type = 1;
  string office_id = 2;
  google.type.Date start_date = 3;
  google.type.Date end_date = 4;
}

message GetReportResponse {
  rpc.Status status = 1;
  string data = 2;
}

message PreDisbursementChargePaymentRequest {
  string loan_id = 1;
  string product_id = 2;
  google.type.Date transaction_date = 3;
  google.type.Money transaction_amount = 4;
  PaymentDetail payment_detail = 5;

  message PaymentDetail {
    string routing_code = 1;
    stockguardian.lms.enums.PaymentProtocol payment_protocol = 2;
  }
}

message PreDisbursementChargePaymentResponse {
  rpc.Status status = 1;
}
