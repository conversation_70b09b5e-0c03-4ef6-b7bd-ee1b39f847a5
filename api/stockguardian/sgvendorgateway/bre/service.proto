syntax = "proto3";

package stockguardian.sgvendorgateway.bre;

import "api/rpc/status.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/employment_type.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/name.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/sgvendorgateway/bre";
option java_package = "com.github.epifi.gringott.api.stockguardian.sgvendorgateway.bre";

service BreService{
  // rpc to check loan decisioning by as returned by scienaptic
  // need to send all the raw data and hit http server of iris/scienaptic
  // returns with if user is ineligible, or eligible with loan offer details
  rpc GetLoanDecisioning(GetLoanDecisioningRequest) returns (GetLoanDecisioningResponse);
}

message GetLoanDecisioningRequest{
  // customer for whom to fetch loan decisioning
  string customer_id = 1;
  string client_id = 2;
  google.type.Date dob = 3;
  api.typesv2.common.EmploymentType employment_type = 4;
  google.type.Money monthly_income = 5;
  api.typesv2.common.Name name = 6;
  api.typesv2.common.Gender gender = 7;
  string pan = 8;
  api.typesv2.common.PostalAddress address = 9;
  string employer_name = 10;
  string work_email = 11;
  string scheme_id = 13;
  string batch_id = 14;
  PolicyParams policy_params = 16;
}

message PolicyParams {
  string pricing_scheme = 1;
  string ever_vkyc_attempted = 2;
  double pd_score = 3;
  string pd_score_version = 4;
  string scheme_id = 5;
  string pricing_scheme_bre = 6;
  string batch_id = 7;
}

message GetLoanDecisioningResponse {
  rpc.Status status = 1;
  Decision loan_decision = 2;
  bytes raw_bre_response = 3;
  OfferDetails offer_details = 4;
  string scheme_id = 5;
  string batch_id = 6;
  string cust_id = 7;
  string loan_program = 8;
  repeated string external_reasons = 9;
  PolicyParams policy_params = 11;

  message OfferDetails {
    google.type.Money min_amount = 1;
    google.type.Money max_amount = 2;
    google.type.Money max_emi_amount = 3;
    double interest_percentage = 4;
    double processing_fee_percentage = 5;
    double gst_percentage = 6;
    int32 min_tenure_in_months = 7;
    int32 max_tenure_in_months = 8;
    google.type.Date emi_due_date = 9;
    google.protobuf.Timestamp valid_till = 10;
  }
}

enum Decision {
  DECISION_UNSPECIFIED = 0;
  DECISION_APPROVED = 1;
  DECISION_REJECTED = 2;
}
