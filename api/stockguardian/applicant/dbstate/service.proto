syntax = "proto3";

package stockguardian.applicant.dbstate;

import "api/sherlock/dev/db_state/db_state.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/applicant/dbstate";

service DBStateService {
  rpc GetEntityList (sherlock.dev.dbstate.GetEntityListRequest) returns (sherlock.dev.dbstate.GetEntityListResponse) {}

  // For each entity as defined above, the parameter required to fetch that data will be different
  // This service will return appropriate params based on entity passed in request
  rpc GetParameterList (sherlock.dev.dbstate.GetParameterListRequest) returns (sherlock.dev.dbstate.GetParameterListResponse) {}

  // The actual get data API call where we will make a DB call to get the required data
  rpc GetData (sherlock.dev.dbstate.GetDataRequest) returns (sherlock.dev.dbstate.GetDataResponse) {}
}
