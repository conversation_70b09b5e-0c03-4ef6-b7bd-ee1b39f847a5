//go:generate gen_sql -types=FailureReason,ValidationResult,PANStatus
syntax = "proto3";

package stockguardian.sgpan;

option go_package = "github.com/epifi/gringott/api/stockguardian/sgpan";

enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;
  // If the NSDL API responds with a name mismatch
  FAILURE_REASON_NAME_MISMATCH = 1;
  // If the NSDL API responds with a dob mismatch
  FAILURE_REASON_DOB_MISMATCH = 2;
  // If the NSDL API responds with both name & dob mismatch
  FAILURE_REASON_NAME_DOB_MISMATCH = 3;
}

enum PANStatus {
  PAN_STATUS_UNSPECIFIED = 0;
  PAN_STATUS_EXISTING_AND_VALID = 1;
  PAN_STATUS_MARKED_AS_FAKE = 2;
  PAN_STATUS_MARKED_AS_DEACTIVATED = 3;
  PAN_STATUS_DELETED = 4;
  PAN_STATUS_INVALID_PAN = 5;
  PAN_STATUS_EXISTING_MARKED_AMALGAMATION = 6;
  PAN_STATUS_EXISTING_MARKED_ACQUISITION = 7;
  PAN_STATUS_EXISTING_MARKED_DEATH = 8;
  PAN_STATUS_EXISTING_MARKED_DISSOLUTION = 9;
  PAN_STATUS_EXISTING_MARKED_LIQUIDATED = 10;
  PAN_STATUS_EXISTING_MARKED_MERGER = 11;
  PAN_STATUS_EXISTING_MARKED_PARTITION = 12;
  PAN_STATUS_EXISTING_MARKED_SPLIT = 13;
  PAN_STATUS_EXISTING_MARKED_UNDER_LIQUIDATION = 14;
  PAN_STATUS_INOPERATIVE = 15;
}
