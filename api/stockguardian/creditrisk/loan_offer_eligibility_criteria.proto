//go:generate gen_sql -types=LoanOfferEligibilityCriteria,PolicyParams,DataRequirementDetails
syntax = "proto3";

package stockguardian.creditrisk;

import "google/protobuf/timestamp.proto";
import "api/stockguardian/creditrisk/enums.proto";

option go_package = "github.com/epifi/gringott/api/stockguardian/creditrisk";
option java_package = "com.github.epifi.gringott.api.stockguardian.creditrisk";

message LoanOfferEligibilityCriteria{
    string id = 1;
    string customer_id = 2;
    string client_id = 3;
    LOECStatus status = 4;
    string sub_status = 5;
    string client_request_id = 6;
    string offer_id = 7;
    // Represents the timestamp at which the LOEC record reached terminal status, i.e either REJECTED or APPROVED
    google.protobuf.Timestamp completed_at = 8;
    // Will be more useful in pre-qualified flow where a user will be soft approved for some duration
    google.protobuf.Timestamp expires_at = 9;
    // Policy parameters based on which the user’s eligibility was evaluated
    PolicyParams policy_parameters = 10;
    google.protobuf.Timestamp created_at = 11;
    google.protobuf.Timestamp updated_at = 12;
    google.protobuf.Timestamp deleted_at = 13;
    DataRequirementDetails data_requirement_details = 14;
}

message PolicyParams {
  Pre pre = 1;
  Final final = 2;

  message Pre {
    double pd_score = 1;
    string pd_score_version = 2;
    string scheme_id = 3;
    string batch_id = 4;
    int32 ever_vkyc_attempted = 5;
    string pricing_scheme = 6;
  }
  message Final {
    string scheme_id = 1;
    string batch_id = 2;
    string pricing_scheme_bre = 3;
  }
}

message DataRequirementDetails {
  // GetLoanDecisioningV2 request id
  string loan_decisioning_v2_request_id = 1;
}
