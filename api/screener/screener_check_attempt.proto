syntax = "proto3";

package screener;

import "api/screener/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/screener";
option java_package = "com.github.epifi.gamma.api.screener";

message ScreenerCheckAttempt {
  string id = 1;
  string screener_attempt_id = 2;
  CheckType check_type = 3;
  CheckResult check_result = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  int64 deleted_at_unix = 7;
}
