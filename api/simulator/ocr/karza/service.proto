syntax = "proto3";

package simulator.ocr.karza;

import "api/vendors/karza/passport_ocr.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/ocr/karza";
option java_package = "com.github.epifi.gamma.api.simulator.ocr.karza";


service OCR {
  rpc ExtractPassport (vendors.karza.PassportOcrRequest) returns (vendors.karza.PassportOcrResponse) {
    option (google.api.http) = {
      post: "/karza/v1/extract_passport"
      body: "*"
    };
  };
}