syntax = "proto3";

package simulator.epan.karza;

import "google/api/annotations.proto";
import "api/vendors/karza/epan.proto";

option go_package = "github.com/epifi/gamma/api/simulator/epan/karza";
option java_package = "com.github.epifi.gamma.api.simulator.epan.karza";

service EPAN {
  // GetEPanStatus RPC is used to get the ePan events history
  rpc GetEPANStatus(vendors.karza.EPANStatusRequest) returns (vendors.karza.EPANStatusResponse) {
    option (google.api.http) = {
      get: "/karza/epan"
    };
  }
}
