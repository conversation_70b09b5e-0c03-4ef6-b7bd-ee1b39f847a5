//go:generate gen_sql -types=MfRequestType,MfRequestStatus,MutualFundFolioDetails,MfRequestDetails

syntax = "proto3";

package simulator.mutualfund;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/simulator/mutualfund";
option java_package = "com.github.epifi.gamma.api.simulator.mutualfund";

message MutualFundUser {
    string id = 1;
    string phone = 2;
    string email = 3;
    MutualFundFolioDetails folio_details = 4;
}

message MutualFundFolioDetails {
  repeated FolioDetailsEntity folio_list = 1;
}

message MutualFundRequest {
    string request_id = 1;
    MfRequestType type = 2;
    string client_ref_id = 3;
    string user_id = 4;
    MfRequestStatus status = 5;
    MfRequestDetails details = 6;
    google.protobuf.Timestamp created_at = 7;
    google.protobuf.Timestamp updated_at = 8;
    google.protobuf.Timestamp deleted_at = 9;
}

message FolioDetailsEntity {
  string folio_id = 1;
  string phone_number = 2;
  string email_id = 3;
  float units = 4;
  float available_units = 5;
  string isin = 6;
  string rta_name = 7;
  string amc=8;
  string scheme_code=9;
  float total_amount=10;
  float nav=11;
  bool is_demat=12;
}

enum MfRequestType {
    MF_REQUEST_TYPE_UNSPECIFIED = 0;
    MF_REQUEST_TYPE_CAS_DETAILED = 1;
    MF_REQUEST_TYPE_CAS_SUMMARY = 2;
    MF_REQUEST_TYPE_NET_PHONE_UPDATE = 3;
    MF_REQUEST_TYPE_NET_EMAIL_UPDATE = 4;
}

enum MfRequestStatus {
    MF_REQUEST_STATUS_UNSPECIFIED = 0;
    MF_REQUEST_STATUS_INITIATED = 1;
    MF_REQUEST_STATUS_SUCCESS = 2;
    MF_REQUEST_STATUS_FAILED = 3;
    MF_REQUEST_STATUS_FIRST_OTP_SUCCESS=4;
}

message MfRequestDetails {
    oneof details {
        NftUpdateEmail nft_update_email = 1;
        NftUpdatePhone nft_update_phone = 2;
        CasSummaryDetails cas_summary_details = 3;
    }
    message CasSummaryDetails{
        oneof contact{
        string phone=3;
        string email=4;
        }
    }
}

message NftUpdatePhone {
    string new_phone = 1;
    repeated string folio_ids = 2;
}

message NftUpdateEmail {
    string new_email = 1;
    repeated string folio_ids = 2;
}

enum MfRequestFieldMask {
    MF_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
    MF_REQUEST_FIELD_MASK_REQUEST_ID = 1;
    MF_REQUEST_FIELD_MASK_CLIENT_REF_ID = 2;
    MF_REQUEST_FIELD_MASK_USER_ID = 3;
    MF_REQUEST_FIELD_MASK_STATUS = 4;
    MF_REQUEST_FIELD_MASK_DETAILS = 5;
    MF_REQUEST_FIELD_MASK_TYPE = 6;
}

enum MfUserFieldMask {
    MF_USER_FIELD_MASK_UNSPECIFIED = 0;
    MF_USER_FIELD_MASK_ID = 1;
    MF_USER_FIELD_MASK_EMAIL = 2;
    MF_USER_FIELD_MASK_PHONE = 3;
    MF_USER_FIELD_MASK_FOLIO_DETAILS = 4;
}
