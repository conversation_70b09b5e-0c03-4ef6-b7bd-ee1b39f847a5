syntax = "proto3";

package simulator.namecheck.federal;

import "google/api/annotations.proto";
import "api/vendors/federal/namecheck.proto";

option go_package = "github.com/epifi/gamma/api/simulator/namecheck/federal";
option java_package = "com.github.epifi.gamma.api.simulator.namecheck.federal";


// Simulator service for Federal UN Name Check API.
// RBI has made it mandatory for banks to conduct necessary checks before
// opening a new account to ensure that the identity of the customer does
// not match with any person with known criminal background or with banned
// entities such as terrorist individuals or terrorist organizations.
// The proposed customer should not appear in the United Nations’ list
// under Security Council Resolutions and the terrorist lists circulated by RBI.
// Federal Bank has exposed UN Name Check API, in order to meet the above requirement
// Reference Spec: https://docs.google.com/document/d/1gnWLcSZWwfpyywUh9FVXJUjeIfLRvU1f/edit
service UNNameCheck {
  rpc UNNameCheck(vendors.federal.UNNameCheckRequest) returns (vendors.federal.UNNameCheckResponse) {
    option (google.api.http) = {
      post: "/bcwsgateway/services/UNCHKService"
      body: "*"
    };
  };
}
