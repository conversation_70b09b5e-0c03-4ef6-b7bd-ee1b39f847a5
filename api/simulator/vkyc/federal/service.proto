syntax = "proto3";

package simulator.vkyc.federal;

import "google/api/annotations.proto";
import "api/vendors/inhouse/vkyc.proto";

option go_package = "github.com/epifi/gamma/api/simulator/vkyc/federal";
option java_package = "com.github.epifi.gamma.api.simulator.vkyc.federal";

service VKYC {
  // SendAgentData RPC is used to sent agent data to vendor
  rpc SendAgentData(vendors.inhouse.TriggerAgentCallbackRequest) returns (vendors.inhouse.TriggerAgentCallbackResponse) {
    option (google.api.http) = {
      post: "/vkyc/federal/send-agent-data"
      body: "*"
    };
  }

  // SendAuditorData RPC is used to sent auditor data to vendor
  rpc SendAuditorData(vendors.inhouse.TriggerAuditorCallbackRequest) returns (vendors.inhouse.TriggerAuditorCallbackResponse) {
    option (google.api.http) = {
      put: "/vkyc/federal/send-auditor-data"
      body: "*"
    };
  }
}
