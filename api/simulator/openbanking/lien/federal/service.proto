syntax = "proto3";

package simulator.openbanking.lien.federal;

import "api/vendors/federal/lien.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/openbanking/lien/federal";
option java_package = "com.github.epifi.gamma.api.simulator.openbanking.lien.federal";

// Currently supported requests - ADD and ENQUIRY
// This API is used for adding, modifying, lifting, inquiring and enquiry lien and fetching the CBS response and status against the lien request.
// The input will be Account number, amount, currency, reason, start date, end date, request channel, etc.
// This is an async api, to get status of lien request use Request type as ENQUIRY
service Lien {
  // RPC method to add fresh lien and enquire lien on a particular account.
  // add or enquire will be inferenced from request type in request
  rpc LienMarking (LienRequest) returns (vendors.federal.LienResponse) {
    option (google.api.http) = {
      post: "/openbanking/lien/federal"
      body: "*"
    };
  }
}

message LienRequest {
  // lien request type. "ADD" to levy fresh lien
  string request_type = 1 [json_name = "reqType"];
  // account id on which lien should be put
  string account_id = 2 [json_name = "acctId"];
  // amount which should be put on lien
  string amount_value = 3 [json_name = "amountValue"];
  // currency code for lien amount
  string currency_code = 4 [json_name = "currencyCode"];
  // reason code for lien
  string reason_code = 5 [json_name = "reasonCode"];
  // remarks for the lien action
  string remarks = 6 [json_name = "Remarks"];
  // date at which lien should start on account
  string start_date = 7 [json_name = "StartDt"];
  // date at which lien should end on account
  string end_date = 8 [json_name = "endDt"];
  // unique request id generated by epifi, can be used for status check or retries.
  string channel_request_id = 9 [json_name = "channelRequesetId"];
  // channel, for us it is FI
  string channel = 10 [json_name = "Channel"];
}
