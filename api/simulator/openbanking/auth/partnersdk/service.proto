// Partner SDK has two components
//  1. Client side implementation
//  2. Server side implementation at partner bank's end
//
// Partner SDK is implemented in Java
// And both Java native implementation and Bouncy Castle are
// missing direct support of curve forms other than Weierstrass.
//
// Whereas golang crypto libraries support either Edwards or Montgomery forms
//
// To make this interoperable, crypto functionality of partner SDK will be
// used in Java language in a different deployment
//
// Simulator will communicate with this deployment via RPCs
// And this file defines the request/response protos for this RPC
//
// Java component of PartnerSDK is common for all the partner banks
//
// This service defines the RPCs that are required for the communication
// simulator in golang and simulator in java
// Hence, expect the package names out of sync with the standard practise
//
//
// This is a stateless service and hence expects all the crypto keys
// including private keys as an input to the RPCs

syntax = "proto3";

package simulator.openbanking.auth.partnersdk;

import "api/auth/partnersdk/credblock.proto";

option go_package = "github.com/epifi/gamma/api/simulator/openbanking/auth/partnersdk";
option java_package = "in.epifi.model";

service PartnerSDK {

  // GetSessionParams registers the client details at partner bank
  // and responds with session parameters. Client can make use of
  // these parameters to securely interact with SDK and partner bank
  rpc GetSessionParams(GetSessionParamsRequest) returns (GetSessionParamsResponse);

  // DecryptCredBlock decrypts the credentials
  rpc DecryptCredBlock(DecryptCredBlockRequest) returns (DecryptCredBlockResponse);

}

message ECDHKeyPair {
  // Unique identifier of the key pair
  string key_id = 1 [json_name="KeyId"];
  // Public key, base64 encoded
  string public_key = 2 [json_name="PublicKey"];
  // Private key, base64 encoded
  string private_key = 3 [json_name="PrivateKey"];
}

message GetSessionParamsRequest {
  // Basic auth factor
  string access_key = 1 [json_name="AccessKey"];

  // Partner bank's active key pair for encryption/decryption
  // Encryption Algo: X25519 + ECIES using AES 256
  ECDHKeyPair encrypt_key = 2 [json_name="PartnerEcdhEncryptKeyPair"];

  // Partner bank's active key pair for signature
  // Ed25519 signature
  ECDHKeyPair sign_key = 3 [json_name="PartnerEcdhSignKeyPair"];

  // List of session parameters that helps the partner bank
  // to authenticate the request
  message SessionParams {
    // Unique identifier of the partner bank's key
    // that is used in the process of generating
    // the session attributes
    string key_id = 1 [json_name="KeyId"];
    // Initial session keys to register the client app
    // with the partner bank
    // Base64 encoded
    string encrypted_keys = 2 [json_name="EncryptedKeys"];
    // HMAC of keys
    // If HMAC verification fails, the request will be rejected
    // Base64 encoded
    string hmac = 3 [json_name="Hmac"];
    // Ephemeral public key that is used in
    // Elliptic curve's Diffie hellman(ECDH) key agreement protocol
    // to generate the keys to encrypt the payload i.e., keys
    // Base64 encoded
    string ecdh_pk = 4 [json_name="EcdhPk"];
    // k0 of previous session, if available, base64 encoded
    string k0 = 5 [json_name = "K0"];
    // Unique Device ID of the client
    string device_id = 6 [json_name="DeviceId"];
    //app platform - Android, iOS etc.
    string platform = 7 [json_name = "Platform"];
  }
  SessionParams session_params = 4 [json_name="SessionParams"];
}


message GetSessionParamsResponse {
  // Client's key pair for encryption/decryption
  // Encryption Algo: X25519 + ECIES using AES 256
  ECDHKeyPair client_ecdh_key = 3 [json_name="ClientEcdhKeyPair"];

  // HMAC key that is to be used by the app
  // to generate HMAC for all the request payloads
  // between app and SDK
  // Base64 encoded
  string app_hmac_key = 4 [json_name="AppHmacKey"];

  // ECDH's Public key to be registered with the SDK
  // Encrypted value is to be passed to SDK as it is
  // Base64 encoded
  string encrypted_ecdh_pk = 5 [json_name="EncryptedEcdhPk"];

  // Signature of the payload
  // Signature is to be passed to SDK as it is
  // Base64 encoded
  string signature = 6 [json_name="Signature"];

  // There are multiple keys using which a partner bank
  // can sign a payload. Key ID identified the key
  // using which a payload has been signed
  // Signature's Key ID is to be passed to SDK as it is
  // Base64 encoded
  string signature_key_id = 7 [json_name="SignatureKeyId"];

  // Corresponding Response code and reason
  string response_code = 8 [json_name = "ResponseCode"];
}

message DecryptCredBlockRequest {
  // Basic auth factor
  string access_key = 1 [json_name="AccessKey"];

  // Client's active key pair for encryption/decryption
  // Encryption algo: X25519 + ECIES using AES 256
  ECDHKeyPair client_encrypt_key = 2 [json_name="ClientEcdhKeyPair"];

  message CredBlock {
    .auth.partnersdk.CredentialType credential_type = 1 [json_name="CredentialType"];
    string encrypted_credential = 2 [json_name="EncryptedCredential"];
    // Ephemeral public key that is used in
    // Elliptic curve's Diffie hellman(ECDH) key agreement protocol
    // to generate the keys to encrypt the payload i.e., keys
    // Base64 encoded
    string ecdh_pk = 3 [json_name="EcdhPK"];
  }
  repeated CredBlock credblocks = 3 [json_name="CredBlocks"];
}

message DecryptCredBlockResponse {
  message DecryptedCredBlock {
    // Type of the credential
    .auth.partnersdk.CredentialType credential_type = 1 [json_name="CredentialType"];
    // Credential
    // Can be PIN + Salt as per the workflow
    string plain_credential = 2 [json_name="PlainCredential"];
  }
  repeated DecryptedCredBlock credblocks = 1 [json_name="CredBlocks"];
  // Response code
  string response_code = 2 [json_name = "ResponseCode"];
}
