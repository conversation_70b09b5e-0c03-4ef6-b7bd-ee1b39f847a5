// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package simulator.openbanking.accounts.federal;

import "api/vendors/federal/account.proto";
import "api/vendors/federal/customer.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/openbanking/accounts/federal";
option java_package = "com.github.epifi.gamma.api.simulator.openbanking.accounts.federal";

service Accounts {

  // TODO(PRANSHU): move createCustomer simulator customer service
  // monorail :- https://monorail.pointz.in/p/fi-app/issues/detail?id=38158
  // RPC method to create customer on federal bank.
  rpc CreateCustomer (vendors.federal.CreateCustomerRequest) returns (vendors.federal.CreateCustomerResponse) {
    option (google.api.http) = {
      post: "/createCustomerFederal"
      body: "*"
    };
  }

  // RPC method to create account on federal bank.
  // TODO(team): move this to savings service
  rpc CreateAccount (vendors.federal.CreateAccountRequest) returns (vendors.federal.CreateAccountResponse) {
    option (google.api.http) = {
      post: "/createAccountFederal"
      body: "*"
    };
  }

  // RPC method to for enquiring the balance for a savings account. The API returns Ledger Balance
  // (which is the available balance as of the beginning of the day) and Available Balance (Ledger balance
  // plus or minus any subsequent activity in the account, balance at that point of time). In addition, the
  // API returns the Account Holder Name, Account Type and Status also.
  // This is a SYNC rpc
  // TODO(team): move this to savings service
  rpc GetBalance (vendors.federal.GetBalanceRequest) returns (vendors.federal.GetBalanceResponse) {
    option (google.api.http) = {
      post: "/openbanking/accounts/federal/getBalance"
      body: "*"
    };
  }


  // GetAccountStatement RPC can be used to get statement for an account created by epiFi (both savings and deposit)
  // from start date to end date. Start date and end date values are passed in the request message.
  //
  // The requestID format for GetAccountStatement is:
  // NEOLIGAS<DDD><yyyyMMdd><hhmmss><XXXXX (5digit sequence Number)>
  // The request message should include the account number for which the statement is to be fetched. It should
  // also include the customerID along with the start and end date values. It also includes the PageNum attribute which
  // specifies the page number from which the next set of transactions is to be fetched.
  //
  // The response includes all transaction details belonging to the deposit account. Each transaction comprises of the
  // transaction amount, transaction type (C - Credit, D - Debit), transaction reference number, etc.
  rpc GetAccountStatement (vendors.federal.GetAccountStatementRequest) returns (vendors.federal.GetAccountStatementResponse) {
    option (google.api.http) = {
      post: "/openbanking/accounts/federal/GetAccStatement"
      body: "*"
    };
  };

  rpc GetAccountStatementByDrApi (vendors.federal.GetAccountStatementByDrApiRequest) returns (vendors.federal.GetAccountStatementByDrResponse) {
    option (google.api.http) = {
      post: "/openbanking/accounts/federal/GetAccStatementByDrApi"
      body: "*"
    };
  };

  // GetCustomerDetails rpc is used to fetch customer details which includes the communication, permanent & shipping
  // addresses along with other details like salutation, customer name, gender, email, occupation etc.
  rpc GetCustomerDetails (vendors.federal.GetCustomerDetailsRequest) returns (vendors.federal.GetCustomerDetailsResponse) {
    option (google.api.http) = {
      post: "/openbanking/accounts/federal/GetCustomerDetails"
      body: "*"
    };
  }

  // GetClosingBalance rpc is used to fetch Closing Balance for an account number based on a given date.
  // ClosingBalance asked for a given date will provide closing balance on the last EOD for that account.
  rpc GetClosingBalance (vendors.federal.GetClosingBalanceRequest) returns (vendors.federal.GetClosingBalanceResponse) {
    option (google.api.http) = {
      post: "/openbanking/accounts/federal/GetClosingBalance"
      body: "*"
    };
  }

  // GetBalanceV1 RPC method for enquiring the balance for a savings account. The API returns Ledger Balance
  // (which is the available balance as of the beginning of the day), Available Balance (Ledger balance
  // plus or minus any subsequent activity in the account, balance at that point of time) and Lien amount.
  // In addition, the API returns the Customer Name, Freeze code, Freeze Reason and timestamp at which the
  // returned balance was computed.
  rpc GetBalanceV1 (vendors.federal.GetBalanceV1Request) returns (vendors.federal.GetBalanceV1Response) {
    option (google.api.http) = {
      post: "/openbanking/accounts/federal/GetBalanceV1"
      body: "*"
    };
  }

  // GetMiniStatement RPC can be used to get statement for an account created by epiFi (both savings and deposit)
  // from start date to end date. Start date and end date values are passed in the request message.
  // The request message should include the account number for which the statement is to be fetched.
  // Compared to GetAccountStatement RPC, we get the same transaction body in response except the running balance field.
  rpc GetMiniStatement (vendors.federal.GetMiniStatementRequest) returns (vendors.federal.GetMiniStatementResponse) {
    option (google.api.http) = {
      post: "/openbanking/accounts/federal/GetMiniStatement"
      body: "*"
    };
  }

  // GetAccountStatus RPC to get account status from for given account
  // Response includes all account details belonging to the account comprising of the
  // last transaction amount, freeze status (Total freeze, Credit freeze), account status (active, dormant etc.)
  // API returns NOT_FOUND in-case of no details found for account
  // This is a SYNC rpc
  rpc AccountStatusEnquiry (vendors.federal.AccountStatusEnquiryRequest) returns (vendors.federal.AccountStatusEnquiryResponse) {
    option (google.api.http) = {
      post: "/openbanking/accounts/federal/AccountStatusEnquiry"
      body: "*"
    };
  }

  // ThirdPartyAccountCollection RPC to share third party account details with vendor
  rpc ThirdPartyAccountCollection (vendors.federal.ThirdPartyAccountCollectionRequest) returns (vendors.federal.ThirdPartyAccountCollectionResponse) {
    option (google.api.http) = {
      post: "/openbanking/accounts/federal/tpAccountCollection"
      body: "*"
    };
  };
}
