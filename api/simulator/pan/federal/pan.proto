syntax = "proto3";

package simulator.pan.federal;

import "api/vendors/federal/pan_aadhaar_validation.proto";
import "api/vendors/federal/panvalidation.proto";
import "api/vendors/federal/panvalidationv2.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/pan/federal";
option java_package = "com.github.epifi.gamma.api.simulator.pan.federal";


// Simulator service for Federal PAN Validation API
// Reference Spec: https://drive.google.com/file/d/1RQktJMCQHmhVbtlfOJQ3Pla5aKFH3GgY/view
service PAN {
  rpc ValidatePAN (vendors.federal.ValidatePANRequest) returns (vendors.federal.ValidatePANResponse) {
    option (google.api.http) = {
      post: "/fedbnk/uat/pan/validation"
      body: "*"
    };
  };

  rpc ValidatePANAadhaar (vendors.federal.PANAadhaarValidationRequest) returns (vendors.federal.PANAadhaarValidationResponse) {
    option (google.api.http) = {
      post: "/panAadhaarValidation"
      body: "*"
    };
  };

  rpc ValidateV2 (vendors.federal.ValidatePanV2Request) returns (vendors.federal.ValidatePanV2Response) {
    option (google.api.http) = {
      post: "/fedbnk/pan/v2.0.0/validate"
      body: "*"
    };
  };
}
