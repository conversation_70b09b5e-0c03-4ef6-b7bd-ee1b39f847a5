syntax = "proto3";

package simulator.usstocks.alpaca;

import "api/vendors/alpaca/investment.proto";
import "api/vendors/alpaca/funding.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/usstocks/alpaca";
option java_package = "com.github.epifi.gamma.api.simulator.usstocks.alpaca";

service USStocks {
  rpc CreateAccount (vendors.alpaca.CreateAccountRequest) returns (vendors.alpaca.Account) {
    option (google.api.http) = {
      post: "/test/alpaca/v1/accounts"
      body:"*"
    };
  }

  rpc UploadCIPDetails(vendors.alpaca.UploadCIPDetailRequest) returns (vendors.alpaca.UploadCIPDetailResponse) {
    option (google.api.http) = {
      post: "/test/alpaca/v1/accounts/{account_id}/cip"
      body: "*"
    };
  }

  rpc CreateOrder (vendors.alpaca.CreateOrderRequest) returns (vendors.alpaca.Order) {
    option (google.api.http) = {
      post: "/test/alpaca/v1/trading/accounts/{account_id}/orders"
      body: "*"
    };
  }

  rpc GetAccountDetails(vendors.alpaca.GetAccountDetailsRequest) returns (vendors.alpaca.Account) {
    option (google.api.http) = {
      get: "/test/alpaca/v1/accounts/{account_id}"
    };
  }

  rpc GetOrderDetails(vendors.alpaca.GetOrderDetailsRequest) returns (vendors.alpaca.Order) {
    option (google.api.http) = {
      get: "/test/alpaca/v1/trading/accounts/{account_id}/orders:by_client_order_id"
    };
  }

  rpc GetAllOpenPositions(AlpacaRequest) returns (vendors.alpaca.GetAllOpenPositionsResponse){
    option (google.api.http) = {
      get:"/test/alpaca/v1/trading/accounts/{account_id}/positions"
    };
  }

  rpc GetPositionForSymbol(AlpacaRequest) returns (vendors.alpaca.Position){
    option (google.api.http) = {
      get: "/test/alpaca/v1/trading/accounts/{account_id}/positions/{symbol}"
    };
  }

  rpc UpdateTradeConfiguration(AlpacaRequest) returns (vendors.alpaca.TradeConfigurations){
    option (google.api.http) = {
      patch: "/test/alpaca/v1/trading/accounts/{account_id}/account/configurations"
      body: "*"
    };
  }

  rpc GetReportFile(AlpacaRequest) returns (vendors.alpaca.GetReportFileResponse){
    option (google.api.http) = {
      get: "/test/alpaca/v1/transfers/jit/reports"
    };
  }

  rpc CancelOrder(AlpacaRequest) returns (vendors.alpaca.CancelOrderResponse){
    option (google.api.http) = {
      delete: "/test/alpaca/v1/trading/accounts/{account_id}/orders/{order_id}"
    };
  }

  rpc GetExchangeStatus(vendors.alpaca.GetExchangeStatusRequest) returns (vendors.alpaca.GetExchangeStatusResponse){
    option (google.api.http) = {
      get: "/test/alpaca/v1/clock"
    };
  }

  /*
  200 - Success
    The created Bank relationship
  400 - Bad Request
  409 - Conflict
    A Bank relationship already exists for this account
   */
  rpc SendBankDetails (vendors.alpaca.BankDetailsRequest) returns (vendors.alpaca.Bank) {
    option (google.api.http) = {
      post: "/test/alpaca/v1/accounts/{account_id}/recipient_banks"
      body: "*"
    };
  }

  rpc GetBankDetails(vendors.alpaca.GetBankDetailsRequest) returns (vendors.alpaca.GetBankDetailsResponse){
    option (google.api.http) = {
      get: "/test/alpaca/v1/accounts/{account_id}/recipient_banks"
    };
  }

  // UpdateAccount for PATCH /v1/accounts/{account_id}
  rpc UpdateAccount(vendors.alpaca.UpdateAccountRequest) returns (vendors.alpaca.UpdateAccountResponse) {
    option (google.api.http) = {
      patch: "/test/alpaca/v1/accounts/{account_id}"
      body: "*"
    };
  }

}

// Store's required query and path param from url
message AlpacaRequest{
  string account_id = 1 [json_name = "account_id"];
  string order_id = 2 [json_name = "order_id"];
  string symbol = 3 [json_name = "symbol"];
  string report_type = 4[json_name = "report_type"];
  string system_date = 5[json_name = "system_date"];
  string sort_order = 6[json_name = "direction"];
  string page_size = 7[json_name = "page_size"];
  string page_token = 8[json_name = "page_token"];
}
