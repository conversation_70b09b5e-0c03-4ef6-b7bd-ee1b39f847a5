// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package simulator.shipway;

import "api/vendors/shipway/shipway.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/shipway";
option java_package = "com.github.epifi.gamma.api.simulator.shipway";

service Shipway {
  rpc GetOrderShipmentDetails (vendors.shipway.GetOrderShipmentDetailsRequest) returns (vendors.shipway.GetOrderShipmentDetailsResponse) {
    option (google.api.http) = {
      post: "/shipway/GetOrderShipmentDetails"
      body: "*"
    };
  }

  rpc AddOrUpdateWebhook (vendors.shipway.AddOrUpdateWebhookRequest) returns (vendors.shipway.AddOrUpdateWebhookResponse) {
    option (google.api.http) = {
      post: "/shipway/AddOrUpdateWebhook"
      body: "*"
    };
  }

  rpc PushOrderData(vendors.shipway.PushOrderDataRequest) returns (vendors.shipway.PushOrderDataResponse) {
    option (google.api.http) = {
      post: "/shipway/PushOrderData"
      body: "*"
    };
  }
}
