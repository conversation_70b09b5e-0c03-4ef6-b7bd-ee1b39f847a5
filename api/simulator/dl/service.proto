syntax = "proto3";

package simulator.dl;

import "api/vendors/karza/dl_auth.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/dl/karza";
option java_package = "com.github.epifi.gamma.api.simulator.dl.karza";

service DL {
  // rpc to validate DL (Driving License) details in a user's CKYC record.
  // We use this API to validate whether the DL present in CKYC records is valid or expired.
  rpc DLExpiryCheck(vendors.karza.DLExpiryCheckRequest) returns (vendors.karza.DLExpiryCheckResponse) {
    option(google.api.http) = {
      post: "/v3/dl"
      body: "*"
    };
  }
}
