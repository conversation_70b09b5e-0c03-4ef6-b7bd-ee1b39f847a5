// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package simulator.p2pinvestment.liquiloans;

import "api/vendors/liquiloans/investment.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/p2pinvestment/liquiloans";
option java_package = "com.github.epifi.gamma.api.simulator.p2pinvestment.liquiloans";


service Liquiloans {
  rpc CreateInvestor (vendors.liquiloans.CreateInvestorRequest) returns (vendors.liquiloans.CreateInvestorResponse) {
    option (google.api.http) = {
      post: "/CreateInvestor"
      body: "*"
    };
  }

  rpc GetInvestorDashboard (vendors.liquiloans.GetInvestorDashboardRequest) returns (vendors.liquiloans.GetInvestorDashboardResponse) {
    option (google.api.http) = {
      post: "/GetInvestorDashboard"
      body: "*"
    };
  }

  rpc AddMoneyInvestorOffline (vendors.liquiloans.AddMoneyInvestorOfflineRequest) returns (vendors.liquiloans.AddMoneyInvestorOfflineResponse) {
    option (google.api.http) = {
      post: "/AddMoneyInvestorOffline"
      body: "*"
    };
  }

  rpc WithdrawMoneyInvestor (vendors.liquiloans.WithdrawMoneyInvestorRequest) returns (vendors.liquiloans.WithdrawMoneyInvestorResponse) {
    option (google.api.http) = {
      post: "/WithdrawMoneyInvestor"
      body: "*"
    };
  }

  rpc GetTransactionByExtTxnId (vendors.liquiloans.GetTransactionByExtTxnIdRequest) returns (vendors.liquiloans.GetTransactionByExtTxnIdResponse) {
    option (google.api.http) = {
      post: "/GetTransactionByExtTxnId"
      body: "*"
    };
  }

  rpc GetTransactionById (vendors.liquiloans.GetTransactionByIdRequest) returns (vendors.liquiloans.GetTransactionByIdResponse) {
    option (google.api.http) = {
      post: "/GetTransactionById"
      body: "*"
    };
  }

  rpc UploadInvestorDocs (vendors.liquiloans.UploadInvestorDocsRequest) returns (vendors.liquiloans.UploadInvestorDocsResponse) {
    option (google.api.http) = {
      post: "/UploadInvestorDocs"
      body: "*"
    };
  }

  rpc IsInvestorExist (vendors.liquiloans.IsInvestorExistRequest) returns (vendors.liquiloans.IsInvestorExistResponse) {
    option (google.api.http) = {
      post: "/IsInvestorExist"
      body: "*"
    };
  }

  rpc GetInvestmentSummary (vendors.liquiloans.GetInvestmentSummaryRequest) returns (vendors.liquiloans.GetInvestmentSummaryResponse) {
    option (google.api.http) = {
      post: "/GetInvestmentSummary"
      body: "*"
    };
  }

  rpc VerifyCkyc (vendors.liquiloans.VerifyCkycRequest) returns (vendors.liquiloans.VerifyCkycResponse) {
    option (google.api.http) = {
      post: "/Ifa/VerifyCkyc"
      body: "*"
    };
  }

  rpc GetMaxInvestmentAmount (vendors.liquiloans.GetMaxInvestmentAmountRequest) returns (vendors.liquiloans.GetMaxInvestmentAmountResponse) {
    option (google.api.http) = {
      post: "/GetMaxInvestmentAmount"
      body: "*"
    };
  }
  rpc CreateMaturityAction (vendors.liquiloans.CreateMaturityActionRequest) returns (vendors.liquiloans.CreateMaturityActionResponse) {
    option (google.api.http) = {
      post: "/CreateMaturityAction"
      body: "*"
    };
  }
  rpc GetMaturityLinkOTP (vendors.liquiloans.GetMaturityLinkOTPRequest) returns (vendors.liquiloans.GetMaturityLinkOTPResponse) {
    option (google.api.http) = {
      post: "/GetMaturityLinkOTP"
      body: "*"
    };
  }
  rpc GetInvestorMaturityTransactions (vendors.liquiloans.GetInvestorMaturityTransactionsRequest) returns (vendors.liquiloans.GetInvestorMaturityTransactionsResponse) {
    option (google.api.http) = {
      post: "/GetInvestorMaturityTransactions"
      body: "*"
    };
  }
  rpc GetBulkMaturityTransactions (vendors.liquiloans.GetBulkMaturityTransactionsRequest) returns (vendors.liquiloans.GetBulkMaturityTransactionsResponse) {
    option (google.api.http) = {
      post: "/GetBulkMaturityTransactions"
      body: "*"
    };
  }
  rpc CreateMaturityActionByRequestId (vendors.liquiloans.CreateMaturityActionByRequestIdRequest) returns (vendors.liquiloans.CreateMaturityActionByRequestIdResponse) {
    option (google.api.http) = {
      post: "/CreateMaturityActionByRequestId"
      body: "*"
    };
  }
  rpc GetCashLedger (vendors.liquiloans.GetCashLedgerRequest) returns (vendors.liquiloans.GetCashLedgerResponse) {
    option (google.api.http) = {
      post: "/GetCashLedger"
      body: "*"
    };
  }
  rpc MaturityUploadInvestorDocs (vendors.liquiloans.MaturityUploadInvestorDocsRequest) returns (vendors.liquiloans.MaturityUploadInvestorDocsResponse) {
    option (google.api.http) = {
      post: "/MaturityUploadInvestorDocs"
      body: "*"
    };
  }
}
