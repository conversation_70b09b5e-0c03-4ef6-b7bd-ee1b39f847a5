// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package simulator.p2pinvestment.developer;

import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/simulator/p2pinvestment/developer";
option java_package = "com.github.epifi.gamma.api.simulator.p2pinvestment.developer";

message UpdateP2PVendorResponsesApprovalStatusRequest {
  // p2pVendorResponses id of the user for which GetTransactionByTxnId approval status is being updated
  string p2p_vendor_responses_id = 1 [deprecated = true];
  string response_data_type = 2 [deprecated = true];
  string approval_status = 3;
  string order_external_id = 4;
  string vendor_transaction_id = 5;
  string actor_id = 6;
}

message UpdateP2PVendorResponsesApprovalStatusResponse {
  rpc.Status status = 1;
}

message UpdateP2PVendorResponseMaturityTransactionDaysToExpireRequest {
  string actor_id = 1;
  string maturity_action_type = 2;
  int32 remaining_days_to_expire = 3;
  string order_external_id = 4;
  string vendor_transaction_id = 5;
}

message UpdateP2PVendorResponseMaturityTransactionDaysToExpireResponse {
  rpc.Status status = 1;
}

// gRPC service to facilitate sherlock debugging tool for stimulator service related entities for p2p
service DevSimulatorP2PInvestment {
  // The function where we will make a DB call to update the P2PVendorResponses approval status
  rpc UpdateP2PVendorResponsesApprovalStatus(UpdateP2PVendorResponsesApprovalStatusRequest) returns (UpdateP2PVendorResponsesApprovalStatusResponse) {}
  // This is use to change the expiry date of maturity transaction
  // when the number of days is 0, we will the transaction as approved
  // for some other days just update the maturity transaction date
  rpc UpdateP2PVendorResponseMaturityTransactionDaysToExpire(UpdateP2PVendorResponseMaturityTransactionDaysToExpireRequest) returns (UpdateP2PVendorResponseMaturityTransactionDaysToExpireResponse) {}
}
