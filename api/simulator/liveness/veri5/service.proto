// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package simulator.liveness.veri5;

import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/simulator/liveness/veri5";
option java_package = "com.github.epifi.gamma.api.simulator.liveness.veri5";

message CheckLivenessRequest {
  // Video in base64 encoding
  string video_file = 1 [json_name = "videoFile"];
  // OTP being said.
  string otp = 2 [json_name = "otp"];
}

message CheckLivenessResponse {
  // Score 0-100 for OTP check.
  float otp_match_score = 1 [json_name = "matchScore"];
  // Score 0-100 for passive liveness.
  float liveness_score = 2 [json_name = "livenessScore"];
}

message MatchFaceRequest {
  message Headers {
    string client_code = 1 [json_name = "client_code"];
    string sub_client_code = 2 [json_name = "sub_client_code"];
    string channel_code = 3 [json_name = "channel_code"];
    string channel_version = 4 [json_name = "channel_version"];
    string Stan = 5 [json_name = "<PERSON>"];
    string client_ip = 6 [json_name = "client_ip"];
    string transmission_datetime = 7 [json_name = "transmission_datetime"];
    string operation_mode = 8 [json_name = "operation_mode"];
    string run_mode = 9 [json_name = "run_mode"];
    string actor_type = 10 [json_name = "actor_type"];
    string user_handle_type = 11 [json_name = "user_handle_type"];
    string user_handle_value = 12 [json_name = "user_handle_value"];
    string location = 13 [json_name = "location"];
    string function_code = 14 [json_name = "function_code"];
    string function_sub_code = 15 [json_name = "function_sub_code"];
  }
  Headers headers = 1 [json_name = "headers"];
  message SubRequest {
    string api_key = 1 [json_name = "api_key"];
    string request_id = 2 [json_name = "request_id"];
    string purpose = 5 [json_name = "purpose"];
    string hash = 6 [json_name = "hash"];
    // Image1 in base64 encoding
    string image_1 = 3 [json_name = "image_1"];
    // Image2 in base64 encoding
    string image_2 = 4 [json_name = "image_2"];
  }
  SubRequest request = 2 [json_name = "request"];
}

message MatchFaceResponse {
  message ResponseData {
    float score = 1;
    string hash = 2;
    string status = 3;
  }
  ResponseData response_data = 1 [json_name = "response_data"];
  message ResponseStatus {
    string code = 1;
    string message = 2;
    string status = 3;
  }
  ResponseStatus response_status = 2 [json_name = "response_status"];
}

// Liveness simulates service provided by liveness vendors including liveness check and face match.
service Liveness {
  rpc CheckLiveness (CheckLivenessRequest) returns (CheckLivenessResponse) {
    option (google.api.http) = {
      post: "/video-id-kyc/api/1.0/liveness"
      body: "*"
    };
  }

  rpc MatchFace (MatchFaceRequest) returns (MatchFaceResponse) {
    option (google.api.http) = {
      post: "/video-id-kyc/api/1.0/faceCompare"
      body: "*"
    };
  }
}
