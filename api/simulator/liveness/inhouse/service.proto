// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package simulator.liveness.inhouse;

import "google/api/annotations.proto";
import "api/vendors/inhouse/liveness.proto";

option go_package = "github.com/epifi/gamma/api/simulator/liveness/inhouse";
option java_package = "com.github.epifi.gamma.api.simulator.liveness.inhouse";

service Liveness {
  rpc CheckInhouseLiveness (vendors.inhouse.CheckLivenessRequest) returns (vendors.inhouse.CheckLivenessResponse) {
    option (google.api.http) = {
      post: "/inhouse-liveness"
      body: "*"
    };
  }

  rpc MatchFaceInhouse (vendors.inhouse.MatchFaceInHouseRequest) returns (vendors.inhouse.MatchFaceInHouseResponse) {
    option (google.api.http) = {
      post: "/inhouse-facematch"
      body: "*"
    };
  }
}
