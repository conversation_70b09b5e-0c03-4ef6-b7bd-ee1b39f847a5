syntax = "proto3";

package simulator.wealth.mutualfund.cams;

import "api/queue/consumer_headers.proto";
import "api/investment/mutualfund/order/order.proto";

option go_package = "github.com/epifi/gamma/api/simulator/wealth/mutualfund/cams";
option java_package = "com.github.epifi.gamma.api.simulator.wealth.mutualfund.cams";


service CamsService {
  // GenerateReverseFeedFileAfterPaymentNotification processes the orders for which credit MIS report is uploaded successfully i.e. payment credit is notified successfully.
  // This is to simulate the case when credit mis file upload is done manually on the RTA's portal.
  // When a credit MIS report is uploaded, simulator is expected to generate a reverse feed file and upload to SFTP server.
  //
  // The rpc listens to SQS events that contains necessary data to generate a reverse feed file. A single update packet may contain data for multiple orders. A reverse feed file is expected to
  // contain all these orders.
  rpc GenerateReverseFeedFileAfterPaymentNotification(GenerateReverseFeedFileAfterPaymentNotificationRequest) returns (GenerateReverseFeedFileAfterPaymentNotificationResponse) {}
}

message GenerateReverseFeedFileAfterPaymentNotificationRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  repeated api.investment.mutualfund.order.Order orders = 2;
}

message GenerateReverseFeedFileAfterPaymentNotificationResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}
