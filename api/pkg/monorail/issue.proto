syntax = "proto3";

package pkg.monorail;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/be-common/api/pkg/monorail";
option java_package = "com.github.epifi.gamma.api.pkg.monorail";

message Issue {
  int64 id = 1;
  // the project to which this issue belongs
  string project_id = 2;
  // Title shown on top the monorail issue -- same as Summary
  string title = 3;
  // Summary / title of the monorail issue
  string summary = 4;
  // Detailed description of the monorail issue
  string description = 5;
  // Author of the monorail issue. i.e. reported by.
  Person author = 6;
  // The owner i.e. to whom the monorail issue is assigned
  Person owner = 7;
  // persons cc-ed (carbon copy) in the monorail issue
  repeated Person cc = 8;
  // BlockedOn: what other issues this issue is blocked on.
  repeated int64 blocked_on = 9;
  // Blocking: the issues blocked on this issue.
  repeated int64 blocking = 10;
  // components to which this monorail issue belongs
  repeated string components = 11;
  // labels attached to this monorail issue
  repeated string labels = 12;
  // Granular status of the issue
  MonorailIssueStatus status = 13;
  // State of the issue i.e. open/ closed
  MonorailIssueState state = 14;
  google.protobuf.Timestamp created_at = 15;
  google.protobuf.Timestamp updated_at = 16;
  // The timestamp at which the owner was last updated
  google.protobuf.Timestamp owner_updated_at = 17;
  // The timestamp at which the status was last updated
  google.protobuf.Timestamp status_updated_at = 18;
  // The timestamp at which the component was last updated
  google.protobuf.Timestamp component_updated_at = 19;
  // Custom fields and their values in the monorail issues
  // For a multivalued custom field there will be multiple entries for the FieldName in this list
  repeated CustomFieldValue custom_field_values = 20;
  // approval logs related to approval type monorail requests i.e. DataAccessApprover/ ApplicationAccessApprover
  repeated MonorailApprovalLog approval_log = 21;
}

message Person {
  // email Id of the person
  string email = 1;
  string html_link = 2;
  bool is_email_bouncing = 3;
  int64 last_visit_day_ago = 4;
}

message Comment {
  Person author = 1;
  string content = 2;
  bool is_description = 3;
  google.protobuf.Timestamp created_at = 4;
}

// Approval log gives info related to approval type monorail issues
message MonorailApprovalLog {
  // approval type represent various types of monorail approval requests
  // i.e. DataAccessApprover/ ApplicationAccessApprover/ BAAccessApprover
  string approval_type = 1;
  // time at which the issue was approved
  google.protobuf.Timestamp approval_time = 2;
  // approval status i.e. approved/ NeedsReview/ ReviewRequested/ NeedInfo
  string approval_status = 3;
}

// Filter applied to get a list of monorail issues
message MonorailIssueFilters {
  // Generic options for filtering the tickets
  CannedOption canned_option = 1;
  // Filter by Label
  string label = 2;
  // Filter by owner:(Email Id of the person object)
  string owner = 3;
  // filter by status
  MonorailIssueStatus status = 4;
  // max created_at timestamp
  google.protobuf.Timestamp created_max = 5;
  // min created_at timestamp
  google.protobuf.Timestamp created_min = 6;
  // max updated_at timestamp
  google.protobuf.Timestamp updated_max = 7;
  // min updated_at timestamp
  google.protobuf.Timestamp updated_min = 8;
  // maximum number of results to be returned (for pagination)
  int32 max_results = 9;
  // start index of results to be returned (for pagination)
  int32 start_index = 10;
  // custom query which we would enter on monorail search bar eg: "reporter:<EMAIL>"
  string custom_query = 11;
}

message CustomFieldValue {
  // name of the custom field
  string field_name = 1;
  // A single value of the custom field
  // Even for a multi-valued field, this corresponds to a single value
  string field_value = 2;
  bool derived = 3;
}

// Canned options for filtering the ticket
enum CannedOption {
  CANNED_OPTION_UNSPECIFIED = 0;
  // default value is all
  CANNED_OPTION_ALL = 1;
  // to get new issues
  CANNED_OPTION_NEW = 2;
  // to get all open tickets
  CANNED_OPTION_OPEN =3;
  // to get all issues owned by the current user
  CANNED_OPTION_OWNED = 4;
  // to get all issues reported by the current user
  CANNED_OPTION_REPORTED = 5;
  // to filter all the starred issues
  CANNED_OPTION_STARRED = 6;
  // to filter all issues to be verified
  CANNED_OPTION_TO_VERIFY = 7;
}

// Current status of the monorail issue
enum MonorailIssueStatus {
  MONORAIL_ISSUE_STATUS_UNSPECIFIED = 0;
  // new issue: Issue has not had initial review yet
  // state: open
  MONORAIL_ISSUE_STATUS_NEW = 1;
  // Problem reproduced / Need acknowledged
  // state: open
  MONORAIL_ISSUE_STATUS_ACCEPTED = 2;
  // Work on this issue has begun
  // state: open
  MONORAIL_ISSUE_STATUS_STARTED = 3;
  // Work done on this issue is being reviewed
  // state: open
  MONORAIL_ISSUE_STATUS_IN_REVIEW = 4;
  // Resolving comments the PR came back with
  // state: open
  MONORAIL_ISSUE_STATUS_RESOLVING_COMMENTS = 5;
  // Issue is blocked on completion of another issue
  // state: open
  MONORAIL_ISSUE_STATUS_BLOCKED = 6;
  // Issue is not working as expected
  // state: open
  MONORAIL_ISSUE_STATUS_REOPENED = 7;
  // Developer made source code changes, QA should verify
  // state: closed
  MONORAIL_ISSUE_STATUS_FIXED = 8;
  // QA has verified that the fix worked
  // state: closed
  MONORAIL_ISSUE_STATUS_VERIFIED = 9;
  // This was not a valid issue report
  // state: closed
  MONORAIL_ISSUE_STATUS_INVALID = 10;
  // This report duplicates an existing issue
  // state: closed
  MONORAIL_ISSUE_STATUS_DUPLICATE = 11;
  // We decided to not take action on this issue
  // state: closed
  MONORAIL_ISSUE_STATUS_WONT_FIX = 12;
  // The requested task/defect/enhancement was deployed
  // state: closed
  MONORAIL_ISSUE_STATUS_DEPLOYED = 13;
  // The requested non-coding task/defect/enhancement was fixed
  // state: closed
  MONORAIL_ISSUE_STATUS_DONE = 14;
}

// State of the monorail issue: opened / closed depending upon the status
enum MonorailIssueState {
  MONORAIL_ISSUE_STATE_UNSPECIFIED = 0;
  // New, Accepted, Started etc., statuses are open states
  MONORAIL_ISSUE_STATE_OPEN = 1;
  // Fixed, Verified etc. status are closed states
  MONORAIL_ISSUE_STATE_CLOSED = 2;
}
