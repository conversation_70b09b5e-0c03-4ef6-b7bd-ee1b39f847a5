syntax = "proto3";

package pkg.monorail.payload;

import "api/pkg/monorail/payload/create_issue.proto";

option go_package = "github.com/epifi/be-common/api/pkg/monorail/payload";
option java_package = "com.github.epifi.gamma.api.pkg.monorail.payload";

message UpdateIssueCommentRequest {
  int64 issue_id = 1 [json_name = "id"];
  MonorailPerson author = 2 [json_name = "author"];
  // whether this content is actually description of the Monorail Issue
  bool is_description = 3 [json_name = "is_description"];
  // content of the comment or description
  string content = 4 [json_name = "content"];
  bool can_delete = 5[json_name = "canDelete"];
  // DeletedBy: Atomic person.
  MonorailPerson deleted_by = 6[json_name = "deletedBy"];
  string published = 7 [json_name = "published"];
  // Updates: Issue update.
  IssueUpdates updates = 8 [json_name = "updates"];
}

message UpdateIssueCommentResponse {
  // error, if any, during the API call
  ErrorMessage error = 1 [json_name = "error"];
  int64 issue_id = 2 [json_name = "id"];
  MonorailPerson author = 3 [json_name = "author"];
  // whether this content is actually description of the Monorail Issue
  bool is_description = 4 [json_name = "is_description"];
  // content of the comment or description
  string content = 5 [json_name = "content"];
  bool can_delete = 6[json_name = "canDelete"];
  string published = 7 [json_name = "published"];
  // Updates: Issue update.
  IssueUpdates updates = 8 [json_name = "updates"];
}

message IssueUpdates {
  repeated string blocked_on = 1 [json_name = "blockedOn"];
  repeated string blocking = 2 [json_name = "blocking"];
  repeated string cc = 3 [json_name = "cc"];
  repeated string components = 4  [json_name = "components"];
  // FieldValues: Custom field values.
  repeated CustomFieldValue field_values = 5 [json_name = "fieldValues"];
  bool is_description = 6 [json_name = "is_description"];
  repeated string labels = 7[json_name = "labels"];
  string merged_into = 8 [json_name = "mergedInto"];
  string move_to_project = 9 [json_name = "moveToProject"];
  string owner = 10 [json_name = "owner"];
  string status = 11 [json_name = "status"];
  string summary = 12 [json_name = "summary"];
}
