syntax = "proto3";

package pkg.monorail.payload;

option go_package = "github.com/epifi/be-common/api/pkg/monorail/payload";
option java_package = "com.github.epifi.gamma.api.pkg.monorail.payload";

// Mandatory fields: project_id, summary, status
message CreateMonorailIssueRequest {
  // the monorail project in which the issue is to be created
  string project_id = 1 [json_name = "projectId"];
  string title = 2 [json_name = "title"];
  // Summary / title of the monorail issue
  string summary = 3 [json_name = "summary"];
  // Detailed description of the monorail issue
  string description = 4 [json_name = "description"];
  // issue reported by
  MonorailPerson author = 5 [json_name = "author"];
  // Person to whom the issue is assigned
  MonorailPerson owner = 6 [json_name = "owner"];
  repeated MonorailPerson cc = 7 [json_name = "cc"];
  // BlockedOn: what other issues this issue is blocked on.
  repeated IssueRef blocked_on = 8 [json_name = "blockedOn"];
  // Blocking: the issues blocked on this issue.
  repeated IssueRef blocking = 9 [json_name = "blocking"];
  // components to which this monorail issue belongs
  repeated string components = 10 [json_name = "components"];
  // labels attached to this monorail issue
  repeated string labels = 11 [json_name = "labels"];
  // Granular status of the issue
  string status = 12 [json_name = "status"];
  // State of the issue: Possible values: "closed", "open"
  string state = 13 [json_name = "state"];
  bool can_comment = 14 [json_name = "canComment"];
  bool can_edit = 15 [ json_name = "canEdit"];
  // values of the custom fields for the issue
  // For a multivalued custom field there will be multiple entries for the FieldName in this list
  repeated CustomFieldValue field_values  = 16 [json_name = "fieldValues"];
}

message CreateGetMonorailIssueResponse {
  int64 id = 1 [json_name = "id"];
  // the monorail project to which this issue belongs e.g: we have fi-app, staging-fi-app projects
  string project_id = 2 [json_name = "projectId"];
  // error, if any, during the API call
  ErrorMessage error = 3 [json_name = "error"];
  string title = 4 [json_name = "title"];
  // Summary / title of the monorail issue
  string summary = 5 [json_name = "summary"];
  // Detailed description of the monorail issue
  string description = 6 [json_name = "description"];
  // issue reported by
  MonorailPerson author = 7 [json_name = "author"];
  // Person to whom the issue is assigned
  MonorailPerson owner = 8 [json_name = "owner"];
  // persons cc-ed (carbon copy) in the monorail issue
  repeated MonorailPerson cc = 9 [json_name = "cc"];
  // State: opened / closed depending upon the status
  // eg: New, Accepted, Started etc., statuses are open states and Fixed, Verified etc. status are closed states
  string state = 10 [json_name = "state"];
  // Granular status of the issue
  string status = 11 [json_name = "status"];
  // components to which this monorail issue belongs
  repeated string components = 12 [json_name = "components"];
  // labels attached to this monorail issue
  repeated string labels = 13 [json_name = "labels"];
  bool can_comment = 14 [json_name = "canComment"];
  bool can_edit = 15 [ json_name = "canEdit"];
  repeated IssueRef blocked_on = 16 [json_name = "blockedOn"];
  // BlockedOn: what other issues this issue is blocked on.
  repeated IssueRef blocking = 17 [json_name = "blocking"];
  // values of the custom fields for the issue
  // For a multivalued custom field there will be multiple entries for the FieldName in this list
  repeated CustomFieldValue field_values = 18 [json_name = "fieldValues"];
  bool starred = 19 [json_name = "starred"];
  // time at which the issue was created/published
  string published = 20 [json_name = "published"];
  // last updated time of the monorail issue
  string updated = 21 [json_name = "updated"];
  // time at which owner was last updated
  string owner_modified = 22 [json_name = "owner_modified"];
  // time at which status of the issue was last updated
  string status_modified = 23 [json_name = "status_modified"];
  // time at which components of the issue were last updated
  string component_modified = 24[json_name = "component_modified"];
  // approval status for monorail issue i.e. DataAccessApprover/ ApplicationAccessApprover/ BAAccessApprover
  repeated MonorailApprovalLog approval_log = 25 [json_name = "approvalValues"];
}

// The object representing a person on Monorail
message MonorailPerson {
  // name of the person. This is actually the email address
  string name = 1 [json_name = "name"];
  // link to profile page of the person on monorail dashboard
  string html_link = 2 [json_name = "htmlLink"];
  // whether emails to this person are bouncing
  bool email_bouncing = 3 [json_name = "email_bouncing"];
  // number of days ago the person last visited
  int64 last_visit_day_ago = 4 [json_name = "last_visit_days_ago"];
}

message IssueRef {
  int64 issue_id = 1 [json_name = "issueId"];
  string project_id = 2 [json_name = "projectId"];
}

// Custom fields defined on monorail issues
message CustomFieldValue {
  string field_name = 1 [json_name = "fieldName"];
  string field_value = 2 [json_name = "fieldValue"];
  bool derived = 3 [json_name = "derived"];
}

// Error associated with the API calls
message ErrorMessage {
  int64 code = 1 [json_name = "code"];
  string message = 2 [json_name = "message"];
  string reason = 3 [json_name = "reason"];
}

// Service account is used for auth to access the APIs
// This represents the json structure of the key stored in secret manager
message ServiceAccountKey {
  // Id associated with this generated key (can be found in GCP console)
  string private_key_id = 1 [json_name = "private_key_id"];
  // Actual private key used for Auth (can be downloaded from GCP console only during key creation)
  string private_key = 2 [json_name = "private_key"];
}

// the object representing approval logs of monorail approval type tickets
message MonorailApprovalLog {
  // approval type represents various types of monorail approval requests
  // i.e. DataAccessApprover/ ApplicationAccessApprover/ BAAccessApprover
  string approval_type = 1 [json_name = "approvalName"];
  // time at which the issue was approved
  string approval_time = 2 [json_name = "setOn"];
  // approval status i.e. approved/ NeedsReview/ ReviewRequested/ NeedInfo
  string approval_status = 3 [json_name = "status"];
}
