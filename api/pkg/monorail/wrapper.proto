syntax = "proto3";

package pkg.monorail;

import "api/pkg/monorail/issue.proto";

option go_package = "github.com/epifi/be-common/api/pkg/monorail";
option java_package = "com.github.epifi.gamma.api.pkg.monorail";

/*
This proto file contains the Wrapper requests and responses for accessing monorail APIs
The following project Ids will be used. For logical separation between environments (since there is no separate non-prod deployment of Monorail)
1. staging-fi-app --> Non-prod environments
2. fi-app --> for prod environment
*/

message CreateIssueRequest {
  // issue object with details to create a monorail issue
  Issue issue = 1;
}

message CreateIssueResponse {
  // The created issue object with additional fields like id, created_at etc., filled
  Issue issue = 2;
}

// RPC request to Get a monorail issue
// The following project Ids will be used. For logical separation between environments (since there is no separate non-prod deployment of Monorail)
//  1. staging-fi-app --> Non-prod environments
//  2. fi-app --> for prod environment
message GetIssueRequest {
  // Id of the issue to be fetched
  int64 issue_id = 1;
}

message GetIssueResponse {
  Issue issue = 1;
}

message ListIssuesRequest {
  MonorailIssueFilters filters = 1;
}

message ListIssuesResponse {
  repeated Issue issue_list = 1;
}

// The following project Ids will be used. For logical separation between environments (since there is no separate non-prod deployment of Monorail)
//  1. staging-fi-app --> Non-prod environments
//  2. fi-app --> for prod environment
message ListIssueCommentsRequest {
  int64 issue_id = 1;
}

message ListIssueCommentsResponse {
  repeated Comment comments_list = 1;
}

message UpdateIssueRequest {
  // for updating attributes of an issue
  Issue issue = 1;
  // for adding comment to an issue
  Comment comment = 2;
}

message UpdateIssueResponse {
  Issue issue = 1;
}
