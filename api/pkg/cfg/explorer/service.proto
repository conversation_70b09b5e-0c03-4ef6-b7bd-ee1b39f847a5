syntax = "proto3";

package api.pkg.cfg.explorer;

import "api/cx/method_options.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/pkg/web/values.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/pkg/cfg/explorer/common/service.proto";
import "api/pkg/cfg/explorer/enums.proto";

option go_package = "github.com/epifi/gamma/api/pkg/cfg/explorer";
option java_package = "com.github.epifi.gamma.api.pkg.cfg.explorer";

// ConfigExplorer is a GRPC Service to fetch the live config of a given service.
// It will be integrated with all the microservices and is not intended to be called by mobile clients.
service ConfigExplorer {
  // Method to fetch the config of a service. It takes the name of the service and
  // returns the static & dynamic config of the service after excluding the secrets.
  // It also takes a flag to denote if only dynamic config is to be returned.
  // Since this RPC will be called from a Jenkins job, it disables the CX & Frontend server auth checks.
  rpc GetConfig (GetConfigRequest) returns (GetConfigResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";

    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_tokenization) = true;
    option (rpc.add_device_verification_nonce) = false;
    option (rpc.device_integrity_check) = NOT_REQUIRED;
    option (rpc.skip_device_integrity_check) = true;
  }

  // RPC to get default values of the quest variables for the given variable paths
  rpc GetDefaultConfigValue (GetDefaultConfigValueRequest) returns (GetDefaultConfigValueResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";

    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_tokenization) = true;
    option (rpc.add_device_verification_nonce) = false;
    option (rpc.device_integrity_check) = NOT_REQUIRED;
    option (rpc.skip_device_integrity_check) = true;
  }
}


message GetConfigRequest {
  // Deprecated: Name of the config which needs to be fetched.
  string config_name = 1;

  // Deprecated: Flag to denote if only dynamic config is to be returned.
  // If false both the static and dynamic config are returned.
  bool only_dynamic_fields = 2;

  // Deprecated: Flag to denote if only the config values from the local files are to be
  // loaded. If true the config values from Consul KV store are over-ridden.
  bool only_local_values = 3;

  // Since this service is integrated with all the microservices, this header is required
  // to make request to the frontend server. Clients can ignore this field while making a
  // request, since all the checks in frontend server are disabled for this RPC.
  frontend.header.RequestHeader req = 4;

  // Deprecated: Enum type to denote the type of config that needs to be fetched, eg service config or server config.
  ConfigType config_type = 5;

  // Deprecated: Env variable to be passed in case we need configs of different envs
  string environment = 6;

  // common config explorer request
  common.GetConfigRequest req_params = 7;
}

message GetConfigResponse {
  // Deprecated: Config of the service in the format of a JSON string.
  string config_json = 1;

  // Deprecated: Name of the branch from which the config is loaded.
  string branch_name = 2;

  // Since this service is integrated with all the microservices, this header
  // is required to receive response from the frontend server.
  frontend.header.ResponseHeader resp_header = 3;

  // common config explorer response
  common.GetConfigResponse res = 4;
}

message GetDefaultConfigValueRequest {
  // Deprecated: Name of the service of which to fetch the config.
  string config_name = 1;
  // Deprecated: slash seperated path of the quest variable with service name as prefix
  repeated string variable_paths = 2;

  // Since this service is integrated with all the microservices, this header is required
  // to make request to the frontend server. Clients can ignore this field while making a
  // request, since all the checks in frontend server are disabled for this RPC.
  frontend.header.RequestHeader req = 3;

  // common config explorer request
  common.GetDefaultConfigValueRequest req_params = 4;
}

message GetDefaultConfigValueResponse {
  // Deprecated: Status of the request.
  rpc.Status status = 1;
  // Deprecated: list of variables with their default values
  map<string, pkg.web.DataValue> values = 2;
  // Since this service is integrated with all the microservices, this header
  // is required to receive response from the frontend server.
  frontend.header.ResponseHeader resp_header = 3;

  // common config explorer response
  common.GetDefaultConfigValueResponse res = 4;
}
