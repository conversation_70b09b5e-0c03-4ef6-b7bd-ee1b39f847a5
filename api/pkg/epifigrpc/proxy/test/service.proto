// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package api.pkg.epifigrpc.proxy.test;

import "google/protobuf/duration.proto";


option go_package = "github.com/epifi/gamma/api/pkg/epifigrpc/proxy/test;testpb";
option java_package = "com.github.epifi.gamma.api.pkg.epifigrpc.proxy.test";

service TestService1 {
  rpc UnaryRpc(UnaryRpcRequest) returns (UnaryRpcResponse) {}

  rpc ServerStreamRPC(ServerStreamRPCRequest) returns (stream ServerStreamRPCResponse) {}
  rpc ClientStreamRPC(stream ClientStreamRPCRequest) returns (ClientStreamRPCResponse) {}
  rpc BiDirectionStreamRPC(stream BiDirectionStreamRPCRequest) returns (stream BiDirectionStreamRPCResponse) {}
}


service TestService2 {
  rpc UnaryRpc(UnaryRpcRequest) returns (UnaryRpcResponse) {}

  rpc ServerStreamRPC(ServerStreamRPCRequest) returns (stream ServerStreamRPCResponse) {}
  rpc ClientStreamRPC(stream ClientStreamRPCRequest) returns (ClientStreamRPCResponse) {}
  rpc BiDirectionStreamRPC(stream BiDirectionStreamRPCRequest) returns (stream BiDirectionStreamRPCResponse) {}
}

message UnaryRpcRequest {
  ReqHeader header = 1;
  string fmt_field = 2;
  bool need_error = 3;
  bool need_req_header_from_proxy_interceptor = 4;
}

message UnaryRpcResponse {
  RespHeader header = 1;
  string field1 = 2;
}


message ReqHeader {
  string fmt_field = 1;
  string field1 = 2;
}

message RespHeader {
  string field1 = 1;
  string proxy_server_id = 2;
}


message ServerStreamRPCRequest {
  ReqHeader header = 1;
  string fmt_field = 2;
  StreamBehavior behavior = 3;
}
message ClientStreamRPCRequest {
  ReqHeader header = 1;
  string fmt_field = 2;
  StreamBehavior behavior = 3;
}
message BiDirectionStreamRPCRequest {
  ReqHeader header = 1;
  string fmt_field = 2;
  StreamBehavior behavior = 3;
}


message ServerStreamRPCResponse {
  RespHeader header = 1;
  string field1 = 2;
}
message ClientStreamRPCResponse {
  RespHeader header = 1;
  string field1 = 2;
}
message BiDirectionStreamRPCResponse {
  RespHeader header = 1;
  string field1 = 2;
}

message StreamBehavior {
  google.protobuf.Duration wait_after_each_msg = 1;
  bool need_header = 2;
  bool need_trailer = 3;
  int32 error_after_msg = 4;
  bool need_req_header_from_proxy_interceptor = 5;
  int32 number_of_responses = 6;
}
