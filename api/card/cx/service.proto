syntax = "proto3";

package card.cx;

import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/card/cx";
option java_package = "com.github.epifi.gamma.api.card.cx";

service Cx {

  // MarkCardDeliveryTrackingStateAsReceived is invoked by Cx dev-action
  // it takes card id and marks the card delivery tracking status to RECEIVED_BY_USER in DB. This will be mainly done to
  // activate cards of the users who have lost their box or not able to activate their cards because of any particular reason
  rpc MarkCardDeliveryTrackingStateAsReceived (MarkCardDeliveryTrackingStateAsReceivedRequest) returns (MarkCardDeliveryTrackingStateAsReceivedResponse);

  // MapForexTransaction is invoked by cx dev-action to manually map a parent international txn with
  // its forex refund child txn.
  rpc MapForexTransaction (MapForexTransactionRequest) returns (MapForexTransactionResponse);

  // FailDebitCardCreation fails the debit card creation request enabling user to re-try.
  // It takes cardId as input. It has handling for physical/digital form and renewal workflow cases.
  rpc FailDebitCardCreation (FailDebitCardCreationRequest) returns (FailDebitCardCreationResponse);
}

message FailDebitCardCreationRequest {
  string card_id = 1;
}

message FailDebitCardCreationResponse {
  rpc.Status status = 1;
}

message MarkCardDeliveryTrackingStateAsReceivedRequest {
  string card_id = 1;
}

message MarkCardDeliveryTrackingStateAsReceivedResponse {
  rpc.Status status = 1;
}

message MapForexTransactionRequest {
  string parent_txn_id = 1;
  string child_txn_id = 2;
}

message MapForexTransactionResponse {
  rpc.Status status = 1;
}
