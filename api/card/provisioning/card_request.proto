// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package card.provisioning;

import "api/card/card.proto";
import "api/card/card_block.proto";
import "api/card/enums/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/address.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "api/tiering/external/external.proto";

option go_package = "github.com/epifi/gamma/api/card/provisioning";
option java_package = "com.github.epifi.gamma.api.card.provisioning";

//go:generate gen_sql -types=CardRequestDetails,StageDetails
// Message to keep track of the card requests
message CardRequest {
  // primary key to identify a card request
  string id = 1;
  // id for the card for which request is getting formed
  string card_id = 2;
  // primary identifier to the actor table, card is being provisioned for this actor
  string actor_id = 3;
  // Orchestration identifier which has started this execution
  string orchestration_id = 4;
  // vendor handling the request
  vendorgateway.Vendor vendor = 5;
  // Metadata for a given request. This might contain the error reason and codes received from the vendor for a request.
  CardRequestDetails request_details = 6;
  // Deeplink to redirect to the next screen
  frontend.deeplink.Deeplink next_action = 7;
  // stage details of different stages card request processing
  StageDetails stage_details = 8;
  enums.CardRequestWorkflow workflow = 9;
  // Status of the request
  enums.CardRequestStatus status = 10;

  // enum denoting entry point for the request, APP/SHERLOCK etc
  card.Provenance provenance = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp deleted_at = 14;
}

message CardRequestDetails {
  // one of containing request details based on the workflow type
  oneof Data {
    RenewCardRequestDetails renew_card_request_details = 1;
    AmcChargesDetails amc_charges_details = 2;
  }
}

message RenewCardRequestDetails {
  // reason for blocking the previous card
  string block_card_reason = 1;
  // address type where card needs to be delivered
  api.typesv2.AddressType address_type = 2;
  // card form selected by user for card renewal
  card.CardForm card_form = 3;
  card.Provenance block_card_provenance = 4;
}

message StageDetails {
  map<string, CardRequestStage> card_request_stages = 1;
}

message CardRequestStage {
  enums.CardRequestStageName stage_name = 1;
  enums.CardRequestStageStatus status = 2;
  enums.CardRequestStageSubStatus sub_status = 3;
  google.protobuf.Timestamp staled_at = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
}

message AmcChargesDetails {
  google.type.Date file_gen_date = 1;
  int32 batch_number = 2;
  google.type.Money amc_charge_amount = 3;
  card.enums.OperationalStatus operational_status = 4;
  card.enums.FreezeStatus freeze_status = 5;
  google.type.Date anniversary_date = 6;
  tiering.external.Tier tier_during_anniversary = 7;
}
