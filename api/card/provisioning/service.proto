// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package card.provisioning;

import "api/dynamic_elements/dynamic_elements.proto";
import "api/card/card.proto";
import "api/card/card_auth_attempt.proto";
import "api/card/card_block.proto";
import "api/card/card_notifications.proto";
import "api/card/card_sku.proto";
import "api/card/enums/enums.proto";
import "api/card/forex_txn_refunds.proto";
import "api/card/provisioning/card_delivery_tracking.proto";
import "api/card/provisioning/card_pin.proto";
import "api/card/provisioning/card_tracking_request.proto";
import "api/card/provisioning/creation_request.proto";
import "api/card/provisioning/physical_card_dispatch_request.proto";
import "api/card/provisioning/provisioning.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/tiering/external/external.proto";
import "api/typesv2/actor.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/user_group.proto";
import "api/typesv2/money.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "google/type/postal_address.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/card/provisioning";
option java_package = "com.github.epifi.gamma.api.card.provisioning";

// Api signature is generic to work for both the debit and credit cards. This is done with
// an anticipated overlap. If and when, Credit cards come in, evaluate the actual overlap in specifications.
// If the overlap is true, it should be sufficient to add CardType(CREDIT/DEBIT) in all these apis and
// delegate the responsibility to corresponding processor. If not, we leave these set of apis specific to
// Debit card and serve Credit cards using a different grpc service. Overall, we don't lose much trying to
// be optimistic.

// The Card Provisioning Service facilitates card provisioning for the user linked to
// a bank account.
//
// It provides for following w.r.t cards
//   * creation
//   * activation
//   * dispatch and tracking
//   * pin management
service CardProvisioning {

  // Initiates card creation for the user. The decision to create single or dual card is controlled by the
  // provisioning strategy for the card.
  // If provisioning strategy is a ProvisioningStrategy.SINGLE_NUMBERED_CARD || SINGLE_NUMBERLESS_CARD,
  // a single card is provisioned for the user. The created cards in this case have default FORM as PHYSICAL.
  //
  // If provisioning strategy is a ProvisioningStrategy.DUAL_NUMBERLESS_CARD,
  // 2 cards are created for the user:
  // A PHYSICAL and a DIGITAL card. Post the successful sign up & account creation,
  // all cards will be created for the users. Post the card creation, digital card will be displayed
  // in the epiFi app  & physical cards will be shipped to the shipping address of the user.
  //
  // For initiating a new card creation request the mandatory parameters includes actor id and issuing bank.
  rpc CreateCard (CreateCardRequest) returns (CreateCardResponse) {}

  // Fetches the status of the card creation request already made.
  rpc FetchCardCreationStatus (FetchCardCreationStatusRequest) returns (FetchCardCreationStatusResponse) {}

  // RPC fetches all cards for the user based on the input filters. A user is expected to have multiple cards issued.
  // The card filters include:
  // - state: ACTIVATED, EXPIRED etc
  // - issuing bank : FEDERAL etc
  // - network type : VISA, MASTER etc
  // - type : DEBIT or CREDIT
  rpc FetchCards (FetchCardsRequest) returns (FetchCardsResponse) {}

  // Activates the given cards. The cards must be created by now. By default, both physical and digital
  // debit cards will not be activated. Any usage of the card is only permitted after the
  // activation step.
  // 1. PHYSICAL card needs to be activate for POS, ATM transactions
  // 2. DIGITAL card needs to be activated for ECOM/Online transactions.
  rpc ActivateCard (ActivateCardRequest) returns (ActivateCardResponse) {}

  // Fetches the status of the card activation requests already made.
  rpc FetchCardActivationStatus (FetchCardActivationStatusRequest) returns (FetchCardActivationStatusResponse) {}

  // Initiates the dispatch of the PHYSICAL card. Card will be dispatched as per the
  // address mentioned by the user.
  // TODO(anand): As per the RBI guidelines, the user needs to be provide the proof of shipping address within 3 months
  rpc DispatchCard (CardDispatchRequest) returns (CardDispatchResponse) {}

  // Provides tracking details for given card
  // Deprecated
  rpc CardDeliveryTracking (CardDeliveryTrackingRequest) returns (CardDeliveryTrackingResponse) {}

  // Fetches all the card related data and the status of the card.
  //
  // Any card information or sensitive information w.r.t bank has to be treated separately.
  // PCI - DSS has compliance requirements and guidelines regarding how such information has
  // to be stored/processed. Any system that touches the card information will fall in the scope of PCI.
  //
  // Hence, card service will have no access to card number, expiry or cvv. These will be stored in a
  // secret vault a.k.a Data Bunker. Card service will use token(issued by the data bunker) to refer
  // to the actual data.
  //
  // TODO(anand): If cvv cannot be stored in data bunker, how do we pass it along to the user?
  rpc FetchCardDetails (FetchCardDetailsRequest) returns (FetchCardDetailsResponse) {}

  // Fetches card details for the specified savings account id and card state
  rpc FetchCardDetailsByAccountId (FetchCardDetailsByAccountIdRequest) returns (FetchCardDetailsByAccountIdResponse) {}

  // TODO (anand): Add card block, (un)suspend, management (DOM_ON_OFF, INT_ON_OFF etc) api.
  // Based on if we'll use Fed or VISA for this.

  //==============PIN MANAGEMENT APIS==============
  //
  // Epifi has defined our own specifications around pin management. The common library approach
  // is expected to be adapted by all the partner banks.
  // As part of this, all the pin related use-cases, there exists a encryptedBlock containing
  // all the sensitive information (card pin, secure pin etc).
  //
  // For card related RPCs, the cred block contains following fields:
  // 1. secure pin : same as UPI pin. The secure pin is verified against the customer/account details in the request.
  //                 The verification is done at the issuing bank. If success, the pin set, change & reset proceeds.
  // 2. transaction id : TODO(anand) is it same as vendor request id?
  // 3. card pin: a.k.a the atm pin. This is the actual value which is set, changed or reset. In case of pin validation
  //              the card pin is verified against the stored pin. The verification happens at the end of issuing bank.
  //
  // Reference: https://docs.google.com/document/d/1PXrt_tGI5vmKYwpokqUoxVTxeey69zG6US1eNkJIEW0/edit?ts=5ebcc259#
  //
  // RPC facilitates setting the pin for the card.
  // Pin must be set after receiving the card and will be required for all the ATM/POS transactions.
  // Pin setup can be done only once for a particular card. Any subsequent call to set the pin must be rejected.
  // Only pin change and pin reset can be performed post setup.
  rpc SetCardPin (SetCardPinRequest) returns (SetCardPinResponse) {}

  // RPC facilitates changing the pin for the card to a new value.
  // Change pin is only available to the user if pin was previously set. The card pin can be changed voluntarily
  // and can also be forced by Epifi to be changed periodically. To change the pin, user needs to provide additional
  // authentication like old pin or some other auth-factor ( a secret value etc) registered with the issuing bank.
  // The changed pin must be used for all the transactions that follow.
  rpc ChangeCardPin (ChangeCardPinRequest) returns (ChangeCardPinResponse) {}

  // RPC facilitates resetting the pin for the card to a new entered value.
  // Reset pin is only available to the user if pin was previously set. Reset pin can be used when the
  // user has forgotten the pin. To reset the pin, user needs to provide additional authentication like a secret
  // value registered with the issuing bank. The pin is reset only if the additional authentication is validated
  // successfully.
  rpc ResetCardPin (ResetCardPinRequest) returns (ResetCardPinResponse) {}

  // RPC validates if the entered pin was valid for associated card.
  // Validate pin shall return failure if pin was not set or if the entered didn't match the pin set for the card.
  rpc ValidateCardPin (ValidateCardPinRequest) returns (ValidateCardPinResponse) {}

  // Checks if given card details(CardNumber, Expiry, PIN) are valid.
  // Usecase is around re-oobe scenarios. TODO: understand better and update
  rpc ValidateCard (CardValidateRequest) returns (CardValidateResponse) {}

  // RPC fetches card groups for the given user. A user is expected to have multiple cards issued.
  // A card group is a collection of cards identified by the same group_id identifier.
  //
  // The resulting card groups are sorted by `created_at` timestamps. Callers need to specify the order(ASC/DESC) of sorting.
  // NOTE: for the same `group_id`, cards are ordered by primary-key
  //
  // Callers can limit the number of card groups to be fetched by setting the `num_groups` field in request.
  // Additionally, caller can request to fetch all the available card groups for the user by setting the `get_all` to true.
  // TODO(anand) [low priority]: evaluate if this needs to be made paginated and prevent fetching all cards.
  // We don't expect large number of cards to be created per user and hence this should be fine for now.
  // TODO(anand): add option to filter the cards by vendor, network_type, card_type, states etc.
  rpc GetCardGroups (GetCardGroupsRequest) returns (GetCardGroupsResponse) {}

  // Fetches card pin status for card ids in the request
  rpc CardPinStatus (CardPinStatusRequest) returns (CardPinStatusResponse);

  // RPC to generate txn id based on flow type and vendor.
  // This RPC will return a unique txnid for the flow type for a vendor.
  rpc GenerateTxnId (GenerateTxnIdRequest) returns (GenerateTxnIdResponse) {}

  // Fetches card 16/4/3 in plain text for the card id in request.
  rpc GetCardDetailsWithCvv (GetCardDetailsWithCvvRequest) returns (GetCardDetailsWithCvvResponse) {};

  // RPC to block card and publish packet to create new card after card is blocked successfully
  rpc RenewCard (RenewCardRequest) returns (RenewCardResponse) {};

  // RPC to fetch renew card status for the given card id in the request
  // We will get new card id only when the old card is blocked successfully
  // If old card is blocked successfully and new card creation fails then we will again initiate creation of new card
  // and that card id will be sent in the response
  rpc RenewCardStatus (RenewCardStatusRequest) returns (RenewCardStatusResponse) {};

  // VerifyQRCode rpc decrypts the encrypted data and verify the data present in the qr code.
  // The data present in the qr code will be (Last 4 digits of the card + Last 4 digit of mobile number + last 4 digits of pin code)
  // After successful verification we will mark the card as delivered for the user and
  // user can then enable ATM/POS transaction for that card.
  rpc VerifyQRCode (VerifyQRCodeRequest) returns (VerifyQRCodeResponse) {};

  // FetchDeliveryTrackingStatus rpc fetches delivery status for the given card id
  rpc FetchDeliveryTrackingStatus (FetchDeliveryTrackingStatusRequest) returns (FetchDeliveryTrackingStatusResponse) {};

  // FetchTransactionableCards rpc fetches all cards for an actor through which transaction can be made or
  // could have been made in the past. All cards which were activated at least once, qualify.
  rpc FetchTransactionableCards (FetchTransactionableCardsRequest) returns (FetchTransactionableCardsResponse) {};

  // InitiateAdditionalAuthAttempt rpc creates a new auth attempt and returns the attempt id for the same
  rpc InitiateAdditionalAuthAttempt (InitiateAdditionalAuthAttemptRequest) returns (InitiateAdditionalAuthAttemptResponse) {};

  // GetAdditionalAuthInfo fetches the card id and action for a given auth attempt id.
  rpc GetAdditionalAuthInfo (GetAdditionalAuthInfoRequest) returns (GetAdditionalAuthInfoResponse) {};

  // UpdateAuthInfo updates the state of the auth such as SUCCESS, LIVENESS_FAILED, FACEMATCH_FAILED in case of Liveness and FM auth
  rpc UpdateAuthInfo (UpdateAuthInfoRequest) returns (UpdateAuthInfoResponse) {};

  // Triggers card specific notifications for particular user groups
  rpc TriggerCardNotifications (TriggerCardNotificationsRequest) returns (TriggerCardNotificationsResponse) {};

  // rpc to update free card replacements for a user. We will charge for card replacement when user reaches the maximum
  // free card replacement count. We might still need to give additional free card to users for cases when there is some issue with the card
  // and we need to give user an extra free card.
  rpc UpdateFreeCardReplacement (UpdateFreeCardReplacementRequest) returns (UpdateFreeCardReplacementResponse) {};

  // FetchCardTrackingDetails fetches cards for which awb details are not present, and get the awb details from the
  // bank and publishes packet to the queue to register those shipments at Shipway's end.
  // We will only fetch the AWB details for limited number of cards specified in the request based on their created_at
  // timestamp in ascending order.
  // This rpc will be triggered using a cron job which will between specific hours with an hourly frequency.
  // We are running it between specific hours because bank updates the AWB at a given time everyday.
  rpc FetchCardTrackingDetails (FetchCardTrackingDetailsRequest) returns (FetchCardTrackingDetailsResponse) {};

  // GetCardShipmentTrackingDetails returns the tracking details for a given card id. We will return awb number, carrier,
  // scans and delivery state information.
  rpc GetCardShipmentTrackingDetails (GetCardShipmentTrackingDetailsRequest) returns (GetCardShipmentTrackingDetailsResponse) {};

  // UploadCardTrackingDetails takes data present in csv file containing the tracking details such as Awb, courier partner
  // and updates it at our end in card tracking requests table and publishes the packet to register them as shipway
  rpc UploadCardTrackingDetails (UploadCardTrackingDetailsRequest) returns (UploadCardTrackingDetailsResponse) {};

  // ProcessManualCardPinSet will be triggered manually via a script or from sherlock.
  // It will mark pin set done at our end for a card and trigger the events to be published.
  // It is to be used for cases where card pin set is done through Federal IVR.
  // Long term solution is to get these events from federal and remove the manual dependency.
  rpc ProcessManualCardPinSet (ProcessManualCardPinSetRequest) returns (ProcessManualCardPinSetResponse) {};

  // ForceCardCreationEnquiry rpc will be triggered from Sherlock for card creation requests where we need to do enquiry at vendor's end
  // This can be due various reasons but not limited to -
  // 1. We got failure from vendor but on following up vendor asked us to do enquiry again
  // 2. Retries exhausted for card creation enquiry
  // 3. We got a failure ack but request actually reached vendor and they have asked us to enquire again
  rpc ForceCardCreationEnquiry (ForceCardCreationEnquiryRequest) returns (ForceCardCreationEnquiryResponse) {};

  // UpdateTrackingDetails takes data present in csv file containing the updated tracking details for shipments for
  // which the initial delivery partner refused and we received a new awb number and tracking partner
  rpc UpdateTrackingDetails (UpdateTrackingDetailsRequest) returns (UpdateTrackingDetailsResponse) {};

  // GetCardActionAttempts rpc fetches all the attempts for a given card id corresponding to a given action (pin set, pin reset)
  // Each attempt has details regarding if the action was successful or failed, along with the failure reasons
  rpc GetCardActionAttempts (GetCardActionAttemptsRequest) returns (GetCardActionAttemptsResponse) {};

  // ProcessManualCardUnsuspend rpc will be triggered from Sherlock dev action where we manually need to un-suspend card.
  // Card state will be updated from suspended to activated and pi state will be updated from suspended to verified.
  // It is to be used for cases where card state is un-suspended through Federal IVR and at our end it is still is in
  // suspended state.
  rpc ProcessManualCardUnsuspend (ProcessManualCardUnsuspendRequest) returns (ProcessManualCardUnsuspendResponse) {};

  // CreatePhysicalCardDispatchAttempt creates a new dispatch request in our db and publishes packet for processing of the dispatch request at vendor
  // for the card id sent in the request.
  // We will validate if card is not a physical card and there are no dispatch request in progress for this card
  // Deprecated this in favour of new rpc InitiatePhysicalCardDispatch
  rpc CreatePhysicalCardDispatchAttempt (CreatePhysicalCardDispatchAttemptRequest) returns (CreatePhysicalCardDispatchAttemptResponse) {};

  // InitiateShippingAddressUpdateAndDispatchPhysicalCard is invoked by client to issue physical card to a user, it creates shipping preference
  // with the address type received in the request and publishes packet for initiating and checking the status for
  // shipping address and triggering physical dispatch request post successful address update.
  // As the process for issuing physical card is not synchronous client needs to check the current state of the request using
  // CheckPhysicalCardDispatchStatus rpc.
  rpc InitiateShippingAddressUpdateAndDispatchPhysicalCard (InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest) returns (InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse) {};

  // CheckShippingAddressUpdateAndPhysicalCardDispatchStatus is called by client to check the status of shipping address
  // update and physical card dispatch request status
  rpc CheckShippingAddressUpdateAndPhysicalCardDispatchStatus (CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest) returns (CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse) {};

  // GetPhysicalCardDispatchStatus returns the current status of the physical card dispatch request
  rpc GetPhysicalCardDispatchStatus (GetPhysicalCardDispatchStatusRequest) returns (GetPhysicalCardDispatchStatusResponse) {};

  // GetLatestCardForActorIds returns the latest card for the actor id's sent in the request, latest card is fetched
  // based on the latest created at timestamp, in case if card not found for any actor we will return the remaining cards.
  // We will have card state as filter for fetching cards only within the given states, if no filter is passed we will
  // fetch cards with any state
  rpc GetLatestCardForActorIds (GetLatestCardForActorIdsRequest) returns (GetLatestCardForActorIdsResponse) {};

  // GetPhysicalCardActivationInfo to determine card physical activation status. It will take either card id or actor id as input. In case of actor-id
  // we will send the activation status of the latest physical card of the user. If activated we will share more details like activation timestamp etc
  rpc GetPhysicalCardActivationInfo (GetPhysicalCardActivationInfoRequest) returns (GetPhysicalCardActivationInfoResponse) {};

  // InitiatePhysicalCardDispatch creates a physical card dispatch request & initiates Physical Card Dispatch workflow and
  // creates a fund transfer request which further initiates the Fund Transfer workflow. It returns a deeplink corresponding
  // to the next action i.e. Authorize fund transfer
  rpc InitiatePhysicalCardDispatch (InitiatePhysicalCardDispatchRequest) returns (InitiatePhysicalCardDispatchResponse) {};

  // FetchPhysicalCardChargesForUser fetches the amount to be paid by different users to order a physical debit card
  rpc FetchPhysicalCardChargesForUser (FetchPhysicalCardChargesForUserRequest) returns (FetchPhysicalCardChargesForUserResponse) {};

  // GetLastPhysicalCardIssuedForUser returns the timestamp of the most recent physical card issued for the user.
  rpc GetLastPhysicalCardIssuedForUser (GetLastPhysicalCardIssuedForUserRequest) returns (GetLastPhysicalCardIssuedForUserResponse) {};

  // ActivatePhysicalCard rpc will be used to activate the card by switching on the POS end ECOM for the user.
  rpc ActivatePhysicalCard (ActivatePhysicalCardRequest) returns (ActivatePhysicalCardResponse) {};
  // GetForexRefunds RPC will fetch forex refund data based on an identifier like id, txn_id, etc.
  rpc GetForexRefunds (GetForexRefundsRequest) returns (GetForexRefundsResponse) {};
  // GetPaginatedForexRefundsByActorId RPC will fetch a paginated list of forex refunds for an actor
  rpc GetPaginatedForexRefundsByActorId (GetPaginatedForexRefundsByActorIdRequest) returns (GetPaginatedForexRefundsByActorIdResponse) {};
  // GetPhysicalCardDispatchRequests RPC will fetch all the dispatch requests for card id
  rpc FetchPhysicalCardDispatchRequests (FetchPhysicalCardDispatchRequestsRequest) returns (FetchPhysicalCardDispatchRequestsResponse) {};
  // GetCompletedForexRefundsByActorId RPC will fetch a list of forex refunds for an actor in completed state
  rpc GetForexRefundsByActorId (GetForexRefundsByActorIdRequest) returns (GetForexRefundsByActorIdResponse) {};
  // FetchCardRenewalCharges RPC will fetch card renewal charges
  rpc FetchCardRenewalChargesForUser (FetchCardRenewalChargesForUserRequest) returns (FetchCardRenewalChargesForUserResponse);

  // FetchForexRefundAggregates will fetch the aggregate refunds received in a given time range. . This can also give result for a
  // particular user if the actor id request param is not empty.
  rpc FetchForexRefundAggregates (FetchForexRefundAggregatesRequest) returns (FetchForexRefundAggregatesResponse);

  // GetHomeLayoutConfiguration will fetch the home layout configuration,
  // based on which the ordering of the component on the home screen will be decided.
  // We'll store the ordering of the components in `client(frontend)` configs for each expected layout configuration(primarily layout_id) that this RPC can return.
  rpc GetHomeLayoutConfiguration (GetHomeLayoutConfigurationRequest) returns (GetHomeLayoutConfigurationResponse);

  // GetCardSwitchNotification fetches card notification based on a given identifier
  rpc GetCardSwitchNotification (GetCardSwitchNotificationRequest) returns (GetCardSwitchNotificationResponse);

  // rpc to determine is international DC widget is to be shown to the user on Fi home screen
  // also provide the content to be displayed in the widget
  // implementation doc: https://docs.google.com/document/d/1zunr5fcKh3bf07mFEk5ACmQ1ECagU060NQY-YAXEkvo/edit?tab=t.0
  // Figma: https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=14513-39427&t=TEwxuxe0x7VombxZ-4
  rpc GetDcInternationalWidget (GetDcInternationalWidgetRequest) returns (GetDcInternationalWidgetResponse);

  // RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse);

  // DynamicElementCallback rpc processes callback received on user action on any of the dynamic elements
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse);
}

message GetCardSwitchNotificationRequest {
  oneof identifier {
    string rrn = 1;
    string external_ref_id = 2;
  }
}

message GetCardSwitchNotificationResponse {
  rpc.Status status = 1;
  CardNotification card_notification = 2;
}

message GetHomeLayoutConfigurationRequest {
  // actor id of the user
  string actor_id = 1;
}

message GetHomeLayoutConfigurationResponse {
  rpc.Status status = 1;
  // layout id of the layout to be shown on the home screen.
  // for every layout id we will have a predefined ordering of the layout components which are to be shown.
  string layout_id = 2;
  // data related to the user's card, this will be used to show the card related data on the card home screen
  UserCardData card_usage_data = 3;
}

// data related to the user's card, this will be used to show the card related data on the card home screen
message UserCardData {
  // travel mode status
  bool is_travel_mode_on = 1;
  // all the cards of user in descending order of created time
  repeated card.Card cards = 2;
  // current card of the user
  card.Card current_card = 3;
  // current tier of the user
  tiering.external.Tier currentTier = 4;
  // latest dispatch request for the current card
  PhysicalCardDispatchRequest latestPhysicalDispatchRequest = 5;
  // physical card delivery tracking info
  CardDeliveryTracking CardDeliveryTrackingInfo = 6;
}


message FetchForexRefundAggregatesRequest {
  // [OPTIONAL] This will be used to fetch forex refund aggregates for a particular actor.
  // If this is blank, the aggregates will be fetched without the actor_id filter, i.e for all
  // users
  string actor_id = 1;
  // Start timestamp of the refunds received.
  google.protobuf.Timestamp start_time = 2 [(validate.rules).timestamp.required = true];
  // end timestamp of the refunds received
  google.protobuf.Timestamp end_time = 3 [(validate.rules).timestamp.required = true];
  // The list of tiers for which the refund aggregates needs to be fetched. If empty, then the refund aggregates for
  // all tiers needs to be fetched
  repeated tiering.external.Tier tiers = 4;
}

message FetchForexRefundAggregatesResponse {
  rpc.Status status = 1;
  // refund aggregates grouped by tier string. The key in the map will be the stringified
  // tier (For eg. TIER_FI_PLUS, TIER_FI_INFINITE, etc.) and the value will be the forex refunds
  // based on the input parameters
  map<string, google.type.Money> refund_aggregates = 2;
}

message FetchCardRenewalChargesForUserRequest {
  string actor_id = 1;
}

message FetchCardRenewalChargesForUserResponse {
  rpc.Status status = 1;
  // total amount to be paid for card renewal including gst
  api.typesv2.Money total_amount = 2;
  // card renewal charges (gst not included),
  api.typesv2.Money amount_without_gst = 3;
}

message GetForexRefundsByActorIdRequest {
  string actor_id = 1;
  repeated enums.RefundStatus refund_statuses = 2;
}

message GetForexRefundsByActorIdResponse {
  rpc.Status status = 1;
  repeated card.DcForexTxnRefund refunds = 2;
}

message FetchPhysicalCardDispatchRequestsRequest {
  // Deprecated: use `card_identifier` instead
  // card id for which dispatch requests needs to be fetched
  string card_id = 1 [deprecated = true];
  // Deprecated: use `card_identifier` instead
  // actor of the user to which card is related
  string actor_id = 2 [deprecated = true];
  // limit on the number of physical card dispatch requests to be sent.
  // If 0, then all the dispatch requests will be sent in the response
  int32 limit = 3;
  // Optional: state filter to be applied on the physical dispatch requests,
  // if no state filter is provided all physical dispatch requests fulfilling the criteria,
  // base on the other provided parameters, will be returned regardless of the states,
  // in case no requests fulfilling pre-criteria and applying the filters are found, rpc will return with RNF status
  repeated card.provisioning.RequestState states = 4;
  CardIdentifier card_identifier = 5;
}

// CardIdentifier is a generic Identifier which can be used across the card APIs,
// to provide support to it's clients to choose and send an identifier which can be used to identify the card,
// the provided identifier will be used to identify the card and send the required data to the client,
// this helps adding support for multiple identifiers for card (primarily - card_id and actor_id) where client can choose among,
// based on availability and use-case, and it'll also help preventing corruption on logic on card's end where card APIs asks for multiple,
// identifiers but misses to have validation that all the identifiers belong to same card
message CardIdentifier {
  oneof Identifier {
    string card_id = 1;
    string actor_id = 2;
  }
}

message FetchPhysicalCardDispatchRequestsResponse {
  rpc.Status status = 1;
  // all dispatch requests for the card_id passed in request
  repeated PhysicalCardDispatchRequest physical_card_dispatch_requests = 2;
}

message GetPaginatedForexRefundsByActorIdRequest {
  string actor_id = 1;
  rpc.PageContextRequest page_context_request = 2;
}

message GetPaginatedForexRefundsByActorIdResponse {
  rpc.Status status = 1;
  repeated card.DcForexTxnRefund refunds = 2;
  rpc.PageContextResponse page_context_response = 3;
}

message GetForexRefundsRequest {
  oneof identifier {
    // referring to the forex refund record primary key
    string id = 1;
    // id of the txn for which we need to fetch the forex refund data
    string txn_id = 2;
    string actor_id = 3 [deprecated = true];
    string refund_id = 4;
  }
}

message GetForexRefundsResponse {
  rpc.Status status = 1;
  repeated DcForexTxnRefund refunds = 2;
}

message ActivatePhysicalCardRequest {
  // card id for which the activation is to be done
  string card_id = 1;
  // unique request id for which cred block is generated
  // client will get this request id by calling the GenerateTxnId api call
  string request_id = 2;
  // base64 encoded cred block json string required for enabling ecom and pos
  // on vendor's end
  string cred_block = 3;
  // actor to whom the card belongs
  string actor_id = 4;
}

message ActivatePhysicalCardResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
  // Internal response code returned by VG API corresponding to vendor returned code.
  // We will use this status code to convert error response to client error view which will be shown on the UI.
  string internal_response_code = 3;
}

message FetchPhysicalCardChargesForUserRequest {
  // actor id of the user
  string actor_id = 1;
  // Deprecated: in favour of card.enums.OrderPhysicalCardUiEntryPoint
  // caller to provide next action to where user should be redirected post dispatch init success
  frontend.deeplink.Deeplink post_success_next_action = 2 [deprecated = true];
  // Deprecated: in favour of card.enums.OrderPhysicalCardUiEntryPoint
  // next action when user click to skip cta
  frontend.deeplink.Deeplink on_skip_next_action = 3 [deprecated = true];
  // entry point to identify the source of the rpc call
  card.enums.OrderPhysicalCardUiEntryPoint entry_point = 4;
}

message FetchPhysicalCardChargesForUserResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    VKYC_REJECTED = 201;
  }
  rpc.Status status = 1;
  // Next action of the user
  frontend.deeplink.Deeplink next_action = 2;
  // amount to be paid by user for physical debit card
  api.typesv2.Money payable_amount = 3;
  // amount to be displayed on screen
  api.typesv2.Money display_amount = 4;
}

message InitiatePhysicalCardDispatchRequest {
  // actor id of the user
  string actor_id = 1;
  // card id of the card which needs to be dispatched
  string card_id = 2;
  // amount to be paid to execute transaction
  api.typesv2.Money amount = 3;
  // address type of the user's address where card needs to be delivered
  api.typesv2.AddressType address_type = 4;
  // entry point for physical dispatch init
  // Deprecated: in favour of `card.enums.OrderPhysicalCardUiEntryPoint`
  card.provisioning.UIEntryPoint ui_entry_point = 5 [deprecated = true];
  // caller to provide next action to where user should be redirected post dispatch init success
  frontend.deeplink.Deeplink redirect_action = 6 [deprecated = true];
  // entry point to identify in which flow order flow was initiated
  card.enums.OrderPhysicalCardUiEntryPoint entry_point = 7;
}

message InitiatePhysicalCardDispatchResponse {
  enum Status {
    OK = 0;
    // Dispatch request already exists for the card
    ALREADY_EXISTS = 6;

    INTERNAL = 13;

    INSUFFICIENT_FUNDS = 102;
  }
  rpc.Status status = 1;
  // Next action of the user
  frontend.deeplink.Deeplink next_action = 2;
}

message GetPhysicalCardActivationInfoRequest {
  oneof identifier {
    string card_id = 1;
    string actor_id = 2;
  }
}

message GetPhysicalCardActivationInfoResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  enum CardActivationStatus {
    CARD_ACTIVATION_STATUS_UNSPECIFIED = 0;

    CARD_ACTIVATION_STATUS_ACTIVATED = 1;

    CARD_ACTIVATION_STATUS_NOT_ACTIVATED = 2;
  }
  // enum to determine the card activation status
  CardActivationStatus card_activation_status = 2;

  // time at which user activated the card via qr code scan
  google.protobuf.Timestamp card_activated_at = 3;
}

message GetPhysicalCardDispatchStatusRequest {
  string card_id = 1;
}

message GetPhysicalCardDispatchStatusResponse {
  enum Status {
    OK = 0;

    // Dispatch request not found for the card id
    // This means user has not initiated physical card dispatch request
    NOT_FOUND = 5;

    // Card dispatch is in progress
    IN_PROGRESS = 100;

    // Card dispatch request failed
    FAILED = 101;

    // Card payment is in progress
    PAYMENT_IN_PROGRESS = 102;

    // Card dispatch request failed due to payment failure
    PAYMENT_FAILED = 103;
  }
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message CreatePhysicalCardDispatchAttemptRequest {
  // card id for which dispatch needs to be initiated
  string card_id = 1;
}

message CreatePhysicalCardDispatchAttemptResponse {
  enum Status {
    OK = 0;
    // Dispatch request already exists for the card
    ALREADY_EXISTS = 6;
    // card is already a physical card
    FAILED_PRECONDITION = 9;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message InitiateShippingAddressUpdateAndDispatchPhysicalCardRequest {
  // card id of the card which needs to be dispatched
  string card_id = 1;

  // address type of the user's address where card needs to be delivered
  api.typesv2.AddressType address_type = 2;

  string actor_id = 3;
}

message InitiateShippingAddressUpdateAndDispatchPhysicalCardResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message CheckShippingAddressUpdateAndPhysicalCardDispatchStatusRequest {
  string card_id = 1;
}

message CheckShippingAddressUpdateAndPhysicalCardDispatchStatusResponse {
  enum Status {
    OK = 0;

    SHIPPING_ADDRESS_UPDATE_IN_PROGRESS = 100;

    SHIPPING_ADDRESS_UPDATE_FAILED = 101;

    PHYSICAL_CARD_DISPATCH_IN_PROGRESS = 102;

    PHYSICAL_CARD_DISPATCH_FAILED = 103;
  }
  rpc.Status status = 1;
}

message GetCardActionAttemptsRequest {
  string card_id = 1 [(validate.rules).string = {min_len: 2, max_len: 100}];
  // action was which attempts need to be fetched
  card.CardAction action = 2 [(validate.rules).enum = {not_in: [0]}];
  // terminal states of the action
  // We will fetch all attempts if this is empty
  repeated card.ActionState action_states = 3;
}

message GetCardActionAttemptsResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated card.CardAuthAttempt action_attempts = 2;
}

message UpdateTrackingDetailsRequest {
  bytes card_tracking_update_csv_data = 1;
}

message UpdateTrackingDetailsResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message ForceCardCreationEnquiryRequest {
  string card_id = 1 [(validate.rules).string = {min_len: 2, max_len: 100}];
}

message ForceCardCreationEnquiryResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message ProcessManualCardPinSetRequest {
  string card_id = 1 [(validate.rules).string = {min_len: 2, max_len: 100}];
}

message ProcessManualCardPinSetResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message UploadCardTrackingDetailsRequest {
  bytes card_tracking_csv_data = 1;
  // Vendor printing the card
  card.provisioning.CardPrintingVendor card_printing_vendor = 2;
}

message UploadCardTrackingDetailsResponse {
  rpc.Status status = 1;
  // failure reason for upload of tracking details
  map<string, string> awb_to_failure_reason = 2;

  // total number of tracking details
  int32 total_count = 3;

  // count of tracking details uploaded successfully
  int32 successful_count = 4;
}

message GetCardShipmentTrackingDetailsRequest {
  string card_id = 1;
}

message GetCardShipmentTrackingDetailsResponse {
  rpc.Status status = 1;
  card.provisioning.CardTrackingRequest tracking_details = 2;
}

message UpdateFreeCardReplacementRequest {
  string actor_id = 1;
  // Card SKU Type defines the type of card issued to the user.
  // Currently we are only issuing card with CLASSIC sku type.
  card.CardSKUType card_sku_type = 2;
}

message UpdateFreeCardReplacementResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
}

message TriggerCardNotificationsRequest {
  // user groups for which notification is to be triggered
  repeated api.typesv2.common.UserGroup user_groups = 1;
  // Defines actions for which manual notification is to be sent
  ManualTriggeredNotificationType notification_type = 2;
}

message TriggerCardNotificationsResponse {
  rpc.Status status = 1;
}

// Defines actions for which manual notification is to be sent
enum ManualTriggeredNotificationType {
  MANUAL_TRIGGERED_NOTIFICATION_TYPE_UNSPECIFIED = 0;

  REQUEST_NEW_CARD = 1;
}

message InitiateAdditionalAuthAttemptRequest {
  string card_id = 1;
  // Action for which auth is initiated
  CardAction action = 2;
  // Auth for which attempt is initiated
  AuthType auth = 3;
}

message InitiateAdditionalAuthAttemptResponse {
  rpc.Status status = 1;
  string attempt_id = 2;
}

message GetAdditionalAuthInfoRequest {
  string attempt_id = 1;
}

message GetAdditionalAuthInfoResponse {
  rpc.Status status = 1;
  string card_id = 2 [deprecated = true];
  CardAction action = 3 [deprecated = true];
  card.CardAuthAttempt auth_attempt = 4;
}

message UpdateAuthInfoRequest {
  string attempt_id = 1;
  // State to be updated
  AuthState state = 2 [deprecated = true];

  card.CardAuthAttempt updated_auth_attempt = 3;

  repeated card.CardAuthAttemptFieldMask card_auth_field_mask = 4;
}

message UpdateAuthInfoResponse {
  rpc.Status status = 1;
  string card_id = 2;
  CardAction action = 3;
}

// TODO(anand): Instead of exposing this services clients to the
// vendorgateway.Vendor concept can we pass in some other context like
// Account for which the card is required to be created/dispatched etc?
// Request to create card(s) for the user.
message CreateCardRequest {
  api.typesv2.Actor actor = 1;
  CardIssueType issue_type = 2;
  vendorgateway.Vendor issuing_bank = 3;
  CardType type = 4;
  string savings_account_id = 5;
  // card id of the blocked card. There are two types of card creation request:
  // 1. creating a card for a user for the first time i.e. FRESH card issuance
  // 2. create a card for a user after the previous card was blocked or expired.
  //    We maintain the previous_card_id to preserve this information.
  string blocked_card_id = 6;
  // Card SKU Type defines the type of card issued to the user.
  // If not passed we will create card with basic functionality.
  card.CardSKUType card_sku_type = 7;
  // Card form of the card to be created. There are two types of card form:
  // 1. DIGITAL
  // 2. PHYSICAL
  card.CardForm card_form = 8;
}

// Response from CreateCard RPC.
message CreateCardResponse {
  rpc.Status status = 1;
  repeated Card cards = 2;
}

message FetchCardCreationStatusRequest {
  repeated string card_ids = 1;
  vendorgateway.Vendor issuing_bank = 2;
}

message CreationStatesInfo {
  // deprecated. Use card creation request instead which has request state and other fields
  RequestState state = 1 [deprecated = true];
  Card card = 2;
  // card creation request containing request state along with other fields such as failure response code and reason
  CardCreationRequest card_creation_request = 3;
}

message FetchCardCreationStatusResponse {
  rpc.Status status = 1;
  map<string, CreationStatesInfo> creation_states = 3;
}

message FetchCardsRequest {
  // actor for which cards needs to be fetched
  api.typesv2.Actor actor = 1;
  // issuing banks of cards which needs to be fetched
  repeated vendorgateway.Vendor issuing_banks = 2;

  // Indicates the cards to be fetched based on the states.
  // For example: fetch all the activated cards or expired cards
  repeated CardState card_states = 3;

  // Types of card that was provisioned for the user.
  repeated CardType card_types = 4;

  // The Network types of the underlying cards to be fetched.
  repeated CardNetworkType card_network_types = 6;

  // The Card Forms of the underlying cards to be fetched.
  repeated CardForm card_forms = 7;

  // Card fields by which cards needs to be sorted
  CardFieldMask sorted_by = 8;

  // maximum number of cards to fetch
  int32 limit = 9;
}

message FetchCardsResponse {
  rpc.Status status = 1;
  repeated Card cards = 2;
}

message ActivateCardRequest {
  repeated string card_ids = 1;
  vendorgateway.Vendor issuing_bank = 2;
}

message ActivationStatesInfo {
  RequestState state = 1;
  Card card = 2;
}

message ActivateCardResponse {
  rpc.Status status = 1;
  map<string, ActivationStatesInfo> activation_states = 3;
}

message FetchCardActivationStatusRequest {
  repeated string card_ids = 1;
  vendorgateway.Vendor issuing_bank = 2;
}

message FetchCardActivationStatusResponse {
  rpc.Status status = 1;
  map<string, ActivationStatesInfo> activation_states = 3;
}

message FetchCardDetailsRequest {
  vendorgateway.Vendor issuing_bank = 1;
  repeated string card_ids = 2;
}

message FetchCardDetailsResponse {
  rpc.Status status = 1;
  map<string, Card> cards = 2;
}

message CardDispatchRequest {
  api.typesv2.Actor actor = 1;
  google.type.PostalAddress address = 2;
  // dispatch is inherently associated with PHYSICAL card only, skipping card_form here
  vendorgateway.Vendor issuing_bank = 3;
}

message CardDispatchResponse {
  rpc.Status status = 1;
  RequestState state = 2;
}

message CardDeliveryTrackingRequest {
  string actor_id = 1;
  string card_id = 2;
}

message CardDeliveryTrackingResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  // date at which card generation request was made
  google.type.Date card_generation_date = 2;
  // date at which card got dispatched
  google.type.Date card_dispatch_date = 3;
  // bill number for card dispatch
  string awb = 4;
  // If card was returned to origin, then date at which card was returned
  google.type.Date card_returned_date = 5;
  // Courier agency name
  string courier_partner = 6;
  string remarks = 7;
  // current state of the card delivery
  CardDeliveryTrackingState state = 8;
}

message SetCardPinRequest {
  string card_id = 1;
  string cred_block = 2;
  // card pin set flow. Based on this correct cred block need to be sent.
  card.provisioning.PinSetFlow pin_set_flow = 3;

  // requestId/txnId used in salt for credblock generation. We need to pass on the same request-id to the vendor during the pin set API. The vendor will validate the txnId in credblock with the requestId in set pin API call.
  string request_id = 4;

  // For pin set during onboarding we need not validate the auth attempt state, but for pin actions initiated
  // from debit card screens we will only allow user to proceed if auth corresponding to the given attempt id was
  // successful.
  card.provisioning.UIEntryPoint ui_entry_point = 5;

  // unique attempt id for an auth attempt. We will validate if the auth associated with the given attempt id was success
  // and belongs to the same card for which pin set is requested.
  // After validation we will mark the auth as consumed to maintain 1:1 mapping belong auth attempt and pin action
  string auth_attempt_id = 6;
}

// Entry point from which action is triggered.
// For example for card pin set sser  can set card pin during onboarding or from debit card landing page after requesting new card.
enum UIEntryPoint {
  UI_ENTRY_POINT_UNSPECIFIED = 0;

  ONBOARDING = 1;

  DEBIT_CARD = 2;

  RENEW_CARD_FLOW = 3;
}

message SetCardPinResponse {
  enum Status {
    OK = 0;
    // Auth validation failed user will not be allowed to set pin and asked for authentication again
    PERMISSION_DENIED = 7;
    // If the pin was not set, validation will fail.
    PIN_NOT_SET = 101;
    // If the secure-pin (used as an AUTH factor to validate the reset request) doesn't match the secure-pin
    // set for the user account/card.
    INCORRECT_SECURE_PIN = 102;
    // pin setup is pending. Use the checkStatus api to check the status later.
    PENDING = 103;

    INVALID_OTP_NUMBER = 200;
    // User has entered a weak pin such as 0000 or 1234
    WEAK_PIN_ENTERED = 201;
    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  rpc.Status status = 1;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 2;
}

message ChangeCardPinRequest {
  string card_id = 1;
  string cred_block = 2;
}

message ChangeCardPinResponse {
  enum Status {
    OK = 0;
    // If the pin was not set, validation will fail.
    PIN_NOT_SET = 101;
    // If the secure-pin (used as an AUTH factor to validate the reset request) doesn't match the secure-pin
    // set for the user account/card.
    INCORRECT_SECURE_PIN = 102;
  }
  rpc.Status status = 1;
}

message ResetCardPinRequest {
  string card_id = 1;
  string cred_block = 2;
  // card pin set flow. Based on this correct cred block need to be sent.
  card.provisioning.PinSetFlow pin_set_flow = 3;

  // requestId/txnId used in salt for credblock generation. We need to pass on the same request-id to the vendor during the pin set API. The vendor will validate the txnId in credblock with the requestId in set pin API call.
  string request_id = 4;

  // unique attempt id for an auth attempt. We will validate if the auth associated with the given attempt id was success
  // and belongs to the same card for which pin reset is requested.
  // After validation we will mark the auth as consumed to maintain 1:1 mapping belong auth attempt and pin action
  string auth_attempt_id = 5;
}

message ResetCardPinResponse {
  enum Status {
    OK = 0;
    // Auth validation failed user will not be allowed to reset pin and asked for authentication again
    PERMISSION_DENIED = 7;
    // If the pin was not set, validation will fail.
    PIN_NOT_SET = 101;
    // If the secure-pin (used as an AUTH factor to validate the reset request) doesn't match the secure-pin
    // set for the user account/card.
    INCORRECT_SECURE_PIN = 102;

    INVALID_OTP_NUMBER = 200;
    // User has entered a weak pin such as 0000 or 1234
    WEAK_PIN_ENTERED = 201;
    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
    // Card status : Lost card/Stolen Card/Restricted Card
    RESTRICTED_CARD = 204;
  }
  rpc.Status status = 1;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 2;
}

message ValidateCardPinRequest {
  string card_id = 1;
  string cred_block = 2;
}

message ValidateCardPinResponse {
  enum Status {
    OK = 0;
    // If the pin was not set, validation will fail.
    PIN_NOT_SET = 101;
    // If the entered card pin doesn't match with the pin that was set for the card.
    INCORRECT_CARD_PIN = 102;
  }
  rpc.Status status = 1;
}

// TODO(anand): do we require validation for both card forms?
message CardValidateRequest {
  api.typesv2.Actor actor = 3;
  vendorgateway.Vendor issuing_bank = 4;

  // encrypted block containing 16/4/3 of the card to be validated.
  string credBlock = 5;
}

message CardValidateResponse {
  rpc.Status status = 1;
  RequestState state = 2;
}

message GetCardGroupsRequest {
  api.typesv2.Actor actor = 1;
  bool get_all = 2;
  int32 num_groups = 3;
  bool asc_order_created_time = 4;
}

message GetCardGroupsResponse {
  rpc.Status status = 1;
  repeated Card cards = 2;
}

message CardPinStatusRequest {
  repeated string card_ids = 1;
}

message CardPinStatusResponse {
  rpc.Status status = 1;

  // Card ID to CardPinState mapping
  map<string, CardPinState> card_pin_states = 2;
}

message GenerateTxnIdRequest {
  // txn type for which txn id need to be generated.
  card.provisioning.TxnType txn_type = 1;

  // vendor for which txn id need to be generated
  vendorgateway.Vendor vendor = 2;

  // card-id for which txn-id will be generated
  string card_id = 3;
}

message GenerateTxnIdResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // pin set attempt request before threshold
    PIN_SET_ATTEMPT_REQUEST_BEFORE_THRESHOLD = 100;
  }
  rpc.Status status = 1;

  // unique txn id
  string txn_id = 2;

  // masked card number to use in salt
  string masked_card_number = 3;

  // Additional parameters which needs to be send along with the transaction id
  oneof ResponseParams {
    // For setting card pin we will send pin set flow which can be either the token flow or otp flow
    PinSetFlow pin_set_flow = 4;
  }
}

enum TxnType {
  TXN_TYPE_UNSPECIFIED = 0;

  // Txn type for pin set/ pin change request.
  PIN_SET = 1;

  // Txn type for cvv enquiry request.
  CVV_ENQUIRY = 2;

  // Card control txn type for setting card control
  CONTROL_ACTION_TXN_TYPE = 3;

  // Limit txn type for get and update card limit
  LIMIT_TXN_TYPE = 4;

  // Txn type for enabling/disabling any number of card controls
  CONSL_CARD_CONTROL_TXN_TYPE = 5;
}

message GetCardDetailsWithCvvRequest {
  // Card of card for which cvv is requested
  string card_id = 1;

  // Encrypted pin/credentials required to perform cvv enquiry.
  // It is generated by partnerSdk in UI and can be validated at vendor end only.
  string cred_block = 2;

  // requestId/txnId used in salt for credblock generation. We need to pass on the same request-id to the vendor during the pin set API. The vendor will validate the txnId in credblock with the requestId in set pin API call.
  string request_id = 3;
}

message GetCardDetailsWithCvvResponse {
  enum Status {
    OK = 0;

    TRANSIENT_FAILURE = 100;

    PERMANENT_FAILURE = 101;

    PIN_RETRIES_EXCEEDED = 201;

    INVALID_SECURE_PIN = 202;

    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  rpc.Status status = 1;

  // TODO(vivek):Can this be tokenized. In my opinion we can't do tokenization as tokenizer will store cvv but as per norm we can't. Either tokenizer need to keep this in temporary storage or discuss other options.
  // 16/4/3 of card in plain text.
  BasicCardInfo card_info = 2;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 3;
}


message RenewCardRequest {
  // Card id of the card to be blocked
  string card_id = 1;

  string block_card_reason = 2;

  card.Provenance block_card_provenance = 3;

  string actor_id = 4;

  api.typesv2.AddressType address_type = 5;

  card.CardForm card_form = 6;
}

message RenewCardResponse {
  enum Status {
    // successfully published packet for card block
    OK = 0;
  }
  rpc.Status status = 1;

  frontend.deeplink.Deeplink next_action = 2;
}

message RenewCardStatusRequest {
  // card id of the card which needs to be blocked
  string card_id = 1;
}

message RenewCardStatusResponse {
  enum Status {
    // card was created. Success state
    OK = 0;

    // shipping address update pending. Client needs to poll for terminal status using the RenewCardStatus api
    SHIPPING_ADDRESS_UPDATE_PENDING = 104;

    // shipping address update failed. Client must retry for requesting new card using the renew card api
    SHIPPING_ADDRESS_UPDATE_FAILED = 105;

    // card block pending. Client needs to poll for terminal status using the RenewCardStatus api
    // with the old card id
    CARD_BLOCK_PENDING = 100;

    // failed to block card, Client must retry for blocking the card using the renew card api
    CARD_BLOCK_FAILED = 101;

    // Old card is blocked successfully and new card creation is in pending state. Client should poll again after some time
    // to get the terminal status
    CARD_CREATION_PENDING = 102;

    // Old card is blocked successfully but new card creation has failed.
    CARD_CREATION_FAILED = 103;

    // does not clear min balance condition to trigger card renewal request
    INSUFFICIENT_FUNDS = 106;

    // Virtual card created but physical card dispatch init failed.
    INITIATE_PHYSICAL_DISPATCH_FAILED = 107;

    // Virtual card created but physical card dispatch initiated at our end and is in progress.
    PHYSICAL_DISPATCH_PENDING = 108;
  }
  rpc.Status status = 1;

  // We will get new card id only when status code is `OK` i.e.
  // old card is blocked successfully and new card creation is successful.
  string new_card_id = 2;

  frontend.deeplink.Deeplink next_action = 3;

  // internal status code of the request mapped with vg response code
  string internal_status_code = 4;
}

message VerifyQRCodeRequest {
  api.typesv2.Actor actor = 1;
  // card_id of the card for which we need to verify data
  string card_id = 2;
  // encrypted data present in the qr code
  string qr_data = 3;
  // vendor at which card was printed
  // we need this for identifying the keys which needs to be used for validating the qr data
  card.provisioning.CardPrintingVendor card_printing_vendor = 4;
}

message VerifyQRCodeResponse {
  enum Status {
    OK = 0;
    VENDOR_API_FAILURE = 100;
    VERIFICATION_FAILED = 101;
    // User has already activated card
    CARD_ALREADY_ACTIVATED = 102;
  }
  rpc.Status status = 1;
}

message FetchDeliveryTrackingStatusRequest {
  string card_id = 1;
}

message FetchDeliveryTrackingStatusResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  CardDeliveryTrackingState delivery_state = 2;

  bool mobile_number_updated = 3;

  // time at which user activated the card via qr code scan
  google.protobuf.Timestamp card_activated_at = 4;
}

message FetchTransactionableCardsRequest {
  string actor_id = 1;
}

message FetchTransactionableCardsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  repeated Card cards = 2;
}

message FetchCardDetailsByAccountIdRequest {
  string savings_account_id = 1;
  // Indicates the cards to be fetched based on the states.
  // For example: fetch all the activated cards or expired cards
  repeated CardState card_states = 3;
}

message FetchCardDetailsByAccountIdResponse {
  rpc.Status status = 1;
  repeated Card card = 2;
}

message FetchCardTrackingDetailsRequest {
  // Number for requests to be processed
  int32 limit = 1;
}

message FetchCardTrackingDetailsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
}

message ProcessManualCardUnsuspendRequest {
  // card id for the card which is to be marked as active/un-suspended
  string card_id = 1;
  // source/entry point from where card is to be marked un-suspended
  card.Provenance provenance = 2;
  // reason for un-suspending the card
  string reason = 3;
}

message ProcessManualCardUnsuspendResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message GetLatestCardForActorIdsRequest {
  repeated string actor_ids = 1 [(validate.rules).repeated.min_items = 1];
  // card states filter for fetching cards only with the given states
  repeated card.CardState card_states = 2;
}

message GetLatestCardForActorIdsResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // map of actor id to current card
  map<string, card.Card> cards = 2;
}

message GetLastPhysicalCardIssuedForUserRequest {
  string actor_id = 1;
}

message GetLastPhysicalCardIssuedForUserResponse {
  rpc.Status status = 1;
  // last physical card issued timestamp
  // if physical card is not issued we will return NotFound status code
  google.protobuf.Timestamp last_issued_time = 2;
}

message GetDcInternationalWidgetRequest {
  string actor_id = 1;
  // country code is the country name which will be supplied by the client, or taken as a default value otherwise
  // The code should match the key in frontend web country map
  // Reference: https://github.com/epiFi/gamma/blob/a0fbb54faa0e453c033405bb29fc33283365a671/webfe/travel/constant.go
  string country_code = 2;
}

message GetDcInternationalWidgetResponse {
  enum Status {
    OK = 0;
    // if international widget is not to be displayed
    // the widget is currently being displayed is international travel mode is on
    DO_NOT_DISPLAY_WIDGET = 100;
  }
  rpc.Status status = 1;

  DcInternationalWidgetDetails dc_international_widget_details = 2;

}

message DcInternationalWidgetDetails {
  // returning country in case it is determined by location & not manually by the user
 // the value will be a key in WebFe CountryInfoMap
  string country = 1;
  // account balance in country's currency code
  string account_balance = 2;
  // value of 1 country's currency to INR
  float exchange_rate = 3;
  enum BottomSectionBackgroundState {
    BOTTOM_SECTION_BACKGROUND_UNSPECIFIED = 0;
    BOTTOM_SECTION_BACKGROUND_LIGHT = 1;
    BOTTOM_SECTION_BACKGROUND_DARK = 2;
  }
  BottomSectionBackgroundState bottom_section_background_state = 4;
  enum BottomSectionCtaState {
    BOTTOM_SECTION_CTA_UNSPECIFIED = 0;
    BOTTOM_SECTION_CTA_DC_INTERNATIONAL_USAGE_OR_LIMITS_DISABLED = 1;
    BOTTOM_SECTION_CTA_ECOMMERCE_USAGE_OFF = 2;
    BOTTOM_SECTION_CTA_ECOMMERCE_LIMITS_LOW = 3;
    BOTTOM_SECTION_CTA_ALL_TRANSACTIONS = 4;
    BOTTOM_SECTION_CTA_DC_HOME = 5;
  }
  BottomSectionCtaState bottom_section_cta_state = 5;
  string bottom_section_text = 6;
  string card_id = 7;
  string card_savings_account_id = 8;
}
