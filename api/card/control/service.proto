// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package card.control;

import "api/card/card.proto";
import "api/card/card_block.proto";
import "api/card/control.proto";
import "api/card/control/card_limit.proto";
import "api/rpc/status.proto";
import "api/vendorgateway/vendor.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/card/control";
option java_package = "com.github.epifi.gamma.api.card.control";

// CardControl service facilitates managing various controls related to card aimed at providing
// a better sense of security and more control over user's money.
//
// It provides for following w.r.t cards
// * transaction based controls - control expenditure for ATM, POS, E-commerce txns
// * location based controls - enable/disable card for the domestic/international usage
// * merchant based controls - N.A.
// * Block a card permanently.
// * Suspend/Unsuspend a card (temporarily)
service CardControl {

  // Rpc blocks the card. Block is a permanent operation i.e. the same card cannot be used after blocking.
  // Users may require to request a new card to make ATM/POS transactions.
  rpc BlockCard (BlockCardRequest) returns (BlockCardResponse) {}

  // Rpc suspends/un-suspends cards. A suspended card cannot be used for any
  // transactions. Suspend is a temporary state. A card can be un-suspended(OFF) by the user.
  rpc SuspendCard (SuspendCardRequest) returns (SuspendCardResponse) {}

  // Rpc facilitates to enable/disable cards for domestic/international usages.
  rpc LocationOnOff (LocationOnOffRequest) returns (LocationOnOffResponse) {}

  // Rpc facilitates to enable/disable cards for e-commerce transactions.
  rpc ECommerceOnOff (ECommerceOnOffRequest) returns (ECommerceOnOffResponse) {}

  // Rpc facilitates to enable/disable cards for NFC transactions for all locations.
  rpc NfcOnOff (NfcOnOffRequest) returns (NfcOnOffResponse) {}

  // Rpc facilitates to enable/disable cards for a given combination of transaction type and usage location type.
  rpc ControlOnOff (ControlOnOffRequest) returns (ControlOnOffResponse) {}

  // RPC to fetch card limit details
  rpc GetCardLimits (GetCardLimitsRequest) returns (GetCardLimitsResponse) {}

  // RPC to update card limit at vendor
  rpc UpdateCardLimits (UpdateCardLimitsRequest) returns (UpdateCardLimitsResponse) {}

  // Rpc facilitates to enable/disable cards for atm transactions.
  rpc ATMOnOff (ATMOnOffRequest) returns (ATMOnOffResponse) {}

  // Rpc facilitates to enable/disable cards for pos transactions.
  rpc POSOnOff (POSOnOffRequest) returns (POSOnOffResponse) {}

  // Rpc fetches card limits from database table
  rpc FetchCardLimits (FetchCardLimitsRequest) returns (FetchCardLimitsResponse) {}

  // ConsolidatedCardControlOnOff rpc to enable/disable any number of card controls at once. Caller need to pass enable/disable flag again
  // each card control and it will be set at vendor end accordingly.
  // Cred block is required if any of the control has to be enabled. During onboarding we can use the
  // one time pin set token to enable the controls.
  rpc ConsolidatedCardControlOnOff (ConsolidatedCardControlOnOffRequest) returns (ConsolidatedCardControlOnOffResponse) {}

  // GetInternationalAtmLimits RPC retrieves the ATM withdrawal limits for various countries.
  // It provides information on the maximum amount a user can withdraw from ATMs in different countries.
  rpc GetInternationalAtmLimits (GetInternationalAtmLimitsRequest) returns (GetInternationalAtmLimitsResponse);

  // RPC to update travel mode for a user
  // Enabling travel mode will enable only the travel mode for the user.
  // Disabling travel mode will disable POS, ATM, Tap n Pay for International transactions
  rpc SetTravelMode (SetTravelModeRequest) returns (SetTravelModeResponse);

  // RPC to get the current travel mode status for a user
  rpc GetTravelMode (GetTravelModeRequest) returns (GetTravelModeResponse);
}

message SetTravelModeRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 1}];
  bool enable_travel_mode = 2;
  ControlActionWorkflow workflow_type = 3;
}

message SetTravelModeResponse {
  rpc.Status status = 1;
  bool is_travel_mode_on = 2;
}

message GetTravelModeRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 1}];
}

message GetTravelModeResponse {
  rpc.Status status = 1;
  bool is_travel_mode_on = 2;
  // this identifies the workflow via which travel mode was enabled
  // will be populated only if the travel is active
  ControlActionWorkflow enable_workflow = 3;
}

message GetInternationalAtmLimitsRequest {
  oneof GetBy {
    CountryCodes country_codes = 1;
    bool get_all = 2;
  }
  // Optional: will be used for getting user's current location country; international only
  string actor_id = 7;

  message CountryCodes {
    repeated string codes = 1;
  }
}

message GetInternationalAtmLimitsResponse {
  rpc.Status status = 1;
  // List of ATM limits for different countries
  repeated InternationalAtmLimit international_atm_limits = 2;
  // Indicates if the user is currently at an international location
  bool is_user_at_international_location = 3;
  // Name of the international country where the user is currently visiting
  string user_country_name = 4;
}

// Represents ATM withdrawal limit details for a specific country
message InternationalAtmLimit {
  string country_name = 1;
  // ISO code of the country
  string country_code = 2;
  // emoji for the country's flag
  string country_flag = 3;
  // Maximum ATM withdrawal limit in the indian currency
  google.type.Money max_atm_withdrawal_limit = 4;
}

message BlockCardRequest {
  repeated string card_ids = 1;
  vendorgateway.Vendor vendor = 2;
  string block_card_reason = 3;
  card.Provenance block_card_provenance = 4;
  // When we want to block the card only at our end, e.x. when the card is already blocked at vendor end like hotmarked cards
  bool skip_vendor_call = 5;
}

message BlockCardResponse {
  rpc.Status status = 1;
  map<string, Card> blocked_cards = 2;
}

message SuspendCardRequest {
  repeated string card_ids = 1;
  CardControlAction action = 2;
  vendorgateway.Vendor vendor = 3;
  string cred_block = 5;
  // request/txn-id used as a salt in credblock generation
  string request_id = 6;
  // Workflow from where the request was initiated
  ControlActionWorkflow control_action_workflow = 7;
}

message SuspendStatesInfo {
  Card card = 1;
  CardControlAction action = 2;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 3;
}

message SuspendCardResponse {
  enum Status {
    OK = 0;

    TRANSIENT_FAILURE = 100;

    PERMANENT_FAILURE = 101;

    PIN_RETRIES_EXCEEDED = 201;

    INVALID_SECURE_PIN = 202;

    // Cred block failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  rpc.Status status = 1;
  map<string, SuspendStatesInfo> suspend_states = 2;
}

message LocationOnOffRequest {
  repeated string card_ids = 1;
  CardUsageLocationType locType = 2;
  CardControlAction action = 3;
  vendorgateway.Vendor vendor = 4;
  string cred_block = 5;
  // request/txn-id used as a salt in credblock generation (if credblock is provided for request)
  string request_id = 6;
  // Workflow from where the request was initiated
  ControlActionWorkflow control_action_workflow = 7;
}

message LocationOnOffStatesInfo {
  Card card = 1;
  CardControlAction action = 2;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 3;
}

message LocationOnOffResponse {
  enum Status {
    OK = 0;

    TRANSIENT_FAILURE = 100;

    PERMANENT_FAILURE = 101;

    PIN_RETRIES_EXCEEDED = 201;

    INVALID_SECURE_PIN = 202;

    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  rpc.Status status = 1;
  map<string, LocationOnOffStatesInfo> location_on_off_states = 2;
}

message ECommerceOnOffRequest {
  repeated string card_ids = 1;
  CardControlAction action = 2;
  vendorgateway.Vendor vendor = 3;
  string cred_block = 4;
  // request/txn-id used as a salt in credblock generation (if credblock is provided for request)
  string request_id = 5;
  // CardControlWorkflow is to mark the type of card control request.
  // Such as secure pin workflow or token workflow.
  // We will pass CARD_CTRL_TOKEN_WF when we enable e-commerce during setting card pin in background
  CardControlWorkflow ecomm_enable_flow = 6;
  // Workflow from where the request was initiated
  ControlActionWorkflow control_action_workflow = 7;
}

// CardControlWorkflow is to mark the type of card control request.
enum CardControlWorkflow {
  CARD_CTRL_WF_UNSPECIFIED = 0;

  // for enabling control via otp auth/credblock
  CARD_CTRL_SECURE_PIN_WF = 1;

  // For enabling control via token. It will be used while onboarding only.
  CARD_CTRL_TOKEN_WF = 2;
}

message ECommerceOnOffStatesInfo {
  Card card = 1;
  CardControlAction action = 2;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 3;
}

message ECommerceOnOffResponse {
  enum Status {
    OK = 0;

    TRANSIENT_FAILURE = 100;

    PERMANENT_FAILURE = 101;

    PIN_RETRIES_EXCEEDED = 201;

    INVALID_SECURE_PIN = 202;

    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  rpc.Status status = 1;
  map<string, ECommerceOnOffStatesInfo> ecomm_on_off_states = 2;
}

message ATMOnOffRequest {
  string card_id = 1;
  CardControlAction action = 2;
  string cred_block = 3;
  // request/txn-id used as a salt in credblock generation (if credblock is provided for request)
  // Cred block will be required only in case of enabling ATM transactions
  string request_id = 5;
  // Workflow from where the request was initiated
  ControlActionWorkflow control_action_workflow = 7;
}

message ATMOnOffResponse {
  enum Status {
    OK = 0;

    TRANSIENT_FAILURE = 100;

    PERMANENT_FAILURE = 101;

    PIN_RETRIES_EXCEEDED = 201;

    INVALID_SECURE_PIN = 202;

    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  rpc.Status status = 1;

  ATMOnOffStatesInfo atm_on_off_state = 2;
}

message ATMOnOffStatesInfo {
  Card card = 1;
  CardControlAction action = 2;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 3;
}

message POSOnOffRequest {
  string card_id = 1;
  CardControlAction action = 2;
  string cred_block = 3;
  // request/txn-id used as a salt in credblock generation (if credblock is provided for request)
  // Cred block will be required only in case of enabling POS transactions
  string request_id = 4;
  // Workflow from where the request was initiated
  ControlActionWorkflow control_action_workflow = 7;
}

message POSOnOffResponse {
  enum Status {
    OK = 0;

    TRANSIENT_FAILURE = 100;

    PERMANENT_FAILURE = 101;

    PIN_RETRIES_EXCEEDED = 201;

    INVALID_SECURE_PIN = 202;

    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  rpc.Status status = 1;

  POSOnOffStatesInfo pos_on_off_state = 2;
}

message POSOnOffStatesInfo {
  Card card = 1;
  CardControlAction action = 2;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 3;
}

message NfcOnOffRequest {
  repeated string card_ids = 1;
  CardControlAction action = 2;
  vendorgateway.Vendor vendor = 3;
  string cred_block = 4;
  // request/txn-id used as a salt in credblock generation (if credblock is provided for request)
  string request_id = 5;
  // Workflow from where the request was initiated
  ControlActionWorkflow control_action_workflow = 7;
}

message NfcOnOffStatesInfo {
  Card card = 1;
  CardControlAction action = 2;
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 3;
}

message NfcOnOffResponse {
  enum Status {
    OK = 0;

    TRANSIENT_FAILURE = 100;

    PERMANENT_FAILURE = 101;

    PIN_RETRIES_EXCEEDED = 201;

    INVALID_SECURE_PIN = 202;

    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  rpc.Status status = 1;
  map<string, NfcOnOffStatesInfo> nfc_on_off_states = 2;
}

message ControlOnOffRequest {
  repeated string card_ids = 1;
  CardUsageLocationType loc_type = 2;
  CardTransactionType txn_type = 3;
  CardControlAction action = 4;
  vendorgateway.Vendor vendor = 5;
  string cred_block = 6;
  // request/txn-id used as a salt in credblock generation (if credblock is provided for request)
  string request_id = 7;
}

message ControlOnOffStatesInfo {
  Card card = 1;
  CardControlAction action = 2;
}

message ControlOnOffResponse {
  rpc.Status status = 1;
  map<string, ControlOnOffStatesInfo> control_on_off_states = 2;
}



message GetCardLimitsRequest {
  // Card-id of enquired card.
  string card_id = 1;
}

message GetCardLimitsResponse {
  rpc.Status status = 1;

  // card limits
  card.control.CardLimitData card_limit_data = 2;

  // request-id used to get card limit at vendor.
  // This request id need to be send in update card limit for any update request.
  string request_id = 3;

  // Federal mandates that the same request-id be used for both GetLimit and UpdateLimit api calls.
  // During limit update, if credblock is required, masked card number will be used as salt to generate credblock
  string masked_card_number = 4;

  // maximum daily allowed limits for a card
  AllowedLimits daily_allowed_limits = 5;

  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 6;

  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Requested entity i.e., card-id or controls not found
    // One of the reasons could be card doesn't exist.
    NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
}

message UpdateCardLimitsRequest {
  // Card-id of enquired card.
  string card_id = 1;

  // request-id used to generate cred block
  string request_id = 2;

  // cred block (encrypted pin+salt in base64 format)
  string cred_block = 3;

  // list of card limit request to update at vendor
  repeated card.control.CardLimitDetail update_card_limit_details = 4;

  // it should be true if any card limit detail is increased from it's current value.
  bool is_limit_increased = 5;

  // If limit update is initiated by the user via Card limit screen this flag should be true else false
  bool is_user_initiated = 6;
}

message UpdateCardLimitsResponse {
  rpc.Status status = 1;

  card.control.CardLimitData card_limit_data = 2;

  // request-id used to get updated card limit at vendor.
  // This request id need to be send if user again want to update card limit.
  string request_id = 3;

  // Federal mandates that the same request-id be used for both GetLimit and UpdateLimit api calls.
  // During limit update, if credblock is required, masked card number will be used as salt to generate credblock
  string masked_card_number = 4;

  // maximum daily allowed limits for a card
  AllowedLimits daily_allowed_limits = 5;

  // List of status codes returned
  enum Status {
    // Returned an success
    OK = 0;
    // Requested entity i.e., card-id or controls not found
    // One of the reasons could be card doesn't exist.
    NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // pre condition to mark a card is not in state to make update card limits.
    FAILED_PRECONDITION = 9;

    TRANSIENT_FAILURE = 100;

    PERMANENT_FAILURE = 101;

    PIN_RETRIES_EXCEEDED = 201;

    INVALID_SECURE_PIN = 202;

    // Cred block validation failures includes all the failures related to cred block such as invalid cred block,
    // deviceKeys not registered for given details, hmac does not match, key id not matched with open bank key etc
    CRED_BLOCK_FAILURE = 203;
  }
  // Internal status codes returned by vendor gateway API's corresponding to vendor returned code.
  // We will use this status code to convert error response to UI error view which will be shown to the client.
  string internal_status_code = 6;
}

message AllowedLimits {
  // max limit for card
  google.type.Money card_max_limit = 1;

  // max purchase amount that can be set.
  // Purchase limit is a combined limit for POS, NFC, ECOM. It also includes both domestic and international spending.
  google.type.Money purchase_max_limit = 2;

  // max atm amount that can be set.
  google.type.Money atm_max_limit = 3;
}

message FetchCardLimitsRequest {
  // Card-id of enquired card.
  string card_id = 1;
}

message FetchCardLimitsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  // card limits
  card.control.CardLimitData card_limit_data = 2;
}

enum ControlActionWorkflow {
  CONTROL_ACTION_WORKFLOW_UNSPECIFIED = 0;

  USER_INITIATED = 1;

  INTERNAL = 2;
}

message ConsolidatedCardControlOnOffRequest {
  // unique identifier of the card for which controls needs to be changed
  string card_id = 1;

  // map<CardControlType, CardControlAction>. Specifies the action to be performed for each control
  map<int32, card.CardControlAction> control_actions = 2;

  // unique identifier for the request
  string request_id = 3;

  // encrypted pin. Required when any action is to be enabled
  string cred_block = 4;

  // workflow for card control request.
  // We will pass CARD_CTRL_TOKEN_WF when we enable e-commerce during setting card pin in background
  card.control.CardControlWorkflow control_workflow = 5;
  // Workflow from where the request was initiated
  // This is to not initiate any sort of communication to user if done from internal process
  card.control.ControlActionWorkflow control_action_workflow = 6;
}

message ConsolidatedCardControlOnOffResponse {
  enum Status {
    OK = 0;

    NOT_FOUND = 5;

    INTERNAL = 13;

    // user has entered incorrect pin more than x times
    PIN_RETRIES_EXCEEDED = 201;

    // invalid secure(upi) pin entered by user
    INVALID_SECURE_PIN = 202;
  }
  rpc.Status status = 1;
  // Internal response code returned by VG API corresponding to vendor returned code.
  // We will use this status code to convert error response to client error view which will be shown on the UI.
  string internal_response_code = 2;
}
