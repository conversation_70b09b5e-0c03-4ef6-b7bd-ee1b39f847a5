// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package aws.s3;

option go_package = "github.com/epifi/gamma/api/aws/s3";
option java_package = "com.github.epifi.gamma.api.aws.s3";

// The notification message that Amazon S3 sends to publish an event.
message Record {
  // The eventVersion key value contains a major and minor version in the form <major>.<minor>.
  // The major version is incremented if Amazon S3 makes a change to the event structure that is not backward compatible.
  // This includes removing a JSON field that is already present or changing how the contents of a field are represented (for example, a date format).
  // The minor version is incremented if Amazon S3 adds new fields to the event structure.
  // This might occur if new information is provided for some or all existing events, or if new information is provided on only newly introduced event api.typesv2.
  // Applications should ignore new fields to stay forward compatible with new minor versions of the event structure.
  // If new event types are introduced but the structure of the event is otherwise unmodified, the event version does not change.
  string event_version = 1 [json_name = "eventVersion"];

  string event_source = 2 [json_name = "eventSource"];
  string aws_region = 3 [json_name = "awsRegion"];

  // The time, in ISO-8601 format, for example, 1970-01-01T00:00:00.000Z, when Amazon S3 finished processing the request
  string event_time = 4 [json_name = "eventTime"];

  // The eventName references the list of event notification types but does not contain the s3: prefix.
  // ex: ObjectCreated:Put
  string eventName = 5 [json_name = "eventName"];

  // The s3 key provides information about the bucket and object involved in the event.
  // The object key name value is URL encoded. For example, "red flower.jpg" becomes "red+flower.jpg" (Amazon S3 returns "application/x-www-form-urlencoded" as the content type in the response).
  aws.s3.S3 s3 = 6 [json_name = "s3"];
}

message S3 {
  aws.s3.Bucket bucket = 1 [json_name = "bucket"];
  aws.s3.Object object = 2 [json_name = "object"];
}

message Bucket {
  string name = 1 [json_name = "name"];
  string arn = 2 [json_name = "arn"];
}

message Object {
  string key = 1 [json_name = "key"];
  int32 size = 2 [json_name = "size"];
  string e_tag = 3 [json_name = "eTag"];
}
