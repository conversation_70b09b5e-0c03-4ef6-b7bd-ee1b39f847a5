// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package consumer;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/customdelayqueue/consumer";
option java_package = "com.github.epifi.gamma.api.customdelayqueue.consumer";

message OrchestratorHeader {
  // time at which the event needs to be delivered to destination queue
  google.protobuf.Timestamp delivery_to_dest_at = 1;
  // destination queue where event needs to be delivered
  string dest_queue_name = 2;
  // event payload in base64 encoded format
  string actual_event_payload = 3;
}
