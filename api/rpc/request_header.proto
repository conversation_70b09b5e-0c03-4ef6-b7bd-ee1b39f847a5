syntax = "proto3";

package rpc;

option go_package = "github.com/epifi/be-common/api/rpc";
option java_package = "com.github.epifi.gamma.api.rpc";

// Request header to propagate commonly used fields to all the services
// This will contain values which can be set by clients but not limited to it
// Some of the Auth header fields might also move here in future
message RequestHeader {
  // Since the user_id is generated in the system only after the email_id confirmation step in onboarding flow,
  // we are currently unable to track user-triggered events before that step (i.e. app installed, mobile no verified).
  // A prospect_id will be generated and maintained to keep track of the user’s actions until they become a customer.
  string prospect_id = 1;

  // Sessions play a critical role in analyzing the nature of user behavior on our app.
  // By connecting user engagement to specific sessions, we can understand cadence of activity on our app.
  // Appending a session_id to every event sent from the frontend or backend (where applicable) will
  // help analytics and DS teams at epiFi enrich analysis on downstream tools like Amplitude and Snowflake.
  // Session ID can also help Data Engineering accurately connect/map multiple events for each user as part
  // of said user's session/journey on the Fi App.
  string session_id = 2;

  // Attempt ID is a very flow-specific concept and does NOT apply to all user activity or actions on app.
  // This will be passed with events capturing user actions within specific, pre-defined flows.
  // (e.g. payment flow, search flow etc.)
  string attempt_id = 3;
}
