// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package inapphelp.faq.serving;

import "api/cx/method_options.proto";
import "api/inapphelp/faq/enums/enums.proto";
import "api/inapphelp/faq/serving/message.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/device.proto";
import "api/vendorgateway/cx/solutions/article.proto";
import "api/vendorgateway/cx/solutions/category.proto";
import "api/vendorgateway/cx/solutions/folder.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/inapphelp/faq/serving";
option java_package = "com.github.epifi.gamma.api.inapphelp.faq.serving";

service ServeFAQ {
  // Does not require mandatory input parameters
  // Returns all the categories we have for FAQ as list
  // Internal server error if backend fails to fetch FAQs
  rpc GetAllCategories (GetAllCategoriesRequest) returns (GetAllCategoriesResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Returns a list of all the folders in the category
  // category id is mandatory for this call
  // INVALID ARGUMENT ERROR if category id is missing
  // INTERNAL SERVER ERROR if backend fails to fetch FAQs or id is invalid
  rpc GetAllFoldersInCategory (GetAllFoldersInCategoryRequest) returns (GetAllFoldersInCategoryResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Returns a list of all the articles in the folder
  // folder id is mandatory for this call
  // INVALID ARGUMENT ERROR if folder id is missing
  // INTERNAL SERVER ERROR if backend fails to fetch FAQs or id is invalid
  rpc GetAllArticlesInFolder (GetAllArticlesInFolderRequest) returns (GetAllArticlesInFolderResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Get category from id passed
  // INVALID ARGUMENT if id is not passed
  // CODE NOT FOUND if id does not have a mapping in DB
  // INTERNAL SERVER ERROR if some error is encountered
  rpc GetCategory (GetCategoryRequest) returns (GetCategoryResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Get folder from id passed
  // INVALID ARGUMENT if id is not passed
  // CODE NOT FOUND if id does not have a mapping in DB
  // INTERNAL SERVER ERROR if some error is encountered
  rpc GetFolder (GetFolderRequest) returns (GetFolderResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Get article from id passed
  // INVALID ARGUMENT if id is not passed
  // CODE NOT FOUND if id does not have a mapping in DB
  // INTERNAL SERVER ERROR if some error is encountered
  rpc GetArticle (GetArticleRequest) returns (GetArticleResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetFAQByContext returns a list of Faq mapped to the given context
  // Request contains FAQContextType and FaqContextValue both are mandatory parameters
  // Response contains list of Faqs mapped against given context
  // Before using this RPC we must create a mapping of context value to list of Faqs
  // this can be done using CREATE_FAQ_CONTEXT_MAPPING dev action
  rpc GetFAQByContext (GetFAQByContextRequest) returns (GetFAQByContextResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // API to submit feedback against a faq article
  // actor id, article id and response are mandatory
  // API returns a feedback ID which will be used in further flows to update feedback
  // API also returns bottom sheet with list of options for negative feedback
  rpc SubmitArticleFeedback (SubmitArticleFeedbackRequest) returns (SubmitArticleFeedbackResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // API to update feedback reason against a feedback id
  rpc UpdateFeedbackReason (UpdateFeedbackReasonRequest) returns (UpdateFeedbackReasonResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // API to update FAQ using FAQ ID
  rpc UpdateFAQ (UpdateFAQRequest) returns (UpdateFAQResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // V2 API with updated bottom sheet options and additional sanity for reason options
  // Request accepts actor id, article id and feedback response (POSITIVE, NEGATIVE) enum
  // Response contains feedback id, rpc status code and bottom sheet v2 object
  // feedback id is db record id for that feedback
  // bottom sheet v2 is only returned in case of negative feedback, it contains user to provide extended feedback on that FAQ
  // rpc status code is OK if call is successful
  // InvalidArg if input param has invalid data
  // ISE for any other errors
  rpc SubmitArticleFeedbackV2 (SubmitArticleFeedbackRequestV2) returns (SubmitArticleFeedbackResponseV2) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // V2 API to support feedback on existing comments and support for single/multi select reason use case
  // Request accepts feedback id, reasons list and user comments
  // response contains rpc status code
  // InvalidArg if input param has invalid data
  // ISE if any database calls fails
  // OK if call is successful
  rpc UpdateFeedbackReasonV2 (UpdateFeedbackReasonRequestV2) returns (UpdateFeedbackReasonResponseV2) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to fetch list of popular FAQ (Articles)
  // request contains actor id
  // response contains list of article objects and rpc status code
  // OK if call is successful
  // Internal for server errors
  rpc GetPopularFAQList (GetPopularFAQListRequest) returns (GetPopularFAQListResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // CreateBulkFAQContextMapping rpc creates mapping between given context value and list of FAQs provided
  // Request contains context value, and list of ids for article, folder, and category
  // supports mapping of at max 50 FAQs of each type at a time
  // Response contains RPC status
  // OK - Success
  // InvalidArguments - mandatory parameters missing
  // ISE - Any unknown failure
  rpc CreateBulkFAQContextMapping (CreateBulkFAQContextMappingRequest) returns (CreateBulkFAQContextMappingResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message GetAllCategoriesRequest {
  inapphelp.faq.enums.FaqFetchSource faq_fetch_source = 1;
  string actor_id = 2;
}

message GetAllCategoriesResponse {
  rpc.Status status = 1;

  repeated cx.solutions.Category categories = 2;
}

message GetAllFoldersInCategoryRequest {
  inapphelp.faq.enums.FaqFetchSource faq_fetch_source = 1;

  // category id for which the folders are to be fetched
  int64 category_id = 2;

  string actor_id = 3;
}

message GetAllFoldersInCategoryResponse {
  rpc.Status status = 1;

  repeated cx.solutions.Folder folders = 2;
}

message GetAllArticlesInFolderRequest {
  inapphelp.faq.enums.FaqFetchSource faq_fetch_source = 1;

  // folder id
  int64 folder_id = 2;

  string actor_id = 3;
}

message GetAllArticlesInFolderResponse {
  rpc.Status status = 1;

  repeated cx.solutions.Article articles = 2;
}

message GetFAQByContextRequest {
  // [Mandatory] FAQContext contains type of context and its corresponding value
  // both are mandatory
  FAQContext faq_context = 1;
}

message GetFAQByContextResponse {
  rpc.Status status = 1;
  // List of Faqs mapped against given context
  // FAQContent will contain faq_type and the actual content
  repeated FAQContent faq_list = 2;
}

message GetFAQByIdRequest {
  // mandatory parameter
  int64 faq_id = 1;

  // mandatory parameter
  inapphelp.faq.enums.FAQType faq_type = 2;

  inapphelp.faq.enums.FaqFetchSource faq_fetch_source = 3;
}

message GetFAQByIdResponse {
  rpc.Status status = 1;

  inapphelp.faq.enums.FAQType faq_type = 2;

  oneof faq {
    cx.solutions.Category category = 3;

    cx.solutions.Folder folder = 4;

    cx.solutions.Article article = 5;
  }
}

message GetCategoryRequest {
  int64 category_id = 1;

  inapphelp.faq.enums.FaqFetchSource faq_fetch_source = 2;
}

message GetCategoryResponse {
  rpc.Status status = 1;

  cx.solutions.Category category = 2;
}

message GetFolderRequest {
  int64 folder_id = 1;

  inapphelp.faq.enums.FaqFetchSource faq_fetch_source = 2;
}

message GetFolderResponse {
  rpc.Status status = 1;

  cx.solutions.Folder folder = 2;
}

message GetArticleRequest {
  int64 article_id = 1;

  inapphelp.faq.enums.FaqFetchSource faq_fetch_source = 2;
}

message GetArticleResponse {
  rpc.Status status = 1;

  cx.solutions.Article article = 2;
}

message SubmitArticleFeedbackRequest {
  // Mandatory
  string actor_id = 1;

  // deprecated in favour of SourceIdentifier
  int64 article_id = 2 [deprecated = true];

  // Mandatory
  inapphelp.faq.enums.FeedbackResponse feedback_response = 3;

  // SourceIdentifier helps us find out the source for which feedback is being provided
  SourceIdentifier source_identifier = 4;
}

message SubmitArticleFeedbackResponse {
  rpc.Status status = 1;

  string feedback_id = 2;

  inapphelp.faq.serving.BottomSheet bottom_sheet = 3;
}

message UpdateFeedbackReasonRequest {
  // Mandatory
  string feedback_id = 1;

  // Mandatory
  string reason = 2;
}

message UpdateFeedbackReasonResponse {
  rpc.Status status = 1;
}

message UpdateFAQRequest {
  // specifies what level of faq has to be updated
  inapphelp.faq.enums.FAQType update_faq_type = 1;

  // unique id which identifies that faq
  int64 faq_id = 2;

  // faq visibility
  cx.solutions.CategoryVisibility faq_visibility = 3 [deprecated = true];

  // rank field which has to be updated, restricted to only category level
  int64 category_rank = 4 [deprecated = true];

  // icon url, restricted to only category level
  string category_icon_url = 5 [deprecated = true];

  // faq fields which has to be updated, need to set value in this msg
  inapphelp.faq.serving.FaqFields updated_faq_fields = 6;

  // set to true if all underlying folders and articles of category need to inherit the same property
  // this is only applicable when updated_faq_type is category
  api.typesv2.common.BooleanEnum is_bulk_update_required_for_category_hierarchy = 7;

  // specifies which fields in faq has to be updated
  repeated inapphelp.faq.serving.FaqUpdateFieldMask faq_update_field_mask = 8;
}

message UpdateFAQResponse {
  rpc.Status status = 1;
}

// enum for category update use
enum CategoryFieldMask {
  CategoryFieldMask_UNSPECIFIED = 0;
  CATEGORY_RANK = 1;
  CATEGORY_ICON_URL = 2;
  CATEGORY_VISIBILITY = 3;
  CATEGORY_GROUP = 4;
}

/*
 V2 APIs for FAQ feedback flow
 - Supports multi select and single select reason selection
 - Supports end user to add comments on pre-given reason options
 - Pre-given reason options are now key/enum.string driven (against v1 where client used to options as string) under BottomSheet v2
 V1 APIs are untouched for backward compatibility
 */


// to rate article as positive or negative
// if negative add user for additional feedback using BottomSheet
message SubmitArticleFeedbackRequestV2 {
  // Mandatory
  string actor_id = 1;

  // Mandatory
  int64 article_id = 2;

  // Mandatory
  inapphelp.faq.enums.FeedbackResponse feedback_response = 3;
}

message SubmitArticleFeedbackResponseV2 {
  rpc.Status status = 1;

  // feedback id given in response
  // to allow client to pass when feedback is given on negative rating
  string feedback_id = 2;

  // supports for pre-given option as key-value pair
  // key acts as identifier in backend on which option was selected to have tighter control over what data is stored in db
  inapphelp.faq.serving.BottomSheetV2 bottom_sheet = 3;
}

/*
 Major changes:
 - Multiple reason can be selected instead of just one
 - Client to pass FeedbackReasonOption.value in reason field
 - Client to use FeedbackReasonOption.label to surface human readable reason string
 - If user choose to give feedback for ONE article it will be stored as {reason[0], user_comment}
 - If user choose to give feedback for MORE THAN ONE article than only one feedback should be asked
 - it wil be stored as {[]reasons, user_comment}
 */

message UpdateFeedbackReasonRequestV2 {
  // Mandatory
  string feedback_id = 1;

  // use key-value pair to identify and store pre-given reason options
  // keeping this repeated to support multi select reason on app without any changes
  // pass ReasonOption enum as string in FeedbackReasonOption.value
  // BE will identify what option was selected
  repeated string reasons = 2;

  // this field has to be populated when user choose to write additional feedback on pre-given options
  // for multi-select all selected reason should be populated in reason list and feedback should be populated in this field
  // for single-select same strategy should be followed
  string user_comment = 3;
}

message UpdateFeedbackReasonResponseV2 {
  rpc.Status status = 1;
}

message GetPopularFAQListRequest {
  // actor id
  string actor_id = 1;

  // platform: android, ios, web, etc
  api.typesv2.common.Platform fetch_source = 2;
}

message GetPopularFAQListResponse {
  // rpc status code
  rpc.Status status = 1;
  // list of popular articles
  repeated cx.solutions.Article article_list = 2;
}

message CreateBulkFAQContextMappingRequest {
  // [Mandatory] context to which corresponding faq_ids will be mapped
  FAQContext faq_context = 1;
  // [Mandatory] list of article ids which are to be mapped against context_value
  repeated int64 article_ids = 2 [(validate.rules).repeated.max_items = 50];
}

message CreateBulkFAQContextMappingResponse {
  rpc.Status status = 1;
}
