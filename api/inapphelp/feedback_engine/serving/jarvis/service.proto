syntax = "proto3";

package api.inapphelp.feedback_engine.serving.jarvis;

import "api/jarvis/method_options.proto";
import "api/jarvis/header.proto";
import "api/frontend/analytics/analytics_screen_name.proto";
import "api/inapphelp/feedback_engine/serving/jarvis/enums.proto";
import "api/inapphelp/feedback_engine/serving/jarvis/message.proto";
import "api/pkg/web/components.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/inapphelp/feedback_engine/serving/jarvis";
option java_package = "com.github.epifi.gamma.api.inapphelp.feedback_engine.serving.jarvis";

// allowed_roles parameter and resource_name is based on what is defined on keycloak by devops team
// only people who have a particular role required to access that RPC, will be able to acess it
// do note that roles are not inclusive or hierarchical in nature
service Frontend {
  // RPC to get details of the form to be displayed to user for adding a survey
  // This RPC also requires first question id as a mandatory parameter
  // the reason for this is we first create the questions in bulk for a survey without any approval
  // and then populate the first question id in the form which is non-editable
  rpc GetAddSurveyFormDetails (GetAddSurveyFormDetailsRequest) returns (GetAddSurveyFormDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "edit";
  }

  // RPC to get details of the form to be displayed to user for updating a survey
  rpc GetUpdateSurveyFormDetails (GetUpdateSurveyFormDetailsRequest) returns (GetUpdateSurveyFormDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "edit";
  }

  // RPC to get details of the form to be displayed to user for viewing details of a survey
  rpc GetViewSurveyFormDetails (GetViewSurveyFormDetailsRequest) returns (GetViewSurveyFormDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC internally used by feedback engine web service to add a survey to feedback engine
  // after the approval is complete
  rpc AddSurvey (AddSurveyRequest) returns (AddSurveyResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "approve";
  }

  // RPC to get details of the form to be displayed to add a question
  rpc GetAddQuestionFormDetails (GetAddQuestionFormDetailsRequest) returns (GetAddQuestionFormDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "edit";
  }

  // RPC to get details of the form to be displayed to update a question
  rpc GetUpdateQuestionFormDetails (GetUpdateQuestionFormDetailsRequest) returns (GetUpdateQuestionFormDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "edit";
  }

  // RPC to get details of the form to be displayed to view details of a single question
  // Not being utilized by frontend (jarvis) right now, as all the question are viewable
  // a single place in "view questions" tab of a survey
  rpc GetViewQuestionFormDetails (GetViewQuestionFormDetailsRequest) returns (GetViewQuestionFormDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC to add a list of questions to a created survey
  // This RPC is used in the initial step to add questions for a survey
  // As discussed with product adding questions initially will not require approval
  // and only when the survey to which these questions is attached is approved
  // then these questions will be served
  rpc AddQuestions (AddQuestionsRequest) returns (AddQuestionsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC to get details of a single survey
  rpc GetSurveyDetails (GetSurveyDetailsRequest) returns (GetSurveyDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC to send the updated details of a survey to backend
  rpc UpdateSurveyDetails (UpdateSurveyDetailsRequest) returns (UpdateSurveyDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "approve";
  }

  // RPC to get all feedback questions which will be asked as part of a feedback survey
  rpc GetAllFeedbackQuestionForSurvey (GetAllFeedbackQuestionForSurveyRequest) returns (GetAllFeedbackQuestionForSurveyResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC to get details of a feedback question
  rpc GetFeedbackQuestionDetails (GetFeedbackQuestionDetailsRequest) returns (GetFeedbackQuestionDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC to update details of a feedback question
  rpc UpdateFeedbackQuestionDetails (UpdateFeedbackQuestionDetailsRequest) returns (UpdateFeedbackQuestionDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "edit";
  }

  // RPC to fetch feedback entities that need approval action
  rpc GetFeedbackEntitiesByApprovalStatus (GetFeedbackEntitiesByApprovalStatusRequest) returns (GetFeedbackEntitiesByApprovalStatusResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC to fetch details of form which will be used to take approval actions for survey
  // approval id is a mandatory parameter in the request
  // approval id is defined as the id of the row of the feedback_entity_approvals table whose status is being updated
  rpc GetFeedbackEntityApprovalFormDetails (GetFeedbackEntityApprovalFormDetailsRequest) returns (GetFeedbackEntityApprovalFormDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "approve";
  }

  // RPC used to view a feedback entity's approval status
  // approval id is mandatory in request
  // approval id is defined as id of the row in feedback_entity_approvals table whose details the client is requesting
  // this RPC returns a pre-populated form with the approval details like approval statue, approver note etc
  rpc ViewFeedbackEntityApprovalFormDetails (ViewFeedbackEntityApprovalFormDetailsRequest) returns (ViewFeedbackEntityApprovalFormDetailsResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC to update approval status for a feedback entity
  // a feedback entity can be a survey, question etc
  // entity id and approval status are mandatory parameters in request
  // right now only a single pending approval is supported on a entity
  // also a question would only be allowed to be edited, if its related survey has been approved
  rpc UpdateApprovalStatusForFeedbackEntity (UpdateApprovalStatusForFeedbackEntityRequest) returns (UpdateApprovalStatusForFeedbackEntityResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "approve";
  }

  // RPC to fetch possible answer types for a feedback question
  rpc GetAnswerTypes (GetAnswerTypesRequest) returns (GetAnswerTypesResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC to fetch the UI that is used to show the all surveys table
  rpc GetAllSurveys (GetAllSurveysRequest) returns (GetAllSurveysResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "view";
  }

  // RPC to fetch the UI for table running on a analytics screen name
  // we need a separate RPC for this as GetAllSurveys fetches surveys from feedback_sur
  rpc GetSurveysRunningOnAnalyticsScreenName (GetSurveysRunningOnAnalyticsScreenNameRequest) returns (GetSurveysRunningOnAnalyticsScreenNameResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "edit";
  }

  // RPC to submit feedback survey related change for approval
  rpc SubmitFeedbackSurveyForApproval (SubmitFeedbackSurveyForApprovalRequest) returns (SubmitFeedbackSurveyForApprovalResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "edit";
  }

  // RPC to submit feedback question related change for approval
  rpc SubmitFeedbackQuestionForApproval (SubmitFeedbackQuestionForApprovalRequest) returns (SubmitFeedbackQuestionForApprovalResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "edit";
  }

  // This RPC would be used for pause/resume survey feature
  // survey id is mandatory in request
  // it flips the value of is_survey_mapping_enabled column in feedback_survey_mappings table corresponding to given survey id
  rpc SwitchSurveyMappingStatus (SwitchSurveyMappingStatusRequest) returns (SwitchSurveyMappingStatusResponse) {
    option (api.jarvis.method_options.resource_name) = "epifi:feedback-engine";
    option (api.jarvis.method_options.allowed_roles) = "approve";
  }
}

message GetAddSurveyFormDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  string first_question_id = 2;
}

message GetAddSurveyFormDetailsResponse {
  rpc.Status status = 1;
  FeedbackSurveyFormDetails add_feedback_survey_form_details = 2;
}

message GetUpdateSurveyFormDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  string survey_id = 2;
}

message GetUpdateSurveyFormDetailsResponse {
  rpc.Status status = 1;
  FeedbackSurveyFormDetails update_feedback_survey_form_details = 2;
}

message GetViewSurveyFormDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  string survey_id = 2;
}

message GetViewSurveyFormDetailsResponse {
  rpc.Status status = 1;
  ViewFeedbackSurveyFormDetails view_feedback_survey_form_details = 2;
}

message AddSurveyRequest {
  .jarvis.Header jarvis_header = 1;
  FeedbackSurveyFrontend feedback_survey = 2;
}

message AddSurveyResponse {
  rpc.Status status = 1;
  string survey_id = 2;
}

message GetAddQuestionFormDetailsRequest {
  .jarvis.Header jarvis_header = 1;
}

message GetAddQuestionFormDetailsResponse {
  rpc.Status status = 1;
  // map containing <feedbackAnswerType in string format, web form> mapping
  // the form contains only details related to the fields for question text and question heading
  // so if an answer type only has support for question heading then only that particular text input would be returned
  map<string, api.pkg.web.Form> feedback_questions_form_details_map = 2;
}

message GetUpdateQuestionFormDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  string question_id = 2;
}

message GetUpdateQuestionFormDetailsResponse {
  rpc.Status status = 1;
  UpdateFeedbackQuestionFormDetails update_feedback_question_form_details = 2;
}

message GetViewQuestionFormDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  string question_id = 2;
}

message GetViewQuestionFormDetailsResponse {
  rpc.Status status = 1;
  ViewFeedbackQuestionFormDetails view_feedback_question_form_details = 2;
}

message AddQuestionsRequest {
  .jarvis.Header jarvis_header = 1;
  repeated FeedbackQuestionFrontend feedback_question_details_list = 2;
}

message AddQuestionsResponse {
  rpc.Status status = 1;
  repeated string feedback_question_id_list = 2;
}

message GetSurveyDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  string survey_id = 2;
}

message GetSurveyDetailsResponse {
  rpc.Status status = 1;
  FeedbackSurveyFrontend feedback_survey = 2;
}

message UpdateSurveyDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  // contains details of the old as well as the newly updated values of a feedback survey
  FeedbackSurveyFrontend feedback_survey = 2;
}

message UpdateSurveyDetailsResponse {
  rpc.Status status = 1;
  // contains updated details of the feedback survey
  FeedbackSurveyFrontend feedback_survey = 2;
}

message GetAllFeedbackQuestionForSurveyRequest {
  .jarvis.Header jarvis_header = 1;
  // survey id for which the feedback questions are needed
  string survey_id = 2;
}

message GetAllFeedbackQuestionForSurveyResponse {
  rpc.Status status = 1;
  repeated ViewFeedbackQuestionsFormDetails feedback_question_list = 2;
}

message GetFeedbackQuestionDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  string question_id = 2;
}

message GetFeedbackQuestionDetailsResponse {
  rpc.Status status = 1;
  FeedbackQuestionFrontend feedback_question = 2;
}

message UpdateFeedbackQuestionDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  // contains the details of the old fields as well as the updated fields
  FeedbackQuestionFrontend feedback_question = 2;
}

message UpdateFeedbackQuestionDetailsResponse {
  rpc.Status status = 1;
  FeedbackQuestionFrontend feedback_question = 2;
}

message GetFeedbackEntitiesByApprovalStatusRequest {
  .jarvis.Header jarvis_header = 1;
  FeedbackEntityApprovalStatus approval_status = 2;
}

message GetFeedbackEntitiesByApprovalStatusResponse {
  rpc.Status status = 1;
  pkg.web.Table feedback_entities = 2;
}

message GetFeedbackEntityApprovalFormDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  string feedback_entity_approval_id = 2;
}

message GetFeedbackEntityApprovalFormDetailsResponse {
  rpc.Status status = 1;
  ApproveFeedbackEntityFormDetails approve_feedback_entity_form_details = 2;
}

message ViewFeedbackEntityApprovalFormDetailsRequest {
  .jarvis.Header jarvis_header = 1;
  string feedback_entity_approval_id = 2;
}

message ViewFeedbackEntityApprovalFormDetailsResponse {
  rpc.Status status = 1;
  UpdateApproveFeedbackEntityFormDetails update_approval_feedback_entity_form_details = 2;
  pkg.web.JsonDiffView json_diff_view = 3;
}

message UpdateApprovalStatusForFeedbackEntityRequest {
  .jarvis.Header jarvis_header = 1;
  // approval id is defined as the id of the row of the feedback_entity_approvals table on which approval action is being taken
  string approval_id = 2;
  FeedbackEntityApprovalStatus approval_status = 3;
  string approval_note = 4;
}

message UpdateApprovalStatusForFeedbackEntityResponse {
  rpc.Status status = 1;
}

message GetAnswerTypesRequest {
  .jarvis.Header jarvis_header = 1;
}

message GetAnswerTypesResponse {
  rpc.Status status = 1;
  pkg.web.DropDownInput answer_types = 2;
}

message GetAllSurveysRequest {
  .jarvis.Header jarvis_header = 1;
}

message GetAllSurveysResponse {
  rpc.Status status = 1;
  pkg.web.Table feedback_surveys = 2;
}

message GetSurveysRunningOnAnalyticsScreenNameRequest {
  .jarvis.Header jarvis_header = 1;
  .frontend.analytics.AnalyticsScreenName analytics_screen_name = 2;
}

message GetSurveysRunningOnAnalyticsScreenNameResponse {
  rpc.Status status = 1;
  pkg.web.Table feedback_surveys = 2;
}

message SubmitFeedbackSurveyForApprovalRequest {
  .jarvis.Header jarvis_header = 1;
  FeedbackSurveyFrontend feedback_survey = 2;
}

message SubmitFeedbackSurveyForApprovalResponse {
  rpc.Status status = 1;
  string survey_id = 2;
}

message SubmitFeedbackQuestionForApprovalRequest {
  .jarvis.Header jarvis_header = 1;
  FeedbackQuestionFrontend feedback_question = 2;
}

message SubmitFeedbackQuestionForApprovalResponse {
  rpc.Status status = 1;
}

message ApproveFeedbackEntityRequest {
  .jarvis.Header jarvis_header = 1;
  // approval id is defined as the id of the row of the feedback_entity_approvals table on which approval action is being taken
  string approval_id = 2;
}

message ApproveFeedbackEntityResponse {
  rpc.Status status = 1;
}

message SwitchSurveyMappingStatusRequest {
  .jarvis.Header jarvis_header = 1;
  string survey_id = 2;
}

message SwitchSurveyMappingStatusResponse {
  rpc.Status status = 1;
}
