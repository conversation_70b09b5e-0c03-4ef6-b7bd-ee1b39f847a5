syntax = "proto3";

package inapphelp.feedback_engine;

option go_package = "github.com/epifi/gamma/api/inapphelp/feedback_engine";
option java_package = "com.github.epifi.gamma.api.inapphelp.feedback_engine";

// enum to identify type of feedback
enum FeedbackAnswerType {
  FEEDBACK_ANSWER_TYPE_UNSPECIFIED = 0;
  // feedback answer is in textual format
  FEEDBACK_ANSWER_TYPE_TEXT_BOX = 1;
  // feedback answer is a single/multi selection based checkbox
  FEEDBACK_ANSWER_TYPE_CHECK_BOX = 2;
  // feedback answer is a thumbs up/down
  FEEDBACK_ANSWER_TYPE_THUMBS_UP_THUMBS_DOWN = 3;
  // feedback answer is emoji selection over a range
  FEEDBACK_ANSWER_TYPE_TYPE_EMOJI = 4;
  // feedback answer is from a dropdown
  FEEDBACK_ANSWER_TYPE_DROP_DOWN = 5;
  // feedback answer is a value over a scale, for ex. a scale of stars
  FEEDBACK_ANSWER_TYPE_TYPE_SCALE = 6;
  // feedback answer is from a rating slider
  FEEDBACK_ANSWER_TYPE_RATING_SLIDER = 7;
  // feedback answer is from a radio button
  FEEDBACK_ANSWER_TYPE_RADIO_BUTTON = 8;
  // Feedback answer type where no input is required from the user.
  // Note: When this answer type is used, the content type is explicitly set to
  // FEEDBACK_CONTENT_TYPE_ENGAGEMENT to display contextual or promotional content
  // (e.g., bottom sheets with dynamic visuals or information).
  // This answer type will be only used in last question of the survey.
  FEEDBACK_ANSWER_TYPE_NO_ANSWER = 9;
}

// indicates the type of app feedback survey
enum FeedbackSurveyType {
  FEEDBACK_SURVEY_TYPE_UNSPECIFIED = 0;
  FEEDBACK_SURVEY_TYPE_NPS = 1;
  FEEDBACK_SURVEY_TYPE_APP_RATING = 2;
  FEEDBACK_SURVEY_TYPE_IN_APP = 3;
}

// Enum to specify the type of feedback content.
enum FeedbackContentType {
  FEEDBACK_CONTENT_TYPE_UNSPECIFIED = 0;
  // Default type for standard question-answer feedback.
  FEEDBACK_CONTENT_TYPE_QUESTION = 1;
  // Type for engagement content, such as contextual cards or promotional content.
  FEEDBACK_CONTENT_TYPE_ENGAGEMENT = 2;
}

// indicates the approval status of an app feedback survey
// auto approved enum is missing here as we are not expecting frontend to send auto approval enum yet
enum FeedbackSurveyApprovalStatus {
  FEEDBACK_SURVEY_APPROVAL_STATUS_UNSPECIFIED = 0;
  FEEDBACK_SURVEY_APPROVAL_STATUS_APPROVED = 1;
  FEEDBACK_SURVEY_APPROVAL_STATUS_DISAPPROVED = 2;
  FEEDBACK_SURVEY_APPROVAL_STATUS_PENDING = 3;
}

// indicates the attempt status for an app feedback survey
enum FeedbackSurveyAttemptStatus {
  FEEDBACK_SURVEY_ATTEMPT_STATUS_UNSPECIFIED = 0;
  FEEDBACK_SURVEY_ATTEMPT_STATUS_STARTED = 1;
  FEEDBACK_SURVEY_ATTEMPT_STATUS_PARTIALLY_COMPLETED = 2;
  FEEDBACK_SURVEY_ATTEMPT_STATUS_COMPLETED = 3;
}

// denotes the type of entity for which approval is required
// this enum is used to maintain state at client side in approval flow through url params
// also the enum is prettified and rendered to inform the approver
// about what type of entity is submitted for approval
enum FeedbackApprovalEntityType {
  FEEDBACK_APPROVAL_ENTITY_TYPE_UNSPECIFIED = 0;
  FEEDBACK_APPROVAL_ENTITY_TYPE_NEW_QUESTION_LIST = 1;
  FEEDBACK_APPROVAL_ENTITY_TYPE_NEW_SURVEY = 2;
  FEEDBACK_APPROVAL_ENTITY_TYPE_EXISTING_SURVEY = 3;
  FEEDBACK_APPROVAL_ENTITY_TYPE_EXISTING_QUESTION = 4;
}

// approval status of a feedback entity
// toggle the mapping of a survey i.e pause/resume would utilize auto approved enum
enum FeedbackEntityApprovalStatus {
  FEEDBACK_ENTITY_APPROVAL_STATUS_UNSPECIFIED = 0;
  FEEDBACK_ENTITY_APPROVAL_STATUS_APPROVED = 1;
  FEEDBACK_ENTITY_APPROVAL_STATUS_PENDING = 2;
  FEEDBACK_ENTITY_APPROVAL_STATUS_DISAPPROVED = 3;
  FEEDBACK_ENTITY_APPROVAL_STATUS_AUTO_APPROVED = 4;
}

// indicates field mask for feedback_surveys table
enum FeedbackSurveyFieldMask {
  FEEDBACK_SURVEY_FIELD_MASK_UNSPECIFIED = 0;
  FEEDBACK_SURVEY_FIELD_MASK_SURVEY_NAME = 1;
  FEEDBACK_SURVEY_FIELD_MASK_SURVEY_TYPE = 2;
  FEEDBACK_SURVEY_FIELD_MASK_FIRST_QUESTION_ID = 3;
  FEEDBACK_SURVEY_FIELD_MASK_SURVEY_METADATA = 4;
  FEEDBACK_SURVEY_FIELD_MASK_APPROVAL_STATUS = 5;
  FEEDBACK_SURVEY_FIELD_MASK_START_TIME = 6;
  FEEDBACK_SURVEY_FIELD_MASK_END_TIME = 7;
  FEEDBACK_SURVEY_FIELD_MASK_MAX_RESPONSES_ALLOWED = 8;
  FEEDBACK_SURVEY_FIELD_MASK_OWNER_SERVICE = 9;
  FEEDBACK_SURVEY_FIELD_MASK_APPROVED_BY = 10;
  FEEDBACK_SURVEY_FIELD_MASK_ROLLOUT_PERCENTAGE = 11;
  FEEDBACK_SURVEY_FIELD_MASK_PRIORITY = 12;
  FEEDBACK_SURVEY_FIELD_MASK_CREATED_BY = 13;
  FEEDBACK_SURVEY_FIELD_MASK_LAST_UPDATED_BY = 14;
  FEEDBACK_SURVEY_FIELD_MASK_IS_CUSTOM_EVALUATION_ENABLED = 15;
  FEEDBACK_SURVEY_FIELD_MASK_APPROVER_NOTE = 16;
}

// indicates field mask for feedback_questions table
enum FeedbackQuestionFieldMask {
  FEEDBACK_QUESTION_FIELD_MASK_UNSPECIFIED = 0;
  FEEDBACK_QUESTION_FIELD_MASK_ANSWER_TYPE = 1;
  FEEDBACK_QUESTION_FIELD_MASK_ANSWER_OPTIONS = 2;
  FEEDBACK_QUESTION_FIELD_MASK_NEXT_QUESTION_ID = 3;
  FEEDBACK_QUESTION_FIELD_MASK_QUESTION_CONTENT = 4;
}

// indicates field mask for feedback_survey_attempts table
enum FeedbackSurveyAttemptFieldMask {
  FEEDBACK_SURVEY_ATTEMPT_FIELD_MASK_UNSPECIFIED = 0;
  FEEDBACK_SURVEY_ATTEMPT_FIELD_MASK_ACTOR_ID = 1;
  FEEDBACK_SURVEY_ATTEMPT_FIELD_MASK_APP_PLATFORM = 2;
  FEEDBACK_SURVEY_ATTEMPT_FIELD_MASK_APP_VERSION = 3;
  FEEDBACK_SURVEY_ATTEMPT_FIELD_MASK_FLOW_ID = 4;
  FEEDBACK_SURVEY_ATTEMPT_FIELD_MASK_SURVEY_ID = 5;
  FEEDBACK_SURVEY_ATTEMPT_FIELD_MASK_ATTEMPT_STATUS = 6;
  FEEDBACK_SURVEY_ATTEMPT_FIELD_MASK_ATTEMPT_METADATA = 7;
}

// indicates field mask for feedback_question_responses table
enum FeedbackQuestionResponseFieldMask {
  FEEDBACK_QUESTION_RESPONSE_FIELD_MASK_UNSPECIFIED = 0;
  FEEDBACK_QUESTION_RESPONSE_FIELD_MASK_ATTEMPT_ID = 1;
  FEEDBACK_QUESTION_RESPONSE_FIELD_MASK_QUESTION_ID = 2;
  FEEDBACK_QUESTION_RESPONSE_FIELD_MASK_USER_RESPONSE = 3;
}

// indicates field mask for feedback_survey_cool_offs table
enum FeedbackSurveyCoolOffsFieldMask {
  FEEDBACK_SURVEY_COOL_OFFS_FIELD_MASK_UNSPECIFIED = 0;
  FEEDBACK_SURVEY_COOL_OFFS_FIELD_MASK_PREVIOUS_SURVEY_ID = 1;
  FEEDBACK_SURVEY_COOL_OFFS_FIELD_MASK_CURRENT_SURVEY_ID = 2;
  FEEDBACK_SURVEY_COOL_OFFS_FIELD_MASK_COOL_OFF_DURATION = 3;
}

// indicates field mask for feedback_survey_mappings table
enum FeedbackSurveyMappingsFieldMask {
  FEEDBACK_SURVEY_MAPPINGS_FIELD_MASK_UNSPECIFIED = 0;
  FEEDBACK_SURVEY_MAPPINGS_FIELD_MASK_ANALYTICS_SCREEN_NAME = 1;
  FEEDBACK_SURVEY_MAPPINGS_FIELD_MASK_FLOW_ID = 2;
  FEEDBACK_SURVEY_MAPPINGS_FIELD_MASK_SURVEY_ID = 3;
  FEEDBACK_SURVEY_MAPPINGS_FIELD_MASK_IS_SURVEY_MAPPING_ENABLED = 4;
}

enum FeedbackEntityApprovalFieldMask {
  FEEDBACK_ENTITY_APPROVAL_FIELD_MASK_UNSPECIFIED = 0;
  FEEDBACK_ENTITY_APPROVAL_FIELD_MASK_ENTITY_ID = 1;
  FEEDBACK_ENTITY_APPROVAL_FIELD_MASK_ENTITY_TYPE = 2;
  FEEDBACK_ENTITY_APPROVAL_FIELD_MASK_CURRENT_ENTITY_STATE = 3;
  FEEDBACK_ENTITY_APPROVAL_FIELD_MASK_NEXT_ENTITY_STATE = 4;
  FEEDBACK_ENTITY_APPROVAL_FIELD_MASK_APPROVER = 5;
  FEEDBACK_ENTITY_APPROVAL_FIELD_MASK_REQUESTER = 6;
  FEEDBACK_ENTITY_APPROVAL_FIELD_MASK_APPROVAL_STATUS = 7;
  FEEDBACK_ENTITY_APPROVAL_FIELD_MASK_APPROVER_NOTE = 8;
}
