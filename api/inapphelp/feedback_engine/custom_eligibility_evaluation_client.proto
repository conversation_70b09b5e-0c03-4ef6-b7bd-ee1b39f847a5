syntax = "proto3";

package inapphelp.feedback_engine;

import "api/frontend/analytics/analytics_screen_name.proto";
import "api/inapphelp/feedback_engine/enums.proto";
import "api/inapphelp/feedback_engine/message.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/inapphelp/feedback_engine";
option java_package = "com.github.epifi.gamma.api.inapphelp.feedback_engine";

// Request for IsActorEligibleForSurvey method to be implemented by a client
message IsActorEligibleForSurveyRequest {
  string survey_id = 1;
  FlowIdentifier flow_id = 2;
  frontend.analytics.AnalyticsScreenName analytics_screen_name = 3;
  FeedbackSurveyType survey_type = 4;
  string actor_id = 5;
  // flow id meta is some flow specific information in string format passed back by the client
  // while calling GetFirstFeedbackQuestion RPC
  // For example : Ticket id of which the CSAT was responded by the user
  string flow_id_meta = 6;
}

// Response for IsActorEligibleForSurvey method to be implemented by a client
message IsActorEligibleForSurveyResponse {
  rpc.Status status = 1;
  bool is_actor_eligible_for_survey = 2;
}
