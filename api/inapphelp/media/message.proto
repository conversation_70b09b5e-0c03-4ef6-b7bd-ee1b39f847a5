syntax = "proto3";
package inapphelp.media;

import "api/inapphelp/media/enum.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/inapphelp_media_uicontext.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/inapphelp/media";
option java_package = "com.github.epifi.gamma.api.inapphelp.media";

// story proto msg to be used at service layer
// msg defines attributes for story content
message Story {
  // story title, to be displayed on story header
  string title = 1;
  // optional story description, to store what story is about
  string description = 2;
  // defines actual location/URL where content resides on
  string url = 3;
  // icon URL: to be surfaced on story header
  string icon = 4;
  // thumbnail color
  string thumbnail_color = 5;
  // e.g: "2 min"
  string duration = 6;
  // based on the type of story, we will decide the ui to be rendered on client
  // if not set, it'll be defaulted to STORY_TYPE_V1
  StoryType story_type = 7;
}

enum StoryType {
  STORY_TYPE_UNSPECIFIED = 0;
  // old story type
  // example usage in investment landing screen, mutual fund...
  // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?node-id=862%3A7378&t=Bx2Q1rkpx8wj4Fbs-4
  STORY_TYPE_V1 = 1;
  // new story type
  // example usage in us stocks landing screen
  // https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3584%3A17332&t=tmgxUJZlWg2uFU83-4
  STORY_TYPE_V2 = 2;
}

// proto msg for MediaPayload
// determines what type of payload is associated with MediaContent record
// there cannot be more than one payload attached/mapped with one MediaContent record
// this is done to make media information generic so no new tables are required for every content type
// this is to be stored as JSON blob in database table
message MediaPayload {
  // at most one payload selection
  oneof payload {
    // story
    Story story = 1;
  }
}

// media content proto msg to be used at service layer
// defines general attributes of media content as an entity
message MediaContent {
  // id of the db row, acts as primary key in the DB
  string id = 1;
  // defines what type of content is being returned: story, video, etc
  ContentType media_content_type = 2;
  // controls visibility on which platform content should be displayed on
  ContentVisibility media_content_visibility = 3;
  // defines in-depth attributes of content attached to the record
  // payload can be story, video, audio, etc
  // currently only story is being considered
  // gives content msg which has title, url, icon, etc
  // this has to be used by client to actually render information on platform
  MediaPayload media_payload = 4;
  // created at timestamp for the row
  google.protobuf.Timestamp created_at = 5;
  // updated at timestamp for the row
  google.protobuf.Timestamp updated_at = 6;
}

// proto msg for MediaPlaylist
// one MediaPlaylist can have more than one MediaContent
message MediaPlaylist {
  // id of the db row, acts as primary key in the DB
  string id = 1;
  // title of playlist
  string title = 2;
  // optional playlist description
  string description = 3;
  // playlist icon: to be used in future use cases
  string icon = 4;
  // playlist thumbnail: to be used in future use cases
  string thumbnail = 5;
  // controls visibility of playlist
  ContentVisibility playlist_content_visibility = 6;
  // created at timestamp for the row
  google.protobuf.Timestamp created_at = 7;
  // updated at timestamp for the row
  google.protobuf.Timestamp updated_at = 8;
}

// proto msg for Playlist to Media Content mapping
message MediaPlaylistToMediaContentMapping {
  // db record id, acts as primary key
  string id = 1;
  // db record id from media_playlist table
  string media_playlist_id = 2;
  // media_content id from media_content table
  // which has to be mapped to media_playlist id
  string media_content_id = 3;
  // specifies individual media content rank at playlist level
  // use this to sort / calculate in backend to figure out relevance / priority of the media content
  int64 rank = 4;
  // created at timestamp for the row
  google.protobuf.Timestamp created_at = 5;
  // updated at timestamp for the row
  google.protobuf.Timestamp updated_at = 6;
}

// proto msg for UIContext to Media Playlist
message UIContextToMediaPlaylistMapping {
  // db record id, acts as primary key
  string id = 1;
  // identifies granular section of the app where playlist has to be attached
  UIContext ui_context = 2;
  // db record id from media_playlist table
  string media_playlist_id = 3;
  // specifies individual playlist rank at UIContext level
  int64 rank = 4;
  // created at timestamp for the row
  google.protobuf.Timestamp created_at = 5;
  // updated at timestamp for the row
  google.protobuf.Timestamp updated_at = 6;
  // additional info associated with UIContext
  api.typesv2.InapphelpMediaUIContextMeta ui_context_meta = 7;
  // hash value of ui_context_meta
  string ui_context_meta_hash = 8;
  // specifies whether segmentation is enabled for the respective mapping
  api.typesv2.common.BooleanEnum is_segmentation_enabled = 9;
}

// message to store individual rank for each content id
// to be used to associate content ranking in playlist to content rank
message ContentRankMap {
  // media content id
  string media_content_id = 1;
  // rank
  int64 rank = 2;
}

// message to store individual rank for each playlist id
// to be used to associate playlist ranking in UIContext to playlist rank
message PlaylistRankMap {
  // media playlist id
  string media_playlist_id = 1;
  //  rank
  int64 rank = 2;
  // specifies whether segmentation is applicable for playlist
  repeated string segment_id_list = 3;
}
