syntax = "proto3";

package vendors.qwikcilver;

option go_package = "github.com/epifi/gamma/api/vendors/qwikcilver";
option java_package = "com.github.epifi.gamma.api.vendors.qwikcilver";

import "api/vendors/qwikcilver/image.proto";

message GetCategoryRequest {
  // GetCategoryRequest api does not requires request body, only accepts query params
}

message GetCategoryResponse {
  // category id
  string id = 1 [json_name="id"];
  // category name
  string name = 2 [json_name="name"];

  Images images = 3 [json_name="images"];

  message SubCategory {
    // sub category id
    string id = 1 [json_name="id"];
    // sub category name
    string name = 2 [json_name="name"];
    // images
    Images images = 3 [json_name="images"];
  }
  repeated SubCategory subcategories = 4 [json_name="subcategories"];

  // following fields get populated in case of failure
  // denotes error code
  int32 code = 9 [json_name="code"];
  // denotes message in case of error
  string message = 10 [json_name="message"];
}
