syntax = "proto3";

package vendors.qwikcilver;

option go_package = "github.com/epifi/gamma/api/vendors/qwikcilver";
option java_package = "com.github.epifi.gamma.api.vendors.qwikcilver";


message AuthorizationCodeRequest {
  string client_id = 1 [json_name="clientId"];
  string username = 2 [json_name="username"];
  string password = 3 [json_name="password"];
}

message AuthorizationCodeResponse {
  string authorization_code = 1 [json_name="authorizationCode"];

  // following fields get populated in case of failure
  // denotes error code
  int32 code = 9 [json_name="code"];
  // denotes message in case of error
  string message = 10 [json_name="message"];
}


message BearerTokenRequest {
  string client_id = 1 [json_name="clientId"];
  string client_secret = 2 [json_name="clientSecret"];
  string authorization_code = 3 [json_name="authorizationCode"];
}

message BearerTokenResponse {
  string bearer_token = 1 [json_name="token"];

  // following fields get populated in case of failure
  // denotes error code
  int32 code = 9 [json_name="code"];
  // denotes message in case of error
  string message = 10 [json_name="message"];
}
