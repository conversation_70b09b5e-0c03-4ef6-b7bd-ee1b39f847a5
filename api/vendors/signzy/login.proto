syntax = "proto3";

package vendors.signzy;

option go_package = "github.com/epifi/gamma/api/vendors/signzy";
option java_package = "com.github.epifi.gamma.api.vendors.signzy";

message LoginRequest {
  string username = 1 [json_name = "username"];
  string password = 2 [json_name = "password"];
}

message LoginResponse {
  string access_token = 1 [json_name = "id"];
  // ttl is the time till the token is valid
  int64 ttl = 2 [json_name = "ttl"];
  // created_at is the time at which token was created
  string created_at = 3 [json_name = "created"];
  string user_id = 4 [json_name = "userId"];
}
