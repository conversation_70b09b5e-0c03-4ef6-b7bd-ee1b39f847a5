syntax = "proto3";

package vendors.signzy;

option go_package = "github.com/epifi/gamma/api/vendors/signzy";
option java_package = "com.github.epifi.gamma.api.vendors.signzy";

message DomainVerificationRequest {
  message Essentials {
    // domain url for which we need details for
    string web_domain = 1 [json_name = "webDomain"];
  }
  Essentials essentials = 16 [json_name = "essentials"];
}

message DomainVerificationResponse {
  message Result {
    // Name of company of the entered domain
    string name = 1 [json_name = "name"];
    // Legal name of company
    string legal_name = 2 [json_name = "legalName"];
    // Domain of company’s website
    string domain = 3 [json_name = "domain"];
    // List of domains also used by the company
    repeated string domain_aliases = 4 [json_name = "domainAliases"];

    message Geo {
      // Headquarters country name
      string country = 1 [json_name = "country"];
      // Headquarters two character country code
      string country_code = 2 [json_name = "countryCode"];
    }
    Geo geo = 5 [json_name = "geo"];

    message Facebook {
      // Company’s Facebook ID
      string handle = 1 [json_name = "handle"];
    }
    Facebook facebook = 6 [json_name = "facebook"];

    message Linkedin {
      // Company’s Linkedin URL
      string handle = 1 [json_name = "handle"];
    }
    Linkedin linkedin = 7 [json_name = "linkedin"];

    message Twitter {
      // Twitter screen name
      string handle = 1 [json_name = "handle"];
    }
    Twitter twitter = 8 [json_name = "twitter"];

    // It is the domain associated with a free email provider (i.e. Gmail)?
    string email_provider = 9 [json_name = "emailProvider"];

    message Metrics {
      // Alexa’s global site rank
      string alexa_global_rank = 3 [json_name = "alexaGlobalRank"];
      // Amount of employees
      string employees = 4 [json_name = "employees"];
    }
    Metrics metrics = 10 [json_name = "metrics"];

    // Similar domains list
    repeated string similar_domains = 11 [json_name = "similarDomains"];

    message Parent {
      // The domain of the parent company (if any)
      string domain = 1 [json_name = "domain"];
    }
    Parent parent = 12 [json_name = "parent"];
  }
  Result result = 1 [json_name = "result"];
  string request_id = 2 [json_name = "id"];
}
