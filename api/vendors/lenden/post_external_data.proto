syntax = "proto3";

package vendors.lenden;

import "api/vendors/lenden/common.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendors/lenden";
option java_package = "com.github.epifi.gamma.api.vendors.lenden";

message PostExternalDataRequestPayload {
  string product_id = 1 [json_name = "product_id"];
  string loan_id = 2 [json_name = "loan_id"];
  string user_id = 3 [json_name = "user_id"];
  // Data of the user's AA account needed by LDC for checking eligibility
  google.protobuf.Struct data = 4 [json_name = "data"];
  BankDetails bank_details = 5 [json_name = "bank_details"];
  string type = 6 [json_name = "type"];
}

message BankDetails {
  string holder_name = 1 [json_name = "holder_name"];
  string account_number = 2 [json_name = "account_number"];
  string ifsc = 3 [json_name = "ifsc"];
  string type = 4 [json_name = "type"];
}

message PostExternalDataRequest {
  Params params = 1 [json_name = "params"];
  Fields fields = 2 [json_name = "fields"];
  PostExternalDataRequestPayload json = 3 [json_name = "json"];
  Attributes attributes = 4 [json_name = "attributes"];
  string api_code = 5 [json_name = "api_code"];
}

message PostExternalDataResponseWrapper {
  string message = 1 [json_name = "message"];
  ResponseData response = 2 [json_name = "response"];
  message ResponseData {
    string trace_id = 1 [json_name = "trace_id"];
    string message_code = 2 [json_name = "message_code"];
    string message = 3 [json_name = "message"];
    PostExternalDataResponse response_data = 4 [json_name = "response_data"];
  }
  message PostExternalDataResponse {
    string status = 1 [json_name = "status"];
  }
}
