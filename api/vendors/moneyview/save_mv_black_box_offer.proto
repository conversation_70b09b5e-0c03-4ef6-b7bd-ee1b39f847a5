syntax = "proto3";

package vendors.moneyview;

option go_package = "github.com/epifi/gamma/api/vendors/moneyview";
option java_package = "com.github.epifi.gamma.api.vendors.moneyview";

message SaveBlackBoxOfferVendorRequest {
  string lead_user_ref = 1 [json_name = "leadUserRef"];
  string status = 2;
  double best_offer_amount = 3 [json_name = "bestOfferAmount"];
  int32 best_offer_tenure = 4 [json_name = "bestOfferTenure"];
  double best_offer_roi = 5 [json_name = "bestOfferRoi"];
  string expiry_date = 6 [json_name = "expiryDate"];
  string category = 7 [json_name = "category"];
  string lead_source = 8 [json_name = "leadSource"];
  string meta_income = 9 [json_name = "metaIncome"];
  string meta_tag_name = 10 [json_name = "metaTagName"];
  string employment_type = 11 [json_name = "employmentType"];
}

message SaveBlackBoxOfferVendorResponse {
  string status = 1 [json_name = "status"];
  string message = 2 [json_name = "message"];
  string code = 3 [json_name = "code"];
}
