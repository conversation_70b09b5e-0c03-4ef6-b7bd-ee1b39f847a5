syntax = "proto3";

package vendors.moneyview;

option go_package = "github.com/epifi/gamma/api/vendors/moneyview";
option java_package = "com.github.epifi.gamma.api.vendors.moneyview";

message Address {
  string address_type = 1 [json_name = "addressType"];
  string address_line_1 = 2 [json_name = "addressLine1"];
  string address_line_2 = 3 [json_name = "addressLine2"];
  string city = 4 [json_name = "city"];
  string state = 5 [json_name = "state"];
  string pincode = 6 [json_name = "pincode"];
}