syntax = "proto3";

package vendors.moneyview;

option go_package = "github.com/epifi/gamma/api/vendors/moneyview";
option java_package = "com.github.epifi.gamma.api.vendors.moneyview";

message GetJourneyUrlRequest {
}

message GetJourneyUrlResponse {
  string pwa_url = 1 [json_name = "pwa"];
  int32 ttl = 2 [json_name = "ttl"];
  string status = 3 [json_name = "status"];
  string message = 4 [json_name = "message"];
  string code = 5 [json_name = "code"];
}