syntax = "proto3";

package vendors.moneyview;

option go_package = "github.com/epifi/gamma/api/vendors/moneyview";
option java_package = "com.github.epifi.gamma.api.vendors.moneyview";

message GenerateMvBlackBoxOfferVendorRequest {
  string leadUserRef = 1 [json_name = "leadUserRef"];
  string leadSource = 2 [json_name = "leadSource"];
  string dataFormat = 3 [json_name = "dataFormat"];
  string pincode = 4;
  string employmentType = 5 [json_name = "employmentType"];
  double declaredIncome = 6 [json_name = "declaredIncome"];
  string dob = 7;
  string bureauProvider = 8 [json_name = "bureauProvider"];
  PkgInfoData pkgInfoData = 9 [json_name = "pkgInfoData"];
  SmsData smsData = 10 [json_name = "smsData"];
  string bureauDataStr = 11 [json_name = "bureauDataStr"];
}


message PkgInfoData {
  repeated PkgInfo pkgInfoDTOList = 1 [json_name = "pkgInfoDTOList"];
}

message PkgInfo {
  string pkgName = 1 [json_name = "pkgName"];
  string userId = 2 [json_name = "userId"];
  int64 dateCreated = 3 [json_name = "dateCreated"];
}

message SmsData {
  repeated Sms smsList = 1 [json_name = "smsList"];
}


message Sms {

  string msg = 1 [json_name = "msg"];
  string address = 2 [json_name = "address"];
  int64 date = 3 [json_name = "date"];
}

message GenerateMvBlackBoxOfferVendorResponse {
  VendorResponseBody body = 1 [json_name = "body"];
  string message = 2 [json_name = "message"];
  bool success = 3 [json_name = "success"];
}

message VendorResponseBody {
  string leadUserRef = 1 [json_name = "leadUserRef"];
  string status = 2 [json_name = "status"];

  double bestOfferAmount = 3 [json_name = "bestOfferAmount"];
  int32 bestOfferTenure = 4 [json_name = "bestOfferTenure"];
  double bestOfferRoi = 5 [json_name = "bestOfferRoi"];

  string expiryDate = 6 [json_name = "expiryDate"];
  string category = 7 [json_name = "category"];
  string leadSource = 8 [json_name = "leadSource"];
  string metaIncome = 9 [json_name = "metaIncome"];
  string metaTagName = 10 [json_name = "metaTagName"];
  string employmentType = 11 [json_name = "employmentType"];
  string summaryVariables = 12 [json_name = "summaryVariables"];
}
