syntax = "proto3";

package vendors.karza;

option go_package = "github.com/epifi/gamma/api/vendors/karza";
option java_package = "com.github.epifi.gamma.api.vendors.karza";

message ValidateVoterIdRequest {
  string voter_id = 1 [json_name= "epic_no"];
  string consent = 2 [json_name= "consent"];
}

message ValidateVoterIdResponse{
  int64 status_code = 1 [json_name = "status-code"];
  string request_id = 2 [json_name = "request_id"];
  ValidateVoterIdResult result = 3 [json_name = "result"];
}

message ValidateVoterIdResult {
  string voter_id = 1 [json_name="epic_no"];
}
