syntax = "proto3";

package vendors.karza;


option go_package = "github.com/epifi/gamma/api/vendors/karza";
option java_package = "com.github.epifi.gamma.api.vendors.karza";

//
//VKYC Generate Org level Session token
//
//Request
message GenerateSessionTokenRequest {
  repeated string product_id = 1 [json_name = "productId"];
}

//Response
message GenerateSessionTokenResponseData {
  string karza_token = 1 [json_name = "karzaToken"];
}

message GenerateSessionTokenResponseResult {
  string reason = 1 [json_name = "reason"];
  GenerateSessionTokenResponseData data = 2 [json_name = "data"];
  bool success = 3 [json_name = "success"];
}

message GenerateSessionTokenResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  GenerateSessionTokenResponseResult result = 3 [json_name = "result"];
  int64 status = 4 [json_name = "status"];
  string error = 5 [json_name = "error"];
  string status_message = 6 [json_name = "statusMessage"];
}

//
//VKYC Add a New customer Request Message
//
//Request
message AddNewCustomerapplicantFormData {
  string application_type = 1 [json_name = "applicationType"];
  string applicant_type = 2 [json_name = "applicantType"];
  string application_id = 3 [json_name = "applicationId"];
  string phone = 4 [json_name = "phone"];
  string first_name = 5 [json_name = "firstName"];
  string father_name = 6 [json_name = "fatherName"];
  string dob = 7 [json_name = "dob"];
  string email = 8 [json_name = "email"];
  string gender = 9 [json_name = "gender"];
  string current_address = 10 [json_name = "currentAddress"];
  string permanent_address = 11 [json_name = "permanentAddress"];
  string middle_name = 12 [json_name = "middleName"];
  string last_name = 13 [json_name = "lastName"];
  string photo = 14 [json_name = "photo"];
  IncomeDetails income_details = 15 [json_name = "incomeDetails"];
}

message IncomeDetails{
  string occupation = 1 [json_name = "occupation"];
  string monthly_income = 2 [json_name = "monthlyIncome"];
  string type = 3 [json_name = "type"];
  string annual_income = 4 [json_name = "annualIncome"];
  string organization = 5 [json_name = "organization"];
  uint32 employee_strength = 6 [json_name = "employeeStrength"];
}

message AddNewCustomerpanData {
  string name = 1 [json_name = "name"];
  string father_name = 2 [json_name = "fatherName"];
  string dob = 3 [json_name = "dob"];
  string pan_no = 4 [json_name = "panNo"];
}

message AddNewCustomerAadhaarData {
  string image_base64 = 1 [json_name = "imageBase64"];
  string name = 2 [json_name = "name"];
  string address = 3 [json_name = "address"];
  string dob = 4 [json_name = "dob"];
  string gender = 5 [json_name = "gender"];
  string generation_date = 6 [json_name = "genDate"];
  string phone = 7 [json_name = "phone"];
  string email = 8 [json_name = "email"];
  string phone_hash = 9 [json_name = "phoneHash"];
  string email_hash = 10 [json_name = "emailHash"];
  string share_code = 11  [json_name = "shareCode"];
  string mask_aadhaar = 12  [json_name = "maskAadhaar"];
  string type = 13 [json_name = "type"];
}

message AddNewCustomerRequest {
  string customer_id = 1 [json_name = "customerId"];
  string consent = 2 [json_name = "consent"];
  AddNewCustomerapplicantFormData applicant_form_data = 3 [json_name = "applicantFormData"];
  AddNewCustomerpanData pan_data = 4 [json_name = "panData"];
  AddNewCustomerAadhaarData aadhaarData = 5 [json_name = "aadhaar"];
  bool auto_schedule = 6 [json_name = "autoSchedule"];
}
//Response

message AddNewCustomerResponseData {
  string transaction_id = 1 [json_name = "transactionId"];
  string customerId = 2 [json_name = "customerId"];
}

message AddNewCustomerResponseResult {
  string reason = 1 [json_name = "reason"];
  AddNewCustomerResponseData data = 2 [json_name = "data"];
  bool success = 3 [json_name = "success"];
}

message AddNewCustomerResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  AddNewCustomerResponseResult result = 3 [json_name = "result"];
  int64 status = 4 [json_name = "status"];
  string error = 5 [json_name = "error"];
}

//
//VKYC Generate Customer Session Token
//
//Request


//Response

message GenerateCustomerTokenResponseData {
  string user_token = 1 [json_name = "userToken"];
  string transaction_id = 2 [json_name = "transactionId"];
}

message GenerateCustomerTokenResponseResult {
  string reason = 1 [json_name = "reason"];
  GenerateCustomerTokenResponseData data = 2 [json_name = "data"];
  bool success = 3 [json_name = "success"];
}

message GenerateCustomerTokenResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  GenerateCustomerTokenResponseResult result = 3 [json_name = "result"];
  int64 status = 4 [json_name = "status"];
  string error = 5 [json_name = "error"];
  string message = 6 [json_name = "message"];
}

message GenerateCustomerTokenRequest {
  string transaction_id = 1 [json_name = "transactionId"];
}



//
//VKYC Get Available Slots for a customer Request
//
//Request
message GetSlotRequest {
  string start_time = 1 [json_name = "startTime"];
  string end_time = 2 [json_name = "endTime"];
}

//Response
message GetSlotResponseData {
  string slot_start = 1 [json_name = "slotStart"];
  bool is_slot_available = 2 [json_name = "isSlotAvailable"];
  string slot_end = 3 [json_name = "slotEnd"];
  string slot_id = 4 [json_name = "slotId"];
}


message GetSlotResponseResult {
  string reason = 1 [json_name = "reason"];
  repeated GetSlotResponseData data = 2 [json_name = "data"];
  bool success = 3 [json_name = "success"];
}

message GetSlotResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  GetSlotResponseResult result = 3 [json_name = "result"];
  int64 status = 4 [json_name = "status"];
  string error = 5 [json_name = "error"];
}


//
//VKYC Book Slot Request
//
//Request
message BookSlotRequest {
  string slot_id = 1 [json_name = "slotId"];
}
//Response

message BookSlotResponseData {
  bool is_booked = 1 [json_name = "isBooked"];
}

message BookSlotResponseResult {
  string reason = 1 [json_name = "reason"];
  BookSlotResponseData data = 2 [json_name = "data"];
  bool success = 3 [json_name = "success"];
}

message BookSlotResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  BookSlotResponseResult result = 3 [json_name = "result"];
  int64 status = 4 [json_name = "status"];
  string error = 5 [json_name = "error"];
}

// VKYC Reschedule slot request and response
message ReScheduleSlotRequest {
  string slot_id = 1 [json_name = "slotId"];
  // Optional parameter
  string agent_id = 2 [json_name = "agentId"];
}

message ReScheduleSlotResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  ReScheduleSlotResponseResult result = 3 [json_name = "result"];
  int64 status = 4 [json_name = "status"];
  string error = 5 [json_name = "error"];
}

message ReScheduleSlotResponseData {
  bool is_booked = 1 [json_name = "isBooked"];
}

message ReScheduleSlotResponseResult {
  string reason = 1 [json_name = "reason"];
  ReScheduleSlotResponseData data = 2 [json_name = "data"];
  bool success = 3 [json_name = "success"];
}

//
//VKYC Get Slot Agent Request
//
//Request
message GetAgentsInSlotRequest {
  string slot_id = 1 [json_name = "slotId"];
  bool get_agents = 2 [json_name = "getAgents"];
}
//Response
//Response

message GetAgentsInSlotResponseData {
  string agent_image = 1 [json_name = "agentImage"];
  string agent_id = 2 [json_name = "agentId"];
  string agent_name = 3 [json_name = "agentName"];
}

message GetAgentsInSlotResponseResult {
  string reason = 1 [json_name = "reason"];
  repeated GetAgentsInSlotResponseData data = 2 [json_name = "data"];
  bool success = 3 [json_name = "success"];
}

message GetAgentsInSlotResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  GetAgentsInSlotResponseResult result = 3 [json_name = "result"];
  int64 status = 4 [json_name = "status"];
  string error = 5 [json_name = "error"];
}

//
//Generate Customer WebLink Request
//
//Request
//message GenerateCustomerWeblinkRequest {
//
//}

//Response
message GenerateCustomerWeblinkRequest {
  string customer_token = 2;
}

message GenerateCustomerWeblinkResponseData {
  int64 link_expiry_timestamp = 1 [json_name = "linkExpiryTimestamp"];
  string transaction_id = 2 [json_name = "transactionId"];
  string web_link = 3 [json_name = "webLink"];
}

message GenerateCustomerWeblinkResponseResult {
  string reason = 1 [json_name = "reason"];
  GenerateCustomerWeblinkResponseData data = 2 [json_name = "data"];
  bool success = 3 [json_name = "success"];
}

message GenerateCustomerWeblinkResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  GenerateCustomerWeblinkResponseResult result = 3 [json_name = "result"];
  int64 status = 4 [json_name = "status"];
  string error = 5 [json_name = "error"];
}

message TransactionStatusEnquiryRequest  {
  string transaction_id = 1;
  string karza_token = 2;
}

message TransactionStatusEnquiryResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  message Result {
    message Data {
      string request_id = 1 [json_name = "requestId"];
      string event = 2 [json_name = "event"];
      message Data {
        string application_id = 1 [json_name = "applicationId"];
        string customer_id = 2 [json_name = "customerId"];
        string agent_user_name = 3 [json_name = "agentUserName"];
        string timestamp = 4 [json_name = "timeStamp"];
        string customer_arrival_time = 5 [json_name = "customerArrivalTime"];
        string call_start_time = 6 [json_name = "callStartTime"];
        string agent_id = 7 [json_name = "agentId"];
        bool call_success = 8 [json_name = "callSuccess"];
        string reason = 9 [json_name = "reason"];
        string scheduled_time = 10 [json_name = "scheduledTime"];
        string approved_by_agent = 11 [json_name = "agentStatus"];
        float call_duration = 12 [json_name = "callDuration"];
        string user_agent_string = 13 [json_name = "userAgentString"];
        string session_id = 14 [json_name = "sessionId"];
        int32 expected_wait_time = 15 [json_name = "expectedWaitTime"];
        string auditor_status = 16 [json_name = "auditorStatus"];
        string preferred_language = 17 [json_name = "customerLanguagePreference"];
        string last_stage_completed = 18 [json_name = "lastStageCompleted"];
        string call_connection_time = 19 [json_name = "callConnectionTime"];
        bool is_customer_blocked = 20 [json_name = "isCustomerBlocked"];
        string agent_employee_id = 21 [json_name = "agentEmployeeId"];
        string call_end_time = 22 [json_name = "callEndTime"];
        string auditor_remark = 23 [json_name = "auditorRemarks"];
        string agent_remark = 24 [json_name = "agentRemarks"];
        string auditor_failure_reason = 25 [json_name = "auditorFailureReason"];
      }
      Data data = 3 [json_name = "data"];
      string new_timestamp = 4 [json_name = "timeStamp"];
      bool terminal_state = 5[json_name = "terminalState"];
      string status = 6[json_name = "status"];
      string event_version = 7 [json_name = "eventVersion"];
      string event_type = 8 [json_name = "eventType"];
      string failure_reason = 9 [json_name = "failureReason"];
    }
    repeated Data data = 1 [json_name = "data"];
    string reason = 2 [json_name = "reason"];
    bool success = 3 [json_name = "success"];
  }
  Result result = 3 [json_name = "result"];
}

message TriggerCallbackRequest {
  string type = 1 [json_name = "type"];
  repeated string transaction_id = 2 [json_name = "transactionId"];
}

message TriggerCallbackResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  TriggerCallbackResponseResult result = 3 [json_name = "result"];
}

message TriggerCallbackResponseResult {
  string reason = 1 [json_name = "reason"];
  TriggerCallbackResponseResultData data = 2 [json_name = "data"];
  bool success = 3 [json_name = "success"];
}

// The vendor api being async, we do not not expect any data in this field as conveyed by vendor, for now
message TriggerCallbackResponseResultData {
}

message UpdateCustomerV3Request {
  string customer_id = 1 [json_name = "customerId"];
  ApplicationFormData application_form_data = 2 [json_name = "applicationFormData"];
  Docs docs = 3 [json_name = "docs"];
  CallAllocation call_allocation = 4 [json_name = "callAllocation"];
}

message Account {
  string branch = 1 [json_name = "branch"];
  string account_status = 2 [json_name = "accountStatus"];
  string account_number = 3 [json_name = "accountNumber"];
}

message ApplicationFormData {
  string application_id = 1 [json_name = "applicationId"];
  string application_type = 2 [json_name = "applicationType"];
  string applicant_type = 3 [json_name = "applicantType"];
  string phone = 4 [json_name = "phone"];
  string first_name = 5 [json_name = "firstName"];
  string middle_name = 6 [json_name = "middleName"];
  string last_name = 7 [json_name = "lastName"];
  string father_name = 8 [json_name = "fatherName"];
  string dob = 9 [json_name = "dob"];
  string email = 10 [json_name = "email"];
  string gender = 11 [json_name = "gender"];
  string current_address = 12 [json_name = "currentAddress"];
  string permanent_address = 13 [json_name = "permanentAddress"];
  bool is_current_and_permanent_address_same = 14 [json_name = "isCurrentAndPermanentAddressSame"];
  string productType = 15 [json_name = "productType"];
  string customerStatus = 16 [json_name = "customerStatus"];
  Account account = 17 [json_name = "account"];
  IncomeDetails incomeDetails = 18 [json_name = "incomeDetails"];
  UserLocation location = 19[json_name = "location"];
}

message UserLocation {
  float latitude = 1 [json_name = "latitude"];
  float longitude = 2 [json_name = "longitude"];
}

message FaceOnDocument {
  string file_base_64 = 1 [json_name = "fileBase64"];
  string file_type = 2 [json_name = "fileType"];
  string meta_data = 3 [json_name = "metaData"];
}

message Aadhaar {
  string name = 1 [json_name = "name"];
  string gen_date = 2 [json_name = "genDate"];
  string address = 3 [json_name = "address"];
  string dob = 4 [json_name = "dob"];
  string gender = 5 [json_name = "gender"];
  string source = 6 [json_name = "source"];
  string type = 7 [json_name = "type"];
  bool is_aadhaar_masked = 8 [json_name = "isAadhaarMasked"];
  bool digitally_signed = 9 [json_name = "digitallySigned"];
  bool verified = 10 [json_name = "verified"];
  string mobile_hash = 11 [json_name = "mobileHash"];
  string email_hash = 12 [json_name = "emailHash"];
  string share_code = 13 [json_name = "shareCode"];
  FaceOnDocument face_on_document = 14 [json_name = "faceOnDocument"];
  string document_category = 15 [json_name = "documentCategory"];
}

message Pan {
  string first_name = 1 [json_name = "firstName"];
  string middle_name = 2 [json_name = "middleName"];
  string last_name = 3 [json_name = "lastName"];
  string printed_name = 4 [json_name = "printedName"];
  string pan_no = 5 [json_name = "panNo"];
  string type = 6 [json_name = "type"];
  string source = 7 [json_name = "source"];
  bool verified = 8 [json_name = "verified"];
  string document_category = 9 [json_name = "documentCategory"];
  string generation_date = 10 [json_name = "genDate"];

  string name = 11 [json_name = "name"];
  string father_name = 12 [json_name = "fatherName"];
  string dob = 13 [json_name = "dob"];
  string mobile = 14[json_name = "mobile"];
  string email = 15 [json_name = "email"];
  string  gender = 16 [json_name = "gender"];
  string password = 17 [json_name = "password"];
  string consent_date = 18 [json_name = "consentDate"];
  bool digitally_signed = 19 [json_name = "digitallySigned"];
  Files face_on_document = 20 [json_name = "faceOnDocument"];
  repeated Files pan_files = 21 [json_name = "files"];
}

message Signature {
  bool verified = 1 [json_name = "verified"];
  string source = 2 [json_name = "source"];
  Files sign_on_document = 3 [json_name = "signOnDocument"];
}

message Files {
  string fileBase64 = 1 [json_name = "fileBase64"];
  string fileType = 2 [json_name = "fileType"];
  string metaData = 3 [json_name = "metaData"];
}

message ElectricityBill {
  string consumer_id = 1 [json_name = "consumerId"];
  string distribution_board = 2 [json_name = "distributionBoard"];
  string name = 3 [json_name = "name"];
  string address = 4 [json_name = "address"];
  string billDate = 5 [json_name = "billDate"];
  string mobile = 6 [json_name = "mobile"];
  string email = 7 [json_name = "email"];
  string document_category = 8 [json_name = "documentCategory"];
  bool verified = 9 [json_name = "verified"];
  repeated Files files = 10 [json_name = "Files"];
}

message IndividualDocs {
  Aadhaar aadhaar = 1 [json_name = "aadhaar"];
  Pan pan = 2 [json_name = "pan"];
  ElectricityBill electricity_bill = 3 [json_name = "electricityBill"];
  Signature signature = 4 [json_name = "signature"];
}

message Docs {
  IndividualDocs individual_docs = 1 [json_name = "individualDocs"];
}

message CallAllocation {
  string applicant_priority = 1 [json_name = "applicantPriority"];
  string agentId = 2 [json_name = "agentId"];
  repeated string preferred_languages = 3 [json_name = "preferredLanguage"];
}

message UpdateCustomerV3Response {
  int64 status_code = 1 [json_name = "statusCode"];
  string request_id = 2 [json_name = "requestId"];
  UpdateCustomerV3ResponseResult result = 3 [json_name = "result"];
}

message UpdateCustomerV3ResponseResult {
  string data = 1 [json_name = "data"];
  bool success = 2 [json_name = "success"];
  string reason = 3 [json_name = "reason"];
}

message AddNewCustomerV3Request {
  string customer_id = 1 [json_name = "customerId"];
  ApplicationFormData application_form_data = 2 [json_name = "applicationFormData"];
  Docs docs = 3 [json_name = "docs"];
  CallAllocation call_allocation = 4 [json_name = "callAllocation"];
}

message AddNewCustomerV3Response {
  int64 status_code = 1 [json_name = "statusCode"];
  string status = 2 [json_name = "status"];
  string error = 3 [json_name = "error"];
  string request_id = 4 [json_name = "requestId"];
  AddNewCustomerV3ResponseResult result = 5 [json_name = "result"];
}

message AddNewCustomerV3ResponseResult {
  AddNewCustomerV3Data data = 1 [json_name = "data"];
  bool success = 2 [json_name = "success"];
}

message AddNewCustomerV3Data {
  string transaction_id = 1 [json_name = "transactionId"];
}

message AgentDashboardResponse {
  int64 status_code = 1 [json_name = "statusCode"];
  message Result {
    message Data {
      message Stats {
        int32 total_agent = 1 [json_name = "totalAgents"];
        int32 idle_agent = 2 [json_name = "onlineAgents"];
        int32 busy_agent = 3 [json_name = "busyAgents"];
      }
      Stats stats = 1 [json_name = "stats"];
    }
    Data data = 1 [json_name = "data"];
    bool success = 2 [json_name = "success"];
    string reason = 3 [json_name = "reason"];
  }
  Result result = 2 [json_name = "result"];
  string request_id = 3 [json_name = "requestId"];
}

message AgentDashboardAuthRequest {
  string password = 1 [json_name = "password"];
  string username = 2 [json_name = "username"];
  string userType = 3 [json_name = "userType"];
  bool encryption = 4 [json_name = "encryption"];
}
