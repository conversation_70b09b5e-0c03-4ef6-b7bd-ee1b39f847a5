/*
 * Vendor Ref: https://docs.credgenics.com/?shell#lending-apis
 * The comments in this file are taken from the above link and might contain redundant information.
 */
syntax = "proto3";

package vendors.credgenics.types;

import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendors/credgenics/types";
option java_package = "com.github.epifi.gamma.api.vendors.credgenics.types";

// Notice contains information about the legal activities performed on the loan account.
// It includes legal notice and physical notice tracking.
// Example JSON 1:
//  {
//      "notice_id": 125792,
//      "case_type": "lrn",
//      "document_type": "Notice",
//      "s3_link": "https://s3-ap-south-1.amazonaws.com/credgenics-cases/production/notice_links/930b9ecfee7e.pdf",
//      "created": "2021-04-15 06:43:50",
//      "data": {}
//  }
// Example JSON 2:
//  {
//    "notice_id": 12543,
//      "case_type": "lrn",
//      "document_type": "Speedpost",
//      "created": "2021-05-15 18:44:15",
//      "data": {
//        "events": [
//          {
//            "date": "05/10/2020",
//            "time": "15:27:17",
//            "office": "Bhuj HO",
//            "description": "Item Delivery Confirmed"
//          },
//          .
//          .
//          {
//            "date": "29/09/2020",
//            "time": "16:17:42",
//            "office": "Rohini Sector7 SO",
//            "description": "Item Booked"
//          }
//        ],
//          "booked_at": "Rohini Sector7 SO",
//          "article_type": "Inland Speed Post",
//          "speedpost_id": "ED670494304IN",
//          "applicant_type": "applicant",
//          "speedpost_tarrif": "41.30",
//          "delivery_location": "Sonipat HO",
//          "speedpost_s3_link": "",
//          "speedpost_booked_on": "29/09/2020 16:17:42",
//          "applicant_address_type": "home",
//          "applicant_address_index": 0,
//          "speedpost_delivery_status": "Delivered",
//          "co_applicant_address_index": -1,
//          "speedpost_undelivered_reason": "",
//          "speedpost_destination_pincode": "131001",
//          "speedpost_delivery_confirmed_on": "05/10/2020 15:27:17"
//      }
//  }
message Notice {
  // e.g: 125792
  string notice_id = 1 [json_name = "notice_id"];
  // e.g: "lrn"
  string case_type = 2 [json_name = "case_type"];
  // e.g: "Notice", "Speedpost"
  string document_type = 3 [json_name = "document_type"];
  string s3_link = 4 [json_name = "s3_link"];
  // e.g: "2021-04-15 06:43:50"
  string created = 5 [json_name = "created"];
  // as the data is dynamic and we don't have clarity on the structure from the vendor, we use google.protobuf.Struct
  google.protobuf.Struct data = 6 [json_name = "data"];
}
