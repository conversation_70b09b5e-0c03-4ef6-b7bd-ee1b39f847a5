/*
 * Vendor Ref: https://docs.credgenics.com/?shell#lending-apis
 * The comments in this file are taken from the above link and might contain redundant information.
 */
syntax = "proto3";

package vendors.credgenics.types;

import "google/protobuf/wrappers.proto";

option go_package = "github.com/epifi/gamma/api/vendors/credgenics/types";
option java_package = "com.github.epifi.gamma.api.vendors.credgenics.types";

// LoanDetails contains the information provided at the time of loan insertion and payment related details.
// Example usages: Get Loan API response, Upload Loan API request, Update Loan API request, Upload Payment API request
message LoanDetails {
  // The loan_id of the loan provided by the lender
  // e.g: "283921"
  string loan_id = 1 [json_name = "loan_id"];
  // A unique ID provided by the client which uniquely maps to a borrower who may or may not have multiple loans with the client
  string client_customer_id = 2 [json_name = "client_customer_id"];
  // The type of loan based on the function it serves e.g. - Personal Loan, Business Loan, Vehicle Loan, etc.
  // e.g: "Personal Loan"
  string loan_type = 3 [json_name = "loan_type"];
  // Full name of the applicant
  // e.g: "<PERSON> <PERSON>"
  string applicant_name = 4 [json_name = "applicant_name"];
  // Email ID of the applicant (should be separated by comma in case of multiple emails, ordering is important)
  // e.g: "<EMAIL>"
  string applicant_email = 5 [json_name = "applicant_email"];
  // Contact number of borrower (should be separated by comma in case of multiple contact numbers, ordering is important)
  // e.g: "9805265926"
  string applicant_contact_number = 6 [json_name = "applicant_contact_number"];
  // Gender of the applicant (Options: Male, Female, Non-Binary)
  // e.g: "Male"
  string applicant_gender = 7 [json_name = "applicant_gender"];
  // Date of birth of the applicant in the format "YYYY-MM-DD"
  // e.g: "1992-03-23"
  string applicant_dob = 8 [json_name = "applicant_dob"];
  // Last updated monthly income of the applicant
  // e.g: 40000
  float applicant_monthly_income = 9 [json_name = "applicant_monthly_income"];
  // Last updated bureau score of the borrower
  // e.g: 820
  int32 applicant_cibil_score = 10 [json_name = "applicant_cibil_score"];
  // The Aadhar number of the applicant
  // e.g: "************"
  string applicant_aadhar_number = 11 [json_name = "applicant_aadhar_number"];
  // PAN number of the applicant
  // e.g: "ADJIY-3516-D"
  string applicant_pan_number = 12 [json_name = "applicant_pan_number"];
  // URL of the profile photo uploaded for the loan applicant
  string applicant_photo_link = 13 [json_name = "applicant_photo_link"];
  // Notice reference number of the Applicant
  // e.g: "**********"
  string applicant_notice_reference_number = 14 [json_name = "applicant_notice_reference_number"];
  // Last updated occupation of the applicant
  // e.g: "Job"
  string applicant_occupation = 15 [json_name = "applicant_occupation"];
  // Language of the applicant as per ISO 639-1 Code standards (Options:English,Hindi,Tamil,Punjabi,Telugu,Marathi,Gujarati,Kannada,Bengali,Malayalam,Oriya,Assamese)
  // e.g: "te"
  string applicant_language = 16 [json_name = "applicant_language"];
  // Total amount that was disbursed by the bank for the given loan account
  // e.g: 50000
  float total_loan_amount = 17 [json_name = "total_loan_amount"];
  // The vehicle registration number for the vehicle linked to the loan at the time of approval
  // e.g: "KA-01-1234"
  string vehicle_registration_number = 18 [json_name = "vehicle_registration_number"];
  // Tenure of the loan in months
  // e.g: 12
  int32 loan_tenure = 19 [json_name = "loan_tenure"];
  // The sanction date of the loan in the format 'YYYY-MM-DD'
  // e.g: "2019-04-06"
  string client_loan_sanction_date = 20 [json_name = "client_loan_sanction_date"];
  // The date on which the loan was supposed to end as per the amortisation schedule e.g. "YYYY-MM-DD"
  // e.g: "2020-04-06"
  string loan_end_date = 21 [json_name = "loan_end_date"];
  // The total interest amount that was determined at the time of loan disbursal as per the amortisation schedule
  // e.g: 9.1
  float interest_on_loan = 22 [json_name = "interest_on_loan"];
  // Boolean indicator showing whether the tenure of the loan is finished or not. (Options: True/False)
  bool tenure_finished = 23 [json_name = "tenure_finished"];
  // The type of security against the loan in case the loan is a secured loan (Options: Secured/Secured Movable/Secured Immovable) otherwise the value will be 'Unsecured'
  // e.g: "Secured immovable"
  string security_type = 24 [json_name = "security_type"];
  // Boolean variable providing information of whether the loan has a guarantor or not (Options: True/False)
  bool backed_by_security = 25 [json_name = "backed_by_security"];
  // Chassis number of the vehicle linked to the loan
  string chassis_number = 26 [json_name = "chassis_number"];
  // The name of the NBFC linked to the loan (if applicable)
  // e.g: "AKARA CAPITAL ADVISORS PVT. LTD."
  string loan_nbfc_name = 27 [json_name = "loan_nbfc_name"];
  // The CIN of the NBFC linked to the loan (if applicable)
  // e.g: "U65999MH2017PTC293229"
  string loan_nbfc_cin = 28 [json_name = "loan_nbfc_cin"];
  // List of tags that need to be applied to the loan at the time of upload [multiple values should be comma-separated, order not important]
  // e.g: "tag1,tag2,tag3"
  string tags = 29 [json_name = "tags"];
  // Email of the user to which the loan should be allocated to at the time of loan data upload
  // e.g: "john.doe@akaracapital"
  string agent_email = 30 [json_name = "agent_email"];
  // The amount of a single EMI that was determined at the time of loan disbursal as per the amortisation schedule
  float emi_amount = 31 [json_name = "emi_amount"];
  // The engine number of the vehicle linked to the loan
  string engine_number = 32 [json_name = "engine_number"];
  // The group_id assigned to the linked loan by the lender
  string group_id = 33 [json_name = "group_id"];
  // The financial product type in which the loan falls in, a further classification of loan type e.g. - 4-wheeler loan, 2-wheeler Loan, Overdraft loan, Term Loan, etc.
  string product_type = 34 [json_name = "product_type"];
  // The account number of the borrower in which he/she received the loan amount. Eg - "**********"
  string credit_account_number = 35 [json_name = "credit_account_number"];
  // Full name of the credit account holder e.g - "Ramesh Singh"
  string credit_account_holder_name = 36 [json_name = "credit_account_holder_name"];
  // The account type of the credit bank account holder e.g - Savings, Current, etc.
  string credit_account_holder_type = 37 [json_name = "credit_account_holder_type"];
  // The name of the bank in which the credit account holder (borrower) received the loan amount
  string credit_bank_name = 38 [json_name = "credit_bank_name"];
  // The IFSC code of the bank in which the borrower received the loan amount
  string credit_bank_ifsc_code = 39 [json_name = "credit_bank_ifsc_code"];
  // Last updated name of the business of the applicant
  string business_name = 40 [json_name = "business_name"];
  // The make and model of the vehicle linked to the loan (Applicable in case of vehicle/auto loans)
  string make_and_model = 41 [json_name = "make_and_model"];
  // Name of the merchant linked to the loan e.g. the merchant to which
  string merchant_name = 42 [json_name = "merchant_name"];
  // The date that is to be printed on the draft PDF
  string notice_date = 43 [json_name = "notice_date"];
  // The name of the delivery partner merchant that was used to deliver the notice
  string notice_delivery_partner = 44 [json_name = "notice_delivery_partner"];
  // The URL corresponding to the given allocation month and loan to send to the borrower through various communications in order to get them to pay through it
  string payment_link = 45 [json_name = "payment_link"];
  // The expiry time for the payment link (seconds)
  int32 payment_link_expiry = 46 [json_name = "payment_link_expiry"];
  // Channel partner name of the applicant
  string channel_partner = 47 [json_name = "channel_partner"];
  // Geographical Region to which the loan belongs to
  string loan_region = 48 [json_name = "loan_region"];
  // Name of the regional manager assigned by the client to the loan
  string loan_regional_manager_name = 49 [json_name = "loan_regional_manager_name"];
  // Contact number of the regional manager assigned by the client to the loan
  string loan_regional_manager_contact_number = 50 [json_name = "loan_regional_manager_contact_number"];
  // Contact email of the regional manager assigned by the client to the loan
  string loan_regional_manager_contact_email = 51 [json_name = "loan_regional_manager_contact_email"];
  // Address of the physical branch which disbursed the loan (if applicable)
  string loan_source_branch_address = 52 [json_name = "loan_source_branch_address"];
  repeated DocumentDetails document_details = 53 [json_name = "document_details"];
  repeated ApplicantAddress applicant_address = 54 [json_name = "applicant_address"];
  repeated DefaultDetails defaults = 55 [json_name = "defaults"];
  repeated CoApplicant co_applicant = 56 [json_name = "co_applicant"];
  repeated ReferenceDetails references = 57 [json_name = "references"];
  float disbursed_amount = 58 [json_name = "disbursed_amount"];
  float outstanding_amount = 59 [json_name = "outstanding_amount"];
  // total amount paid by user till now for the loan account since the beginning of loan tenure
  float repaid_amount = 60 [json_name = "repaid_amount"];
  uint32 past_default_count = 61 [json_name = "past_default_count"];
  repeated RepaymentHistory past_repayments = 62 [json_name = "past_repayments"];
  string lender_name = 63 [json_name = "lender_name"];
  string actor_id = 64 [json_name = "actor_id"];
  // denotes if user's savings account was blocked due to suspicious activity
  string is_risky_user = 65 [json_name = "is_risky_user"];
  string credit_score = 66 [json_name = "credit_score"];
  string is_salary_account = 67 [json_name = "is_salary_account"];
  // employment status as shared by user as in onboarding service
  string employment_status = 68 [json_name = "employment_status"];
  // Income range shared by user as in onboarding service
  string income_range = 69 [json_name = "income_range"];
  // Loan identifier shared by vendor
  string vendor_loan_identifier = 70 [json_name = "vendor_loan_identifier"];
  string alternate_contact_number = 71 [json_name = "alternate_contact_number"];
  string alternate_address = 72 [json_name = "alternate_address"];
  string freshdesk_tickets = 73 [json_name = "freshdesk_tickets"];
  string last_app_active_time = 74 [json_name = "last_app_active_time"];
  string debit_bank_name = 75 [json_name = "debit_bank_name"];
  string debit_bank_acc_no = 76 [json_name = "debit_bank_acc_no"];
  float foreclosure_outstanding_amt = 77 [json_name = "foreclosure_outstanding_amt"];
  float foreclosure_principal_amt = 78 [json_name = "foreclosure_principal_amt"];
  float foreclosure_interest_amt = 79 [json_name = "foreclosure_interest_amt"];
  float foreclosure_fees_charges_amt = 80 [json_name = "foreclosure_fees_charges_amt"];
  string trust_name = 81 [json_name = "trust_name"];
  repeated CustomerAddress customer_addresses = 82 [json_name = "customer_address"];
  repeated CustomerContactDetails customer_contact_details = 83 [json_name = "customer_contact_details"];
  repeated CustomerEmailDetails customer_email_details = 84 [json_name = "customer_email_details"];
  repeated LoanInstallmentPayout loan_installment_payouts = 85 [json_name = "loan_installment_payout_data"];
  repeated ApplicantContactDetails applicant_contact_details = 86 [json_name = "applicant_contact_details"];
}

message RepaymentHistory {
  string payout_date = 1 [json_name = "payout_date"];
  float repaid_amount = 2 [json_name = "instalment_repaid_amount"];
  int32 dpd = 3 [json_name = "dpd"];
}

// ApplicantAddress contains list of Variable containing all the sub-variables corresponding to the address of the
// applicant.
message ApplicantAddress {
  // The type of address that has been provided for applicant address [Options - Home, Business, Office]
  // e.g: "Home"
  string applicant_address_type = 1 [json_name = "applicant_address_type"];
  // Entire text body of address of applicant including lane, street, house number, colony etc.
  // e.g: "395/13 veer dua colony baldev nager"
  string applicant_address_text = 2 [json_name = "applicant_address_text"];
  // State provided in the applicant address
  // e.g: "Rajasthan"
  string applicant_state = 3 [json_name = "applicant_state"];
  // City provided in the applicant address
  // e.g: "Jodhpur"
  string applicant_city = 4 [json_name = "applicant_city"];
  // Nearby Landmark provided in the applicant address
  string applicant_landmark = 5 [json_name = "applicant_landmark"];
  // Pincode provided in the applicant address
  // e.g: 342001
  int32 applicant_pincode = 6 [json_name = "applicant_pincode"];
  // The latitude of the location this applicant address points to, this will get used at the time of allocation
  string applicant_address_latitude = 7 [json_name = "applicant_address_latitude"];
  // The longitude of the location this applicant address points to, this will get used at the time of allocation
  string applicant_address_longitude = 8 [json_name = "applicant_address_longitude"];
}

// DefaultDetails contains the list of variable containing all the different allocation month level variables that
// provide information about the different amounts that are due, recovered and general details of the EMI or allocation
// in question.
message DefaultDetails {
  // The month and year for which the loan has been allocated on the CG Platform
  // e.g: "2021-7-01"
  string allocation_month = 1 [json_name = "allocation_month"];
  // The DPD value of the borrower at the time of allocation of loan onto the CG Platform
  int32 allocation_dpd_value = 2 [json_name = "allocation_dpd_value"];
  // The DPD bracket of the borrower at the time of allocation of loan onto the CG Platform
  string allocation_dpd_bracket = 3 [json_name = "allocation_dpd_bracket"];
  // The total amount that can be claimed from the borrower for the given month
  // e.g: 113000
  float total_claim_amount = 4 [json_name = "total_claim_amount"];
  // Late fee that needs to be paid by the borrower
  // e.g: 59443
  float late_fee = 5 [json_name = "late_fee"];
  // Date on which the borrower defaulted on the loan (first default)
  // e.g: "2019-04-07"
  string date_of_default = 6 [json_name = "date_of_default"];
  // The EMI amount without late fees and penalties that is to be recovered for the given month from the borrower
  // e.g: 54000
  float expected_emi = 7 [json_name = "expected_emi"];
  // The serial number of the EMI that the borrower defaulted on as per the amortization schedule created at the time of loan disbursal by the lender
  // e.g: 12
  int32 default_emi_number = 8 [json_name = "default_emi_number"];
  // The settlement amount that is to be recovered from the borrower to in order to generate the settlement NOC by lender
  // e.g: 45000
  float settlement_amount = 9 [json_name = "settlement_amount"];
  // The amount that was already recovered by the client/lender before allocating the loan on CG Platform
  // e.g: 0
  float client_amount_recovered = 10 [json_name = "client_amount_recovered"];
  // The principal outstanding amount corresponding to the loan at the time of allocation to CG
  // e.g: 45000
  float principal_outstanding_amount = 11 [json_name = "principal_outstanding_amount"];
  // The interest amount corresponding to the EMI that is to be recovered for the given month from the borrower
  float expected_emi_interest_amount = 12 [json_name = "expected_emi_interest_amount"];
  // The principal amount corresponding to the EMI that is to be recovered for the given month from the borrower
  // e.g: 45000
  float expected_emi_principal_amount = 13 [json_name = "expected_emi_principal_amount"];
  // The penalty amount corresponding to the EMI that is to be recovered for the given month from the borrower
  float other_penalty = 14 [json_name = "other_penalty"];
  // The recovery status of this default entry
  // eg. "Not Recovered"
  string recovery_status = 15 [json_name = "recovery_status"];
  // custom variables or non-default variables which are created at allocation level has to be embedded under additional_variables field
  AdditionalVariables additional_variables = 16 [json_name='additional_variables'];

  message AdditionalVariables {
    // due amount of a future emi (i.e due date of emi is a future date w.r.t current date on which loan details are posted/synced)
    // whose due date falls in the current allocation month.
    // using FloatValue data type to update this field value to zero
    // if primitive float data type is used and values is set to zero
    // then this field would not be populated in the request since default value is zero
    google.protobuf.FloatValue future_emi_amount_in_current_allocation_month = 1 [json_name='future_emi_amount_in_current_allocation_month'];
  }
}

// ReferenceDetails contains the list of Variable containing all the details of the person who has served as a
// reference for the borrower.
message ReferenceDetails {
  // The relation of the reference to the applicant
  string relation_with_applicant = 1 [json_name = "relation_with_applicant"];
  // The full name of the reference
  string name = 2 [json_name = "name"];
  // The contact number of the reference
  string contact_number = 3 [json_name = "contact_number"];
  // The gender of the reference
  string reference_gender = 4 [json_name = "reference_gender"];
  // The PAN card number of the reference
  string reference_pan_number = 5 [json_name = "reference_pan_number"];
  // List of Variable containing all the address details of the reference
  repeated ReferenceAddress reference_address = 6 [json_name = "reference_address"];
}

// ReferenceAddress contains list of Variable containing all the address details of the reference.
message ReferenceAddress {
  // The address type of the reference
  string reference_address_type = 1 [json_name = "reference_address_type"];
  // The full address text of the reference
  string reference_address_text = 2 [json_name = "reference_address_text"];
  // The city of residence of the reference
  string reference_city = 3 [json_name = "reference_city"];
  // The state of residence of the reference
  string reference_state = 4 [json_name = "reference_state"];
  // The pincode of residence of the refernece
  int32 reference_pincode = 5 [json_name = "reference_pincode"];
}

// CoApplicant contains list of Variable containing all the details of the co-applicant.
message CoApplicant {
  // Full name of the co-applicant
  // e.g: "Naveen Shekhawat"
  string co_applicant_name = 1 [json_name = "co_applicant_name"];
  // The type of the co-applicant
  // e.g: "Individual"
  string co_applicant_type = 2 [json_name = "co_applicant_type"];
  // The language used by the co-applicant
  // e.g: "English"
  string co_applicant_language = 3 [json_name = "co_applicant_language"];
  // Email ID of the co-applicant [should be separated by comma in case of multiple emails, ordering is important]
  // e.g: "pravinsingh@gmail,com"
  string co_applicant_email = 4 [json_name = "co_applicant_email"];
  // Contact number of borrower [should be separated by comma in case of multiple contact numbers, ordering is important]
  // e.g: "9814274470"
  string co_applicant_contact_number = 5 [json_name = "co_applicant_contact_number"];
  // Gender of the co-applicant [Options: Male, Female, Other]
  // e.g: "Male"
  string co_applicant_gender = 6 [json_name = "co_applicant_gender"];
  // Date of birth of the co-applicant in the format "YYYY-MM-DD"
  // e.g: "1990-06-29"
  string co_applicant_dob = 7 [json_name = "co_applicant_dob"];
  // Notice reference number of co-applicant
  // e.g: "**********"
  string co_applicant_notice_reference_number = 8 [json_name = "co_applicant_notice_reference_number"];
  // URL of the profile photo uploaded for the loan co-applicant
  string co_applicant_photo_link = 9 [json_name = "co_applicant_photo_link"];
  // List of Variable containing all the address details of the co-applicant
  repeated CoApplicantAddress co_applicant_address = 10 [json_name = "co_applicant_address"];
}

// CoApplicantAddress contains list of Variable containing all the address details of the co-applicant.
message CoApplicantAddress {
  // The address type of the co-applicant
  // e.g: "Home"
  string co_applicant_address_type = 1 [json_name = "co_applicant_address_type"];
  // The full address text of the co-applicant
  // e.g: "123, ABC Colony, XYZ Road"
  string co_applicant_address_text = 2 [json_name = "co_applicant_address_text"];
  // The city of residence of the co-applicant
  // e.g: "Alwar"
  string co_applicant_city = 3 [json_name = "co_applicant_city"];
  // The state of residence of the co-applicant
  // e.g: "Rajasthan"
  string co_applicant_state = 4 [json_name = "co_applicant_state"];
  // The pincode of residence of the co-applicant
  // e.g: 301024
  int32 co_applicant_pincode = 5 [json_name = "co_applicant_pincode"];
  // The latitude of the location this co-applicant address points to, this will get used at the time of allocation
  string co_applicant_address_latitude = 6 [json_name = "co_applicant_address_latitude"];
  // The longitude of the location this co-applicant address points to, this will get used at the time of allocation
  string co_applicant_address_longitude = 7 [json_name = "co_applicant_address_longitude"];
}

message DocumentDetails {
  // The security mode for the loan provided by the borrower at the time of loan approval e.g. - NACH, ECS, ENACH, PDC, Others (Two wheeler, Four wheeler, house) etc.
  // e.g: "NACH"
  string security_mode = 1 [json_name = "security_mode"];
  // Document number of the security document e.g. Cheque Number, ECS number etc.
  // e.g: "757493"
  string document_number = 2 [json_name = "document_number"];
  // The name of bank/lender that issued the security document e.g. cheque bank name
  // e.g: "HDFC Bank"
  string document_bank_name = 3 [json_name = "document_bank_name"];
  // The IFSC code of the bank that has issued the security document
  string document_bank_ifsc_code = 4 [json_name = "document_bank_ifsc_code"];
  // The amount mentioned on the security document. Eg. cheque amount
  // e.g: 50000
  float document_amount = 5 [json_name = "document_amount"];
  // The execution date of the security document submitted by borrower e.g. cheque date
  // e.g: "2018-04-10"
  string document_date = 6 [json_name = "document_date"];
  // The dishonour date of the security document e.g. Cheque dishonour date
  // e.g: "2018-04-12"
  string document_dishonour_date = 7 [json_name = "document_dishonour_date"];
  // The name of the signee on the security document. Eg. Signee name on the Cheque
  // e.g: "John Doe"
  string document_signature_name = 8 [json_name = "document_signature_name"];
  // The account number of the bank account in which document was bounced
  // e.g: "**********"
  string document_bounce_bank_account_number = 9 [json_name = "document_bounce_bank_account_number"];
  // The charges associated with the document bounce processing
  // e.g: 10
  float document_bounce_charges = 10 [json_name = "document_bounce_charges"];
  // Bank name in which the security_mode document was bounced
  // e.g: "Yes Bank"
  string document_bounce_bank_name = 11 [json_name = "document_bounce_bank_name"];
  // IFSC code of the bank in which document was bounced
  string document_bounce_bank_ifsc_code = 12 [json_name = "document_bounce_bank_ifsc_code"];
  // Address of the bank in which document was bounced
  string document_bounce_bank_address = 13 [json_name = "document_bounce_bank_address"];
  // Date of document bounce memo
  // e.g: "2018-04-12"
  string document_bounce_memo_date = 14 [json_name = "document_bounce_memo_date"];
  // Reason behind the security_mode document bounce. Eg. Insufficient balance
  string reason_of_document_bounce = 15 [json_name = "reason_of_document_bounce"];
  // 9 digit Document MICR Code of the document
  string document_micr = 16 [json_name = "document_micr"];
  // Account in which document is presented
  string document_presented_in_account = 17 [json_name = "document_presented_in_account"];
  // Zone in which document is presented
  string document_presented_zone = 18 [json_name = "document_presented_zone"];
  // Address of the document bounce memo branch
  string document_bounce_memo_branch_address = 19 [json_name = "document_bounce_memo_branch_address"];
  // Document signature address
  string document_signature_address_text = 20 [json_name = "document_signature_address_text"];
  // Document signature city
  string document_signature_city = 21 [json_name = "document_signature_city"];
  // Document signature state
  string document_signature_state = 22 [json_name = "document_signature_state"];
  // Document signature pincode
  int32 document_signature_pincode = 23 [json_name = "document_signature_pincode"];
  // Reference number of document bounce memo
  string document_bounce_memo_reference_number = 24 [json_name = "document_bounce_memo_reference_number"];
  // Sequence number of document
  string document_sequence_number = 25 [json_name = "document_sequence_number"];
  // Document bounce memo return date
  string document_bounce_memo_return_date = 26 [json_name = "document_bounce_memo_return_date"];
  // Document Unique Mandate Reference Number
  string document_umrn = 27 [json_name = "document_umrn"];
}

// Remark contains all the activities that are performed on the loan account.
// It provides a snapshot view of the loan account.
message Remark {
  // e.g: 5237
  string remark_id = 1 [json_name = "remark_id"];
  // e.g: (Legal Notice Template ) SMS Sent
  string remarks = 2 [json_name = "remarks"];
  // e.g: 2020-11-15 16:57:26
  string created = 3 [json_name = "created"];
  // e.g: ssingh@credgenics
  string author = 4 [json_name = "author"];
}
message CustomerAddress {
  // e.g: residential
  string customer_address_type = 1 [json_name = "customer_address_type"];
  // contains postal address
  string customer_address_text = 2 [json_name = "customer_address_text"];
  string customer_city = 3 [json_name = "customer_city"];
  string address_pin_code = 4 [json_name = "address_pin_code"];
  // e.g: Technostar
  string customer_address_landmark = 5 [json_name = "customer_address_landmark"];
  // e.g: Karnataka
  string customer_state = 6 [json_name = "customer_state"];
  // e.g: Experian
  string address_data_source = 7 [json_name = "address_data_source"];
  // the date on which address was submitted/registered
  string data_capture_date = 8 [json_name = "data_capture_date"];
}
message CustomerContactDetails {
  // e.g: 1231665403
  string customer_contact_number = 1 [json_name = "customer_contact_number"];
  // e.g: personal
  string customer_contact_type = 2 [json_name = "customer_contact_type"];
  // e.g: cibil
  string customer_contact_data_source = 3 [json_name = "customer_contact_data_source"];
  // the date on which contact details was submitted/registered
  string customer_contact_details_capture_date = 8 [json_name = "customer_contact_details_capture_date"];
}
message CustomerEmailDetails {
  // e.g: <EMAIL>
  string customer_email = 1 [json_name = "customer_email"];
  // e.g: personal
  string customer_email_type = 2 [json_name = "customer_email_type"];
  // e.g: cibil
  string customer_email_data_source = 3 [json_name = "customer_email_data_source"];
  // the date on which email details was submitted/registered
  string customer_email_capture_date = 8 [json_name = "customer_email_capture_date"];
}

message LoanInstallmentPayout {
  string installment_due_date = 1 [json_name='installment_due_date'];
  float installment_due_amount = 2 [json_name='installment_due_amount'];
  float installment_paid_amount = 3 [json_name='installment_paid_amount'];
  string installment_paid_date = 4 [json_name='installment_paid_date'];
  string installment_status = 5 [json_name='installment_status'];
}

message ApplicantContactDetails {
  // e.g: 1231665403
  string applicant_contact_number = 1 [json_name='applicant_contact_number'];
  // e.g: primary, alternate, credit report, etc
  string applicant_contact_number_type = 2 [json_name='applicant_contact_number_type'];
}
