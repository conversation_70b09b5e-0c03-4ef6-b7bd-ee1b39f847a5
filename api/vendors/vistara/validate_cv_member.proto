syntax = "proto3";

package vendors.vistara;

option go_package = "github.com/epifi/gamma/api/vendors/vistara";
option java_package = "com.github.epifi.gamma.api.vendors.vistara";

message ValidateCvMemberRequest {
  ValidateCvMember validate_cv_member = 1 [json_name = "validateCVMember"];

  message ValidateCvMember {
    // denotes the unique identifier for the validation request.
    string unique_id = 1 [json_name = "uniqueId"];
    // cv membership ID to be validated
    string ff_id = 2 [json_name = "ffid"];
    // email ID to be validated
    string email_id = 3 [json_name = "emailId"];
  }
}

message ValidateCvMemberResponse {
  // denotes the unique identifier for the validation request.
  string unique_id = 1 [json_name = "uniqueId"];
  // cv membership ID to be validated
  string ff_id = 2 [json_name = "ffid"];
  // email ID to be validated
  string email_id = 3 [json_name = "emailId"];
  // response status code
  string status_code = 4 [json_name = "statusCode"];
  // status message describing the response
  string status = 5 [json_name = "status"];
  // error code (will be empty in case of success cases)
  string error_code = 6 [json_name = "errorCode"];
  // descriptive error message
  string error_message = 7 [json_name = "errorMessage"];
}
