// A service for handling communication with users through notifications.
syntax = "proto3";

package vendors.karvy;


option go_package = "github.com/epifi/gamma/api/vendors/karvy";
option java_package = "com.github.epifi.gamma.api.vendors.karvy";

// GetFolioDetailsRequest is the json request structure for GetMobileAndEmailBasedOnFolio api of karvy. Given a folio number,
// this api helps us to fetch the contact details related to the folio stored by the vendor.
message GetFolioDetailsRequest {
  string app_id = 1 [json_name = "Appid"];
  string app_pwd = 2 [json_name = "Apppwd"];
  string app_iden = 3 [json_name = "AppIden"];
  string agent_code = 4 [json_name = "AgentCode"];
  string branch_code = 5 [json_name = "BranchCode"];
  string amc_code = 6 [json_name = "AMC_Code"];
  string folio_no = 7 [json_name = "Folio_No"];
}

message GetFolioDetailsResponse {
  string return_code = 1 [json_name = "return_code"];
  string return_msg = 2 [json_name = "return_msg"];
  string fund = 3 [json_name = "Fund"];
  string acc_no = 4 [json_name = "acno"];
  string mobile_no = 5 [json_name = "mobile"];
  string email_id = 6 [json_name = "email"];
}
