//go:generate gen_sql -types=CompanyMarketStatistics,CompanyMarketData,CompanyFundamentalParameters,CompanyFundamentalParagraph,CompanyDetails,AssetDetails
syntax = "proto3";

package vendors.catalog.bridgewise;

import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendors/catalog/bridgewise";
option java_package = "com.github.epifi.gamma.api.vendors.catalog.bridgewise";

message GetAccessTokenReq {
  string application_client_id = 1 [json_name = 'application_client_id'];
  string secret = 2 [json_name = 'secret'];
}

message GetAccessTokenRes {
  string access_token = 1 [json_name = 'access_token'];
}

message AssetDetails {
  int64 company_id = 1 [json_name = "company_id"];
  int64 security_id = 2 [json_name = "security_id"];
  bool security_primary_flag = 3 [json_name = "security_primary_flag"];
  int64 trading_item_id = 4 [json_name = "trading_item_id"];
  bool primary_flag = 5 [json_name = "primary_flag"];
  string ticker_symbol = 6 [json_name = "ticker_symbol"];
  string exchange_symbol = 7 [json_name = "exchange_symbol"];
  string exchange_name = 8 [json_name = "exchange_name"];
  int64 exchange_id = 9 [json_name = "exchange_id"];
  int64 exchange_importance_level = 10 [json_name = "exchange_importance_level"];
  string currency_iso3 = 11 [json_name = "currency_iso3"];
  string asset_type = 12 [json_name = "asset_type"];
  bool has_score = 13 [json_name = "has_score"];
}

message GetAssetIdentifierDetailsRes {
  repeated AssetDetails results = 1 [json_name = "results"];
}

message CompanyDetails {
  string language = 1 [json_name = "language"];
  string updated_at = 2 [json_name = "updated_at"];
  int64 company_id = 3 [json_name = "company_id"];
  string company_name = 4 [json_name = "company_name"];
  string company_name_short = 5 [json_name = "company_name_short"];
  string website = 6 [json_name = "website"];
  string primary_ticker_symbol = 7 [json_name = "primary_ticker_symbol"];
  string primary_exchange_symbol = 8 [json_name = "primary_exchange_symbol"];
  int64 region_id = 9 [json_name = "region_id"];
  string region_name = 10 [json_name = "region_name"];
  int64 incorporation_country_id = 11 [json_name = "incorporation_country_id"];
  string incorporation_country_name = 12 [json_name = "incorporation_country_name"];
  int64 domicile_country_id = 13 [json_name = "domicile_country_id"];
  string domicile_country_name = 14 [json_name = "domicile_country_name"];
  int64 gics_sector_id = 15 [json_name = "gics_sector_id"];
  string gics_sector_name = 16 [json_name = "gics_sector_name"];
  int64 gics_industry_group_id = 17 [json_name = "gics_industry_group_id"];
  string gics_industry_group_name = 18 [json_name = "gics_industry_group_name"];
  int64 gics_industry_id = 19 [json_name = "gics_industry_id"];
  string gics_industry_name = 20 [json_name = "gics_industry_name"];
  int64 csa_industry_id = 21 [json_name = "csa_industry_id"];
  string csa_industry_name = 22 [json_name = "csa_industry_name"];
  int64 primary_currency_market_id = 23 [json_name = "primary_currency_market_id"];
  string primary_currency_market = 24 [json_name = "primary_currency_market"];
  string primary_currency_financials_iso3 = 25 [json_name = "primary_currency_financials_iso3"];
  int64 primary_security_id = 26 [json_name = "primary_security_id"];
  string primary_security_name = 27 [json_name = "primary_security_name"];
  string ipo_date = 28 [json_name = "ipo_date"];
  string pdf_path = 29 [json_name = "pdf_path"];
  google.protobuf.Value include = 30 [json_name = "include"];
  google.protobuf.Value is_red_flagged = 31 [json_name = "is_red_flagged"];
}

message FundDetails {
  string language = 1 [json_name = "language"];
  string updated_at = 2 [json_name = "updated_at"];
  int64 fund_id = 3 [json_name = "fund_id"];
  string fund_name = 4 [json_name = "fund_name"];
  string fund_name_short = 5 [json_name = "fund_name_short"];
  int64 fund_type_id = 6 [json_name = "fund_type_id"];
  string fund_type_name = 7 [json_name = "fund_type_name"];
  string domicile_country_name = 8 [json_name = "domicile_country_name"];
  int64 primary_trading_item = 9 [json_name = "primary_trading_item"];
  string primary_ticker_symbol = 10 [json_name = "primary_ticker_symbol"];
  string primary_exchange_symbol = 11 [json_name = "primary_exchange_symbol"];
  string primary_exchange_name = 12 [json_name = "primary_exchange_name"];
  string primary_market_currency_iso3 = 13 [json_name = "primary_market_currency_iso3"];
  string geographic_country_mandate = 14 [json_name = "geographic_country_mandate"];
  string market_capitalization_emphasis = 15 [json_name = "market_capitalization_emphasis"];
  string sector_emphasis = 16 [json_name = "sector_emphasis"];
  string inception_date = 17 [json_name = "inception_date"];
  string weight_method = 18 [json_name = "weight_method"];
  string exposure = 19 [json_name = "exposure"];
  int64 leverage = 20 [json_name = "leverage"];
  string sponsor_name = 21 [json_name = "sponsor_name"];
  string sponsor_name_short = 22 [json_name = "sponsor_name_short"];
  int64 benchmark_id = 23 [json_name = "benchmark_id"];
  string benchmark_name = 24 [json_name = "benchmark_name"];
  string benchmark_name_short = 25 [json_name = "benchmark_name_short"];
  string management_style = 26 [json_name = "management_style"];
  int64 number_of_holdings = 27 [json_name = "number_of_holdings"];
  bool has_score = 28 [json_name = "has_score"];
  double pct_of_equity_holdings = 29 [json_name = "pct_of_equity_holdings"];
  string portfolio_strategy = 30 [json_name = "portfolio_strategy"];
}

message GetCompanyRes {
  repeated CompanyDetails companies = 1 [json_name = "companies"];
}

message CompaniesData {
  repeated CompanyDetails data = 1 [json_name = "data"];
  int32 total_count = 2 [json_name = "total_count"];
}

message FundsData {
  repeated FundDetails data = 1 [json_name = "data"];
  int32 total_count = 2 [json_name = "total_count"];
}

message CompanyFundamentalParameters {
  string language = 1 [json_name = "language"];
  string updated_at = 2 [json_name = "updated_at"];
  int64 calendar_year = 3 [json_name = "calendar_year"];
  int64 calendar_quarter = 4 [json_name = "calendar_quarter"];
  string analysis_type = 5 [json_name = "analysis_type"];
  string analysis_type_name = 6 [json_name = "analysis_type_name"];
  string section_type = 7 [json_name = "section_type"];
  string section_type_name = 8 [json_name = "section_type_name"];
  string filing_date = 9 [json_name = "filing_date"];
  string period_type = 10 [json_name = "period_type"];
  int64 parameter_id = 11 [json_name = "parameter_id"];
  google.protobuf.Value parameter_name = 12 [json_name = "parameter_name"];
  google.protobuf.Value parameter_description = 13 [json_name = "parameter_description"];
  double parameter_value = 14 [json_name = "parameter_value"];
  double parameter_value_change = 15 [json_name = "parameter_value_change"];
  string parameter_type = 16 [json_name = "parameter_type"];
  string parameter_currency_iso3 = 17 [json_name = "parameter_currency_iso3"];
  double parameter_exchange_rate_to_usd = 18 [json_name = "parameter_exchange_rate_to_usd"];
}

message FundParameters {
  string language = 1 [json_name = "language"];
  string updated_at = 2 [json_name = "updated_at"];
  int32 year = 3 [json_name = "year"];
  int32 quarter = 4 [json_name = "quarter"];
  string section_type = 5 [json_name = "section_type"];
  string section_type_name = 6 [json_name = "section_type_name"];
  string analysis_type = 7 [json_name = "analysis_type"];
  string analysis_type_name = 8 [json_name = "analysis_type_name"];
  int64 parameter_id = 9 [json_name = "parameter_id"];
  google.protobuf.Value parameter_name = 10 [json_name = "parameter_name"];
  string parameter_type = 11 [json_name = "parameter_type"];
  double parameter_value = 12 [json_name = "parameter_value"];
  double parameter_value_change = 13 [json_name = "parameter_value_change"];
  string parameter_currency_iso3 = 14 [json_name = "parameter_currency_iso3"];
  double parameter_exchange_rate_to_usd = 15 [json_name = "parameter_exchange_rate_to_usd"];
  google.protobuf.Value parameter_description = 16 [json_name = "parameter_description"];
  bool has_score = 17 [json_name = "has_score"];
  int32 parameter_score_group = 18 [json_name = "parameter_score_group"];
  string parameter_score_group_text = 19 [json_name = "parameter_score_group_text"];
}

message GetCompanyFundamentalParametersRes {
  repeated CompanyFundamentalParameters parameters = 1 [json_name = "parameters"];
}

message CompanyFundamentalParagraph {
  string analysis_type = 1 [json_name = "analysis_type"];
  string section_type = 2 [json_name = "section_type"];
  string paragraph_type = 3 [json_name = "paragraph_type"];
  string paragraph = 4 [json_name = "paragraph"];
  string language = 5 [json_name = "language"];
  string updated_at = 6 [json_name = "updated_at"];
}

message FundParagraphs {
  string analysis_type = 1 [json_name = "analysis_type"];
  string section_type = 2 [json_name = "section_type"];
  string paragraph_type = 3 [json_name = "paragraph_type"];
  string paragraph = 4 [json_name = "paragraph"];
  string language = 5 [json_name = "language"];
  string updated_at = 6 [json_name = "updated_at"];
  int32 year = 7 [json_name = "year"];
  int32 quarter = 8 [json_name = "quarter"];
}

message GetCompanyFundamentalParagraphsRes {
  repeated CompanyFundamentalParagraph paragraphs = 1 [json_name = "paragraphs"];
}

message CompanyMarketData {
  int64 trading_item_id = 1 [json_name = "trading_item_id"];
  string updated_at = 2 [json_name = "updated_at"];
  string date = 3 [json_name = "date"];
  double open_price = 4 [json_name = "open_price"];
  double close_price = 5 [json_name = "close_price"];
  double high_price = 6 [json_name = "high_price"];
  double low_price = 7 [json_name = "low_price"];
  int64 volume = 8 [json_name = "volume"];
  double market_cap = 9 [json_name = "market_cap"];
  double implied_market_cap = 10 [json_name = "implied_market_cap"];
  string market_cap_currency_iso3 = 11 [json_name = "market_cap_currency_iso3"];
  string price_currency_iso3 = 12 [json_name = "price_currency_iso3"];
  double exchange_rate_to_usd = 13 [json_name = "exchange_rate_to_usd"];
  double daily_return = 14 [json_name = "daily_return"];
  double market_cap_exchange_rate_to_usd = 15 [json_name = "market_cap_exchange_rate_to_usd"];
  double market_cap_usd = 16 [json_name = "market_cap_usd"];
  string filing_date = 17 [json_name = "filing_date"];
  double change_since_filing = 18 [json_name = "change_since_filing"];
}

message FundMarketData {
  int64 trading_item_id = 1 [json_name = "trading_item_id"];
  string updated_at = 2 [json_name = "updated_at"];
  string date = 3 [json_name = "date"];
  double open_price = 4 [json_name = "open_price"];
  double close_price = 5 [json_name = "close_price"];
  double high_price = 6 [json_name = "high_price"];
  double low_price = 7 [json_name = "low_price"];
  int64 volume = 8 [json_name = "volume"];
  int64 shares_outstanding = 9 [json_name = "shares_outstanding"];
  string price_currency_iso3 = 10 [json_name = "price_currency_iso3"];
  double exchange_rate_to_usd = 11 [json_name = "exchange_rate_to_usd"];
  double daily_return = 12 [json_name = "daily_return"];
  double market_cap = 13 [json_name = "market_cap"];
}

message GetCompanyMarketDataRes {
  repeated CompanyMarketData market_data = 1 [json_name = "market_data"];
}

message CompanyLogoLink {
  string size = 1 [json_name = "size"];
  string url = 2 [json_name = "url"];
  string type = 3 [json_name = "type"];
  string extension = 4 [json_name = "extension"];
  bool is_sponsor = 5 [json_name = "is_sponsor"];
}

message CompanyLogo {
  int64 company_id = 1 [json_name = "company_id"];
  google.protobuf.Value sponsor_id = 2 [json_name = "sponsor_id"];
  repeated CompanyLogoLink links = 3 [json_name = "links"];
}

message GetCompanyLogosRes {
  repeated CompanyLogo items = 1 [json_name = "items"];
  int64 total = 2 [json_name = "total"];
  int64 page = 3 [json_name = "page"];
  int64 size = 4 [json_name = "size"];
  int64 pages = 5 [json_name = "pages"];
}

message CompanyMarketStatistics {
  int64 company_id = 1 [json_name = "company_id"];
  int64 trading_item_id = 2 [json_name = "trading_item_id"];
  double one_day = 3 [json_name = "1D"];
  double five_days = 4 [json_name = "5D"];
  double one_month = 5 [json_name = "1M"];
  double six_months = 6 [json_name = "6M"];
  double one_year = 7 [json_name = "1Y"];
  double two_years = 8 [json_name = "2Y"];
  double five_years = 9 [json_name = "5Y"];
  double year_to_date = 10 [json_name = "YTD"];
}

message GetCompanyMarketStatisticsRes {
  repeated CompanyMarketStatistics market_statistics = 1 [json_name = "market_statistics"];
}

message SegmentDetails {
  string language = 1 [json_name = "language"];
  int32 year = 2 [json_name = "year"];
  int32 quarter = 3 [json_name = "quarter"];
  string asset_type = 4 [json_name = "asset_type"];
  string segment = 5 [json_name = "segment"];
  string segment_type = 6 [json_name = "segment_type"];
  string segment_type_name = 7 [json_name = "segment_type_name"];
  double segment_weight = 8 [json_name = "segment_weight"];
}

message HoldingDetails {
  string language = 1 [json_name = "language"];
  int64 company_id = 2 [json_name = "company_id"];
  string company_name = 3 [json_name = "company_name"];
  string composition_type = 4 [json_name = "composition_type"];
  string composition_type_name = 5 [json_name = "composition_type_name"];
  double weight = 6 [json_name = "weight"];
  string sector = 7 [json_name = "sector"];
  string industry = 8 [json_name = "industry"];
  string region = 9 [json_name = "region"];
  string country = 10 [json_name = "country"];
  double market_cap_usd = 11 [json_name = "market_cap_usd"];
  double return_1y = 12 [json_name = "return_1y"];
  string financial_currency_iso_3 = 13 [json_name = "financial_currency_iso_3"];
  double pb_ratio = 14 [json_name = "pb_ratio"];
  double pe_ratio = 15 [json_name = "pe_ratio"];
  double ps_ratio = 16 [json_name = "ps_ratio"];
  double net_debt_divided_by_ebitda = 17 [json_name = "net_debt_divided_by_ebitda"];
  double return_on_equity_percent = 18 [json_name = "return_on_equity_percent"];
  double revenue_growth = 19 [json_name = "revenue_growth"];
  double dividend_yield = 20 [json_name = "dividend_yield"];
  string primary_ticker_symbol = 21 [json_name = "primary_ticker_symbol"];
  string primary_exchange_symbol = 22 [json_name = "primary_exchange_symbol"];
  int32 fund_analysis_score_group = 23 [json_name = "fund_analysis_score_group"];
  string fund_analysis_score_group_text = 24 [json_name = "fund_analysis_score_group_text"];
  double analysis_score = 25 [json_name = "analysis_score"];
}
