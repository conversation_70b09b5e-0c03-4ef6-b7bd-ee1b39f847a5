syntax = "proto3";

package vendors.finflux;

import "api/vendors/finflux/types/loan.proto";

option go_package = "github.com/epifi/gamma/api/vendors/finflux";
option java_package = "com.github.epifi.gamma.api.vendors.finflux";

/*
 * Reject Loan API
 * Request body is RejectLoanRequest
 * Response body is RejectLoanResponse
 */

// Example JSON:
// {
//    "rejectedOnDate": "07 March 2024",
//    "note": "Customer no longer need loan",
//    "locale": "en",
//    "dateFormat": "dd MMMM yyyy"
//}
message RejectLoanRequest {
  string rejected_on_date = 1 [json_name = "rejectedOnDate"];
  string note = 2 [json_name = "note"];
  string locale = 3 [json_name = "locale"];
  string date_format = 4 [json_name = "dateFormat"];
}

// Example JSON:
//  {
//    "officeId": 1,
//    "clientId": 93,
//    "loanId": 150,
//    "resourceId": 150,
//    "changes": {
//        "status": {
//            "id": 500,
//            "code": "loanStatusType.rejected",
//            "value": "Rejected",
//            "pendingApproval": false,
//            "waitingForDisbursal": false,
//            "active": false,
//            "closedObligationsMet": false,
//            "closedWrittenOff": false,
//            "closedRescheduled": false,
//            "closed": false,
//            "overpaid": false,
//            "transferInProgress": false,
//            "transferOnHold": false,
//            "underTransfer": false
//        },
//        "rejectedOnDate": [
//            2024,
//            3,
//            8
//        ],
//        "closedOnDate": [
//            2024,
//            3,
//            8
//        ]
//    }
//   }
message RejectLoanResponse {
  message Changes {
    types.LoanStatus status = 1 [json_name = "status"];
    repeated int32 rejected_on_date = 2 [json_name = "rejectedOnDate"];
    repeated int32 closed_on_date = 3 [json_name = "closedOnDate"];
  }
  int32 office_id = 1 [json_name = "officeId"];
  int32 client_id = 2 [json_name = "clientId"];
  int32 loan_id = 3 [json_name = "loanId"];
  int32 resource_id = 4 [json_name = "resourceId"];
  Changes changes = 5 [json_name = "changes"];
}
