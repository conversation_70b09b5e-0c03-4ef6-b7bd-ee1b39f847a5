syntax = "proto3";

package vendors.finflux;

option go_package = "github.com/epifi/gamma/api/vendors/finflux";
option java_package = "com.github.epifi.gamma.api.vendors.finflux";

/*
 * Disburse Loan API - Marks a loan as disbursed in the Finflux system
 * Request body is DisburseLoanRequest
 * Response body is empty
 */

// Example JSON:
//    {
//      "transactionAmount": 70000,
//      "actualDisbursementDate": "2023-10-14",
//      "dateFormat": "yyyy-MM-dd",
//      "locale": "en",
//      "paymentTypeId": 1,
//      "note": "Disbursement notes",
//      "bankAccountDetailId": null
//    }
message DisburseLoanRequest {
  // (mandatory)
  double transaction_amount = 1 [json_name = "transactionAmount"];
  // (mandatory)
  string actual_disbursement_date = 2 [json_name = "actualDisbursementDate"];
  // (mandatory)
  string date_format = 3 [json_name = "dateFormat"];
  // (mandatory)
  string locale = 4 [json_name = "locale"];
  // (mandatory)
  int32 payment_type_id = 5 [json_name = "paymentTypeId"];
  // (optional)
  string note = 6 [json_name = "note"];
  reserved 7; // reserved for "bankAccountDetailId" as the structure is not known
}
