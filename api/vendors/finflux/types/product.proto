syntax = "proto3";

package vendors.finflux.types;

import "api/vendors/finflux/types/common.proto";

option go_package = "github.com/epifi/gamma/api/vendors/finflux/types";
option java_package = "com.github.epifi.gamma.api.vendors.finflux.types";


// Example JSON:
//  {
//    "loanProductId": 3,
//    "loanProductName": "LL - A2L - V1",
//    "isLoanProductLinkedToFloatingRate": false,
//    "fundId": 1,
//    "fundName": "LL",
//    "currency": {
//        "code": "INR",
//        "name": "Indian Rupee",
//        "decimalPlaces": 2,
//        "inMultiplesOf": 0,
//        "displaySymbol": "₹",
//        "nameCode": "currency.INR",
//        "displayLabel": "Indian Rupee (₹)"
//    },
//    "principal": 10000,
//    "approvedPrincipal": 10000,
//    "proposedPrincipal": 10000,
//    "termFrequency": 12,
//    "termPeriodFrequencyType": {
//        "id": 2,
//        "code": "repaymentFrequency.periodFrequencyType.months",
//        "value": "Months"
//    },
//    "numberOfRepayments": 12,
//    "repaymentEvery": 1,
//    "repaymentFrequencyType": {
//        "id": 2,
//        "code": "repaymentFrequency.periodFrequencyType.months",
//        "value": "Months"
//    },
//    "interestRatePerPeriod": 20,
//    "interestRatesListPerPeriod": [],
//    "interestRateFrequencyType": {
//        "id": 3,
//        "code": "interestRateFrequency.periodFrequencyType.years",
//        "value": "Per year"
//    },
//    "annualInterestRate": 20,
//    "isFloatingInterestRate": false,
//    "interestRateDifferential": 0,
//    "allowUpfrontCollection": false,
//    "amortizationType": {
//        "id": 1,
//        "code": "amortizationType.equal.installments",
//        "value": "Equal installments"
//    },
//    "interestType": {
//        "id": 0,
//        "code": "interestType.declining.balance",
//        "value": "Declining Balance"
//    },
//    "interestCalculationPeriodType": {
//        "id": 0,
//        "code": "interestCalculationPeriodType.daily",
//        "value": "Daily"
//    },
//    "allowPartialPeriodInterestCalcualtion": false,
//    "inArrearsTolerance": 0,
//    "transactionProcessingStrategyId": 6,
//    "graceOnPrincipalPayment": 0,
//    "isCancellationAllowed": false,
//    "charges": [
//        {
//            "chargeId": 1,
//            "name": "Processing Fees",
//            "chargeTimeType": {
//                "id": 1,
//                "code": "chargeTimeType.disbursement",
//                "value": "Disbursement"
//            },
//            "chargeCalculationType": {
//                "id": 2,
//                "code": "chargeCalculationType.percent.of.amount",
//                "value": "% Approved Amount"
//            },
//            "percentage": 4.72,
//            "currency": {
//                "code": "INR",
//                "name": "Indian Rupee",
//                "decimalPlaces": 2,
//                "displaySymbol": "â‚¹",
//                "nameCode": "currency.INR",
//                "displayLabel": "Indian Rupee (â‚¹)"
//            },
//            "amount": 4.72,
//            "amountPaid": 0,
//            "amountWaived": 0,
//            "amountWrittenOff": 0,
//            "amountOutstanding": 4.72,
//            "amountOrPercentage": 4.72,
//            "penalty": false,
//            "chargePaymentMode": {
//                "id": 0,
//                "code": "chargepaymentmode.regular",
//                "value": "Regular"
//            },
//            "paid": false,
//            "waived": false,
//            "chargePayable": false,
//            "isGlimCharge": false,
//            "isCapitalized": false,
//            "taxGroupId": 2,
//            "isMandatory": false,
//            "isSlabBased": false,
//            "isAmountNonEditable": false,
//            "isUpdateThroughDiscount": false,
//            "isAccruable": false,
//            "canLendCharge": false,
//            "canAddChargeToPrincipalForComputation": false
//        }
//    ],
//    "productOptions": [
//        {
//            "id": 3,
//            "name": "LL - A2L - V1",
//            "shortName": "LL - A2L",
//            "includeInBorrowerCycle": false,
//            "isLinkedToFloatingInterestRates": false,
//            "defaultDifferentialLendingRate": 0,
//            "isLockInterestPeriod": false,
//            "isFloatingInterestRateCalculationAllowed": false,
//            "allowVariableInstallments": false,
//            "isInterestRecalculationEnabled": false,
//            "canDefineInstallmentAmount": false,
//            "adjustFirstEMIAmount": false,
//            "adjustInterestForRounding": false,
//            "principalVariationsForBorrowerCycle": [],
//            "interestRateVariationsForBorrowerCycle": [],
//            "numberOfRepaymentVariationsForBorrowerCycle": [],
//            "canUseForTopup": false,
//            "isAllowedForColending": false,
//            "holdGuaranteeFunds": false,
//            "accountMovesOutOfNPAOnlyOnArrearsCompletion": false,
//            "closeLoanOnOverpayment": false,
//            "syncExpectedWithDisbursementDate": false,
//            "isEmiBasedOnDisbursements": false,
//            "stopLoanProcessingOnNpa": false,
//            "allowDisbursementToGroupBankAccounts": false,
//            "excludeAdjustedRoundedAmountWithInterest": false,
//            "precloseEmiRounding": false,
//            "considerTenureForIRRCalculation": false,
//            "isOverdueAccountingEnabled": false,
//            "isPostIRDEnabled": false,
//            "isDpConfigured": false,
//            "brokenPeriodInterestCollectAtDisbursement": false,
//            "isUpfrontInterestEnabled": false,
//            "isInterestRateDiscountingEnabled": false,
//            "isLoanDPDChargesApplicable": false,
//            "isPartialInterestCalculationAllowed": false,
//            "isPartReleaseAllowed": false,
//            "isInterestTypeConvertable": false
//        },
//        {
//            "id": 6,
//            "name": "LL - A2L - V2",
//            "shortName": "LL-A2LV2",
//            "includeInBorrowerCycle": false,
//            "isLinkedToFloatingInterestRates": false,
//            "defaultDifferentialLendingRate": 0,
//            "isLockInterestPeriod": false,
//            "isFloatingInterestRateCalculationAllowed": false,
//            "allowVariableInstallments": false,
//            "isInterestRecalculationEnabled": false,
//            "canDefineInstallmentAmount": false,
//            "adjustFirstEMIAmount": false,
//            "adjustInterestForRounding": false,
//            "principalVariationsForBorrowerCycle": [],
//            "interestRateVariationsForBorrowerCycle": [],
//            "numberOfRepaymentVariationsForBorrowerCycle": [],
//            "canUseForTopup": false,
//            "isAllowedForColending": false,
//            "holdGuaranteeFunds": false,
//            "accountMovesOutOfNPAOnlyOnArrearsCompletion": false,
//            "closeLoanOnOverpayment": false,
//            "syncExpectedWithDisbursementDate": false,
//            "isEmiBasedOnDisbursements": false,
//            "stopLoanProcessingOnNpa": false,
//            "allowDisbursementToGroupBankAccounts": false,
//            "excludeAdjustedRoundedAmountWithInterest": false,
//            "precloseEmiRounding": false,
//            "considerTenureForIRRCalculation": false,
//            "isOverdueAccountingEnabled": false,
//            "isPostIRDEnabled": false,
//            "isDpConfigured": false,
//            "brokenPeriodInterestCollectAtDisbursement": false,
//            "isUpfrontInterestEnabled": false,
//            "isInterestRateDiscountingEnabled": false,
//            "isLoanDPDChargesApplicable": false,
//            "isPartialInterestCalculationAllowed": false,
//            "isPartReleaseAllowed": false,
//            "isInterestTypeConvertable": false
//        },
//        {
//            "id": 4,
//            "name": "LL - IS - V1",
//            "shortName": "LL - IS",
//            "includeInBorrowerCycle": false,
//            "isLinkedToFloatingInterestRates": false,
//            "defaultDifferentialLendingRate": 0,
//            "isLockInterestPeriod": false,
//            "isFloatingInterestRateCalculationAllowed": false,
//            "allowVariableInstallments": false,
//            "isInterestRecalculationEnabled": false,
//            "canDefineInstallmentAmount": false,
//            "adjustFirstEMIAmount": false,
//            "adjustInterestForRounding": false,
//            "principalVariationsForBorrowerCycle": [],
//            "interestRateVariationsForBorrowerCycle": [],
//            "numberOfRepaymentVariationsForBorrowerCycle": [],
//            "canUseForTopup": false,
//            "isAllowedForColending": false,
//            "holdGuaranteeFunds": false,
//            "accountMovesOutOfNPAOnlyOnArrearsCompletion": false,
//            "closeLoanOnOverpayment": false,
//            "syncExpectedWithDisbursementDate": false,
//            "isEmiBasedOnDisbursements": false,
//            "stopLoanProcessingOnNpa": false,
//            "allowDisbursementToGroupBankAccounts": false,
//            "excludeAdjustedRoundedAmountWithInterest": false,
//            "precloseEmiRounding": false,
//            "considerTenureForIRRCalculation": false,
//            "isOverdueAccountingEnabled": false,
//            "isPostIRDEnabled": false,
//            "isDpConfigured": false,
//            "brokenPeriodInterestCollectAtDisbursement": false,
//            "isUpfrontInterestEnabled": false,
//            "isInterestRateDiscountingEnabled": false,
//            "isLoanDPDChargesApplicable": false,
//            "isPartialInterestCalculationAllowed": false,
//            "isPartReleaseAllowed": false,
//            "isInterestTypeConvertable": false
//        },
//        {
//            "id": 9,
//            "name": "LLIS-Sharath",
//            "shortName": "LLIS-Sha",
//            "includeInBorrowerCycle": false,
//            "isLinkedToFloatingInterestRates": false,
//            "defaultDifferentialLendingRate": 0,
//            "isLockInterestPeriod": false,
//            "isFloatingInterestRateCalculationAllowed": false,
//            "allowVariableInstallments": false,
//            "isInterestRecalculationEnabled": false,
//            "canDefineInstallmentAmount": false,
//            "adjustFirstEMIAmount": false,
//            "adjustInterestForRounding": false,
//            "principalVariationsForBorrowerCycle": [],
//            "interestRateVariationsForBorrowerCycle": [],
//            "numberOfRepaymentVariationsForBorrowerCycle": [],
//            "canUseForTopup": false,
//            "isAllowedForColending": false,
//            "holdGuaranteeFunds": false,
//            "accountMovesOutOfNPAOnlyOnArrearsCompletion": false,
//            "closeLoanOnOverpayment": false,
//            "syncExpectedWithDisbursementDate": false,
//            "isEmiBasedOnDisbursements": false,
//            "stopLoanProcessingOnNpa": false,
//            "allowDisbursementToGroupBankAccounts": false,
//            "excludeAdjustedRoundedAmountWithInterest": false,
//            "precloseEmiRounding": false,
//            "considerTenureForIRRCalculation": false,
//            "isOverdueAccountingEnabled": false,
//            "isPostIRDEnabled": false,
//            "isDpConfigured": false,
//            "brokenPeriodInterestCollectAtDisbursement": false,
//            "isUpfrontInterestEnabled": false,
//            "isInterestRateDiscountingEnabled": false,
//            "isLoanDPDChargesApplicable": false,
//            "isPartialInterestCalculationAllowed": false,
//            "isPartReleaseAllowed": false,
//            "isInterestTypeConvertable": false
//        },
//        {
//            "id": 1,
//            "name": "Test Bullet Loan",
//            "shortName": "PL_BL",
//            "includeInBorrowerCycle": false,
//            "isLinkedToFloatingInterestRates": false,
//            "defaultDifferentialLendingRate": 0,
//            "isLockInterestPeriod": false,
//            "isFloatingInterestRateCalculationAllowed": false,
//            "allowVariableInstallments": false,
//            "isInterestRecalculationEnabled": false,
//            "canDefineInstallmentAmount": false,
//            "adjustFirstEMIAmount": false,
//            "adjustInterestForRounding": false,
//            "principalVariationsForBorrowerCycle": [],
//            "interestRateVariationsForBorrowerCycle": [],
//            "numberOfRepaymentVariationsForBorrowerCycle": [],
//            "canUseForTopup": false,
//            "isAllowedForColending": false,
//            "holdGuaranteeFunds": false,
//            "accountMovesOutOfNPAOnlyOnArrearsCompletion": false,
//            "closeLoanOnOverpayment": false,
//            "syncExpectedWithDisbursementDate": false,
//            "isEmiBasedOnDisbursements": false,
//            "stopLoanProcessingOnNpa": false,
//            "allowDisbursementToGroupBankAccounts": false,
//            "excludeAdjustedRoundedAmountWithInterest": false,
//            "precloseEmiRounding": false,
//            "considerTenureForIRRCalculation": false,
//            "isOverdueAccountingEnabled": false,
//            "isPostIRDEnabled": false,
//            "isDpConfigured": false,
//            "brokenPeriodInterestCollectAtDisbursement": false,
//            "isUpfrontInterestEnabled": false,
//            "isInterestRateDiscountingEnabled": false,
//            "isLoanDPDChargesApplicable": false,
//            "isPartialInterestCalculationAllowed": false,
//            "isPartReleaseAllowed": false,
//            "isInterestTypeConvertable": false
//        },
//        {
//            "id": 2,
//            "name": "Test Personal Loan",
//            "shortName": "PL_Reg",
//            "includeInBorrowerCycle": false,
//            "isLinkedToFloatingInterestRates": false,
//            "defaultDifferentialLendingRate": 0,
//            "isLockInterestPeriod": false,
//            "isFloatingInterestRateCalculationAllowed": false,
//            "allowVariableInstallments": false,
//            "isInterestRecalculationEnabled": false,
//            "canDefineInstallmentAmount": false,
//            "adjustFirstEMIAmount": false,
//            "adjustInterestForRounding": false,
//            "principalVariationsForBorrowerCycle": [],
//            "interestRateVariationsForBorrowerCycle": [],
//            "numberOfRepaymentVariationsForBorrowerCycle": [],
//            "canUseForTopup": false,
//            "isAllowedForColending": false,
//            "holdGuaranteeFunds": false,
//            "accountMovesOutOfNPAOnlyOnArrearsCompletion": false,
//            "closeLoanOnOverpayment": false,
//            "syncExpectedWithDisbursementDate": false,
//            "isEmiBasedOnDisbursements": false,
//            "stopLoanProcessingOnNpa": false,
//            "allowDisbursementToGroupBankAccounts": false,
//            "excludeAdjustedRoundedAmountWithInterest": false,
//            "precloseEmiRounding": false,
//            "considerTenureForIRRCalculation": false,
//            "isOverdueAccountingEnabled": false,
//            "isPostIRDEnabled": false,
//            "isDpConfigured": false,
//            "brokenPeriodInterestCollectAtDisbursement": false,
//            "isUpfrontInterestEnabled": false,
//            "isInterestRateDiscountingEnabled": false,
//            "isLoanDPDChargesApplicable": false,
//            "isPartialInterestCalculationAllowed": false,
//            "isPartReleaseAllowed": false,
//            "isInterestTypeConvertable": false
//        },
//        {
//            "id": 10,
//            "name": "test-2",
//            "shortName": "test2",
//            "includeInBorrowerCycle": false,
//            "isLinkedToFloatingInterestRates": false,
//            "defaultDifferentialLendingRate": 0,
//            "isLockInterestPeriod": false,
//            "isFloatingInterestRateCalculationAllowed": false,
//            "allowVariableInstallments": false,
//            "isInterestRecalculationEnabled": false,
//            "canDefineInstallmentAmount": false,
//            "adjustFirstEMIAmount": false,
//            "adjustInterestForRounding": false,
//            "principalVariationsForBorrowerCycle": [],
//            "interestRateVariationsForBorrowerCycle": [],
//            "numberOfRepaymentVariationsForBorrowerCycle": [],
//            "canUseForTopup": false,
//            "isAllowedForColending": false,
//            "holdGuaranteeFunds": false,
//            "accountMovesOutOfNPAOnlyOnArrearsCompletion": false,
//            "closeLoanOnOverpayment": false,
//            "syncExpectedWithDisbursementDate": false,
//            "isEmiBasedOnDisbursements": false,
//            "stopLoanProcessingOnNpa": false,
//            "allowDisbursementToGroupBankAccounts": false,
//            "excludeAdjustedRoundedAmountWithInterest": false,
//            "precloseEmiRounding": false,
//            "considerTenureForIRRCalculation": false,
//            "isOverdueAccountingEnabled": false,
//            "isPostIRDEnabled": false,
//            "isDpConfigured": false,
//            "brokenPeriodInterestCollectAtDisbursement": false,
//            "isUpfrontInterestEnabled": false,
//            "isInterestRateDiscountingEnabled": false,
//            "isLoanDPDChargesApplicable": false,
//            "isPartialInterestCalculationAllowed": false,
//            "isPartReleaseAllowed": false,
//            "isInterestTypeConvertable": false
//        },
//        {
//            "id": 7,
//            "name": "test-product",
//            "shortName": "test1",
//            "includeInBorrowerCycle": false,
//            "isLinkedToFloatingInterestRates": false,
//            "defaultDifferentialLendingRate": 0,
//            "isLockInterestPeriod": false,
//            "isFloatingInterestRateCalculationAllowed": false,
//            "allowVariableInstallments": false,
//            "isInterestRecalculationEnabled": false,
//            "canDefineInstallmentAmount": false,
//            "adjustFirstEMIAmount": false,
//            "adjustInterestForRounding": false,
//            "principalVariationsForBorrowerCycle": [],
//            "interestRateVariationsForBorrowerCycle": [],
//            "numberOfRepaymentVariationsForBorrowerCycle": [],
//            "canUseForTopup": false,
//            "isAllowedForColending": false,
//            "holdGuaranteeFunds": false,
//            "accountMovesOutOfNPAOnlyOnArrearsCompletion": false,
//            "closeLoanOnOverpayment": false,
//            "syncExpectedWithDisbursementDate": false,
//            "isEmiBasedOnDisbursements": false,
//            "stopLoanProcessingOnNpa": false,
//            "allowDisbursementToGroupBankAccounts": false,
//            "excludeAdjustedRoundedAmountWithInterest": false,
//            "precloseEmiRounding": false,
//            "considerTenureForIRRCalculation": false,
//            "isOverdueAccountingEnabled": false,
//            "isPostIRDEnabled": false,
//            "isDpConfigured": false,
//            "brokenPeriodInterestCollectAtDisbursement": false,
//            "isUpfrontInterestEnabled": false,
//            "isInterestRateDiscountingEnabled": false,
//            "isLoanDPDChargesApplicable": false,
//            "isPartialInterestCalculationAllowed": false,
//            "isPartReleaseAllowed": false,
//            "isInterestTypeConvertable": false
//        }
//    ],
//    "fundOptions": [
//        {
//            "id": 1,
//            "name": "LL",
//            "fundSource": {
//                "id": 0,
//                "isActive": false,
//                "mandatory": false
//            },
//            "fundCategory": {
//                "id": 0,
//                "isActive": false,
//                "mandatory": false
//            },
//            "facilityType": {
//                "id": 36,
//                "name": "Own",
//                "isActive": false,
//                "mandatory": false
//            },
//            "fundRepaymentFrequency": {
//                "id": 0,
//                "isActive": false,
//                "mandatory": false
//            },
//            "tenure": 0,
//            "tenureFrequency": {
//                "id": 0,
//                "code": "periodFrequencyType.days",
//                "value": "DAYS"
//            },
//            "isActive": true,
//            "isLoanAssigned": false,
//            "fundLoanPurposeData": []
//        }
//    ],
//    "termFrequencyTypeOptions": [
//        {
//            "id": 0,
//            "code": "loanTermFrequency.periodFrequencyType.days",
//            "value": "Days"
//        },
//        {
//            "id": 1,
//            "code": "loanTermFrequency.periodFrequencyType.weeks",
//            "value": "Weeks"
//        },
//        {
//            "id": 2,
//            "code": "loanTermFrequency.periodFrequencyType.months",
//            "value": "Months"
//        },
//        {
//            "id": 3,
//            "code": "loanTermFrequency.periodFrequencyType.years",
//            "value": "Years"
//        }
//    ],
//    "repaymentFrequencyTypeOptions": [
//        {
//            "id": 0,
//            "code": "repaymentFrequency.periodFrequencyType.days",
//            "value": "Days"
//        },
//        {
//            "id": 1,
//            "code": "repaymentFrequency.periodFrequencyType.weeks",
//            "value": "Weeks"
//        },
//        {
//            "id": 2,
//            "code": "repaymentFrequency.periodFrequencyType.months",
//            "value": "Months"
//        }
//    ],
//    "repaymentFrequencyNthDayTypeOptions": [
//        {
//            "id": 1,
//            "code": "repaymentFrequency.nthDayType.one",
//            "value": "first"
//        },
//        {
//            "id": 2,
//            "code": "repaymentFrequency.nthDayType.two",
//            "value": "second"
//        },
//        {
//            "id": 3,
//            "code": "repaymentFrequency.nthDayType.three",
//            "value": "third"
//        },
//        {
//            "id": 4,
//            "code": "repaymentFrequency.nthDayType.four",
//            "value": "fourth"
//        },
//        {
//            "id": -1,
//            "code": "repaymentFrequency.nthDayType.last",
//            "value": "last"
//        },
//        {
//            "id": -2,
//            "code": "repaymentFrequency.nthDayType.onday",
//            "value": "onday"
//        }
//    ],
//    "repaymentFrequencyDaysOfWeekTypeOptions": [
//        {
//            "id": 7,
//            "code": "repaymentFrequency.weekDayType.sunday",
//            "value": "SUNDAY"
//        },
//        {
//            "id": 1,
//            "code": "repaymentFrequency.weekDayType.monday",
//            "value": "MONDAY"
//        },
//        {
//            "id": 2,
//            "code": "repaymentFrequency.weekDayType.tuesday",
//            "value": "TUESDAY"
//        },
//        {
//            "id": 3,
//            "code": "repaymentFrequency.weekDayType.wednesday",
//            "value": "WEDNESDAY"
//        },
//        {
//            "id": 4,
//            "code": "repaymentFrequency.weekDayType.thursday",
//            "value": "THURSDAY"
//        },
//        {
//            "id": 5,
//            "code": "repaymentFrequency.weekDayType.friday",
//            "value": "FRIDAY"
//        },
//        {
//            "id": 6,
//            "code": "repaymentFrequency.weekDayType.saturday",
//            "value": "SATURDAY"
//        }
//    ],
//    "interestRateFrequencyTypeOptions": [
//        {
//            "id": 2,
//            "code": "interestRateFrequency.periodFrequencyType.months",
//            "value": "Per month"
//        },
//        {
//            "id": 3,
//            "code": "interestRateFrequency.periodFrequencyType.years",
//            "value": "Per year"
//        }
//    ],
//    "amortizationTypeOptions": [
//        {
//            "id": 1,
//            "code": "amortizationType.equal.installments",
//            "value": "Equal installments"
//        },
//        {
//            "id": 0,
//            "code": "amortizationType.equal.principal",
//            "value": "Equal principal payments"
//        }
//    ],
//    "interestTypeOptions": [
//        {
//            "id": 1,
//            "code": "interestType.flat",
//            "value": "Flat"
//        },
//        {
//            "id": 0,
//            "code": "interestType.declining.balance",
//            "value": "Declining Balance"
//        }
//    ],
//    "interestCalculationPeriodTypeOptions": [
//        {
//            "id": 0,
//            "code": "interestCalculationPeriodType.daily",
//            "value": "Daily"
//        },
//        {
//            "id": 1,
//            "code": "interestCalculationPeriodType.same.as.repayment.period",
//            "value": "Same as repayment period"
//        }
//    ],
//    "transactionProcessingStrategyOptions": [
//        {
//            "id": 1,
//            "code": "mifos-standard-strategy",
//            "name": "Penalties, Fees, Interest, Principal order"
//        },
//        {
//            "id": 4,
//            "code": "rbi-india-strategy",
//            "name": "Overdue/Due Fee/Int,Principal"
//        },
//        {
//            "id": 5,
//            "code": "principal-interest-penalties-fees-order-strategy",
//            "name": "Principal, Interest, Penalties, Fees Order"
//        },
//        {
//            "id": 6,
//            "code": "interest-principal-penalties-fees-order-strategy",
//            "name": "Interest, Principal, Penalties, Fees Order"
//        },
//        {
//            "id": 8,
//            "code": "overdue-interest-principal-due-penalties-fees-order-strategy",
//            "name": "Overdue/Int/Principal,Fees"
//        },
//        {
//            "id": 9,
//            "code": "overdue-fee-interest-adv-principal-order-strategy",
//            "name": "Overdue/fee/int,Adv/principal"
//        },
//        {
//            "id": 10,
//            "code": "adv-principal-penalty-fee-int-principal",
//            "name": "Adv/Principal,Penalties,Fees,Interest,Principal order"
//        },
//        {
//            "id": 12,
//            "code": "overdue-principal-interest-due-penalties-fees-order-strategy",
//            "name": "Overdue/Principal/Interest,Fees"
//        },
//        {
//            "id": 13,
//            "code": "overdue-due-interest-penalties-or-fees-principal-order-strategy",
//            "name": " Overdue/Due Interest,Overdue/Due Penalties/Fees, Principal Order"
//        },
//        {
//            "id": 14,
//            "code": "overdue-due-adv-principal-or-interest-overdue-due-penalties-fees-order-strategy",
//            "name": "Overdue/Due/Adv Principal/Interest, Overdue/Due Penalties, Fees Order"
//        },
//        {
//            "id": 15,
//            "code": "overdue-due-adv-principal-or-interest-overdue-due-penalties-or-fees-order-strategy",
//            "name": "Overdue/Due/Adv Principal/Interest, Overdue/Due Penalties/Fees Order"
//        },
//        {
//            "id": 16,
//            "code": "adv-principal-and-interest-principal-penalty-fee-order-strategy",
//            "name": "Adv/Principal,Interest,Principal,Penalties,Fees"
//        },
//        {
//            "id": 17,
//            "code": "adv-principal-overdue-interest-principal-and-overdue-penalties-and-overdue-fees-order-strategy",
//            "name": "Adv/Principal, Overdue/Due Interest/Principal, Overdue/Due Penalties, Overdue/Due Fess order"
//        },
//        {
//            "id": 18,
//            "code": "overdue-interest-principal-and-penalties-and-fees-adv-principal-order-strategy",
//            "name": "Overdue/Due Principal,Interest,Overdue/Due Penalties/Fess,Adv/Principal Order"
//        },
//        {
//            "id": 19,
//            "code": "proportionate-penalty-fees-emi(interest,principal)",
//            "name": "proportionate-penalty-fees-emi(interest,principal)"
//        }
//    ],
//    "chargeOptions": [
//        {
//            "id": 3,
//            "name": "Mandate Representation Charge",
//            "active": true,
//            "penalty": false,
//            "currency": {
//                "code": "INR",
//                "name": "Indian Rupee",
//                "decimalPlaces": 2,
//                "displaySymbol": "â‚¹",
//                "nameCode": "currency.INR",
//                "displayLabel": "Indian Rupee (â‚¹)"
//            },
//            "amount": 118,
//            "chargeTimeType": {
//                "id": 2,
//                "code": "chargeTimeType.specifiedDueDate",
//                "value": "Specified due date"
//            },
//            "chargeAppliesTo": {
//                "id": 1,
//                "code": "chargeAppliesTo.loan",
//                "value": "Loan"
//            },
//            "chargeCalculationType": {
//                "id": 1,
//                "code": "chargeCalculationType.flat",
//                "value": "Flat"
//            },
//            "chargePaymentMode": {
//                "id": 0,
//                "code": "chargepaymentmode.regular",
//                "value": "Regular"
//            },
//            "taxGroup": {
//                "id": 2,
//                "name": "GST - New",
//                "isActive": true
//            },
//            "percentageType": {
//                "id": 1,
//                "code": "ChargePercentageType.flat",
//                "value": "Flat"
//            },
//            "percentagePeriodType": {
//                "id": 1,
//                "code": "ChargePercentagePeriodType.daily",
//                "value": "Daily"
//            },
//            "emiRoundingGoalSeek": false,
//            "isGlimCharge": false,
//            "glimChargeCalculation": {
//                "id": 0,
//                "code": "glimChargeCalculationType.invalid",
//                "value": "Invalid"
//            },
//            "isCapitalized": false,
//            "isSlabBased": false,
//            "isCollectedAsCash": false,
//            "percentageCalculationDaysInYearType": {
//                "id": 1,
//                "code": "DaysInYearType.actual",
//                "value": "Actual"
//            },
//            "isChargeCalculationInclusiveOfTax": true,
//            "isUpdateThroughDiscount": false,
//            "businessIdentificationCode": "CB",
//            "accountingType": "CASH_BASED",
//            "daysInMonthType": {
//                "id": 0,
//                "code": "DaysInMonthType.invalid",
//                "value": "Invalid"
//            },
//            "chargeType": {
//                "id": 1,
//                "code": "USER_CHARGE",
//                "value": "User Charge"
//            },
//            "recognitionType": {
//                "id": 1,
//                "code": "SINGLE",
//                "value": "Single"
//            },
//            "showCharge": true,
//            "isChartDefined": false
//        },
//        {
//            "id": 2,
//            "name": "NACH Bounce Charge",
//            "active": true,
//            "penalty": false,
//            "currency": {
//                "code": "INR",
//                "name": "Indian Rupee",
//                "decimalPlaces": 2,
//                "displaySymbol": "â‚¹",
//                "nameCode": "currency.INR",
//                "displayLabel": "Indian Rupee (â‚¹)"
//            },
//            "amount": 118,
//            "chargeTimeType": {
//                "id": 2,
//                "code": "chargeTimeType.specifiedDueDate",
//                "value": "Specified due date"
//            },
//            "chargeAppliesTo": {
//                "id": 1,
//                "code": "chargeAppliesTo.loan",
//                "value": "Loan"
//            },
//            "chargeCalculationType": {
//                "id": 1,
//                "code": "chargeCalculationType.flat",
//                "value": "Flat"
//            },
//            "chargePaymentMode": {
//                "id": 0,
//                "code": "chargepaymentmode.regular",
//                "value": "Regular"
//            },
//            "taxGroup": {
//                "id": 2,
//                "name": "GST - New",
//                "isActive": true
//            },
//            "percentageType": {
//                "id": 1,
//                "code": "ChargePercentageType.flat",
//                "value": "Flat"
//            },
//            "percentagePeriodType": {
//                "id": 1,
//                "code": "ChargePercentagePeriodType.daily",
//                "value": "Daily"
//            },
//            "emiRoundingGoalSeek": false,
//            "isGlimCharge": false,
//            "glimChargeCalculation": {
//                "id": 0,
//                "code": "glimChargeCalculationType.invalid",
//                "value": "Invalid"
//            },
//            "isCapitalized": false,
//            "isSlabBased": false,
//            "isCollectedAsCash": false,
//            "percentageCalculationDaysInYearType": {
//                "id": 1,
//                "code": "DaysInYearType.actual",
//                "value": "Actual"
//            },
//            "isChargeCalculationInclusiveOfTax": true,
//            "isUpdateThroughDiscount": false,
//            "businessIdentificationCode": "CB",
//            "accountingType": "CASH_BASED",
//            "daysInMonthType": {
//                "id": 0,
//                "code": "DaysInMonthType.invalid",
//                "value": "Invalid"
//            },
//            "chargeType": {
//                "id": 1,
//                "code": "USER_CHARGE",
//                "value": "User Charge"
//            },
//            "recognitionType": {
//                "id": 1,
//                "code": "SINGLE",
//                "value": "Single"
//            },
//            "showCharge": true,
//            "isChartDefined": false
//        },
//        {
//            "id": 1,
//            "name": "Processing Fees",
//            "active": true,
//            "penalty": false,
//            "currency": {
//                "code": "INR",
//                "name": "Indian Rupee",
//                "decimalPlaces": 2,
//                "displaySymbol": "â‚¹",
//                "nameCode": "currency.INR",
//                "displayLabel": "Indian Rupee (â‚¹)"
//            },
//            "amount": 4.72,
//            "chargeTimeType": {
//                "id": 1,
//                "code": "chargeTimeType.disbursement",
//                "value": "Disbursement"
//            },
//            "chargeAppliesTo": {
//                "id": 1,
//                "code": "chargeAppliesTo.loan",
//                "value": "Loan"
//            },
//            "chargeCalculationType": {
//                "id": 2,
//                "code": "chargeCalculationType.percent.of.amount",
//                "value": "% Approved Amount"
//            },
//            "chargePaymentMode": {
//                "id": 0,
//                "code": "chargepaymentmode.regular",
//                "value": "Regular"
//            },
//            "taxGroup": {
//                "id": 2,
//                "name": "GST - New",
//                "isActive": true
//            },
//            "percentageType": {
//                "id": 1,
//                "code": "ChargePercentageType.flat",
//                "value": "Flat"
//            },
//            "percentagePeriodType": {
//                "id": 1,
//                "code": "ChargePercentagePeriodType.daily",
//                "value": "Daily"
//            },
//            "emiRoundingGoalSeek": false,
//            "isGlimCharge": false,
//            "glimChargeCalculation": {
//                "id": 0,
//                "code": "glimChargeCalculationType.invalid",
//                "value": "Invalid"
//            },
//            "isCapitalized": false,
//            "isSlabBased": false,
//            "isCollectedAsCash": false,
//            "percentageCalculationDaysInYearType": {
//                "id": 1,
//                "code": "DaysInYearType.actual",
//                "value": "Actual"
//            },
//            "isChargeCalculationInclusiveOfTax": true,
//            "isUpdateThroughDiscount": false,
//            "businessIdentificationCode": "PF",
//            "accountingType": "CASH_BASED",
//            "daysInMonthType": {
//                "id": 0,
//                "code": "DaysInMonthType.invalid",
//                "value": "Invalid"
//            },
//            "chargeType": {
//                "id": 1,
//                "code": "USER_CHARGE",
//                "value": "User Charge"
//            },
//            "recognitionType": {
//                "id": 1,
//                "code": "SINGLE",
//                "value": "Single"
//            },
//            "showCharge": true,
//            "isChartDefined": false
//        }
//    ],
//    "advanceEmiAdjustmentTypeOptions": [
//        {
//            "id": 1,
//            "code": "INITIAL INSTALLMENT",
//            "value": "INITIAL INSTALLMENT ADJUSTMENT"
//        },
//        {
//            "id": 2,
//            "code": "LAST INSTALLMENT",
//            "value": "LAST INSTALLMENT ADJUSTMENT"
//        },
//        {
//            "id": 3,
//            "code": "START OF THE TENURE WITH IMMEDIATE EMI",
//            "value": "START OF THE TENURE WITH IMMEDIATE EMI"
//        }
//    ],
//    "multiDisburseLoan": false,
//    "canDefineInstallmentAmount": false,
//    "canDisburse": false,
//    "canUseForTopup": false,
//    "isTopup": false,
//    "product": {
//        "id": 3,
//        "name": "LL - A2L - V1",
//        "shortName": "LL - A2L",
//        "fundId": 1,
//        "fundName": "LL",
//        "includeInBorrowerCycle": false,
//        "status": "loanProduct.active",
//        "currency": {
//            "code": "INR",
//            "name": "Indian Rupee",
//            "decimalPlaces": 2,
//            "inMultiplesOf": 0,
//            "displaySymbol": "₹",
//            "nameCode": "currency.INR",
//            "displayLabel": "Indian Rupee (₹)"
//        },
//        "principal": 10000,
//        "minPrincipal": 10000,
//        "maxPrincipal": 500000,
//        "numberOfRepayments": 12,
//        "minNumberOfRepayments": 1,
//        "maxNumberOfRepayments": 48,
//        "repaymentEvery": 1,
//        "repaymentFrequencyType": {
//            "id": 2,
//            "code": "repaymentFrequency.periodFrequencyType.months",
//            "value": "Months"
//        },
//        "interestRatePerPeriod": 20,
//        "minInterestRatePerPeriod": 14,
//        "maxInterestRatePerPeriod": 30,
//        "interestRateFrequencyType": {
//            "id": 3,
//            "code": "interestRateFrequency.periodFrequencyType.years",
//            "value": "Per year"
//        },
//        "annualInterestRate": 20,
//        "isFlatInterestRate": false,
//        "interestRatesListPerPeriod": [],
//        "isLinkedToFloatingInterestRates": false,
//        "defaultDifferentialLendingRate": 0,
//        "isLockInterestPeriod": false,
//        "isFloatingInterestRateCalculationAllowed": false,
//        "allowVariableInstallments": false,
//        "minimumGap": 0,
//        "maximumGap": 0,
//        "amortizationType": {
//            "id": 1,
//            "code": "amortizationType.equal.installments",
//            "value": "Equal installments"
//        },
//        "interestType": {
//            "id": 0,
//            "code": "interestType.declining.balance",
//            "value": "Declining Balance"
//        },
//        "interestCalculationPeriodType": {
//            "id": 0,
//            "code": "interestCalculationPeriodType.daily",
//            "value": "Daily"
//        },
//        "allowPartialPeriodInterestCalcualtion": false,
//        "inArrearsTolerance": 0,
//        "transactionProcessingStrategyId": 6,
//        "transactionProcessingStrategyName": "Interest, Principal, Penalties, Fees Order",
//        "graceOnPrincipalPayment": 0,
//        "overdueDaysForNPA": 90,
//        "daysInMonthType": {
//            "id": 30,
//            "code": "DaysInMonthType.days360",
//            "value": "30 Days"
//        },
//        "partialPeriodType": {
//            "id": 3,
//            "code": "partialPeriodType.actual",
//            "value": "Actual"
//        },
//        "daysInYearType": {
//            "id": 360,
//            "code": "DaysInYearType.days360",
//            "value": "360 Days"
//        },
//        "isInterestRecalculationEnabled": false,
//        "isMinDurationApplicableForAllDisbursements": false,
//        "canDefineInstallmentAmount": false,
//        "installmentAmountInMultiplesOf": 1,
//        "adjustFirstEMIAmount": false,
//        "adjustInterestForRounding": false,
//        "installmentCalculationPeriodType": {
//            "id": 1,
//            "code": "interestCalculationPeriodType.same.as.repayment.period",
//            "value": "Same as repayment period"
//        },
//        "isGlim": false,
//        "insuranceProviderDetails": [],
//        "charges": [
//            {
//                "id": 42,
//                "productLoanId": 3,
//                "chargeData": {
//                    "id": 1,
//                    "name": "Processing Fees",
//                    "active": true,
//                    "penalty": false,
//                    "currency": {
//                        "code": "INR",
//                        "name": "Indian Rupee",
//                        "decimalPlaces": 2,
//                        "displaySymbol": "â‚¹",
//                        "nameCode": "currency.INR",
//                        "displayLabel": "Indian Rupee (â‚¹)"
//                    },
//                    "amount": 4.72,
//                    "chargeTimeType": {
//                        "id": 1,
//                        "code": "chargeTimeType.disbursement",
//                        "value": "Disbursement"
//                    },
//                    "chargeAppliesTo": {
//                        "id": 1,
//                        "code": "chargeAppliesTo.loan",
//                        "value": "Loan"
//                    },
//                    "chargeCalculationType": {
//                        "id": 2,
//                        "code": "chargeCalculationType.percent.of.amount",
//                        "value": "% Approved Amount"
//                    },
//                    "chargePaymentMode": {
//                        "id": 0,
//                        "code": "chargepaymentmode.regular",
//                        "value": "Regular"
//                    },
//                    "taxGroup": {
//                        "id": 2,
//                        "name": "GST - New",
//                        "isActive": true
//                    },
//                    "percentageType": {
//                        "id": 1,
//                        "code": "ChargePercentageType.flat",
//                        "value": "Flat"
//                    },
//                    "percentagePeriodType": {
//                        "id": 1,
//                        "code": "ChargePercentagePeriodType.daily",
//                        "value": "Daily"
//                    },
//                    "emiRoundingGoalSeek": false,
//                    "isGlimCharge": false,
//                    "glimChargeCalculation": {
//                        "id": 0,
//                        "code": "glimChargeCalculationType.invalid",
//                        "value": "Invalid"
//                    },
//                    "slabs": [
//                        {
//                            "id": 0,
//                            "type": {
//                                "id": 0,
//                                "code": "slabChargeType.installment.invalid",
//                                "value": "INVALID"
//                            },
//                            "subSlabs": []
//                        }
//                    ],
//                    "isCapitalized": false,
//                    "isSlabBased": false,
//                    "isCollectedAsCash": false,
//                    "percentageCalculationDaysInYearType": {
//                        "id": 1,
//                        "code": "DaysInYearType.actual",
//                        "value": "Actual"
//                    },
//                    "isChargeCalculationInclusiveOfTax": true,
//                    "isUpdateThroughDiscount": false,
//                    "businessIdentificationCode": "PF",
//                    "accountingType": "CASH_BASED",
//                    "daysInMonthType": {
//                        "id": 0,
//                        "code": "DaysInMonthType.invalid",
//                        "value": "Invalid"
//                    },
//                    "chargeType": {
//                        "id": 1,
//                        "code": "USER_CHARGE",
//                        "value": "User Charge"
//                    },
//                    "recognitionType": {
//                        "id": 1,
//                        "code": "SINGLE",
//                        "value": "Single"
//                    },
//                    "showCharge": true,
//                    "isChartDefined": false
//                },
//                "isMandatory": false,
//                "isAmountNonEditable": false,
//                "canLendCharge": false,
//                "canAddChargeToPrincipalForComputation": false
//            },
//            {
//                "id": 43,
//                "productLoanId": 3,
//                "chargeData": {
//                    "id": 4,
//                    "name": "Late Fee - Penalty",
//                    "active": true,
//                    "penalty": true,
//                    "currency": {
//                        "code": "INR",
//                        "name": "Indian Rupee",
//                        "decimalPlaces": 2,
//                        "displaySymbol": "â‚¹",
//                        "nameCode": "currency.INR",
//                        "displayLabel": "Indian Rupee (â‚¹)"
//                    },
//                    "amount": 0.59,
//                    "chargeTimeType": {
//                        "id": 9,
//                        "code": "chargeTimeType.overdueInstallment",
//                        "value": "Overdue Fees"
//                    },
//                    "chargeAppliesTo": {
//                        "id": 1,
//                        "code": "chargeAppliesTo.loan",
//                        "value": "Loan"
//                    },
//                    "chargeCalculationType": {
//                        "id": 3,
//                        "code": "chargeCalculationType.percent.of.amount.and.interest",
//                        "value": "% Loan Amount + Interest"
//                    },
//                    "chargePaymentMode": {
//                        "id": 0,
//                        "code": "chargepaymentmode.regular",
//                        "value": "Regular"
//                    },
//                    "taxGroup": {
//                        "id": 2,
//                        "name": "GST - New",
//                        "isActive": true
//                    },
//                    "percentageType": {
//                        "id": 1,
//                        "code": "ChargePercentageType.flat",
//                        "value": "Flat"
//                    },
//                    "percentagePeriodType": {
//                        "id": 1,
//                        "code": "ChargePercentagePeriodType.daily",
//                        "value": "Daily"
//                    },
//                    "chargeOverdueData": {
//                        "id": 1,
//                        "gracePeriod": 0,
//                        "penaltyFreePeriod": 0,
//                        "graceType": {
//                            "id": 1,
//                            "code": "penaltyGraceType.first.overdue.installment",
//                            "value": "First overdue installment"
//                        },
//                        "applyChargeForBrokenPeriod": false,
//                        "isBasedOnOriginalSchedule": false,
//                        "considerOnlyPostedInterest": false,
//                        "calculateChargeOnCurrentOverdue": true,
//                        "stopChargeOnNPA": false,
//                        "overdueBasedOn": {
//                            "id": 0,
//                            "code": "overdueBasedOn.invalid",
//                            "value": "Invalid"
//                        },
//                        "overdueClassification": {
//                            "id": 1,
//                            "code": "overdueChargeClassification.standard",
//                            "value": "Standard",
//                            "systemCode": "Standard"
//                        }
//                    },
//                    "emiRoundingGoalSeek": false,
//                    "isGlimCharge": false,
//                    "glimChargeCalculation": {
//                        "id": 0,
//                        "code": "glimChargeCalculationType.invalid",
//                        "value": "Invalid"
//                    },
//                    "slabs": [
//                        {
//                            "id": 0,
//                            "type": {
//                                "id": 0,
//                                "code": "slabChargeType.installment.invalid",
//                                "value": "INVALID"
//                            },
//                            "subSlabs": []
//                        }
//                    ],
//                    "isCapitalized": false,
//                    "isSlabBased": false,
//                    "isCollectedAsCash": false,
//                    "percentageCalculationDaysInYearType": {
//                        "id": 1,
//                        "code": "DaysInYearType.actual",
//                        "value": "Actual"
//                    },
//                    "isChargeCalculationInclusiveOfTax": true,
//                    "isUpdateThroughDiscount": false,
//                    "businessIdentificationCode": "OD",
//                    "accountingType": "ACCRUAL_BASED",
//                    "daysInMonthType": {
//                        "id": 0,
//                        "code": "DaysInMonthType.invalid",
//                        "value": "Invalid"
//                    },
//                    "chargeType": {
//                        "id": 1,
//                        "code": "USER_CHARGE",
//                        "value": "User Charge"
//                    },
//                    "recognitionType": {
//                        "id": 1,
//                        "code": "SINGLE",
//                        "value": "Single"
//                    },
//                    "showCharge": true,
//                    "isChartDefined": false
//                },
//                "isMandatory": false,
//                "isAmountNonEditable": false,
//                "canLendCharge": false,
//                "canAddChargeToPrincipalForComputation": false
//            }
//        ],
//        "loanProductApplicableCharges": {
//            "chargeDataList": [
//                {
//                    "id": 3,
//                    "name": "Mandate Representation Charge",
//                    "active": true,
//                    "penalty": false,
//                    "currency": {
//                        "code": "INR",
//                        "name": "Indian Rupee",
//                        "decimalPlaces": 2,
//                        "displaySymbol": "â‚¹",
//                        "nameCode": "currency.INR",
//                        "displayLabel": "Indian Rupee (â‚¹)"
//                    },
//                    "amount": 118,
//                    "chargeTimeType": {
//                        "id": 2,
//                        "code": "chargeTimeType.specifiedDueDate",
//                        "value": "Specified due date"
//                    },
//                    "chargeAppliesTo": {
//                        "id": 1,
//                        "code": "chargeAppliesTo.loan",
//                        "value": "Loan"
//                    },
//                    "chargeCalculationType": {
//                        "id": 1,
//                        "code": "chargeCalculationType.flat",
//                        "value": "Flat"
//                    },
//                    "chargePaymentMode": {
//                        "id": 0,
//                        "code": "chargepaymentmode.regular",
//                        "value": "Regular"
//                    },
//                    "taxGroup": {
//                        "id": 2,
//                        "name": "GST - New",
//                        "isActive": true
//                    },
//                    "percentageType": {
//                        "id": 1,
//                        "code": "ChargePercentageType.flat",
//                        "value": "Flat"
//                    },
//                    "percentagePeriodType": {
//                        "id": 1,
//                        "code": "ChargePercentagePeriodType.daily",
//                        "value": "Daily"
//                    },
//                    "emiRoundingGoalSeek": false,
//                    "isGlimCharge": false,
//                    "glimChargeCalculation": {
//                        "id": 0,
//                        "code": "glimChargeCalculationType.invalid",
//                        "value": "Invalid"
//                    },
//                    "isCapitalized": false,
//                    "isSlabBased": false,
//                    "isCollectedAsCash": false,
//                    "percentageCalculationDaysInYearType": {
//                        "id": 1,
//                        "code": "DaysInYearType.actual",
//                        "value": "Actual"
//                    },
//                    "isChargeCalculationInclusiveOfTax": true,
//                    "isUpdateThroughDiscount": false,
//                    "businessIdentificationCode": "CB",
//                    "accountingType": "CASH_BASED",
//                    "daysInMonthType": {
//                        "id": 0,
//                        "code": "DaysInMonthType.invalid",
//                        "value": "Invalid"
//                    },
//                    "chargeType": {
//                        "id": 1,
//                        "code": "USER_CHARGE",
//                        "value": "User Charge"
//                    },
//                    "recognitionType": {
//                        "id": 1,
//                        "code": "SINGLE",
//                        "value": "Single"
//                    },
//                    "showCharge": true,
//                    "isChartDefined": false
//                },
//                {
//                    "id": 2,
//                    "name": "NACH Bounce Charge",
//                    "active": true,
//                    "penalty": false,
//                    "currency": {
//                        "code": "INR",
//                        "name": "Indian Rupee",
//                        "decimalPlaces": 2,
//                        "displaySymbol": "â‚¹",
//                        "nameCode": "currency.INR",
//                        "displayLabel": "Indian Rupee (â‚¹)"
//                    },
//                    "amount": 118,
//                    "chargeTimeType": {
//                        "id": 2,
//                        "code": "chargeTimeType.specifiedDueDate",
//                        "value": "Specified due date"
//                    },
//                    "chargeAppliesTo": {
//                        "id": 1,
//                        "code": "chargeAppliesTo.loan",
//                        "value": "Loan"
//                    },
//                    "chargeCalculationType": {
//                        "id": 1,
//                        "code": "chargeCalculationType.flat",
//                        "value": "Flat"
//                    },
//                    "chargePaymentMode": {
//                        "id": 0,
//                        "code": "chargepaymentmode.regular",
//                        "value": "Regular"
//                    },
//                    "taxGroup": {
//                        "id": 2,
//                        "name": "GST - New",
//                        "isActive": true
//                    },
//                    "percentageType": {
//                        "id": 1,
//                        "code": "ChargePercentageType.flat",
//                        "value": "Flat"
//                    },
//                    "percentagePeriodType": {
//                        "id": 1,
//                        "code": "ChargePercentagePeriodType.daily",
//                        "value": "Daily"
//                    },
//                    "emiRoundingGoalSeek": false,
//                    "isGlimCharge": false,
//                    "glimChargeCalculation": {
//                        "id": 0,
//                        "code": "glimChargeCalculationType.invalid",
//                        "value": "Invalid"
//                    },
//                    "isCapitalized": false,
//                    "isSlabBased": false,
//                    "isCollectedAsCash": false,
//                    "percentageCalculationDaysInYearType": {
//                        "id": 1,
//                        "code": "DaysInYearType.actual",
//                        "value": "Actual"
//                    },
//                    "isChargeCalculationInclusiveOfTax": true,
//                    "isUpdateThroughDiscount": false,
//                    "businessIdentificationCode": "CB",
//                    "accountingType": "CASH_BASED",
//                    "daysInMonthType": {
//                        "id": 0,
//                        "code": "DaysInMonthType.invalid",
//                        "value": "Invalid"
//                    },
//                    "chargeType": {
//                        "id": 1,
//                        "code": "USER_CHARGE",
//                        "value": "User Charge"
//                    },
//                    "recognitionType": {
//                        "id": 1,
//                        "code": "SINGLE",
//                        "value": "Single"
//                    },
//                    "showCharge": true,
//                    "isChartDefined": false
//                },
//                {
//                    "id": 1,
//                    "name": "Processing Fees",
//                    "active": true,
//                    "penalty": false,
//                    "currency": {
//                        "code": "INR",
//                        "name": "Indian Rupee",
//                        "decimalPlaces": 2,
//                        "displaySymbol": "â‚¹",
//                        "nameCode": "currency.INR",
//                        "displayLabel": "Indian Rupee (â‚¹)"
//                    },
//                    "amount": 4.72,
//                    "chargeTimeType": {
//                        "id": 1,
//                        "code": "chargeTimeType.disbursement",
//                        "value": "Disbursement"
//                    },
//                    "chargeAppliesTo": {
//                        "id": 1,
//                        "code": "chargeAppliesTo.loan",
//                        "value": "Loan"
//                    },
//                    "chargeCalculationType": {
//                        "id": 2,
//                        "code": "chargeCalculationType.percent.of.amount",
//                        "value": "% Approved Amount"
//                    },
//                    "chargePaymentMode": {
//                        "id": 0,
//                        "code": "chargepaymentmode.regular",
//                        "value": "Regular"
//                    },
//                    "taxGroup": {
//                        "id": 2,
//                        "name": "GST - New",
//                        "isActive": true
//                    },
//                    "percentageType": {
//                        "id": 1,
//                        "code": "ChargePercentageType.flat",
//                        "value": "Flat"
//                    },
//                    "percentagePeriodType": {
//                        "id": 1,
//                        "code": "ChargePercentagePeriodType.daily",
//                        "value": "Daily"
//                    },
//                    "emiRoundingGoalSeek": false,
//                    "isGlimCharge": false,
//                    "glimChargeCalculation": {
//                        "id": 0,
//                        "code": "glimChargeCalculationType.invalid",
//                        "value": "Invalid"
//                    },
//                    "isCapitalized": false,
//                    "isSlabBased": false,
//                    "isCollectedAsCash": false,
//                    "percentageCalculationDaysInYearType": {
//                        "id": 1,
//                        "code": "DaysInYearType.actual",
//                        "value": "Actual"
//                    },
//                    "isChargeCalculationInclusiveOfTax": true,
//                    "isUpdateThroughDiscount": false,
//                    "businessIdentificationCode": "PF",
//                    "accountingType": "CASH_BASED",
//                    "daysInMonthType": {
//                        "id": 0,
//                        "code": "DaysInMonthType.invalid",
//                        "value": "Invalid"
//                    },
//                    "chargeType": {
//                        "id": 1,
//                        "code": "USER_CHARGE",
//                        "value": "User Charge"
//                    },
//                    "recognitionType": {
//                        "id": 1,
//                        "code": "SINGLE",
//                        "value": "Single"
//                    },
//                    "showCharge": true,
//                    "isChartDefined": false
//                }
//            ]
//        },
//        "principalVariationsForBorrowerCycle": [],
//        "interestRateVariationsForBorrowerCycle": [],
//        "numberOfRepaymentVariationsForBorrowerCycle": [],
//        "accountingRule": {
//            "id": 1,
//            "code": "accountingRuleType.none",
//            "value": "NONE"
//        },
//        "canUseForTopup": false,
//        "isAllowedForColending": false,
//        "multiDisburseLoan": false,
//        "principalThresholdForLastInstallment": 0,
//        "holdGuaranteeFunds": false,
//        "accountMovesOutOfNPAOnlyOnArrearsCompletion": false,
//        "allowAttributeOverrides": {
//            "amortizationType": true,
//            "interestType": true,
//            "transactionProcessingStrategyId": true,
//            "interestCalculationPeriodType": true,
//            "inArrearsTolerance": true,
//            "repaymentEvery": true,
//            "graceOnPrincipalAndInterestPayment": true,
//            "graceOnArrearsAgeing": true
//        },
//        "closeLoanOnOverpayment": false,
//        "syncExpectedWithDisbursementDate": false,
//        "minLoanTerm": 3,
//        "maxLoanTerm": 48,
//        "loanTenureFrequencyType": {
//            "id": 4,
//            "code": "periodFrequencyType.invalid",
//            "value": "Invalid"
//        },
//        "weeksInYearType": {
//            "id": 1,
//            "code": "52",
//            "value": "52"
//        },
//        "isEmiBasedOnDisbursements": false,
//        "considerFutureDisbursementsInSchedule": false,
//        "considerAllDisbursementsInSchedule": false,
//        "allowNegativeLoanBalance": false,
//        "allowUpfrontCollection": false,
//        "stopLoanProcessingOnNpa": false,
//        "canCrossMaturityDateOnFixingEMI": false,
//        "trancheAmountLimitType": {
//            "id": 1,
//            "code": "trancheAmountLimitType.on.total.disbursed.amount",
//            "value": "Disburse amount <= Sanction amount - Total disbursed amount"
//        },
//        "trancheLoanClosureType": {
//            "id": 1,
//            "code": "trancheLoanClosureType.auto.closure.before.maturity.date",
//            "value": "Auto closure before maturity"
//        },
//        "calculateIrr": false,
//        "isRepaymentAtDisbursement": true,
//        "applicableForLoanType": {
//            "id": 1,
//            "code": "loanProductApplicableForLoanType.all.types",
//            "value": "All Customers"
//        },
//        "loanProductEntityProfileMappingDatas": [
//            {
//                "profileType": {
//                    "id": 1,
//                    "code": "clientProfileType.legal.form",
//                    "value": "Legal Form"
//                },
//                "values": [
//                    {
//                        "id": 1,
//                        "code": "legalFormType.person",
//                        "value": "Person"
//                    }
//                ]
//            }
//        ],
//        "allowDisbursementToGroupBankAccounts": false,
//        "excludeAdjustedRoundedAmountWithInterest": false,
//        "precloseEmiRounding": true,
//        "borrowerCycleType": {
//            "id": 0,
//            "code": "borrowerCycleType.none",
//            "value": "None"
//        },
//        "considerTenureForIRRCalculation": false,
//        "isOverdueAccountingEnabled": false,
//        "isPostIRDEnabled": false,
//        "loanProductGroupId": 106,
//        "isDpConfigured": false,
//        "brokenPeriodInterestCollectAtDisbursement": false,
//        "repaymentFrequencyNthDayTypeOptions": [
//            {
//                "id": 1,
//                "code": "repaymentFrequency.nthDayType.one",
//                "value": "first"
//            },
//            {
//                "id": 2,
//                "code": "repaymentFrequency.nthDayType.two",
//                "value": "second"
//            },
//            {
//                "id": 3,
//                "code": "repaymentFrequency.nthDayType.three",
//                "value": "third"
//            },
//            {
//                "id": 4,
//                "code": "repaymentFrequency.nthDayType.four",
//                "value": "fourth"
//            },
//            {
//                "id": -1,
//                "code": "repaymentFrequency.nthDayType.last",
//                "value": "last"
//            },
//            {
//                "id": -2,
//                "code": "repaymentFrequency.nthDayType.onday",
//                "value": "onday"
//            }
//        ],
//        "repaymentFrequencyDaysOfWeekTypeOptions": [
//            {
//                "id": 7,
//                "code": "repaymentFrequency.weekDayType.sunday",
//                "value": "SUNDAY"
//            },
//            {
//                "id": 1,
//                "code": "repaymentFrequency.weekDayType.monday",
//                "value": "MONDAY"
//            },
//            {
//                "id": 2,
//                "code": "repaymentFrequency.weekDayType.tuesday",
//                "value": "TUESDAY"
//            },
//            {
//                "id": 3,
//                "code": "repaymentFrequency.weekDayType.wednesday",
//                "value": "WEDNESDAY"
//            },
//            {
//                "id": 4,
//                "code": "repaymentFrequency.weekDayType.thursday",
//                "value": "THURSDAY"
//            },
//            {
//                "id": 5,
//                "code": "repaymentFrequency.weekDayType.friday",
//                "value": "FRIDAY"
//            },
//            {
//                "id": 6,
//                "code": "repaymentFrequency.weekDayType.saturday",
//                "value": "SATURDAY"
//            }
//        ],
//        "repaymentFrequencyNthDayType": {
//            "id": -2,
//            "code": "nthDayType.onday",
//            "value": "ONDAY"
//        },
//        "repeatsOnDayOfMonth": [
//            5
//        ],
//        "frequencyHumanReadable": "Monthly on day 5",
//        "isUpfrontInterestEnabled": false,
//        "isInterestRateDiscountingEnabled": false,
//        "eventBasedCharges": [],
//        "additionalInterestComputationType": {
//            "id": 4,
//            "code": "standard",
//            "value": "Standard"
//        },
//        "amortizationStrategy": {
//            "code": "distributed-principal",
//            "value": "Distributed Principal"
//        },
//        "isLoanDPDChargesApplicable": false,
//        "stopAccrualSuspenseEntriesOnNpa": false,
//        "uiConfig": {
//            "approachCode": "PRINCIPAL_TENURE_INTEREST_RATE"
//        },
//        "productType": "PERSONAL LOAN",
//        "beneficiaryType": "SELF",
//        "firstInstallmentInterestMethodAsBpiType": {
//            "id": 0,
//            "code": "firstInstallmentInterestMethodAsBpiType.standard",
//            "value": "Standard"
//        },
//        "isPartialInterestCalculationAllowed": false,
//        "idealDateDueMethodType": {
//            "id": 2,
//            "code": "idealDateDueMethodType.invalid",
//            "value": "Invalid"
//        },
//        "idealDateDueMethodTypeValue": 0,
//        "compoundGraceInterest": false,
//        "productConfigData": {
//            "cancellationDetails": {
//                "cancellationAllowed": false,
//                "cancellationAllowedTillDays": 0
//            }
//        },
//        "firstRepaymentDateMethodCalculationType": 1,
//        "bpiDueAmountStrategyType": 1,
//        "bpiComputationStrategyType": 4,
//        "emiAmountComputationStrategyType": "CURRENT_PERIOD_DUE_DATE",
//        "isPartReleaseAllowed": false,
//        "isInterestTypeConvertable": false
//    },
//    "overdueCharges": [
//        {
//            "id": 43,
//            "productLoanId": 3,
//            "chargeData": {
//                "id": 4,
//                "name": "Late Fee - Penalty",
//                "active": true,
//                "penalty": true,
//                "currency": {
//                    "code": "INR",
//                    "name": "Indian Rupee",
//                    "decimalPlaces": 2,
//                    "displaySymbol": "â‚¹",
//                    "nameCode": "currency.INR",
//                    "displayLabel": "Indian Rupee (â‚¹)"
//                },
//                "amount": 0.59,
//                "chargeTimeType": {
//                    "id": 9,
//                    "code": "chargeTimeType.overdueInstallment",
//                    "value": "Overdue Fees"
//                },
//                "chargeAppliesTo": {
//                    "id": 1,
//                    "code": "chargeAppliesTo.loan",
//                    "value": "Loan"
//                },
//                "chargeCalculationType": {
//                    "id": 3,
//                    "code": "chargeCalculationType.percent.of.amount.and.interest",
//                    "value": "% Loan Amount + Interest"
//                },
//                "chargePaymentMode": {
//                    "id": 0,
//                    "code": "chargepaymentmode.regular",
//                    "value": "Regular"
//                },
//                "taxGroup": {
//                    "id": 2,
//                    "name": "GST - New",
//                    "isActive": true
//                },
//                "percentageType": {
//                    "id": 1,
//                    "code": "ChargePercentageType.flat",
//                    "value": "Flat"
//                },
//                "percentagePeriodType": {
//                    "id": 1,
//                    "code": "ChargePercentagePeriodType.daily",
//                    "value": "Daily"
//                },
//                "chargeOverdueData": {
//                    "id": 1,
//                    "gracePeriod": 0,
//                    "penaltyFreePeriod": 0,
//                    "graceType": {
//                        "id": 1,
//                        "code": "penaltyGraceType.first.overdue.installment",
//                        "value": "First overdue installment"
//                    },
//                    "applyChargeForBrokenPeriod": false,
//                    "isBasedOnOriginalSchedule": false,
//                    "considerOnlyPostedInterest": false,
//                    "calculateChargeOnCurrentOverdue": true,
//                    "stopChargeOnNPA": false,
//                    "overdueBasedOn": {
//                        "id": 0,
//                        "code": "overdueBasedOn.invalid",
//                        "value": "Invalid"
//                    },
//                    "overdueClassification": {
//                        "id": 1,
//                        "code": "overdueChargeClassification.standard",
//                        "value": "Standard",
//                        "systemCode": "Standard"
//                    }
//                },
//                "emiRoundingGoalSeek": false,
//                "isGlimCharge": false,
//                "glimChargeCalculation": {
//                    "id": 0,
//                    "code": "glimChargeCalculationType.invalid",
//                    "value": "Invalid"
//                },
//                "slabs": [
//                    {
//                        "id": 0,
//                        "type": {
//                            "id": 0,
//                            "code": "slabChargeType.installment.invalid",
//                            "value": "INVALID"
//                        },
//                        "subSlabs": []
//                    }
//                ],
//                "isCapitalized": false,
//                "isSlabBased": false,
//                "isCollectedAsCash": false,
//                "percentageCalculationDaysInYearType": {
//                    "id": 1,
//                    "code": "DaysInYearType.actual",
//                    "value": "Actual"
//                },
//                "isChargeCalculationInclusiveOfTax": true,
//                "isUpdateThroughDiscount": false,
//                "businessIdentificationCode": "OD",
//                "accountingType": "ACCRUAL_BASED",
//                "daysInMonthType": {
//                    "id": 0,
//                    "code": "DaysInMonthType.invalid",
//                    "value": "Invalid"
//                },
//                "chargeType": {
//                    "id": 1,
//                    "code": "USER_CHARGE",
//                    "value": "User Charge"
//                },
//                "recognitionType": {
//                    "id": 1,
//                    "code": "SINGLE",
//                    "value": "Single"
//                },
//                "showCharge": true,
//                "isChartDefined": false
//            },
//            "isMandatory": false,
//            "isAmountNonEditable": false,
//            "canLendCharge": false,
//            "canAddChargeToPrincipalForComputation": false
//        }
//    ],
//    "daysInMonthType": {
//        "id": 30,
//        "code": "DaysInMonthType.days360",
//        "value": "30 Days"
//    },
//    "daysInYearType": {
//        "id": 360,
//        "code": "DaysInYearType.days360",
//        "value": "360 Days"
//    },
//    "isInterestRecalculationEnabled": false,
//    "interestRecalculationData": {
//        "isSubsidyApplicable": false,
//        "allowScheduleAfterMaturity": false
//    },
//    "isVariableInstallmentsAllowed": false,
//    "minimumGap": 0,
//    "maximumGap": 0,
//    "paymentOptions": [
//        {
//            "id": 1,
//            "name": "online transfer",
//            "isCashPayment": false,
//            "position": 0,
//            "paymentMode": {
//                "id": 1,
//                "code": "paymentModeType.realTimeApi",
//                "value": "Real time Api"
//            },
//            "applicableOn": [
//                {
//                    "id": 3,
//                    "code": "paymentApplicableOnType.all",
//                    "value": "All"
//                }
//            ],
//            "serviceProvider": {
//                "id": 0,
//                "isActive": false,
//                "mandatory": false
//            }
//        },
//        {
//            "id": 1002,
//            "name": "Ezetap",
//            "description": "",
//            "isCashPayment": false,
//            "position": 0,
//            "externalServiceId": 6,
//            "paymentMode": {
//                "id": 1,
//                "code": "paymentModeType.realTimeApi",
//                "value": "Real time Api"
//            },
//            "applicableOn": [
//                {
//                    "id": 1,
//                    "code": "paymentApplicableOnType.repayment",
//                    "value": "Repayment"
//                }
//            ],
//            "serviceProvider": {
//                "id": 0,
//                "isActive": false,
//                "mandatory": false
//            }
//        },
//        {
//            "id": 1003,
//            "name": "Razorpay",
//            "description": "",
//            "isCashPayment": false,
//            "position": 0,
//            "externalServiceId": 5,
//            "paymentMode": {
//                "id": 1,
//                "code": "paymentModeType.realTimeApi",
//                "value": "Real time Api"
//            },
//            "applicableOn": [
//                {
//                    "id": 1,
//                    "code": "paymentApplicableOnType.repayment",
//                    "value": "Repayment"
//                }
//            ],
//            "serviceProvider": {
//                "id": 0,
//                "isActive": false,
//                "mandatory": false
//            }
//        },
//        {
//            "id": 1004,
//            "name": "finflux-payu",
//            "description": "Finflux Pay U Payment Gateway",
//            "isCashPayment": false,
//            "position": 0,
//            "externalServiceId": 7,
//            "paymentMode": {
//                "id": 1,
//                "code": "paymentModeType.realTimeApi",
//                "value": "Real time Api"
//            },
//            "applicableOn": [
//                {
//                    "id": 1,
//                    "code": "paymentApplicableOnType.repayment",
//                    "value": "Repayment"
//                }
//            ],
//            "serviceProvider": {
//                "id": 0,
//                "isActive": false,
//                "mandatory": false
//            }
//        },
//        {
//            "id": 1005,
//            "name": "Insurance Claim Settlement",
//            "description": "Insurance Claim Settlement",
//            "isCashPayment": false,
//            "position": 0,
//            "paymentMode": {
//                "id": 1,
//                "code": "paymentModeType.realTimeApi",
//                "value": "Real time Api"
//            },
//            "applicableOn": [
//                {
//                    "id": 1,
//                    "code": "paymentApplicableOnType.repayment",
//                    "value": "Repayment"
//                }
//            ],
//            "serviceProvider": {
//                "id": 0,
//                "isActive": false,
//                "mandatory": false
//            }
//        }
//    ],
//    "brokenPeriodMethodTypeOptions": [
//        {
//            "id": 0,
//            "code": "brokenPeriodMethod.distribute.equally",
//            "value": "Distribute equally among all installments"
//        },
//        {
//            "id": 1,
//            "code": "brokenPeriodMethod.adjust.in.first.emi",
//            "value": "Add to first installment interest"
//        },
//        {
//            "id": 2,
//            "code": "brokenPeriodMethod.post.interest",
//            "value": "Post interest"
//        },
//        {
//            "id": 3,
//            "code": "brokenPeriodMethod.adjust.in.first.emi.with.principal.grace",
//            "value": "Add to first installment interest with principal grace"
//        },
//        {
//            "id": 4,
//            "code": "brokenPeriodMethod.emi.plush.broken.period.interest",
//            "value": "EMI + Broken Period Interest"
//        },
//        {
//            "id": 5,
//            "code": "brokenPeriodMethod.emi.plush.broken.period.interest.for.all.tranche.with.principal.grace",
//            "value": "EMI + Broken Period Interest For All Tranche with principal grace "
//        },
//        {
//            "id": 6,
//            "code": "brokenPeriodMethod.broken.period.with.emi.grace",
//            "value": "Broken Period with EMI grace"
//        }
//    ],
//    "isLocked": false,
//    "deferPaymentsForHalfTheLoanTerm": false,
//    "isClientVerified": false,
//    "paymentModeOptions": [
//        {
//            "id": 1,
//            "code": "paymentModeType.realTimeApi",
//            "value": "Real time Api"
//        },
//        {
//            "id": 2,
//            "code": "paymentModeType.fileBased",
//            "value": "File Based"
//        },
//        {
//            "id": 3,
//            "code": "paymentModeType.manual",
//            "value": "Manual"
//        },
//        {
//            "id": 4,
//            "code": "paymentModeType.paymentLink",
//            "value": "Payment Link Api"
//        }
//    ],
//    "allowsDisbursementToGroupBankAccounts": false,
//    "isDpConfigured": false,
//    "brokenPeriodInterestCollectAtDisbursement": false,
//    "isUpfrontInterestEnabled": false,
//    "discountTypeOptions": [
//        {
//            "id": 1,
//            "code": "chargeDiscountCalculationType.discountInAmount",
//            "value": "Discount in Amount"
//        },
//        {
//            "id": 2,
//            "code": "chargeCalculationType.discountInPercentage",
//            "value": "Discount in Percentage"
//        }
//    ],
//    "anchors": [],
//    "productFloatingData": {},
//    "insurancePolicyTemplate": {
//        "insuranceClientTypeOptions": [
//            {
//                "id": 1,
//                "code": "insuranceClientType.insured",
//                "value": "INSURED"
//            },
//            {
//                "id": 2,
//                "code": "insuranceClientType.coInsured",
//                "value": "CO-INSURED"
//            }
//        ],
//        "insuranceProviderOptions": [],
//        "genderTypeOptions": [
//            {
//                "id": 23,
//                "name": "Male",
//                "position": 1,
//                "description": "Male",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 24,
//                "name": "Female",
//                "position": 2,
//                "description": "Female",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            }
//        ],
//        "relationshipTypeOptions": [
//            {
//                "id": 20,
//                "name": "Father",
//                "position": 0,
//                "description": "Father",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 22,
//                "name": "father-in-law",
//                "position": 0,
//                "description": "father-in-law",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 21,
//                "name": "Spouse",
//                "position": 0,
//                "description": "Spouse",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 31,
//                "name": "Brother",
//                "position": 1,
//                "description": "Brother",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 33,
//                "name": "Brother in law",
//                "position": 1,
//                "description": "Brother in law",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 29,
//                "name": "Daughter",
//                "position": 1,
//                "description": "Daughter",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 34,
//                "name": "Daughter in law",
//                "position": 1,
//                "description": "Daughter in law",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 46,
//                "name": "Father In Law",
//                "position": 1,
//                "description": "Father In Law",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 27,
//                "name": "Husband",
//                "position": 1,
//                "description": "Husband",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 26,
//                "name": "Mother",
//                "position": 1,
//                "description": "Mother",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 32,
//                "name": "Mother in law",
//                "position": 1,
//                "description": "Mother in law",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 35,
//                "name": "Other",
//                "position": 1,
//                "description": "Other",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 28,
//                "name": "Son",
//                "position": 1,
//                "description": "Son",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            },
//            {
//                "id": 30,
//                "name": "Wife",
//                "position": 1,
//                "description": "Wife",
//                "isActive": true,
//                "codeScore": 0,
//                "mandatory": false
//            }
//        ]
//    },
//    "isColendingEnabled": false
//  }
// TODO: Add the remaining fields (if required)
message ProductDetails {
  int32 loan_product_id = 1 [json_name = "loanProductId"];
  string loan_product_name = 2 [json_name = "loanProductName"];
  int32 term_frequency = 3 [json_name = "termFrequency"];
  types.Enum term_period_frequency_type = 4 [json_name = "termPeriodFrequencyType"];
  types.Enum interest_type = 5 [json_name = "interestType"];
  types.Enum amortization_type = 6 [json_name = "amortizationType"];
  int32 repayment_every = 7 [json_name = "repaymentEvery"];
  types.Enum repayment_frequency_type = 8 [json_name = "repaymentFrequencyType"];
  //  to define the transaction allocation repayment strategy:
  //    Penalties, Fees, Interest, Principal order - 1
  //    Overdue/Due Fee/Int,Principal- 4
  //    Principal, Interest, Penalties, Fees Order - 5
  //    Interest, Principal, Penalties, Fees Order - 6
  //    Overdue/Int/Principal,Fees - 8
  //    Overdue/fee/int,Adv/principal - 9
  //    Adv/Principal,Penalties,Fees,Interest,Principal order - 10
  //    Overdue/Principal/Interest,Fees -12
  //    Overdue/Due Interest,Overdue/Due Penalties/Fees, Principal Order -13
  //    Overdue/Due/Adv Principal/Interest, Overdue/Due Penalties, Fees Order- 14
  //    Overdue/Due/Adv Principal/Interest, Overdue/Due Penalties/Fees Order - 15
  //    Adv/Principal,Interest,Principal,Penalties,Fees - 16
  // e.g.: 5 (for Principal, Interest, Penalties, Fees Order)
  int32 transaction_processing_strategy_id = 9 [json_name = "transactionProcessingStrategyId"];
  types.Enum interest_calculation_period_type = 10 [json_name = "interestCalculationPeriodType"];
  Product product = 11 [json_name = "product"];
  message Product {
    types.Enum repayment_frequency_nth_day_type = 1 [json_name = "repaymentFrequencyNthDayType"];
    repeated int32 repeats_on_day_of_month = 2 [json_name = "repeatsOnDayOfMonth"];
  }
}
