syntax = "proto3";

package vendors.visa;

import "google/type/latlng.proto";

// Ref: https://developer.visa.com/capabilities/atmlocator/reference#tag/Locate-ATMs-API_/paths/~1globalatmlocator~1v3~1localatms~1totalsinquiry/post_v3%20-%20Latest
option go_package = "github.com/epifi/gamma/api/vendors/visa";
option java_package = "com.github.epifi.gamma.api.vendors.visa";

message FindNearByAtmTotalsRequest {
  // Request data for finding nearby ATMs
  FindAtmsLocationsRequestData request_data = 1 [json_name = "requestData"];
  // Web service request header
  WsRequestHeaderV2 ws_request_header_v2 = 2 [json_name = "wsRequestHeaderV2"];
}

message FindNearByAtmTotalsResponse {
  // Data of ATM locations found
  AtmLocationsData atm_locations_data = 1 [json_name = "responseData"];
  // Summary data of the response
  ResponseSummaryData response_summary_data = 2 [json_name = "responseSummaryData"];
  // Web service response header
  WsResponseHeader ws_response_header = 3 [json_name = "wsResponseHeader"];
  // Web service response header
  WsResponseHeaderV2 ws_request_header_v2 = 4 [json_name = "wsResponseHeaderV2"];
  // Status from vg
  VisaResponseStatus ws_status = 8 [json_name = "wsStatus"];
  // Status in case of failure due to bad request (400)
  VisaResponseStatus failure_response_status = 9 [json_name = "responseStatus"];

  // following fields will be populated in case of any unexpected error.
  //
  // // Error message in case of any unexpected error
  string error = 10 [json_name = "error"];
  // Detailed error message
  string error_message = 11 [json_name = "message"];
  // Path where the error occurred
  string path = 12 [json_name = "path"];
  // HTTP status code of the error
  int32  error_status_code = 13 [json_name = "status"];
  // Timestamp of the error
  string timestamp = 14 [json_name = "timestamp"];
}

message FindNearByAtmsRequest {
  // Request data for finding nearby ATMs
  FindAtmsLocationsRequestData request_data = 1 [json_name = "requestData"];
  // Web service request header
  WsRequestHeaderV2 ws_request_header_v2 = 2 [json_name = "wsRequestHeaderV2"];
}

message FindNearByAtmsResponse {
  // Data of ATM locations found
  AtmLocationsData atm_locations_data = 1 [json_name = "responseData"];
  // Summary data of the response
  ResponseSummaryData response_summary_data = 2 [json_name = "responseSummaryData"];
  // Web service response header
  WsResponseHeader ws_response_header = 3 [json_name = "wsResponseHeader"];
  // Web service response header
  WsResponseHeaderV2 ws_request_header_v2 = 4 [json_name = "wsResponseHeaderV2"];
  // Status from vg
  VisaResponseStatus ws_status = 8 [json_name = "wsStatus"];
  // Status in case of failure due to bad request (400)
  VisaResponseStatus failure_response_status = 9 [json_name = "responseStatus"];

  // following fields will be populated in case of any unexpected error.
  // Error message in case of any unexpected error
  string error = 10 [json_name = "error"];
  // Detailed error message
  string error_message = 11 [json_name = "message"];
  // Path where the error occurred
  string path = 12 [json_name = "path"];
  // HTTP status code of the error
  int32  error_status_code = 13 [json_name = "status"];
  // Timestamp of the error
  string timestamp = 14 [json_name = "timestamp"];
}

message FindGeocodesRequest {
  // Request data for finding geocodes
  FindGeocodesRequestData  request_data = 1 [json_name = "requestData"];

  message FindGeocodesRequestData {
    // Country for the geocode search
    string country = 1 [json_name = "country"];
    // City for the geocode search
    string city = 2 [json_name = "city"];
    // Main address for the geocode search
    string main_address = 3 [json_name = "mainAddress"];
    // Postal code for the geocode search
    string postal_code = 4 [json_name = "postalCode"];
    // State for the geocode search
    string state = 5 [json_name = "state"];
  }
}

message FindGeocodesResponse {
  // Response data for the geocode search
  FindGeocodesResponseData response_data = 1 [json_name = "responseData"];
  // Status from vg
  // Will  be populated in case of success
  VisaResponseStatus ws_status = 2 [json_name = "wsStatus"];
  // Status in case of failure due to bad request (400
  VisaResponseStatus failure_response_status = 3 [json_name = "responseStatus"];
  // Following fields will be populated in case of any unexpected error.
  //
  // Error message in case of any unexpected error
  string error = 4 [json_name = "error"];
  // Detailed error message
  string error_message = 5 [json_name = "message"];
  // Path where the error occurred
  string path = 6 [json_name = "path"];
  // HTTP status code of the error
  int32 error_status_code = 7 [json_name = "status"];
  // Timestamp of the error
  string timestamp = 8 [json_name = "timestamp"];

  message FindGeocodesResponseData {
    // Latitude of the geocode
    double latitude = 1 [json_name = "latitude"];
    // Longitude of the geocode
    double longitude = 2 [json_name = "longitude"];
    // Score of the geocode
    string score = 3 [json_name = "score"];
  }
}

message FindAtmsLocationsRequestData {
  // Radius of the circular area in which search results are restricted.
  // It is limited to <=100 km.
  double distance = 1 [json_name = "distance"];
  // Unit of `distance`, can be one of mi and km
  string distance_unit = 2 [json_name = "distanceUnit"];
  // Centre of the circular area in which search query will be done.
  Location location = 3 [json_name = "location"];
  // Provide various required infos for search query such as - max results to be returned, sort direction etc.
  AtmLocationSearchOptions atm_location_search_options = 4 [json_name = "options"];
  string culture = 5 [json_name = "culture"];
  // Optional: Metadata options for the search query
  double meta_data_options = 6 [json_name = "metaDataOptions"];
}

message Location {
  // Geocodes of the location
  Geocodes geocodes = 1 [json_name = "geocodes"];
  // Address of the location
  Address address = 2 [json_name = "address"];
  // Place name of the location
  string place_name = 3 [json_name = "placeName"];
}

message Geocodes {
  // Latitude of the geocode
  string latitude = 1 [json_name = "latitude"];
  // Longitude of the geocode
  string longitude = 2 [json_name = "longitude"];
}

message Address {
  // City of the address
  string city = 1 [json_name = "city"];
  // Country of the address
  string country = 2 [json_name = "country"];
  // Formatted address
  string formatted_address = 3 [json_name = "formattedAddress"];
  // Postal code of the address
  string postal_code = 4 [json_name = "postalCode"];
  // State of the address
  string state = 5 [json_name = "state"];
  // Street of the address
  string street = 6 [json_name = "street"];
  // Additional street information
  string street2 = 7 [json_name = "street2"];
}

message AtmLocationSearchOptions {
  // Range options for the search query
  Range range = 1 [json_name = "range"];
  // Sort options for the search query
  Sort sort = 2 [json_name = "sort"];
  // Filter options for the search query
  repeated FilterOption filter_options = 3 [json_name = "findFilters"];
  // Operation name for the search query
  string operation_name = 4 [json_name = "operationName"];
  // Use the first ambiguous result
  bool use_first_ambiguous = 5 [json_name = "useFirstuiuous"];

  message Sort {
    // Sort direction, can be asc or desc
    string direction = 1 [json_name = "direction"];
    // Primary sort field, can be distance, placeName, or city
    string primary = 2 [json_name = "primary"];
  }

  message Range {
    // Number of results to return
    int32 count = 1 [json_name = "count"];
    // Starting index for the results
    int32 start = 2 [json_name = "start"];
  }

  message FilterOption {
    // Name of the filter
    string filter_name = 1 [json_name = "filterName"];
    // Value of the filter
    string filter_value = 2 [json_name = "filterValue"];
  }
}

// AtmLocationsData holds the detailed location of the atms.
message AtmLocationsData {
  // Best map view for the ATM locations
  MapView map_view = 1 [json_name = "bestMapView"];
  // Unit of distance for the ATM locations
  string distance_unit = 2 [json_name = "distanceUnit"];
  // List of found ATM locations
  repeated AtmLocation found_atm_locations = 3 [json_name = "foundATMLocations"];
  // Matched locations for the ATM search
  MatchedLocations matched_locations = 4 [json_name = "matchedLocations"];
  // Metadata for the ATM locations
  MetaData meta_data = 5 [json_name = "metaData"];
  // Properties of the ATM locations
  repeated Property properties = 6 [json_name = "properties"];
  // Total count of ATMs found
  int64 total_atm_count = 7 [json_name = "totalATMCount"];

  message MapView {
    // Distance across the map view
    double distance_across = 1 [json_name = "distanceAcross"];
    // North-east coordinates of the map view
    google.type.LatLng north_east = 2 [json_name = "northEast"];
    // North-west coordinates of the map view
    google.type.LatLng north_west = 3 [json_name = "northWest"];
    // South-east coordinates of the map view
    google.type.LatLng south_east = 4 [json_name = "southEast"];
    // South-west coordinates of the map view
    google.type.LatLng south_west = 5 [json_name = "southWest"];
  }

  message AtmLocation {
    Location location = 1 [json_name = "location"];

    message Location {
      // Address of the ATM location
      Address address = 1 [json_name = "address"];
      // Coordinates of the ATM location
      google.type.LatLng coordinates = 2 [json_name = "coordinates"];
      // Distance to the ATM location
      string distance = 3 [json_name = "distance"];
      // Whether the ATM location is mappable
      string is_mappable = 4 [json_name = "isMappable"];
      // Business name of the ATM owner
      string owner_bus_name = 5 [json_name = "ownerBusName"];
      // Place name of the ATM location
      string place_name = 6 [json_name = "placeName"];
      // Properties of the ATM location
      repeated Property properties = 7 [json_name = "properties"];
      // Score of the ATM location
      double score = 8 [json_name = "score"];
      // Type name of the ATM location
      string type_name = 9 [json_name = "typeName"];
    }
  }

  message MatchedLocations {}

  message MetaData {
    // Fields of the metadata
    string fields = 1 [json_name = "fields"];
    // Filters of the metadata
    string filters = 2 [json_name = "filters"];
    // Target culture of the metadata
    string target_culture = 3 [json_name = "targetCulture"];
  }

  message Property {
    // Name of the property
    string name = 1 [json_name = "name"];
    // Value of the property
    string value = 2 [json_name = "value"];
  }
}

message ResponseSummaryData {}

message WsRequestHeaderV2 {
  // Application ID for the web service request
  string application_id = 1 [json_name = "applicationId"];
  // Correlation ID for the web service request
  string correlation_id = 2 [json_name = "correlationId"];
  // Request message ID for the web service request
  string request_message_id = 3 [json_name = "requestMessageId"];
  // Timestamp of the web service request
  string request_ts = 4 [json_name = "requestTs"];
  // User BID for the web service request
  string user_bid = 5 [json_name = "userBid"];
  // User ID for the web service request
  string user_id = 6 [json_name = "userId"];
}

message WsResponseHeader {}

message WsResponseHeaderV2 {
  // Correlation ID for the web service response
  string correlation_id = 1 [json_name = "correlationId"];
  // Number of rows returned in the response
  int64 num_of_rows_returned = 2 [json_name = "numOfRowsReturned"];
  // Request message ID for the web service response
  string request_message_id = 3 [json_name = "requestMessageId"];
  // Response message ID for the web service response
  string response_message_id = 4 [json_name = "responseMessageId"];
  // Timestamp of the web service response
  string response_ts = 5 [json_name = "responseTs"];
  // Total number of records in the response
  string total_number_of_records = 6 [json_name = "totalNumberOfRecords"];
}

message VisaResponseStatus {
  // Status code of the response
  string status_code = 1 [json_name = "statusCode"];
  // Description of the status
  string status_description = 2 [json_name = "statusDescription"];
}

