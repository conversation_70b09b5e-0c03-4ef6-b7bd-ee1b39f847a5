syntax = "proto3";

package vendors.visa;

// Ref: https://developer.visa.com/capabilities/foreign_exchange/reference#tag/Enhanced-Foreign-Exchange-Rates-API/operation/Get%20Foreign%20Exchange%20Rates_v1%20-%20Latest
option go_package = "github.com/epifi/gamma/api/vendors/visa";
option java_package = "com.github.epifi.gamma.api.vendors.visa";

message GetEnhancedForeignExchangeRatesRequest {
  // EpochDateTime in seconds. Date/Time of the requested FX rates.
  // If not specified, will be assumed to be current EpochDateTime.
  // Cannot be earlier than one year from today (GMT timezone) and cannot be in the future.
  int64 as_of_date = 1 [json_name = "asOfDate"];
  // 3 letter alphabetic ISO 4217 currency code ( USD,EUR,GBP ... etc ) of the transaction currency.
  // This is the currency that would generally be bought in a transaction.
  string from_currency = 2 [json_name = "fromCurrency"];
  // 3 letter alphabetic ISO 4217 currency code ( USD,EUR,GBP ... etc ) of the cardholder billing currency.
  // This is the currency that would generally be sold in a transaction.
  string to_currency  = 3 [json_name = "toCurrency"];
}

message GetEnhancedForeignExchangeRatesResponse {
  // EpochDateTime in seconds. Date/Time of the requested FX rates.
  // If not specified, will be assumed to be current EpochDateTime.
  // Cannot be earlier than one year from today (GMT timezone) and cannot be in the future.
  int64 as_of_date = 1 [json_name = "asOfDate"];
  // Visa Treasury Rate
  string exchange_rate_visa = 2 [json_name = "exchangeRateVisa"];
  // 3 letter ISO currency code from which you are converting the amount (USD, EUR, GBP, ... etc)
  string from_currency = 3 [json_name = "fromCurrency"];
  // ISO 4217 Currency Name of the fromCurrency
  string from_currency_name = 4 [json_name = "fromCurrencyName"];
  // The Epoch timestamp corresponding to the timestamp Visa Rates were updated last
  int64 last_updated_visa_rate = 5 [json_name = "lastUpdatedVisaRate"];
  // 3 letter ISO currency code from which we you converting the amount( USD,EUR,GBP ... etc )
  string to_currency = 6 [json_name = "toCurrency"];
  // ISO 4217 Currency Name of the toCurrency
  string to_currency_name = 7 [json_name = "toCurrencyName"];
}

message GetEnhancedMarkupForexExchangeRatesRequest {
  // From Amount also known as transaction amount that will be converted from the "fromCurrency" and to the "toCurrency".
  // "." will be the radix separator.
  string from_amount = 1 [json_name = "fromAmount"];
  // 3 letter alphabetic ISO 4217 currency code (USD, EUR, GBP, ... etc) of the transaction currency.
  // This is the currency that would generally be bought in a transaction.
  string from_currency = 2 [json_name = "fromCurrency"];
  // 3 letter alphabetic ISO 4217 currency code (USD, EUR, GBP, ... etc) of the cardholder billing currency.
  // This is the currency that would generally be sold in a transaction.
  string to_currency  = 3 [json_name = "toCurrency"];
  // Fixed Additional flat fee. No symbols other than +/- and radix separator “.” allowed.
  // Length should be <= 10 characters
  string additional_fee = 4 [json_name = "additionalFee"];
  // Additional rate in percentage. Rate of Mark-Up/Down in +/-% (e.g. +2.00)
  // Length should be <= 10 characters
  string additional_rate = 5 [json_name = "additionalRate"];
  // EpochDateTime in seconds. Date/Time of the requested FX rates. If not specified, will be assumed to be current EpochDateTime.
  // Cannot be earlier than one year from today (GMT timezone) and cannot be in the future.
  int64 as_of_date = 6 [json_name = "asOfDate"];
}

message GetEnhancedMarkupForexExchangeRatesResponse {
 // EpochDateTime in seconds. Date/Time of the requested FX rates.
 // If not specified, will be assumed to be current EpochDateTime.
 // Cannot be earlier than one year from today (GMT timezone) and cannot be in the future
  int64 as_of_date = 1 [json_name = "asOfDate"];
  // Will return the benchmark properties of all benchmarks specified in the request OR if no benchmarks are specified in the request,
  // will return all the benchmark properties that apply to the “fromCurrency” and “toCurrency” pair in the Visa system.
  // If only one benchmark is specified in the request and is not configured for the “fromCurrency” and “toCurrency” pair, system will return an error.
  // If multiple benchmarks are specified in the request and some of the requested benchmarks are configured for the “fromCurrency”
  // and “toCurrency” pair while others are not, system will return the available benchmark properties and not return the benchmark properties
  // for benchmarks not configured for the “fromCurrency” and “toCurrency” pair.
  repeated BenchmarkDetails benchmarks = 2 [json_name = "benchmarks"];
  // From Amount also known as transaction amount that will be converted from the "fromCurrency" to the "toCurrency".
  // "." will be the radix separator.
  string from_amount = 3 [json_name = "fromAmount"];
  // 3 letter alphabetic ISO 4217 currency code (USD, EUR, GBP, ... etc) of the transaction currency.
  // This is the currency that would generally be bought in a transaction.
  string from_currency = 4 [json_name = "fromCurrency"];
  // ISO 4217 Currency Name of the fromCurrency
  string from_currency_name = 5 [json_name = "fromCurrencyName"];
  // Visa Treasury Exchange Rate for the “fromCurrency” and “toCurrency” currency pair.
  // This rate will not include any “additionalFee” or “additionalRate”.
  // Will reflect the last rate available on the “asOfDate” Will signify radix separator as “.” Rate with 10 digit decimal precision.
  string fx_rate_visa = 6 [json_name = "fxRateVisa"];
  // Applicable Exchange Rate between the fromCurrency and toCurrency currency pair including additionalRate and not including additionalFee on the asOfDate.
  // Will signify radix separator as “.” Rate with 10 digit decimal precision
  string fx_rate_with_additional_fee = 7 [json_name = "fxRateWithAdditionalFee"];
  // EpochDateTime in seconds when the Visa Treasury rates were updated for calculation on the as of the ”asOfDate”.
  // For example for ECB benchmark, will reflect 6th December,2019 when the “asOfDate” falls on 8th December 2019,
  // since 8th December is a weekend and rates are not published on this date by Visa Treasury.
  int64 last_updated_visa_rate = 8 [json_name = "lastUpdatedVisaRate"];
  // Number field with at most 6 decimal places using radix separator as "." Also referred to as Cardholder Billing amount with additional fee and rate.
  // Derived by converting the “fromAmount” using Visa Treasury Rates between “fromCurrency” and “toCurrency” currency pairs.
  // “additionalFee” and “additionalRate” is included in the calculations. Visa treasury rates used for conversion will be as of the last rates available on the epoch date/time.
  // Field be returned only if either the “additionalRate” or “additionalFee” is specified in the request
  string to_amount_with_additional_fee = 9 [json_name = "toAmountWithAdditionalFee"];
  // Number field with at most 6 decimal places using radix separator as "." To Amount also referred to as Cardholder Billing amount without “addtionalFee” or “additionalRate”.
  // Derived by converting the “fromAmount” using Visa Treasury Rates between from and to currency pairs. Additional Fee or rate is not included in the calculations.
  // Visa treasury rates used for conversion will be as of the last rates available on the epoch date/time.
  string to_amount_with_visa_rate = 10 [json_name = "toAmountWithVisaRate"];
  // 3 letter alphabetic ISO 4217 currency code (USD, EUR, GBP, ... etc) of the cardholder billing currency.
  // This is the currency that would generally be sold in a transaction.
  string to_currency = 11 [json_name = "toCurrency"];
  // ISO 4217 Currency Name of the toCurrency
  string to_currency_name = 12 [json_name = "toCurrencyName"];

  message BenchmarkDetails {
    // 3 letter ISO 4217 currency code ( USD,EUR,GBP ... etc ) denoting the base currency of the benchmark,
    // for example if benchmark system is ECB,then this will be EUR
    string  benchmark_base_currency = 1 [json_name = "benchmarkBaseCurrency"];
    // ISO 4217 currency name of the benchmark currency
    string  benchmark_base_currency_name = 2 [json_name = "benchmarkBaseCurrencyName"];
    // Exchange rate between the "from" and "to" currency pair using the benchmark rates on the asOfDate.
    // The rate will have 10 digit decimal precision. Will signify radix separator as “.”
    string  benchmark_fx_rate = 3 [json_name = "benchmarkFxRate"];
    // The benchmark system used for getting the benchmark properties. For example will be ECB for ECB benchmark rate system.
    // All benchmark rates and any markups are calculated based on the last available benchmark rates (from the standard rate system - in this case ECB)
    // and Visa treasury rates on the “asOfDate”.
    // This can be "ECB" or "VISA".
    string  benchmark_system = 4 [json_name = "benchmarkSystem"];
    // EpochDateTime in seconds when the benchmark rates were updated for calculation as of the ”asOfDate”. For example for ECB benchmark,
    // will reflect 24th December when the “asofDate” falls on 25th December since 25th December is a holiday and rates are not published on this date by ECB.
    int64 last_updated_benchmark_rate = 5 [json_name = "lastUpdatedBenchmarkRate"];
    // Number field with at most 6 decimal places using radix separator as ".".
    // Markup calculated between the toAmountWithAdditionalFee and toAmountWithBenchmarkRate.
    // Calculated as (toAmountWithAdditionalFee - toAmountWithBenchmarkRate)/toAmountWithBenchmarkRate.
    // Will be returned only if either the additionalRate or additionalFee is specified in the request. No symbols other than +/- and radix separator “.” allowed.
    string  markup_with_additional_fee = 6 [json_name = "markupWithAdditionalFee"];
    // Number field with at most 6 decimal places using radix separator as ".". Markup calculated between the toAmountWithVisaRate and toAmountWithBenchmarkRate.
    // Calculated as (toAmountWithVisaRate - toAmountWithBenchmarkRate)/toAmountWithBenchmarkRate. Rate of Mark-Up/Down in +/- (e.g. +2.00).
    // No symbols other than +/- and radix separator “.” will be returned.
    string  markup_without_additional_fee = 7 [json_name = "markupWithoutAdditionalFee"];
    // Number field with at most 6 decimal places using radix separator as "." Derived by converting the “fromAmount” using Benchmark Rates
    // between “fromCurrency” and “toCurrency” currency pairs.
    // “additionalFee” and “additionalRate” is not included in the calculations.
    // Benchmark rates used for conversion will be as of the last rates available on the “asOfDate” from the benchmark system for example ECB.
    string  to_amount_with_benchmark_rate = 8 [json_name = "toAmountWithBenchmarkRate"];
  }
}
