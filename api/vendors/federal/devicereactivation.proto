// protolint:disable MAX_LINE_LENGTH

/*
  This proto file defines Request & Response objects for Federal's
  Device/User Reactivation API. This Service is used for activating
  a temporarily deactivated user device/profile
*/

syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

message DeviceReactivationRequest {
  // E<PERSON><PERSON>'s credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string SenderCode = 1;
  string ServiceAccessId = 2;
  string ServiceAccessCode = 3;

  // Format: NEOREDVC<DDD><yyyyMMdd><hhmmss><XXXXX (5digit sequence Number)>
  string RequestId = 4;

  // Device ID of the client that has initiated the request
  string DeviceId = 5;

  // User profile id registered with the federal bank
  string UserProfileId = 6;

  // Device token that is issued by the federal bank at the time of device registration
  string DeviceToken = 7;

  string MobileNumber = 8;
  string EmailId = 9;
}

message DeviceReactivationResponse {
  string SenderCode = 1;
  string RequestId = 2;
  string DeviceToken = 3;
  string TranTimeStamp = 4;

  // Response code defines the success status of the request
  string Response = 5;

  // Description of response code
  string Reason = 6;
}
