syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

message ShippingAddressUpdateRequest {
  // Call back URL. API will send the response back to this URL, provided by the client.
  string callback_url = 1 [json_name = "RespUrl"];

  // E<PERSON><PERSON>'s credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 2 [json_name = "SenderCode"];
  string access_id = 3 [json_name = "ServiceAccessId"];
  string access_code = 4 [json_name = "ServiceAccessCode"];

  // expected format: NEOCDMDM<DDD><yyyyMMdd><hhmmss><XXXXX (5digit sequence Number)>
  // example: NEOCDMDM1132020043014155500003
  string request_id = 5 [json_name = "RequestId"];

  // Device ID of the client that has initiated the request
  string device_id = 6 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 7 [json_name = "DeviceToken"];
  // Actor's mobile number
  string mobile_number = 8 [json_name = "MobileNumber"];
  // Customer identifier provided by Federal.
  string customer_id = 9 [json_name = "CustomerId"];

  // Type of address that needs to be modified. Example: "Shipping"
  string address_category = 10 [json_name = "AddrCategory"];
  string city = 11 [json_name = "City"];
  string state = 12 [json_name = "State"];
  string country = 13 [json_name = "Country"];
  string address_line1 = 14 [json_name = "AddrLine1"];
  string address_line2 = 15 [json_name = "AddrLine2"];
  string address_line3 = 16 [json_name = "AddrLine3"];
  string pin_code = 17 [json_name = "PostalCode"];
}

message ShippingAddressUpdateResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // TODO(bhrigu): federal documentation has the json_name "DevieToken", verify on testing
  string device_token = 2 [json_name = "DeviceToken"];
  // Request ID that is passed in the request
  string request_id = 3 [json_name = "RequestId"];

  // Response Code for the Transaction
  string response_code = 4 [json_name = "ResponseCode"];

  string response_reason = 5 [json_name = "ResponseReason"];
  string response_action = 6 [json_name = "ResponseAction"];
}

message UpdateShippingAddressCallBackRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  string sender_code = 1 [json_name = "SenderCode"];

  string request_id = 2 [json_name="RequestId"];

  // Device token that is issued by the federal bank at the time of client's device registration
  string device_token = 3 [json_name="DeviceToken"];
  // Response Code
  string response_code = 4 [json_name="ResponseCode"];

  // Response Description
  string response_reason = 5 [json_name="ResponseReason"];

  // High level response codes depicting the stage of the transaction
  // PROCESSED, SUCCESS, FAILURE, SUSPECT
  string response_action = 6 [json_name="ResponseAction"];

  // Response message contains ErrorList(contains additional error information) field only if the
  // ResponseCode is ‘OBE0071’.
  repeated UpdateShippingAddressError errors = 7 [json_name="ErrorList"];
}

message UpdateShippingAddressError {
  string code = 1 [json_name="ErrorCode"];
  string reason = 2 [json_name="Reason"];
}
