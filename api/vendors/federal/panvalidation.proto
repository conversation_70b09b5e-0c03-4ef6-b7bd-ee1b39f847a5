// protolint:disable MAX_LINE_LENGTH

// Defines a service for integrating with the PAN Validation service
// provided by the partner bank

syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

message ValidatePANRequest {
  // Standard request header data
  string ChannelID = 1;
  string AccessId = 2;
  string AccessCode = 3;
  string RequestID = 4;

  // PAN numbers to be validated
  string PAN1 = 5;
  string PAN2 = 6;
  string PAN3 = 7;
  string PAN4 = 8;
  string PAN5 = 9;
}

message ValidatePANResponse {
  // Refer to enum PANValidationResponseCode
  string ResponseCode = 1;

  // Description of response code
  string ResponseDescription = 2;

  // Statuses corresponding to the PAN numbers in request
  repeated PANStatus Statuses = 3;
}

message PANStatus {
  // PAN Number
  string PAN = 1;

  // Refer to enum PANStatus
  string PANStatus = 2;

  // Name of PAN holder
  string FirstName = 3;
  string LastName = 4;

  // Title of the PAN holder maintained by NSDL eg. "Shri"
  string PANTitle = 5;

  // in DD/MM/YYYY format
  string LastUpdatedDate = 6;

  // aadhaar seeding status to check if aadhaar is linked to pan
  string AadhaarSeedingStatus = 7;
}
