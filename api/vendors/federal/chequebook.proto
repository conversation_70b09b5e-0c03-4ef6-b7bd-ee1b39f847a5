// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

message OrderChequebookRequest {
  // Epi<PERSON>'s credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string service_access_id = 2 [json_name = "ServiceAccessId"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];

  // expected format: federal<10 digit number>
  // example: federal2220202028
  string request_id = 4 [json_name = "RequestId"];

  // Device ID of the client that has initiated the request
  string device_id = 5 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 6 [json_name = "DeviceToken"];
  string mobile_number = 7 [json_name = "MobileNumber"];
  string account_number = 8 [json_name = "AccountNumber"];
  string cheque_number = 9 [json_name = "ChequeNumber"];
  string delivery_point = 10 [json_name = "DeliveryPoint"];
  string cred_block = 11 [json_name = "CredBlock"];
}

message OrderChequebookResponse {
  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];

  string device_token = 3 [json_name = "DeviceToken"];

  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];

  // Response Code for the Transaction
  string response_code = 5 [json_name = "ResponseCode"];

  string response_reason = 6 [json_name = "ResponseReason"];
  string response_action = 7 [json_name = "ResponseAction"];
}

message TrackChequebookRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string service_access_id = 2 [json_name = "ServiceAccessId"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];

  // expected format: federal<10 digit number>
  // example: federal2220202028
  string request_id = 4 [json_name = "RequestId"];

  // Device ID of the client that has initiated the request
  string device_id = 5 [json_name = "DeviceId"];
  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 6 [json_name = "DeviceToken"];
  string mobile_number = 7 [json_name = "MobileNumber"];
  string account_number = 8 [json_name = "AccountNumber"];
  string cred_block = 9 [json_name = "CredBlock"];
}

message TrackChequebookResponse {
  message TrackResult {
    string request_id = 1 [json_name = "RequestId"];
    string acc_no = 2 [json_name = "AccNo"];
    string courier_cd = 3 [json_name = "CourierCd"];
    string bar_code = 4 [json_name = "BarCode"];
    string request_date = 5 [json_name = "RequestDate"];
  }

  string sender_code = 1 [json_name = "SenderCode"];
  // Request ID that is passed in the request
  string request_id = 2 [json_name = "RequestId"];

  string device_token = 3 [json_name = "DeviceToken"];

  string tran_time_stamp = 4 [json_name = "TranTimeStamp"];

  // Response Code for the Transaction
  string response_code = 5 [json_name = "ResponseCode"];
  string response_reason = 6 [json_name = "ResponseReason"];
  string response_action = 7 [json_name = "ResponseAction"];

  repeated TrackResult track_result = 8 [json_name = "TrackResult"];
}

message IssueDigitalCancelledChequeRequest {
  // Epifi's credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string sender_code = 1 [json_name = "SenderCode"];
  string service_access_id = 2 [json_name = "ServiceAccessId"];
  string service_access_code = 3 [json_name = "ServiceAccessCode"];

  // request type: ADD/ENQUIRY
  string request_type = 4 [json_name = "req_type"];
  // account number of the federal account on which digital cancelled cheque will be issued.
  string account_number = 5 [json_name = "acct_num"];
  string input_channel = 6 [json_name = "input_chnl"];
  string channel_request_id = 7 [json_name = "chnl_req_id"];
}

message IssueDigitalCancelledChequeResponse {
  // unique request id of cheque issuance.
  string channel_request_id = 1 [json_name = "channel_req_id"];
  // Status to meaning mapping -
  // S000 - Record inserted for Digital cheque leaf addition
  // F001 - Duplicate insert not allowed
  // 0000 - Some error occured
  string status = 2 [json_name = "status"];
  // message from vendor
  string message = 3 [json_name = "message"];
  string cbs_status = 4 [json_name = "cbs_status"];
  string cbs_response = 5 [json_name = "cbs_response"];
}
