syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

message DedupeRequest {
  string reference_id = 1 [json_name = "reference_id"];
  string pan_number = 2 [json_name = "pan_number"];
  string aadhaar_number = 3 [json_name = "aadhaar_number"];
  string passport_number = 4 [json_name = "passport_number"];
  string driving_license = 5 [json_name = "driving_license"];
  string voter_id = 6 [json_name = "voter_id"];
  string mobile_number = 7 [json_name = "mobile_num"];
  string user_id = 8 [json_name = "user_id"];
  string dob = 9 [json_name = "dob"];
  string res_field1 = 10 [json_name = "res_field1"];
  string res_field2 = 11 [json_name = "res_field2"];
  string res_field3 = 12 [json_name = "res_field3"];
  string res_field4 = 13 [json_name = "res_field4"];
  string res_field5 = 14 [json_name = "res_field5"];
  string res_field6 = 15 [json_name = "res_field6"];
  string res_field7 = 16 [json_name = "res_field7"];
  string res_field8 = 17 [json_name = "res_field8"];
  string res_field9 = 18 [json_name = "res_field9"];
  string res_field10 = 19 [json_name = "res_field10"];
}

message DedupeResponse {
  string reference_id = 1 [json_name = "reference_id"];
  string ddupe_flag = 2 [json_name = "ddupe_flag"];
  string kyc_flag = 3 [json_name = "kyc_flag"];
  string kyc_profile_flag = 4 [json_name = "kyc_profile_flag"];
  string partial_kyc_flag = 5 [json_name = "partial_kyc_flag"];
  string dob_flag = 6 [json_name = "dob_flag"];
  string mobile_flag = 7 [json_name = "mobile_flag"];
  string customer_id = 8 [json_name = "customer_id"];
  string customer_name = 9 [json_name = "customer_name"];
  string nri_flag = 10 [json_name = "nri_flag"];
  string minor_flag = 11 [json_name = "minor_flag"];
  string reserve_field1 = 12 [json_name = "reserve_field1"];
  string reserve_field2 = 13 [json_name = "reserve_field2"];
  string reserve_field3 = 14 [json_name = "reserve_field3"];
  string reserve_field4 = 15 [json_name = "reserve_field4"];
  string reserve_field5 = 16 [json_name = "reserve_field5"];
  string reserve_field6 = 17 [json_name = "reserve_field6"];
  string reserve_field7 = 18 [json_name = "reserve_field7"];
  string reserve_field8 = 19 [json_name = "reserve_field8"];
  string reserve_field9 = 20 [json_name = "reserve_field9"];
  string reserve_field10 = 21 [json_name = "reserve_field10"];
  DedupeErrorResponse error_response = 22 [json_name = "errorResponse"];
  string reserve_field11 = 23 [json_name = "reserve_field11"];
  string reserve_field12 = 24 [json_name = "reserve_field12"];
  string reserve_field13 = 25 [json_name = "reserve_field13"];
  string reserve_field14 = 26 [json_name = "reserve_field14"];
  string reserve_field15 = 27 [json_name = "reserve_field15"];
}

message DedupeErrorResponse {
  string tran_time_Stamp = 1 [json_name = "trantimeStamp"];
  string status_code = 2 [json_name = "statuscode"];
  string status_reason = 3 [json_name = "statusreason"];
  string custom_code = 4 [json_name = "customcode"];
  string custom_reason = 5 [json_name = "customreason"];
  string tran_id = 6 [json_name = "tranId"];
  string description = 7 [json_name = "description"];
}
