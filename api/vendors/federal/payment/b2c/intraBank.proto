// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendors.federal.payment.b2c;

option go_package = "github.com/epifi/gamma/api/vendors/federal/payment/b2c";
option java_package = "com.github.epifi.gamma.api.vendors.federal.payment.b2c";


// Customer Details
message CustomerDetails {
  // Customer’s Name
  string name = 1 [json_name = "Name"];
  // Customer’s Account Number
  string account_number = 2 [json_name = "AccNumber"];
  // Conditional Mandatory. Will be passed only in case of Remitter
  string account_type = 3 [json_name = "Acctype"];
  // Customer’s mobile number. Notifications will be sent to this mobile.
  string mobile = 4 [json_name = "Mobile"];
  // Customer’s e-mail. Notifications will be sent on this Id.
  string email = 5 [json_name = "Email"];
  // Can take only BOTH || EMAIL || NONE || SMS which gives information about how notification will be sent to the customer
  // BOTH will send Email and SMS notification to mobile and email-id
  // EMAIL will send Email Notification to email-id
  // SMS will send SMS notification to mobile
  // NONE : No notification will be sent
  string notification_flag = 6 [json_name = "Notification_Flag"];
  string ifsc = 7 [json_name = "IFSC"];
}

message InitiateIntraBankPaymentRequest {
  // Call back URL. API will send the response back to this URL, provided by the client.
  string callback_url = 1 [json_name = "respUrl"];
  // We will be given unique userId,password and sender code for Epifi Businees account through which rewards transfer
  // will occur. Credentials will be provided by Federal Bank and will include
  // user_id, password and sender code
  string user_id = 2 [json_name = "userid"];
  string password = 3 [json_name = "password"];
  string sender_code = 4 [json_name = "sendercd"];
  // Date of Transaction in “DD-MM-YYYY” format E.g. “23-03-2020”
  string transaction_date = 5 [json_name = "tranDate"];
  // Alphanumeric. Client should pass unique value to each call to identify the request.
  // API will validate the uniqueness against the Client and Channel
  string reference_id = 6 [json_name = "ReferenceId"];
  // Conditional Mandatory. The value provided will be displayed in account statements. If value is not provided
  // Reference Id value will be used in account statement.
  string cust_ref_no = 7 [json_name = "Cust_Ref_No"];
  // Remitter details. Bank Account from which the money is transferred.
  CustomerDetails remitter = 8 [json_name = "RemmiterDetails"];
  // Beneficiary details. Bank Account to which the money is transferred
  CustomerDetails beneficiary = 9 [json_name = "BeneficiaryDetails"];
  // Amount to be transferred. Will be of the format 100.23 with maximum of 2 decimal places.
  string amount = 10 [json_name = "Amount"];
  // Description of the transaction
  string remarks = 11 [json_name = "Remarks"];
  // Additional transaction details if any. This will be part of the e-mail notification sent to the beneficiary.
  string sender_data = 12 [json_name = "Sender_Data"];
}

message InitiateIntraBankPaymentAckResponse {
  // Reference Id that is passed in the request.
  string reference_id = 1 [json_name = "ReferenceId"];
  // Transaction TimeStamp
  string tran_time_stamp = 2 [json_name = "tranTimeStamp"];
  // Response code for the transaction
  string response_code = 3 [json_name = "responseCode"];
  // Response Description for the Transaction
  string reason = 4 [json_name = "reason"];
}
