// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendors.federal.payment.b2c;

option go_package = "github.com/epifi/gamma/api/vendors/federal/payment/b2c";
option java_package = "com.github.epifi.gamma.api.vendors.federal.payment.b2c";

message UpdateB2CTransactionRequest {
  // Reference Id that is passed in the request.
  string request_id = 1 [json_name = "REFERENCE_ID"];
  // unique identifier for a transaction
  // it may be empty and non empty depending on the status of transaction
  string utr = 2 [json_name = "UTR"];
  // account from which money is transferred
  string remitter_account_number = 3 [json_name = "REMITTER_ACCOUNTNO"];
  // account to which money is transferred
  string beneficiary_account_number = 4 [json_name = "BENEFICIARY_ACCOUNTNO"];
  // Registered name of Account holder with the beneficiary bank
  // Populated only in the case of IMPS transaction
  string credited_account_name = 5 [json_name = "CREDITED_ACCOUNT_NAME"];
  // transaction amount passed in the request
  double amount = 6 [json_name = "TRAN_AMOUNT"];
  // Timestamp when transaction got processed
  string trans_timestamp = 7 [json_name = "TRAN_DATE"];
  // Response Code for the Transaction
  string response_code = 8 [json_name = "RESPONSE_CODE"];
  // Response Description for the Transaction
  string response_reason = 9 [json_name = "RESPONSE_REASON"];
  // High level response codes depicting the stage of the transaction. Can take these
  // PROCESSED, SUCCESS, FAILURE, SUSPECT values
  string response_action = 10 [json_name = "RESPONSE_ACTION"];
}

message EnquireTransactionStatusRequest {
  // Business account credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string user_id = 1 [json_name = "userid"];
  string password = 2 [json_name = "password"];
  string sender_code = 3 [json_name = "sendercd"];
  // Client should pass unique value to each call, to identify the request
  string reference_id = 4 [json_name = "ReferenceId"];
  // Original ReferenceId i.e., ReferenceId of the transaction about which the status is sought.
  // API will return the status of the transaction identified by the this reference Id.
  string original_reference_id = 5 [json_name = "Org_ReferenceId"];
  // Determine the service for which the status is being enquired
  // Can be one of NEFT, IMPS, FT, RTGS, UPI
  string transaction_type = 6 [json_name = "TxnType"];
  // Unique Id which has been assigned to this transaction.
  string transaction_id = 7 [json_name = "TxnId"];
  // Date on which this transaction is initiated. Date should be in this format “DD-MM-YYYY”
  string transaction_date = 8 [json_name = "Tran_Date"];
}

message EnquireTransactionStatusResponse {
  // ReferenceId passed in the request
  string reference_id = 2 [json_name = "ReferenceId"];
  // Original reference Id passed in the request
  string original_reference_id = 3 [json_name = "Org_ReferenceId"];
  // Transaction date passed in the request
  string transaction_date = 4 [json_name = "tranTimeStamp"];
  // Response Code for the Transaction
  string response = 5 [json_name = "Response"];
  // Response Description for the Transaction
  string reason = 6 [json_name = "Reason"];
  // transaction details for which enquiry is requested
  message OriginalTransactionDetails {
    // unique transaction reference
    string utr = 1 [json_name = "Utr"];
    // account number from which amount is deducted
    string remitter_account_number = 2 [json_name = "RemitterAccountNumber"];
    // account number to which amount is credited
    string beneficiary_account_number = 3 [json_name = "BeneficiaryAccountNumber"];
    // transacted amount
    string transaction_amount = 4 [json_name = "TranAmount"];
    // transaction time
    string created_time = 5 [json_name = "CreatedTime"];
    // response code from vendor
    string response_code = 6 [json_name = "ResponseCode"];
    // response reason from vendor
    string response_reason = 7 [json_name = "ResponseReason"];
    // response action based on which user need to take call.
    // value could be SUCCESS/FAILURE/SUSPECT
    string response_action = 8 [json_name = "ResponseAction"];
    // beneficiary name of the account to which amount is credited
    // note: this field may not be present for all the responses(may depend on version of the api used)
    string beneficiary_name = 9 [json_name = "BeneficiaryName"];
  }
  OriginalTransactionDetails original_txn_details = 7 [json_name = "Original_Transaction_Details"];

}

message BalanceEnquiryRequest {
  // Business account credentials provided by Federal bank
  // Credentials will be different for UAT and PROD
  // Credentials will be provided by Federal Bank's API support team
  // Credentials include user_id, password and sender code
  string user_id = 1 [json_name = "userid"];
  string password = 2 [json_name = "password"];
  string sender_code = 3 [json_name = "sendercd"];
  // Client should pass unique value to each call, to identify the request
  string reference_id = 4 [json_name = "ReferenceId"];
  // b2c account's number to get balance data
  string account_number = 5 [json_name = "Account_Number"];
}

message BalanceEnquiryResponse {
  string sender_code = 1 [json_name = "sendercd"];
  string reference_id = 2 [json_name = "ReferenceId"];
  string account_number = 3 [json_name = "Account_Number"];
  string customer_name = 4 [json_name = "CustomerName"];
  string account_type = 5 [json_name = "Account_Type"];
  string account_status = 6 [json_name = "Account_Status"];
  string ledger_balance = 7 [json_name = "LedgerBalance"];
  string available_balance = 8 [json_name = "AvailableBalance"];
  string float_balance = 9 [json_name = "FloatBalance"];
  string ffd_balance = 10 [json_name = "FFDBalance"];
  string user_defined_balance = 11 [json_name = "UserDefinedBalance"];
  string balance_currency = 12 [json_name = "BalCurrencycode"];
  string response = 13 [json_name = "Response"];
  string reason = 14 [json_name = "Reason"];
  string cust_id = 15 [json_name = "CustId"];
}
