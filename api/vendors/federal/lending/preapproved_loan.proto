syntax = "proto3";

package vendors.federal.lending;


option go_package = "github.com/epifi/gamma/api/vendors/federal/lending";
option java_package = "com.github.epifi.gamma.api.vendors.federal.lending";

message GetInstantLoanClosureRequest {
  string resp_url = 1 [json_name = "respUrl"];
  string access_id = 2 [json_name = "ServiceAccessId"];
  string access_code = 3 [json_name = "ServiceAccessCode"];
  string sender_code = 4 [json_name = "SenderCode"];
  string request_id = 5 [json_name = "RequestId"];
  string value_date = 6 [json_name = "ValueDate"]; // (DD-MM-YYYY)
  string customer_account_number = 7 [json_name = "CustomerAccNum"];
  string loan_account_number = 8 [json_name = "LoanAccNumber"];
  string net_pay_amount = 9 [json_name = "NetPayAmount"];
  string pay_amount_id = 10 [json_name = "NetPayAmt_Id"];
  string remarks = 11 [json_name = "Remarks"];
}

message GetInstantLoanClosureResponse {
  string request_id = 1 [json_name = "RequestId"];
  string loan_account_number = 2 [json_name = "LoanAccNumber"];
  string loan_close_date = 3 [json_name = "LoanCloseDate"];
  string transaction_id = 4 [json_name = "TranRefId"]; //Only in Successful Response
  string response_action = 5 [json_name = "ResponseAction"];
  string response_code = 6 [json_name = "ResponseCode"];
  string response_reason = 7 [json_name = "ResponseReason"];
  string net_pay_amount = 8 [json_name = "NetPayAmount"]; //Only in Acknowledge Response
  string net_pay_id = 9 [json_name = "NetPayAmt_Id"]; //Only in some of the Failure responses
}

message GetInstantLoanClosureEnquiryRequest {
  // Will be using fields and URL of Gold Loan Calculate Closure Amount API
  string account_number = 1 [json_name = "AccountNumber"];
  string date = 2 [json_name = "Date"]; // (DD-MM-YYYY)
}

message GetInstantLoanClosureEnquiryResponse {
  // Will be using fields and URL of Gold Loan Calculate Closure Amount API
  string account_number = 1 [json_name = "AccountNumber"];
  string closure_date = 2 [json_name = "ClosureDate"];  // (DD-MM-YYYY)
  string closure_amount = 3 [json_name = "ClosureAmount"];
  // will be populated for failure responses
  string error = 4 [json_name = "Error"];
}

message GetInstantLoanBalanceRequest {
  string user_id = 1 [json_name = "userid"];
  string password = 2 [json_name = "password"];
  string sender_code = 3 [json_name = "sendercd"];
  string reference_id = 4 [json_name = "ReferenceId"];
  string account_number = 5 [json_name = "Account_Number"];
}

message GetInstantLoanBalanceResponse {
  // will not be present for failure responses
  string account_number = 1 [json_name = "Account_Number"];
  string account_status = 2 [json_name = "Account_Status"];
  string account_type = 3 [json_name = "Account_Type"];
  string available_balance = 4 [json_name = "AvailableBalance"];
  string balance_currency = 5 [json_name = "BalCurrencycode"];
  string customer_name = 6 [json_name = "CustomerName"];
  string ffd_balance = 7 [json_name = "FFDBalance"];
  string float_balance = 8 [json_name = "FloatBalance"];
  string ledger_balance = 9 [json_name = "LedgerBalance"];
  string reason = 10 [json_name = "Reason"];
  string reference_id = 11 [json_name = "ReferenceId"];
  string response = 12 [json_name = "Response"];
  string user_defined_balance = 13 [json_name = "UserDefinedBalance"];
  string sender_code = 14 [json_name = "sendercd"];

  // will not be present for success responses
  ErrorResponse error = 15 [json_name = "errorResponse"];

  message ErrorResponse {
    string transaction_time = 1 [json_name = "trantimeStamp"];
    string status_code = 2 [json_name = "statuscode"];
    string status_reason = 3 [json_name = "statusreason"];
    string custom_code = 4 [json_name = "customcode"];
    string custom_reason = 5 [json_name = "customreason"];
    string transaction_id = 6 [json_name = "tranId"];
    string description = 7 [json_name = "description"];
  }
}

message GetInstantLoanStatusEnquiryRequest {
  InstantLoanStatusEnquiryRequest instant_loan_status_enquiry_request = 1 [json_name = "InstantLoanAPIStatusEnqRequest"];

  message InstantLoanStatusEnquiryRequest {
    Header header = 1 [json_name = "header"];
    Body body = 2 [json_name = "body_deprecated", deprecated = true];

    message Body {
      string sender_code = 1 [json_name = "senderCode"];
      string application_id = 2 [json_name = "applicationId"];
      string api_type = 3 [json_name = "apiType"];
    }

    BodyV2 body_v2 = 3 [json_name = "body"];
    message BodyV2 {
      string sender_code = 1 [json_name = "senderCode"];
      string api_type = 2 [json_name = "apiType"];
      string application_id = 3 [json_name = "applicationId"];

    }
  }


}

message GetInstantLoanStatusEnquiryResponse {
  InstantLoanStatusEnquiryResponse instant_loan_status_enquiry_response = 1 [json_name = "InstantLoanAPIStatusEnqResponse"];

  message InstantLoanStatusEnquiryResponse {
    string response_code = 1 [json_name = "responseCode"];
    string response_reason = 2 [json_name = "responseReason"];
    string application_id = 3 [json_name = "applicationId"];
  }
}

message GetInstantLoanUnblockRequest {
  InstantLoanUnblockRequest instant_loan_unblock_request = 1 [json_name = "InstantLoanUnBlockRequest"];

  message InstantLoanUnblockRequest {
    Header header = 1 [json_name = "header"];
    Body body = 2 [json_name = "body"];

    message Body {
      string sender_code = 1 [json_name = "senderCode"];
      string application_id = 2 [json_name = "applicationId"];
    }
  }
}

message GetInstantLoanUnblockResponse {
  InstantLoanUnblockResponse instant_loan_unblock_response = 1 [json_name = "InstantLoanUnBlockResponse"];

  message InstantLoanUnblockResponse {
    string response_code = 1 [json_name = "responseCode"];
    string response_reason = 2 [json_name = "responseReason"];
    string application_id = 3 [json_name = "applicationId"];
  }
}

message GetInstantLoanInfoRequest {
  InstantLoanInfoRequest instant_loan_info_request = 1 [json_name = "InstantLoanInfoRequest"];

  message InstantLoanInfoRequest {
    Header header = 1 [json_name = "header"];
    Body body = 2 [json_name = "body"];

    message Body {
      string sender_code = 1 [json_name = "senderCode"];
      string application_id = 2 [json_name = "applicationId"];
    }
  }
}

message GetInstantLoanInfoResponse {
  InstantLoanInfoResponse instant_loan_info_response = 1 [json_name = "InstantLoanInfoResponse"];

  message InstantLoanInfoResponse {
    string response_code = 1 [json_name = "responseCode"];
    string response_reason = 2 [json_name = "responseReason"];
    string application_id = 3 [json_name = "applicationId"];
    string loan_state = 4 [json_name = "loanState"];
  }
}

message GetInstantLoanApplicationRequest {
  InstantLoanProcessRequest instant_loan_process_request = 1 [json_name = "InstantLoanProcessRequest"];

  message InstantLoanProcessRequest {
    Header header = 1 [json_name = "header"];
    Body body = 2 [json_name = "body"];

    message Body {
      string sender_code = 1 [json_name = "senderCode"];
      string otp = 2 [json_name = "otp"];
      string offer_id = 3 [json_name = "offerId"];
      string phone_number = 4 [json_name = "phoneNumber"];
      string masked_account_number = 5 [json_name = "maskedAccountNumber"];
      double processing_fee = 6 [json_name = "processingFee"];
      string customer_ip_device_details = 7 [json_name = "custIp_Devicedetails"];
      string application_id = 8 [json_name = "applicationId"];
      double loan_amount = 9 [json_name = "loanAmount"];
      double emi_amount = 10 [json_name = "emiAmount"];
      double interest_rate = 11 [json_name = "interestRate"];
      int32 tenure_months = 12 [json_name = "tenureMonths"];
    }
  }
}

message GetInstantLoanApplicationResponse {
  InstantLoanProcessResponse instant_loan_process_response = 1 [json_name = "InstantLoanProcessResponse"];

  message InstantLoanProcessResponse {
    string response_code = 1 [json_name = "responseCode"];
    string response_reason = 2 [json_name = "responseReason"];
    string application_id = 3 [json_name = "applicationId"];
  }
}

message GetInstantLoanOTPRequest {
  InstantLoanOtpRequest instant_loan_otp_request = 1 [json_name = "InstantLoanOTPRequest"];

  message InstantLoanOtpRequest {
    Header header = 1 [json_name = "header"];
    Body body = 2 [json_name = "body"];

    message Body {
      string sender_code = 1 [json_name = "senderCode"];
      string offer_id = 2 [json_name = "offerId"];
      string application_id = 3 [json_name = "applicationId"];
      string otp_token = 4 [json_name = "otpToken"];
      string phone_number = 5 [json_name = "phoneNumber"];
    }
  }
}

message GetInstantLoanOTPResponse {
  InstantLoanOtpResponse instant_loan_otp_response = 1 [json_name = "InstantLoanOTPResponse"];

  message InstantLoanOtpResponse {
    string response_code = 1 [json_name = "responseCode"];
    string response_reason = 2 [json_name = "responseReason"];
    string application_id = 3 [json_name = "applicationId"];
    int64 otp = 4 [json_name = "OTP"];
  }
}

message GetInstantLoanOffersRequest {
  InstantLoanOffersRequest instant_loan_offers_request = 1 [json_name = "InstantLoanOffersRequest"];

  message InstantLoanOffersRequest {
    Header header = 1 [json_name = "header"];
    Body body = 2 [json_name = "body"];

    message Body {
      string sender_code = 1 [json_name = "senderCode"];
      string req_type = 2 [json_name = "ReqType"];
      string application_id = 3 [json_name = "applicationId"];
      string pan = 4 [json_name = "PAN"];
      string phone_number = 5 [json_name = "phoneNumber"];
    }
  }
}

message GetInstantLoanOffersResponse {
  InstantLoanOffersResponse instant_loan_offers_response = 1 [json_name = "InstantLoanOffersResponse"];

  message InstantLoanOffersResponse {
    string response_code = 1 [json_name = "responseCode"];
    string response_reason = 2 [json_name = "responseReason"];
    string loan_eligible_flag = 3 [json_name = "loanEligibleFlag"];
    string offer_id = 4 [json_name = "offerId"];
    double max_amount = 5 [json_name = "maxAmount"];
    double max_allowed_emi = 6 [json_name = "maxAllowedEMI"];
    double max_tenure_months = 7 [json_name = "maxTenureMonths"];
    repeated RangeToFrom interest_rate = 8 [json_name = "interestRate"];
    repeated RangeToFrom processing_fee_percentage = 9 [json_name = "processingFeePercentage"];
    double service_tax_gst = 10 [json_name = "serviceTax_GST"];
    int64 expiry_date_timestamp_seconds = 11 [json_name = "expiryDateTimestampSeconds"];
    string application_id = 12 [json_name = "applicationId"];

    message RangeToFrom {
      double range_from = 1 [json_name = "rangeFrom"];
      double range_to = 2 [json_name = "rangeTo"];
      string type = 3 [json_name = "type"];
      double value = 4 [json_name = "value"];
    }
  }
}

message Header {
  string user_name = 1 [json_name = "userName"];
  string password = 2 [json_name = "password"];
}

message FetchLoanDetailsRequest {
  string customer_id = 1 [json_name = "Customer_Id"];
  string phone_number = 2 [json_name = "Mobile_No"];
  string sender_cd = 3 [json_name = "Sender_Cd"];
}

message FetchLoanDetailsResponse {
  string request_id = 1 [json_name = "RequestID"];
  string sender_id = 2 [json_name = "SenderId"];
  string sender_code = 3 [json_name = "Sender_Code"];
  Response response = 4 [json_name = "Response"];
  ErrorDetails error_details = 5 [json_name = "ErrorDetails"];

  message Response {
    Fetch fetch = 1 [json_name = "FETCH"];

    message Fetch {
      repeated LoanDetails loan_details = 1 [json_name = "loandetails"];

      message LoanDetails {
        string account_number = 1 [json_name = "ACCNTNUM"];
        string phone_number = 2 [json_name = "MOBILE_NUM"];
        string loan_period = 3 [json_name = "LOAN_PERIOD"];
        string open_date = 4 [json_name = "OPEN_DATE"];
        string loan_amount = 5 [json_name = "LOAN_AMOUNT"];
        string end_date = 6 [json_name = "END_DATE"];
        string interest_rate = 7 [json_name = "INT_RATE"];
        string account_type = 8 [json_name = "ACCNTTYPE"];
        string scheme_description = 9 [json_name = "SCHM_DESC"];
        string next_pay_date = 10 [json_name = "NEXT_PAY_DATE"];
        string next_pay_amount = 11 [json_name = "NEXT_PAY_AMNT"];
        string due_amount = 12 [json_name = "DUE_AMNT"];
        string status = 13 [json_name = "STATUS"];
        string ledger_balance = 14 [json_name = "LEDGERBALANCE"];
        string free_text1 = 15 [json_name = "FREETEXT1"];
        string free_text2 = 16 [json_name = "FREETEXT2"];
        string free_text3 = 17 [json_name = "FREETEXT3"];
        string free_text4 = 18 [json_name = "FREETEXT4"];
        string free_text5 = 19 [json_name = "FREETEXT5"];
        string free_text6 = 20 [json_name = "FREETEXT6"];
        string free_text7 = 21 [json_name = "FREETEXT7"];
        string free_text8 = 22 [json_name = "FREETEXT8"];
        string free_text9 = 23 [json_name = "FREETEXT9"];
        string free_text10 = 24 [json_name = "FREETEXT10"];
      }
    }
  }

  message ErrorDetails {
    string code = 1 [json_name = "Error_Code"];
    string reason = 2 [json_name = "Error_Reason"];
  }
}

message FetchLoanDetailsErrorResponse {
  string request_id = 1 [json_name = "RequestID"];
  string sender_id = 2 [json_name = "SenderId"];
  string sender_code = 3 [json_name = "Sender_Code"];
  string response = 4 [json_name = "Response"];
  ErrorDetails error_details = 5 [json_name = "ErrorDetails"];
  message ErrorDetails {
    string code = 1 [json_name = "Error_Code"];
    string reason = 2 [json_name = "Error_Reason"];
  }
}

message GetRepaymentScheduleRequest {
  string request_uuid = 1 [json_name = "requestUUID"];
  string service_request_id = 2 [json_name = "serviceRequestId"];
  string message_datetime = 3 [json_name = "messageDatetime"];
  string account_number = 4 [json_name = "accountNumber"];
  string sol_id = 5 [json_name = "solId"];
}

message GetRepaymentScheduleResponse {
  Body body = 1 [json_name = "Body"];
  message Body {
    AmortScheduleResponse amort_schedule_response = 1 [json_name = "findAmortScheduleResponse"];
    ErrorResponse error_response = 2 [json_name = "Error"];

    message AmortScheduleResponse {
      AmortShdlOutStruct amort_schedule_out_struct = 1 [json_name = "AmortShdlOutStruct"];

      message AmortShdlOutStruct {
        repeated OutLl out_ll = 1 [json_name = "ooutLL"];

        message OutLl {
          AmortScheduleDetails amort_schedule_details = 1 [json_name = "amortShdlDtls"];
          Key key = 2 [json_name = "key"];

          message AmortScheduleDetails {
            Amount comb_instl_amount = 1 [json_name = "combInstlAmt"];
            Amount cumm_int_amount = 2 [json_name = "cummIntAmt"];
            Amount cumm_principal_amount = 3 [json_name = "cummPrincAmt"];
            string flow_date = 4 [json_name = "flowDate"];
            Amount installment_amount = 5 [json_name = "instlAmt"];
            Amount int_amount = 6 [json_name = "intAmt"];
            Amount principal_amount = 7 [json_name = "princAmt"];
            Amount principal_outstanding_amount = 8 [json_name = "princOutStanding"];

            message Amount {
              string currency_code = 1 [json_name = "currencyCode"];
              string amount_value = 2 [json_name = "amountValue"];
            }
          }

          message Key {
            string serial_number = 1 [json_name = "serial_num"];
          }
        }
      }
    }
    message ErrorResponse {
      FiBusinessException fi_business_exception = 1 [json_name = "FIBusinessException"];
      message FiBusinessException {
        ErrorDetails error_details = 1 [json_name = "ErrorDetail"];
        message ErrorDetails {
          string error_code = 1 [json_name = "ErrorCode"];
          string error_dessc = 2 [json_name = "ErrorDesc"];
          string error_src = 3 [json_name = "ErrorSource"];
          string error_typ = 4 [json_name = "ErrorType"];
        }
      }
    }
  }
}

message LoanDisbursementRequest {
  string partner_name = 1 [json_name = "PartnerName"];
  string partner_code = 2 [json_name = "PartnerCode"];
  string sender_code = 3 [json_name = "SenderCode"];
  string request_id = 4 [json_name = "Request_ID"];
  string customer_id = 5 [json_name = "CustomerId"];
  string loan_account_number = 6 [json_name = "LoanAccountNumber"];
  int64 loan_amount = 7 [json_name = "LoanAmount"];
  string operative_account_number = 8 [json_name = "OperativeAccountNumber"];
  string beneficiary_ifsc = 9 [json_name = "BeneficiaryIFSC"];
  string beneficiary_account_number = 10 [json_name = "BeneficiaryAccountNumber"];
  string beneficiary_account_name = 11 [json_name = "BeneficiaryAccountName"];
  string pennydrop_req_id = 12 [json_name = "PennydropReqId"];
  string pennydrop_tran_date = 13 [json_name = "PennydropTranDate"];
  string hunter_req_id = 14 [json_name = "HunterReqId"];
  string bre_req_id = 15 [json_name = "BreReqId"];
  string payment_mode = 16 [json_name = "PaymentMode"];
  string reserved_field2 = 17 [json_name = "ReservedField2"];
  string reserved_field3 = 18 [json_name = "ReservedField3"];
  string reserved_field4 = 19 [json_name = "ReservedField4"];
  string reserved_field5 = 20 [json_name = "ReservedField5"];
  string reserved_field6 = 21 [json_name = "ReservedField6"];
  string reserved_field7 = 22 [json_name = "ReservedField7"];
  string reserved_field8 = 23 [json_name = "ReservedField8"];
  string reserved_field10 = 24 [json_name = "ReservedField10"];
}

message LoanDisbursementReponse {
  string reference_number = 1 [json_name = "reference_number"];
  string response_code = 2 [json_name = "responseCode"];
  string message = 3 [json_name = "message"];
}

message LoanDisbursementEnquiryRequest {
  string partner_name = 1 [json_name = "PartnerName"];
  string partner_code = 2 [json_name = "PartnerCode"];
  string sender_code = 3 [json_name = "SenderCode"];
  string request_id = 4 [json_name = "Request_ID"];
  string loan_request_id = 5 [json_name = "LoanRequestId"];
}

message LoanDisbursementEnquiryResponse {
  string reference_number = 1 [json_name = "reference_number"];
  string response_code = 2 [json_name = "responseCode"];
  string message = 3 [json_name = "message"];
}
