syntax = "proto3";

package vendors.federal.lending;

option go_package = "github.com/epifi/gamma/api/vendors/federal/lending";
option java_package = "com.github.epifi.gamma.api.vendors.federal.lending";

// API Doc: https://docs.google.com/document/d/1buoPl3y6P4SermBAEfjLpsE2aJKFTwRe/edit?pli=1
message CreditCardDetailsUpdateRequest {
  string cc_acct_no = 1 [json_name = "CC_ACCT_NO"];
  string cbs_cust_id = 2 [json_name = "CBS_CUST_ID"];
  string card_limit = 3 [json_name = "CARD_LIMIT"];
  string cc_open_date = 4 [json_name = "CC_OPEN_DATE"];
  string status = 5 [json_name = "STATUS"];
  string channel = 6 [json_name = "CHANNEL"];
  string input1 = 7 [json_name = "INPUT1"];
  string input2 = 8 [json_name = "INPUT2"];
  string input3 = 9 [json_name = "INPUT3"];
  string input4 = 10 [json_name = "INPUT4"];
  string input5 = 11 [json_name = "INPUT5"];
}

message CreditCardDetailsUpdateResponse {
  string status = 1 [json_name = "STATUS"];
  string message = 2 [json_name = "MESSAGE"];
}
