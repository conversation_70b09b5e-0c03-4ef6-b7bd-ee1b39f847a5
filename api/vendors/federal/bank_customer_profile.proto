syntax = "proto3";

package vendors.federal;

option go_package = "github.com/epifi/gamma/api/vendors/federal";
option java_package = "com.github.epifi.gamma.api.vendors.federal";

message UpdateProfileAtBankRequest {
  string request_id = 1 [json_name = "requestId"];
  string channel_id = 2 [json_name = "channelId"];
  string customer_id = 3 [json_name = "customerId"];
  string request_type = 4 [json_name = "reqType"];
  string ekyc_rrn = 5 [json_name = "EKYCrrn"];
  string date_of_birth = 6 [json_name = "DOB"];
  string address_category = 7 [json_name = "addCategory"];
  string address_line_1 = 8 [json_name = "addrLine1"];
  string address_line_2 = 9 [json_name = "addrLine2"];
  string city_code = 10 [json_name = "cityCode"];
  string state_code = 11 [json_name = "stateCode"];
  string country_code = 12 [json_name = "countryCode"];
  string pin_code = 13 [json_name = "pinCode"];
  string qualification = 14 [json_name = "qualification"];
  string income_range_from = 15 [json_name = "incomeRangeFrom"];
  string income_range_to = 16 [json_name = "incomeRangeTo"];
  string community = 17 [json_name = "community"];
  string caste = 18 [json_name = "caste"];
  string occupation = 19 [json_name = "occupation"];
  string pan = 20 [json_name = "panNumber"];
  // identifier for updation, eg. OCCUPATION
  string profile_field = 21 [json_name = "reserveFreetext10"];
}

message UpdateProfileAtBankResponse {
  string request_id = 1 [json_name = "requestId"];
  string status = 2 [json_name = "status"];
  string message = 3 [json_name = "message"];
  string customer_id = 4 [json_name = "CustomerID"];
  // federal bank (core banking solution) internal status for profile update request
  string cbs_status = 5 [json_name = "cbsStatus"];
  // federal bank internal system response for profile update request
  string cbs_response = 6 [json_name = "cbsResponse"];
}

message CheckProfileUpdateStatusRequest {
  string request_id = 1 [json_name = "requestId"];
  string channel_id = 2 [json_name = "channelId"];
  string customer_id = 3 [json_name = "customerId"];
  string request_type = 4 [json_name = "reqType"];
  string ekyc_rrn = 5 [json_name = "EKYCrrn"];
  string date_of_birth = 6 [json_name = "DOB"];
  string address_category = 7 [json_name = "addCategory"];
  string address_line_1 = 8 [json_name = "addrLine1"];
  string address_line_2 = 9 [json_name = "addrLine2"];
  string city_code = 10 [json_name = "cityCode"];
  string state_code = 11 [json_name = "stateCode"];
  string country_code = 12 [json_name = "countryCode"];
  string pin_code = 13 [json_name = "pinCode"];
  string qualification = 14 [json_name = "qualification"];
  string income_range_from = 15 [json_name = "incomeRangeFrom"];
  string income_range_to = 16 [json_name = "incomeRangeTo"];
  string community = 17 [json_name = "community"];
  string caste = 18 [json_name = "caste"];
  string occupation = 19 [json_name = "occupation"];
  string pan = 20 [json_name = "panNumber"];
}

message CheckProfileUpdateStatusResponse {
  string request_id = 1 [json_name = "requestId"];
  string status = 2 [json_name = "status"];
  string message = 3 [json_name = "message"];
  string customer_id = 4 [json_name = "CustomerID"];
  // federal bank (core banking solution) internal status for profile update request
  string cbs_status = 5 [json_name = "cbsStatus"];
  // federal bank internal system response for profile update request
  string cbs_response = 6 [json_name = "cbsResponse"];
}
