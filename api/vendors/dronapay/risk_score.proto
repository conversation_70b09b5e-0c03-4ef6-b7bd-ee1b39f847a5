syntax = "proto3";

package vendors.dronapay;

import "api/vendors/dronapay/user.proto";

option go_package = "github.com/epifi/gamma/api/vendors/dronapay";
option java_package = "com.github.epifi.gamma.api.vendors.dronapay";

message ScoreAttribs {
  // originating point for transaction EXTERNAL, APP
  string provenance = 1 [json_name = "provenance"];
  // p2p or p2m transaction
  string p2p_p2m = 2 [json_name = "p2p_p2m"];
  // domestic or international transaction
  string domestic_international = 3 [json_name = "domestic_international"];
  // card country code
  string card_country_code = 4 [json_name = "card_country_code"];
  // raw error code
  string raw_error_code = 5 [json_name = "raw_error_code"];
  // status
  string status = 6 [json_name = "status"];
  // wallet or non wallet transaction
  bool is_wallet_transaction = 7 [json_name = "is_wallet_transaction"];
  // comments/remarks added by an user during transaction
  string remarks = 8 [json_name = "remarks"];
  // determines the processing approach for a transaction
  int32 skip_processing = 9 [json_name = "skip_processing"];
  // granular payment product or payment channel e.g. UPI|NEFT|IMPS|INTRA BANK|RTGS|AEPS
  string sub_class = 10 [json_name = "sub_class"];
  // category of transaction e.g. INTEREST|RENT etc.
  // deprecated in favour of to_category, from_category
  string category = 11 [json_name = "category", deprecated = true];
  // transaction category from perspective of receiver eg. for a rent transaction this should be INCOME
  string to_category = 12 [json_name = "to_category"];
  // transaction category from perspective of payer eg. for a rent transaction this should be RENT
  string from_category = 13 [json_name = "from_category"];
}
// Score Request API is used to get the risk score of a transaction.
message ScoreRequest {
  // Score Request ID, marks uniqueness to this score request (Unique & Case Sensitive)
  string reqid = 1 [json_name = "reqid"];
  // Organization (Epifi)
  string org = 2 [json_name = "org"];
  // Request Timestamp (ISO-8601 with fractions and timezone)
  string ts = 3 [json_name = "ts"];
  message Transaction {
    // Transaction Timestamp
    string ts = 1 [json_name = "ts"];
    // Unique identifier of the transaction, (Unique & Case Sensitive)
    string id = 2 [json_name = "id"];
    // Original Transaction ID, if this transaction is in relation to another
    string org_txn_id = 3 [json_name = "org_txn_id"];
    // Transaction note by customer
    string note = 4 [json_name = "note"];
    // Type of the transaction e.g. PAY|COLLECT| FAILED
    string type = 5 [json_name = "type"];
    // Classifier indicating organization code or payment product or payment channel e.g. UPI|API
    // Class can be further broken down into subclasses.
    string class = 6 [json_name = "class"];
    // Additional attributes
    ScoreAttribs attribs = 7 [json_name = "attribs"];
  }
  Transaction txn = 4 [json_name = "txn"];

  User payer = 5 [json_name = "payer"];
  User payee = 6 [json_name = "payee"];
}

// API will be used for sending back the score of transactions initiated through Score Request API.
message ScoreResponseSync {
  // some fields will echo-back request's fields
  // to keep link between request and response

  // Score Request ID (Echo back, Case Sensitive)
  string reqid = 1 [json_name = "reqid"];
  // Organization (Epifi) (Echo back)
  string org = 2 [json_name = "org"];
  // Response Timestamp
  string ts = 3 [json_name = "ts"];
  message Transaction {
    // Transaction ID (Echo back, Case Sensitive)
    string id = 1 [json_name = "id"];
  }
  Transaction transaction = 4 [json_name = "txn"];
  // Success/Failed (Green/Red)
  string status = 5 [json_name = "status"];
  // Remark in case of rule breach / failed transaction
  string msg = 6 [json_name = "msg"];
  // Fetches risk score of a transaction, can have multiple scores on which this particular
  // transaction is evaluated on, with their rule name and remarks.
  message Score {
    message DecisionDetail {
      // Score if any, in case of rule breach or failure
      float score = 1 [json_name = "score"];
      // Remarks if any, in case of rule breach or failure
      string remarks = 2 [json_name = "remarks", deprecated = true];
      // Name of the rule
      string rule_name = 3 [json_name = "rulename"];
      // Index of the rule
      float rule_number = 4 [json_name = "ruleno"];
      // Rule version - it changes every time there is a change in the logic/threshold tuning/sequencing
      float rule_version = 5 [json_name = "ruleversion"];
      // Rule id - rule id is unique to every rule in dronapay
      float rule_id = 6 [json_name = "ruleid"];
      // Suspected entity for this score
      string side = 7 [json_name = "side"];
    }
    // Array of Rule name, score obtained (in case of rule breach), remark for the rule if any
    repeated DecisionDetail decisiondetails = 1 [json_name = "decisiondetails"];
    // Risk score value between 0 and 100. Max of the scores obtained in all rules
    float score = 2 [json_name = "score"];
  }
  Score score = 7 [json_name = "score"];
}

