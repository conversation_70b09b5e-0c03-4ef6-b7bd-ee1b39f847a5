syntax = "proto3";

package vendors.dronapay;

option go_package = "github.com/epifi/gamma/api/vendors/dronapay";
option java_package = "com.github.epifi.gamma.api.vendors.dronapay";

// Dronapay Account APIs to create the accounts related to the Customer
message AccountRequest {
  // External identifier for account at vendor side
  // Specifications - Min length = 3, Max length = 100, Unique, Cannot be NULL, Cannot be Updated
  string external_id = 1 [json_name = "externalId"];
  // Type of Account : Savings, Current, Credit, Loan & PrePaid
  int64 account_type = 2 [json_name = "accountType"];
  // Bank account number e.g. *********** Specification - Max length = 255
  string account_number = 3 [json_name = "accountNumber"];
  // Bank’s IFSC e.g. SBI00000320 Specification - Max length = 11
  string ifsc = 4 [json_name = "ifsc"];
  // Date of onboarding of the customer e.g. 2020-01-13
  // Specifications - ISO-8601 Pattern = yyyy-mm-dd, Timezone = IST
  string onboarding_date = 5 [json_name = "onboarding_date"];
  // Customer is a merchant or not e.g. TRUE|FALSE
  bool merchant = 6 [json_name = "merchant"];
  // Whether customer is verified or not e.g. TRUE|FALSE
  bool verified = 7 [json_name = "verified"];
  message Attributes {
    // Custom attributes to identify whether the customer is below the HNI (high net worth individual) segment.
    string HNI = 1 [json_name = "HNI"];
  }
  Attributes attribs = 8 [json_name = "attribs"];

  // Category of account e.g. Classic|Salary|UMC|Yrs10+
  repeated string account_categories = 9 [json_name = "account_categories"];
  // Value set to 0 for create and update. Value set to 1 for delete.
  int64 record_status = 10 [json_name = "record_Status"];
}

message AccountResponse {
  // Status Code for request.
  int64 statusCode = 1 [json_name = "statusCode"];
}
