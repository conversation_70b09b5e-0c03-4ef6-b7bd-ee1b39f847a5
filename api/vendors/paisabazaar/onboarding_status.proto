syntax = "proto3";

package vendors.paisabazaar;

option go_package = "github.com/epifi/gamma/api/vendors/paisabazaar/creditcard";
option java_package = "com.github.epifi.gamma.api.vendors.paisabazaar.creditcard";

message GetCreditCardOnboardingStatusRequest {
  string unique_user_id = 1 [json_name = "unique_user_id"];
  bool fetch_history = 2 [json_name = "fetch_history"];
}

message GetCreditCardOnboardingStatusResponse {
  string overall_onboarding_status = 1 [json_name = "overall_onboarding_status"];
  string current_onboarding_stage = 2 [json_name = "current_onboarding_stage"];
  string current_onboarding_stage_status = 3 [json_name = "current_onboarding_stage_status"];
  string troubleshooting_advice = 4 [json_name = "troubleshooting_advice"];
  repeated OnboardingStageDetails onboarding_stage_history = 5 [json_name = "onboarding_stage_history"];
  Exception exception = 6 [json_name = "exception"];
}

message Exception {
  string status_code = 1 [json_name = "status_code"];
  string error_message = 2 [json_name = "error_message"];
}

message OnboardingStageDetails {
  string stage_name = 1 [json_name = "stage"];
  string stage_status = 2 [json_name = "status"];
  string created_at = 3 [json_name = "created_at"];
  string updated_at = 4 [json_name = "updated_at"];
}
