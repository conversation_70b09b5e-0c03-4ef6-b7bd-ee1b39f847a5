syntax = "proto3";

package vendors.mfcentral.collateral;

import "api/vendors/mfcentral/collateral/error.proto";

option go_package = "github.com/epifi/gamma/api/vendors/mfcentral/collateral";

message InvestorConsentRequest {
  string client_ref_no = 1[json_name = 'clientRefNo'];
  string request_id = 2[json_name = 'reqId'];
  string user_subject_reference = 3 [json_name = 'userSubjectReference'];
  string otp_reference = 4 [json_name = 'otpRef'];
  string entered_otp = 5[json_name = 'enteredOtp'];
}

message InvestorConsentResponse {
  repeated Error errors = 1 [json_name = 'errors'];
  // optional request id populated only in error case
  // Eg. {"reqId":"*********","errors":[{"code":"406","message":"Entered OTP appears to be incorrect. Please try again."}]}
  int64 request_id = 2 [json_name = 'reqId'];
  string response = 3 [json_name = 'response'];
}
