syntax = "proto3";

package vendors.mfcentral.collateral;

import "api/vendors/mfcentral/collateral/error.proto";
option go_package = "github.com/epifi/gamma/api/vendors/mfcentral/collateral";

message InvokeRevokeLienRequest {
  string req_id = 1 [json_name = "reqId"];
  string pan = 2 [json_name = "pan"];
  string pekrn = 3 [json_name = "pekrn"];
  string mobile = 4 [json_name = "mobile"];
  string email = 5 [json_name = "email"];
  string client_id = 6 [json_name = "clientId"];
  string client_ref_no = 7 [json_name = "clientRefNo"];
  string lender_code = 8 [json_name = "lenderCode"];
  repeated IRData data = 9 [json_name = "data"];
}

message InvokeRevokeLienResponse {
  int64 req_id = 1 [json_name = "reqId"];
  string pan = 2 [json_name = "pan"];
  string pekrn = 3 [json_name = "pekrn"];
  string mobile = 4 [json_name = "mobile"];
  string email = 5 [json_name = "email"];
  string client_id = 6 [json_name = "clientId"];
  string lender_code = 7 [json_name = "lenderCode"];
  repeated IRData pending = 9 [json_name = "pending"];
  repeated IRData success = 10 [json_name = "success"];
  repeated IRData errors = 11 [json_name = "errors"];
  string validate_id = 12 [json_name = "validateId"];
}

message InvokeRevokeLienResponseErr {
  repeated Error errors = 1 [json_name = "errors"];
  string req_id = 2 [json_name = "reqId"];
  string response = 3 [json_name = "response"];
}

message IRData {
  string rta_name = 1 [json_name = "rtaName"];
  string lien_inv_rev_ref_no = 2 [json_name = "lienInvRevRefNo"];
  string invoke_revoke_type = 3 [json_name = "invokeRevokeType"];
  string invoke_revoke_token = 4 [json_name = "invokeRevokeToken"];
  repeated IRSchemeDetails scheme_details = 5 [json_name = "schemeDetails"];
}

message IRSchemeDetails {
  string amc = 1 [json_name = "amc"];
  string amc_name = 2 [json_name = "amc_name"];
  string folio = 3 [json_name = "folio"];
  string isin = 4 [json_name = "isin"];
  string item_no = 5 [json_name = "itemNo"];
  string scheme_code = 6 [json_name = "schemeCode"];
  string scheme_name = 7 [json_name = "schemeName"];
  string scheme_type = 8 [json_name = "schemeType"];
  string lien_units = 9 [json_name = "lienUnits"];
  string lien_sub_ref_no = 10 [json_name = "lienSubRefNo"];
}
