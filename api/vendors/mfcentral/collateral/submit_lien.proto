syntax = "proto3";

package vendors.mfcentral.collateral;

import "api/vendors/mfcentral/collateral/error.proto";
import"api/vendors/mfcentral/collateral/validate_lien.proto";

option go_package = "github.com/epifi/gamma/api/vendors/mfcentral/collateral";

message SubmitLienRequest {
  string req_id = 1 [json_name = "reqId"];
  string pan = 2 [json_name = "pan"];
  string pekrn = 3 [json_name = "pekrn"];
  string mobile = 4 [json_name = "mobile"];
  string email = 5 [json_name = "email"];
  string client_id = 6 [json_name = "clientId"];
  string client_ref_no = 7 [json_name = "clientRefNo"];
  string lender_code = 8 [json_name = "lenderCode"];
  LienData data = 9 [json_name = "data"];
}

message SubmitLienResponse {
  int64 req_id = 1 [json_name = "reqId"];
  string otp_ref = 2 [json_name = "otpRef"];
  string user_subject_reference = 3[json_name = "userSubjectReference"];
  string client_ref_no = 4[json_name = "clientRefNo"];
  repeated Error errors = 5 [json_name = "errors"];
}

message SubmitLienResponseErr {
  repeated Error errors = 1 [json_name = "errors"];
  string req_id = 2 [json_name = "reqId"];
  string response = 3[json_name = "response"];
}
