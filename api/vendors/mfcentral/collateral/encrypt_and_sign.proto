syntax = "proto3";

package vendors.mfcentral.collateral;

import "api/vendors/mfcentral/collateral/error_info.proto";

option go_package = "github.com/epifi/gamma/api/vendors/mfcentral/collateral";

// The proto in this file is specific to Nsdl APIs.
message EncryptAndSignRequest {
  string payload = 1[json_name = "data"];
  string signature = 2[json_name = "signature"];
}

message EncryptAndSignResponse {
  string encrypted_payload = 1[json_name = "data"];
  string signature = 2[json_name = "signature"];
  ErrorInfo error_info = 3[json_name = "errorInfo"];
}
