syntax = "proto3";

package vendors.mfcentral;

option go_package = "github.com/epifi/gamma/api/vendors/mfcentral";

import "api/vendors/mfcentral/verify_and_decrypt.proto";


message EncryptAndSignRequest {
  string payload = 1[json_name = 'data'];
}

message EncryptAndSignResponse {
  string encrypted_payload = 1[json_name = "data"];
  string digital_signature = 2[json_name = "signature"];
  ErrorInfo error_info = 3[json_name = "errorInfo"];
}
