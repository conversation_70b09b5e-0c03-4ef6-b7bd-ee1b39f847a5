syntax = "proto3";

package vendors.smallcase;

import 'api/vendors/mfcentral/mf_central_payload.proto';
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendors/smallcase";

// Proto to marshall smallcase mutual fund analytics api request
// Api Doc : https://docs.google.com/document/d/1rcLWRNMR7TPCsTir5EApNkIDrQCCTlwZwspuwJeruDI/edit#bookmark=id.5e1s8k9pctnh
// Smallcase Gateway Doc : https://developers.gateway.smallcase.com/docs/mf-analytics-api
message GetMfAnalyticsRequest {
  string gateway = 1 [json_name = "gateway"];
  string request_id = 2 [json_name = "requestId"];
  MfRawData data = 3 [json_name = "data"];
}

// MfRawData required by smallcase mf analytics api
message MfRawData {
  string format = 1  [json_name = "format"];
  MFCentralFormat payload = 2 [json_name = "payload"];
}

// MFCentralFormat contains mf transactions and summary in mf central format
message MFCentralFormat {
  repeated mfcentral.DtTransaction dt_transaction = 1 [json_name = "dtTransaction"];
  repeated mfcentral.DtSummary dt_summary = 2  [json_name = "dtSummary"];
}

// Proto to unmarshall smallcase mutual fund analytics api response
// Api Doc : https://docs.google.com/document/d/1rcLWRNMR7TPCsTir5EApNkIDrQCCTlwZwspuwJeruDI/edit#bookmark=id.hmedsfk6ytxc
message GetMfAnalyticsResponse {
  bool success = 1 [json_name = "success"];
  google.protobuf.Value errors = 2 [json_name = "errors"];
  string error_type = 4 [json_name = "errorType"];
  MFAnalyticsResponseData data = 5 [json_name = "data"];
}

// MFAnalyticsResponseData enriched analytics response from smallcase
message MFAnalyticsResponseData {
  MFAnalyticsSummary summary = 1 [json_name = "summary"];
  repeated MFTransaction transactions = 2 [json_name = "transactions"];
}

// MFAnalyticsSummary contains scheme level and portfolio level analytics
message MFAnalyticsSummary {
  repeated MFExitedDetails exited = 1 [json_name = "exited"];
  repeated SchemeDetails scheme_details = 2 [json_name = "schemeDetails"];
  MFPortfolioAnalytics portfolio = 3 [json_name = "portfolio"];
}

// MFExitedDetails analytics about the folios that had been exited
message MFExitedDetails {
  string isin = 1 [json_name = "isin"];
  string folio_number = 2 [json_name = "folioNumber"];
  string scheme = 3 [json_name = "scheme"];
  string scheme_code = 4 [json_name = "schemeCode"];
  string amc = 5 [json_name = "amc"];
  string amc_code = 6 [json_name = "amcCode"];
  string asset_type = 7 [json_name = "assetType"];
  string broker_name = 8 [json_name = "brokerName"];
  string broker_code = 9 [json_name = "brokerCode"];
  string entry_load = 10 [json_name = "entryLoad"];
  string exit_load = 11 [json_name = "exitLoad"];
  string is_demat = 12 [json_name = "isDemat"];
  double current_value = 13 [json_name = "currentValue"];
  double units = 14 [json_name = "units"];
  double nav = 15 [json_name = "nav"];
  string nav_updated_date = 16 [json_name = "navUpdatedDate"];
  double avg_buy_nav = 17 [json_name = "avgBuyNav"];
  double returns = 18 [json_name = "returns"];
  double returns_percent = 19 [json_name = "returnsPercent"];
  google.protobuf.Value xirr = 20 [json_name = "xirr"];
  string sip_frequency = 21 [json_name = "sipFrequency"];
  string next_sip_date = 22 [json_name = "nextSipDate"];
  string rta_code = 23 [json_name = "rtaCode"];
  string kyc_status = 24 [json_name = "kycStatus"];
  string email = 25 [json_name = "email"];
  double decimal_units = 26 [json_name = "decimalUnits"];
  double decimal_amount = 27 [json_name = "decimalAmount"];
  double decimal_nav = 28 [json_name = "decimalNav"];
  string last_trxn_date = 29 [json_name = "lastTrxnDate"];
  double opening_bal = 30 [json_name = "openingBal"];
  double closing_balance = 31 [json_name = "closingBalance"];
  string last_nav_date = 32 [json_name = "lastNavDate"];
}

message SchemeDetails {
  string isin = 1 [json_name = "isin"];
  repeated MFFolioDetails folios = 2 [json_name = "folios"];
  string scheme = 3 [json_name = "scheme"];
  string scheme_code = 4 [json_name = "schemeCode"];
  string amc = 5 [json_name = "amc"];
  string amc_code = 6 [json_name = "amcCode"];
  string asset_type = 7 [json_name = "assetType"];
  string entry_load = 8 [json_name = "entryLoad"];
  string exit_load = 9 [json_name = "exitLoad"];
  double units = 10 [json_name = "units"];
  double nav = 11 [json_name = "nav"];
  double current_value = 12 [json_name = "currentValue"];
  string fund_type = 13 [json_name = "fundType"];
  string exit_load_remarks = 14 [json_name = "exitLoadRemarks"];
  MFSchemeAnalytics analytics = 15 [json_name = "analytics"];
}

message MFSchemeAnalytics {
  double invested_value = 1 [json_name = "investedValue"];
  double average_price = 2 [json_name = "averagePrice"];
  string timestamp = 3 [json_name = "timestamp"];
  double realised_returns = 4 [json_name = "realisedReturns"];
  double total_returns = 5 [json_name = "totalReturns"];
  double current_returns = 6 [json_name = "currentReturns"];
  double current_returns_percentage = 7 [json_name = "currentReturnsPercentage"];
  repeated MFSectorAllocationPercentage sector_allocation_percentage = 8 [json_name = "sectorAllocationPercentage"];
  double scheme_weight = 9 [json_name = "schemeWeight"];
  google.protobuf.Value xirr = 10 [json_name = "xirr"];
  string remarks = 11 [json_name = "remarks"];
}

message MFSectorAllocationPercentage {
  string sector_name = 1 [json_name = "sectorName"];
  double value = 2 [json_name = "value"];
}

message MFFolioDetails {
  string folio_number = 1 [json_name = "folioNumber"];
  string broker_name = 2 [json_name = "brokerName"];
  string broker_code = 3 [json_name = "brokerCode"];
  string is_demat = 4 [json_name = "isDemat"];
  double units = 5 [json_name = "units"];
  double invested_value = 6 [json_name = "investedValue"];
  double current_value = 7 [json_name = "currentValue"];
  double current_returns = 8 [json_name = "currentReturns"];
  double total_returns = 9 [json_name = "totalReturns"];
  double realised_returns = 10 [json_name = "realisedReturns"];
  double average_price = 11 [json_name = "averagePrice"];
  double current_returns_percentage = 12 [json_name = "currentReturnsPercentage"];
  google.protobuf.Value xirr = 13 [json_name = "xirr"];
  string rta_code = 14 [json_name = "rtaCode"];
  string kyc_status = 15 [json_name = "kycStatus"];
  string email = 16 [json_name = "email"];
  double decimal_units = 17 [json_name = "decimalUnits"];
  double decimal_amount = 18 [json_name = "decimalAmount"];
  double decimal_nav = 19 [json_name = "decimalNav"];
  string last_trxn_date = 20 [json_name = "lastTrxnDate"];
  double opening_bal = 21 [json_name = "openingBal"];
  double closing_balance = 22 [json_name = "closingBalance"];
  string last_nav_date = 23 [json_name = "lastNavDate"];
  string remarks = 24 [json_name = "remarks"];
}

message MFPortfolioAnalytics {
  google.protobuf.Value xirr = 1 [json_name = "xirr"];
  int64 scheme_count = 2 [json_name = "schemeCount"];
  double realised_returns = 3 [json_name = "realisedReturns"];
  double total_returns = 4 [json_name = "totalReturns"];
  double current_returns = 5 [json_name = "currentReturns"];
  repeated MFSectorAllocationPercentage sector_allocation_percentage = 6 [json_name = "sectorAllocationPercentage"];
}

message MFTransaction {
  string isin = 1 [json_name = "isin"];
  string folio_number = 2 [json_name = "folioNumber"];
  string amc = 3 [json_name = "amc"];
  string amc_code = 4 [json_name = "amcCode"];
  string scheme_code = 5 [json_name = "schemeCode"];
  string scheme = 6 [json_name = "scheme"];
  string transaction_date = 7 [json_name = "transactionDate"];
  string posted_date = 8 [json_name = "postedDate"];
  string trxn_description = 9 [json_name = "trxnDescription"];
  double trxn_amount = 10 [json_name = "trxnAmount"];
  double trxn_units = 11 [json_name = "trxnUnits"];
  double purchase_price = 12 [json_name = "purchasePrice"];
  double stamp_duty = 13 [json_name = "stampDuty"];
  double total_tax = 14 [json_name = "totalTax"];
  string transaction_mode = 15 [json_name = "transactionMode"];
  string cash_flow = 16 [json_name = "cashFlow"];
  MFTransactionAnalytics analytics = 17 [json_name = "analytics"];
  double check_digit = 18 [json_name = "checkDigit"];
  string email = 19 [json_name = "email"];
  double trxn_charge = 20 [json_name = "trxnCharge"];
  double stt_tax = 21 [json_name = "sttTax"];
  double tax = 22 [json_name = "tax"];
}

message MFTransactionAnalytics {
  bool exited = 1 [json_name = "exited"];
  string transaction_type = 2 [json_name = "transactionType"];
  string transaction_status = 3 [json_name = "transactionStatus"];
  string transaction_description = 4 [json_name = "transactionDescription"];
}
