syntax = "proto3";

package vendors.smallcase;

option go_package = "github.com/epifi/gamma/api/vendors/smallcase";

message GetOTPRequest {
  string step = 1 [json_name = "step"];
  string transaction_id = 2 [json_name = "transactionId"];
  GetOTPPayLoad pay_load = 3 [json_name = "payload"];
  string gateway = 4 [json_name = "gateway"];
  string access_token = 5 [json_name = "accessToken"];
}

message GetOTPPayLoad {
  string request = 1 [json_name = "request"];
  string signature = 2 [json_name = "signature"];
}

message GetOTPResponse {
  bool success = 1 [json_name = "success"];
  string errors = 2 [json_name = "errors"];
  GetOTPPayLoadData data = 3 [json_name = "data"];
  string error_type = 4 [json_name = "errorType"];
}

message GetOTPPayLoadData {
  string response = 1 [json_name = "response"];
  string signature = 2 [json_name = "signature"];
}
