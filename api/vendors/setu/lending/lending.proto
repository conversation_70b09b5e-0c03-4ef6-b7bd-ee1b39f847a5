syntax = "proto3";

package vendors.setu.lending;

option go_package = "github.com/epifi/gamma/api/vendors/setu/lending";
option java_package = "com.github.epifi.gamma.api.vendors.setu.lending";

message Error {
  string message = 1 [json_name = "message"];
  string consentId = 2 [json_name = "consentId"];
}

message CreateConsentRequest {
  AACreateConsentRequest aa_create_consent_request = 1 [json_name = "aaCreateConsentRequest"];
  BreRequest bre_request = 2 [json_name = "breRequest"];
  EmployerDetails employer_details = 3 [json_name = "employerDetails"];

  message EmployerDetails {
    string employer_name = 1 [json_name = "employerName"];
  }

  message AACreateConsentRequest {
    ConsentDuration consent_duration = 1 [json_name = "consentDuration"];
    // unique identifier for the user at setu's end
    string vua = 2 [json_name = "vua"];
    DataRange data_range = 3 [json_name = "dataRange"];
    repeated string context = 4 [json_name = "context"];

    message ConsentDuration {
      string unit = 1 [json_name = "unit"];
      string value = 2 [json_name = "value"];
    }

    message DataRange {
      string from = 1 [json_name = "from"];
      string to = 2 [json_name = "to"];
    }
  }

  message BreRequest {
    LoanApplication loan_application = 1 [json_name = "loanApplication"];

    message LoanApplication {
      string application_type = 1 [json_name = "applicationType"];
      repeated string loan_type = 2 [json_name = "loanType"];
      int32 loan_amount = 3 [json_name = "loanAmount"];
      Borrower borrower = 4 [json_name = "borrower"];

      message Borrower {
        repeated CustomerIdentifier customer_identifiers = 1 [json_name = "customerIdentifiers"];
        string name = 2 [json_name = "name"];
        string source_ipv4 = 3 [json_name = "sourceIPv4"];
        ContactDetails contact_details = 4 [json_name = "contactDetails"];
        string dob = 5 [json_name = "dob"];
        string gender = 6 [json_name = "gender"];
        string applicant_type = 7 [json_name = "applicantType"];
        string customer_id = 8 [json_name = "customerId"];

        message ContactDetails {
          repeated Address addresses = 4 [json_name = "addresses"];
          repeated Email email_list = 5 [json_name = "emailList"];
          repeated Phone phone_list = 6 [json_name = "phoneList"];
        }

        message CustomerIdentifier {
          string id_name = 1 [json_name = "idName"];
          string id_value = 2 [json_name = "idValue"];
        }

        message Address {
          string co = 1 [json_name = "co"];
          string hba = 2 [json_name = "hba"];
          string srl = 3 [json_name = "srl"];
          string landmark = 4 [json_name = "landmark"];
          string als = 5 [json_name = "als"];
          string vtc = 6 [json_name = "vtc"];
          string pincode = 7 [json_name = "pincode"];
          string po = 8 [json_name = "po"];
          string district = 9 [json_name = "district"];
          string state = 10 [json_name = "state"];
          string country = 11 [json_name = "country"];
          string uri = 12 [json_name = "uri"];
          string latitude = 13 [json_name = "latitude"];
          string longitude = 14 [json_name = "longitude"];
          string type = 15 [json_name = "type"];
        }

        message Email {
          string email_type = 1 [json_name = "emailType"];
          string email_id = 2 [json_name = "emailId"];
        }

        message Phone {
          string country_code = 1 [json_name = "countryCode"];
          string phone_type = 2 [json_name = "phoneType"];
          string phone_number = 3 [json_name = "phoneNumber"];
        }
      }
    }
  }
}

message CreateConsentResponse {
  string timestamp = 1 [json_name = "timestamp"];
  string core_trace_id = 2 [json_name = "coreTraceId"];
  bool success = 3 [json_name = "success"];
  Data data = 4 [json_name = "data"];
  Error error = 5 [json_name = "error"];

  message Data {
    string id = 1 [json_name = "id"];
    string url = 2 [json_name = "url"];
    string status = 3 [json_name = "status"];
    Detail detail = 4 [json_name = "detail"];
    string redirect_url = 5 [json_name = "redirectUrl"];
    repeated string context = 6 [json_name = "context"];
    string PAN = 7 [json_name = "PAN"];
    Usage usage = 8 [json_name = "usage"];
    repeated string accounts_linked = 9 [json_name = "accountsLinked"];
    string trace_id = 10 [json_name = "traceId"];
    string message = 11 [json_name = "message"];
    string consent_id = 12 [json_name = "consentId"];

    message Detail {
      Frequency frequency = 1 [json_name = "frequency"];
      string fetch_type = 2 [json_name = "fetchType"];
      DataRange data_range = 3 [json_name = "dataRange"];
      string consent_start = 4 [json_name = "consentStart"];
      DataLife data_life = 5 [json_name = "dataLife"];
      string consent_mode = 6 [json_name = "consentMode"];
      string vua = 7 [json_name = "vua"];
      repeated string consent_types = 8 [json_name = "consentTypes"];
      Purpose purpose = 9 [json_name = "purpose"];
      string consent_expiry = 10 [json_name = "consentExpiry"];
      repeated string fi_types = 11 [json_name = "fiTypes"];

      message Frequency {
        int32 value = 1 [json_name = "value"];
        string unit = 2 [json_name = "unit"];
      }

      message DataRange {
        string to = 1 [json_name = "to"];
        string from = 2 [json_name = "from"];
      }

      message DataLife {
        int32 value = 1 [json_name = "value"];
        string unit = 2 [json_name = "unit"];
      }

      message Purpose {
        Category category = 1 [json_name = "category"];
        string ref_uri = 2 [json_name = "refUri"];
        string code = 3 [json_name = "code"];
        string text = 4 [json_name = "text"];

        message Category {
          string type = 1 [json_name = "type"];
        }
      }
    }

    message Usage {
      string last_used = 1 [json_name = "lastUsed"];
      string count = 2 [json_name = "count"];
    }
  }
}

message GetJourneyStatusResponse {
  string timestamp = 1 [json_name = "timestamp"];
  string core_trace_id = 2 [json_name = "coreTraceId"];
  bool success = 3 [json_name = "success"];
  Data data = 4 [json_name = "data"];
  Error error = 5 [json_name = "error"];
}

message JourneyStatusCallbackRequest {
  string consent_id = 1 [json_name = "consentId"];
  string type = 2 [json_name = "type"];
  Data data = 3 [json_name = "data"];
}

message Data {
  string status = 1 [json_name = "status"];
  string approved_amount = 2 [json_name = "approvedAmount"];
  string roi = 3 [json_name = "roi"];
  string emi = 4 [json_name = "emi"];
  string max_eligible_emi = 5 [json_name = "maxEligibleEmi"];
  string processing_fee = 6 [json_name = "processingFee"];
  string reason = 7 [json_name = "reason"];
}
