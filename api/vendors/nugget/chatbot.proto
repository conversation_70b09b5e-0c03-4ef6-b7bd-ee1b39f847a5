syntax = "proto3";

package vendors.nugget;

option go_package = "github.com/epifi/gamma/api/vendors/nugget";

// FetchChatbotAccessTokenRequest represents the request payload for Nugget's fetch access token API
message FetchChatbotAccessTokenRequest {
  string uid = 1 [json_name = "uid"];
  int32 client_id = 2 [json_name = "clientId"];
  string platform = 3 [json_name = "platform"];
  string display_name = 4 [json_name = "displayName"];
  string email = 5 [json_name = "email"];
  string phone_number = 6 [json_name = "phoneNumber"];
  string photo_url = 7 [json_name = "photoUrl"];
  int32 business_id = 8 [json_name = 'businessId'];
}

// FetchChatbotAccessTokenResponse represents the response from Nugget's fetch access token API
message FetchChatbotAccessTokenResponse {
  bool success = 1 [json_name = "success"];
  AccessToken access_token = 2 [json_name = "accessToken"];
}

message AccessToken {
  Payload payload = 1 [json_name = "payload"];
  string token = 2 [json_name = "token"];
}

message Payload {
  string app_version = 1 [json_name = "appVersion"];
  int32 business_id = 2 [json_name = "businessId"];
  int32 client_id = 3 [json_name = "clientId"];
  string client_name = 4 [json_name = "client_name"];
  string display_name = 5 [json_name = "displayName"];
  string email = 6 [json_name = "email"];
  int64 exp = 7 [json_name = "exp"];
  string host_name = 8 [json_name = "hostName"];
  int64 iat = 9 [json_name = "iat"];
  string phone_number = 10 [json_name = "phoneNumber"];
  string photo_url = 11 [json_name = "photoURL"];
  string source = 12 [json_name = "source"];
  int32 tenant_id = 13 [json_name = "tenantID"];
  string uid = 14 [json_name = "uid"];
}
