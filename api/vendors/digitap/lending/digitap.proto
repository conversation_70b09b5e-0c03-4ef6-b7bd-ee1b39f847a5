syntax = "proto3";

package vendors.digitap.lending;

option go_package = "github.com/epifi/gamma/api/vendors/digitap/lending";
option java_package = "com.github.epifi.gamma.api.vendors.digitap.lending";

message UanAdvancedRequest {
  string client_ref_num = 1 [json_name = "client_ref_num"];
  string mobile = 3 [json_name = "mobile"];
  string uan = 4 [json_name = "uan"];
  string dob = 5 [json_name = "dob"];
  string employee_name = 6 [json_name = "employee_name"];
  string employer_name = 7 [json_name = "employer_name"];
  string pan = 8 [json_name = "pan"];
  string name_match_method = 9 [json_name = "name_match_method"];
}

message Epfo {
  bool is_recent = 1 [json_name = "is_recent"];
  bool is_name_unique = 2 [json_name = "is_name_unique"];
  bool has_pf_filings_details = 3 [json_name = "has_pf_filings_details"];
}

message RecentEmployerData {
  string member_id = 1 [json_name = "member_id"];
  string establishment_id = 2 [json_name = "establishment_id"];
  string date_of_exit = 3 [json_name = "date_of_exit"];
  string date_of_joining = 4 [json_name = "date_of_joining"];
  string establishment_name = 5 [json_name = "establishment_name"];
  double employer_confidence_score = 6 [json_name = "employer_confidence_score"];
  string matching_uan = 7 [json_name = "matching_uan"];
  Epfo epfo = 8 [json_name = "epfo"];
}

message Summary {
  RecentEmployerData recent_employer_data = 1 [json_name = "recent_employer_data"];
  string matching_uan = 2 [json_name = "matching_uan"];
  bool is_employed = 3 [json_name = "is_employed"];
  bool employee_name_match = 4 [json_name = "employee_name_match"];
  bool employer_name_match = 5 [json_name = "employer_name_match"];
  int32 uan_count = 6 [json_name = "uan_count"];
  bool date_of_exit_marked = 7 [json_name = "date_of_exit_marked"];
}

message BasicDetails {
  string gender = 1 [json_name = "gender"];
  string date_of_birth = 2 [json_name = "date_of_birth"];
  double employee_confidence_score = 3 [json_name = "employee_confidence_score"];
  string name = 4 [json_name = "name"];
  string mobile = 5 [json_name = "mobile"];
  int32 aadhaar_verification_status = 6 [json_name = "aadhaar_verification_status"];
}

message EmploymentDetails {
  string member_id = 1 [json_name = "member_id"];
  string establishment_id = 2 [json_name = "establishment_id"];
  string date_of_exit = 3 [json_name = "date_of_exit"];
  string date_of_joining = 4 [json_name = "date_of_joining"];
  string leave_reason = 5 [json_name = "leave_reason"];
  string establishment_name = 6 [json_name = "establishment_name"];
  double employer_confidence_score = 7 [json_name = "employer_confidence_score"];
}

message AdditionalDetails {
  string aadhaar = 1 [json_name = "aadhaar"];
  string member_id = 2 [json_name = "member_id"];
  string email = 3 [json_name = "email"];
  string pan = 4 [json_name = "pan"];
  string bank_ifsc = 5 [json_name = "bank_ifsc"];
  string bank_acc_no = 6 [json_name = "bank_acc_no"];
  string bank_address = 7 [json_name = "bank_address"];
  string relation = 8 [json_name = "relation"];
  string relative_name = 9 [json_name = "relative_name"];
}

message UanDetails {
  BasicDetails basic_details = 1 [json_name = "basic_details"];
  EmploymentDetails employment_details = 2 [json_name = "employment_details"];
  AdditionalDetails additional_details = 3 [json_name = "additional_details"];
}

message UanSource {
  string uan = 1 [json_name = "uan"];
  string source = 2 [json_name = "source"];
}

message EpfHistory {
  map<string, string> history = 1 [json_name = "history"];
}

message Matches {
  string name = 1 [json_name = "name"];
  double confidence = 2 [json_name = "confidence"];
  EpfHistory epf_history = 3 [json_name = "epf_history"];
}

message PfFilingDetails {
  double total_amount = 1 [json_name = "total_amount"];
  int32 employees_count = 2 [json_name = "employees_count"];
  string wage_month = 3 [json_name = "wage_month"];
}

message EstablishmentInfo {
  string establishment_id = 1 [json_name = "establishment_id"];
  string establishment_name = 2 [json_name = "establishment_name"];
  string date_of_setup = 3 [json_name = "date_of_setup"];
  string ownership_type = 4 [json_name = "ownership_type"];
}

message EpfoDetails {
  repeated Matches matches = 1 [json_name = "matches"];
  repeated PfFilingDetails pf_filing_details = 2 [json_name = "pf_filing_details"];
  EstablishmentInfo establishment_info = 3 [json_name = "establishment_info"];
}

message Result {
  repeated string uan = 1 [json_name = "uan"];
  Summary summary = 2 [json_name = "summary"];
  map<string, UanDetails> uan_details = 3 [json_name = "uan_details"];
  repeated UanSource uan_source = 4 [json_name = "uan_source"];
  int32 name_dob_filtering_score = 5 [json_name = "name_dob_filtering_score"];
  EpfoDetails epfo_details = 6 [json_name = "epfo_details"];
}

message InputData {
  string mobile = 1 [json_name = "mobile"];
  string pan = 2 [json_name = "pan"];
  string employee_name = 3 [json_name = "employee_name"];
  string employer_name = 4 [json_name = "employer_name"];
}

message UanAdvancedResponse {
  uint32 http_response_code = 1 [json_name = "http_response_code"];
  string client_ref_num = 2 [json_name = "client_ref_num"];
  string request_id = 3 [json_name = "request_id"];
  uint32 result_code = 4 [json_name = "result_code"];
  string mobile = 5 [json_name = "mobile"];
  Result result = 6 [json_name = "result"];
  InputData input_data = 7 [json_name = "input_data"];
}
