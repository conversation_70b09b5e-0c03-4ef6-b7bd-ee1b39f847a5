syntax = "proto3";

package vendors.fiftyfin;

option go_package = "github.com/epifi/gamma/api/vendors/fiftyfin";
option java_package = "com.github.epifi.gamma.api.vendors.fiftyfin";

message FetchUserResponse {
  uint32 code = 1 [json_name = 'code'];
  string detail = 2 [json_name = 'detail'];
  Data data = 3 [json_name = 'data'];

  message Data {
    int32 id = 1 [json_name = 'id'];
    string email = 2 [json_name = 'email'];
    string phone_number = 3 [json_name = 'phone_number'];
    string user_name = 4 [json_name = 'user_name'];
    string profile_picture_url = 5 [json_name = 'profile_picture_url'];
    int32 db_id = 6 [json_name = 'db_id'];
    bool is_verified = 7 [json_name = 'is_verified'];
    bool is_whatsapp_opt_in = 8 [json_name = 'is_whatsapp_opt_in'];
    string updated_at = 9 [json_name = 'updated_at'];
    string country_code = 10 [json_name = 'country_code '];
    string first_name = 11 [json_name = 'first_name '];
    string last_name = 12 [json_name = 'last_name '];
    string firebase_id = 13 [json_name = 'firebase_id '];
    string date_of_birth = 14 [json_name = 'date_of_birth '];
    string referral_code = 15 [json_name = 'referral_code '];
    bool is_delete = 16 [json_name = 'is_delete '];
    string created_at = 17 [json_name = 'created_at '];
    bool is_pan_linked = 18 [json_name = 'is_pan_linked '];
    bool is_bank_linked = 19 [json_name = 'is_bank_linked '];
    bool is_pan_image_uploaded = 20 [json_name = 'is_pan_image_uploaded '];
    bool is_aadhaar_image_uploaded = 21 [json_name = 'is_aadhaar_image_uploaded '];
    bool is_ckyc_completed = 22 [json_name = 'is_ckyc_completed '];
    double interest_rate = 23 [json_name = 'interest_rate '];
  }
}
