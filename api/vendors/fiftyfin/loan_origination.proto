syntax = "proto3";

package vendors.fiftyfin;

import "api/vendors/fiftyfin/common.proto";

option go_package = "github.com/epifi/gamma/api/vendors/fiftyfin";
option java_package = "com.github.epifi.gamma.api.vendors.fiftyfin";

message InitiateLoanRequest {
  int32 user_id = 1 [json_name = "user_id"];
  uint32 loan_term = 2 [json_name = "loan_term"];
  double disbursement_amount = 3 [json_name = "disbursement_amount"];
  double interest_rate = 4 [json_name = "interest_rate"];
  uint32 processing_fees = 5 [json_name = "processing_fees"];
}

message InitiateLoanResponse {
  uint32 code = 1 [json_name = "code"];
  string detail = 2 [json_name = "detail"];
  InitiateLoanData data = 3 [json_name = "data"];
}

message InitiateLoanData {
  int32 user_id = 1 [json_name = "user_id"];
  int32 lender_id = 2 [json_name = "lender_id"];
  int32 bank_account_id = 3 [json_name = "bank_account_id"];
  int32 third_party_id = 4 [json_name = "third_party_id"];
  double loan_amount = 5 [json_name = "loan_amount"];
  double disbursement_amount = 6 [json_name = "disbursement_amount"];
  string loan_type = 7 [json_name = "loan_type"];
  string loan_frequency = 8 [json_name = "loan_frequency"];
  uint32 loan_tenure = 9 [json_name = "loan_tenure"];
  string start_date = 10 [json_name = "start_date"];
  string end_date = 11 [json_name = "end_date"];
  double interest_amount = 12 [json_name = "interest_amount"];
  double processing_fees = 13 [json_name = "processing_fees"];
  string next_payment_date = 14 [json_name = "next_payment_date"];
  double daily_interest_amount = 15 [json_name = "daily_interest_amount"];
  double interest_till_date = 16 [json_name = "interest_till_date"];
  double remaining_amount = 17 [json_name = "remaining_amount"];
  repeated int32 portfolio_ids = 18 [json_name = "portfolio_id"];
  bool is_top_up = 19 [json_name = "is_top_up"];
  double interest_rate = 20 [json_name = "interest_rate"];
  int32 old_loan_id = 21 [json_name = "old_loan_id"];
  Data data = 22 [json_name = "data"];
  int32 loan_id = 23 [json_name = "loan_id"];
  string process_step = 24 [json_name = "process_step"];
  string status = 25 [json_name = "status"];

  message Data {
    map<string, InterestTimelineDetail> interest_timeline = 1 [json_name = "interest_timeline"];
  }
}

message CreateLoanRequest {
  int32 user_id = 1 [json_name = "user_id"];
  int32 loan_id = 2 [json_name = "loan_id"];
}

message CreateLoanResponse {
  uint32 code = 1 [json_name = "code"];
  string detail = 2 [json_name = "detail"];
  CreateLoanData data = 3 [json_name = "data"];
}

message CreateLoanData {
  int32 user_id = 1 [json_name = "user_id"];
  int32 loan_id = 2 [json_name = "loan_id"];
  string loan_no = 3 [json_name = "loan_no"];
  string loan_ref_id = 4 [json_name = "loan_ref_id"];
  bool bajaj_doc_upload = 6 [json_name = "bajaj_doc_upload"];
  bool ckyc = 7 [json_name = "ckyc"];
  repeated CreateLoanDataPayload payload = 8 [json_name = "payload"];
}

message CreateLoanDataPayload {
  FASRecord FASRecords = 1;

  message FASRecord {
    repeated MasterData master_data = 1 [json_name = "MasterData"];
    repeated AccountDetails account_details = 2 [json_name = "Account"];
    repeated OpportunityDetails opportunity_details = 3 [json_name = "Opportunity"];
    repeated ContactDetails contact_details = 4 [json_name = "Contact"];
    repeated ScripDetails scrip_details = 5 [json_name = "ScripDetails"];
    repeated BankDetails bank_details = 6 [json_name = "BankDetails"];
    repeated ApplicantDetails applicant_details = 7 [json_name = "Applicant"];
    repeated RepaymentModeDetails repayment_mode_details = 8 [json_name = "RepaymentModeDetails"];
    repeated CamDetails cam_details = 9 [json_name = "CamDetails"];
  }

  message MasterData {
    string sourcing_channel_name = 1 [json_name = "SourcingChannelName"];
  }

  message AccountDetails {
    string name = 1 [json_name = "Name"];
    string last_name = 2 [json_name = "Last_Name__c"];
    string first_name = 3 [json_name = "First_Name__c"];
    string middle_name = 4 [json_name = "Middle_Name__c"];
    string address_1st_line = 5 [json_name = "Address_1st_Line__c"];
    string address_2nd_line = 6 [json_name = "Address_2nd_Line__c"];
    string address_3rd_line = 7 [json_name = "Address_3rd_Line__c"];
    string acc_city = 8 [json_name = "AccCity__c"];
    string date_of_birth = 9 [json_name = "Date_of_Birth__c"];
    string pan = 10 [json_name = "PANNumber__c"];
    string mobile = 11 [json_name = "Mobile__c"];
    string current_email_id = 12 [json_name = "Current_Email_Id__c"];
    string pincode = 13 [json_name = "PinCode__c"];
    string gender = 14 [json_name = "Gender__c"];
    string permanent_residence_address1 = 15 [json_name = "Permanent_Residence_Address1__c"];
    string permanent_residence_address2 = 16 [json_name = "Permanent_Residence_Address2__c"];
    string permanent_residence_address3 = 17 [json_name = "Permanent_Residence_Address3__c"];
    string permanent_city = 18 [json_name = "Permanent_City__c"];
    string permanent_pincode = 19 [json_name = "Permanent_PinCode__c"];
    string permanent_state = 20 [json_name = "Permanent_State__c"];
    string residence_type_acc = 21 [json_name = "Residence_TypeAcc__c"];
    string permanent_address_as_above = 22 [json_name = "Permanent_Address_as_above__c"];
  }

  message OpportunityDetails {
    string approved_rate = 1 [json_name = "Approved_Rate__c"];
    string approved_tenor = 2 [json_name = "Approved_Tenor__c"];
    string existing_customer = 3 [json_name = "Existing_Customer__c"];
    string loan_type = 4 [json_name = "Loan_Type__c"];
    string end_use = 5 [json_name = "End_Use__c"];
    string existing_loan_remarks = 6 [json_name = "Existing_Loan_Remarks__c"];
    string program_type = 7 [json_name = "Program_Type__c"];
    string type_of_constitution = 8 [json_name = "Type_of_Constitution__c"];
    string name = 9 [json_name = "Name"];
    string close_date = 10 [json_name = "CloseDate"];
    string stage_name = 11 [json_name = "StageName"];
    string product = 12 [json_name = "Product__c"];
    string loan_amount = 13 [json_name = "Loan_Amount__c"];
    string amount_rs = 14 [json_name = "Amount_Rs__c"];
    string sales_notes = 15 [json_name = "Sales_Notes__c"];
    string processing_fees = 16 [json_name = "Processing_Fees__c"];
    string scheme_master = 17 [json_name = "Scheme_Master__c"];
    string tenor = 18 [json_name = "Tenor__c"];
    string location = 19 [json_name = "Location__c"];
    string branch = 20 [json_name = "Branch__c"];
    string branch_name = 21 [json_name = "Branch_Name__c"];
    string sourcing_channel = 22 [json_name = "Sourcing_Channel__c"];
    string customer_reference_number = 23 [json_name = "Customer_reference_number__c"];
  }

  message ContactDetails {
    string address_1 = 1 [json_name = "Address_1__c"];
    string address_2 = 2 [json_name = "Address_2__c"];
    string address_3 = 3 [json_name = "Address_3__c"];
    string app_city = 4 [json_name = "AppCity__c"];
    string applicant_type = 5 [json_name = "ApplicantType__c"];
    string occupation_ckyc = 6 [json_name = "Occupation_CKYC__c"];
    string marital_status = 7 [json_name = "Marital_Status__c"];
    string father_spouse = 8 [json_name = "Father_Spouse__c"];
    string father_spouse_first_name = 9 [json_name = "Father_Spouse_First_Name__c"];
    string father_spouse_last_name = 10 [json_name = "Father_Spouse_Last_Name__c"];
    string father_spouse_salutation = 11 [json_name = "Father_Spouse_Salutation__c"];
    string mother_salutation = 12 [json_name = "Mother_Salutation__c"];
    string mother_first_name = 13 [json_name = "Mother_First_Name__c"];
    string mother_last_name = 14 [json_name = "Mother_Last_Name__c"];
    string branch_address = 15 [json_name = "Branch_Address__c"];
    string customer_consents_with_date_and_time = 16 [json_name = "Customer_Consents_with_date_and_time__c"];
    string customer_device_ip = 17 [json_name = "Customer_Device_IP__c"];
    string customer_device_type = 18 [json_name = "Customer_Device_Type__c"];
    string customer_type = 19 [json_name = "Customer_Type__c"];
    string date_of_birth = 20 [json_name = "Date_of_Birth__c"];
    string district = 21 [json_name = "District__c"];
    string email = 22 [json_name = "Email__c"];
    string first_name = 23 [json_name = "FirstName"];
    string gender = 24 [json_name = "Gender__c"];
    string last_name = 25 [json_name = "LastName"];
    string mobile = 26 [json_name = "Mobile__c"];
    string pan_number = 27 [json_name = "PAN_Number__c"];
    string partner_website = 28 [json_name = "Partner_Website__c"];
    string permanent_address_line_1 = 29 [json_name = "Permanant_Address_Line_1__c"];
    string permanent_address_line_2 = 30 [json_name = "Permanant_Address_Line_2__c"];
    string permanent_address_line_3 = 31 [json_name = "Permanant_Address_Line_3__c"];
    string permanent_city = 32 [json_name = "Permanant_City__c"];
    string permanent_address_same_as_residence = 33 [json_name = "Permanent_Address_same_as_Residence__c"];
    string permanent_pin_code = 34 [json_name = "Permanent_Pin_Code__c"];
    string permanent_state = 35 [json_name = "Permanent_State__c"];
    string pin_code = 36 [json_name = "Pin_Code__c"];
    string residence_type = 37 [json_name = "residence_type__c"];
    string salutation = 38 [json_name = "Salutation"];
    string state = 39 [json_name = "State__c"];
    string website_ip_address = 40 [json_name = "Website_IP_Address__c"];
    string cif_id = 41 [json_name = "CIF_Id__c"];
    string dnc_response = 42 [json_name = "DNC_Response__c"];
    string lan_no_s = 43 [json_name = "LAN_No_s__c"];
  }

  message ScripDetails {
    string digital_scrip_name = 1 [json_name = "Digital_Scrip_Name__c"];
    string digital_scrip_type = 2 [json_name = "Digital_Scrip_Type__c"];
    string number_of_shares = 3 [json_name = "Number_of_Shares__c"];
    string digital_scrip_market_price = 4 [json_name = "Digital_Scrip_market_price__c"];
    string digital_scrips_eligibility = 5 [json_name = "Digital_Scrips_Eligibility__c"];
    string isin = 6 [json_name = "ISIN__c"];
  }

  message BankDetails {
    string bank_name = 1 [json_name = "Bank_Name__c"];
    string bank_branch = 2 [json_name = "Bank_Branch__c"];
    string ifsc_code = 3 [json_name = "IFSC_Code__c"];
    string bank_acct_number = 4 [json_name = "Bank_Acct_Number__c"];
    string account_type = 5 [json_name = "Account_Type__c"];
    string applicant_name = 6 [json_name = "Applicant_Name__c"];
  }

  message ApplicantDetails {
    string ckyc_no = 1 [json_name = "CKYC_No__c"];
    string identity_document_expiry_date = 2 [json_name = "Identity_Document_Expiry_Date__c"];
    string identity_document_no = 3 [json_name = "Identity_Document_No__c"];
    string permanent_address_doc_no = 4 [json_name = "Permanent_Address_Doc_No__c"];
    string permanent_address_expiry_date = 5 [json_name = "Permanent_Address_Expiry_Date__c"];
    string proof_of_address_submitted_for_permanent = 6 [json_name = "Proof_of_Address_Submitted_for_Permanent__c"];
    string proof_of_identity = 7 [json_name = "Proof_of_Identity__c"];
    string proof_of_residence_address_submitted = 8 [json_name = "Proof_of_Residence_Address_Submitted__c"];
    string rekyc = 9 [json_name = "ReKYC__c"];
    string residence_address_doc_no = 10 [json_name = "Residence_Address_Doc_No__c"];
    string residence_address_expiry_date = 11 [json_name = "Residence_Address_Expiry_Date__c"];
  }

  message RepaymentModeDetails {
    string open_ecs_max_limit = 1 [json_name = "Open_ECS_Max_Limit__c"];
    string ecs_end_date = 2 [json_name = "ECS_End_Date__c"];
    string ecs_start_date = 3 [json_name = "ECS_Start_Date__c"];
    string ecs_amount = 4 [json_name = "ECS_Amount__c"];
    string open_ecs_facility = 5 [json_name = "Open_ECS_Facility__c"];
  }

  message CamDetails {
    string name = 1 [json_name = "Name"];
    string roi = 2 [json_name = "ROI__c"];
    string currency_iso_code = 3 [json_name = "CurrencyIsoCode"];
    string loan_amount = 4 [json_name = "Loan_Amount__c"];
    string cy_tenor = 5 [json_name = "CY_Tenor__c"];
  }
}

message FetchExistingLoanResponse {
  uint32 code = 1 [json_name = "code"];
  string detail = 2 [json_name = "detail"];
  ExisingLoanData data = 3 [json_name = "data"];
}

message ExisingLoanData {
  string e_sign_link = 1 [json_name = "e_agreement"];
  string e_mandate_link = 2 [json_name = "e_mandate"];
  string kfs_link = 6 [json_name = "kfs"];
  bool e_sign_completed = 3 [json_name = "e_agreement_signed"];
  bool e_mandate_completed = 4 [json_name = "e_mandate_signed"];
  bool kfs_completed = 7 [json_name = "kfs_signed"];
  bool kfs_rejected = 8 [json_name = "kfs_rejected"];
  bool process_completed = 5 [json_name = "process_completed"];
}

message FetchIndividualLoanResponse {
  uint32 code = 1 [json_name = "code"];
  string detail = 2 [json_name = "detail"];
  IndividualLoanData data = 3 [json_name = "data"];
}

message IndividualLoanData {
  int32 user_id = 1 [json_name = "user_id"];
  int32 loan_id = 2 [json_name = "loan_id"];
  string loan_status = 3 [json_name = "loan_status"];
  double loan_amount = 4 [json_name = "loan_amount"];
  double remaining_amount = 5 [json_name = "remaining_amount"];
  double disbursement_amount = 6 [json_name = "disbursement_amount"];
  string next_payment_date = 7 [json_name = "next_payment_date"];
  double due_amount = 8 [json_name = "due_amount"];
  double interest_rate = 9 [json_name = "interest_rate"];
  string loan_tenure = 10 [json_name = "loan_tenure"];
  string start_date = 11 [json_name = "start_date"];
  string end_date = 12 [json_name = "end_date"];
  double processing_fees = 13 [json_name = "processing_fees"];
  bool is_top_up = 14 [json_name = "is_top_up"];
  int32 old_loan_id = 15 [json_name = "old_loan_id"];
  bool is_active = 16 [json_name = "is_active"];
  map<string, InterestTimelineDetail> repayment_timeline = 17 [json_name = "repayment_timeline"];
  string application_number = 18 [json_name = "application_number"];
  string account_number = 19 [json_name = "account_number"];
  string lan_no = 20 [json_name = "lan_no"];
}

message RegenerateLoanDocumentLinkRequest {
  int32 user_id = 1 [json_name = "user_id"];
  int32 loan_id = 2 [json_name = "loan_id"];
  string doc_type = 3 [json_name = "doc_type"];
}

message RegenerateLoanDocumentLinkResponse {
  uint32 code = 1 [json_name = "code"];
  string detail = 2 [json_name = "detail"];
  RegenerateLoanDocumentLinkData data = 3 [json_name = "data"];
}

message RegenerateLoanDocumentLinkData {
  int32 user_id = 1 [json_name = "user_id"];
  int32 loan_id = 2 [json_name = "loan_id"];
  string doc_type = 3 [json_name = "doc_type"];
  string process_id = 4 [json_name = "process_id"];
}
