syntax = "proto3";

package vendors.fiftyfin;

option go_package = "github.com/epifi/gamma/api/vendors/fiftyfin";
option java_package = "com.github.epifi.gamma.api.vendors.fiftyfin";

message UserSignupRequest {
  string first_name = 1 [json_name = 'first_name'];
  string last_name = 2 [json_name = 'last_name'];
  string email = 3 [json_name = 'email'];
  string phone_number = 4 [json_name = 'phone_number'];
  string date_of_birth = 5 [json_name = 'date_of_birth'];
  string referral_code = 6 [json_name = 'referral_code'];
}

message UserSignupResponse {
  int32 code = 1 [json_name = 'code'];
  string detail = 2 [json_name = 'detail'];
  Data data = 3 [json_name = 'data'];

  message Data {
    int32 id = 1;
    string referral_code = 2;
    int32 third_party_id = 3;
  }
}
