syntax = "proto3";

package vendors.fiftyfin;

option go_package = "github.com/epifi/gamma/api/vendors/fiftyfin";

message GenerateKarvyOtpRequest {
  string user_id = 1 [json_name = "user_id"];
}

message GenerateKarvyOtpResponse {
  message Data {
    string request_id = 1 [json_name = "karvy_request_id"];
  }

  uint32 code = 1 [json_name = "code"];
  string detail = 2 [json_name = "detail"];
  Data data = 3 [json_name = "data"];
}

