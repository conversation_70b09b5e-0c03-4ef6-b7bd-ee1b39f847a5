syntax = "proto3";

package vendors.m2p.lending;

import "api/vendors/m2p/lending/credit_card.proto";

option go_package = "github.com/epifi/gamma/api/vendors/m2p/lending/lms";
option java_package = "com.github.epifi.gamma.api.vendors.m2p.lending.lms";

// API doc: https://m2p-fintech.stoplight.io/docs/lms/e0a83c5463de3-fetch-transactions
message GetTransactionsRequest {
  // Entity id is the unique customer id for a credit card user
  string entity_id = 1 [json_name = "entityId"];
}

message GetTransactionsResponse {
  repeated GetTransactionsResponseResult results = 1 [json_name = "result"];
  Exception exception = 2 [json_name = "exception"];
  Pagination pagination = 3 [json_name = "pagination"];
}

message GetTransactionsResponseResult {
  int64 transaction_date = 1 [json_name = "transactionDate"];
  string amount = 2 [json_name = "amount"];
  // credit or debit
  string crdr = 3 [json_name = "crDr"];
  string transaction_category = 4 [json_name = "txnCategory"];
  string ext_transaction_id = 5 [json_name = "extTxnId"];
  string merchant_id = 6 [json_name = "merchantId"];
  string merchant_name = 7 [json_name = "merchantName"];
  string merchant_terminal_id = 8 [json_name = "terminalId"];
  string rrn_number = 9 [json_name = "rrn"];
  string stan_number = 10 [json_name = "stan"];
  string acquirer_id = 11 [json_name = "acquirerId"];
  string merchant_category_code = 12 [json_name = "mcc"];
  string after_transaction_limit = 13 [json_name = "afterTransactionLimit"];
  string billed_status = 14 [json_name = "billedStatus"];
  string authorization_status = 15 [json_name = "authorizationStatus"];
  string kit_number = 16 [json_name = "kitNo"];
  string transaction_origin = 17 [json_name = "txnOrigin"];
  string auth_code = 18 [json_name = "authCode"];
  string transaction_currency_code = 19 [json_name = "txnCurrencyCode"];
  // Transaction Description
  string description = 20 [json_name = "description"];
  EmiEligibility emi_eligibility = 21 [json_name = "emiEligibility"];
}

message EmiEligibility {
  double loan_eligible_amount = 1 [json_name = "eligibleAmount"];
  SingleEmi single_emi = 2 [json_name = "singleEmi"];
  repeated GroupEmi group_emi = 3 [json_name = "groupEmi"];
}

message SingleEmi {
  // Unique Loan Product Id
  string rule_id = 1 [json_name = "ruleId"];
  // Minimum amount of Loan
  string min_amount = 2 [json_name = "minAmount"];
  // Maximum amount of Loan
  string max_amount = 3 [json_name = "maxAmount"];
}

message GroupEmi {
  // Unique Loan Product Id
  string rule_id = 1 [json_name = "ruleId"];
  // Minimum amount of Loan
  string min_amount = 2 [json_name = "minAmount"];
  // Maximum amount of Loan
  string max_amount = 3 [json_name = "maxAmount"];
}

// API doc: https://m2p-fintech.stoplight.io/docs/lms/6b58cf561b3f4-get-eligible-transactions
message GetEligibleTransactionsRequest {
  // Entity id is the unique customer id for a credit card user
  string entity_id = 1 [json_name = "entityId"];
}

message GetEligibleTransactionsResponse {
  repeated GetEligibleTransactionsResponseResult results = 1 [json_name = "result"];
  Exception exception = 2 [json_name = "exception"];
  Pagination pagination = 3 [json_name = "pagination"];
}

message GetEligibleTransactionsResponseResult {
  double amount = 1 [json_name = "amount"];
  // Unique External Transaction Id
  string ext_transaction_id = 2 [json_name = "extTxnId"];
  SingleEmi single_emi = 3 [json_name = "singleEmi"];
  repeated GroupEmi group_emi = 4 [json_name = "groupEmi"];
}

// API doc: https://m2p-fintech.stoplight.io/docs/lms/55014a31ab7e7-preview-loan
message PreviewLoanRequest {
  // Entity id is the unique customer id for a credit card user
  string entity_id = 1 [json_name = "entityId"];
  // Unique Loan Product Id
  string rule_id = 2 [json_name = "ruleId"];
  string request_type = 3 [json_name = "requestType"];
  repeated EligibleTransaction eligible_transactions = 4 [json_name = "transactions"];
}

message EligibleTransaction {
  // Loan amount
  double amount = 1 [json_name = "amount"];
  // Unique External Transaction Id
  string ext_transaction_id = 2 [json_name = "extTxnId"];
}

message PreviewLoanResponse {
  PreviewLoanResponseResult result = 1 [json_name = "result"];
  Exception exception = 2 [json_name = "exception"];
  Pagination pagination = 3 [json_name = "pagination"];
}

message PreviewLoanResponseResult {
  repeated EligibleOption eligible_options = 1 [json_name = "eligibleOptions"];
  string request_id = 2 [json_name = "requestId"];
}

message EligibleOption {
  double loan_amount = 1 [json_name = "loanAmount"];
  double interest_rate = 2 [json_name = "interestRate"];
  // Total Number of Months
  string tenure = 3 [json_name = "tenure"];
  double emi_amount = 4 [json_name = "emiAmount"];
  double broken_period_interest = 5 [json_name = "brokenPeriodInterest"];
  double total_principal = 6 [json_name = "totalPrincipal"];
  double total_principal_paid = 7 [json_name = "totalPrincipalPaid"];
  double total_interest = 8 [json_name = "totalInterest"];
  double total_fee = 9 [json_name = "totalFee"];
  double total_tax = 10 [json_name = "totalTax"];
  double processing_fee = 11 [json_name = "processingFee"];
  double processing_fee_tax = 12 [json_name = "processingFeeTax"];
  double discount_amount = 13 [json_name = "discountAmount"];
  double total_expected_repayment = 14 [json_name = "totalExpectedRepayment"];
  string kit_number = 15 [json_name = "kitNo"];
  string transaction_origin = 16 [json_name = "txnOrigin"];
  string auth_code = 17 [json_name = "authCode"];
  string transaction_currency_code = 18 [json_name = "txnCurrencyCode"];
  // Transaction Description
  string description = 19 [json_name = "description"];
  repeated Amortization amortizations = 20 [json_name = "amortizations"];
}

message Amortization {
  int64 installment_number = 1 [json_name = "installmentNo"];
  // Customer Due Date
  string due_date = 2 [json_name = "dueDate"];
  double principal = 3 [json_name = "principal"];
  double principal_paid = 4 [json_name = "principalPaid"];
  double principal_outstanding = 5 [json_name = "principalOutstanding"];
  double interest = 6 [json_name = "interest"];
  double interest_paid = 7 [json_name = "interestPaid"];
  double interest_outstanding = 8 [json_name = "interestOutstanding"];
  double interest_waived = 9 [json_name = "interestWaived"];
  double fee = 10 [json_name = "fee"];
  double fee_paid = 11 [json_name = "feePaid"];
  double fee_waived = 12 [json_name = "feeWaived"];
  double fee_outstanding = 13 [json_name = "feeOutstanding"];
  double closing_principal_amount = 14 [json_name = "closingPrincipalAmount"];
  double total_due = 15 [json_name = "totalDue"];
  double total_paid = 16 [json_name = "totalPaid"];
  double total_outstanding = 17 [json_name = "totalOutstanding"];
  double total_waived = 18 [json_name = "totalWaived"];
  double tax = 19 [json_name = "tax"];
  bool complete = 20 [json_name = "complete"];
}

// API doc: https://m2p-fintech.stoplight.io/docs/lms/77791f85346a3-create-loan
message CreateLoanRequest {
  // Entity id is the unique customer id for a credit card user
  string entity_id = 1 [json_name = "entityId"];
  string request_id = 2 [json_name = "requestId"];
  // Total Number of Months
  int64 tenure = 3 [json_name = "tenure"];
}

message CreateLoanResponse {
  CreateLoanResponseResult result = 1 [json_name = "result"];
  Exception exception = 2 [json_name = "exception"];
  Pagination pagination = 3 [json_name = "pagination"];
}

message CreateLoanResponseResult {
  string loan_id = 1 [json_name = "loanId"];
}

// API doc: https://m2p-fintech.stoplight.io/docs/lms/6bdfe2378495d-get-loan-by-id
message GetLoanByIdRequest {
  // Entity id is the unique customer id for a credit card user
  string entity_id = 1 [json_name = "entityId"];
  // Loan Id
  string loan_id = 2 [json_name = "loanId"];
}

message GetLoanByIdResponse {
  GetLoanByIdResponseResult result = 1 [json_name = "result"];
  Exception exception = 2 [json_name = "exception"];
  Pagination pagination = 3 [json_name = "pagination"];
}

message GetLoanByIdResponseResult {
  string loan_id = 1 [json_name = "loanId"];
  double loan_amount = 2 [json_name = "loanAmount"];
  double interest_rate = 3 [json_name = "interestRate"];
  string tenure = 4 [json_name = "tenure"];
  double emi_amount = 5 [json_name = "emiAmount"];
  string loan_status = 6 [json_name = "loanStatus"];
  string broken_period_interest = 7 [json_name = "brokenPeriodInterest"];
  double total_principal = 8 [json_name = "totalPrincipal"];
  double principal_over_due = 9 [json_name = "principalOverdue"];
  double principal_outstanding = 10 [json_name = "principalOutstanding"];
  double total_interest = 11 [json_name = "totalInterest"];
  double total_fee = 12 [json_name = "totalFee"];
  double total_tax = 13 [json_name = "totalTax"];
  double fee_waived = 14 [json_name = "feeWaived"];
  double fee_outstanding = 15 [json_name = "feeOutstanding"];
  double discount_amount = 16 [json_name = "discountAmount"];
  double total_expected_repayment = 17 [json_name = "totalExpectedRepayment"];
  double interest_paid = 18 [json_name = "interestPaid"];
  double interest_outstanding = 19 [json_name = "interestOutstanding"];
  double interest_over_due = 20 [json_name = "interestOverdue"];
  double fee_over_due = 21 [json_name = "feeOverdue"];
  double total_fee_paid = 22 [json_name = "totalFeePaid"];
  double processing_fee = 23 [json_name = "processingFee"];
  double processing_fee_tax = 24 [json_name = "processingFeeTax"];
  string number_of_due_repayments = 25 [json_name = "numberOfDueRepayments"];
  string number_of_paid_repayments = 26 [json_name = "numberOfPaidRepayments"];
  string disbursed_date = 27 [json_name = "disbursedDate"];
  double total_repayment = 28 [json_name = "totalRepayment"];
  double total_outstanding = 29 [json_name = "totalOutstanding"];
  double total_over_due = 30 [json_name = "totalOverDue"];
  double total_waived_off = 31 [json_name = "totalWaivedOff"];
  repeated Transactions transactions = 32 [json_name = "transactions"];
  bool is_loan_cancel_applicable = 33 [json_name = "isLoanCancelApplicable"];
  bool is_loan_reschedule_applicable = 34 [json_name = "isLoanRescheduleApplicable"];
  bool is_loan_pre_closure_applicable = 35 [json_name = "isLoanPreClosureApplicable"];
  repeated Amortization amortizations = 36 [json_name = "currentAmortizations"];
}

message Transactions {
  string ext_transaction_id = 1 [json_name = "extTxnId"];
  double transaction_amount = 2 [json_name = "txnAmount"];
  double amount = 3 [json_name = "amount"];
  string description = 4 [json_name = "description"];
  int64 transaction_date = 5 [json_name = "transactionDate"];
  string merchant_category_code = 6 [json_name = "mcc"];
  string transaction_type = 7 [json_name = "txnType"];
  string sub_transaction_type = 8 [json_name = "subTxnType"];
  string transaction_origin = 9 [json_name = "txnOrigin"];
  string merchant_name = 10 [json_name = "merchantName"];
}

// API doc: https://m2p-fintech.stoplight.io/docs/lms/a62cf332717d3-get-loan-by-status
message GetLoanByStatusRequest {
  // Entity id is the unique customer id for a credit card user
  string entity_id = 1 [json_name = "entityId"];
  // Loan Id
  string status = 2 [json_name = "status"];
}

message GetLoanByStatusResponse {
  repeated GetLoanByStatusResponseResult result = 1 [json_name = "result"];
  Exception exception = 2 [json_name = "exception"];
  Pagination pagination = 3 [json_name = "pagination"];
}

message GetLoanByStatusResponseResult {
  string loan_id = 1 [json_name = "loanId"];
  double loan_amount = 2 [json_name = "loanAmount"];
  double interest_rate = 3 [json_name = "interestRate"];
  string tenure = 4 [json_name = "tenure"];
  double emi_amount = 5 [json_name = "emiAmount"];
  string loan_status = 6 [json_name = "loanStatus"];
  string broken_period_interest = 7 [json_name = "brokenPeriodInterest"];
  double total_principal = 8 [json_name = "totalPrincipal"];
  double principal_over_due = 9 [json_name = "principalOverdue"];
  double principal_outstanding = 10 [json_name = "principalOutstanding"];
  double total_interest = 11 [json_name = "totalInterest"];
  double total_fee = 12 [json_name = "totalFee"];
  double total_tax = 13 [json_name = "totalTax"];
  double fee_waived = 14 [json_name = "feeWaived"];
  double fee_outstanding = 15 [json_name = "feeOutstanding"];
  double discount_amount = 16 [json_name = "discountAmount"];
  double total_expected_repayment = 17 [json_name = "totalExpectedRepayment"];
  double interest_paid = 18 [json_name = "interestPaid"];
  double interest_outstanding = 19 [json_name = "interestOutstanding"];
  double interest_over_due = 20 [json_name = "interestOverdue"];
  double fee_over_due = 21 [json_name = "feeOverdue"];
  double total_fee_paid = 22 [json_name = "totalFeePaid"];
  double processing_fee = 23 [json_name = "processingFee"];
  double processing_fee_tax = 24 [json_name = "processingFeeTax"];
  string number_of_due_repayments = 25 [json_name = "numberOfDueRepayments"];
  string number_of_paid_repayments = 26 [json_name = "numberOfPaidRepayments"];
  string disbursed_date = 27 [json_name = "disbursedDate"];
  double total_repayment = 28 [json_name = "totalRepayment"];
  double total_outstanding = 29 [json_name = "totalOutstanding"];
  double total_over_due = 30 [json_name = "totalOverDue"];
  double total_waived_off = 31 [json_name = "totalWaivedOff"];
  repeated Transactions transactions = 32 [json_name = "transactions"];
  bool is_loan_cancel_applicable = 33 [json_name = "isLoanCancelApplicable"];
  bool is_loan_reschedule_applicable = 34 [json_name = "isLoanRescheduleApplicable"];
  bool is_loan_pre_closure_applicable = 35 [json_name = "isLoanPreClosureApplicable"];
  repeated Amortization amortizations = 36 [json_name = "currentAmortizations"];
}

// API doc: https://m2p-fintech.stoplight.io/docs/lms/ba49d3a6bb766-preclose-preview-loan
message PreviewPreCloseLoanRequest {
  // Entity id is the unique customer id for a credit card user
  string entity_id = 1 [json_name = "entityId"];
  string loan_id = 2 [json_name = "loanId"];
}

message PreviewPreCloseLoanResponse {
  PreviewPreCloseLoanResponseResult result = 1 [json_name = "result"];
  Exception exception = 2 [json_name = "exception"];
  Pagination pagination = 3 [json_name = "pagination"];
}

message PreviewPreCloseLoanResponseResult {
  double total_payable_amount = 1 [json_name = "totalPayableAmount"];
  double principal = 2 [json_name = "principal"];
  double interest = 3 [json_name = "interest"];
  double interest_tax = 4 [json_name = "interestTax"];
  double processing_fee = 5 [json_name = "processingFee"];
  double processing_fee_tax = 6 [json_name = "processingFeeTax"];
  double pre_closure_fee = 7 [json_name = "preClosureFee"];
  double pre_closure_fee_tax = 8 [json_name = "preClosureFeeTax"];
  double total_fee = 9 [json_name = "totalFee"];
  double total_tax = 10 [json_name = "totalTax"];
  string loan_id = 11 [json_name = "loanId"];
}

// API doc: https://m2p-fintech.stoplight.io/docs/lms/ba49d3a6bb766-preclose-preview-loan
message PreCloseLoanRequest {
  // Entity id is the unique customer id for a credit card user
  string entity_id = 1 [json_name = "entityId"];
  string loan_id = 2 [json_name = "loanId"];
}

message PreCloseLoanResponse {
  PreCloseLoanResponseResult result = 1 [json_name = "result"];
  Exception exception = 2 [json_name = "exception"];
  Pagination pagination = 3 [json_name = "pagination"];
}

message PreCloseLoanResponseResult {
  double total_payable_amount = 1 [json_name = "totalPayableAmount"];
  double principal = 2 [json_name = "principal"];
  double interest = 3 [json_name = "interest"];
  double interest_tax = 4 [json_name = "interestTax"];
  double processing_fee = 5 [json_name = "processingFee"];
  double processing_fee_tax = 6 [json_name = "processingFeeTax"];
  double pre_closure_fee = 7 [json_name = "preClosureFee"];
  double pre_closure_fee_tax = 8 [json_name = "preClosureFeeTax"];
  double total_fee = 9 [json_name = "totalFee"];
  double total_tax = 10 [json_name = "totalTax"];
}

// API doc: https://m2p-fintech.stoplight.io/docs/lms/b1f7760f75879-cancel-loan
message CancelLoanRequest {
  // Entity id is the unique customer id for a credit card user
  string entity_id = 1 [json_name = "entityId"];
  string loan_id = 2 [json_name = "loanId"];
  string interest = 3 [json_name = "interest"];
  string charges = 4 [json_name = "charges"];
}

message CancelLoanResponse {
  CancelLoanResponseResult result = 1 [json_name = "result"];
  Exception exception = 2 [json_name = "exception"];
  Pagination pagination = 3 [json_name = "pagination"];
}

message CancelLoanResponseResult {
  double interest = 1 [json_name = "interest"];
  double interest_tax = 2 [json_name = "interestTax"];
  double fee = 3 [json_name = "fee"];
  double fee_tax = 4 [json_name = "feeTax"];
  double total_payable_amount = 5 [json_name = "totalPayableAmount"];
  RefundDetails refund_details = 6 [json_name = "RefundDetails"];
}

message RefundDetails {
  double total_refund_amount = 1 [json_name = "totalRefundAmount"];
  double principal = 2 [json_name = "principal"];
  double interest = 3 [json_name = "interest"];
  double interest_tax = 4 [json_name = "interestTax"];
  double fee = 5 [json_name = "fee"];
  double fee_tax = 6 [json_name = "feeTax"];
}


