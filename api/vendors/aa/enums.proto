syntax = "proto3";

package vendors.aa;

option go_package = "github.com/epifi/gamma/api/vendors/aa";
option java_package = "com.github.epifi.gamma.api.vendors.aa";

enum RebitApiVersion {
  REBIT_API_VERSION_UNSPECIFIED = 0;
  // 1.1.2 - https://api.rebit.org.in/viewSpec/AA_1_1_2.yaml
  REBIT_API_VERSION_1_1_2 = 1;
  // 2.1.0 - https://specifications.rebit.org.in/api_schema/account_aggregator/AA_ChangeLog_2_0_0.txt
  // https://api.rebit.org.in/viewSpec/AA_2_1_0.yaml
  REBIT_API_VERSION_2_1_0 = 2;
  // 2.0.0 - https://specifications.rebit.org.in/api_schema/account_aggregator/AA_ChangeLog_2_0_0.txt
  // https://api.rebit.org.in/viewSpec/AA_2_0_0.yaml
  REBIT_API_VERSION_2_0_0 = 3;
}
