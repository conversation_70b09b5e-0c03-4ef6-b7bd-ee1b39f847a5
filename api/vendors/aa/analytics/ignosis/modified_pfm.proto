syntax = "proto3";

package vendors.aa.analytics.ignosis;

import "api/vendors/aa/analytics/ignosis/pfm.proto";

option go_package = "github.com/epifi/gamma/api/vendors/aa/analytics/ignosis";
option java_package = "com.github.epifi.gamma.api.vendors.aa.analytics.ignosis";

message ModifiedAnalysisResponse {
  string tracking_id = 1 [json_name = "trackingId"];
  string reference_id = 2 [json_name = "referenceId"];
  map<string, AccountProfile> account_profiles = 3 [json_name = "accountProfiles"]; // key: Account->linkedAccRef
  OverallAnalysis overall_analysis = 4 [json_name = "overallAnalysis"];
  map<string, AccountWiseAnalysis> account_wise_analysis = 5 [json_name = "accountWiseAnalysis"]; // key: linkedAccRef
  FrequentCredits frequent_credits = 6 [json_name = "frequentCredits"];
  FrequentDebits frequent_debits = 7 [json_name = "frequentDebits"];
  LoanCreditsAnalysis loan_credits_analysis = 8 [json_name = "loanCreditsAnalysis"];
  InvestmentAnalysis investment_analysis = 9 [json_name = "investmentAnalysis"];
  PaymentTypeAnalytics payment_type_analytics = 10 [json_name = "paymentTypeAnalytics"];
  CompleteCategoryWiseAnalysis complete_category_wise_analysis = 11 [json_name = "completeCategoryWiseAnalysis"];
  BounceAnalysis bounce_analysis = 12 [json_name = "bounceAnalysis"];
  map<string, AnalysisResponse.Transaction> all_transactions = 13 [json_name = "allTransactions"]; // key: unique_id
  map<string, Top5FundTransfer> top5_fund_transfer = 14 [json_name = "top5FundTransfer"];  // map as given by bv vendor: "CREDIT","DEBIT"
  AbbTables abb_tables = 15 [json_name = "abbTables"];
  map<string, AnalysisResponse.FcuIndicator> account_wise_fcu_indicators = 16 [json_name = "accountWiseFcuIndicators"]; // key: linkedAccRef
  AnalysisResponse.FcuIndicator fcu_indicators = 17 [json_name = "fcuIndicators"];
  LoanCreditSummary loan_credit_summary = 18 [json_name = "loanCreditSummary"];
  CompleteCategoryWiseAnalysisAllAnalysis complete_category_wise_analysis_all_analysis = 19 [json_name = "completeCategoryWiseAnalysisAllAnalysis"];
  OverallEmiAnalytics overall_emi_analytics = 20 [json_name = "overallEmiAnalytics"];
  FixedDepositAnalysis fixed_deposit_analysis = 21 [json_name = "fixedDepositAnalysis"];
  RecurringDepositAnalysis recurring_deposit_analysis = 22 [json_name = "recurringDepositAnalysis"];
  EquityAnalysis equity_analysis = 23 [json_name = "equityAnalysis"];
  MutualFundAnalysis mutual_fund_analysis = 24 [json_name = "mutualFundAnalysis"];
  bytes sub_analytics_response = 25 [json_name = "subAnalyticsResponse"]; // lenden specific data

  message AccountProfile {
    AnalysisResponse.AccountProfile.Summary summary = 1 [json_name = "summary"];
    AnalysisResponse.AccountProfile.Account account = 2 [json_name = "Account"];
    Holders holders = 3 [json_name = "Holders"];

    message Holders {
      string type = 1 [json_name = "type"];
      map<string, AnalysisResponse.AccountProfile.Holders.Holder> holder = 2 [json_name = "Holder"]; // key: pan + mobile string (just in case if anyone is empty)
    }
  }

  message OverallAnalysis {
    int32 total_accounts = 1 [json_name = "totalAccounts"];
    double credit_debit_ratio = 2 [json_name = "creditDebitRatio"];
    double avg_eod_balance = 3 [json_name = "avgEodBalance"];
    double variance_of_balance = 4 [json_name = "varianceOfBalance"];
    double std_deviation_of_balance = 5 [json_name = "stdDeviationOfBalance"];
    double closing_balance = 6 [json_name = "closingBalance"];
    double minimum_eod_balance = 7 [json_name = "minimumEodBalance"];
    double maximum_eod_balance = 8 [json_name = "maximumEodBalance"];
    int32 total_credit_txn = 9 [json_name = "totalCreditTxn"];
    double total_credit_amount = 10 [json_name = "totalCreditAmount"];
    double avg_credit_amount = 11 [json_name = "avgCreditAmount"];
    int32 total_debit_txn = 12 [json_name = "totalDebitTxn"];
    double total_debit_amount = 13 [json_name = "totalDebitAmount"];
    double avg_debit_amount = 14 [json_name = "avgDebitAmount"];
    double minimum_credit_amount = 15 [json_name = "minimumCreditAmount"];
    double maximum_credit_amount = 16 [json_name = "maximumCreditAmount"];
    double minimum_debit_amount = 17 [json_name = "minimumDebitAmount"];
    double maximum_debit_amount = 18 [json_name = "maximumDebitAmount"];
    double total_credit_debit_amount = 19 [json_name = "totalCreditDebitAmount"];
    double surplus_amount = 20 [json_name = "surplusAmount"];
    double average_surplus_amount = 21 [json_name = "averageSurplusAmount"];
    int32 total_credit_txn_last_90_days = 22 [json_name = "totalCreditTxnLast90Days"];
    int32 total_debit_txn_last_90_days = 23 [json_name = "totalDebitTxnLast90Days"];
    double ratio_of_total_credit_txn_last_30_to_60_days = 24 [json_name = "ratioOfTotalCreditTxnLast30to60Days"];
    double ratio_of_total_credit_txn_last_30_to_90_days = 25 [json_name = "ratioOfTotalCreditTxnLast30To90Days"];
    double credit_amount_last_90_days = 26 [json_name = "creditAmountLast90Days"];
    int32 total_debit_txn_last_180_days = 27 [json_name = "totalDebitTxnLast180Days"];
    double avg_eod_balance_last_90_days = 28 [json_name = "avgEodBalanceLast90Days"];
    double avg_eod_balance_last_180_days = 29 [json_name = "avgEodBalanceLast180Days"];
    double avg_eod_balance_180_to_270_days = 30 [json_name = "avgEodBalance180to270Days"];
    double avg_eod_balance_270_to_365_days = 31 [json_name = "avgEodBalance270to365Days"];
    double ratio_of_avg_eod_balance_0_to_90_vs_90_to_180 = 32 [json_name = "ratioOfAvgEodBalance0to90vs90to180"];
    double min_eod_balance_last_90_days = 33 [json_name = "minEodBalanceLast90Days"];
    map<string, AnalysisResponse.OverallAnalysis.MonthlyAnalytics> monthly_analytics = 34 [json_name = "monthlyAnalytics"]; // key: year-month
    map<string, AnalysisResponse.OverallAnalysis.DailyAnalytics> daily_analytics = 35 [json_name = "dailyAnalytics"]; // key: date
    double velocity = 36 [json_name = "velocity"];
    double total_salary_amount = 37 [json_name = "totalSalaryAmount"];
    int32 total_salary_txns = 38 [json_name = "totalSalaryTxns"];
    double avg_monthly_balance = 39 [json_name = "avgMonthlyBalance"];
    double last_3_months_avg_monthly_balance = 40 [json_name = "last3MonthsAvgMonthlyBalance"];
    double last_6_months_avg_monthly_balance = 41 [json_name = "last6MonthsAvgMonthlyBalance"];
    int32 high_value_credit_txns_count = 42 [json_name = "highValueCreditTxnsCount"];
    double high_value_credit_txns_amount = 43 [json_name = "highValueCreditTxnsAmount"];
    int32 high_value_debit_txns_count = 44 [json_name = "highValueDebitTxnsCount"];
    double high_value_debit_txns_amount = 45 [json_name = "highValueDebitTxnsAmount"];
    double monthly_avg_debit_amount = 46 [json_name = "monthlyAvgDebitAmount"];
    double last_3_month_avg_debit_amount = 47 [json_name = "last3MonthAvgDebitAmount"];
    double last_6_month_avg_debit_amount = 48 [json_name = "last6MonthAvgDebitAmount"];
    double monthly_avg_credit_amount = 49 [json_name = "monthlyAvgCreditAmount"];
    double last_3_month_avg_credit_amount = 50 [json_name = "last3MonthAvgCreditAmount"];
    double last_6_month_avg_credit_amount = 51 [json_name = "last6MonthAvgCreditAmount"];
    bool bank_penalties_detected = 52 [json_name = "bankPenaltiesDetected"];
    double total_bank_penalties = 53 [json_name = "totalBankPenalties"];
    int32 total_cash_deposit_txn = 54 [json_name = "totalCashDepositTxn"];
    double total_cash_deposit_amount = 55 [json_name = "totalCashDepositAmount"];
    int32 total_cash_withdrawals_txn = 56 [json_name = "totalCashWithdrawalsTxn"];
    double total_cash_withdrawals_amount = 57 [json_name = "totalCashWithdrawalsAmount"];
    double total_expense_amount = 58 [json_name = "totalExpenseAmount"];
    double average_expense_amount = 59 [json_name = "averageExpenseAmount"];
    int32 total_no_of_expense_transactions = 60 [json_name = "totalNoOfExpenseTransactions"];
    double avg_monthly_high_value_credit_txns_amount = 61 [json_name = "avgMonthlyHighValueCreditTxnsAmount"];
    double avg_monthly_high_value_debit_txns_amount = 62 [json_name = "avgMonthlyHighValueDebitTxnsAmount"];
    int32 total_chq_issue_txns = 63 [json_name = "totalChqIssueTxns"];
    AnalysisResponse.OverallAnalysis.MonthlyValuesAverage monthly_values_average = 64 [json_name = "monthlyValuesAverage"];
    int32 total_no_of_net_credit_transactions = 65 [json_name = "totalNoofNetCreditTransactions"];
    double total_net_credit_transaction_amount = 66 [json_name = "totalNetCreditTransactionAmount"];
    double average_net_credit_transaction_amount = 67 [json_name = "averageNetCreditTransactionAmount"];
    int32 total_no_of_net_debit_transactions = 68 [json_name = "totalNoofNetDebitTransactions"];
    double total_net_debit_transaction_amount = 69 [json_name = "totalNetDebitTransactionAmount"];
    double average_net_debit_transaction_amount = 70 [json_name = "averageNetDebitTransactionAmount"];
    bool high_value_cash_withdrawal_txns = 71 [json_name = "highValueCashWithdrawalTxns"];
    int32 total_transactions = 72 [json_name = "totalTransactions"];
    bool high_value_cash_debit_txns = 73 [json_name = "highValueCashDebitTxns"];
    bool high_value_cash_txns = 74 [json_name = "highValueCashTxns"];
    double volatility_of_balance = 75 [json_name = "volatilityOfBalance"];
    map<string, AnalysisResponse.OverallAnalysis.WeeklyAnalytics> weekly_analytics = 76 [json_name = "weeklyAnalytics"]; // key: year-month-week
    CustomParameterizedVariables custom_parameterized_variables = 77 [json_name = "customParameterizedVariables"];

    message CustomParameterizedVariables {
      double median_amount_salary_last_180_days = 1 [json_name = "medianAmountSalaryLast180Days"];
    }
  }

  message AccountWiseAnalysis {
    string linked_acc_ref = 1 [json_name = "linkedAccRef"];
    string account_number = 2 [json_name = "accountNumber"];
    string bank = 3 [json_name = "bank"];
    int32 total_credit_txn_last_90_days = 4 [json_name = "totalCreditTxnLast90Days"];
    int32 total_debit_txn_last_90_days = 5 [json_name = "totalDebitTxnLast90Days"];
    double count_avg_daily_last_30_days_debit = 6 [json_name = "countAvgDailyLast30DaysDebit"];
    double count_avg_daily_last_90_days_debit = 7 [json_name = "countAvgDailyLast90DaysDebit"];
    double ratio_of_count_avg_daily_30_to_90_days_debit = 8 [json_name = "ratioOfCountAvgDaily30To90DaysDebit"];
    double ratio_of_total_debit_txn_last_30_to_60_days = 9 [json_name = "ratioOfTotalDebitTxnLast30To60Days"];
    double amount_per_transaction_last_180_days_debit = 10 [json_name = "amountPerTransactionLast180DaysDebit"];
    double ratio_of_total_credit_txn_last_30_to_60_days = 11 [json_name = "ratioOfTotalCreditTxnLast30to60Days"];
    double ratio_of_total_credit_txn_last_30_to_90_days = 12 [json_name = "ratioOfTotalCreditTxnLast30To90Days"];
    double credit_amount_last_90_days = 13 [json_name = "creditAmountLast90Days"];
    double avg_eod_balance_last_90_days = 14 [json_name = "avgEodBalanceLast90Days"];
    double avg_eod_balance_last_180_days = 15 [json_name = "avgEodBalanceLast180Days"];
    double avg_eod_balance_180_to_270_days = 16 [json_name = "avgEodBalance180to270Days"];
    double avg_eod_balance_270_to_365_days = 17 [json_name = "avgEodBalance270to365Days"];
    double ratio_of_avg_eod_balance_0_to_90_vs_90_to_180 = 18 [json_name = "ratioOfAvgEodBalance0to90vs90to180"];
    double min_eod_balance_last_90_days = 19 [json_name = "minEodBalanceLast90Days"];
    map<string, AnalysisResponse.AccountWiseAnalysis.MonthlyAnalytics> monthly_analytics = 20 [json_name = "monthlyAnalytics"]; // key: year-month
    map<string, AnalysisResponse.AccountWiseAnalysis.DailyAnalytics> daily_analytics = 21 [json_name = "dailyAnalytics"]; // key: date
    double minimum_balance = 22 [json_name = "minimumBalance"];
    double maximum_balance = 23 [json_name = "maximumBalance"];
    double total_credit_amount = 24 [json_name = "totalCreditAmount"];
    double total_debit_amount = 25 [json_name = "totalDebitAmount"];
    int32 total_credit_txn = 26 [json_name = "totalCreditTxn"];
    int32 total_debit_txn = 27 [json_name = "totalDebitTxn"];
    double minimum_credit_amount = 28 [json_name = "minimumCreditAmount"];
    double maximum_credit_amount = 29 [json_name = "maximumCreditAmount"];
    double minimum_debit_amount = 30 [json_name = "minimumDebitAmount"];
    double maximum_debit_amount = 31 [json_name = "maximumDebitAmount"];
    double closing_balance = 32 [json_name = "closingBalance"];
    double credit_debit_ratio = 33 [json_name = "creditDebitRatio"];
    double surplus_amount = 34 [json_name = "surplusAmount"];
    double total_credit_debit_amount = 35 [json_name = "totalCreditDebitAmount"];
    double median_eod_balance = 36 [json_name = "medianEodBalance"];
    double avg_eod_balance = 37 [json_name = "avgEodBalance"];
    double variance_of_balance = 38 [json_name = "varianceOfBalance"];
    double std_deviation_of_balance = 39 [json_name = "stdDeviationOfBalance"];
    double avg_credit_amount = 40 [json_name = "avgCreditAmount"];
    double avg_monthly_credit_amount = 41 [json_name = "avgMonthlyCreditAmount"];
    double variance_of_credit_amount_mom = 42 [json_name = "varianceOfCreditAmountMOM"];
    double std_deviation_of_credit_amount_mom = 43 [json_name = "stdDeviationOfCreditAmountMOM"];
    double avg_debit_amount = 44 [json_name = "avgDebitAmount"];
    double avg_monthly_debit_amount = 45 [json_name = "avgMonthlyDebitAmount"];
    double variance_of_debit_amount_mom = 46 [json_name = "varianceOfDebitAmountMOM"];
    double std_deviation_of_debit_amount_mom = 47 [json_name = "stdDeviationOfDebitAmountMOM"];
    double velocity = 48 [json_name = "velocity"];
    int32 total_credit_txn_last_30_days = 49 [json_name = "totalCreditTxnLast30Days"];
    int32 total_credit_txn_last_60_days = 50 [json_name = "totalCreditTxnLast60Days"];
    double avg_eod_balance_90_to_180_days = 51 [json_name = "avgEodBalance90to180Days"];
    int32 total_loan_credit_txns = 52 [json_name = "totalLoanCreditTxns"];
    int32 total_loan_credit_txns_last_90_days = 53 [json_name = "totalLoanCreditTxnsLast90Days"];
    double total_loan_credit_amount = 54 [json_name = "totalLoanCreditAmount"];
    double total_loan_credit_amount_last_90_days = 55 [json_name = "totalLoanCreditAmountLast90Days"];
    double total_loan_credit_amount_last_180_days = 56 [json_name = "totalLoanCreditAmountLast180Days"];
    int32 total_loan_credit_txns_last_180_days = 57 [json_name = "totalLoanCreditTxnsLast180Days"];
    double max_amount_last_90_days_without_loan_credit = 58 [json_name = "maxAmountLast90DaysWithoutLoanCredit"];
    int32 total_cash_deposit_txn = 59 [json_name = "totalCashDepositTxn"];
    double total_cash_deposit_amount = 60 [json_name = "totalCashDepositAmount"];
    int32 total_cash_withdrawals_txn = 61 [json_name = "totalCashWithdrawalsTxn"];
    double total_cash_withdrawals_amount = 62 [json_name = "totalCashWithdrawalsAmount"];
    double minimum_eod_balance = 63 [json_name = "minimumEodBalance"];
    double maximum_eod_balance = 64 [json_name = "maximumEodBalance"];
    double average_surplus_amount = 65 [json_name = "averageSurplusAmount"];
    double max_eod_balance_last_30_days = 66 [json_name = "maxEodBalanceLast30Days"];
    int32 total_days_balance_less_than_10rs = 67 [json_name = "totalDaysBalanceLessThan10rs"];
    int32 total_days_balance_less_than_100rs = 68 [json_name = "totalDaysBalanceLessThan100rs"];
    int32 last_90_days_balance_less_than_100rs = 69 [json_name = "last90DaysBalanceLessThan100rs"];
    int32 last_30_days_balance_less_than_1000rs = 70 [json_name = "last30DaysBalanceLessThan1000rs"];
    double avg_cash_withdrawal_amount = 71 [json_name = "avgCashWithdrawalAmount"];
    double avg_cash_withdrawal_amount_last_60_days = 72 [json_name = "avgCashWithdrawalAmountLast60Days"];
    double avg_cash_withdrawal_amount_last_90_days = 73 [json_name = "avgCashWithdrawalAmountLast90Days"];
    double total_withdrawals_amount_last_90_days = 74 [json_name = "totalWithdrawalsAmountLast90Days"];
    double ratio_count_wallet_debit_transaction_last_30_to_60_days = 75 [json_name = "ratioCountWalletDebitTransactionLast30To60Days"];
    double ratio_count_wallet_debit_transaction_last_30_to_lifetime = 76 [json_name = "ratioCountWalletDebitTransactionLast30ToLifetime"];
    double amount_without_loan_credit_last_90_to_180_days = 77 [json_name = "amountWithOutLoanCreditLast90To180Days"];
    double amount_without_loan_credit_last_180_to_270_days = 78 [json_name = "amountWithOutLoanCreditLast180To270Days"];
    double amount_without_loan_credit_last_270_to_365_days = 79 [json_name = "amountWithOutLoanCreditLast270To365Days"];
    int32 total_chq_deposit_txns = 80 [json_name = "totalChqDepositTxns"];
    double total_chq_deposit_amount = 81 [json_name = "totalChqDepositAmount"];
    int32 total_chq_issue_txns = 82 [json_name = "totalChqIssueTxns"];
    double total_chq_issue_amount = 83 [json_name = "totalChqIssueAmount"];
    int32 total_emi_txns = 84 [json_name = "totalEmiTxns"];
    double total_emi_amount = 85 [json_name = "totalEmiAmount"];
    int32 total_salary_txn_last_90_days = 86 [json_name = "totalSalaryTxnLast90Days"];
    double total_salary_amount_last_90_days = 87 [json_name = "totalSalaryAmountLast90Days"];
    int32 total_salary_paid_txns = 88 [json_name = "totalSalaryPaidTxns"];
    double total_salary_paid_amount = 89 [json_name = "totalSalaryPaidAmount"];
    double ratio_count_all_wallet_debit_transaction_last_30_to_60_days = 90 [json_name = "ratioCountAllWalletDebitTransactionLast30To60Days"];
    double ratio_count_all_wallet_debit_transaction_last_30_to_lifetime = 91 [json_name = "ratioCountAllWalletDebitTransactionLast30ToLifetime"];
    double total_salary_amount = 92 [json_name = "totalSalaryAmount"];
    double average_salary_amount = 93 [json_name = "averageSalaryAmount"];
    int32 total_salary_txns = 94 [json_name = "totalSalaryTxns"];
    int32 total_credit_txn_last_180_days = 95 [json_name = "totalCreditTxnLast180days"];
    double total_credit_amt_last_180_days = 96 [json_name = "totalCreditAmtLast180Days"];
    double total_debit_amt_last_90_days = 97 [json_name = "totalDebitAmtLast90Days"];
    int32 total_debit_txn_last_180_days = 98 [json_name = "totalDebitTxnLast180days"];
    double total_debit_amt_last_180_days = 99 [json_name = "totalDebitAmtLast180Days"];
    int32 total_credit_txn_last_3_months = 100 [json_name = "totalCreditTxnLast3Months"];
    double total_credit_amount_last_3_months = 101 [json_name = "totalCreditAmountLast3Months"];
    int32 total_credit_txn_last_6_months = 102 [json_name = "totalCreditTxnLast6Months"];
    double total_credit_amount_last_6_months = 103 [json_name = "totalCreditAmountLast6Months"];
    int32 total_debit_txn_last_3_months = 104 [json_name = "totalDebitTxnLast3Months"];
    int32 total_debit_txn_last_6_months = 105 [json_name = "totalDebitTxnLast6Months"];
    double total_debit_amount_last_3_months = 106 [json_name = "totalDebitAmountLast3Months"];
    double total_debit_amount_last_6_months = 107 [json_name = "totalDebitAmountLast6Months"];
    int32 total_loan_credit_txns_last_3_months = 108 [json_name = "totalLoanCreditTxnsLast3Months"];
    double total_loan_credit_amount_last_3_months = 109 [json_name = "totalLoanCreditAmountLast3Months"];
    int32 total_loan_credit_txns_last_6_months = 110 [json_name = "totalLoanCreditTxnsLast6Months"];
    double total_loan_credit_amount_last_6_months = 111 [json_name = "totalLoanCreditAmountLast6Months"];
    double average_monthly_credit_debit_amount = 112 [json_name = "averageMonthlyCreditDebitAmount"];
    double total_expense_amount = 113 [json_name = "totalExpenseAmount"];
    double average_expense_amount = 114 [json_name = "averageExpenseAmount"];
    int32 total_no_of_expense_transactions = 115 [json_name = "totalNoOfExpenseTransactions"];
    int32 total_no_of_net_credit_transactions = 116 [json_name = "totalNoofNetCreditTransactions"];
    double total_net_credit_transaction_amount = 117 [json_name = "totalNetCreditTransactionAmount"];
    double average_net_credit_transaction_amount = 118 [json_name = "averageNetCreditTransactionAmount"];
    int32 total_no_of_net_debit_transactions = 119 [json_name = "totalNoofNetDebitTransactions"];
    double total_net_debit_transaction_amount = 120 [json_name = "totalNetDebitTransactionAmount"];
    double average_net_debit_transaction_amount = 121 [json_name = "averageNetDebitTransactionAmount"];
    int32 total_transactions = 122 [json_name = "totalTransactions"];
    double volatility_of_balance = 123 [json_name = "volatilityOfBalance"];
    double volatility_of_credit_amount = 124 [json_name = "volatilityOfCreditAmount"];
    double volatility_of_debit_amount = 125 [json_name = "volatilityOfDebitAmount"];
    map<string, AnalysisResponse.AccountWiseAnalysis.WeeklyAnalytics> weekly_analytics = 126 [json_name = "weeklyAnalytics"]; // key: year-month-week
    CustomParameterizedVariables custom_parameterized_variables = 127 [json_name = "customParameterizedVariables"];

    message CustomParameterizedVariables {
      double median_amount_salary_last_180_days = 1 [json_name = "medianAmountSalaryLast180Days"];
    }
  }

  message FrequentCredits {
    AnalysisResponse.FrequentCredits.OtherIncomeAnalysis other_income_analysis = 1 [json_name = "otherIncomeAnalysis"];
    SalaryAnalysis salary_analysis = 2 [json_name = "salaryAnalysis"];
    NonSalaryIncomeAnalysis non_salary_income_analysis = 3 [json_name = "nonSalaryIncomeAnalysis"];
    map<string, RecurringIncome> recurring_incomes = 4 [json_name = "recurringIncomes"]; // key: counterParty
    double avg_frequent_credit_amount = 5 [json_name = "avgFrequentCreditAmount"];

    message SalaryAnalysis {
      AnalysisResponse.FrequentCredits.SalaryAnalysis.Analysis overall = 1 [json_name = "overall"];
      map<string, AnalysisResponse.FrequentCredits.SalaryAnalysis.Analysis> account_wise = 2 [json_name = "accountWise"]; // key: linkedAccRef
    }

    message NonSalaryIncomeAnalysis {
      AnalysisResponse.FrequentCredits.NonSalaryIncomeAnalysis.Analysis overall = 1 [json_name = "overall"];
      repeated AnalysisResponse.FrequentCredits.NonSalaryIncomeAnalysis.Analysis account_wise = 2 [json_name = "accountWise", deprecated=true]; // keeping this as list due to lack of data
      map<string, AnalysisResponse.FrequentCredits.NonSalaryIncomeAnalysis.Analysis> account_wise_map = 3 [json_name = "accountWiseMap"]; // key: linkedAccRef
    }

    message RecurringIncome {
      map<string, AnalysisResponse.FrequentCredits.RecurringIncome.Transaction> transactions = 1 [json_name = "transactions"]; // key: uniqueId
      double amount = 2 [json_name = "amount"];
      string counter_party = 3 [json_name = "counterParty"];
      string category = 4 [json_name = "category"];
      string merchant_name = 5 [json_name = "merchantName"];
      string account = 6 [json_name = "account"];
      string linked_acc_ref = 7 [json_name = "linkedAccRef"];
      string payment_mode_type = 8 [json_name = "paymentModeType"];
      double avg_amount = 9 [json_name = "avgAmount"];
      string upcoming_date = 10 [json_name = "upcomingDate"];
      bool is_active = 11 [json_name = "isActive"];
      bool salary_filtered = 12 [json_name = "salaryFiltered"];
      string recurrence = 13 [json_name = "recurrence"];
    }
  }

  message FrequentDebits {
    EmiAnalysis emi_analysis = 1 [json_name = "emiAnalysis"];
    NonEmiObligationAnalysis non_emi_obligation_analysis = 2 [json_name = "nonEmiObligationAnalysis"];
    map<string, RecurringDebit> recurring_debits = 3 [json_name = "recurringDebits"]; // key: counterParty
    double avg_frequent_debit_amount = 4 [json_name = "avgFrequentDebitAmount"];

    message EmiAnalysis {
      AnalysisResponse.FrequentDebits.Analysis over_all = 1 [json_name = "overAll"];
      map<string, AnalysisResponse.FrequentDebits.Analysis> account_wise = 2 [json_name = "accountWise"]; // key: linkedAccRef
    }
    message NonEmiObligationAnalysis {
      AnalysisResponse.FrequentDebits.Analysis over_all = 1 [json_name = "overAll"];
      map<string, AnalysisResponse.FrequentDebits.Analysis> account_wise = 2 [json_name = "accountWise"]; // key: linkedAccRef
    }
    message RecurringDebit {
      map<string, AnalysisResponse.FrequentDebits.RecurringDebit.Transaction> transactions = 1 [json_name = "transactions"]; // key: uniqueId
      double amount = 2 [json_name = "amount"];
      string counter_party = 3 [json_name = "counterParty"];
      string category = 4 [json_name = "category"];
      string merchant_name = 5 [json_name = "merchantName"];
      string account = 6 [json_name = "account"];
      string linked_acc_ref = 7 [json_name = "linkedAccRef"];
      string payment_mode_type = 8 [json_name = "paymentModeType"];
      double avg_amount = 9 [json_name = "avgAmount"];
      string upcoming_date = 10 [json_name = "upcomingDate"];
      bool is_active = 11 [json_name = "isActive"];
      bool salary_filtered = 12 [json_name = "salaryFiltered"];
      string recurrence = 13 [json_name = "recurrence"];
    }
  }

  message LoanCreditsAnalysis {
    AnalysisResponse.LoanCreditsAnalysis.LoanCreditAnalytics overall_loan_credit_analysis = 1 [json_name = "overallLoanCreditAnalysis"];
    map<string, AnalysisResponse.LoanCreditsAnalysis.LoanCreditAnalytics> account_wise_loan_analytics = 2 [json_name = "accountWiseLoanAnalytics"]; // key: linkedAccRef
    map<string, AnalysisResponse.LoanCreditsAnalysis.LoanCreditTransaction> loan_credit_txns = 3 [json_name = "loanCreditTxns"]; // key: uniqueId
  }

  message InvestmentAnalysis {
    Overall overall = 1 [json_name = "overall"];
    map<string, SubCategory> investment_sub_category_analysis = 2 [json_name = "investmentSubCategoryAnalysis"];
    map<string, Investment> investments = 3 [json_name = "investments"]; // key: investmentType

    message Overall {
      map<string, AnalysisResponse.InvestmentAnalysis.InvestmentAnalysisStruct> account_wise_investment_analysis = 1 [json_name = "accountWiseInvestmentAnalysis"]; // key: linkedAccRef
      AnalysisResponse.InvestmentAnalysis.InvestmentAnalysisStruct overall_investment_analysis = 2 [json_name = "overallInvestmentAnalysis"];
    }
    message SubCategory {
      map<string, AnalysisResponse.InvestmentAnalysis.InvestmentAnalysisStruct> account_wise_investment_analysis = 1 [json_name = "accountWiseInvestmentAnalysis"]; // key: linkedAccRef
      AnalysisResponse.InvestmentAnalysis.InvestmentAnalysisStruct overall_investment_analysis = 2 [json_name = "overallInvestmentAnalysis"];
    }

    message Investment {
      string investment_type = 1 [json_name = "investmentType"];
      string account = 2 [json_name = "account"];
      string linked_acc_ref = 3 [json_name = "linkedAccRef"];
      string bank = 4 [json_name = "bank"];
      map<string, AnalysisResponse.InvestmentAnalysis.Investment.InvestmentTransaction> transactions = 5 [json_name = "transactions"]; // key: uniqueId
    }
  }

  message PaymentTypeAnalytics {
    map<string, TransactionDetailsList> monthly_payment_type_analytics = 1 [json_name = "monthlyPaymentTypeAnalytics"];
    map<string, AnalysisResponse.PaymentTypeAnalytics.PaymentTypeData> payment_type_analytics = 2 [json_name = "paymentTypeAnalytics"];

    message TransactionDetailsList {
      map<string, AnalysisResponse.PaymentTypeAnalytics.TransactionDetailsList.TransactionDetails> transactions = 1; // key: year-month
    }
  }

  message CompleteCategoryWiseAnalysis {
    map<string, AnalysisResponse.CompleteCategoryWiseAnalysis.CategoryAnalysis> overall_category_wise_analysis = 1 [json_name = "overallCategoryWiseAnalysis"];
    map<string, MonthlyCategoryWiseAnalysis> monthly_category_wise_analysis = 2 [json_name = "monthlyCategoryWiseAnalysis"];

    message MonthlyCategoryWiseAnalysis {
      map<string, AnalysisResponse.CompleteCategoryWiseAnalysis.MonthlyCategoryWiseAnalysis.MonthlyCategoryAnalysis> category = 1;  // key: year-month
    }
  }

  message BounceAnalysis {
    AccountWiseBounceAnalytics accountWiseBounceAnalytics = 1 [json_name = "accountWiseBounceAnalytics"];
    OverAllBounceAnalytics over_all_bounce_analytics = 2 [json_name = "overAllBounceAnalytics"];

    message AccountWiseBounceAnalytics {
      map<string, AnalyticsItem> account_wise_bounce_analytics = 1; // key: linkedAccRef

      message AnalyticsItem {
        string linked_acc_ref = 1 [json_name = "linkedAccRef"];
        string bank = 2 [json_name = "bank"];
        string account_number = 3 [json_name = "accountNumber"];
        int32 total_reversal_txns = 4 [json_name = "totalReversalTxns"];
        double total_reversal_amount = 5 [json_name = "totalReversalAmount"];
        int32 total_iw_chq_bounce_charge_txns = 6 [json_name = "totalIWChqBounceChargeTxns"];
        double total_iw_chq_bounce_charge_amount = 7 [json_name = "totalIWChqBounceChargeAmount"];
        int32 total_iw_chq_bounce_txns = 8 [json_name = "totalIWChqBounceTxns"];
        double total_iw_chq_bounce_amount = 9 [json_name = "totalIWChqBounceAmount"];
        int32 total_ow_chq_bounce_charge_txns = 10 [json_name = "totalOWChqBounceChargeTxns"];
        double total_ow_chq_bounce_charge_amount = 11 [json_name = "totalOWChqBounceChargeAmount"];
        int32 total_ow_chq_bounce_txns = 12 [json_name = "totalOWChqBounceTxns"];
        double total_ow_chq_bounce_amount = 13 [json_name = "totalOWChqBounceAmount"];
        int32 total_tech_bounce_txn = 14 [json_name = "totalTechBounceTxn"];
        double total_tech_bounce_amt = 15 [json_name = "totalTechBounceAmt"];
        int32 total_non_tech_bounce_txn = 16 [json_name = "totalNonTechBounceTxn"];
        double total_non_tech_bounce_amt = 17 [json_name = "totalNonTechBounceAmt"];
        int32 total_emi_bounce_txn = 18 [json_name = "totalEmiBounceTxn"];
        double total_emi_bounce_amt = 19 [json_name = "totalEmiBounceAmt"];
        int32 total_iw_non_chq_bounce_charge_txns = 20 [json_name = "totalIWNonChqBounceChargeTxns"];
        double total_iw_non_chq_bounce_charge_amount = 21 [json_name = "totalIWNonChqBounceChargeAmount"];
        int32 total_iw_non_chq_bounce_txns = 22 [json_name = "totalIWNonChqBounceTxns"];
        double total_iw_non_chq_bounce_amount = 23 [json_name = "totalIWNonChqBounceAmount"];
        int32 total_ecs_bounce_charge_txns = 24 [json_name = "totalEcsBounceChargeTxns"];
        double total_ecs_bounce_charge_amount = 25 [json_name = "totalEcsBounceChargeAmount"];
        int32 total_standing_instruction_bounce_charge_txns = 26 [json_name = "totalStandingInstructionBounceChargeTxns"];
        double total_standing_instruction_bounce_charge_amount = 27 [json_name = "totalStandingInstructionBounceChargeAmount"];
        int32 total_ow_non_chq_bounce_charge_txns = 28 [json_name = "totalOWNonChqBounceChargeTxns"];
        double total_ow_non_chq_bounce_charge_amount = 29 [json_name = "totalOWNonChqBounceChargeAmount"];
        int32 total_ow_non_chq_bounce_txns = 30 [json_name = "totalOWNonChqBounceTxns"];
        double total_ow_non_chq_bounce_amount = 31 [json_name = "totalOWNonChqBounceAmount"];
        int32 total_iw_bounce_txn_last_30_days = 32 [json_name = "totalIWBounceTxnLast30Days"];
        int32 total_ow_bounce_txn_last_30_days = 33 [json_name = "totalOWBounceTxnLast30Days"];
        int32 total_iw_bounce_charge_txns_last_30_days = 34 [json_name = "totalIWBounceChargeTxnsLast30Days"];
        int32 total_ow_bounce_charge_txns_last_30_days = 35 [json_name = "totalOWBounceChargeTxnsLast30Days"];
        int32 total_iw_bounce_txn_last_90_days = 36 [json_name = "totalIWBounceTxnLast90Days"];
        int32 total_ow_bounce_txn_last_90_days = 37 [json_name = "totalOWBounceTxnLast90Days"];
        int32 total_iw_bounce_charge_txns_last_90_days = 38 [json_name = "totalIWBounceChargeTxnsLast90Days"];
        int32 total_ow_bounce_charge_txns_last_90_days = 39 [json_name = "totalOWBounceChargeTxnsLast90Days"];
        int32 total_iw_bounce_txn_last_180_days = 40 [json_name = "totalIWBounceTxnLast180Days"];
        int32 total_ow_bounce_txn_last_180_days = 41 [json_name = "totalOWBounceTxnLast180Days"];
        int32 total_iw_bounce_charge_txns_last_180_days = 42 [json_name = "totalIWBounceChargeTxnsLast180Days"];
        int32 total_ow_bounce_charge_txns_last_180_days = 43 [json_name = "totalOWBounceChargeTxnsLast180Days"];
        map<string, AnalysisResponse.BounceAnalysis.BouncedTransaction> bounced_transactions = 44 [json_name = "bouncedTransactions"]; // key: uniqueId
        int32 total_inward_non_tech_cheque_bounce_txns = 45 [json_name = "totalInwardNonTechChequeBounceTxns"];
        double total_inward_non_tech_cheque_bounce_amount = 46 [json_name = "totalInwardNonTechChequeBounceAmount"];
        map<string, MonthlyBounceAnalysis> monthly_bounce_analysis = 47 [json_name = "monthlyBounceAnalysis"]; // key: year-month
        bool bounce_detected = 48 [json_name = "bounceDetected"];
        bool reversal_detected = 49 [json_name = "reversalDetected"];
        bool cheque_bounce_detected = 50 [json_name = "chequeBounceDetected"];
        int32 total_bounce_charge_txns = 51 [json_name = "totalBounceChargeTxns"];
        double total_bounce_charge_amount = 52 [json_name = "totalBounceChargeAmount"];
        int32 total_bounce_txns = 53 [json_name = "totalBounceTxns"];
        double total_bounce_amount = 54 [json_name = "totalBounceAmount"];
        int32 total_chq_bounce_txns = 55 [json_name = "totalChqBounceTxns"];
        double total_chq_bounce_amount = 56 [json_name = "totalChqBounceAmount"];
        int32 total_iw_bounce_charge_txns = 57 [json_name = "totalIWBounceChargeTxns"];
        double total_iw_bounce_charge_amount = 58 [json_name = "totalIWBounceChargeAmount"];
        int32 total_iw_bounce_txns = 59 [json_name = "totalIWBounceTxns"];
        double total_iw_bounce_amount = 60 [json_name = "totalIWBounceAmount"];
        int32 total_ow_bounce_charge_txns = 61 [json_name = "totalOWBounceChargeTxns"];
        double total_ow_bounce_charge_amount = 62 [json_name = "totalOWBounceChargeAmount"];
        int32 total_ow_bounce_txns = 63 [json_name = "totalOWBounceTxns"];
        double total_ow_bounce_amount = 64 [json_name = "totalOWBounceAmount"];
      }
    }

    message OverAllBounceAnalytics {
      double total_reversal_txns = 1 [json_name = "totalReversalTxns"];
      double total_reversal_amount = 2 [json_name = "totalReversalAmount"];
      double total_iw_chq_bounce_charge_txns = 3 [json_name = "totalIWChqBounceChargeTxns"];
      double total_iw_chq_bounce_charge_amount = 4 [json_name = "totalIWChqBounceChargeAmount"];
      double total_iw_chq_bounce_txns = 5 [json_name = "totalIWChqBounceTxns"];
      double total_iw_chq_bounce_amount = 6 [json_name = "totalIWChqBounceAmount"];
      double total_ow_chq_bounce_charge_txns = 7 [json_name = "totalOWChqBounceChargeTxns"];
      double total_ow_chq_bounce_charge_amount = 8 [json_name = "totalOWChqBounceChargeAmount"];
      double total_ow_chq_bounce_txns = 9 [json_name = "totalOWChqBounceTxns"];
      double total_ow_chq_bounce_amount = 10 [json_name = "totalOWChqBounceAmount"];
      double total_tech_bounce_txn = 11 [json_name = "totalTechBounceTxn"];
      double total_tech_bounce_amt = 12 [json_name = "totalTechBounceAmt"];
      double total_non_tech_bounce_txn = 13 [json_name = "totalNonTechBounceTxn"];
      double total_non_tech_bounce_amt = 14 [json_name = "totalNonTechBounceAmt"];
      double total_emi_bounce_txn = 15 [json_name = "totalEmiBounceTxn"];
      double total_emi_bounce_amt = 16 [json_name = "totalEmiBounceAmt"];
      double total_iw_non_chq_bounce_charge_txns = 17 [json_name = "totalIWNonChqBounceChargeTxns"];
      double total_iw_non_chq_bounce_charge_amount = 18 [json_name = "totalIWNonChqBounceChargeAmount"];
      double total_iw_non_chq_bounce_txns = 19 [json_name = "totalIWNonChqBounceTxns"];
      double total_iw_non_chq_bounce_amount = 20 [json_name = "totalIWNonChqBounceAmount"];
      double total_ecs_bounce_charge_txns = 21 [json_name = "totalEcsBounceChargeTxns"];
      double total_ecs_bounce_charge_amount = 22 [json_name = "totalEcsBounceChargeAmount"];
      double total_standing_instruction_bounce_charge_txns = 23 [json_name = "totalStandingInstructionBounceChargeTxns"];
      double total_standing_instruction_bounce_charge_amount = 24 [json_name = "totalStandingInstructionBounceChargeAmount"];
      double total_ow_non_chq_bounce_charge_txns = 25 [json_name = "totalOWNonChqBounceChargeTxns"];
      double total_ow_non_chq_bounce_charge_amount = 26 [json_name = "totalOWNonChqBounceChargeAmount"];
      double total_ow_non_chq_bounce_txns = 27 [json_name = "totalOWNonChqBounceTxns"];
      double total_ow_non_chq_bounce_amount = 28 [json_name = "totalOWNonChqBounceAmount"];
      double total_iw_bounce_txn_last_30_days = 29 [json_name = "totalIWBounceTxnLast30Days"];
      double total_ow_bounce_txn_last_30_days = 30 [json_name = "totalOWBounceTxnLast30Days"];
      double total_iw_bounce_charge_txns_last_30_days = 31 [json_name = "totalIWBounceChargeTxnsLast30Days"];
      double total_ow_bounce_charge_txns_last_30_days = 32 [json_name = "totalOWBounceChargeTxnsLast30Days"];
      double total_iw_bounce_txn_last_90_days = 33 [json_name = "totalIWBounceTxnLast90Days"];
      double total_ow_bounce_txn_last_90_days = 34 [json_name = "totalOWBounceTxnLast90Days"];
      double total_iw_bounce_charge_txns_last_90_days = 35 [json_name = "totalIWBounceChargeTxnsLast90Days"];
      double total_ow_bounce_charge_txns_last_90_days = 36 [json_name = "totalOWBounceChargeTxnsLast90Days"];
      double total_iw_bounce_txn_last_180_days = 37 [json_name = "totalIWBounceTxnLast180Days"];
      double total_ow_bounce_txn_last_180_days = 38 [json_name = "totalOWBounceTxnLast180Days"];
      double total_iw_bounce_charge_txns_last_180_days = 39 [json_name = "totalIWBounceChargeTxnsLast180Days"];
      double total_ow_bounce_charge_txns_last_180_days = 40 [json_name = "totalOWBounceChargeTxnsLast180Days"];
      map<string, AnalysisResponse.BounceAnalysis.BouncedTransaction> bounced_transactions = 41 [json_name = "bouncedTransactions"]; // key: uniqueId
      double total_inward_non_tech_cheque_bounce_txns = 42 [json_name = "totalInwardNonTechChequeBounceTxns"];
      double total_inward_non_tech_cheque_bounce_amount = 43 [json_name = "totalInwardNonTechChequeBounceAmount"];
      map<string, MonthlyBounceAnalysis> monthly_bounce_analysis = 44 [json_name = "monthlyBounceAnalysis"]; // key: year-month
      bool bounce_detected = 45 [json_name = "bounceDetected"];
      bool reversal_detected = 46 [json_name = "reversalDetected"];
      bool cheque_bounce_detected = 47 [json_name = "chequeBounceDetected"];
      double total_bounce_charge_txns = 48 [json_name = "totalBounceChargeTxns"];
      double total_bounce_charge_amount = 49 [json_name = "totalBounceChargeAmount"];
      double total_bounce_txns = 50 [json_name = "totalBounceTxns"];
      double total_bounce_amount = 51 [json_name = "totalBounceAmount"];
      double total_chq_bounce_txns = 52 [json_name = "totalChqBounceTxns"];
      double total_chq_bounce_amount = 53 [json_name = "totalChqBounceAmount"];
      double total_iw_bounce_charge_txns = 54 [json_name = "totalIWBounceChargeTxns"];
      double total_iw_bounce_charge_amount = 55 [json_name = "totalIWBounceChargeAmount"];
      double total_iw_bounce_txns = 56 [json_name = "totalIWBounceTxns"];
      double total_iw_bounce_amount = 57 [json_name = "totalIWBounceAmount"];
      double total_ow_bounce_charge_txns = 58 [json_name = "totalOWBounceChargeTxns"];
      double total_ow_bounce_charge_amount = 59 [json_name = "totalOWBounceChargeAmount"];
      double total_ow_bounce_txns = 60 [json_name = "totalOWBounceTxns"];
      double total_ow_bounce_amount = 61 [json_name = "totalOWBounceAmount"];
    }

    message MonthlyBounceAnalysis {
      double total_reversal_txns = 1 [json_name = "totalReversalTxns"];
      double total_reversal_amount = 2 [json_name = "totalReversalAmount"];
      double total_iw_chq_bounce_charge_txns = 3 [json_name = "totalIWChqBounceChargeTxns"];
      double total_iw_chq_bounce_charge_amount = 4 [json_name = "totalIWChqBounceChargeAmount"];
      double total_iw_chq_bounce_txns = 5 [json_name = "totalIWChqBounceTxns"];
      double total_iw_chq_bounce_amount = 6 [json_name = "totalIWChqBounceAmount"];
      double total_ow_chq_bounce_charge_txns = 7 [json_name = "totalOWChqBounceChargeTxns"];
      double total_ow_chq_bounce_charge_amount = 8 [json_name = "totalOWChqBounceChargeAmount"];
      double total_ow_chq_bounce_txns = 9 [json_name = "totalOWChqBounceTxns"];
      double total_ow_chq_bounce_amount = 10 [json_name = "totalOWChqBounceAmount"];
      double total_tech_bounce_txn = 11 [json_name = "totalTechBounceTxn"];
      double total_tech_bounce_amt = 12 [json_name = "totalTechBounceAmt"];
      double total_non_tech_bounce_txn = 13 [json_name = "totalNonTechBounceTxn"];
      double total_non_tech_bounce_amt = 14 [json_name = "totalNonTechBounceAmt"];
      double total_emi_bounce_txn = 15 [json_name = "totalEmiBounceTxn"];
      double total_emi_bounce_amt = 16 [json_name = "totalEmiBounceAmt"];
      double total_iw_non_chq_bounce_charge_txns = 17 [json_name = "totalIWNonChqBounceChargeTxns"];
      double total_iw_non_chq_bounce_charge_amount = 18 [json_name = "totalIWNonChqBounceChargeAmount"];
      double total_iw_non_chq_bounce_txns = 19 [json_name = "totalIWNonChqBounceTxns"];
      double total_iw_non_chq_bounce_amount = 20 [json_name = "totalIWNonChqBounceAmount"];
      double total_ecs_bounce_charge_txns = 21 [json_name = "totalEcsBounceChargeTxns"];
      double total_ecs_bounce_charge_amount = 22 [json_name = "totalEcsBounceChargeAmount"];
      double total_standing_instruction_bounce_charge_txns = 23 [json_name = "totalStandingInstructionBounceChargeTxns"];
      double total_standing_instruction_bounce_charge_amount = 24 [json_name = "totalStandingInstructionBounceChargeAmount"];
      double total_ow_non_chq_bounce_charge_txns = 25 [json_name = "totalOWNonChqBounceChargeTxns"];
      double total_ow_non_chq_bounce_charge_amount = 26 [json_name = "totalOWNonChqBounceChargeAmount"];
      double total_ow_non_chq_bounce_txns = 27 [json_name = "totalOWNonChqBounceTxns"];
      double total_ow_non_chq_bounce_amount = 28 [json_name = "totalOWNonChqBounceAmount"];
      double total_iw_bounce_txn_last_30_days = 29 [json_name = "totalIWBounceTxnLast30Days"];
      double total_ow_bounce_txn_last_30_days = 30 [json_name = "totalOWBounceTxnLast30Days"];
      double total_iw_bounce_charge_txns_last_30_days = 31 [json_name = "totalIWBounceChargeTxnsLast30Days"];
      double total_ow_bounce_charge_txns_last_30_days = 32 [json_name = "totalOWBounceChargeTxnsLast30Days"];
      double total_iw_bounce_txn_last_90_days = 33 [json_name = "totalIWBounceTxnLast90Days"];
      double total_ow_bounce_txn_last_90_days = 34 [json_name = "totalOWBounceTxnLast90Days"];
      double total_iw_bounce_charge_txns_last_90_days = 35 [json_name = "totalIWBounceChargeTxnsLast90Days"];
      double total_ow_bounce_charge_txns_last_90_days = 36 [json_name = "totalOWBounceChargeTxnsLast90Days"];
      double total_iw_bounce_txn_last_180_days = 37 [json_name = "totalIWBounceTxnLast180Days"];
      double total_ow_bounce_txn_last_180_days = 38 [json_name = "totalOWBounceTxnLast180Days"];
      double total_iw_bounce_charge_txns_last_180_days = 39 [json_name = "totalIWBounceChargeTxnsLast180Days"];
      double total_ow_bounce_charge_txns_last_180_days = 40 [json_name = "totalOWBounceChargeTxnsLast180Days"];
      map<string, AnalysisResponse.BounceAnalysis.BouncedTransaction> bounced_transactions = 41 [json_name = "bouncedTransactions"]; // key: uniqueId
      double total_inward_non_tech_cheque_bounce_txns = 42 [json_name = "totalInwardNonTechChequeBounceTxns"];
      double total_inward_non_tech_cheque_bounce_amount = 43 [json_name = "totalInwardNonTechChequeBounceAmount"];
      bool bounce_detected = 44 [json_name = "bounceDetected"];
      bool reversal_detected = 45 [json_name = "reversalDetected"];
      bool cheque_bounce_detected = 46 [json_name = "chequeBounceDetected"];
      double total_bounce_charge_txns = 47 [json_name = "totalBounceChargeTxns"];
      double total_bounce_charge_amount = 48 [json_name = "totalBounceChargeAmount"];
      double total_bounce_txns = 49 [json_name = "totalBounceTxns"];
      double total_bounce_amount = 50 [json_name = "totalBounceAmount"];
      double total_chq_bounce_txns = 51 [json_name = "totalChqBounceTxns"];
      double total_chq_bounce_amount = 52 [json_name = "totalChqBounceAmount"];
      double total_iw_bounce_charge_txns = 53 [json_name = "totalIWBounceChargeTxns"];
      double total_iw_bounce_charge_amount = 54 [json_name = "totalIWBounceChargeAmount"];
      double total_iw_bounce_txns = 55 [json_name = "totalIWBounceTxns"];
      double total_iw_bounce_amount = 56 [json_name = "totalIWBounceAmount"];
      double total_ow_bounce_charge_txns = 57 [json_name = "totalOWBounceChargeTxns"];
      double total_ow_bounce_charge_amount = 58 [json_name = "totalOWBounceChargeAmount"];
      double total_ow_bounce_txns = 59 [json_name = "totalOWBounceTxns"];
      double total_ow_bounce_amount = 60 [json_name = "totalOWBounceAmount"];
      int32 year = 61 [json_name = "year"];
      int32 month = 62 [json_name = "month"];
    }
  }

  message Top5FundTransfer {
    map<string, TransactionDetail> overall = 1 [json_name = "overall"]; // key: category
    map<string, Monthly> monthly = 2 [json_name = "monthly"]; // key: monthYear

    message Monthly {
      string month_year = 1 [json_name = "monthYear"];
      map<string, TransactionDetail> txns = 2 [json_name = "txns"]; // key: category
    }

    message TransactionDetail {
      string category = 1 [json_name = "category"];
      int32 no_of_txn = 2 [json_name = "noOfTxn"];
      double amount = 3 [json_name = "amount"];
      bool recurring = 4 [json_name = "recurring"];
      map<string, bool> txn_ref_id_list = 5 [json_name = "txnRefIdList"]; // key as string, bool always true
    }
  }

  message AbbTables {
    AccountData overall = 1 [json_name = "overall"];
    map<string, AccountData> account_wise = 2 [json_name = "accountWise"]; // key: linkedAccRef

    message AccountData {
      string account_number = 1 [json_name = "accountNumber"];
      string linked_acc_ref = 2 [json_name = "linkedAccRef"];
      double average_of_monthly_averages = 3 [json_name = "averageOfMonthlyAverages"];
      map<string, AnalysisResponse.AbbTables.AccountData.MonthlyData> monthly_data = 4 [json_name = "monthlyData"]; // key: year-month
    }
  }

  message LoanCreditSummary {
    float total_loan_credit_amount = 1 [json_name = "totalLoanCreditAmount"];
    int32 total_loan_credit_txns = 2 [json_name = "totalLoanCreditTxns"];
    LoanCreditAnalytics overall_loan_credit_analysis = 3 [json_name = "overallLoanCreditAnalysis"];
    map<string, LoanCreditAnalytics> account_wise_loan_analytics = 4 [json_name = "accountWiseLoanAnalytics"]; // key: linkedAccRef

    message LoanCreditAnalytics {
      string bank = 1 [json_name = "bank"];
      string account_number = 2 [json_name = "accountNumber"];
      string linked_acc_ref = 3 [json_name = "linkedAccRef"];
      float total_loan_credit_amount = 4 [json_name = "totalLoanCreditAmount"];
      int32 total_loan_credit_txns = 5 [json_name = "totalLoanCreditTxns"];
      float avg_loan_credit_amount = 6 [json_name = "avgLoanCreditAmount"];
      float monthly_average_loan_creidt_amount = 7 [json_name = "monthlyAverageLoanCreidtAmount"];
      float monthly_average_loan_credit_amount = 8 [json_name = "monthlyAverageLoanCreditAmount"];
      int32 total_loan_credit_txn_last_90_days = 9 [json_name = "totalLoanCreditTxnLast90Days"];
      float total_loan_credit_amount_last_90_days = 10 [json_name = "totalLoanCreditAmountLast90Days"];
      int32 total_loan_credit_txns_last_3_months = 11 [json_name = "totalLoanCreditTxnsLast3Months"];
      int32 total_loan_credit_txns_last_6_months = 12 [json_name = "totalLoanCreditTxnsLast6Months"];
      float total_loan_credit_amount_last_3_months = 13 [json_name = "totalLoanCreditAmountLast3Months"];
      float total_loan_credit_amount_last_6_months = 14 [json_name = "totalLoanCreditAmountLast6Months"];
      float avg_monthly_loan_credit_last_3_months = 15 [json_name = "avgMonthlyLoanCreditLast3Months"];
      float avg_monthly_loan_credit_last_6_months = 16 [json_name = "avgMonthlyLoanCreditLast6Months"];
      map<string, AnalysisResponse.LoanCreditSummary.LoanCreditAnalytics.LoanCreditTxns> loan_credit_txns = 17 [json_name = "loanCreditTxns"]; // key: uniqueId
    }
  }

  message CompleteCategoryWiseAnalysisAllAnalysis {
    map<string, AnalysisResponse.CompleteCategoryWiseAnalysisAllAnalysis.CategoryAnalysisStruct> category_wise_analysis = 1 [json_name = "categoryWiseAnalysis"];
    map<string, MonthlyCategoryAnalysisStruct> monthly_category_wise_analysis = 2 [json_name = "monthlyCategoryWiseAnalysis"];

    message MonthlyCategoryAnalysisStruct {
      map<string, AnalysisResponse.CompleteCategoryWiseAnalysisAllAnalysis.CategoryAnalysisStruct> category_analysis = 1; // key: year-month
    }
  }

  message OverallEmiAnalytics {
    bool is_emi_identified = 1 [json_name = "isEmiIdentified"];
    int32 total_emi_txns = 2 [json_name = "totalEmiTxns"];
    float total_emi_amount = 3 [json_name = "totalEmiAmount"];
    float average_monthly_emi_amount = 4 [json_name = "averageMonthlyEmiAmount"];
    float total_emi_amount_last_90_days = 5 [json_name = "totalEmiAmountLast90Days"];
    int32 total_emi_txn_last_90_days = 6 [json_name = "totalEmiTxnLast90Days"];
    float total_emi_amount_last_60_days = 7 [json_name = "totalEmiAmountLast60Days"];
    int32 total_emi_txn_last_60_days = 8 [json_name = "totalEmiTxnLast60Days"];
    float total_emi_amount_last_30_days = 9 [json_name = "totalEmiAmountLast30Days"];
    int32 total_emi_txn_last_30_days = 10 [json_name = "totalEmiTxnLast30Days"];
    map<string, AnalysisResponse.OverallEmiAnalytics.Emi> emi_list = 11 [json_name = "emiList"]; // key: date
    map<string, EmiGroup> emi_groups = 12 [json_name = "emiGroups"]; // key: counterParty

    message EmiGroup {
      map<string, AnalysisResponse.OverallEmiAnalytics.EmiGroup.EmiTransaction> transactions = 1 [json_name = "transactions"]; // key: uniqueId
      float amount = 2 [json_name = "amount"];
      string counter_party = 3 [json_name = "counterParty"];
      string category = 4 [json_name = "category"];
      string merchant_name = 5 [json_name = "merchantName"];
      string account = 6 [json_name = "account"];
      string linked_acc_ref = 7 [json_name = "linkedAccRef"];
      string payment_mode_type = 8 [json_name = "paymentModeType"];
      float avg_amount = 9 [json_name = "avgAmount"];
      string upcoming_date = 10 [json_name = "upcomingDate"];
      bool is_active = 11 [json_name = "isActive"];
      bool salary_filtered = 12 [json_name = "salaryFiltered"];
      string recurrence = 13 [json_name = "recurrence"];
    }
  }

  message FixedDepositAnalysis {
    map<string, AnalysisResponse.FixedDepositAnalysis.FixedDepositAnalysisStruct> account_wise_analysis = 1 [json_name = "accountWiseAnalysis"]; // key: linkedRefNo
    AnalysisResponse.FixedDepositAnalysis.FixedDepositOverallAnalysis overall_analysis = 2 [json_name = "overallAnalysis"];
  }

  message RecurringDepositAnalysis {
    map<string, AnalysisResponse.RecurringDepositAnalysis.RecurringDeposit> account_wise_analysis = 1 [json_name = "accountWiseAnalysis"]; // key: linkedRefNo
    AnalysisResponse.RecurringDepositAnalysis.RecurringDepositOverallAnalysis overall_analysis = 2 [json_name = "overallAnalysis"];
  }

  message EquityAnalysis {
    AnalysisResponse.EquityAnalysis.EquityOverallAnalysis overall_analysis = 1 [json_name = "overallAnalysis"];
    map<string, AnalysisResponse.EquityAnalysis.StockWiseAnalysis> stock_wise_analysis = 2 [json_name = "stockWiseAnalysis"];  // key: isin
    map<string, AnalysisResponse.EquityAnalysis.MarketCapBasedAnalysis> market_cap_based_analysis = 3 [json_name = "marketCapBasedAnalysis"];
    map<string, AnalysisResponse.EquityAnalysis.SectorBasedAnalysis> sector_based_analysis = 4 [json_name = "sectorBasedAnalysis"];
  }

  message MutualFundAnalysis {
    AnalysisResponse.MutualFundAnalysis.MutualFundOverallAnalysis overall_analysis = 1 [json_name = "overallAnalysis"];
    map<string, MutualFundWiseAnalysis> mutual_fund_wise_analysis = 2 [json_name = "mutualFundWiseAnalysis"]; // key: isin
    map<string, AnalysisResponse.MutualFundAnalysis.MarketCapBasedAnalysis> market_cap_based_analysis = 3 [json_name = "marketCapBasedAnalysis"];

    message MutualFundWiseAnalysis {
      string isin = 1 [json_name = "isin"];
      string amc_name = 2 [json_name = "amcName"];
      string mutual_fund_name = 3 [json_name = "mutualFundName"];
      float total_invested_amount = 4 [json_name = "totalInvestedAmount"];
      float total_portfolio_value = 5 [json_name = "totalPortfolioValue"];
      float nav = 6 [json_name = "nav"];
      float profit_amount = 7 [json_name = "profitAmount"];
      float absolute_percentage_return = 8 [json_name = "absolutePercentageReturn"];
      float expense_ratio = 9 [json_name = "expenseRatio"];
      float cagr = 10 [json_name = "cagr"];
      float xirr = 11 [json_name = "xirr"];
      map<string, AnalysisResponse.MutualFundAnalysis.MutualFundWiseAnalysis.PortfolioDistributionAnalysis> portfolio_distribution_analysis = 12 [json_name = "portfolioDistributionAnalysis"]; // key: isin
      map<string, double> market_cap_based_stock_distribution_analysis = 13 [json_name = "marketCapBasedStockDistributionAnalysis"];
      map<string, double> sector_based_stock_distribution_analysis = 14 [json_name = "sectorBasedStockDistributionAnalysis"];
      map<string, double> historical_nav = 15 [json_name = "historicalNav"];
      string market_capitalization_type = 16 [json_name = "marketCapitalizationType"];
      string fund_type = 17 [json_name = "fundType"];
      string scheme_category = 18 [json_name = "schemeCategory"];
      string scheme_sub_category = 19 [json_name = "schemeSubCategory"];
    }
  }
}

// struct where the vendor data for aa analytics bank account details can be unmarshalled
message AaAnalysedBankAccountDetailsInSubAnalysis {
  SubAnalyticsResponse sub_analytics_response = 1 [json_name = "subAnalyticsResponse"];

  message SubAnalyticsResponse {
    repeated AccountProfiles account_profiles = 1 [json_name = "accountProfiles"];
  }

  message AccountProfiles {
    Account account = 1 [json_name = "Account"];
    Holders holders = 2 [json_name = "Holders"];
    Summary summary = 3 [json_name = "summary"];
  }

  message Account {
    string number = 1 [json_name = "number"];
    string acc_type = 2 [json_name = "acctype"];
    string bank_name = 3 [json_name = "bank"];
    string linked_acc_ref = 4 [json_name = "linkedAccRef"];
  }

  message Summary {
    string ifsc = 1 [json_name = "ifsc"];
  }
  message Holders {
    repeated Holder holder = 1 [json_name = "Holder"];

  }
  message Holder {
    string name = 1 [json_name = "name"];
  }
}

message LdcAaAnalyticsResponse {
  repeated AccountData account = 1 [json_name = "account"];
  bytes analytics = 2 [json_name = "analytics"];
}

message AccountData {
  AccountDetails data = 1 [json_name = "data"];
  string fip_id = 2 [json_name = "fipId"];
  string fi_type = 3 [json_name = "fiType"];
  string fip_name = 4 [json_name = "fipName"];
  string account_type = 5 [json_name = "accountType"];
  string reference_id = 6 [json_name = "referenceId"];
  string link_ref_number = 7 [json_name = "linkRefNumber"];
  string masked_account_number = 8 [json_name = "maskedAccountNumber"];
}

message AccountDetails {
  string type = 1 [json_name = "type"];
  string fip_id = 2 [json_name = "fipId"];
  optional string xmlns = 3 [json_name = "xmlns"];
  Profile profile = 4 [json_name = "profile"];
  Summary summary = 5 [json_name = "summary"];
  string fip_name = 6 [json_name = "fipName"];
  string version = 7 [json_name = "version"];
  optional string xmlns_xsi = 8 [json_name = "xmlnsXsi"];
  Transactions transactions = 9 [json_name = "transactions"];
  string linked_acc_ref = 10 [json_name = "linkedAccRef"];
  string schema_location = 11 [json_name = "schemaLocation"];
  string masked_acc_number = 12 [json_name = "maskedAccNumber"];
  optional string xsi_schema_location = 13 [json_name = "xsiSchemaLocation"];
}

message Profile {
  Holders holders = 1 [json_name = "holders"];
}

message Holders {
  string type = 1 [json_name = "type"];
  Holder holder = 2 [json_name = "holder"];
}

message Holder {
  string dob = 1 [json_name = "dob"];
  string pan = 2 [json_name = "pan"];
  string name = 3 [json_name = "name"];
  string email = 4 [json_name = "email"];
  string mobile = 5 [json_name = "mobile"];
  string address = 6 [json_name = "address"];
  string nominee = 7 [json_name = "nominee"];
  optional string landline = 8 [json_name = "landline"];
  string ckyc_compliance = 9 [json_name = "ckycCompliance"];
}

message Summary {
  string type = 1 [json_name = "type"];
  string branch = 2 [json_name = "branch"];
  string status = 3 [json_name = "status"];
  Pending pending = 4 [json_name = "pending"];
  string currency = 5 [json_name = "currency"];
  optional string facility = 6 [json_name = "facility"];
  string ifsc_code = 7 [json_name = "ifscCode"];
  string micr_code = 8 [json_name = "micrCode"];
  string exchge_rate = 9 [json_name = "exchgeRate"];
  string opening_date = 10 [json_name = "openingDate"];
  string drawing_limit = 11 [json_name = "drawingLimit"];
  string current_balance = 12 [json_name = "currentBalance"];
  string current_od_limit = 13 [json_name = "currentODLimit"];
  string balance_date_time = 14 [json_name = "balanceDateTime"];
}

message Pending {
  string amount = 1 [json_name = "amount"];
  optional string transaction_type = 2 [json_name = "transactionType"];
}

message Transactions {
  string end_date = 1 [json_name = "endDate"];
  string start_date = 2 [json_name = "startDate"];
  repeated Transaction transaction = 3 [json_name = "transaction"];
}

message Transaction {
  string mode = 1 [json_name = "mode"];
  string type = 2 [json_name = "type"];
  string txn_id = 3 [json_name = "txnId"];
  string amount = 4 [json_name = "amount"];
  string narration = 5 [json_name = "narration"];
  string reference = 6 [json_name = "reference"];
  string value_date = 7 [json_name = "valueDate"];
  string current_balance = 8 [json_name = "currentBalance"];
  string transaction_timestamp = 9 [json_name = "transactionTimestamp"];
}
