syntax = "proto3";

package vendors.aa.finvu;

option go_package = "github.com/epifi/gamma/api/vendors/aa/finvu";
option java_package = "com.github.epifi.gamma.api.vendors.aa.finvu";

message AccountLinkStatusBulkRequest {
  // The version of the API
  string ver = 1 [json_name = "ver"];
  // Creation timestamp of the message which will be updated at each leg
  string timestamp = 2 [json_name = "timestamp"];
  // The unique transaction identifier used for providing an end to end traceability.
  string txn_id = 3 [json_name = "txnid"];
  // customer id or vua
  string customer_vua = 4 [json_name = "customerAddress"];
  // accounts for linking
  repeated FipLinkedAccounts fip_linked_accounts = 5 [json_name = "Accounts"];
}

message FipLinkedAccounts {
  string fip_id = 1 [json_name = "fipId"];
  // link ref numbers under a single vua
  repeated string link_ref_numbers = 2 [json_name = "linkRefNumbers"];
}

message AccountLinkStatusBulkResponse {
  // The version of the API
  string ver = 1 [json_name = "ver"];
  // Creation timestamp of the message which will be updated at each leg
  string timestamp = 2 [json_name = "timestamp"];
  // The unique transaction identifier used for providing an end to end traceability.
  string txn_id = 3 [json_name = "txnid"];
  // customer address or vua
  string customer_vua = 4 [json_name = "customerAddress"];
  // account link statuses in bulk
  repeated AccountLinkStatusBulk account_link_status_bulk = 5 [json_name = "Accounts"];
  string error_code = 6 [json_name = "errorCode"];
  string error_message = 7 [json_name = "errorMsg"];
}

message AccountLinkStatusBulk {
  string fip_id = 1 [json_name = "fipId"];
  // link statuses under a single fip
  repeated FipLinkStatus fip_link_status = 2 [json_name = "LinkStatus"];
}

message FipLinkStatus {
  string link_ref_number = 1 [json_name = "linkRefNumber"];
  string account_link_status = 2 [json_name = "status"];
}

message GenerateFinvuJwtTokenRequest {
  // The version of the API
  string ver = 1 [json_name = "ver"];
  // Creation timestamp of the message which will be updated at each leg
  string timestamp = 2 [json_name = "timestamp"];
  // The unique transaction identifier used for providing an end to end traceability.
  string txn_id = 3 [json_name = "txnid"];
  // fiu id
  string entity_id = 4 [json_name = "entityId"];
  // pre shared key
  string entity_key = 5 [json_name = "entityKey"];
}

message GenerateFinvuJwtTokenResponse {
  // The version of the API
  string ver = 1 [json_name = "ver"];
  // Creation timestamp of the message which will be updated at each leg
  string timestamp = 2 [json_name = "timestamp"];
  // The unique transaction identifier used for providing an end to end traceability.
  string txn_id = 3 [json_name = "txnid"];
  // jwt token
  string token = 4 [json_name = "token"];
  string error_code = 6 [json_name = "errorCode"];
  string error_message = 7 [json_name = "errorMsg"];
}
