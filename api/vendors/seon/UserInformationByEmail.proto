// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

option go_package = "github.com/epifi/gamma/api/vendors/appscreener/seon";
option java_package = "com.github.epifi.gamma.api.vendors/appscreener/seon";

message GetUserSocialMediaInformationByEmailResponse {
  bool isSuccess = 1 [json_name = "success"];
  Data data = 2 [json_name = "data"];
}

message Data {
  string email = 1 [json_name = "email"];
  AccountDetails account_details = 2 [json_name = "account_details"];
}

message AccountDetails {
  LinkedIn linkedin = 1 [json_name = "linkedin"];
}

message LinkedIn {
  bool is_registered = 1 [json_name = "registered"];
  string url = 2 [json_name = "url"];
  string name = 3 [json_name = "name"];
  string company_name =4 [json_name = "company"];
  string title = 5 [json_name = "title"];
  string location = 6 [json_name = "location"];
  string website = 7 [json_name = "website"];
  string twitter = 8 [json_name = "twitter"];
  string photo = 9 [json_name = "photo"];
}

message GetUserSocialMediaInformationByEmailRequest {
  string email_id = 1 [json_name = "email_id"];
}
