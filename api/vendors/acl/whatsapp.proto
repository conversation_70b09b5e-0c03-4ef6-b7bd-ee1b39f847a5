syntax = "proto3";

package vendors.acl;

option go_package = "github.com/epifi/gamma/api/vendors/acl";
option java_package = "com.github.epifi.gamma.api.vendors.acl";

message SendWhatsAppMessageRequest {
  string response_type = 1 [json_name="responseType"];
  repeated WhatsAppMessage messages = 2[json_name="messages"];
}

message WhatsAppMessage {
  string sender = 1 [json_name="sender"];
  string to = 2 [json_name="to"];
  string channel = 3 [json_name="channel"];
  string type = 4 [json_name="type"];
  TextMessage text = 5 [json_name="text"];
  MediaMessage media = 6 [json_name="media"];
  TemplateMessage template = 7 [json_name="template"];
  MediaTemplateMessage media_template = 8 [json_name="mediaTemplate"];
}

message TextMessage {
  string content = 1[json_name="content"];
  string preview_first_url = 2[json_name="previewFirstUrl"];
}

message MediaMessage {
  string content_type = 1[json_name="contentType"];
  string media_url = 2[json_name="mediaUrl"];
  string caption = 3[json_name="caption"];
}

message TemplateMessage {
  string lang_code = 1 [json_name="langCode"];
  string template = 2 [json_name="templateId"];
  // Index of Dynamic variable to be passed in parameter(body) attributes should be in the same
  // sequence as in whitelisted template
  repeated DynamicVariable body = 3[json_name="body"];
}

message DynamicVariable {
  string type = 1 [json_name="type"];
  string text = 2 [json_name="text"];
}

message MediaTemplateMessage {
  string lang_code = 1 [json_name="langCode"];
  string media_url = 2 [json_name="mediaUrl"];
  string content_type = 3 [json_name="contentType"];
  string template = 4 [json_name="template"];
  map<string, string> parameters = 5 [json_name="parameters"];
  // File name for the media to be shown on the Whatsapp message to the user
  // This applicable based on the media type.
  // e.g., the name will not be shown for image type media whereas it will be shown for document type like PDF
  string media_file_name = 6 [json_name = "filename"];
}

message SendWhatsAppMessageResponse {
  string success = 1 [json_name="success"];
  string responseId = 2 [json_name="responseId"];
  repeated Description description = 3 [json_name="description"];
}

message Description {
  string error_code = 1 [json_name="errorCode"];
  string error_description = 2 [json_name="errorDescription"];
}

message OptInUserVendorRequest {
  string enterprise_id = 1[json_name="enterpriseId"];
  string phone_number = 2[json_name="msisdn"];
  string token = 3[json_name="token"];
}

message OptInUserVendorResponse {
  bool success = 1[json_name="success"];
  string status = 2[json_name="status"];
  int64 timestamp = 3[json_name="timeStamp"];
  string data = 4[json_name="data"];
  string message = 5[json_name="message"];
}
