syntax = "proto3";

package vendors.cibil;

option go_package = "github.com/epifi/gamma/api/vendors/cibil";
option java_package = "com.github.epifi.gamma.api.vendors.cibil";

message Failure {
  string ie_transaction_id = 1 [json_name = "IETransactionId"];
  string message = 2 [json_name = "Message"];
  string failure_enum = 3 [json_name = "FailureEnum"];
  string service_user_id = 4 [json_name = "ServiceUserId"];
  string btx_ref_key = 5 [json_name = "BtxRefKey"];
  string time_period = 6 [json_name = "TimePeriod"];
  string second_failure_sch_id = 7 [json_name = "SecondFailureSchId"];
  string severity = 8 [json_name = "Severity"];
  string failure_sch_id = 9 [json_name = "FailureSchId"];
  string customer_id = 10 [json_name = "CustomerId"];
  string client_user_key = 11 [json_name = "ClientUserKey"];
  string client_refresh_url = 12 [json_name = "ClientRefreshURL"];
}
