syntax = "proto3";

package vendors.uqudo;

option go_package = "github.com/epifi/gamma/api/vendors/uqudo";
option java_package = "com.github.epifi.gamma.api.vendors.uqudo";

message FetchAccessTokenRequest {
  string grant_type = 1 [json_name = "grant_type"];
  string client_id = 2 [json_name = "client_id"];
  string client_secret = 3 [json_name = "client_secret"];
}

message FetchAccessTokenResponse {
  string access_token = 1 [json_name = "access_token"];
  string scope = 2 [json_name = "scope"];
  string token_type = 3 [json_name = "token_type"];
  int64 expires_in = 4 [json_name = "expires_in"];
  string jti = 5 [json_name = "jti"];
}
