syntax = "proto3";

package vendors.netcore;

option go_package = "github.com/epifi/gamma/api/vendors/netcore";
option java_package = "com.github.epifi.gamma.api.vendors.netcore";

// Ref: https://cpaasdocs.netcorecloud.com/docs/netcore-sms/nvlyafontwbiv-send-message-api
// NetCoreSendSmsResponse response from the send sms api from netcore.
message NetCoreSendSmsResponse {
  // mobile number of the recipient
  string mobile_no = 1 [json_name = "mobileNo"];
  // unique id of the request
  string req_id = 2 [json_name = "req_id"];
  // array of messages data
  repeated NetCoreMessagesData messages = 3 [json_name = "messages"];
  // error details
  NetCoreError error = 4 [json_name = "error"];
}

message NetCoreMessagesData {
  // unique id of the message
  string txn_id = 1 [json_name = "txn_id"];
  // submitted at time, example format: '2024-09-19 12:29:32'
  string submitted_at = 2 [json_name = "submittedAt"];
}

message NetCoreError {
  // error code
  string error_code = 1 [json_name = "error code"];
  // error description
  string desc = 2 [json_name = "desc"];
}

// Netcore callback request for sms delivery status
/*
| Parameters      | Field type              | Sample Data               | Description                                                   |
|-----------------|-------------------------|---------------------------|---------------------------------------------------------------|
| mobile          | integer (upto 64 bytes) | 919000000000              | the mobile number on which message was sent to                |
| publishdate     | datetime                | 2009-02-20 00:00:00       | the date and time when message was Sent                       |
| deliverydate    | datetime                | 2009-02-20 00:00:00       | the date and time when message was delivered                  |
| deliverystatus  | Var char                | Delivered or undelivered  | Delivery status as per operator report                        |
| feedid          | integer (upto 64 bytes) | 534867                    | The feedid from which client sent the message                 |
| requestid       | Long int                | 3453456723                | the id that is originally returned to the client per request  |
| transactionid   | Long int                | 1212121212121212          | the id that is originally returned to the client per request  |
| dlt_template_id | Long int                | 1107161867600000000       | DLT template id                                               |
| jobname         | Var char                | Tag name                  | Uniq identifier value passed by the client                    |
| no_of_sms       | integer                 | 1                         | No of SMS count                                               |
| operator        | Var char                | Vodafone / Airtel         | Mobile numbers operator                                       |
| senderid        | Var char                | NETCRE                    | Header of SMS                                                 |
| circle          | Var char                | Mumbai                    | Mobile number operator- circle                                |
*/
message NetCoreSmsDLRRequest {
  // mobile number of the recipient
  string mobile = 1 [json_name = "mobile"];
  // the date and time when message was Sent
  string publish_date = 2 [json_name = "publishdate"];
  // the date and time when message was delivered
  string delivery_date = 3 [json_name = "deliverydate"];
  // Delivery status as per operator report
  string delivery_status = 4 [json_name = "deliverystatus"];
  // The feed id from which client sent the message
  string feed_id = 5 [json_name = "feedid"];
  // the id that is originally returned to the client per request
  string request_id = 6 [json_name = "requestid"];
  // the id that is originally returned to the client per request
  string transaction_id = 7 [json_name = "transactionid"];
  // DLT template id
  string dlt_template_id = 8 [json_name = "dlt_template_id"];
  // Uniq identifier value passed by the client
  string job_name = 9 [json_name = "jobname"];
  // No of SMS count
  string no_of_sms = 10 [json_name = "no_of_sms"];
  // Mobile numbers operator
  string operator = 11 [json_name = "operator"];
  // Header of SMS
  string sender_id = 12 [json_name = "senderid"];
  // Mobile number operator- circle
  string circle = 13 [json_name = "circle"];
}
