syntax = "proto3";

package vendors.inhouse;

import "api/auth/internal/auth_factor_update.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/device_properties.proto";
import "api/user/user.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendors/inhouse";
option java_package = "com.github.epifi.gamma.api.vendors.inhouse";

message DetectRiskRequest {
  string actor_id = 1 [json_name = "actor_id"];
  string request_id = 2 [json_name = "request_id"];
  // Actor ID of the user who referred the current user
  string referrer_actor_id = 3 [json_name = "referrer_actor_id"];
  string device_id = 4 [json_name = "device_id", deprecated = true];
  string email_id = 5 [json_name = "email"];
  bool credit_report_presence = 6 [json_name = "credit_report_presence"];
  bool credit_report_download_consent = 7 [json_name = "credit_report_download_consent"];
  string device_manufacturer = 8 [json_name = "device_manufacturer", deprecated = true];
  bool is_device_premium = 9 [json_name = "device_is_premium", deprecated = true];
  int32 age = 10 [json_name = "age"];
  // Name match score between gmail and pan name
  float gmail_pan_name_match_score = 11 [json_name = "namematch_gmail_kyc"];
  string phone_number = 12 [json_name = "phone_number"];
  int32 screener_mail_count = 13 [json_name = "screener_mail_count"];
  float latitude = 14 [json_name = "latitude"];
  float longitude = 15 [json_name = "longitude"];
  float threshold = 16 [json_name = "threshold"];
  repeated string liveness_statuses = 17 [json_name = "liveness_status"];
  repeated float liveness_scores = 18 [json_name = "liveness_score"];
  repeated string liveness_inhouse_errors = 19 [json_name = "liveness_inhouse_errors"];
  repeated float liveness_inhouse_scores = 20 [json_name = "liveness_inhouse_score"];
  repeated float facematch_scores = 21 [json_name = "facematch_score"];
  repeated string ckyc_errors = 22 [json_name = "ckyc_errors"];
  string hashed_phone_number = 23 [json_name = "hashed_number"];
  api.typesv2.common.Name father_name = 24 [json_name = "father_name"];
  api.typesv2.common.Name mother_name = 25 [json_name = "mother_name"];
  bool onboarding_ekyc_number_mismatch = 26 [json_name = "aadhar_number_mismatch"];
  api.typesv2.common.Name user_pan_name = 27 [json_name = "user_pan_name"];
  repeated float otp_scores = 28 [json_name = "otp_score"];
  repeated CreditReportAttributeInfo cb_details = 29 [json_name = "cb_details"];
  EmploymentData employment_data = 30 [json_name = "employment_data"];
  DeviceDetails device_details = 31 [json_name = "device_details"];
}

message CreditReportAttributeInfo {
  string attribute_key = 1;
  string attribute_value = 2;
}


message DetectRiskResponse {
  float score = 1 [json_name = "score"];
  float threshold = 2 [json_name = "threshold"];
  // bool flag to denote if the user is risky
  bool risky_user = 3 [json_name = "risky_user"];
  // response time of the API
  float time = 4 [json_name = "time"];
  // list of errors returned by the API
  repeated string error = 5 [json_name = "error"];
}

message DetectReOnboardingRiskRequest {
  string actor_id = 1 [json_name = "actor_id"];
  string request_id = 2 [json_name = "request_id"];
  // Actor ID of the user who referred the current user
  string referrer_actor_id = 3 [json_name = "referrer_actor_id"];
  string device_id = 4 [json_name = "device_id", deprecated = true];
  string email_id = 5 [json_name = "email"];
  bool credit_report_presence = 6 [json_name = "credit_report_presence"];
  bool credit_report_download_consent = 7 [json_name = "credit_report_download_consent"];
  string device_manufacturer = 8 [json_name = "device_manufacturer", deprecated = true];
  bool is_device_premium = 9 [json_name = "device_is_premium", deprecated = true];
  int32 age = 10 [json_name = "age"];
  // Name match score between gmail and pan name
  float gmail_pan_name_match_score = 11 [json_name = "namematch_gmail_kyc"];
  string phone_number = 12 [json_name = "phone_number"];
  int32 screener_mail_count = 13 [json_name = "screener_mail_count"];
  float latitude = 14 [json_name = "latitude"];
  float longitude = 15 [json_name = "longitude"];
  float threshold = 16 [json_name = "threshold"];
  repeated string liveness_statuses = 17 [json_name = "liveness_status"];
  repeated float liveness_scores = 18 [json_name = "liveness_score"];
  repeated string liveness_inhouse_errors = 19 [json_name = "liveness_inhouse_errors"];
  repeated float liveness_inhouse_scores = 20 [json_name = "liveness_inhouse_score"];
  repeated float facematch_scores = 21 [json_name = "facematch_score"];
  repeated string ckyc_errors = 22 [json_name = "ckyc_errors"];
  string hashed_phone_number = 23 [json_name = "hashed_number"];
  api.typesv2.common.Name father_name = 24 [json_name = "father_name"];
  api.typesv2.common.Name mother_name = 25 [json_name = "mother_name"];
  bool onboarding_ekyc_number_mismatch = 26 [json_name = "aadhar_number_mismatch"];
  api.typesv2.common.Name user_pan_name = 27 [json_name = "user_pan_name"];
  repeated float otp_scores = 28 [json_name = "otp_score"];
  string user_city = 30 [json_name = "user_city"];
  string user_postal_code = 31 [json_name = "user_postal_code"];
  string kyc_level = 32 [json_name = "kyc_level"];
  string overall_afu_status = 33 [json_name = "overall_afu_status"];
  repeated string auth_factors = 34 [json_name = "auth_factors"];
  string failure_reason = 35 [json_name = "failure_reason"];
  auth.afu.AuthFactorValues new_values = 36 [json_name = "new_values"];
  int64 afu_attempt_num = 37 [json_name = "afu_attempt_num"];
  repeated float afu_liveness_score = 38 [json_name = "afu_liveness_score"];
  repeated float afu_otp_score = 39 [json_name = "afu_otp_score"];
  repeated float afu_facematch_score = 40 [json_name = "afu_facematch_score"];
  repeated string vendor_request_statuses = 41 [json_name = "vendor_request_statuses"];
  string epifi_email_phone_num_update = 42 [json_name = "epifi_email_phone_num_update"];
  string epifi_device_update = 43 [json_name = "epifi_device_update"];
  auth.afu.AuthFactorValues current_values = 44 [json_name = "current_values"];
  api.typesv2.common.Device new_device = 45 [json_name = "new_device", deprecated = true];
  auth.afu.ActorAuthState actor_auth_state = 46 [json_name = "actor_auth_state"];
  string user_state = 47 [json_name = "user_state"];
  float afu_latitude = 48 [json_name = "afu_latitude"];
  float afu_longitude = 49 [json_name = "afu_longitude"];
  string onboarding_completed_at = 50 [json_name = "onboarding_completed_at"];
  repeated CreditReportAttributeInfo cb_details = 51 [json_name = "cb_details"];
  repeated string afu_liveness_statuses = 52 [json_name = "afu_liveness_status"];
  EmploymentData employment_data = 53 [json_name = "employment_data"];
  DeviceDetails device_details = 54 [json_name = "device_details"];
  repeated AFUAttempt afu_attempts = 55 [json_name = "afu_attempts"];
  ScreenerChecksInfo screener_checks_info = 56 [json_name = "screener_checks_info"];
  float onboarding_model_risk_score = 58 [json_name = "onboarding_model_risk_score"];
  AccountInfo account_info = 59 [json_name = "account_info"];
  DeviceDetails old_device_details = 60 [json_name = "old_device_details"];
  string is_credit_report_download_consent_given = 61 [json_name = "is_credit_report_download_consent_given"];
  // user image will be removed
  user.Profile profile = 62;
}

message DetectReOnboardingRiskResponse {
  float score = 1 [json_name = "score"];
  float threshold = 2 [json_name = "threshold"];
  // bool flag to denote if the user is risky
  bool risky_user = 3 [json_name = "risky_user"];
  // response time of the API
  float time = 4 [json_name = "time"];
  // list of errors returned by the API
  repeated string error = 5 [json_name = "error"];
}

message DetectLocationRiskRequest {
  string actor_id = 1 [json_name = "actor_id", (validate.rules).string.min_len = 1];

  string request_id = 2 [json_name = "request_id", (validate.rules).string.min_len = 1];

  string latitude = 3 [json_name = "latitude", (validate.rules).string.min_len = 1];

  string longitude = 4 [json_name = "longitude", (validate.rules).string.min_len = 1];

  string pincode = 5 [json_name = "pincode", (validate.rules).string.min_len = 1];
}

message DetectLocationRiskResponse {
  float score = 1 [json_name = "score"];

  string severity = 2 [json_name = "severity"];
  // location risk model version
  string version = 3 [json_name = "version"];
}

// EmploymentData will contain the information user has provided about their employment such as salary, employee info.
message EmploymentData {
  message Range {
    int32 min_value = 1 [json_name = "min_value"];
    int32 max_value = 2 [json_name = "max_value"];
  }

  string employment_type = 1 [json_name = "employment_type"];

  // company name selected/entered by salaried users
  string company_name = 2 [json_name = "company_name"];

  Range annual_salary_range = 3 [json_name = "annual_salary_range"];

  // if company is registered with EPFO
  bool is_company_epf_registered = 4 [json_name = "is_company_epf_registered"];
}

// DeviceDetails contains device details and device properties.
message DeviceDetails {
  string id = 1 [json_name = "id"];

  string manufacturer = 8 [json_name = "manufacturer"];

  bool is_premium = 9 [json_name = "is_premium"];

  // app version in semver format e.g. 4.2.0
  string app_version = 5 [json_name = "app_version"];

  string language = 6 [json_name = "language"];

  string sw_version = 7 [json_name = "sw_version"];

  // list of all apps installed
  repeated api.typesv2.UserDeviceInstalledAppInfo installed_apps = 10 [json_name = "installed_apps"];

  string model = 11 [json_name = "model"];
}

// AFUAttempt contains afu data for a single afu attempt such as liveness and facematch scores.
message AFUAttempt {
  string overall_status = 1 [json_name = "overall_status"];
  repeated string auth_factors = 2 [json_name = "auth_factors"];
  repeated float liveness_scores = 3 [json_name = "liveness_scores"];
  repeated float otp_scores = 4 [json_name = "otp_scores"];
  repeated float facematch_scores = 5 [json_name = "facematch_scores"];
  string created_at = 6 [json_name = "created_at"];
  repeated string liveness_statuses = 7 [json_name = "liveness_statuses"];
}

// ScreenerChecksInfo contains screener attempts and related checks.
message ScreenerChecksInfo {
  int32 credit_report_fail_count = 1 [json_name = "credit_report_fail_count"];
}

// AccountInfo contains info related to user account.
message AccountInfo {
  string tier = 1 [json_name = "tier"];
  string created_at = 2 [json_name = "created_at"];
}

message GetCasePrioritisationScoreRequest {
  message Alert {
    // UUID, primary id in alerts table
    string id = 1 [json_name = "id"];
    // id of the linked case in CRM tool
    string case_id = 2 [json_name = "case_id"];
    // rule which triggered the alert
    string rule_id = 3 [json_name = "rule_id"];
    // optional field to tag an alert to a particular batch
    string batch_name = 4 [json_name = "batch_name"];
    // identifier for actor and entity which was flagged by the rule
    string actor_id = 5 [json_name = "actor_id"];
    string account_id = 6 [json_name = "account_id"];
    string entity_type = 7 [json_name = "entity_type"];
    string entity_id = 8 [json_name = "entity_id"];
    // field to indicate what was the final resolution for the case
    string verdict = 9 [json_name = "verdict"];
    string created_at = 10 [json_name = "created_at"];
    string updated_at = 11 [json_name = "updated_at"];
    string account_type = 12 [json_name = "account_type"];
    // Initiation timestamp of the alert
    string initiated_at = 13 [json_name = "initiated_at"];
    // Indicates how alert was handled after creation
    string handling_type = 14 [json_name = "handling_type"];
    // Specifies reasons for handling method of alert
    repeated string handling_reasons = 15 [json_name = "handling_reasons"];
    // field to capture precision score of the rule which triggered the alert
    float rule_precision = 16 [json_name = "rule_precision"];
    // meta details for alert like transaction blocks details and others
    AlertMetaDetails meta_details = 17 [json_name = "meta_details"];
  }

  message AlertMetaDetails {
    repeated TransactionBlock transaction_blocks = 1 [json_name = "transaction_blocks"];
  }

  message TransactionBlock {
    // Unique identifier for the transaction block
    string id = 1 [json_name = "id"];
    // Actor ID associated with this transaction block
    string actor_id = 2 [json_name = "actor_id"];
    // Alert ID that this transaction block is associated with (optional)
    string alert_id = 3 [json_name = "alert_id"];
    // Total credit amount for all transactions in this block
    double aggregated_credit = 4 [json_name = "aggregated_credit"];
    // Total debit amount for all transactions in this block
    double aggregated_debit = 5 [json_name = "aggregated_debit"];
    // Time duration for this transaction block in seconds
    int64 duration_seconds = 6 [json_name = "duration_seconds"];
    // Type of transaction block (e.g., "FIFO" etc.)
    string block_type = 7 [json_name = "block_type"];
    // List of transaction IDs included in this block
    repeated string transaction_ids = 8 [json_name = "transaction_ids"];
    // Creation timestamp
    string created_at = 9 [json_name = "created_at"];
    // Last update timestamp
    string updated_at = 10 [json_name = "updated_at"];
    // Soft deletion timestamp (Unix timestamp)
    int64 deleted_at_unix = 11 [json_name = "deleted_at_unix"];
  }

  message Rule {
    // UUID, primary id of the rule
    string id = 1 [json_name = "id"];
    // name and version of the rule should be unique
    string name = 2 [json_name = "name"];
    uint64 version = 3 [json_name = "version"];
    // rule description will be given while adding the rule
    string description = 4 [json_name = "description"];
    // Determines the evaluation method for the rule
    string evaluation_method = 5 [json_name = "evaluation_method"];
    // System where the rule is triggered
    string provenance = 6 [json_name = "provenance"];
    // current state of rule will determine how alerts generated by rule will be handled
    string state = 7 [json_name = "state"];
    string created_at = 8 [json_name = "created_at"];
    string updated_at = 9 [json_name = "updated_at"];
    string deleted_at = 10 [json_name = "deleted_at"];
    // external id of the rules in the external rule engines such as dronapay
    string external_id = 11 [json_name = "external_id"];
    // type of entity that a rule will be assessing, it could be user, transaction, liveness etc
    string assessed_entity_type = 12 [json_name = "assessed_entity_type"];
    // type of entity that this rule should create the alert against
    string txn_suspect_entity = 13 [json_name = "txn_suspect_entity"];
    // type of group that this rule belong to, it could be crypto, atm etc
    string rule_group = 14 [json_name = "rule_group"];
    // added_by_email for the audit purpose
    string added_by_email = 15 [json_name = "added_by_email"];
    // seed precision for the rule, it denotes the initial precision of a rule
    float seed_precision = 16 [json_name = "seed_precision"];
    // If set true, seed precision will be used/preferred instead of the auto-computed precision
    bool force_use_seed_precision = 17 [json_name = "force_use_seed_precision"];
    // tags associated with this rule like adhoc tag etc.
    repeated string tags = 18 [json_name = "tags"];
  }

  message ExtendedRule {
    Rule rule = 1 [json_name = "rule"];
    // Potential reviews required when a rule triggers
    repeated ReviewTypeDetails review_type_details = 2 [json_name = "review_type_details"];
  }

  message ReviewTypeDetails {
    string review_type = 1 [json_name = "review_type"];
  }

  message AlertWithRuleDetails {
    Alert alert = 1 [json_name = "alert"];
    Rule rule = 2 [json_name = "rule"];
    ExtendedRule extended_rule = 3 [json_name = "extended_rule"];
  }

  message CaseDetails {
    string case_id = 1;
    repeated AlertWithRuleDetails alert_with_rule_details = 2;
  }

  repeated AlertWithRuleDetails alert_with_rule_details = 1 [json_name = "alert_with_rule_details"];
  string actor_id = 2 [json_name = "actor_id"];
  string request_id = 3 [json_name = "request_id"];
  string model_version = 4 [json_name = "model_version"];
  CaseDetails case_details = 5 [json_name = "case_details"];
}


message ModelResponseInfo {
  string name = 1;
  float score = 2;
}

message GetCasePrioritisationScoreResponse {
  string request_id = 1 [json_name = "request_id"];
  string model_version = 2 [json_name = "model_version"];
  float score = 3 [json_name = "score"];
  repeated ModelResponseInfo model_info = 4;
}
