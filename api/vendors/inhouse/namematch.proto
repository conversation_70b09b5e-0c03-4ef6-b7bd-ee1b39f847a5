syntax = "proto3";

package vendors.inhouse;

option go_package = "github.com/epifi/gamma/api/vendors/inhouse";
option java_package = "com.github.epifi.gamma.api.vendors.inhouse";

message NameMatchRequest {
  // First name to compare
  string name1 = 1 [json_name = "name1"];
  // Second name to compare
  string name2 = 2 [json_name = "name2"];
}

message NameMatchResponse {
  // Decision if the names match or not [0 = Don't Match, 1 = Match]
  int32 decision = 1 [json_name = "decision"];
  // Score of similarity between the two names, ranges between 0 and 1
  float score = 2 [json_name = "score"];
  // feature dictionary
  string feature_dict = 3 [deprecated=true];
  // risk_match
  int32 risk_match = 4 [json_name = "risk_match", deprecated = true];

}
