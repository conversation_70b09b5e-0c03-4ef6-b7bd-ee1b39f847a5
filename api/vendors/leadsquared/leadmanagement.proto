syntax = "proto3";

package vendors.leadsquared;

option go_package = "github.com/epifi/gamma/api/vendors/leadsquared";
option java_package = "com.github.epifi.gamma.api.vendors.leadsquared";

message CreateOrUpdateLeadField {
  // custom attribute to which a value will be assigned
  // eg.- EmailAddress, FirstName, LastName, Phone etc.
  string Attribute = 1 [json_name = "Attribute"];
  // value of the given attribute
  string Value = 2 [json_name = "Value"];
}

message CreateOrUpdateLeadResponse {
  // denotes the status of vendor api, eg. "Success"
  string status = 1 [json_name = "Status"];
  message ResponseMessage {
    // id of the lead created at vendor's end
    string id = 2 [json_name = "Id"];
    // number of records affected be the api
    int32 affected_rows = 3 [json_name = "AffectedRows"];
  }
  ResponseMessage message = 2 [json_name = "Message"];
}