syntax = "proto3";

package vendors.crm.freshdesk;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendors/crm/freshdesk";
option java_package = "com.github.epifi.gamma.api.vendors.crm.freshdesk";

// Job status response from Freshdesk API
message Job {
  // Unique ID of the job
  string id = 1 [json_name = "id"];

  // Name of the job (e.g., "ACCOUNT::EXPORT")
  string name = 2 [json_name = "name"];

  // Status of the job: "IN PROGRESS", "COMPLETED", or "FAILED"
  // Todo: Make this string type as enums
  string status = 3 [json_name = "status"];

  // Timestamp when the job was created
  google.protobuf.Timestamp created_at = 4 [json_name = "created_at"];

  // Timestamp when the job was last updated
  google.protobuf.Timestamp updated_at = 5 [json_name = "updated_at"];

  // Timestamp when the status was last updated
  google.protobuf.Timestamp status_updated_at = 6 [json_name = "status_updated_at"];

  // Progress percentage (0-100)
  int32 progress = 7 [json_name = "progress"];

  message JobData {
    bool success = 1 [json_name = "success"];
    int32 id = 2 [json_name = "id"];
    message JobEntryError {
      string field = 1 [json_name = "field"];
      string message = 2 [json_name = "message"];
      string code = 3 [json_name = "code"];
    }
    JobEntryError error = 3 [json_name = "error"];
  }

  repeated JobData data = 8 [json_name = "data"];
}

// Request to get job status by ID
message GetJobRequest {
  // Job ID to retrieve status for
  string job_id = 1 [json_name = "job_id"];
}
