// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendors.alpaca;

import "api/vendors/alpaca/investment.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendors/alpaca";
option java_package = "com.github.epifi.gamma.api.vendors.alpaca";

// OrderUpdate contains updated trade object updates received over SSE endpoints
message OrderUpdate {
  // account id for which event is generated
  string account_id = 1 [json_name = "account_id"];

  // time at which event was generated
  google.protobuf.Timestamp event_time = 2 [json_name = "at"];

  // event_id for uniquely identifying the event
  // it is a ULID, which can be alphanumeric, but for now we are taking it as int64 because we are getting integer values
  int64 event_id = 3 [json_name = "event_id"];

  // order details
  vendors.alpaca.Order order = 4 [json_name = "order"];

  /*
timestamp at which the corresponding transition of state happened at vendor
eg:
1. if order transition to `filled` then value of timestamp is the transition time or filled at time
2. If order is canceled through user action, the value of timestamp is the time at which vendor processed the cancelation request successfully
*/
  google.protobuf.Timestamp timestamp = 5 [json_name = "timestamp"];
}

// AccountUpdate contains updated account object updates received over SSE endpoints
message AccountUpdate{
  string account_id = 1 [json_name = "account_id"];
  string account_number = 2 [json_name = "account_number"];
  // time at which event was generated
  google.protobuf.Timestamp event_time = 3 [json_name = "at"];
  // event_id for uniquely identifying the event
  // it is monotonically increasing 64bit integer
  int64 event_id = 4 [json_name = "event_id"];
  // Account status before update
  string status_from = 5 [json_name = "status_from"];
  // Account status after update
  string status_to = 6 [json_name = "status_to"];
  // Optional reason text in form of string
  string reason = 7 [json_name = "reason"];
  // If true the pattern_day_trader flag was set for the account, if false, the flag was reset.
  bool pattern_day_trader = 8 [json_name = "pattern_day_trader"];
  // If true the account was blocked, if false, the account got unblocked
  bool account_blocked = 9 [json_name = "account_blocked"];
  // If true the account cannot trade going forward, if false, the ban has been lifed
  bool trading_blocked = 10 [json_name = "trading_blocked"];
  // Changed administrative flags
  AdminConfigurations admin_configurations = 11 [json_name = "admin_configurations"];
}

message FundTransferUpdate {
  // firm account id created at vendor's end
  string account_id = 1 [json_name = "account_id"];
  // time at which event was generated
  google.protobuf.Timestamp event_time = 2 [json_name = "at"];
  // event_id for uniquely identifying the event
  int64 event_id = 3 [json_name = "event_id"];
  // old status
  string status_from = 4 [json_name = "status_from"];
  // new status
  string status_to = 5 [json_name = "status_to"];
  // unique identifier for fund transfer request generated at vendor
  string fund_transfer_id = 6 [json_name = "transfer_id"];
}

message JournalUpdate {
  // time at which event was generated
  google.protobuf.Timestamp event_time = 1 [json_name = "at"];
  // JNLC or JNLS
  string journal_type = 2 [json_name = "entry_type"];
  // event_id for uniquely identifying the event
  int64 event_id = 3 [json_name = "event_id"];
  // unique identifier for journal request
  string journal_id = 4 [json_name = "journal_id"];
  // old status
  string status_from = 5 [json_name = "status_from"];
  // new status
  string status_to = 6 [json_name = "status_to"];
}

message AdminConfigurations{
  // Wire-out transfers blocked for the account if false
  bool outgoing_transfers_blocked = 1 [json_name = "outgoing_transfers_blocked"];
  // Deposits are blocked for the account if false
  bool incoming_transfers_blocked = 2 [json_name = "incoming_transfers_blocked"];
  // If true the account is not allowed to create short position orders
  bool disable_shorting = 3 [json_name = "disable_shorting"];
  // If true, the account cannot create orders for fractional share positions
  bool disable_fractional = 4 [json_name = "disable_fractional"];
  // If true, the account is not allowed to trade cryptos
  bool disable_crypto = 5 [json_name = "disable_crypto"];
  // If true, the account is not allowed to day trade (e.g. buy and sell the same security on th
  bool disable_day_trading = 6 [json_name = "disable_day_trading"];
  // Max margin multipler is set by admin to this value
  int64 max_margin_multiplier = 7 [json_name = "max_margin_multiplier"];
  // Override the correspondent level daily transfer limits
  int64 acct_daily_transfer_limit = 8 [json_name = " acct_daily_transfer_limit"];
  // Reasons why the liquidation only flag was set
  RestrictToLiquidationReasons restrict_to_liquidation_reasons = 9 [json_name = "restrict_to_liquidation_reasons"];
}

message RestrictToLiquidationReasons{
  // Set when the trading account is marked as a PDT, but its equity falls below the $25k treshold
  bool pattern_day_trading = 1 [json_name = "pattern_day_trading"];
  // Set when an incoming ACH transfer gets rejected
  bool ach_return = 2 [json_name = "ach_return"];
  // Set when the position to equity ration exceeds the maximum limit
  bool position_to_equity_ratio = 3 [json_name = "position_to_equity_ratio"];
  // Default value for unknown reason
  bool unspecified = 4 [json_name = "unspecified"];
}
