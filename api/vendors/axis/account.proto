syntax = "proto3";

package vendors.axis;

option go_package = "github.com/epifi/gamma/api/vendors/axis";
option java_package = "com.github.epifi.gamma.api.vendors.axis";

message ProductAllocationCallBackResponse{
  string application_reference_id = 1 [json_name = "app_ref_id"];
  string vcip_reference_id = 2 [json_name = "vcip_ref_id"];
  string vcip_url = 3 [json_name = "vcip_url"];
  string resume_vcip_url = 4 [json_name = "resume_vcip_url"];
  string status = 5 [json_name = "status"];
  string message = 6 [json_name = "msg"];
}

message AccountActivationCallBackResponse{
  string customer_id = 1 [json_name = "customerId"];
  string application_reference_id = 2 [json_name = "appRefId"];
  string account_number = 3 [json_name = "accountNumber"];
  string ckyc_id = 4 [json_name = "ckycId"];
  AccountDetails account_details = 5 [json_name = "accountDetails"];
  BranchDetails branch_details = 6 [json_name = "branchDetails"];
  CustomerDetails customer_details = 7 [json_name = "customerDetails"];
  CustomerDemographics customer_demographics = 8 [json_name = "customerDemographics"];
  NomineeDetails nominee_details = 9 [json_name = "nomineeDetails"];
  KycDetails kyc_details = 10 [json_name = "kycDetails"];
  CustomerConsents customer_consents = 11 [json_name = "customerConsents"];
}

message AccountDetails  {
  string account_number = 1 [json_name = "accountNumber"];
  string account_scheme_code = 2 [json_name = "accountSchemeCode"];
  string account_status = 3 [json_name = "accountStatus"];
  string opening_date = 4 [json_name = "openingDate"];
  string ifsc = 5 [json_name = "ifsc"];
  string email_statement = 6 [json_name = "emailStatement"];
  string status = 7 [json_name = "status"];
  string desc = 8 [json_name = "desc"];
}

message  CustomerConsents {
  ConsentDetails fatca_tax_declaration = 1 [json_name = "fatcaTaxDeclaration"];
  ConsentDetails non_pep_declaration = 2 [json_name = "nonPepDeclaration"];
  ConsentDetails account_opening_consent = 3 [json_name = "accountOpeningConsent"];
  ConsentDetails secondary_account_opening_consent = 4 [json_name = "secondaryAccountOpeningConsent"];
}

message ConsentDetails{
  string consent_timestamp = 1 [json_name = "consentTimestamp"];
  string customer_consent = 2 [json_name = "customerConsent"];
}

message NomineeDetails  {
  GuardianDetails guardian_details = 1 [json_name = "guardianDetails"];
  string nominee_date_of_birth = 2 [json_name = "nomineeDateOfBirth"];
  NomineeAddress nominee_address = 3 [json_name = "nomineeAddress"];
  string nomineeName = 4 [json_name = "nomineeName"];
  string relationship_with_accountHolder = 5 [json_name = "relationshipWithAccountHolder"];
}

message GuardianDetails {
  GuardianAddress guardian_address = 1 [json_name = "guardianAddress"];
  string guardian_name = 2 [json_name = "guardianName"];
  string relationship_with_nominee = 3 [json_name = "relationshipWithNominee"];
}

message NomineeAddress    {
  string address_line1 = 1 [json_name = "addressLine1"];
  string address_line2 = 2 [json_name = "addressLine2"];
  string address_line3 = 3 [json_name = "addressLine3"];
  string landmark = 4 [json_name = "landmark"];
  string associated_phone_number = 5 [json_name = "associatedPhoneNumber"];
  string city = 6 [json_name = "city"];
  string country = 7 [json_name = "country"];
}

message GuardianAddress  {
  string address_line1 = 1 [json_name = "addressLine1"];
  string address_line2 = 2 [json_name = "addressLine2"];
  string address_line3 = 3 [json_name = "addressLine3"];
  string landmark = 4 [json_name = "landmark"];
  string pincode = 5 [json_name = "pincode"];
  string associated_phone_number = 6 [json_name = "associatedPhoneNumber"];
  string city = 7 [json_name = "city"];
  string country = 8 [json_name = "country"];
}

message KycDetails  {
  string kyc_type = 1 [json_name = "kycType"];
  string date_of_declaration = 2 [json_name = "dateOfDeclaration"];
  string place_of_declaration = 3 [json_name = "placeOfDeclaration"];
  string verification_date = 4 [json_name = "verificationDate"];
  string checker_id = 5 [json_name = "checkerId"];
}

message BranchDetails  {
  string sol_id = 1 [json_name = "solId"];
  BranchAddress branch_address = 2 [json_name = "branchAddress"];
  string branch_name = 3 [json_name = "branchName"];
}

message BranchAddress  {
  string address_line1 = 1 [json_name = "addressLine1"];
  string address_line2 = 2 [json_name = "addressLine2"];
  string address_line3 = 3 [json_name = "addressLine3"];
  string landmark = 4 [json_name = "landmark"];
  string pincode = 5 [json_name = "pincode"];
  string city = 6 [json_name = "city"];
  string country = 7 [json_name = "country"];
}

message CustomerName  {
  string first_name = 1 [json_name = "firstName"];
  string middle_name = 2 [json_name = "middleName"];
  string last_name = 3 [json_name = "lastName"];
}

message CommunicationAddress  {
  string customer_declaration = 1 [json_name = "customerDeclaration"];
  string address_line1 = 2 [json_name = "addressLine1"];
  string address_line2 = 3 [json_name = "addressLine2"];
  string address_line3 = 4 [json_name = "addressLine3"];
  string landmark = 5 [json_name = "landmark"];
  string pincode = 6 [json_name = "pincode"];
  string associated_phone_number = 7 [json_name = "associatedPhoneNumber"];
  string city = 8 [json_name = "city"];
  string country = 9 [json_name = "country"];
  string poa = 10 [json_name = "poa"];
}

message CustomerDemographics  {
  string mothers_maiden_name = 1 [json_name = "mothersMaidenName"];
  string fathers_name = 2 [json_name = "fathersName"];
  string occupation = 3 [json_name = "occupation"];
  string date_of_birth = 4 [json_name = "dateOfBirth"];
  string gender = 5 [json_name = "gender"];
  string income_range = 6 [json_name = "incomeRange"];
  string marital_status = 7 [json_name = "maritalStatus"];
  string nationality = 8 [json_name = "nationality"];
}

message CustomerDetails  {
  string customer_type = 1 [json_name = "customerType"];
  CustomerName customer_name = 2 [json_name = "customerName"];
  string mobile_number = 3 [json_name = "mobileNumber"];
  string email = 4 [json_name = "email"];
  string pan = 5 [json_name = "pan"];
  string customer_photo = 6 [json_name = "customerPhoto"];
  CommunicationAddress communication_address = 7 [json_name = "communicationAddress"];
  CommunicationAddress permanent_address = 8 [json_name = "permanentAddress"];
}
