syntax = "proto3";

package vendors.email.sendgrid;


option go_package = "github.com/epifi/gamma/api/vendors/email/sendgrid";
option java_package = "com.github.epifi.gamma.api.vendors.email.sendgrid";


// Event             - Description
// Processed         - Message has been received and is ready to be delivered.
// Dropped           - You may see the following drop reasons: Invalid SMTPAPI header, 
//                     Spam Content , Unsubscribed Address, Bounced Address, 
//                     Spam Reporting Address, Invalid, Recipient List over Package Quota
// Delivered         - Message has been successfully delivered to the receiving server.
// Bounce            - Receiving server could not or would not accept mail to this recipient 
//                     permanently. If a recipient has previously unsubscribed from your 
//                     emails, the message is dropped.
// Blocked	         - Receiving server could not or would not accept the message 
//                     temporarily. If a recipient has previously unsubscribed from your 
//                     emails, the message is dropped.
// Open	             - Recipient has opened the HTML message. Open Tracking needs to be 
//                     enabled for this type of event.
// Click	         - Recipient clicked on a link within the message. Click Tracking needs 
//                     to be enabled for this type of event.
// Spam Report       - Recipient marked message as spam.
// Unsubscribe	     - Recipient clicked on the 'Opt Out of All Emails' link 
//                     (available after clicking the message's subscription management link). 
//                     Subscription Tracking needs to be enabled for this type of event.
// Group Unsubscribe - Recipient unsubscribed from a specific group either by clicking the 
//                     link directly or updating their preferences. Subscription Tracking 
//                     needs to be enabled for this type of event.
// Group Resubscribe - Recipient resubscribed to a specific group by updating their 
//                     preferences. Subscription Tracking needs to be enabled for this 
//                     type of event.


// SendGrid sends event callbacks as a list 
message SendGridCallbackList {
    repeated SendGridCallback send_grid_callback = 1;
}

message SendGridCallback{

    // the email address of the recipient
    string email = 1 [json_name = "email"];

    // the UNIX timestamp of when the message was sent
    int32 timestamp = 2 [json_name = "timestamp"];

    // the event type. Possible values are processed, dropped, delivered, deferred, bounce,
    // open, click, spam report, unsubscribe, group unsubscribe, and group resubscribe.
    string event = 3 [json_name = "event"];

    // a unique ID attached to the message by the originating system.
    string smtp_id = 4 [json_name = "smtp-id"];

    // the user agent responsible for the event. 
    // For example, "Mozilla/5.0 (Macintosh; Intel Mac OS X 1084)"
    string useragent = 5 [json_name = "useragent"];

    // the IP address used to send the email. For open and click events, 
    // it is the IP address of the recipient who engaged with the email.
    string ip = 6 [json_name = "ip"];

    // a unique ID to this event that you can use for deduplication purposes.
    string sg_event_id = 7 [json_name = "sg_event_id"];
    
    // a unique, internal SendGrid ID for the message.
    string sg_message_id = 8 [json_name = "sg_message_id"];
    
    // any sort of error response returned by the receiving server that describes 
    // the reason this event type was triggered.
    string reason = 9 [json_name = "reason"];

    // status code string. Corresponds to HTTP status code - 
    // for example, a JSON response of 5.0.0 is the same as a 500 error response.
    string status = 10 [json_name = "status"];  
    
    // the full text of the HTTP response error returned from the receiving server.
    string response = 11 [json_name = "response"];

    // the URL where the event originates. 
    // For click events, this is the URL clicked on by the recipient.
    string url = 12 [json_name = "url"];

    // The ID of the unsubscribe group the recipient's email address is included in. 
    // ASM IDs correspond to the ID that is returned when you create an unsubscribe group.
    int32 asm_group_id = 13 [json_name = "asm_group_id"];
    
    // The ID of an marketing campaign send
    int32 marketing_campaign_id = 14 [json_name = "marketing_campaign_id"];
    
    // The name of an marketing campaign send
    string marketing_campaign_name = 15 [json_name = "marketing_campaign_name"];
    
    // the number of times SendGrid has attempted to deliver this message.
    string attempt = 16 [json_name = "attempt"];
    
    // For emails sent with a specified IP Pool, 
    // you can view the IP Pool in the event data for a processed event.
    Pool pool = 17 [json_name = "pool"];
    
    // indicates whether the bounce event was a 
    // hard bounce (type=bounce) or block (type=blocked)
    string type = 18 [json_name = "type"];

    // Categories are custom tags that you set for the purpose of organizing your emails.
    // If you send single categories as an array, they will be returned by the webhook as an array. 
    // If you send single categories as a string, they will be returned by the webhook as a string.
    // string category = 19 [json_name = "category"];
}

message Pool{
    string name = 1 [json_name = "name"];
    int32 id = 2 [json_name = "id"];
}
