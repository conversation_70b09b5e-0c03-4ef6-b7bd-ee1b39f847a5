// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package savings.statement;

import "api/rpc/status.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/savings/statement";
option java_package = "com.github.epifi.gamma.api.savings.statement";

// TODO(vivek): Remove this service since it is migrated to accounts/statement
service SavingsStatement{
  // GenerateAccountStatement RPC can be used to generate statement for an account onboarded by epiFi
  // from start date to end date. Start date and end date values are passed in the request message.
  // This is an async rpc call and the request will be queued. In case of failed response user will be
  // notified by a push notification and in case of success generated account statement will be sent to user's mail id.
  // The account statement generation in PDF format is only supported to begin with.
  rpc GenerateAccountStatement(GenerateAccountStatementRequest) returns (GenerateAccountStatementResponse) {
    option deprecated = true;
  }
}

enum SavingsStatementFormat {
  STATEMENT_FORMAT_UNSPECIFIED = 0;
  PDF = 1;
}

message GenerateAccountStatementRequest {
  // internal account id
  string account_id = 1 [(validate.rules).string.min_len = 1];

  // date from which account statement needs to be fetched (inclusive)
  google.type.Date from_date = 2 [(validate.rules).message.required = true];

  // date upto which account statement needs to be fetched (inclusive)
  google.type.Date to_date = 3 [(validate.rules).message.required = true];

  // format in which account statement needs to be generated
  SavingsStatementFormat format = 4[(validate.rules).enum = {not_in: [0]}];

  // flag to determine if it is a request to generate monthly statement
  bool is_monthly_statement = 5;
}

message GenerateAccountStatementResponse {
  enum Status {
    OK = 0;
    // request parameters invalid
    INVALID_ARGUMENT = 3;
    // Internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}
