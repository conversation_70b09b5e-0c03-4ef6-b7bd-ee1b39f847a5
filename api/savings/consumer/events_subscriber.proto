syntax = "proto3";

package savings.consumer;

import "api/queue/consumer_headers.proto";
import "api/tiering/external/tier_update_event.proto";
import "api/user/kyc_event.proto";

option go_package = "github.com/epifi/gamma/api/savings/consumer";
option java_package = "com.github.epifi.gamma.api.savings.consumer";

// Defines the GRPC service to process events subscriber
service EventsSubscriber {
  // ProcessTierUpdateEventConsumer listens to tier update topic and take relevant actions
  // Need to update the scheme change on vendor and update in savings account
  rpc ProcessTierUpdateEventConsumer (tiering.external.TierUpdateEvent) returns (ProcessTierUpdateEventConsumerResponse) {
  };
  // ProcessTierUpdateEventConsumer listens to kyc update topic and take relevant actions
  // Need to update the scheme change on vendor if tier is Regular and update in savings account table
  rpc ProcessKycUpdateEvent (user.KycEvent) returns (ProcessKycUpdateEventResponse) {
  };
}

message ProcessTierUpdateEventConsumerResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}

// response for kyc update event which will be used to update scheme code of a savings account if applicable
message ProcessKycUpdateEventResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}
