syntax = "proto3";
//go:generate gen_queue_pb
package savings.consumer;

import "api/queue/consumer_headers.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/savings/consumer";
option java_package = "com.github.epifi.gamma.api.savings.consumer";

/* Consumer to notify the user when their balance is lower than a threshold amount.
   Whenever balance is fetched in the background, we run a async check on whether user's balance is below a threshold amount.
   If balance is low, the user will be notified through PN to add money. During some of the off-app transactions,
   the user is unaware of their actual balance being low.
   Threshold for low balance decision is config driven.
*/
service BalanceNotificationConsumer {
  rpc NotifyLowBalanceUsers(BalanceUpdateEvent) returns (NotifyLowBalanceUsersResponse);
}

message NotifyLowBalanceUsersResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

// Event used by balance update event
message BalanceUpdateEvent {
  queue.ConsumerRequestHeader request_header = 1;
  // actor id for whom the new balance was received
  string actor_id = 2;
  // the latest balance amount
  google.type.Money balance_amount = 4;
}
