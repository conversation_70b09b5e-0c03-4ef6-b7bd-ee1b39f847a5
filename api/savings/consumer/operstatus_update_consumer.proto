//go:generate gen_queue_pb
syntax = "proto3";

package savings.consumer;

import "api/accounts/operstatus/events.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/savings/consumer";
option java_package = "com.github.epifi.gamma.api.savings.consumer";

// Defines the GRPC service to process updates to operational status update for a savings account.
// This GRPC service is registered with queue subscriber and
// RPC method will be invoked by the consumer on receiving an event
service AccountOperStatusConsumer {
  // UpdateSavingsAccountStatus listens to account operational status change and
  // makes relevant state changes savings account service
  rpc UpdateSavingsAccountStatus (accounts.operstatus.OperationalStatusUpdateEvent) returns (UpdateSavingsAccountStatusResponse) {
  };
}

message UpdateSavingsAccountStatusResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}
