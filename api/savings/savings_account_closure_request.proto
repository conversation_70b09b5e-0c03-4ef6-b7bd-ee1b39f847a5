//go:generate gen_sql -types=SaClosureRequestUserFeedback
syntax = "proto3";

package savings;

import "api/savings/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/savings";
option java_package = "com.github.epifi.gamma.api.savings";

message SavingsAccountClosureRequest {
  // unique id of the closure request
  string id = 1;
  // actor-id to whom the account closure request belongs to
  string actor_id = 2;
  // savings account id for reference
  string savings_account_id = 3;
  // status of the account closure request
  SAClosureRequestStatus status = 4;
  // reason associated with the status of the account closure request
  SAClosureRequestStatusReason status_reason = 5;
  // entry point from where the closure request was raised
  SAClosureRequestEntryPoint entry_point = 6;
  // feedback taken from the user during the closure flow, i.e. reason for the SA closure
  SaClosureRequestUserFeedback user_feedback = 7;

  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  google.protobuf.Timestamp deleted_at = 15;
}

message SaClosureRequestUserFeedback {
  // user selected feedback text / user's custom feedback text
  string feedback_text = 1;
}

enum SavingsAccountClosureRequestFieldMask {
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_ID = 1;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_ACTOR_ID = 2;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_SAVINGS_ACCOUNT_ID = 3;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_STATUS = 4;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_STATUS_REASON = 5;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_ENTRY_POINT = 6;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_USER_FEEDBACK = 7;

  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_CREATED_AT = 13;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_UPDATED_AT = 14;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_FIELD_MASK_DELETED_AT = 15;
}

enum SavingsAccountClosureRequestUpdateFieldMask {
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_UPDATE_FIELD_MASK_UNSPECIFIED = 0;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_UPDATE_FIELD_MASK_STATUS_REASON = 1;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_UPDATE_FIELD_MASK_ENTRY_POINT = 2;
  SAVINGS_ACCOUNT_CLOSURE_REQUEST_UPDATE_FIELD_MASK_USER_FEEDBACK = 3;
}
