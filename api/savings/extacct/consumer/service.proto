//go:generate gen_queue_pb
syntax = "proto3";

package savings.extacct.consumer;

import "api/queue/consumer_headers.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/savings/extacct/consumer";
option java_package = "com.github.epifi.gamma.api.savings.extacct.consumer";

service Consumer {
  // ProcessThirdPartyAccountSharingEvent receives event for third party account details sharing
  // and shares it with vendor(Federal)
  // Events are published mostly from min kyc account closure flow(both Fe and WebFe)
  rpc ProcessThirdPartyAccountSharingEvent (ThirdPartyAccountSharingEvent)
    returns (ProcessThirdPartyAccountSharingEventResponse) {};

  rpc ProcessThirdPartyFundTransferEnquiryEvent (ThirdPartyFundTransferEnquiryEvent)
    returns (ProcessThirdPartyFundTransferEnquiryEventResponse) {};
}

message ThirdPartyAccountSharingEvent {
  queue.ConsumerRequestHeader request_header = 1;
  // actor id of the user who's sharing the third party account details
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // Third party account number to share balance to
  string tp_account_number = 3 [(validate.rules).string.min_len = 1];
  // Third party account IFSC code corresponding to the account number
  string tp_ifsc = 4 [(validate.rules).string.min_len = 1];
  // User's savings account number
  string savings_account_number = 5 [(validate.rules).string.min_len = 1];
  // Vendor request id to use as unique penny drop id
  string vendor_req_id = 6 [(validate.rules).string.min_len = 1];
  // Mobile number of the user in 91XXXXXXXXXX format
  string mobile_number = 7 [(validate.rules).string.len = 12];
}

message ProcessThirdPartyAccountSharingEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ThirdPartyFundTransferEnquiryEvent {
  queue.ConsumerRequestHeader request_header = 1;
  // actor id of the user who's sharing the third party account details
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // Third party account number to share balance to
  string tp_account_number = 3 [(validate.rules).string.min_len = 1];
  // Third party account IFSC code corresponding to the account number
  string tp_ifsc = 4 [(validate.rules).string.min_len = 1];
  // User's savings account number
  string savings_account_number = 5 [(validate.rules).string.min_len = 1];
  // Vendor request id to use as unique penny drop id
  string vendor_req_id = 6 [(validate.rules).string.min_len = 1];
  // Mobile number of the user in 91XXXXXXXXXX format
  string mobile_number = 7 [(validate.rules).string.len = 12];
  // MetaData
  map<string, string> meta_data = 8;

}

message ProcessThirdPartyFundTransferEnquiryEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

