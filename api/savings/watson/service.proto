
// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package savings.watson;

import "api/cx/watson/watson_client.proto";


option go_package = "github.com/epifi/gamma/api/savings/watson";
option java_package = "com.github.epifi.gamma.api.savings.watson";

service Watson {

//This implements watson service's interface
//https://docs.google.com/document/d/1tGlfZs5jxZ7evIHY5Rm7wyVlQlmd2CuboDsi8GDTaxM/edit#


  //This API would be invoked to check if the reported incident is still valid to create a freshdesk ticket.
  rpc IsIncidentValid (.cx.watson.IsIncidentValidRequest) returns (.cx.watson.IsIncidentValidResponse);

  //The client can use this API to send all  custom fields to be populated in the ticket like troubleshooting advice.
  rpc GetTicketDetails (.cx.watson.GetTicketDetailsRequest) returns (.cx.watson.GetTicketDetailsResponse) ;
}
