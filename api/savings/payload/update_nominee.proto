syntax = "proto3";

package savings.payload;

import "api/savings/account.proto";


option go_package = "github.com/epifi/gamma/api/savings/payload";
option java_package = "com.github.epifi.gamma.api.savings.payload";


message UpdateNomineePayload {
  string account_id = 1;
  string req_id = 22;
  string service_request_id = 2;
  string ekyc_crrn = 3;
  string nominee_name = 4;
  string nominee_reg_no = 5;
  string nominee_relation_type = 6;
  string nominee_minor_flag = 7;
  string nominee_dob = 8;
  string nominee_address_line_1 = 9;
  string nominee_address_line_2 = 10;
  string nominee_address_line_3 = 11;
  string nominee__city = 12;
  string nominee_state = 13;
  string nominee_country = 14;
  string nominee_postal_code = 15;
  string guardian_code = 16;
  string guardian_name = 17;
  string channel = 18;

  string actor_id = 19;
  NomineeDetails nominee_details = 20;
  string client_req_id = 21;
}


