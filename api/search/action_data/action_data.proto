syntax = "proto3";

package search.actiondata;

import "api/accounts/account_type.proto";
import "api/typesv2/deposit.proto";
import "api/typesv2/money.proto";
import "google/type/money.proto";
import "api/frontend/deeplink/deeplink.proto";

option go_package = "github.com/epifi/gamma/api/search/actiondata";
option java_package = "com.github.epifi.gamma.api.search.actiondata";


// `PayAccountActionData` contains all parameters to initiate
// a payment to account of current actor
message PayAccountActionData {
  string actor_id = 1;
  string account_type = 2;
  string account_id = 3;
  google.type.Money amount = 4;
  string schedule = 5;
}

// `PayActionData` contains all parameters to initiate
// a timeline request to timeline service
// Timeline will be the entry point to start a payment in
// case of queries "pay vodafone"
message PayActionData {
  string payee_actor_id = 1;
  // payee actor name, used in making timeline
  string payee_actor_name = 2;
  // profile url to show image of the payee
  string payee_actor_profile_url = 3;
  string payer_actor_id = 4;
  string payer_actor_name = 5;
  string payer_profile_url = 6;
  string schedule = 7;
}

// `PayInstrumentAction` contains all parameters to initiate
// a payment to instrument of current actor
message PayInstrumentAction {
  // account id for the deposit account
  string account_id = 1;
  // [Optional] The amount to pre-fill in add money screen
  api.typesv2.Money amount_to_add = 2;
}

// `PayAccountActionData` contains all parameters to initiate
// a payment to account of current actor
message StartAccountActionData {
  string account_type = 2;
  string account_id = 3;
  string schedule = 5;
}

// `StartInstrumentActionData` contains all parameters to initiate
// a start instrument
message StartInstrumentActionData {
  // type of the instrument account (e.g., FD, SD)
  accounts.Type deposit_type = 1;
  // [Optional] The amount to pre-fill in open instrument screen
  api.typesv2.Money amount_to_add = 2;
  // [Optional] The maturity term to pre-fill while opening account
  api.typesv2.DepositTerm term = 3;
}

// `StartInstrumentActionData` contains all parameters to initiate
// a start instrument
message StartReminderActionData {
}


// `PayAccountActionData` contains all parameters to initiate
// a payment to account of current actor
message StopAccountData {
  // account id for the deposit account
  string account_id = 1;
}

// 'MutualFundsActionData' contains data related to a mutual fund in which the user has invested in.
// It will be used by frontend for creating mutual funds related deeplinks.
message MutualFundsActionData {
  string mutual_fund_id = 1;
  string mutual_fund_name = 2;
  string mutual_fund_icon_url = 3;
  frontend.deeplink.Screen deeplink_type = 4;
}

// `StopActorActionData` contains all parameters to initiate
// a block an actor
message StopActorActionData {
  string primary_actor_id = 1;
  // payee actor name, used in making timeline
  string primary_actor_name = 2;
  // profile url to show image of the payee
  string primary_actor_profile_url = 3;
  string secondary_actor_id = 4;
  string secondary_actor_name = 5;
  string secondary_profile_url = 6;
}

// `PayInstrumentAction` contains all parameters to initiate
// a payment to instrument of current actor
message StopInstrumentActionData {
  // account id for the deposit account
  string account_id = 1;
}

// `PayAccountActionData` contains all parameters to initiate
// a payment to account of current actor
message UpdateAccountData {
  string actor_id = 1;
  string account_type = 2;
  string account_id = 3;
  map<string, string> details = 4;
}

message UpdateActorAction {
  string actor_id = 1;
  // payee actor name, used in making timeline
  string actor_name = 2;
  // profile url to show image of the payee
  string actor_profile_url = 3;
  map<string, string> detail = 4;
}

// `StartInstrumentActionData` contains all parameters to initiate
// a start instrument
message UpdateInstrumentActionData {
  string actor_id = 1;
  string instrument_type = 2;
  string instrument_id = 3;
  map<string, string> details = 4;
}

// action data for info intent
message ShowActorActionData {
  string actor_id = 1;
  string title = 2;
}

message ShowAccountActionData {
  string actor_id = 1;
  string account_type = 2;
  string account_id = 3;
}

message ShowInstrumentActionData {
  // account id for the deposit account
  string account_id = 1;
  // type of the deposit account (e.g., FD, SD)
  accounts.Type deposit_type = 2;
  // Name of Deposit account
  string deposit_name = 3;
  // Img Url of Deposit account
  string deposit_img_url = 4;
}

message ShowHelpActionData {
  string title = 1;
}

message ShowTransactionActionData {
  string primary_actor_id = 1;
  string secondary_actor_id = 2;
  map<string, string> applied_filters = 3;
  string title = 4;
}

message BlockAccountScreen {

}

message BlockInstrumentScreen {

}




