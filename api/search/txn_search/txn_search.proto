syntax = "proto3";

package search.txn;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/search/txn";
option java_package = "com.github.epifi.gamma.api.search.txn";

// top level filter fields for txn page search
enum FilterField {
  FILTER_UNSPECIFIED = 0;
  MERCHANT = 1;
  ACCOUNT = 2;
  PEOPLE = 3;
  TIME = 4;
  LOCATION = 5;
  AMOUNT = 6;
  CATEGORY = 7;
  TYPE = 8;
}

// Type of transaction.
enum TransactionType {
  TRANSACTION_TYPE_UNSPECIFIED = 0;
  TRANSACTION_TYPE_CREDIT = 1;
  TRANSACTION_TYPE_DEBIT = 2;
}

// search before token - pagination token
message BeforeToken {
  string search_after = 1;
}

// search after
message AfterToken {
  uint32 from = 1;
  uint32 size = 2;
}


// page context to set offset, and limit in the request
// 1st request will have *limit* flag to specify number of elements from backend
// backend will send `PageContext` -- setting before_token and after_token
// next paginated request will send the same PageContext which will have 2 tokens and 2 flags
// to identify `next` and `before` token to paginate forward and backward
// read more on this - "https://hackernoon.com/guys-were-doing-pagination-wrong-f6c18a91b232"
message PageContext {
  // token to paginate backward in time
  BeforeToken before_token = 1;
  // *true* - if older results are available
  // *false* - no older results are available and client should not poll for more results
  bool has_before = 2;
  // token to paginate forward in time
  AfterToken after_token = 3;
  // *true* - response doesn't has latest records, and more records added ahead in time
  // *false* - response has all the latest records
  bool has_after = 4;
}

// message for home-page credit aggregation response
message CreditAggr {
  google.type.Money amount = 1;
}

// message for home-page debit aggregation response
message DebitAggr {
  google.type.Money amount = 1;
}

// message for home-page saved aggregation response
message SavedAggr {
  google.type.Money amount = 1;
}

message Range {
  oneof data {
    TimeRange time_range = 1;
    AmountRange amount_range = 2;
  }
}

message TimeRange {
  google.protobuf.Timestamp from_time = 1;
  google.protobuf.Timestamp to_time = 2;
}

message AmountRange {
  google.type.Money from_amount = 1;
  google.type.Money to_amount = 2;
}


// option field denote one selectable option inside a filter
message Option {
  // title of the option,
  //example = Merchant filter will have "Netflix" as one of the option
  string title = 1;
  // igf selected by the user
  bool selected = 2;
  // is visible on the ui
  bool visible = 3;
}

// Filter is comprised of all the settings that belong to single filter
message Filter {
  // top level filter field
  FilterField filter_field = 1;
  // widgetType for the client
  string widget_type = 2;
  // title of the filter to be shown on client
  string title = 3;
  // is this filter visible
  bool visible = 4;
  // all options for the filter
  repeated Option options = 5;
  // if this is range facet, it will have range values
  Range range = 6;
  // visible heading for the filter
  string heading = 7;
  // is this filter applicable
  bool applicable = 24;
  // allow client to provide search on this filter
  bool searchable = 25;
  // if client wants to show detail of the filter,
  // it will be populated by this field
  string detail = 26;
}

message RequestFilter {
  FilterField filter_field = 1;
  repeated Option options = 2;
  // if this is range facet, it will have range values
  Range range = 6;
}

// amount badge to be shown on client
// can be Credit / debit / strikethrough
enum AmountBadge {
  AMOUNT_TYP_UNSPECIFIED = 0;
  // credit badge
  CREDIT = 1;
  // debit badge
  DEBIT = 2;
  // strikethrough
  STRIKETHROUGH = 3;
  // savings
  SAVINGS = 4;
}

// single transaction element
message TransactionView {
  // bold desc / title for the payment
  string title = 1;
  // fazed desc for the payment
  string short_desc = 2;
  // amount to be shown on right
  google.type.Money amount = 3;
  // badge to be applied on amount
  AmountBadge amount_badge = 4;
  // timestamp associated with the transaction view
  // can be transaction created/updated timestamp based on use case
  // eg. for all transactions screen, this will correspond to last
  // updated timestamp
  google.protobuf.Timestamp transaction_timestamp = 5;
  // order-id to create deeplink in FE
  string order_id = 6;
  // second actor id to fetch profile url
  string second_actor_id = 7;
  // remarks to be shown on 3rd line of single txn
  string remarks = 8;
  // aa_txn_id will be not null when this transaction is related to AA
  string aa_txn_id = 9;
  // loan_activity_id will be not null when this transaction is related to Loan
  string loan_activity_id = 10;
  // credit card txn id will be populated for credit card txns.
  string cc_txn_id = 11;
}
message TransactionGroup {
  // title for transactions section
  string title = 1;
  // list of all the transactions for this section
  repeated TransactionView transactions = 2;
}

message Data {
  // page context will contain data for, page-index, offset, next-page etc
  PageContext page_context = 1;
  // all the filters for the txn page with option and other data
  repeated Filter filters = 2;
  // indicates length of all documents after filters are applied
  uint32 total_size = 3;
  // offset from which results are shown
  uint32 curr_offset = 4;
  // list of transaction sections
  // each transaction section includes - title, []transaction, link_to_view_all
  repeated TransactionGroup transactions = 5;
}


