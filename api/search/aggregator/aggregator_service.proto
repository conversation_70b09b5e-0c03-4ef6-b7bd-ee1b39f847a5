//protolint:disable MAX_LINE_LENGTH
syntax = "proto3";
package search.aggregator;

import "api/order/order.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/search/aggregator";
option java_package = "com.github.epifi.gamma.api.search.aggregator";

service Aggregator {
  // `Ingest` rpc to insert an order into the clickhouse table.
  rpc Ingest(IngestRequest) returns (IngestResponse);

  // `GetBalance` rpc to get net balance for an actor as of a specified time.
  rpc GetBalance(BalanceRequest) returns (BalanceResponse);

  // `GetCreditBalance` rpc to get net credited amount for an actor
  // (optionally we can specify a list of secondary actor ids)
  // as of a specified time.
  rpc GetCreditBalance(BalanceRequest) returns (BalanceResponse);

  // `GetDebitBalance` rpc to get net debited amount for an actor
  // (optionally we can specify a list of secondary actor ids)
  // as of a specified time.
  rpc GetDebitBalance(BalanceRequest) returns (BalanceResponse);

  // `GetAggregate` rpc to get aggregation result.
  rpc GetAggregate(AggregateRequest) returns (AggregateResponse);
}

message IngestRequest {
  order.OrderWithTransactions order_with_transactions = 1;
}

message IngestResponse {
  enum Status {
    OK = 0;
    // Internal error can come because of several reasons,
    // example database got disconnected.
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}

message BalanceRequest {
  // actor_id for actor whose balance is requested
  string primary_actor_id = 1;

  // balance as of end_timestamp
  google.protobuf.Timestamp end_timestamp = 2;

  // optional list of secondary actors ids
  // if provided, then only those transactions involving
  // both primary_actor_id and secondary_actor_id are considered
  repeated string secondary_actor_ids = 5;

  // optional list of payment instrument ids
  // if provided, then only those transactions whose
  // pi_from/pi_to belong to these pi_ids are considered
  repeated string pi_ids = 7;

}

message BalanceResponse {
  enum Status {
    OK = 0;
    // request parameters are invalid, like wrong actor ids.
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
  }

  rpc.Status status = 1;
  google.type.Money amount = 2;
}

message AggregateRequest {
  string payer_actor_id = 1;
  string payee_actor_id = 2;
  enum AggregationFunction {
    AGGREGATION_FUNCTION_UNSPECIFIED = 0;
    SUM = 1;
    COUNT = 2;
    MIN = 3;
    MAX = 4;
    AVG = 5;
  }

  AggregationFunction aggregation_function = 3;
  repeated string tags = 7;
  repeated string remarks = 9;
  repeated string pi_ids = 10;
  google.protobuf.Timestamp start_timestamp = 11;
  google.protobuf.Timestamp end_timestamp = 13;
}

message AggregateResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
    // when no rows are found for the given conditions
    NO_RESULT_FOUND = 100;
  }

  rpc.Status status = 1;
  google.type.Money amount = 2;
  // for storing count of transactions for the query
  int32 count = 3;
}

