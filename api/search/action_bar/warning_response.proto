syntax = "proto3";

package search.actionbar;

import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/search/actionbar";
option java_package = "com.github.epifi.gamma.api.search.actionbar";

// widget to show any error / warning message
// https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9200%3A108088&mode=dev
message WarningResponse {
  api.typesv2.ui.IconTextComponent.ContainerProperties container_properties = 1;
  api.typesv2.common.VisualElement header_icon = 2;
  api.typesv2.common.Text heading = 3;
  api.typesv2.common.Text subheading = 4;
  api.typesv2.ui.IconTextComponent cta = 5;
  string tab_name = 6;
}
