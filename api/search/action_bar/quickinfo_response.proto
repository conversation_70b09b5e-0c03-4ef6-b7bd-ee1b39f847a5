syntax = "proto3";

package search.actionbar;

import "api/search/txn_search/txn_search.proto";

option go_package = "github.com/epifi/gamma/api/search/actionbar";
option java_package = "com.github.epifi.gamma.api.search.actionbar";

message QuickInfoResponse {
  // title for overall response.
  // This title will be used in FE to assign block title
  // examples: "Uber Transactions", "Uber-Eats Transactions"
  string title = 1;
  // list of all the transactions for this section
  repeated search.txn.TransactionView transactions = 2;
  // bank-name for which this set of txns have to be shown
  string tab_name = 3;
  // total results available
  uint64 total = 4;
  // fip-id for the current tab
  string fip_id = 5;
}
