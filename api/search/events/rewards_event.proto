syntax = "proto3";

package search.events;

import "api/queue/consumer_headers.proto";
import "api/search/enums/enums.proto";
import "api/search/txn_search/txn_search.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/search/events";
option java_package = "com.github.epifi.gamma.api.search.events";

message OrderDetailForRewards {
  string order_id = 1;
  search.txn.AmountBadge amount_badge = 4;
}

message SearchResultUnitModified {
  enums.ResponseType resp_type = 1;
  enums.TabName tab_name = 2;
  repeated OrderDetailForRewards order_detail_for_rewards = 3;
}

message UserSearchEvent {
  string actor_id = 1;
  repeated OrderDetailForRewards order_detail_for_rewards = 2[deprecated=true];
  repeated SearchResultUnitModified search_result_units = 6;
  google.protobuf.Timestamp event_timestamp = 3;
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 4;
  // event id
  string event_id = 5;
}
