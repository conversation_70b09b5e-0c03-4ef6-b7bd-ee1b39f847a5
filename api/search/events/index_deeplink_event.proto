syntax = "proto3";

package search.events;

import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/search/events";
option java_package = "com.github.epifi.gamma.api.search.events";

message DeeplinkEvent {
  repeated string keywords = 1;
  string deeplink_name = 2;
  bool status = 3;
  float weight = 4;
  google.protobuf.Timestamp event_timestamp = 5;
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 6;
}
