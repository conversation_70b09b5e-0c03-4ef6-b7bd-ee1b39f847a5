// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package user.onboarding;

import "api/rpc/status.proto";
import "api/user/onboarding/internal/enums.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/user/onboarding";
option java_package = "com.github.epifi.gamma.api.user.onboarding";

message GetPinCodeDetailsRequest {
  // PIN code for which city/cities and state name is desired
  // Indian PIN codes are 6 digits long
  string pinCode = 1 [(validate.rules).string.len = 6];
  repeated PinCodeField field_mask = 2;
}

// Geographical area details for a PIN code
message GetPinCodeDetailsResponse {
  rpc.Status status = 1;
  repeated PinCodeDetails details = 2;
}

// Geographical area details for a PIN code
message AddPinCodeEntryRequest {
  PinCodeDetails pin_code_details = 1;
}

message AddPinCodeEntryResponse {
  rpc.Status status = 1;
}

message PinCodeDetails {
  string pinCode = 1;
  string division = 2;
  string region = 3;
  string circle = 4;
  string taluk = 5;
  string district = 6;
  string state = 7;
  string longitude = 8;
  string latitude = 9;
  // PostOffice denotes the name of the post office for the given pin code detail entry
  string post_office = 10;
}
