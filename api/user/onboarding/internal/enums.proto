//go:generate gen_sql -types=OnboardingIntent
syntax = "proto3";

package user.onboarding;

option go_package = "github.com/epifi/gamma/api/user/onboarding";
option java_package = "com.github.epifi.gamma.api.user.onboarding";

// OnboardingIntent is the intent with which a user starts onboarding. This is currently user declared when the person
// chooses a selection when ONBOARDING_INTENT_SELECTION screen is showed.
enum OnboardingIntent {
  ONBOARDING_INTENT_UNSPECIFIED = 0;
  ONBOARDING_INTENT_FEDERAL_SAVINGS_ACCOUNT = 1;
  ONBOARDING_INTENT_FI_LITE = 2;
  ONBOARDING_INTENT_CREDIT_CARD = 3;
  ONBOARDING_INTENT_PERSONAL_LOANS = 4;
  ONBOARDING_INTENT_NET_WORTH = 5;
  ONBOARDING_INTENT_DEBIT_CARD = 6;
  ONBOARDING_INTENT_WEALTH_ANALYSER = 7;
}

// OnboardingSoftIntent is the soft intent which a user can optionally choose while onboarding
// A user can select multiple soft intents upto a maximum limit
// This will only be used as soft signal to personalise the app
enum OnboardingSoftIntent {
  ONBOARDING_SOFT_INTENT_UNSPECIFIED = 0;
  ONBOARDING_SOFT_INTENT_ZERO_BALANCE_SAVINGS_ACCOUNT = 1;
  ONBOARDING_SOFT_INTENT_EXPENSE_TRACKER = 2;
  ONBOARDING_SOFT_INTENT_CASHBACK_ON_UPI = 3;
  ONBOARDING_SOFT_INTENT_INVESTMENT_TRACKER = 4;
  ONBOARDING_SOFT_INTENT_LOANS_UP_TO_5L = 5;
  ONBOARDING_SOFT_INTENT_INSTANT_SALARY = 6;
  ONBOARDING_SOFT_INTENT_LOANS_AGAINST_MUTUAL_FUNDS = 7;
  ONBOARDING_SOFT_INTENT_INTERNATIONAL_DEBIT_CARD = 8;
  ONBOARDING_SOFT_INTENT_ZERO_FOREX_FEES = 9;
  ONBOARDING_SOFT_INTENT_MUTUAL_FUNDS = 10;
  ONBOARDING_SOFT_INTENT_FIXED_DEPOSITS = 11;
  ONBOARDING_SOFT_INTENT_US_STOCKS = 12;
  ONBOARDING_SOFT_INTENT_INDIAN_STOCKS = 13;
  ONBOARDING_SOFT_INTENT_INVESTMENTS = 14;
}

// OnboardingSoftIntentCategory represents the broad level categories in which soft intents are classified into
enum OnboardingSoftIntentCategory {
  ONBOARDING_SOFT_INTENT_CATEGORY_UNSPECIFIED = 0;
  ONBOARDING_SOFT_INTENT_CATEGORY_EVERYDAY_NEEDS = 1;
  ONBOARDING_SOFT_INTENT_CATEGORY_INSTANT_LOANS = 2;
  ONBOARDING_SOFT_INTENT_CATEGORY_GLOBAL_SPENDING = 3;
  ONBOARDING_SOFT_INTENT_CATEGORY_INVEST_MONEY = 4;
}

// Direct to fi lite variants
// Users can have different experiences of fi lite depending on the variant to which the fi lite user belongs to
// NOTE - It is not necessary for every fi lite user to be tagged to a variant
enum DirectToFiLiteVariant {
  DIRECT_TO_FI_LITE_VARIANT_UNSPECIFIED = 0;
  // Upselling for core products
  DIRECT_TO_FI_LITE_VARIANT_UPSELL = 1;
  // Engagement in networth related products
  DIRECT_TO_FI_LITE_VARIANT_NETWORTH = 2;
}

enum PinCodeField {
  PIN_CODE_FIELD_UNSPECIFIED = 0;
  PIN_CODE_FIELD_DIVISION = 1;
  PIN_CODE_FIELD_REGION = 2;
  PIN_CODE_FIELD_CIRCLE = 3;
  PIN_CODE_FIELD_TALUK = 4;
  PIN_CODE_FIELD_DISTRICT = 5;
  PIN_CODE_FIELD_STATE = 6;
  PIN_CODE_FIELD_LONGITUDE = 7;
  PIN_CODE_FIELD_LATITUDE = 8;
  PIN_CODE_FIELD_POST_OFFICE = 9;
}

enum CrossValidationFailureReason {
  CROSS_VALIDATION_FAILURE_REASON_UNSPECIFIED = 0;
  CROSS_VALIDATION_FAILURE_REASON_NAME_MISMATCH = 1;
  CROSS_VALIDATION_FAILURE_REASON_DOB_MISMATCH = 2;
  CROSS_VALIDATION_FAILURE_REASON_GENDER_MISMATCH = 3;
  CROSS_VALIDATION_FAILURE_REASON_NATIONALITY_MISMATCH = 4;
  CROSS_VALIDATION_FAILURE_REASON_MOTHER_NAME_MISMATCH = 5;
  CROSS_VALIDATION_FAILURE_REASON_FATHER_NAME_MISMATCH = 6;
  CROSS_VALIDATION_FAILURE_REASON_FACE_MISMATCH = 7;
  CROSS_VALIDATION_FAILURE_REASON_PASSPORT_NUMBER_MISMATCH = 8;
  CROSS_VALIDATION_FAILURE_REASON_PASSPORT_DATE_OF_EXPIRY_MISMATCH = 9;
}
enum CrossValidationDataSource {
  CROSS_VALIDATION_DATA_SOURCE_UNSPECIFIED = 0;
  CROSS_VALIDATION_DATA_SOURCE_USER_INPUT = 1;
  CROSS_VALIDATION_DATA_SOURCE_PAN = 2;
  CROSS_VALIDATION_DATA_SOURCE_PASSPORT = 3;
  CROSS_VALIDATION_DATA_SOURCE_EMIRATES_ID = 4;
  CROSS_VALIDATION_DATA_SOURCE_QATAR_ID = 5;
}

enum CrossValidationVerdict {
  CROSS_VALIDATION_VERDICT_UNSPECIFIED = 0;
  CROSS_VALIDATION_VERDICT_SUCCESS = 1;
  CROSS_VALIDATION_VERDICT_FAIL = 2;
}

enum CrossValidationCheck {
  CROSS_VALIDATION_CHECK_UNSPECIFIED = 0;
  CROSS_VALIDATION_CHECK_USER_NAME = 1;
  CROSS_VALIDATION_CHECK_DOB = 2;
  CROSS_VALIDATION_CHECK_GENDER = 3;
  CROSS_VALIDATION_CHECK_NATIONALITY = 4;
  CROSS_VALIDATION_CHECK_FATHER_NAME = 5;
  CROSS_VALIDATION_CHECK_MOTHER_NAME = 6;
  CROSS_VALIDATION_CHECK_FACE = 7;
  CROSS_VALIDATION_CHECK_PASSPORT_NUMBER = 8;
  CROSS_VALIDATION_CHECK_PASSPORT_DATE_OF_EXPIRY = 9;
}
