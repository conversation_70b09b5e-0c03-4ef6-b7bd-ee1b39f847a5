syntax = "proto3";

package user.onboarding;

import "api/card/card.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/persistentqueue/queue_element.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/deeplink_screen_option/consent/ack_screen_options.proto";
import "api/typesv2/deeplink_screen_option/onboarding/fi_lite_screen_options.proto";
import "api/typesv2/document_details.proto";
import "api/user/onboarding/internal/enums.proto";
import "api/user/onboarding/internal/onboarding_details.proto";
import "api/user/onboarding/onboarding_details_essentials.proto";
import "api/user/onboarding/pin_code_details.proto";
import "api/user/onboarding/troubleshooting.proto";
import "api/user/user.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/user/onboarding";
option java_package = "com.github.epifi.gamma.api.user.onboarding";

service Onboarding {
  // Terminology:
  // 1. Stage: Represents a step in onboarding process - Customer Creation, Account creation etc.
  // 2. State: Current state of a stage - Initiated, Inprogress, Succuss etc.
  //
  // CheckAccountSetupStatus returns the current account setup status for given actor and vendor.
  // The api currently populates status in db on demand, i.e. the db update for the service is done when the
  // api is triggered. However we optimise the service calls by avoiding querying respective services for stages
  // which has reached terminal states.
  rpc CheckAccountSetupStatus (CheckAccountSetupStatusRequest) returns (CheckAccountSetupStatusResponse);

  // Given the PIN code of an area, this API returns a list of cities and a state to which this PIN code belongs to.
  // List of cities as a PIN code can belong to more than 1 city (though such occurrences are rare)
  // In case the PIN code belongs to only 1 city (which is the most common scenario), the list wll contain only 1 city name
  rpc GetPinCodeDetails (GetPinCodeDetailsRequest) returns (GetPinCodeDetailsResponse);

  // this rpc is used for storing pincode details
  rpc AddPinCodeEntry (AddPinCodeEntryRequest) returns (AddPinCodeEntryResponse);

  // GetNextAction evaluates and returns the next onboarding
  // action for the user. onboarding starts once email is verified and
  // carries on till user is landed on home page.
  rpc GetNextAction (GetNextActionRequest) returns (GetNextActionResponse);

  // GetDetails returns current onboarding status of the stages.
  // If the onboarding is not complete, fetches the latest states
  // of the stage next in line and returns the updated status.
  rpc GetDetails (GetDetailsRequest) returns (GetDetailsResponse);

  // GetTroubleshootingDetails returns all info required to trouble shoot stuck users.
  // The idea is to get the whole picture at one place. It's a batched API.
  // For a given stage, the API can return 3 levels of in-depth root cause analysis.
  rpc GetTroubleshootingDetails (GetTroubleshootingDetailsRequest) returns (GetTroubleshootingDetailsResponse);

  // RPC to allow a user to restart onboarding from scratch again. This RPC
  // after validating if it’s possible to safely reset a user, calls
  // necessary backend services to delete corresponding user entries.
  // E.g. user, actor & auth token entries are deleted. This way,
  // the user when trying to onboard again, will be considered a new user.
  rpc ResetUser (ResetUserRequest) returns (ResetUserResponse);

  // RPC to "reset" a stage. "Reset" will allow "re-performing" a partially/fully completed stage.
  //  For e.g. a user entered the wrong parents
  // name and would like to add them again after completing the stage.
  // The RPC first reset the dependent stages before resetting the input stage.
  // If reset for a stage is not possible PERMISSION DENIED status is returned.
  rpc ResetStage (ResetStageRequest) returns (ResetStageResponse);

  // RPC to register a user for VKYC
  // This will collect data required for registration and call vkyc backend service
  rpc RegisterUserForVKYC (RegisterUserForVKYCRequest) returns (RegisterUserForVKYCResponse);

  // GetOnboardedUser returns user details along with onboarding completion timestamp, for a given user
  // identifier
  rpc GetOnboardedUser (GetOnboardedUserRequest) returns (GetOnboardedUserResponse);

  // UpdateStage forcefully updates the status of a stage. Use with caution.
  rpc UpdateStage (UpdateStageRequest) returns (UpdateStageResponse);

  // GetUnonboardedUsers fetches all the currently unonboarded users
  rpc GetUnonboardedUsers (GetUnonboardedUsersRequest) returns (GetUnonboardedUsersResponse);

  // rpc to check user entered debit card name with KYC and PAN name using the name match algorithm
  // and if name match passes then updates debit card name in user
  rpc DebitCardNameCheck (DebitCardNameCheckRequest) returns (DebitCardNameCheckResponse);

  // rpc to reset retries for debit card name match.
  // This would allow users to retry debit card name match after the initial retries are exhausted and they reach
  // out via CX team.
  rpc ResetDebitCardNameRetries (ResetDebitCardNameRetriesRequest) returns (ResetDebitCardNameRetriesResponse);

  // rpc to fetch onboarded users after/before a time
  // this is a paginated rpc
  // If fromTime and ToTime both are passed, all the onboarded users
  // between this time are returned
  // if only from time is passed onboarded users after the from time are returned
  // if only toTime is passed onboarded users after the toTime are returned
  // if both from and to time are not passed then returns invalid argument
  // NOTE - max page size supported is 50
  rpc GetOnboardedUsers (GetOnboardedUsersRequest) returns (GetOnboardedUsersResponse);

  // rpc to create new onboarding entry for a user
  rpc StartOnboarding (StartOnboardingRequest) returns (StartOnboardingResponse);

  // rpc to fetch the elements in the persistent queue based on a specific payload type
  // The max limit supported is 100, default limit is 20 if no limit is passed
  rpc GetQueueElements (GetQueueElementsRequest) returns (GetQueueElementsResponse);

  // rpc to delete elements from the persistent queue
  rpc DeleteQueueElement (DeleteQueueElementRequest) returns (DeleteQueueElementResponse);

  rpc CountQueueElements (CountQueueElementsRequest) returns (CountQueueElementsResponse);

  // rpc to update the verdict on pan name mis-match during onboarding
  rpc UpdatePanNameVerdict (UpdatePanNameVerdictRequest) returns (UpdatePanNameVerdictResponse);

  // rpc to get minimal onboarding details
  // this rpc fetches only minimal onboarding details from the cache. (Populates in cache if not found)
  rpc GetOnboardingDetailsMin (GetOnboardingDetailsMinRequest) returns (GetOnboardingDetailsMinResponse);

  // ProcessRiskVerdict is an API to block/unblock onboarding journey of a user based on liveness/risk review
  rpc ProcessRiskVerdict (ProcessRiskVerdictRequest) returns (ProcessRiskVerdictResponse);

  // UpdateFiLiteAccessibility rpc is used to updates the status of Fi lite accessibility of the user
  // If this rpc is called then user will be redirected to home and will always land on home if user reopens the app
  rpc UpdateFiLiteAccessibility (UpdateFiLiteAccessibilityRequest) returns (UpdateFiLiteAccessibilityResponse);

  // GetFeatureDetails rpc will be used to fetch details regarding the fi lite status of an actor and other relevant fi lite details
  rpc GetFeatureDetails (GetFeatureDetailsRequest) returns (GetFeatureDetailsResponse);

  // SetOnboardingIntent rpc will be used to set the onboarding journey for a user based
  // on the intent selected upon prompt.
  rpc SetOnboardingIntent (SetOnboardingIntentRequest) returns (SetOnboardingIntentResponse);

  // GetOnboardingSoftIntentOptions rpc will be used to get the soft intent options for a user
  rpc GetOnboardingSoftIntentOptions (GetOnboardingSoftIntentOptionsRequest) returns (GetOnboardingSoftIntentOptionsResponse);

  // SetOnboardingSoftIntent rpc will be used to set the soft intent choices for a user
  // These soft intent choices will further be used to personalise their in-app experience
  rpc SetOnboardingSoftIntent (SetOnboardingSoftIntentRequest) returns (SetOnboardingSoftIntentResponse);

  // PerformStage rpc will be used by the callers to run a particular stage order for a user based on feature up until the
  // stage which is passed in the request
  // The rpc will return a deeplink if needed and will return the stage status and the current stage
  // NextAction should be preferred over StageState, since a deeplink needs to be shown
  // If NextAction is nil then status needs to be checked, SKIPPED/SUCCESS/FAILURE essentially are terminal states
  rpc PerformStage (PerformStageRequest) returns (PerformStageResponse);

  // rpc to serve the intent selection deeplink as this deeplink is attached to the onboarding config
  rpc GetIntentSelectionDeeplink (GetIntentSelectionDeeplinkRequest) returns (GetIntentSelectionDeeplinkResponse);

  // UploadPassport stores passed passport image in s3. It also updates the passport verification onboarding stage
  // metadata depending on what side of the passport was uploaded.
  // This is only to be triggered from passport verification onboarding stage.
  rpc UploadPassport (UploadPassportRequest) returns (UploadPassportResponse);

  // GetFeatureLifecycle returns all the details pertaining to the lifecycle of a feature for a user
  // It returns the following details for each feature for a user -
  // 1. intent selection info - what is the intent selection info of the feature
  // 2. eligibility status - what is the eligibility status of the feature
  // 3. activation status - what is the activation status of the feature
  // Use case - Home, comms and other in-app properties require intent, eligibility and activation to determine the best cross-selling options
  rpc GetFeatureLifecycle (GetFeatureLifecycleRequest) returns (GetFeatureLifecycleResponse);

  // FetchPassport fetches the passport data for a user
  rpc FetchPassport (FetchPassportRequest) returns (FetchPassportResponse);

  // ProcessUserAck: Listens for acknowledgements from the client
  // For example, when a user interacts with a CTA or banner, the client can send an acknowledgement to the backend
  rpc ProcessUserAck (ProcessUserAckRequest) returns (ProcessUserAckResponse);

  // RPC to add queue elements to persistent queue, it takes in the payload to be added to the queue
  rpc AddQueueElement (AddQueueElementRequest) returns (AddQueueElementResponse);

  // RPC to verify the passport which is issued outside india
  rpc VerifyGlobalIssuedPassport (VerifyGlobalIssuedPassportRequest) returns (VerifyGlobalIssuedPassportResponse);

  // RPC to update cross validation manual review info for NR users
  rpc ProcessCrossValidationManualReview (ProcessCrossValidationManualReviewRequest) returns (ProcessCrossValidationManualReviewResponse);

  // RPC to get cross validation data for manual review
  rpc GetDataForCrossValidationManualReview (GetDataForCrossValidationManualReviewRequest) returns (GetDataForCrossValidationManualReviewResponse);

  // RPC to update passport manual review status for the passport issued outside india for NR users
  rpc ProcessPassportManualReview (ProcessPassportManualReviewRequest) returns (ProcessPassportManualReviewResponse);
}

message ProcessPassportManualReviewRequest {
  string actor_id = 1;
  Verdict verdict = 2;
  string agent_email = 3;
  string remarks = 4;
  // Screenshots/proof images of the PSK portal
  api.typesv2.common.Image review_proof_image = 5;
}

message ProcessPassportManualReviewResponse {
  rpc.Status status = 1;
}

message ProcessCrossValidationManualReviewRequest {
  string actor_id = 1;
  CrossValidationVerdict verdict = 2;
  string agent_email = 3;
  string remarks = 4;
}

message ProcessCrossValidationManualReviewResponse {
  rpc.Status status = 1;
}

message GetDataForCrossValidationManualReviewRequest {
  string actor_id = 1;
}

message GetDataForCrossValidationManualReviewResponse {
  message DataSourceValuePairList {
    repeated DataSourceValuePair data_source_value_pairs = 1;
  }
  message DataSourceValuePair {
    // source maps to CrossValidationDataSource enum: PAN, PASSPORT, EMIRATES_ID, etc.
    string data_source = 1;
    // If data is for an image, value wil be base64 value of the image
    string value = 2;
  }
  rpc.Status status = 1;
  // Key maps to CrossValidationCheck enum: USER_NAME, DOB, FACE_IMAGE, etc.
  map<string, DataSourceValuePairList> cross_validation_data = 2;
}

message VerifyGlobalIssuedPassportRequest {
  string actor_id = 1;
  // passport_identifier is mutually exclusive field required to verify the passport
  oneof passport_identifier {
    // file number of old passport issued inside india
    // old passport file number and old passport number(receive from new passport back OCR) use to validate the passport
    string file_number = 2;
    // application reference number of new passport issued outside india
    // ARN is received as a receipts or acknowledgement number on renewal of old passport
    string arn = 3;
  }
}

message VerifyGlobalIssuedPassportResponse {
  // Apart from common GRPC error code (INVALID_ARGUMENT, NOT_FOUND, DATA_LOSS etc..)
  // Below are the custom error codes
  enum Status {
    OK = 0;
    PASSPORT_NUMBER_MISMATCH = 101;
    PASSPORT_NAME_MISMATCH = 102;
  }
  rpc.Status status = 1;
}

message FetchPassportRequest {
  string actor_id = 1;
}

message FetchPassportResponse {
  rpc.Status status = 1;
  api.typesv2.PassportData passport = 2;
}

message UpdatePanNameVerdictRequest {
  string actor_id = 1;
  PanManualReviewAnnotation annotation = 3;
}

message UpdatePanNameVerdictResponse {
  rpc.Status status = 1;
}

message ResetDebitCardNameRetriesRequest {
  string actor_id = 1;
}

message ResetDebitCardNameRetriesResponse {
  enum Status {
    OK = 0;

    FAILED_PRECONDITION = 9;
  }
  rpc.Status status = 1;
}

message CheckAccountSetupStatusRequest {
  // Bank vendor with whom the user holds the account
  vendorgateway.Vendor vendor = 1;
  // Actor id of the user
  string actor_id = 2;
  // onboarding id
  string onboarding_id = 3;
}

enum AccountSetupStage {
  // Account setup stage un-specified
  ACCOUNT_SETUP_STAGE_UNSPECIFIED = 0;
  // Stage represents creation of customer with vendor
  ACCOUNT_SETUP_CUSTOMER_CREATION = 1;
  // Stage represents creation of account with vendor
  ACCOUNT_SETUP_ACCOUNT_CREATION = 2;
  // Stage represents updating shipping address of the user with vendor
  ACCOUNT_SETUP_SHIPPING_ADDRESS_UPDATE = 5;
  // Stage represents creation of card with vendor
  ACCOUNT_SETUP_CARD_CREATION = 3;
  // Stage represents upi creation
  ACCOUNT_SETUP_UPI_CREATION = 4;
  // Stage represent profile update
  ACCOUNT_SETUP_UPDATE_PROFILE_DETAILS = 6;
  // Stage maps to NRO account creation in onboarding
  ACCOUNT_SETUP_NRO_ACCOUNT_CREATION = 7;
}

enum AccountSetupState {
  STATUS_UNSPECIFIED = 0;
  STAGE_INITIATED = 1;
  STAGE_IN_PROGRESS = 2;
  STAGE_SUCCESS = 3;
  STAGE_FAILED = 4;
}

message CheckAccountSetupStatusResponse {
  // This value is between 0 to 100. It's used on the client side to display how much
  // the account setup process is complete.
  int32 account_setup_progress_percent = 1 [deprecated = true];
  // Information related to user's savings account
  AccountInformation account_info = 2;
  // Information related to user's card
  repeated CardInformation card_info = 3;
  // rpc status
  rpc.Status status = 4;
  // upi details
  VPA vpa = 5;
  // Next action that the user should perform
  frontend.deeplink.Deeplink next_action = 6;
  // Account stages, and their progress
  map<string, AccountSetupState> account_setup_progress_map = 7;
  // is user stuck on the stage for more than a certain duration
  bool is_user_stuck = 8;
}

message AccountInformation {
  // Account number of the savings account
  string account_number = 1;
  // account Id
  string account_id = 2;
  // denotes if the account is primary or not
  bool is_primary = 3;
  // name of the partner bank
  string partner_bank_name = 4;
  // partner bank icon url
  string bank_icon_url = 5;
}

message CardInformation {
  // Card id of the user debit card
  string card_id = 1;
  // Card information of the user debit card
  card.BasicCardInfo basic_card_info = 2;
}

message VPA {
  string upi_id = 1;
}

message GetNextActionRequest {
  string actor_id = 1;
  // returns next action depending only on the next action decision cache. If onboarding is not completed, it returns GET_NEXT_ONBOARDING_ACTION_API, thus
  // prescribing the client to call GetNextOnboardingAction FE RPC
  api.typesv2.common.BooleanEnum force_cache = 2;

  // Feature for which the next action needs to be fetched
  Feature onboarding_feature = 3;

  api.typesv2.deeplink_screen_option.onboarding.FeatureOnboardingEntryPoint feature_onboarding_entry_point = 4;
}

message GetNextActionResponse {
  rpc.Status status = 1;

  // Next action that the user should perform
  frontend.deeplink.Deeplink next_action = 2;
}

message GetDetailsRequest {
  string actor_id = 1;
  vendorgateway.Vendor vendor = 2;
  // Flag to control if in response we need cached data. If set to true, the orchestrator is not run & data is directly returned from the DB.
  // Optimises for availability & speed over consistency; data can be stale.
  bool cached_data = 3;
}

message GetDetailsResponse {
  rpc.Status status = 1;
  onboarding.OnboardingDetails details = 2;
}

message GetTroubleshootingDetailsRequest {
  // max 100 actor ids allowed
  repeated string actor_ids = 1;
}

message GetTroubleshootingDetailsResponse {
  rpc.Status status = 1;
  repeated StageTroubleshootingDetails details = 2;
}

message ResetUserRequest {
  string actor_id = 1;
  user.DeletionDetails deletion_details = 2;
}

message ResetUserResponse {
  enum Status {
    // Success
    OK = 0;

    // Validations failed. The user can not be reset.
    PERMISSION_DENIED = 7;

    MISSING_CONSENT = 101;
  }
  rpc.Status status = 1;
}

message ResetStageRequest {
  string actor_id = 1;

  // stage to reset
  user.onboarding.OnboardingStage stage = 2;
}

message ResetStageResponse {
  enum Status {
    // Success
    OK = 0;

    // Validations failed. The stage can not be reset.
    PERMISSION_DENIED = 7;
  }
  rpc.Status status = 1;
}

message RegisterUserForVKYCRequest {
  string actor_id = 1;
}

message RegisterUserForVKYCResponse {
  rpc.Status status = 1;
}

message GetOnboardedUserRequest {
  oneof Identifier {
    api.typesv2.common.PhoneNumber phone_number = 1;

    string email = 2;
  }
}

message GetOnboardedUserResponse {
  enum Status {
    OK = 0;
    // user not found for the given requested params
    NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // user record exists for a given identifier but is not onboarded
    USER_NOT_ONBOARDED = 100;
  }

  // RPC status code Code_NOT_FOUND refers to user
  // not present in system with given identifier
  rpc.Status status = 1;

  user.User user = 2;

  // time stamp when the user is successfully onboarded
  google.protobuf.Timestamp onboarded_at = 3;

  // actor id for the onboarded user
  string actor_id = 4;
}

message UpdateStageRequest {
  string actor_id = 1;

  // stage to update
  user.onboarding.OnboardingStage stage = 2;

  // the state of the stage will be force updated to this
  onboarding.OnboardingState new_state = 3;
}

message UpdateStageResponse {
  rpc.Status status = 1;

  // new updated details
  onboarding.OnboardingDetails onboarding_details = 2;
}

message GetUnonboardedUsersRequest {
  google.protobuf.Timestamp from_time = 1;
  google.protobuf.Timestamp to_time = 2;
  repeated user.onboarding.OnboardingStage stage_list = 3;
}

message GetUnonboardedUsersResponse {
  rpc.Status status = 1;

  message UnonboardedUser {
    string actor_id = 2;
    string user_id = 3;
    user.onboarding.OnboardingStage stage = 4;
    string stuck_duration = 5;
  }

  repeated UnonboardedUser unonboardedusers = 2;
}

message DebitCardNameCheckRequest {
  string actor_id = 1;

  // User entered debit card name
  api.typesv2.common.Name debit_card_name = 2;

  // Flag to skip name check
  bool skipNameCheck = 3;
}

message DebitCardNameCheckResponse {
  enum Status {
    OK = 0;

    NAME_CHECK_FAILED = 100;
  }
  rpc.Status status = 1;
}

enum manualScreeningDecision {
  MANUAL_SCREENING_DECISION_UNSPECIFIED = 0;
  ACCEPT = 1;
  REJECT = 2;
}

message GetOnboardedUsersRequest {
  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp from_time_stamp = 1;

  // timestamp to which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp to_time_stamp = 2;

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // max page size supported is 50
  int32 page_size = 3 [(validate.rules).int32 = {gte: 1, lte: 50}];

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 users starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 offset = 4 [(validate.rules).int32.gte = 0];
}

message GetOnboardedUsersResponse {
  enum Status {
    OK = 0;
    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;

  // starting with a few fields
  // will add more fields as and when required
  message OnboardedUser {
    // user id of the onboarded user
    string user_id = 2;
    // actor id of the onboarded user
    string actor_id = 3;
    // timestamp at which the user completed onboarding
    google.protobuf.Timestamp onboarded_at = 4;
  }

  // list of onboarded users
  repeated OnboardedUser onboarded_users = 2;
}

message GetQueueElementsRequest {
  // type of payload required from the queue
  persistentqueue.PayloadType payload_type = 1;
  // Number of elements required from the queue, max supported is 100
  int32 limit = 2;
  int32 page_num = 3;
  // From time for date filter
  google.protobuf.Timestamp from_time = 4;
  // To time for date filter
  google.protobuf.Timestamp to_time = 5;
  // By default elements in response are in LIFO order
  // is_fifo=true will return elements in FIFO order
  // deprecated in favour of sortByColumn field which should be used for specifying sorting details going forward
  bool is_fifo = 6 [deprecated = true];
  // sortByColumns accepts array of elements, in order for columns to be sorted on
  // currently the array length is limited to two
  persistentqueue.SortByColumns sortByColumns = 7;
}

message GetQueueElementsResponse {
  rpc.Status status = 1;
  repeated QueueElement elements = 2;
}

message AddQueueElementRequest {
  persistentqueue.Payload element = 1;
}

message AddQueueElementResponse {
  rpc.Status status = 1;
  string element_id = 2;
}

message QueueElement {
  string id = 1;
  oneof payload {
    persistentqueue.LivenessReview liveness_review = 2;
    persistentqueue.FacematchReview facematch_review = 3;
    persistentqueue.LivenessSampleReview liveness_sample_review = 4;
    persistentqueue.AFULivenessReview afu_liveness_review = 5;
    persistentqueue.AFUFacematchReview afu_facematch_review = 6;
    persistentqueue.NamematchReview namematch_review = 7;
    persistentqueue.FacematchSampleReview facematch_sample_review = 8;
    persistentqueue.PanNameSampleReview pan_name_sample_review = 9;
    persistentqueue.LivenessAndFacematchReview liveness_and_facematch_review = 10;
    persistentqueue.AFULivenessAndFacematchReview afu_liveness_and_facematch_review = 11;
    persistentqueue.UserReview user_review = 12;
    persistentqueue.StockGuardianCkycDocuments stock_guardian_ckyc_documents = 13;
  }
}

message DeleteQueueElementRequest {
  string id = 1;
  string actor_id = 2;
  persistentqueue.PayloadType payload_type = 3;
}

message DeleteQueueElementResponse {
  rpc.Status status = 1;
}

message CountQueueElementsRequest {
  persistentqueue.PayloadType payload_type = 1;
}

message CountQueueElementsResponse {
  rpc.Status status = 1;
  int64 count = 2;
}

message StartOnboardingRequest {
  string actor_id = 1;
}

message StartOnboardingResponse {
  rpc.Status status = 1;
  string onboarding_id = 2;
}

message GetOnboardingDetailsMinRequest {
  string actor_id = 1;
}

message GetOnboardingDetailsMinResponse {
  rpc.Status status = 1;
  OnboardingDetailsMin onboarding_details_min = 2;
}

message ProcessRiskVerdictRequest {
  string actor_id = 1;
  // case id of risk case
  string case_id = 2;
  Verdict verdict = 3;
}

message ProcessRiskVerdictResponse {
  rpc.Status status = 1;
}

message UpdateFiLiteAccessibilityRequest {
  string actor_id = 1;
  FiLiteSource source = 2;
  api.typesv2.common.BooleanEnum is_enabled = 3;
}

message UpdateFiLiteAccessibilityResponse {
  rpc.Status status = 1;
}

message GetFeatureDetailsRequest {
  string actor_id = 1;
  Feature feature = 2;
}

message GetFeatureDetailsResponse {
  rpc.Status status = 1;
  bool is_fi_lite_user = 2;
  FeatureInfo feature_info = 3;
}

message SetOnboardingIntentRequest {
  string actor_id = 1;
  onboarding.OnboardingIntent intent = 2;
  onboarding.IntentSelectionEntryPoint entry_point = 3;
}

message SetOnboardingIntentResponse {
  rpc.Status status = 1;
}

message GetOnboardingSoftIntentOptionsRequest {
  string actor_id = 1;
  string session_id = 2;
}

message GetOnboardingSoftIntentOptionsResponse {
  rpc.Status status = 1;

  repeated SoftIntentCollection soft_intent_collections = 2;
  message SoftIntentCollection {
    onboarding.OnboardingSoftIntentCategory category = 1;
    repeated onboarding.OnboardingSoftIntent intents = 2;
  }
}

message SetOnboardingSoftIntentRequest {
  string actor_id = 1;
  repeated onboarding.OnboardingSoftIntent soft_intents = 2;
  onboarding.SoftIntentSelectionEntryPoint entry_point = 3;
}

message SetOnboardingSoftIntentResponse {
  rpc.Status status = 1;
}

message PerformStageRequest {
  string actor_id = 1;
  OnboardingStage stage = 2;
  Feature feature = 3;
}

message PerformStageResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
  OnboardingState stage_state = 3;
  OnboardingStage current_stage = 4;
}

message GetIntentSelectionDeeplinkRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string entry_point = 2;
}

message GetIntentSelectionDeeplinkResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink intent_selection_deeplink = 2;
}

// IntentSelectionEntryPoint denotes where the user entered the intent selection screen from
enum IntentSelectionEntryPoint {
  INTENT_SELECTION_ENTRY_POINT_UNSPECIFIED = 0;
  // User entered intent selection screen via the deeplink returned from onboarding stage processor
  INTENT_SELECTION_ENTRY_POINT_ONBOARDING_INTENT_SELECTION_STAGE = 1;
  INTENT_SELECTION_ENTRY_POINT_SA_INTRO_SCREEN = 2;
  INTENT_SELECTION_ENTRY_POINT_PL_INTRO_SCREEN = 3;
  INTENT_SELECTION_ENTRY_POINT_CC_INTRO_SCREEN = 4;
  INTENT_SELECTION_ENTRY_POINT_CC_INELIGIBLE = 5;
  INTENT_SELECTION_ENTRY_POINT_PL_ACQ_TO_LEND_LANDING_SCREEN = 6;
  INTENT_SELECTION_ENTRY_POINT_PL_ELIGIBILITY_NO_OFFER_AVAILABLE_SCREEN = 7;
  INTENT_SELECTION_ENTRY_POINT_DC_INTRO_SCREEN = 8;
}

// IntentSelectionEntryPoint denotes where the user entered the intent selection screen from
enum SoftIntentSelectionEntryPoint {
  SOFT_INTENT_SELECTION_ENTRY_POINT_UNSPECIFIED = 0;
  // User entered soft intent selection screen via the deeplink returned from onboarding stage processor
  SOFT_INTENT_SELECTION_ENTRY_POINT_ONBOARDING_SOFT_INTENT_SELECTION_STAGE = 1;
  // User entered soft intent selection screen via the home banner
  SOFT_INTENT_SELECTION_ENTRY_POINT_HOME_BANNER = 2;
}

message UploadPassportRequest {
  string actor_id = 1;
  string image_base64 = 2;
  PassportSide passport_side = 3;
  string file_name = 4;
}

enum PassportSide {
  PASSPORT_SIDE_UNSPECIFIED = 0;
  PASSPORT_SIDE_FRONT = 1;
  PASSPORT_SIDE_BACK = 2;
}

message UploadPassportResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetFeatureLifecycleRequest {
  string actor_id = 1;
  // list of features for which lifecycle data is required
  repeated Feature features = 2;
  // flag to control if in response we need cached data
  // if false, orchestrator may be run to fetch data
  bool want_cached_data = 3;
}

message GetFeatureLifecycleResponse {
  rpc.Status status = 1;
  // map of request features vs their lifecycle data
  map<string, FeatureLifecycle> feature_lifecycle_map = 2;
  // onboarding details of the user
  onboarding.OnboardingDetails onboarding_details = 3;
}

message ProcessUserAckRequest {
  api.typesv2.deeplink_screen_option.consent.AckType ack_type = 1;
  // id of the nudge for which acknowledgement was triggered
  string ack_id = 2;
}

message ProcessUserAckResponse {
  rpc.Status status = 1;
  frontend.deeplink.Deeplink next_action = 2;
}
