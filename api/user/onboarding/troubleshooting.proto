syntax = "proto3";

package user.onboarding;

option go_package = "github.com/epifi/gamma/api/user/onboarding";
option java_package = "com.github.epifi.gamma.api.user.onboarding";

import "api/kyc/kyc.proto";
import "api/kyc/internal/kyc_attempt.proto";
import "api/kyc/liveness.proto";
import "api/vendorgateway/openbanking/customer/service.proto";
import "google/protobuf/timestamp.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/user/onboarding/internal/onboarding_details.proto";
import "api/vendorstore/vendorstore.proto";

// StageTroubleshootingDetails gathers all info required by a human
// to find the lowest level reason for a user to be stuck on a stage.
// The goal is that no other source of information should be required
// to debug and analyse a user's stage.
message StageTroubleshootingDetails {
  string actor_id = 1;
  // The stage the user is stuck on
  OnboardingStage stage = 2;
  // stage status and last updated at
  OnboardingState state = 3;
  // time at which the state was last updated
  google.protobuf.Timestamp updated_at = 10;
  // state in DB
  OnboardingDetails onb = 4;

  // 3 levels of root cause analysis for display
  string L1 = 5;
  string L2 = 6;
  string L3 = 7;

  // Advice for the CX agent for a user reaching out. This advice is based on the current stage.
  string advice = 8;

  // what screen is the user being shown
  frontend.deeplink.Deeplink next_action = 9;

  oneof stage_debug_info {
    // INITIATE_CKYC
    InitiateCkycDebugInfo initiate_ckyc = 30;
    // KYC_AND_LIVENESS_COMPLETION
    KycLivenessDebugInfo kyc_liveness = 31;
    // PAN_NAME_CHECK
    PanNameCheckDebugInfo pan_name_check = 32;
    // KYC_DEDUPE_CHECK
    KYCDedupeDebugInfo kyc_dedupe_check = 33;
    // EKYC_NAME_DOB_VALIDATION
    EkycNameDobValidationDebugInfo ekyc_name_dob_validation = 34;
    // CONFIRM_CARD_MAILING_ADDRESS
    CardAddressDebugInfo confirm_card_address = 35;
    // DEVICE_REGISTRATION
    DeviceRegistrationDebugInfo device_registration = 36;
    // CUSTOMER_CREATION
    CustomerCreationDebugInfo customer_creation = 37;
    // ACCOUNT_CREATION
    AccountCreationDebugInfo account_creation = 38;
    // DEDUPE_CHECK
    DedupeDebugInfo dedupe_check = 39;

    // Must have pending stages
    // SHIPPING_ADDRESS_UPDATE
    // CARD_CREATION
  }

  // Troubleshooting advice for employees and internal folks stuck on non-prod env.
  string non_prod_advice = 11;
}

message InitiateCkycDebugInfo {
  vendorgateway.openbanking.customer.DedupeStatus dedupe_status = 1;
}

message KycLivenessDebugInfo {
  kyc.KYCLevel kyc_level = 2;

  // To identify the type of KYC in progress
  kyc.KycType kyc_type = 4;

  // This field is used by caller to evaluate the next action for the client.
  kyc.KycStatus kyc_status = 5;

  // Liveness/FM status
  kyc.LivenessStatus liveness_status = 6;

  // Time by which KYC data is expected to expire.
  // This field is set only when with_expiry is set to true
  // in the CheckKYCStatusRequest.
  google.protobuf.Timestamp expiry = 10;

  // it's populated if KycStatus is ERRORED
  kyc.FailureType failure_reason = 11;

  // we allow user to update their name or dob in case of a mismatch to retry
  // kyc data validation. this flag represents if user has exhausted all retries.
  bool data_validation_max_retries = 12;

  // this field is used by auto resolve job to check if riskops rejected liveness
  bool is_liveness_manually_failed = 13;

  // this field is used by auto resolve job to check if riskops rejected facematch
  bool is_facematch_manually_failed = 14;

  // this field is used by auto resolve job to check if liveness passed
  bool is_liveness_passed = 15;
}

message PanNameCheckDebugInfo {
  // Inhouse DS name match API decision & score
  bool inhouse_name_match_passed = 1;
  float inhouse_name_match_score = 2;

  // Old(Already being used) name match API decision & score
  bool old_name_match_passed = 3;
  float old_name_match_score = 4;

  PanManualReviewAnnotation manual_review_annotation = 5;
}

message KYCDedupeDebugInfo {
  vendorgateway.openbanking.customer.DedupeStatus kyc_dedupe_status = 1;
  int32 kyc_dedupe_retry_count = 2;
}

message DedupeDebugInfo {
  vendorgateway.openbanking.customer.DedupeStatus dedupe_status = 1;
}

message EkycNameDobValidationDebugInfo {
  // API retries for tracking errored vendor API responses
  int32 retries = 1;
  // Validation failure description
  string failure_desc = 2;
  // API raw response
  string raw_response = 3;
}

message CardAddressDebugInfo {
  // Inhouse DS name match API decision & score
  bool inhouse_name_match_passed = 1;
  float inhouse_name_match_score = 2;

  // Old(Already being used) name match API decision & score
  bool old_name_match_passed = 3;
  float old_name_match_score = 4;

  // Number of retries for name check.
  // User will be redirected to error screen if name check fails
  // for more than a given threshold.
  int32 name_check_retry_count = 5;
}

message DeviceRegistrationDebugInfo {
  // Total tries of RegisterDevice client API by the user
  int32 register_device_api_attempts = 1;

  // vendor responses (limit 10)
  repeated vendorstore.VendorResponse vendor_responses = 2;
}


message CustomerCreationDebugInfo {
  vendorgateway.openbanking.customer.DedupeStatus dedupe_status = 1;
  // vendor responses (limit 5)
  repeated vendorstore.VendorResponse init_vendor_responses = 2;
  repeated vendorstore.VendorResponse inquiry_vendor_responses = 3;
  repeated vendorstore.VendorResponse callback_vendor_responses = 4;
}

message AccountCreationDebugInfo {
  vendorgateway.openbanking.customer.DedupeStatus dedupe_status = 1;
  // vendor responses (limit 5)
  repeated vendorstore.VendorResponse init_vendor_responses = 2;
  repeated vendorstore.VendorResponse inquiry_vendor_responses = 3;
  repeated vendorstore.VendorResponse callback_vendor_responses = 4;
}
