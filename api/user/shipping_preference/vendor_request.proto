syntax = "proto3";

package shipping_preference;

option go_package = "github.com/epifi/gamma/api/user/shipping_preference";
option java_package = "com.github.epifi.gamma.api.user.shipping_preference";

import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

message ShippingPreferenceVendorRequest {
  // Primary identifier to the request made for updating shipping address.
  // Any retry-able error is generally associated with
  // same set of request parameters and doesn't change the attempt id.
  string id = 1;

  // Vendor to which th request is sent
  vendorgateway.Vendor vendor = 2;

  // Identifier for all preference details. The details are persiste in shipping_preferences table
  // preference_id here is named 'id' in the shi
  string preference_id = 3;

  // The state of the request in the State Machine.
  // A sample state machines:
  // case 1: QUEUED -> INITIATED -> SUCCESS
  // Happy case. Request was successful at the vendor.
  //
  // QUEUED -> MANUAL_INTERVENTION
  // Request was retried multiple times till retry limit was reached.
  // All the retry attempts to initiate the creation request failed.
  //
  // QUEUED -> INITIATED -> FAILED
  // Request was initiated successfully but failed due to an error.
  //
  // QUEUED -> INITIATED -> MANUAL_INTERVENTION
  // Request was initiated successfully. All the tries to fetch the status
  // failed.
  RequestState status = 4;

  // Unique identifier to the request made for updating shipping address for the vendor.
  // Any followup on state changes is done using
  // this id as a reference. Any retry-able error is generally associated with
  // same set of request parameters and doesn't change the request id.
  string request_id = 5;

  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  google.protobuf.Timestamp deleted_at = 12;
}

enum RequestState {
  REQUEST_STATE_UNSPECIFIED = 0;
  // QUEUED - request is queued to the retry queue and will be processed shortly
  QUEUED = 1;
  // INITIATED - request is successfully initiated with the partner bank. Awaiting update.
  INITIATED = 2;
  // SUCCESS - request was successfully processed at the partner bank.
  SUCCESS = 3;
  // FAILED - request failed at the partner bank. Denotes a non-retry-able failure.
  FAILED = 4;
  // MANUAL_INTERVENTION- System has exhausted all the retries post transient errors so this needs attention from a human.
  MANUAL_INTERVENTION = 5;
}
