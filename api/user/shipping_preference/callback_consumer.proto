syntax = "proto3";

package shipping_preference;

import "api/queue/consumer_headers.proto";
import "api/vendorgateway/vendor.proto";
import "validate/validate.proto";
import "api/user/shipping_preference/vendor_request.proto";
import "api/vendorgateway/vendor_status.proto";

option go_package = "github.com/epifi/gamma/api/user/shipping_preference";
option java_package = "com.github.epifi.gamma.api.user/shipping_preference";

// Defines the GRPC service to process shipping preferences updates related callback from the partner bank.
// This GRPC service is registered with queue subscriber and RPC method will be invoked by the consumer
// on receiving an event.
// The relevant events are pushed to the corresponding queues after the callback is received by the Vendor notification
// service. Vendor notification layer does very minimal work to type the callback into the consumer protos of this
// service.
service ShippingPreferenceCallbackConsumer {
  // RPC updates the state of a shipping address update request.
  // This method is invoked by queue subscriber to consume final response from vendor's service.
  rpc ProcessShippingAddressUpdateCallback (ProcessShippingAddressUpdateCallbackRequest) returns (ProcessShippingAddressUpdateCallbackResponse) {}
}

message ProcessShippingAddressUpdateCallbackRequest {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;

  vendorgateway.Vendor vendor = 2 [(validate.rules).enum = {not_in: [0]}];

  // Request ID of the update shipping address request for which the call back is initiated
  string request_id = 3 [(validate.rules).string.min_len = 1];

  // state of the update shipping address request. The values allowed should be either success or failure.
  shipping_preference.RequestState state = 4 [(validate.rules).enum = {not_in: [0]}];

  vendorgateway.VendorStatus vendor_status = 5;
}

message ProcessShippingAddressUpdateCallbackResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}
