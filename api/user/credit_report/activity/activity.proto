// This files defines request contracts for various workflow types
syntax = "proto3";

package preapprovedloan.workflow;

import "api/celestial/activity/header.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/name.proto";
import "api/user/credit_report/internal/credit_report_download.proto";

option go_package = "github.com/epifi/gamma/api/user/credit_report/activity";
option java_package = "com.github.epifi.gamma.api.user.credit_report.activity";

message InitiateOtpVerificationRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string credit_report_download_id = 2;
}

message InitiateOtpVerificationResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // client request id to download credit report
  string client_request_id = 2;
  // status of the download process after completion of InitiateOtpVerification activity.
  credit_report.CreditReportDownloadStatus process_status = 3;
}

message CheckAuthFlowProcessStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // client request id to download credit report
  string client_request_id = 2;
}

message CheckAuthFlowProcessStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdateCreditReportStatusBasedOnAuthFlowStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string credit_report_download_id = 2;
}

message UpdateCreditReportStatusBasedOnAuthFlowStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // status of the download process after completion of UpdateCreditReportStatusBasedOnAuthFlowStatus activity.
  credit_report.CreditReportDownloadStatus process_status = 2;
}

message InitiateRecordConsentStepRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string credit_report_download_id = 2;
}

message InitiateRecordConsentStepResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdateCreditReportStatusBasedOnConsentStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string credit_report_download_id = 2;
}

message UpdateCreditReportStatusBasedOnConsentStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // status of the download process after completion of UpdateCreditReportStatusBasedOnConsentStatus activity.
  credit_report.CreditReportDownloadStatus process_status = 2;
}

message CheckRecordConsentStepProgressRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string credit_report_download_id = 2;
}

message CheckRecordConsentStepProgressResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // status of the download process after completion of CheckRecordConsentStepProgress activity.
  credit_report.CreditReportDownloadStatus process_status = 2;
}

message FetchCreditReportFromVendorRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string credit_report_download_id = 2;
  // pan id that can be sent by service which can own pan fetching stage
  string pan = 3;
  // not mandatory field, if not present activity will fetch best name from user
  api.typesv2.common.Name name = 4;
}

message FetchCreditReportFromVendorResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // status of the download process after completion of FetchCreditReportFromVendor activity.
  credit_report.CreditReportDownloadStatus process_status = 2;
}

message ExtendCreditReportSubscriptionRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string credit_report_download_id = 2;
}

message ExtendCreditReportSubscriptionResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // status of the download process after completion of ExtendCreditReportSubscription activity.
  credit_report.CreditReportDownloadStatus process_status = 2;
}

message UpdateCreditReportDownloadProcessStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string credit_report_download_id = 2;
  credit_report.CreditReportDownloadStatus status = 3;
  credit_report.CreditReportDownloadSubStatus sub_status = 4;
  frontend.deeplink.Deeplink next_action = 5;
  // redirect back to the requester
  bool redirect_to_caller = 6;
}

message UpdateCreditReportDownloadProcessStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message PublishCreditReportDownloadEventRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string credit_report_download_id = 2;
  credit_report.CreditReportDownloadStatus status = 3;
  credit_report.CreditReportDownloadSubStatus sub_status = 4;
}

message PublishCreditReportDownloadEventResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}


message GetUserPanDobStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
}

message GetUserPanDobStatusResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  bool is_pan_dob_present = 2;
}

message CheckPanDobPresentRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
}

message CheckPanDobPresentResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}
