//go:generate gen_queue_pb
syntax = "proto3";

package event;

import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/user/event";
option java_package = "com.github.epifi.gamma.api.user.event";

message DeleteUserEvent {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;

  // ActorId of the user getting deleted
  string actor_id = 2;
}
