syntax = "proto3";

package event;

option go_package = "github.com/epifi/gamma/api/user/event";
option java_package = "com.github.epifi.gamma.api.user.event";

enum CompletedAddFundsCategory {
  CATEGORY_UNSPECIFIED = 0;
  // signifies cumulative credited amount between Rs.100 - Rs.2000
  CATEGORY_1 = 1;
  // signifies cumulative credited amount between Rs.2001 - Rs.5000
  CATEGORY_2A = 2;
  // signifies cumulative credited amount between Rs.5001 - Rs.10000
  CATEGORY_2B = 3;
  // signifies cumulative credited amount between Rs.10001 - Rs.50000
  CATEGORY_3 = 4;
  // signifies cumulative credited amount of more than Rs.50001
  CATEGORY_4 = 5;
}

// signifies the transaction category for in incoming credit transaction
enum IncomingCreditCategory {
  INCOMING_CREDIT_CATEGORY_UNSPECIFIED = 0;
  // credits transactions with credit amount <= Rs. 1000
  INCOMING_CREDIT_LOW = 1;
  // credits transactions with credit amount > Rs. 1000
  INCOMING_CREDIT_HIGH = 2;
}
