syntax = "proto3";

package user;

import "api/typesv2/home/<USER>";
import "api/typesv2/user.proto";
import "api/comms/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/user";
option java_package = "com.github.epifi.gamma.api.user";

message UserPreference {
  // unique identifier of type uuid
  string id = 1;
  // foreign key to actor for whom we are storing the preferences
  string actor_id = 2;
  // type of the user preference
  PreferenceType preference_type = 3;
  // value for this preference_type
  PreferenceValue preference_value = 4;
  // timestamp corresponding to creation of entry
  google.protobuf.Timestamp created_at = 5;
}

message PreferenceTypeValuePair {
  // type of the user preference
  PreferenceType preference_type = 1;
  // value for this preference_type
  PreferenceValue preference_value = 2;
}

// User preference type
enum PreferenceType {
  PREFERENCE_TYPE_UNSPECIFIED = 0;
  // The language preferred for voice call
  PREFERENCE_TYPE_PREFERRED_CALL_LANGUAGE = 1;
  // The call languages suggested by the user which are currently unsupported by Fi
  PREFERENCE_TYPE_SUGGESTED_CALL_LANGUAGE = 2;
  // Preference for user shortcuts which the user selects to be displayed
  PREFERENCE_TYPE_HOME_SHORTCUTS = 3;
  // Preference type for home layout for the user
  PREFERENCE_TYPE_APP_HOME_LAYOUT = 4;
  // Preference type comms for a user
  // Preference set by user via device control settings.
  PREFERENCE_TYPE_COMMS = 5;
  // Preference type app comms for a user
  // Used for any communications set by user for any app related communications sent by Fi.
  PREFERENCE_TYPE_APP_COMMS = 6;
}

message PreferenceValue {
  oneof pref_val {
    // call language preference - the list of supported languages preferred by the user
    // There is no order of priority for the list of languages
    // List is used instead of single language to help optimize agent allocation.
    LanguagePreferenceOrder preferred_call_language = 1;
    // call language suggestion - the list of unsupported languages suggested by the user
    // There is no order of priority for the list of languages
    LanguagePreferenceOrder suggested_call_language = 2;
    // These are shortcut preferences which the user selects or is displayed to the user.
    // It is a list of icon types which are same as those displayed on the explore page.
    ShortcutsPreference home_shortcuts_preference = 3;
    // These are the app home layout preferences which are set for the user.
    // The value will be used to check the difference in the layout the user has, and what we want to show them.
    // Based on this value, walkthrough's will be generated
    AppHomeLayoutPreference app_home_layout_preference = 4;
    // These are communication preferences which are set for a user via device control settings
    // User shall be able to set the preference to unsubscribe notifications via device control settings.
    CommsPreference comms_preference = 5;
    // These are communication preferences which are set for a user.
    // User shall be able to set the preference from UI. Sherlock dev action will also facilitate setting up
    // user preference to receive comms on different mediums (PN, Whatsapp, SMS, Email).
    CommsPreference app_comms_preference = 6;
  }
}

message ShortcutsPreference {
  // The list of icons types to be displayed/stored.
  repeated api.typesv2.home.IconType icon_types = 1;
}


// General struct for language preferences.
message LanguagePreferenceOrder {
  // List of languages preferred by the user.
  // Whether or not priority ordering is assumed depends on the use case. eg: for call language there is no order of priority
  repeated api.typesv2.Language preferred_languages = 1;
}

// Preference for setting the app home layout for the user
message AppHomeLayoutPreference {
  string app_home_layout = 1;
}

// Preference for comms sent to a user
message CommsPreference {
  // Comms preferences list, for example a user could have disabled promotions for credit card on whatsApp.
  // This would be a list object specifying feature as Promotions, Area as CreditCard, Medium as WhatsApp and category
  // as promotional. The comms signal corresponding to this will be OFF
  repeated CommsPreferenceInfo comms_preference_infos = 1;
}

// Detailed information on each preference set by user on their communication preferences
message CommsPreferenceInfo {
  // Feature for various communications. Example - Offers & Rewards, Reminders etc
  comms.Feature feature = 1;
  // Area specific communication. Example - CreditCard, Loans etc
  comms.Area area = 2;
  // Communication Medium - PN, SMS, WA, Email
  comms.Medium medium = 3;
  // Preference signal for a category and medium
  CommsSignal signal = 4;
  // timestamp corresponding to snooze end
  google.protobuf.Timestamp snooze_end_date = 5;
  // Category specifies if the comms is mandatory or good to have
  // Mandatory - Transactional, Good to have - Promotional
  // For promotional comms, signal applies. For transaction comms - signals are overridden
  // User visibility of a preference would be driven by frontend config
  comms.Category category = 6;
}

// Preference of the user for receiving communication
enum CommsSignal {
  COMMS_SIGNAL_UNSPECIFIED = 0;
  // Signals that user has opted in for receiving comms
  ON = 1;
  // Signals that user has denied for receiving comms
  OFF = 2;
  // Signals that user has opted for snoozing comms
  SNOOZE = 3;
}
