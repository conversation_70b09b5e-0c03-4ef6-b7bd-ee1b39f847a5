// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package user.developer;

option go_package = "github.com/epifi/gamma/api/user/developer";
option java_package = "com.github.epifi.gamma.api.user.developer";

enum OnboardingEntity {
  ACTOR_ENTITY_UNSPECIFIED = 0;

  ONBOARDING_STATE_ACTOR = 1;

  // Fetching user details with user id
  USER = 2;

  // mapping to fetch user group mapping details
  USER_GROUP_MAPPING = 3;

  SHIPPING_PREFERENCE = 4;

  USER_CONTACT = 5;

  // Fetch vendor responses for onboarding stages
  ONBOARDING_VENDOR_RESPONSES = 6;

  // Fetch credit report for an actor
  CREDIT_REPORT = 8;

  // Fetch Consents DB state
  CONSENT = 10;

  // Fetch Changefeed data DB State
  CHANGE_FEED = 11;

  // Fetch Nominee DB State
  NOMINEE = 12;

  //Fetch UserIntel DB State
  USER_INTEL = 13;

  // fetch state of credit report download process.
  CREDIT_REPORT_DOWNLOAD = 14;

  // fetch details of a credit report download subscription for a user
  CREDIT_REPORT_USER_SUBSCRIPTION = 15;

  // fetch credit reports downloaded manually from vendor via jenkins job.
  // This is a temporary setup to help in faster debugging of issues we're facing with vendor api.
  TEMP_CREDIT_REPORT_MANUAL_FETCH = 16;

  // fetch user device properties
  USER_DEVICE_PROPERTIES = 17;

  // to fetch user preferences
  USER_PREFERENCES = 18;

  ONBOARDING_CROSS_VALIDATION = 19;
}
