syntax = "proto3";

package user;

import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/user";
option java_package = "com.github.epifi.gamma.api.user";

// This event is to be emitted only when user's shipping address is updated
message ShippingAddressUpdateEvent {
  // common request header across all the consumer grpc services.
  queue.ConsumerRequestHeader request_header = 1;

  string actor_id = 2;
}
