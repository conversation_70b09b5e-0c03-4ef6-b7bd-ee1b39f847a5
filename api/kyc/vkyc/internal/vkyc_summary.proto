// protolint:disable MAX_LINE_LENGTH

/*
Protos relating to the VKYC Summary domain for business logic processing
in VKYC service and interaction with VKYCSummaryDAO.
*/

syntax = "proto3";

package kyc.vkyc;

import "api/typesv2/user.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/kyc/vkyc";
option java_package = "com.github.epifi.gamma.api.kyc.vkyc";


// VKYCSummary is an domain model for VKYC.
// It maintains the status of VKYC for an actor.
message VKYCSummary {

  // Primary identifier to vkyc_summary.
  string id = 1;

  // Actor mapped to VKYC
  string actor_id = 2;

  // Represents a vendor that provides VKYC
  vendorgateway.Vendor vendor = 3;

  // Status of VKYC for a user
  VKYCSummaryStatus status = 4;

  // SubStatus/Granular-details of VKYC for a user
  VKYCSummarySubStatus sub_status = 5;

  // created at
  google.protobuf.Timestamp created_at = 6;

  // updated at
  google.protobuf.Timestamp updated_at = 7;

  // metadata currently stores EPAN info and preferred languages
  SummaryMetadata metadata = 8;
}

enum VKYCSummaryStatus {
  // default if not specified
  // VKYC_SUMMARY_STATUS_UNSPECIFIED indicates VKYC not done and user can be prompted to do VKYC
  VKYC_SUMMARY_STATUS_UNSPECIFIED = 0;
  // is customer not registered
  // VKYC_SUMMARY_STATUS_UNREGISTERED indicates VKYC not done and user can be prompted to do VKYC
  VKYC_SUMMARY_STATUS_UNREGISTERED = 1;
  // if registration done and call agent is pending
  // VKYC_SUMMARY_STATUS_REGISTERED indicates VKYC not done and user can be prompted to do VKYC
  VKYC_SUMMARY_STATUS_REGISTERED = 2;
  // if call is scheduled/completed and pending on agent to approve or reject
  // VKYC_SUMMARY_STATUS_IN_PROGRESS indicates VKYC not done and user can be prompted to do VKYC
  VKYC_SUMMARY_STATUS_IN_PROGRESS = 4;
  // VKYC_SUMMARY_STATUS_APPROVED indicates VKYC is done and user kyc level is expected to be at full kyc now
  VKYC_SUMMARY_STATUS_APPROVED = 5;
  // VKYC_SUMMARY_STATUS_REJECTED indicates VKYC being rejected by auditor and user cannot do vkyc on app, user will have to visit bank
  VKYC_SUMMARY_STATUS_REJECTED = 6;
  // retry with new registration in case of customer id update
  // this status update will not happen through automated flow. A manual update will be needed for this
  // VKYC_SUMMARY_STATUS_RE_REGISTER indicates VKYC not done and user can be prompted to do VKYC
  VKYC_SUMMARY_STATUS_RE_REGISTER = 7;
  // when call is complete and documents are in review
  // VKYC_SUMMARY_STATUS_IN_REVIEW indicates VKYC call done and needs to wait for federal's auditor approval
  VKYC_SUMMARY_STATUS_IN_REVIEW = 8;
}

// an summary can depend on multiple things, currently only on attempt and customer registration
// this contains exhaustive list of status from all the dependent things
enum VKYCSummarySubStatus {
  // default if not specified
  VKYC_SUMMARY_SUB_STATUS_UNSPECIFIED = 0;
  // attempt related status
  VKYC_SUMMARY_SUB_STATUS_ATTEMPT_PENDING = 1;
  VKYC_SUMMARY_SUB_STATUS_ATTEMPT_IN_PROGRESS = 2;
  VKYC_SUMMARY_SUB_STATUS_ATTEMPT_APPROVED = 3;
  VKYC_SUMMARY_SUB_STATUS_ATTEMPT_REJECTED = 4;
  VKYC_SUMMARY_SUB_STATUS_ATTEMPT_FAILED = 5;
  // when call is complete and documents are in review
  VKYC_SUMMARY_SUB_STATUS_ATTEMPT_IN_REVIEW = 6;
}

// VKYCSummaryFieldMask is used to mask columns to update in DB Update call
enum VKYCSummaryFieldMask {
  VKYC_SUMMARY_FIELD_MASK_UNSPECIFIED = 0;
  VKYC_SUMMARY_FIELD_MASK_STATUS = 1;
  VKYC_SUMMARY_FIELD_MASK_SUB_STATUS = 2;
  VKYC_SUMMARY_FIELD_MASK_METADATA = 3;
}

message SummaryMetadata {
  string e_pan_client_req_id = 1;
  int32 e_pan_attempt_count = 2;
  repeated api.typesv2.Language preferred_languages = 3;
  // client request id for the scanned PAN document.
  string scanned_pan_req_id = 4;
}
