syntax = "proto3";

package kyc.vkyc;

option go_package = "github.com/epifi/gamma/api/kyc/vkyc";
option java_package = "com.github.epifi.gamma.api.kyc.vkyc";

enum VKYCKarzaAuditorFailureReason {
  VKYC_KARZA_AUDITOR_FAILURE_REASON_UNSPECIFIED = 0;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_NO_LIVE_SIGNATURE = 1;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_THIRD_PARTY_PROMPTING_IN_THE_CALL = 2;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_PARENTS_NAME_MISMATCH = 3;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_DOB_MISMATCH = 4;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_GENDER_MISMATCH = 5;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_CUSTOMER_NAME_MISMATCH = 6;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_CUSTOMER_IS_NRI_OR_MOVING_TO_ABROAD_SOON = 7;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_PAN_IS_NOT_FULLY_CAPTURED = 8;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_PAN_OCR_ISSUE = 9;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_PAN_PHOTO_NOT_CLEAR = 10;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_VIDEO_NOT_AVAILABLE = 11;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_NETWORK_ISSUES = 12;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_VIDEO_NOT_CLEAR_OR_NOT_PRESENT = 13;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_AUDIO_NOT_CLEAR_OR_NOT_PRESENT = 14;

  VKYC_KARZA_AUDITOR_FAILURE_REASON_OTHERS = 15;
}
