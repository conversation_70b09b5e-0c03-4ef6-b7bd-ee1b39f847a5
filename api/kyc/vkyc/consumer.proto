// protolint:disable MAX_LINE_LENGTH

/*
Protos related to the VKYC KARZA consumer
*/

syntax = "proto3";

package kyc.vkyc;

import "api/vendornotification/openbanking/kyctypechange/federal/event.proto";
import "api/vendornotification/vkyc/karza/event.proto";
import "api/user/onboarding/notification.proto";
import "api/kyc/vkyc/internal/vkyc_call_schedule.proto";
import "api/order/order.proto";
import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/kyc/vkyc";
option java_package = "com.github.epifi.gamma.api.kyc.vkyc";

message ProcessKYCStateChangeResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message RefreshCallStatusResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message RefreshCallStatusEvent {
  // standard header to be added to all requests
  queue.ConsumerRequestHeader consumer_request_header = 1;
  string vkyc_summary_id = 2;
  string vkyc_attempt_id = 3;
  string vkyc_karza_call_info_id = 4;
}

message ProcessCallEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessAgentCallbackResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessAuditorCallbackResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message VKYCAgentUpdateEvent {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  // Unique id to represent an event uniquely(uuid for now)
  string event_id = 2;

  // time at which the event was published
  google.protobuf.Timestamp event_timestamp = 3;

  // affected schedules
  VKYCCallSchedule vkycCallSchedule = 4;

  string vkyc_summary_id = 5;
  string vkyc_attempt_id = 6;
}

message VKYCAgentUpdateResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessVKYCOnboardingCompleteEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessVKYCTransactionEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

service Consumer {
  // ProcessKYCStateChange is responsible for updating kyc status of a user
  // possible statuses from vendor -
  // Y - KYC Status of the user is upgraded to full kyc at federal
  // R1 - VKYC is Soft Rejected by the agent where user can retry VKYC
  // R2 - VKYC is Hard Rejected by the auditor where user CANNOT retry VKYC
  rpc ProcessKYCStateChange (vendornotification.openbanking.kyctypechange.federal.KYCStateChangeEvent) returns (ProcessKYCStateChangeResponse) {
  }
  // RefreshCallStatus triggers VKYC state machine to refresh the vkyc status
  rpc RefreshCallStatus (RefreshCallStatusEvent) returns (RefreshCallStatusResponse) {
  }
  // ProcessCallEvent consumes callback events received from karza
  rpc ProcessCallEvent (vendornotification.vkyc.karza.CallEvent) returns (ProcessCallEventResponse) {
  }
  rpc ProcessAgentCallback (vendornotification.vkyc.karza.AgentCallbackEvent) returns (ProcessAgentCallbackResponse) {
  }
  rpc ProcessAuditorCallback (vendornotification.vkyc.karza.AuditorCallbackEvent) returns (ProcessAuditorCallbackResponse) {
  }
  rpc VKYCAgentUpdate (VKYCAgentUpdateEvent) returns (VKYCAgentUpdateResponse) {
  }
  // ProcessVKYCOnboardingCompleteEvent consumes event published on onboarding completion for performing VKYC specific tasks
  rpc ProcessVKYCOnboardingCompleteEvent(user.onboarding.OnboardingStageUpdate) returns (ProcessVKYCOnboardingCompleteEventResponse) {}
  // ProcessVKYCTransactionEvent consumes event published when user performs the transaction
  // eg. Needs to show nudge if user crosses 25% limit of his savings limit
  rpc ProcessVKYCTransactionEvent (order.OrderUpdate) returns (ProcessVKYCTransactionEventResponse) {}
}
