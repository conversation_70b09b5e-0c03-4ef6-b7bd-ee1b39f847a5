// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package kyc.vkyc.notification;

import "api/kyc/vkyc/notification/notification.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/kyc/vkyc/notification";
option java_package = "com.github.epifi.gamma.api.kyc.vkyc.notification";

message ProcessNotificationResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

service Consumer {
  rpc ProcessNotification(NotificationEvent) returns (ProcessNotificationResponse) {}
}
