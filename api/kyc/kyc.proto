// protolint:disable MAX_LINE_LENGTH

/*
Protos relating to the KYC domain - services & data models
EKYC - A<PERSON>haar enabled KYC. Currently enabled via Partner SDK on app
CKYC - KYC data from Cersai. Currently will be routed via Partner. Can be
        a direct integration in the future
MANUAL KYC - Final resort when other mechanisms fail. Needs partner integration
        and implementation
*/


syntax = "proto3";

package kyc;

import "api/queue/consumer_headers.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/gender.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/postal_address.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/kyc";
option java_package = "com.github.epifi.gamma.api.kyc";

// KYC Type
enum KycType {
  KYC_TYPE_UNSPECIFIED = 0;
  EKYC = 1;
  CKYC = 2;
  MANUAL_KYC = 3;
  BKYC = 4;
}

// KYC Provider
enum KycProvider {
  KYC_PROVIDER_UNSPECIFIED = 0;
  FEDERAL = 1;
}

// TODO(aditya): rename to KYCStatus once client stops using it.
// To be in sync with api/frontend/account/signup/kyc_status.proto
enum KycStatus {
  KYC_STATUS_UNSPECIFIED = 0;

  // KYC process in progress
  IN_PROGRESS = 2;

  // KYC verified / success terminal state
  COMPLETED = 3;

  // We can keep KYC data only for limited time after which it's deleted.
  // EXPIRED represents KYC records have expired.
  EXPIRED = 5;

  // permanent error state.
  ERRORED = 6;
}

// Type of Govt. ID Proof corresponding to a user
enum IdProofType {
  ID_PROOF_TYPE_UNSPECIFIED = 0;
  PASSPORT = 1;
  VOTER_ID = 2;
  PAN = 3;
  DRIVING_LICENSE = 4;
  UID = 5;  // Aadhaar number
  NREGA_JOB_CARD = 6;
  NATIONAL_POPULATION_REGISTER_LETTER = 7;
  CKYC_RECORD = 8;
}

// KYC done by user can either be min kyc or full kyc.
enum KYCLevel {
  UNSPECIFIED = 0;
  // Various conditions lead to a customer becoming min kyc customer:
  // 1. Dedupe customer, with partial kyc and goes through ekyc
  // 2. CKYC fails due to some reason, and customer taken through ekyc
  MIN_KYC = 1;
  // Under the following conditions, a customer can become a full kyc customer:
  // 1. Dedupe customer with full kyc done
  // 2. Dedupe customer, with partial kyc and goes through ckyc successfully
  FULL_KYC = 2;
}

// ID Proof is be about government issued documents like PAN, PASSPORT etc.
// Images may or may not be present. Sometimes data could be just an image.
message IdProof {
  IdProofType type = 1;

  // The identifier based on the type of the id. e.g PAN Number / Aadhar Number
  string id_value = 2;

  api.typesv2.common.Image document_image = 3;

  google.type.Date expiry = 4;
}

message KYCRecord {
  api.typesv2.common.Name name = 1;
  google.type.Date dob = 2;
  google.type.PostalAddress permanent_address = 3;

  // correspondence/mailing address
  google.type.PostalAddress communication_address = 4;

  string email = 5;
  api.typesv2.Gender gender = 6;

  // This is set only for CKYC. Unique identifier for CKYC record.
  string ckyc_number = 7;

  // This is set only for EKYC. Unique identifier for EKYC record.
  string uid_reference_key = 8;

  repeated IdProof identity_proofs = 9;

  api.typesv2.common.Image photo = 10;

  // Kyc level denotes if the kyc done by user was min/full kyc.
  // Use case: This kyc level will needed when creating account for the user with vendor.
  // Refer: https://docs.google.com/document/d/1qqtLcb2D4uNGQ3TvPuy1e4oYXKb5oMHUrpy_VCPdzyM/edit
  KYCLevel kyc_level = 11;

  // represents time at which the KYC record was stored in the KYC Vendor Data entity.
  google.protobuf.Timestamp created_at = 12;

  // Sign image in case of CKYC download payload
  string sign_image = 13;

  // This is set only for EKYC
  // Default value will be of latest EKYC attempt
  // incase of lso users, rrn number to be fetched from vkyc
  string customer_creation_uid_reference_key = 14;
}

// EKYCRecord is the user data received by UIDAI through
// Aadhar OTP verification. It is used for bank customer
// and account creation.
message EKYCRecord {
  // Vault Reference Number
  string uid_reference_key = 1;

  api.typesv2.Gender gender = 2;
  api.typesv2.common.Image photo = 3;
  google.type.PostalAddress address = 4;
  google.type.Date dob = 5;
  api.typesv2.common.Name name = 6;

  // raw aadhaar payload received from the client via OTP-based EKYC flow minus the images.
  // It's JSON representation of frontend.account.signup.EKYCRecord (via protojson marshalling).
  // We remove the images from the payload to avoid unnecessary data bloat.
  string raw_record_no_images = 7;
}

message BKYCRecord {
  // Vault Reference Number
  string uid_reference_key = 1;

  api.typesv2.Gender gender = 2;
  api.typesv2.common.Image photo = 3;
  google.type.PostalAddress address = 4;
  google.type.Date dob = 5;
  api.typesv2.common.Name name = 6;
  // we provide user option to update communication address when he confirms BKYC detail
  // will be nil in case user didn't update communication address
  google.type.PostalAddress comm_address = 7;
  // base64 image is stored in DB
  api.typesv2.common.Image signature_image = 8;
}


// EkycSource denotes source which is trying to initiate ekyc
enum EkycSource {
  EKYC_SOURCE_UNSPECIFIED = 0;
  EKYC_SOURCE_ONBOARDING = 1;
  EKYC_SOURCE_VKYC = 2;
  EKYC_SOURCE_DOB_UPDATE = 3;
  EKYC_SOURCE_COMMUNICATION_ADDRESS_UPDATE = 4;
  EKYC_SOURCE_FEDERAL_AFU_PHONE_UPDATE = 5;
  EKYC_SOURCE_RE_KYC = 6;
  EKYC_SOURCE_PAN_UPDATE = 7;
  EKYC_SOURCE_NOMINEE_UPDATE = 8;
  EKYC_SOURCE_AADHAAR_UPDATE = 9;
}

enum BKYCSource {
  BKYC_SOURCE_UNSPECIFIED = 0;
  BKYC_SOURCE_PRE_CIF_CREATION = 1;
  BKYC_SOURCE_POST_CIF_CREATION = 2;
}

message EKYCSuccessEvent {
  queue.ConsumerRequestHeader consumer_request_header = 1;

  string actor_id = 2 [(validate.rules).string.min_len = 1];

  KYCLevel level = 3;
}

enum UNNameCheckStatus {
  UN_NAME_CHECK_STATUS_UNSPECIFIED = 0;
  UN_NAME_CHECK_STATUS_PASSED = 1;
  UN_NAME_CHECK_STATUS_PASSED_WITH_INTERNAL_CHECK = 2;
  UN_NAME_CHECK_STATUS_FAILED = 3;
}

enum BKYCRecordFieldMask {
  BKYC_RECORD_FIELD_MASK_UNSPECIFIED = 0;
  BKYC_RECORD_FIELD_MASK_COMMUNICATION_ADDRESS = 1;
  BKYC_RECORD_FIELD_MASK_SIGNATURE_IMAGE = 2;
}
