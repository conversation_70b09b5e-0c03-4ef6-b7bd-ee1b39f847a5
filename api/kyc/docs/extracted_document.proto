// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=UploadedDocStorageInfo

syntax = "proto3";

package kyc.docs;

import "api/kyc/docs/document.proto";
import "api/kyc/docs/enums.proto";
import "api/kyc/docs/extracted_data.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/kyc/docs";
option java_package = "com.github.epifi.gamma.api.kyc.docs";

message ExtractedDocument {
  string id = 1;
  string client_request_id = 2;
  string actor_id = 3;
  kyc.docs.DocExtractionFlow flow = 4;
  kyc.docs.DocType document_type = 5;
  vendorgateway.Vendor vendor = 6;
  string raw_response = 7;
  // data storage information for the uploaded file
  UploadedDocStorageInfo uploaded_doc_storage_info = 8;
  kyc.docs.ExtractedData extracted_data = 9;
  kyc.docs.DocExtractionFailureReason failure_reason = 10;
  string failure_reason_raw = 11;
  kyc.docs.DocExtractionStatus status = 12;
  kyc.docs.DocUploadStatus upload_status = 13;

  google.protobuf.Timestamp created_at = 14;
  google.protobuf.Timestamp updated_at = 15;

  // Time by which KYC data has to be purged
  // null represents data need not be deleted
  google.protobuf.Timestamp delete_by = 16;
  int64 deleted_at_unix = 17;
}

// All storage info related to the uploaded document is stored here
// Example - User uploads passport - the entire passport data will be stored in this database
// NOTE - Raw data and extracted data will be stored in different fields
message UploadedDocStorageInfo {
  UploadedDocStorageInfoData uploaded_doc_storage_info_data = 1;
}

message UploadedDocStorageInfoData {
  oneof Data {
    // For single page documents - example : EPAN
    kyc.docs.SinglePageDocument single_page_doc_data = 1;
    // For double page documents - example : Passport
    kyc.docs.DoublePageDocument double_page_doc_data = 2;
  }
}
