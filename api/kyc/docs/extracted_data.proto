syntax = "proto3";

package kyc.docs;

import "api/typesv2/date.proto";
import "api/typesv2/document_details.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/marital_status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/nationality.proto";
import "api/typesv2/passport_type.proto";
import "google/type/postal_address.proto";

option go_package = "github.com/epifi/gamma/api/kyc/docs";
option java_package = "com.github.epifi.gamma.api.kyc.docs";

message ExtractedData {
  ExtractedDetails extracted_details = 1;
}

message ExtractedDetails {
  oneof Details {
    UqudoEmiratesIdExtractedDetails uqudo_emirates_id_extracted_details = 1;
    api.typesv2.PassportData extracted_passport_details = 2;
    UqudoPassportFrontExtractedDetails uqudo_passport_front_extracted_details = 3;
    UqudoQatarIdExtractedDetails uqudo_qatar_id_extracted_details = 4;
  }
}

// https://docs.uqudo.com/docs/kyc/uqudo-api/scan/country-specific-ids/qat_id-qatar-id
message UqudoQatarIdExtractedDetails {
  string document_number = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.Date dob = 3;
  api.typesv2.Date date_of_expiry = 4;
  api.typesv2.Nationality nationality = 5;
  // Only available for residents users(not for qatar citizens) as per uqudo doc.
  string passport_number = 6;
  // Only available for residents users(not for qatar citizens) as per uqudo doc.
  api.typesv2.Date passport_date_of_expiry = 7;
  string serial_number = 8;
  api.typesv2.common.Image user_photo = 9;
  api.typesv2.common.Image front_photo = 10;
  api.typesv2.common.Image back_photo = 11;
  string occupation_in_arabic = 12;
  string profession_in_arabic = 13;
  bool chip_available = 14;
  string employer_in_arabic = 15;
  string sponsor_in_arabic = 16;
  string permit_class_in_arabic = 17;
  string residency_type_in_arabic = 18;
  message IdDetectionResults {
    bool enabled = 1;
    float score = 2;
  }
  IdDetectionResults id_screen_detection = 19;
  IdDetectionResults id_print_detection = 20;
  IdDetectionResults id_photo_tampering_detection = 21;
}

message UqudoEmiratesIdExtractedDetails {
  string identity_number = 1;
  api.typesv2.Date issue_date = 2;
  api.typesv2.Date date_of_expiry = 3;
  api.typesv2.common.Name name = 4;
  api.typesv2.common.Name mother_name = 5;
  api.typesv2.Date dob = 6;
  api.typesv2.Gender gender = 7;
  api.typesv2.MaritalStatus marital_status = 8;
  google.type.PostalAddress home_address = 9;
  // holder signature image in base64
  string holder_signature_image = 10;
  api.typesv2.Nationality nationality = 11;
  api.typesv2.common.Image user_photo = 12;
  api.typesv2.common.Image front_photo = 13;
  api.typesv2.common.Image back_photo = 14;
}

message UqudoPassportFrontExtractedDetails {
  api.typesv2.PassportType passport_type = 1;
  string issuer = 2;
  api.typesv2.Nationality nationality = 3;
  string passport_number = 4;
  api.typesv2.common.Name name = 5;
  api.typesv2.Date date_of_birth = 6;
  api.typesv2.Gender gender = 7;
  google.type.PostalAddress place_of_birth = 8;
  google.type.PostalAddress place_of_issue = 9;
  api.typesv2.Date date_of_issue = 10;
  api.typesv2.Date date_of_expiry = 11;
  string mrz_text = 12;
  bool mrz_verified = 13;
  message IdDetectionResults {
    bool enabled = 1;
    float score = 2;
  }
  IdDetectionResults id_screen_detection = 14;
  IdDetectionResults id_print_detection = 15;
  IdDetectionResults id_photo_tampering_detection = 16;
  api.typesv2.common.Image face_image = 17;
}
