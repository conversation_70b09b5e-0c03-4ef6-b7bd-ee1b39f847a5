// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=DocExtractionFlow,DocExtractionFailureReason,DocExtractionStatus,DocType,DocUploadStatus

syntax = "proto3";

package kyc.docs;

option go_package = "github.com/epifi/gamma/api/kyc/docs";
option java_package = "com.github.epifi.gamma.api.kyc.docs";

enum DocExtractionFlow {
  DOC_EXTRACTION_FLOW_UNSPECIFIED = 0;
  DOC_EXTRACTION_FLOW_NRE_ACCOUNT_ONBOARDING = 1;
}

enum DocExtractionFailureReason {
  DOC_EXTRACTION_FAILURE_REASON_UNSPECIFIED = 0;
  DOC_EXTRACTION_FAILURE_REASON_UPLOAD_FAILED = 1;
  DOC_EXTRACTION_FAILURE_REASON_DOCUMENT_TAMPERING = 2;
}

enum DocExtractionStatus {
  DOC_EXTRACTION_STATUS_UNSPECIFIED = 0;
  DOC_EXTRACTION_STATUS_IN_PROGRESS = 1;
  DOC_EXTRACTION_STATUS_SUCCESS = 2;
  DOC_EXTRACTION_STATUS_FAILURE = 3;
  DOC_EXTRACTION_STATUS_EXPIRED = 4;
}

enum DocType {
  DOC_TYPE_UNSPECIFIED = 0;
  DOC_TYPE_PASSPORT = 1;
  DOC_TYPE_VISA = 2;
  DOC_TYPE_EMIRATES_ID = 3;
  DOC_TYPE_PASSPORT_FRONT = 4;
  DOC_TYPE_PASSPORT_BACK = 5;
  DOC_TYPE_QATAR_ID = 6;
}

enum DocUploadStatus {
  DOC_UPLOAD_STATUS_UNSPECIFIED = 0;
  DOC_UPLOAD_STATUS_IN_PROGRESS = 1;
  DOC_UPLOAD_STATUS_SUCCESS = 2;
  DOC_UPLOAD_STATUS_FAILURE = 3;
}

enum ExtractedDocFieldMask {
  EXTRACTED_DOC_FIELD_MASK_UNSPECIFIED = 0;
  EXTRACTED_DOC_FIELD_MASK_RAW_RESPONSE = 1;
  EXTRACTED_DOC_FIELD_MASK_UPLOADED_DOC_STORAGE_INFO = 2;
  EXTRACTED_DOC_FIELD_MASK_EXTRACTED_DATA = 3;
  EXTRACTED_DOC_FIELD_MASK_FAILURE_REASON = 4;
  EXTRACTED_DOC_FIELD_MASK_FAILURE_REASON_RAW = 5;
  EXTRACTED_DOC_FIELD_MASK_STATUS = 6;
  EXTRACTED_DOC_FIELD_MASK_UPLOAD_STATUS = 7;
  EXTRACTED_DOC_FIELD_MASK_DELETE_BY = 8;
}
