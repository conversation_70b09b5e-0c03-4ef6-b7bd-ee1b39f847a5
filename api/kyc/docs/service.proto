syntax = "proto3";

package kyc.docs;

import "api/kyc/docs/enums.proto";
import "api/kyc/docs/extracted_document.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";

option go_package = "github.com/epifi/gamma/api/kyc/docs";
option java_package = "com.github.epifi.gamma.api.kyc.docs";

service DocExtraction {
  // RPC to upload a file, extract data from document and return extracted information as response
  rpc UploadAndExtractData (UploadAndExtractDataRequest) returns (UploadAndExtractDataResponse) {}
  // RPC to fetch extracted data for a file from a vendor/service and return the extracted information from the document as response
  rpc FetchAndExtractData (FetchAndExtractDataRequest) returns (FetchAndExtractDataResponse) {}
  // RPC to get extracted information for a particular file
  rpc GetExtractedData (GetExtractedDataRequest) returns (GetExtractedDataResponse) {}
  // RPC to process data for a document (obtained from a vendor/service), extract information and return the extracted information as response
  // Ex - Client scans a document using a third-party SDK, receives some raw response, and then calls this RPC to extract and store the information
  rpc ProcessAndExtractData (ProcessAndExtractDataRequest) returns (ProcessAndExtractDataResponse) {}
  // rpc to purge KYC documents after expiry
  rpc PurgeExtractedDocuments (PurgeExtractedDocumentsRequest) returns (PurgeExtractedDocumentsResponse) {}
}

message PurgeExtractedDocumentsRequest {
}

message PurgeExtractedDocumentsResponse {
  rpc.Status status = 1;
}

message UploadAndExtractDataRequest {
  string actor_id = 1;
  string client_request_id = 2;
  kyc.docs.DocExtractionFlow upload_flow = 3;
  kyc.docs.DocType doc_type = 4;
  repeated bytes file = 5;
}

message UploadAndExtractDataResponse {
  rpc.Status status = 1;
  // extracted_data deprecated : use extracted_document instead
  reserved 2;
  kyc.docs.ExtractedDocument extracted_document = 3;
}

message FetchAndExtractDataRequest {
  string actor_id = 1;
  string client_req_id = 2;
  kyc.docs.DocType doc_type = 4;
  oneof FetchDataRequestParams {
    FetchUqudoEmiratesIdDataRequestParams fetch_uqudo_emirates_id_data_params = 3;
  }
}

message FetchAndExtractDataResponse {
  rpc.Status status = 1;
  // extracted_data deprecated : use extracted_document instead
  reserved 2;
  kyc.docs.ExtractedDocument extracted_document = 3;
}

message GetExtractedDataRequest {
  string actor_id = 1;
  string client_request_id = 2;
  api.typesv2.common.BooleanEnum want_image_in_base64 = 3;
}

message GetExtractedDataResponse {
  rpc.Status status = 1;
  // extracted_data deprecated : use extracted_document instead
  reserved 2;
  kyc.docs.ExtractedDocument extracted_document = 3;
}

message ProcessAndExtractDataRequest {
  string actor_id = 1;
  string client_request_id = 2;
  kyc.docs.DocExtractionFlow upload_flow = 3;
  kyc.docs.DocType doc_type = 4;
  oneof ProcessDataRequestParams {
    ProcessUqudoEmiratesIdDataRequestParams process_uqudo_emirates_id_data_params = 5;
    ProcessUqudoPassportFrontDataRequestParams process_uqudo_passport_front_data_params = 6;
    ProcessUqudoQatarIdDataRequestParams process_uqudo_qatar_id_data_request_params = 7;
  }
}

message ProcessAndExtractDataResponse {
  rpc.Status status = 1;
  kyc.docs.ExtractedDocument extracted_document = 2;
}

message FetchUqudoEmiratesIdDataRequestParams {
  // TODO: add fields
}

message ProcessUqudoEmiratesIdDataRequestParams {
  // session id used to initialize the uqudo sdk
  string session_id = 1;
  // access token used to initialize the uqudo sdk
  string access_token = 2;
  // jwt response received from the uqudo sdk upon successful verification of emirates id
  // this jwt response has the extracted information with a signature which must be verified to confirm the authenticity of the data
  string raw_response_jwt = 3;
}

message ProcessUqudoPassportFrontDataRequestParams {
  // session id used to initialize the uqudo sdk
  string session_id = 1;
  // access token used to initialize the uqudo sdk
  string access_token = 2;
  // jwt response received from the uqudo sdk upon successful verification of emirates id
  // this jwt response has the extracted information with a signature which must be verified to confirm the authenticity of the data
  string raw_response_jwt = 3;
}


message ProcessUqudoQatarIdDataRequestParams {
  // session id used to initialize the uqudo sdk
  string session_id = 1;
  // access token used to initialize the uqudo sdk
  string access_token = 2;
  // jwt response received from the uqudo sdk upon successful verification of emirates id
  // this jwt response has the extracted information with a signature which must be verified to confirm the authenticity of the data
  string raw_response_jwt = 3;
}

