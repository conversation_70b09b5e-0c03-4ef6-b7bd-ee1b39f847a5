// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package kyc.docs;

import "api/typesv2/file.proto";

option go_package = "github.com/epifi/gamma/api/kyc/docs";
option java_package = "com.github.epifi.gamma.api.kyc.docs";

message SinglePageDocument {
  api.typesv2.File page_data = 1;
}

message DoublePageDocument {
  api.typesv2.File page_one_data = 1;
  api.typesv2.File page_two_data = 2;
}
