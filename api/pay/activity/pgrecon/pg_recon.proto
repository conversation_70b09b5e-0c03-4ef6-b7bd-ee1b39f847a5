syntax = "proto3";

package pay.activity.pgrecon;

import "api/typesv2/common/date.proto";
import "api/celestial/activity/header.proto";
import "api/typesv2/payment_gateway.proto";
import "api/vendorgateway/payment_gateway/payments.proto";

option go_package = "github.com/epifi/gamma/api/pay/activity/pgrecon";
option java_package = "com.github.epifi.gamma.api.pay.activity.pgrecon";

message FetchPgReconPaymentsFromVendorRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;
  // 0-indexed page number for fetching
  int32 page_number = 2;
  // The day for which to reconcile the settlements from razorpay. If the day is passed as 0, then the settlements for
  // the entire month are fetched and reconciled. Month and Year must be passed for the activity to work or else error
  // is returned.
  api.typesv2.common.Date recon_date = 3;
  // payment gateway program for which the reconciliation payments are being fetched.
  api.typesv2.PaymentGatewayProgram pg_program = 4;
}

message FetchPgReconPaymentsFromVendorResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
  repeated vendorgateway.pg.Payment payments = 2;
}

message ReconcilePgPaymentEntitiesRequest {
  // Common request header across all the celestial activities
  celestial.activity.RequestHeader request_header = 1;
  // list of vendor payments which can be used for enriching the
  // db orders and txns
  repeated vendorgateway.pg.Payment payments = 2;
  // payment gateway program for which the db orders reconciliation
  // is being done
  api.typesv2.PaymentGatewayProgram pg_program = 3;
}

message ReconcilePgPaymentEntitiesResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}
