syntax = "proto3";

package pay.signal;

import "api/pkg/pay/payment_options_notification.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/pay/signal";
option java_package = "com.github.epifi.gamma.api.pay.signal";

// PgFundTransferPaymentCompleteSignal is used to signal the CheckAndUpdatePgFundTransfer workflow about the completion
// of a payment (order paid/payment failed/payment cancelled, etc). It also includes cases when the fund transfer initiation on client
// side itself fails and no payment is made in the PG along with the payload returned by the PG Client SDK.
message PgFundTransferCompleteSignal {
  string internal_order_id = 1;
  string vendor_order_id = 2;
  // The action type denotes the type of the client action due to which the fund-transfer is completed.
  api.pkg.pay.PaymentOptionsActionType action_type = 3 [(validate.rules).enum = {not_in: [0]}];
  // Must be non-nil when PaymentOptionsActionType is PAYMENT_OPTIONS_ACTION_TYPE_PAYMENT_INITIATION_FAILURE.
  // Can be nil for other cases.
  ClientSdkResponsePayload payload = 4;
}

message ClientSdkResponsePayload {
  oneof Payload {
    option (validate.required) = true;

    RazorpayClientSuccessPayload razorpay_client_success_payload = 1;
    RazorpayClientFailurePayload razorpay_client_failure_payload = 2;
  }

  message RazorpayClientSuccessPayload {
    string razorpay_payment_id = 1;
    string razorpay_order_id = 2;
    string razorpay_signature = 3;
  }

  message RazorpayClientFailurePayload {
    string http_status_code = 1;

    RazorpayClientSdkError error = 2;

    message RazorpayClientSdkError {
      string code = 1;
      string description = 2;
      string source = 3;
      string step = 4;
      string reason = 5;
      string field = 6;
    }
  }
}
