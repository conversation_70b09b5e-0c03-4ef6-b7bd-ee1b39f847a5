// protolint:disable MAX_LINE_LENGTH
// This file defines different messages that are passed along for B2CFundTransfer business logic processing using temporal
syntax = "proto3";

package pay.payload;

import "api/pay/payload/domain_workflow_details.proto";

option go_package = "github.com/epifi/gamma/api/pay/payload";
option java_package = "com.github.epifi.gamma.api.pay.payload";


message FundTransfer {
  // contains all the necessary details needed by a fund transfer workflow to signal the domain workflow about the payment status, once done.
  payload.DomainWorkflowDetails domain_workflow_details = 1;

  // unique identifier for payer
  string user_id = 2;
}
