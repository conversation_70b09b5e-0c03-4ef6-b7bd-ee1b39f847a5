// protolint:disable MAX_LINE_LENGTH
// This file defines different messages that are passed along for B2CFundTransfer business logic processing using temporal
syntax = "proto3";

package pay.payload;

import "api/celestial/workflow/client.proto";

option go_package = "github.com/epifi/gamma/api/pay/payload";
option java_package = "com.github.epifi.gamma.api.pay.payload";


// DomainWorkflowDetails here contain all the necessary details needed by a fund transfer workflow to signal the domain workflow about the
// payment status, once done. Domain workflow here represents any workflow which necessates payment as part of it's execution and hence is
// dependent over the payment status to continue it's execution. Refer for more details:
// https://docs.google.com/document/d/14_fvL894nNKHEW7hVzq3-PgRTrXi4winzK7UMzEwz00/edit#heading=h.r8qgnp4i94pp
message DomainWorkflowDetails {
  // client workflow signal id over which the data is received by domain workflow for fund transfer status
  // Unique identifier of the signal where client expects to receive the payment status
  // Payment final status, irrespective of success or failure, will be posted
  // in this signal. Check pay.signal.FundTransferStatusSignal for details on how the signal
  // payload will look like
  string client_workflow_signal_id = 1;
  // Unique identifier of the workflow instance where client expects to receive the payment status.
  // This is needed to send the txn status of fund transfer/international fund transfer
  celestial.workflow.ClientReqId domain_workflow_client_req_id = 2;
}
