// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package pay.payload;

import "api/typesv2/common/date.proto";
import "api/typesv2/payment_gateway.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/pay/payload";
option java_package = "com.github.epifi.gamma.api.pay.payload";

// InitiatePgPaymentsRecon is a parent workflow that initiates ReconcilePgPayments workflow for all the PG Programs.
message InitiatePgPaymentsReconWfPayload {
  // The day for which to reconcile the settlements from PG vendor. If the day is passed as 0, then the settlements for
  // the entire month are fetched and reconciled. Month and Year must be passed for the activity to work or else error
  // is returned.
  api.typesv2.common.Date recon_date = 1;
}

message ReconcilePgPaymentsRequestWfPayload {
  // the program for which the reconciliation needs to be done.
  api.typesv2.PaymentGatewayProgram pg_program = 1;

  // The day for which to reconcile the settlements from PG vendor. If the day is passed as 0, then the settlements for
  // the entire month are fetched and reconciled. Month and Year must be passed for the activity to work or else error
  // is returned.
  api.typesv2.common.Date recon_date = 2;

  // The starting page number of the recon API of the vendor from where the reconciliation has to be done.
  // If unset then the starts from the first page.
  optional int32 start_page_number = 3 [(validate.rules).int32.gte = 0];

  // The ending page number of the recon API of the vendor till which the reconciliation has to be done.
  // If unset then the workflow iterates till all the payments are present.
  // We allow only a maximum page number of 100 to be fetched from vendor, since that suffices with our current scale.
  // TODO: Revisit this if the scale increases.
  optional int32 end_page_number = 4 [(validate.rules).int32.lte = 100];
}
