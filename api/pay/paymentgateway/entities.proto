//go:generate gen_queue_pb
syntax = "proto3";

package api.pay.paymentgateway;

import "api/accounts/account_type.proto";
import "api/vendorgateway/payment_gateway/enums.proto";

option go_package = "github.com/epifi/gamma/api/pay/paymentgateway";
option java_package = "com.github.epifi.gamma.api.pay.paymentgateway";

enum PgEntity {
  // Default unspecified value
  PG_ENTITY_UNSPECIFIED = 0;
  // Represents an order entity
  PG_ENTITY_ORDER = 1;
  // Represents a payment entity
  PG_ENTITY_PAYMENT = 2;
  // Represents a token entity
  PG_ENTITY_TOKEN = 3;
}

message PgOrderEntity {
  // Unique identifier for the order
  string id = 1;
  // Type of entity (should be PG_ENTITY_ORDER)
  PgEntity entity = 2;
  // Amount of the order in smallest currency unit
  int64 amount = 3;
  // Amount paid for the order
  int64 amount_paid = 4;
  // Amount due for the order
  int64 amount_due = 5;
  // Currency code of the order
  string currency = 6;
  // Receipt number of the order
  string receipt = 7;
  // Offer ID associated with the order
  string offer_id = 8;
  // Current status of the order
  vendorgateway.pg.VendorOrderStatus status = 9;
  // Number of payment attempts made
  int32 attempts = 10;
  // Timestamp of order creation
  int64 created_at = 11;
  // Token details for the order
  Token token = 12;
}

message Token {
  // Method of authorization
  vendorgateway.pg.AuthorisationMethod method = 1;
  // Status of recurring payments
  string recurring_status = 2;
  // Reason for failure, if any
  string failure_reason = 3;
  // Currency code for the token
  string currency = 4;
  // Maximum amount allowed for the token
  int64 max_amount = 5;
  // Type of authentication
  // Deprecated in favour of auth_type_v2
  string auth_type = 6 [deprecated = true];
  // Expiration timestamp of the token
  int64 expire_at = 7;
  // Bank account details
  BankAccount bank_account = 8;
  // Amount of the first payment
  int64 first_payment_amount = 9;
  // The payment method used to authorise the mandate.
  vendorgateway.pg.AuthType auth_type_v2 = 10;
}

message BankAccount {
  // IFSC code of the bank
  string ifsc = 1;
  // Name of the bank
  string bank_name = 2;
  // Name of the account holder
  string name = 3;
  // Account number
  string account_number = 4;
  // Type of the account
  accounts.Type account_type = 5;
  // Email of the beneficiary
  string beneficiary_email = 6;
  // Mobile number of the beneficiary
  string beneficiary_mobile = 7;
}

message PgPaymentEntity {
  // Unique identifier for the payment
  string id = 1;
  // Type of entity (should be PG_ENTITY_PAYMENT)
  PgEntity entity = 2;
  // Amount of the payment in smallest currency unit
  int64 amount = 3;
  // Currency code of the payment
  string currency = 4;
  // Current status of the payment
  vendorgateway.pg.VendorPaymentStatus status = 5;
  // Associated order ID
  string order_id = 6;
  // Associated invoice ID
  string invoice_id = 7;
  // Whether the payment is international
  bool international = 8;
  // Method of payment
  vendorgateway.pg.PaymentMethod method = 9;
  // Amount refunded, if any
  int64 amount_refunded = 10;
  // Status of the refund
  vendorgateway.pg.RefundStatus refund_status = 11;
  // Whether the payment has been captured
  bool captured = 12;
  // Description of the payment
  string description = 13;
  // ID of the card used, if applicable
  string card_id = 14;
  // Bank used for the payment, if applicable
  string bank = 15;
  // Wallet used for the payment, if applicable
  string wallet = 16;
  // Virtual Payment Address, if applicable
  string vpa = 17;
  // Email of the payer
  string email = 18;
  // Contact number of the payer
  string contact = 19;
  // ID of the customer
  string customer_id = 20;
  // ID of the token used, if applicable
  string token_id = 21;
  // Fee charged for the payment
  int64 fee = 22;
  // Tax applied to the payment
  int64 tax = 23;
  // Error code, if any
  string error_code = 24;
  // Description of the error
  string error_description = 25;
  // Source of the error
  string error_source = 26;
  // Step where the error occurred
  string error_step = 27;
  // Reason for the error
  string error_reason = 28;
  // Data from the acquirer
  AcquirerData acquirer_data = 29;
  // Timestamp of payment creation
  int64 created_at = 30;
}

message AcquirerData {
  // Reference Retrieval Number
  string rrn = 1;
  // Authorization reference number
  string auth_ref_number = 2;
  // Bank's transaction ID
  string bank_transaction_id = 3;
  // Authorization code
  string auth_code = 4;
}

message PgTokenEntity {
  // Unique identifier for the token
  string id = 1;
  // Type of entity (should be PG_ENTITY_TOKEN)
  PgEntity entity = 2;
  // The actual token value
  string token = 3;
  // Associated bank
  string bank = 4;
  // Associated wallet
  string wallet = 5;
  // Method of tokenization
  string method = 6;
  // Whether the token is for recurring payments
  bool recurring = 7;
  // Details for recurring payments
  RecurringDetails recurring_details = 8;
  // Type of authentication
  string auth_type = 9;
  // Mandate Registration Number
  string mrn = 10;
  // Timestamp when the token was last used
  int64 used_at = 11;
  // Timestamp of token creation
  int64 created_at = 12;
  // Bank details associated with the token
  BankDetails bank_details = 13;
  // Maximum amount allowed for the token
  int64 max_amount = 14;
  // Expiration timestamp of the token
  int64 expired_at = 15;
  // Whether Dynamic Currency Conversion is enabled
  bool dcc_enabled = 16;
}

message RecurringDetails {
  // Status of the recurring mandate
  vendorgateway.pg.MandateStatus status = 1;
  // Reason for failure, if any
  string failure_reason = 2;
}

message BankDetails {
  // Name of the beneficiary
  string beneficiary_name = 1;
  // Account number
  string account_number = 2;
  // IFSC code of the bank
  string ifsc = 3;
  // Type of the account
  accounts.Type account_type = 4;
}
