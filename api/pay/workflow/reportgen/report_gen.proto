// This files defines request contracts for report generation workflow
syntax = "proto3";

package pay.workflow.reportgen;

import "api/pay/workflow/filter/filter.proto";
import "api/order/workflow.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/pay/workflow/reportgen";
option java_package = "com.github.epifi.gamma.api.pay.workflow.reportgen";


// Request message for ExecutionReportGenerator workflow
// Execution report generator workflow is a cron scheduled workflow which generates execution
// reports for the workflows which pending workflows and send the details to a sink.
message ExecutionReportGeneratorRequest {
  // Workflow type for which execution report needs to be generated
  order.OrderWorkflow workflow_type = 1;

  // Required: list of filters to be applied while generating the workflow report
  // all the individual filters in the list are applied with a `OR` operator
  repeated pay.workflow.filter.Filter filter_list = 2;

  // Required: workflow reports once generated needs to be sent to a particular sink
  // in order to notify the devs.
  ReportSink report_sink = 3;
}

// Response message for ExecutionReportGenerator workflow.
// Execution report generator workflow is a cron scheduled workflow which generates execution
// reports for the workflows which pending workflows and send the details to a sink.
message ExecutionReportGeneratorResponse {
  // Stores the timestamp till which execution report generation was done.
  // This information is passed on to the next workflow execution in the schedule to make sure,
  // the execution reports are generated doesn't have overlapping interval.
  google.protobuf.Timestamp last_reported_at = 1;
}

message ReportSink {
  oneof identifier {
    // email id where generated report are to be sent.
    string email_id = 1;
  }
}
