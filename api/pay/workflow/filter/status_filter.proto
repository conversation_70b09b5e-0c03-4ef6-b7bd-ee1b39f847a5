syntax = "proto3";

package pay.workflow.filter;

import "api/order/order.proto";
import "google/protobuf/duration.proto";


option go_package = "github.com/epifi/gamma/api/pay/workflow/filter";
option java_package = "com.github.epifi.gamma.api.pay.workflow.filter";


// StatusOption enables filtering workflow stuck in a set of workflow statuses irrespective of the workflow stage
message StatusOption {
  repeated order.OrderStatus status_list = 1;
}


// StatusWithSlaOption enables filtering of workflows stuck in a given status for more than the specified SLA
// duration
message StatusWithSlaOption {
  pay.workflow.filter.StatusOption status_filter_option = 1;
  google.protobuf.Duration sla = 2;
}
