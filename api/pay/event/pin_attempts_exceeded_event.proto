//go:generate gen_queue_pb
// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.pay.event;

import "api/queue/consumer_headers.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/pay/event";
option java_package = "com.github.epifi.gamma.api.pay.event";

// event to be sent when the number of pin retries exceeds the limit.
message PinAttemptsExceededEvent {
  // common request header across all the consumer grpc services.
  queue.ConsumerRequestHeader request_header = 1;

  // actor id of the user
  string actor_id = 2;

  // Vendor bank where retires are exceeded
  vendorgateway.Vendor partner_bank = 3;

  // source helps in identifying for which flow the retries exceeded
  Source source = 4;

  // pin type for which retries exceeded
  PinType pin_type = 5;
}

enum Source {
  SOURCE_UNSPECIFIED = 0;
  SOURCE_TRANSACTION = 1;
  SOURCE_CHANGE_PIN = 2;
  SOURCE_VIEW_CARD_DETAILS = 3;
}

enum PinType {
  PIN_TYPE_UNSPECIFIED = 0;
  PIN_TYPE_MPIN = 1;
  PIN_TYPE_UPI_PIN = 2;
}
