syntax = "proto3";

package api.pay.internationalfundtransfer.file_generator;

import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/pay/internationalfundtransfer/file_generator";
option java_package = "com.github.epifi.gamma.api.pay.internationalfundtransfer.file_generator";

message LRSLimit {
  // Pan for which lrs limit is consumed
  string pan = 1;
  // the money value of the consumed lrs limit
  google.type.Money consumed_lrs_limit = 2;
}


message LRSLimitList {
  repeated LRSLimit lrs_limits = 1;
}
