//go:generate gen_sql -types SOFDocumentInfo,SOFLimitStrategyDetails
syntax = "proto3";

package api.pay.internationalfundtransfer;

import "api/pay/internationalfundtransfer/international_fund_transfer_checks.proto";
import "api/pay/internationalfundtransfer/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/pay/internationalfundtransfer";
option java_package = "com.github.epifi.gamma.api.pay/internationalfundtransfer";

// SofDetails defines the details of sof (source of fund) document provided by user for a internation fund transfer
// e.g in case of US Stocks, SOF document can be user's Fi statement for last 1 year or it can be an ITR document
// this model will also contain result for what amount the user can transfer internationally based on different strategies
// applied on SOF document
message SofDetails {
  string id = 1;
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // type of doc (aa statement, fi statement, ITR etc)
  pay.internationalfundtransfer.SOFDocumentType document_type = 3 [(validate.rules).enum = {not_in: [0]}];
  // url of doc stored in s3
  string sof_document_url = 4;
  // more info about the document based on DocumentType
  SOFDocumentInfo document_info = 5;
  // validity of sof document
  google.protobuf.Timestamp valid_till = 6;
  // map of sof strategy name to the strategy details (e.g remittance limit offered, etc)
  // key is stringified value of SOFLimitStrategy enum defined in api/pay/internationalfundtransfer/enums.proto
  map<string, SOFLimitStrategyDetails> limit_strategies = 7;

  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;

  // sof state defines the state the sof is in e.g. processing for strategies compute, document generated, etc
  pay.internationalfundtransfer.SofState sof_state = 11;
}

// Additional info about the sof document
message SOFDocumentInfo {
  oneof info {
    FiAccountDocInfo fi_account_doc_info = 1;
    AaAccountDocInfo aa_account_doc_info = 2;
  }
}

message FiAccountDocInfo {
  // time after which all the transactions are captured in SOF
  google.protobuf.Timestamp from_time = 1;
  // time before which all the transactions are captured in SOF
  google.protobuf.Timestamp to_time = 2;
  // number of transaction in sof document
  int32 number_of_transactions = 3;
  // account id of fi saving account
  string fi_savings_account_id = 4;
  // oc url generated by docs service (this is later used to download and persist the document in IFT's s3 bucket by UploadSofDocument rpc)
  string temp_doc_url = 5;
}

message AaAccountDocInfo {
  // time after which all the transactions are captured in SOF
  google.protobuf.Timestamp from_time = 1;
  // time before which all the transactions are captured in SOF
  google.protobuf.Timestamp to_time = 2;
  // number of transaction in sof document
  int32 number_of_transactions = 3;
  // account id of AA account used to generate SOF
  string aa_account_id = 4;
  // doc url generated by docs service (this is later used to download and persist the document in IFT's s3 bucket by UploadSofDocument rpc)
  string temp_doc_url = 5;
}

// SOFLimitStrategyDetails defines the details of limit strategy used to determine the limit a user can outward remit in a given duration
// refer to different strategies below for more info on different strategies
message SOFLimitStrategyDetails {
  // timestamp when strategy was last updated
  google.protobuf.Timestamp updated_at = 1;
  oneof strategy {
    SOFLimitLoanFIFOStrategy loan_fifo_strategy = 2;
    SOFLimitManualOverride manual_override = 3;
  }
}

// strategy: loans are set of against subsequent debits in FIFO order and net remaining loan amt is calculated
// service logic can then use this net loan remaining loan amount along with other parameters like current account balance
// to determine the max amount the user can transfer internationally
// refer: https://docs.google.com/document/d/1HKEUbbfwL_2AVPds9LuM5rGGnHXbbYer7rlwBGjpPFw/edit#heading=h.83dnq23fk297
message SOFLimitLoanFIFOStrategy {
  // remaining loan amount in SOF in case the SOF has loans
  // loans are set off again subsequent debit, which finally determines the remaining loan amt
  google.type.Money remaining_loan_amount = 1;
  // transaction ids of loans found in sof
  repeated string loans_txn_ids = 2;
}

// In case of a manual review of user's SOF, the IFT limit can change for the user and it is captured here
message SOFLimitManualOverride {
  // details of person manually overriding the limit for the actor's SOF
  ManualOverridingUserInfo overridden_by = 1;
  // reason why the person is overriding the limit
  // e.g. found no loan in manual review, or found new source of income, etc
  pay.internationalfundtransfer.ManualOverrideReason reason = 2 [(validate.rules).enum = {not_in: [0]}];
  // explanation should be elaborative reasoning so anyone looking later can understand the exact reason
  string explanation = 3;
}


// info about the person manually overriding the limit
message ManualOverridingUserInfo {
  string email = 1 [(validate.rules).string.min_len = 1];
  string name = 2;
}

// SofDetailFieldMask to select specific columns in the modal
enum SofDetailFieldMask {
  SOF_DETAIL_FIELD_MASK_UNSPECIFIED = 0;
  SOF_DETAIL_FIELD_MASK_ID = 1;
  SOF_DETAIL_FIELD_MASK_ACTOR_ID = 2;
  SOF_DETAIL_FIELD_MASK_DOCUMENT_TYPE = 3;
  SOF_DETAIL_FIELD_MASK_SOF_DOCUMENT_URL = 4;
  SOF_DETAIL_FIELD_MASK_DOCUMENT_INFO = 5;
  SOF_DETAIL_FIELD_MASK_VALID_TILL = 6;
  SOF_DETAIL_FIELD_MASK_LIMIT_STRATEGIES = 7;
  SOF_DETAIL_FIELD_MASK_CREATED_AT = 8;
  SOF_DETAIL_FIELD_MASK_UPDATED_AT = 9;
  SOF_DETAIL_FIELD_MASK_DELETED_AT = 10;
  SOF_DETAIL_FIELD_MASK_SOF_STATE = 11;
}
