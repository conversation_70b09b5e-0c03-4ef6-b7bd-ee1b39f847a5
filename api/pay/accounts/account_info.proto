// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package pay.accounts;

import "api/accounts/account_type.proto";
import "api/typesv2/account/enums.proto";
import "api/upi/onboarding/enums/upi_account_status.proto";

option go_package = "github.com/epifi/gamma/api/pay/accounts";
option java_package = "com.github.epifi.gamma.api.pay.accounts";

// AccountInfo contains the account related informations like id, masked account number
// ifsc code of the account, derived account id, vpa related to account and so on.
message AccountInfo {
  // masked_account_number denotes the masked account number of the account
  string masked_account_number = 1;
  // ifsc code for the account
  string ifsc_code = 2;

  // AccountPreference specifies the preference of the account
  enum AccountPreference {
    ACCOUNT_PREFERENCE_UNSCPECIFIED = 0;
    ACCOUNT_PREFERENCE_PRIMARY = 1;
    ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS = 2;
  }
  // AccountPreference specifies the preference of the account
  AccountPreference account_preference = 3;

  // derived_account_id stores base64 encoding for DerivedAccountId which contains different account ids
  // like tpap_account_id, connected_account id etc.
  string derived_account_id = 4;
  // account type (ex. Saving account, current account etc.)
  .accounts.Type account_type = 5;
  // vpa for the given accountId
  string vpa = 6;
  // bank name of account
  string bank_name = 7;
  // status of the upi account (ACTIVE / INACTIVE)
  upi.onboarding.enums.UpiAccountStatus upi_account_status = 8;
  // Account Product Offering associated with the AccountType.
  // 1. This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
  //    We can consider UNSPECIFIED equivalent to APO_REGULAR in such cases.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
  api.typesv2.account.AccountProductOffering account_product_offering = 9;
}
