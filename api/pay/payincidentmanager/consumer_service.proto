//go:generate gen_queue_pb
syntax = "proto3";

package payincidentmanager;

import "api/order/order.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/pay/payincidentmanager";
option java_package = "com.github.epifi.gamma.api.pay.payincidentmanager";

// Defines the GRPC service to process updates to the PayIncidentManager,
// due to other events in the system.
// This GRPC service is registered with queue subscriber and
// RPC method will be invoked by the consumer on receiving an event
service Consumer {
  // ProcessOrderUpdate raises or resolves incidents on the basis of order updates recieved..
  // This method is invoked by queue subscriber to consume transaction terminal state events.
  rpc ProcessOrderUpdate (order.OrderUpdate) returns (ProcessOrderUpdateResponse) {}
}

message ProcessOrderUpdateResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}

// DEPRECATED : Please write all new Consumer methods in pay/payincidentmanager/consumer/service.proto
