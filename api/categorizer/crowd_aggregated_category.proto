syntax = "proto3";

package categorizer;

import "api/order/payment/accounting_entry_type.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/categorizer";
option java_package = "com.github.epifi.gamma.api.categorizer";

// It keeps the crowd aggregated categories derived from user recats of txns against a pi_id and accounting_entry w.r.t the user.
// That is, if a user does a txn against the pi_id, with given accounting_entry, then the mentioned categories should be used for
// categorizing the txn from user's perspective.
// These categories will be used by DS for categorising a txn.
message CrowdAggregatedCategory {
  // unique identifier
  string id = 1;
  // For txn against this pi_id, the given categories will be used.
  string pi_id = 2;
  // Accounting entry w.r.t user in a txn for whom the categories are saved.
  order.payment.AccountingEntryType accounting_entry = 3;
  // List of ontology ids to be used against the given pi_id and accounting_entry of txn.
  repeated string ontology_ids = 4;
  // Create time
  google.protobuf.Timestamp created_at = 5;
  // Update time
  google.protobuf.Timestamp updated_at = 6;
  // delete time
  google.protobuf.Timestamp deleted_at = 7;
}

enum CrowdAggregatedCategoryFieldMask {
  CROWD_AGGREGATED_CATEGORY_FIELD_MASK_UNSPECIFIED = 0;
  CROWD_AGGREGATED_CATEGORY_FIELD_MASK_ID = 1;
  CROWD_AGGREGATED_CATEGORY_FIELD_MASK_PI_ID = 2;
  CROWD_AGGREGATED_CATEGORY_FIELD_MASK_ACCOUNTING_ENTRY = 3;
  CROWD_AGGREGATED_CATEGORY_FIELD_MASK_ONTOLOGY_IDS = 4;
  CROWD_AGGREGATED_CATEGORY_FIELD_MASK_CREATED_AT = 5;
  CROWD_AGGREGATED_CATEGORY_FIELD_MASK_UPDATED_AT = 6;
  CROWD_AGGREGATED_CATEGORY_FIELD_MASK_DELETED_AT = 7;
}
