//go:generate gen_queue_pb
syntax = "proto3";

package categorizer.consumer;

import "api/aws/s3/s3.proto";
import "api/bigdata/categorizer/batch_order_details_event.proto";
import "api/firefly/accounting/consumer/service.proto";
import "api/order/aa/event.proto";
import "api/order/order.proto";
import "api/parser/publisher/payload.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/categorizer/consumer";
option java_package = "com.github.epifi.gamma.api.categorizer.consumer";

service Consumer {
  // ProcessOrderEvent processes order update event, calls categorisation engine and stores txn categories corresponding to each actor involved.
  rpc ProcessOrderEvent (order.OrderUpdate) returns (ProcessOrderEventResponse) {}

  // CategorizeAATxnEvent takes AA order events for txn categorization.
  rpc CategorizeAATxnEvent (order.aa.AATxnUpdate) returns (CategorizeAATxnEventResponse) {}

  // ProcessCCTxnEvent processes the credit card txn event to categorize the txn and store it in the DB and publish the category details for the downstream services to consume.
  rpc ProcessCCTxnEvent (firefly.accounting.consumer.CreditCardTransactionEvent) returns (ProcessCCTxnEventResponse) {}

  // ProcessBatchOrderDetailsEvent takes in batch of order details and their transaction details and processes the categories for them.
  rpc ProcessBatchOrderDetailsEvent(bigdata.categorizer.BatchOrderDetailsEvent) returns (ProcessBatchOrderDetailsEventResponse) {}

  // ProcessBatchAATransactionDetailsEvent gets aa txn details and processes the category for them.
  rpc ProcessBatchAATransactionDetailsEvent(bigdata.categorizer.BatchAATransactionDetailsEvent) returns (ProcessBatchAATransactionDetailsEventResponse) {}

  // ProcessCrowdAggregatedCategoryUpdateFile listens to the file updates in S3 which contains the user recat crowd aggregation results and updates them in the DB.
  rpc ProcessCrowdAggregatedCategoryUpdateFile(ProcessCrowdAggregatedCategoryUpdateFileRequest) returns (ProcessCrowdAggregatedCategoryUpdateFileResponse) {}

  // ProcessPayTxnBackfillEvent processes pay txn backfill event, which is used to backfill txn categories for fi orders.
  rpc ProcessPayTxnBackfillEvent (parser.publisher.PayTxnBackfillPublisher) returns (ProcessPayTxnBackfillEventResponse) {}
}

message ProcessOrderEventResponse {
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessBatchOrderDetailsEventResponse {
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message CategorizeAATxnEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessBatchAATransactionDetailsEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCCTxnEventResponse {
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCrowdAggregatedCategoryUpdateFileRequest {
  queue.ConsumerRequestHeader request_header = 1;
  repeated aws.s3.Record records = 2 [json_name = "Records"];
}

message ProcessCrowdAggregatedCategoryUpdateFileResponse {
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessPayTxnBackfillEventResponse {
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}
