syntax = "proto3";

package categorizer;

import "google/protobuf/timestamp.proto";
import "api/categorizer/enums.proto";

option go_package = "github.com/epifi/gamma/api/categorizer";
option java_package = "com.github.epifi.gamma.api.categorizer";

message UserDisplayCategory {
  DisplayCategory display_category = 1;

  string ontology_id = 2;

  DisplayCategoryType category_type = 3;

  // standard timestamp fields
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;
}
