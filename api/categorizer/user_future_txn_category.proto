syntax = "proto3";

package categorizer;

import "api/order/payment/accounting_entry_type.proto";
import "api/categorizer/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/categorizer";
option java_package = "com.github.epifi.gamma.api.categorizer";

// UserFutureTxnCategory will contain display category selected by actor for future similar transactions
// similar transactions are transactions of actor with the same payment instrument in a given accounting entry (credit/debit)
message UserFutureTxnCategory {
  string id = 1;
  // actor id of user requesting to save categories for future txns
  string actor_id = 2;
  // pi id of other actor in txn
  string pi_id = 3;
  // accounting entry to determine if the txn is credit or debit for the actor
  order.payment.AccountingEntryType   accounting_entry_type = 4;
  // display category the user selected for future cat on similar txn
  DisplayCategory display_category = 5;

  // standard timestamp fields
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;
}

enum UserFutureTxnCategoryFieldMask {
  USER_FUTURE_TXN_CATEGORY_FIELD_MASK_UNSPECIFIED = 0;
  USER_FUTURE_TXN_CATEGORY_ID = 1;
  USER_FUTURE_TXN_CATEGORY_ACTOR_ID = 2;
  USER_FUTURE_TXN_CATEGORY_PI_ID = 3;
  USER_FUTURE_TXN_CATEGORY_ACCOUNTING_ENTRY_TYPE = 4;
  USER_FUTURE_TXN_CATEGORY_DISPLAY_CATEGORY = 5;
  USER_FUTURE_TXN_CATEGORY_CREATED_AT = 6;
  USER_FUTURE_TXN_CATEGORY_UPDATED_AT = 7;
  USER_FUTURE_TXN_CATEGORY_DELETED_AT = 8;
}
