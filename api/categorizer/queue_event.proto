syntax = "proto3";

package categorizer;

import "api/categorizer/ds_categorizer.proto";
import "api/categorizer/enums.proto";
import "api/queue/consumer_headers.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/categorizer";
option java_package = "com.github.epifi.gamma.api.categorizer";


// TransactionCategoryDetail is the message to be published to a topic after category is assigned to txn
message TransactionCategoryDetail {
  reserved 1, 9, 10;
  //unique identifier for a txn
  string   transaction_id = 2;
  // unique order id for the txn
  string order_id = 3;
  // id of actor for which categories are send
  string   actor_id = 4;
  // represent the ontologies and display category corresponding to that of actor
  repeated CategoryDetail categories_detail = 5;
  // source from where event is triggered
  categorizer.Provenance Provenance = 6;
  // actor type (sender or receiver) for which categories are send
  TransactionActorType actor_type = 7;
  // source of txn (e.g. federal or AA)
  DataChannel data_channel = 8;
}

message CategoryDetail  {
  // human readable display category associated with an ontology
  categorizer.DisplayCategory display_category = 1;
  // ontologies for a txn
  FiOntology            fi_ontology = 2;
  // confidence score generated by DS for ontologies
  float                confidence_score = 3;
  // model which predicts the category
  CategorisationSource                categorisation_source = 4;
  // unique ontology identifier
  string ontology_id = 5;
  // category will be shown to app when isDisplayEnabled is true
  bool is_display_enabled = 6;
  // source of the category category DS for DS predicted categories. User for user-recat and user-future preference
  categorizer.Provenance category_provenance = 7;
  // display category type credit or debit
  categorizer.DisplayCategoryType display_category_type = 8;
}

message TransactionCategoryUpdateEvent{
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;
  TransactionCategoryDetail category_detail = 2;
}

message BatchTransactionCategoryUpdateEvent {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;
  repeated TransactionCategoryDetail category_details = 2 [(validate.rules).repeated.max_items = 1000];
}

enum OntologyUpdateEventType {
  ONTOLOGY_UPDATE_EVENT_TYPE_UNSPECIFIED = 0;
  // event is triggered on addition of a new ontology or there's an update in details of an existing ontology
  ONTOLOGY_UPDATE_EVENT_TYPE_UPSERT_ONTOLOGY = 1;
  // event is triggered when we're replacing an ontology.
  // all txns tagged with the older ontology will now be tagged with the new ontology
  ONTOLOGY_UPDATE_EVENT_TYPE_UPDATE_ONTOLOGY = 2;
}

// The event will be used to update any of these values - display category, L0-L3, ontology id
message OntologyUpdateEvent {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;
  // update event type
  OntologyUpdateEventType event_type = 2;

  oneof event_details {
    // contains details of the ontology being added/updated.
    // This field will be present for ONTOLOGY_UPDATE_EVENT_TYPE_UPSERT_ONTOLOGY event
    OntologyDetail ontology_details = 3;
    // will be present for ONTOLOGY_UPDATE_EVENT_TYPE_UPDATE_ONTOLOGY
    OntologyUpdateDetails ontology_update_details = 4;
  }
}

message OntologyDetail {
  // unique identifier for ontology
  string ontology_id = 1;
  // ontologies
  L0 L0 = 2;
  L1 L1 = 3;
  L2 L2 = 4;
  L3 L3 = 5;
  // human readable display category associated with an ontology
  string display_category_string = 6;
}

message OntologyUpdateDetails {
  // old ontology id that is being replaced by new ontology id.
  string old_ontology_id = 1;
  string new_ontology_id = 2;
}
