syntax = "proto3";

package categorizer;

import "api/categorizer/txn_category.proto";
import "api/order/payment/accounting_entry_type.proto";

option go_package = "github.com/epifi/gamma/api/categorizer";
option java_package = "com.github.epifi.gamma.api.categorizer";

message TxnActorCategories {
	string txn_id = 1;
  string actor_id = 2;
  order.payment.AccountingEntryType accounting_entry = 3;
  repeated categorizer.TxnCategory categories = 4;
}
