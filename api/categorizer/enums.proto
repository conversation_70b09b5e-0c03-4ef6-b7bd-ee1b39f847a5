syntax = "proto3";

package categorizer;

option go_package = "github.com/epifi/gamma/api/categorizer";
option java_package = "com.github.epifi.gamma.api.categorizer";

//go:generate gen_sql -types=DisplayCategoryType
// bank name of the user
enum BankName {
  BANK_NAME_UNSPECIFIED = 0;
  FI = 1;
  ICICI = 2;
  HDFC = 3;
}

// model name which predicts the category
enum CategorisationSource {
  CATEGORISATION_SOURCE_UNSPECIFIED = 0;
  MERCHANT_RESOLUTION_SERVICE = 1;
  MERCHANT_VPA_MCC = 2;
  MERCHANT_VPA_SUBCODE = 3;
  MERCHANT_CARD_MCC = 4;
  RULE_ENGINE = 5;
  // Google Place APIs @ https://developers.google.com/maps/documentation/places/web-service/overview
  // They help to figure out the place details such as category, address
  GOOGLE_PLACE_API = 6;
  // Peer_engine will mostly categorize P2P txns. For such cases, Peers_engine will be given as categorisation source
  PEERS_ENGINE = 7;
  ACCOUNT_AGGREGATOR_ENGINE = 8;
  REMARKS_ENGINE = 9;
  CATEGORIZATION_SOURCE_USER_FUTURE_PREFERENCE = 10;
  CROWD_AGGREGATION = 11;
  CATEGORIZATION_SOURCE_USER_SIMILAR_RECAT = 12;
  // CC Txn contains a category populated by the vendor. If this category is used for txn categorisation then categorisation source will be CATEGORIZATION_SOURCE_CC_TXN_CATEGORY.
  CATEGORIZATION_SOURCE_CC_TXN_CATEGORY = 13;
}

enum DisplayCategoryType {
  DISPLAY_CATEGORY_TYPE_UNSPECIFIED = 0;
  CREDIT = 1;
  DEBIT = 2;
}

// source from where category is generated
enum Provenance {
  PROVENANCE_UNSPECIFIED = 0;
  // transaction category was added based on DS categoriser model output and associated meta data for a transaction
  DS = 1;
  // transaction category was attached based on user's input on re-categorisation from the app.
  USER = 2;
}

//enum values of human readable display category associated with an ontology
enum DisplayCategory {
  reserved 83;
  DISPLAY_CATEGORY_UNSPECIFIED = 0;
  SALARY = 1;
  BONUS = 2;
  INCOME = 3;
  REDEMPTION = 4;
  INTEREST_DIVIDENDS = 5;
  REFUNDS = 6;
  CASHBACKS = 7;
  SELF_TRANSFER = 9;
  DEPOSITS = 10;
  STOCKS_MUTUAL_FUNDS = 11;
  SECURITIES = 12;
  INVESTMENTS = 13;
  CRYPTO = 14;
  EMI_PAYMENT = 15;
  LOAN_PREPAYMENT = 16;
  CREDIT_CARD = 18;
  CREDIT_REPAYMENT = 19;
  INSURANCE = 20;
  HOUSE_DEPOSIT = 21;
  UTILITY_BILLS = 22;
  MOBILE_DATA_RECHARGE = 23;
  MAINTENANCE_REPAIR = 25;
  FURNITURE_APPLIANCES = 26;
  HOUSING = 27;
  CHILDCARE = 28;
  DOMESTIC_HELP = 29;
  EDUCATION = 30;
  PETS = 31;
  FAMILY = 32;
  ONLINE_COURSES = 33;
  EATING_OUT = 34;
  ENTERTAINMENT = 35;
  BOOKS_PUBLICATIONS = 36;
  SPORTS_GAMES = 37;
  ONLINE_GAMING = 38;
  MOVIES = 41;
  CONCERTS = 42;
  MUSIC = 43;
  ENTERTAINMENT_SUBSCRIPTIONS = 44;
  CLOTHING_ACCESSORIES = 46;
  GADGETS = 47;
  APPLIANCES = 48;
  ONLINE_SHOPPING = 49;
  SHOPPING = 51;
  JEWELLERY = 52;
  GROCERIES_ESSENTIALS = 55;
  PERSONAL_CARE = 56;
  HEALTH_WELNESS = 57;
  FLIGHTS = 59;
  TRAINS = 60;
  ROAD_TRAVEL = 61;
  COMMUTE = 62;
  FUEL = 64;
  VEHICLE_SERVICE = 66;
  SHIPPING_LOGISTICS = 67;
  TRANSPORTATION = 68;
  MISCELLANEOUS = 69;
  BUSINESS_SPENDS = 70;
  DONATIONS = 71;
  SOFTWARE = 72;
  BANK_FEES = 73;
  FINANCIAL_SERVICES = 74;
  GIFTS = 75;
  TAXES = 76;
  TRAVEL = 77;
  CASH_WITHDRAWALS = 78;
  WALLET_RECHARGE = 79;
  SPENDS = 81;
  RENT = 82;

  EVENTS_ACTIVITIES = 84;
  CREDIT_CARD_LOAN = 85;
  FAMILY_CHILDCARE = 86;
  FOOD_DRINKS_GROCERIES = 87;
  GIFTS_CHARITY = 88;
  HOUSING_UTILITY = 89;
  PERSONAL_CARE_HEALTH = 90;
  REFUNDS_CASHBACKS = 91;
  SELF_CREDIT = 92;
  SELF_DEBIT = 93;
  SUBSCRIPTIONS = 94;
  ELECTRONICS_APPLIANCES = 95;
  TRAVEL_TRANSPORTATION = 96;
  WALLET_PAYMENT_GATEWAY = 97;

  FASHION = 98;
  STOCKS_MF = 99;
  REFUND_REWARD = 100;
  FAMILY_CARE = 101;
  CASH = 102;
  GIFT = 103;
  TAX = 104;
  GROCERY = 105;
  DINING = 106;
  BOOKS = 107;
  WALLET_PAYMENT = 108;
  TRAVEL_COMMUTE = 109;
  MONEY_TRANSFER = 110;
  DIVIDEND = 111;
  INTEREST = 112;
  UNKNOWN = 113;
  GADGET = 114;
  BORROWED = 115;
  INVESTMENT_SALE = 116;
  SELF_TRANSFER_CREDIT = 117;
  SELF_TRANSFER_DEBIT = 118;
  MONEY_TRANSFER_CREDIT = 119;
  MONEY_TRANSFER_DEBIT = 120;
  GROCERIES = 121;
  FOOD_DRINKS = 122;
  TRAVEL_VACATION = 123;
  INVESTMENT_WITHDRAWAL = 124;
  HOUSING_BILLS = 125;
  SETTLEMENT = 127;
  FAMILY_TRANSFER_CREDIT = 128;
  FAMILY_TRANSFER_DEBIT = 129;
  FEES_CHARGES = 130;
}

//enum value of first level of ontology
enum L0 {
  L0_UNSPECIFIED = 0;
  L0_INCOME = 1;
  L0_INVESTMENTS = 2;
  L0_SPEND = 3;
  L0_UNKNOWN = 4;
  L0_LOAN = 5;
  L0_DEBT_SETTLEMENT = 7;
}

//enum value of second level of ontology
enum L1 {
  L1_UNSPECIFIED = 0;
  L1_INCOME = 1;
  L1_INVESTMENTS = 2;
  L1_CREDIT_REPAYMENT = 3;
  L1_INSURANCE = 4;
  L1_HOUSING = 5;
  L1_HOME_AND_FAMILY = 6;
  L1_ENTERTAINMENT = 7;
  L1_SHOPPING = 8;
  L1_FOOD_DRINKS_AND_GROCERIES = 9;
  L1_PERSONAL_CARE_HEALTH_MEDICAL = 10;
  L1_TRANSPORT = 11;
  L1_MISC = 12;

  L1_BANKING = 13;
  L1_TAXES = 14;
  L1_TRAVEL_TRANSPORTATION = 15;
  L1_LOAN_DISBURSEMENT = 16;
  L1_UNKNOWN = 17;
  L1_LOAN = 18;
  L1_CREDIT_CARD = 19;
}

//enum value of third level of ontology
enum L2 {
  L2_UNSPECIFIED = 0;
  L2_INCOME_FROM_EMPLOYMENT = 1;
  L2_INCOME_FROM_RENT = 2;
  L2_REFUNDS = 3;
  L2_FROM_SELF = 5;
  L2_RECURRING_DEPOSITS = 7;
  L2_SMART_DEPOSITS = 8;
  L2_SIDS = 9;
  L2_MUTUAL_FUNDS_AMCS = 10;
  L2_TRANSFER_TO_TRADING_AC = 11;
  L2_MF_STOCKS_BONDS_COMMODITIES = 12;
  L2_PPF = 13;
  L2_PENSION_POLICY = 14;
  L2_OTHER = 15;
  L2_AUTO_LOAN = 16;
  L2_EDUCATION_LOAN = 17;
  L2_HOME_LOAN = 18;
  L2_PERSONAL_LOAN = 19;
  L2_OTHER_LOAN = 20;
  L2_CREDIT_CARD_PAYMENT = 21;
  L2_AUTO = 22;
  L2_GENERAL = 23;
  L2_LIFE = 24;
  L2_MEDICAL = 25;
  L2_TRAVEL = 26;
  L2_PROPERTY = 27;
  L2_MONTHLY_RENT = 28;
  L2_RENTAL_DEPOSIT = 29;
  L2_UTILITIES = 30;
  L2_HOME_IMPROVEMENT = 31;
  L2_MONTHLY_MAINTENANCE_CHARGES = 32;
  L2_APPLIANCES = 33;
  L2_BABIES_CHILDCARE = 35;
  L2_DOMESTIC_HELP = 36;
  L2_EDUCATION = 37;
  L2_EXTRA_CURRICULAR = 38;
  L2_PETS = 39;
  L2_POCKET_MONEY = 40;
  L2_SELF_IMPROVEMENT = 41;
  L2_ALCOHOL_BARS = 43;
  L2_ARTS = 44;
  L2_BOOKS = 45;
  L2_GAMES = 46;
  L2_GAMING_APPS = 47;
  L2_GAMING_SOFTWARE = 48;
  L2_HOBBIES = 49;
  L2_SPORTING_GOODS = 50;
  L2_SPORTS = 51;
  L2_MOVIES = 52;
  L2_CONCERTS = 53;
  L2_MUSIC = 54;
  L2_STREAMING_SERVICES = 55;
  L2_TV_CABLE = 56;
  L2_CLOTHING_FOOTWEAR = 58;
  L2_WEARABLE_ACCESSORIES = 59;
  L2_ELECTRONIC_GADGETS = 60;
  L2_ELECTRICAL_APPLIANCES = 61;
  L2_E_COMMERCE = 62;
  L2_FURNITURE_FURNISHINGS = 63;
  L2_DECORATION = 64;
  L2_JEWELLERY = 65;
  L2_CAFE_COFFEE_SHOP = 67;
  L2_RESTAURANTS = 68;
  L2_FOOD_DELIVERY = 69;
  L2_GROCERIES = 70;
  L2_AESTHETIC_TREATMENTS = 72;
  L2_BARBER_HAIR_STYLIST_SALON = 73;
  L2_SPA_MASSAGE = 74;
  L2_COSMETICS_TOILETRIES = 75;
  L2_GYM = 76;
  L2_MENTAL_HEALTH_WELLNESS = 77;
  L2_DENTIST = 78;
  L2_DOCTOR = 79;
  L2_EYE_CARE = 80;
  L2_HOSPITAL_EXPENSES = 81;
  L2_LAUNDRY_DRY_CLEANING = 82;
  L2_PHARMACY = 83;
  L2_AIR_TRAVEL = 85;
  L2_TRAIN_TRAVEL = 86;
  L2_BUS = 87;
  L2_RENTAL_CAR = 88;
  L2_CAB_RIDE = 89;
  L2_RIDE_HAILING = 90;
  L2_TOLLS = 91;
  L2_FUEL = 92;
  L2_PARKING = 93;
  L2_SERVICE_AUTO_PARTS = 94;
  L2_LOGISTICS = 95;
  L2_MULTI_SPECIALITY = 97;
  L2_BUSINESS_EXPENSES = 98;
  L2_CHARITY = 99;
  L2_ELECTRONICS_SOFTWARE = 100;
  L2_FEES_INTEREST = 101;
  L2_GIFTING_SERVICES_PRODUCTS = 102;
  L2_TAXES = 103;
  L2_HOTEL = 104;
  L2_TRAVEL_AGENCY = 105;
  L2_CASH_WITHDRAWALS = 106;
  L2_WALLET = 108;
  L2_CRYPTO = 109;
  L2_INCOME_FROM_INVESTMENTS = 110;
  L2_FIXED_DEPOSITS = 111;

  L2_BOOKS_PUBLICATIONS = 112;
  L2_EVENTS_ACTIVITIES = 113;
  L2_CREDIT_CARD = 114;
  L2_FIXED_INCOME_INVESTMENTS = 115;
  L2_SPORTS_GAMES = 116;
  L2_FAMILY_CHILDCARE = 117;
  L2_FOOD_DRINKS = 118;
  L2_GIFTS_CHARITY = 119;
  L2_GROCERIES_ESSENTIALS = 120;
  L2_UTILITY = 121;
  L2_REFUNDS_CASHBACKS = 122;
  L2_TO_SELF = 123;
  L2_SUBSCRIPTIONS = 124;
  L2_SOFTWARE = 125;
  L2_CLOTHING_ACCESSORIES = 126;
  L2_ELECTRONICS_APPLIANCES = 127;
  L2_STOCKS_MUTUAL_FUNDS = 128;
  L2_TAX_PREPARATION_SERVICE = 129;
  L2_INCOME_TAX_PAYMENT = 130;
  L2_TDS_AT_SOURCE = 131;
  L2_FROM_OTHERS = 132;
  L2_TO_OTHERS = 133;
  L2_TRAVEL_VACATION = 134;
  L2_COMMUTE = 135;
  L2_LOAN_DISBURSEMENT = 136;
  L2_BILLS = 137;
}

//enum value of fourth level of ontology
enum L3 {
  L3_UNSPECIFIED = 0;
  L3_INCOME_FROM_SALARY = 1;
  L3_BONUS = 2;
  L3_FD_REDEMPTION_PRINCIPAL_AND_INTEREST = 4;
  L3_INTEREST_FROM_FDS = 5;
  L3_INTEREST_FROM_SAVINGS_ACCOUNTS = 6;
  L3_SID_REDEMPTION_PRINCIPAL_AND_INTEREST = 7;
  L3_DIVIDENDS = 8;
  L3_LIQUIDATED_INVESTMENTS = 9;
  L3_OTHER_INCOME = 10;
  L3_PURCHASE_CANCELLATIONS = 11;
  L3_PURCHASE_REFUNDS = 12;
  L3_TAX_REFUNDS = 13;
  L3_CASHBACKS = 14;
  L3_CREDIT_INTEREST_CAPITALISED = 15;
  L3_OTHER = 16;
  L3_MARKET_LINKED_MFS = 17;
  L3_FIXED_INCOME_MFS = 18;
  L3_AUTO_LOAN_EMI = 19;
  L3_AUTO_LOAN_PREPAYMENT = 20;
  L3_EDUCATION_LOAN_EMI = 21;
  L3_EDUCATION_LOAN_PREPAYMENT = 22;
  L3_HOME_LOAN_EMI = 23;
  L3_HOME_LOAN_PREPAYMENT = 24;
  L3_PERSONAL_LOAN_EMI = 25;
  L3_PERSONAL_LOAN_PREPAYMENT = 26;
  L3_OTHER_LOANS_EMI = 27;
  L3_OTHER_LOANS_PREPAYMENT = 28;
  L3_ELECTRICITY = 29;
  L3_GAS_EG_LPG_ = 30;
  L3_WATER = 31;
  L3_INTERNET = 32;
  L3_PHONE = 33;
  L3_MASONRY = 35;
  L3_CARPENTRY = 36;
  L3_ELECTRICAL = 37;
  L3_GARDENING = 38;
  L3_PLUMBING = 39;
  L3_REPAIRS = 40;
  L3_BABY_SUPPLIES = 42;
  L3_CHILDCARE_FEES = 43;
  L3_COOK = 44;
  L3_DRIVER = 45;
  L3_MAID = 46;
  L3_SCHOOL_SUPPLIES = 47;
  L3_COLLEGE_FEES = 48;
  L3_SCHOOL_FEES = 49;
  L3_TUITION_FEES = 50;
  L3_FOOD = 51;
  L3_VET = 52;
  L3_MISC = 53;
  L3_MOOCS = 54;
  L3_PAPERBACKS = 55;
  L3_E_BOOKS = 56;
  L3_AUDIOBOOKS = 57;
  L3_MAGAZINES_AND_NEWSPAPERS = 58;
  L3_APP_DOWNLOAD = 60;
  L3_IN_APP_PURCHASES = 61;
  L3_ADVERTISING = 62;
  L3_BUSINESS_SOFTWARE = 63;
  L3_COMMISSIONS = 64;
  L3_LEGAL = 65;
  L3_OFFICE_SUPPLIES = 66;
  L3_PRINTING = 67;
  L3_SHIPPING = 68;
  L3_MOBILE_APPS = 70;
  L3_ANNUAL_FEES = 72;
  L3_BELOW_MAB_FEES = 73;
  L3_CARD_FEES = 74;
  L3_CHEQUE_BOUNCE_FEES = 75;
  L3_INTEREST_PAID = 76;
  L3_LATE_FEES = 77;
  L3_OVERDRAFT_FEES = 78;
  L3_TAX_PREPARATION_SERVICE = 80;
  L3_INCOME_TAX_PAYMENT = 81;
  L3_TDS_AT_SOURCE = 82;
  L3_ATM = 83;
  L3_BRANCH = 84;
  L3_SD_REDEMPTION_PRINCIPAL_AND_INTEREST = 85;
  L3_RD_REDEMPTION_PRINCIPAL_AND_INTEREST = 86;

  L3_ARTS = 87;
  L3_HOBBIES = 88;
  L3_MOVIES = 89;
  L3_CONCERTS = 90;
  L3_CREDIT_CARD_PAYMENT = 91;
  L3_CRYPTO = 92;
  L3_FIXED_DEPOSITS = 93;
  L3_RECURRING_DEPOSITS = 94;
  L3_SMART_DEPOSITS = 95;
  L3_PPF = 96;
  L3_PENSION_POLICY = 97;
  L3_EXTRA_CURRICULAR = 98;
  L3_GAMES = 99;
  L3_GAMING_SOFTWARE = 100;
  L3_SPORTING_GOODS = 101;
  L3_SPORTS = 102;
  L3_POCKET_MONEY = 103;
  L3_ALCOHOL_CIGARETTES = 104;
  L3_CAFE_COFFEE_SHOP = 105;
  L3_RESTAURANTS = 106;
  L3_FOOD_DELIVERY = 107;
  L3_CHARITY = 108;
  L3_GIFTING_SERVICES_PRODUCTS = 109;
  L3_GROCERIES = 110;
  L3_PROPERTY = 111;
  L3_MONTHLY_RENT = 112;
  L3_RENTAL_DEPOSIT = 113;
  L3_GAS = 114;
  L3_FURNITURE_FURNISHINGS = 115;
  L3_DOMESTIC_HELP = 116;
  L3_INCOME_FROM_RENT = 117;
  L3_MONTHLY_MAINTENANCE_CHARGES = 118;
  L3_MUSIC = 119;
  L3_STREAMING_SERVICES = 120;
  L3_TV_CABLE = 121;
  L3_CLOTHING_FOOTWEAR = 122;
  L3_WEARABLE_ACCESSORIES = 123;
  L3_JEWELLERY = 124;
  L3_ELECTRONIC_GADGETS = 125;
  L3_OTHER_MUTUAL_FUNDS_AMCS = 126;
  L3_TRANSFER_TO_TRADING_AC = 127;
  L3_MF_STOCKS_BONDS_COMMODITIES = 128;
  L3_APPLIANCES = 129;
  L3_NA = 130;
  L3_HOTEL = 132;
  L3_TRAVEL_AGENCY = 133;
  L3_AIR_TRAVEL = 134;
  L3_TRAIN_TRAVEL = 135;
  L3_BUS = 136;
  L3_RENTAL_CAR = 137;
  L3_CAB_RIDE = 138;
  L3_RIDE_HAILING = 139;
  L3_TOLLS = 140;
  L3_FUEL = 141;
  L3_PARKING = 142;
  L3_SERVICE_AUTO_PARTS = 143;
  L3_LOGISTICS = 144;
  L3_INTERNATIONAL_MF_STOCKS_BONDS_COMMODITIES = 145;
  L3_ECS_BOUNCE_FEES = 146;
  L3_EMI_BOUNCE_FEES = 147;
  L3_OTHER_BOUNCE_FEES = 148;
  L3_ACH_BOUNCE_FEES = 149;
  L3_TRANSACTION_BOUNCE_FEES = 150;
  L3_INSUFFICIENT_BALANCE_BOUNCE_FEES = 151;
}

enum TxnCategoryFieldMask {
  TXN_CATEGORY_FIELD_MASK_UNSPECIFIED = 0;
  TXN_CATEGORY_FIELD_MASK_ID = 1;
  TXN_CATEGORY_FIELD_MASK_TXN_ID = 2;
  TXN_CATEGORY_FIELD_MASK_ACTOR_ID = 3;
  TXN_CATEGORY_FIELD_MASK_ONTOLOGY_ID = 4;
  TXN_CATEGORY_FIELD_MASK_CONFIDENCE_SCORE = 5;
  TXN_CATEGORY_FIELD_MASK_PROVENANCE = 6;
  TXN_CATEGORY_FIELD_MASK_CREATED_AT = 7;
  TXN_CATEGORY_FIELD_MASK_UPDATED_AT = 8;
  TXN_CATEGORY_FIELD_MASK_DELETED_AT = 9;
  TXN_CATEGORY_FIELD_MASK_DS_CATEGORISATION_TIME = 10;
  TXN_CATEGORY_FIELD_MASK_MODEL_VERSION = 11;
  TXN_CATEGORY_FIELD_MASK_IS_DISPLAY_ENABLED = 12;
  TXN_CATEGORY_FIELD_MASK_DATA_CHANNEL = 13;
  TXN_CATEGORY_FIELD_MASK_CATEGORISATION_SOURCE = 14;
}

enum TxnCategoryOntologyFieldMask {
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_UNSPECIFIED = 0;
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_ONTOLOGY_ID = 1;
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L0 = 2;
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L1 = 3;
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L2 = 4;
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_L3 = 5;
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_DISPLAY_CATEGORY = 6;
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_CREATED_AT = 7;
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_UPDATED_AT = 8;
  TXN_CATEGORY_ONTOLOGY_FIELD_MASK_DELETED_AT = 9;
}

enum UserDisplayCategoryFieldMask {
  USER_DISPLAY_CATEGORY_FIELD_MASK_UNSPECIFIED = 0;
  USER_DISPLAY_CATEGORY_FIELD_MASK_DISPLAY_CATEGORY = 1;
  USER_DISPLAY_CATEGORY_FIELD_MASK_ONTOLOGY_ID = 2;
  USER_DISPLAY_CATEGORY_FIELD_MASK_CATREGORY_TYPE = 3;
  USER_DISPLAY_CATEGORY_FIELD_MASK_CREATED_AT = 4;
  USER_DISPLAY_CATEGORY_FIELD_MASK_UPDATED_AT = 5;
  USER_DISPLAY_CATEGORY_FIELD_MASK_DELETED_AT = 6;
}


// DataChannel defines the channel of data
// e.g. data channel can be fi, aa, gmail, sms, etc
enum DataChannel {
  DATA_CHANNEL_UNSPECIFIED = 0;
  DATA_CHANNEL_FI = 1;
  DATA_CHANNEL_AA = 2;
  DATA_CHANNEL_FI_CARD = 3;
}

enum TransactionActorType {
  ACTOR_TYPE_UNSPECIFIED = 0;
  FROM_ACTOR = 1;
  TO_ACTOR = 2;
}

enum OrderTag {
  reserved 2;
  ORDER_TAG_UNSPECIFIED = 0;
  ORDER_TAG_MUTUAL_FUND = 1;
  ORDER_TAG_INTEREST_FD = 3;
  ORDER_TAG_INTEREST_SAVINGS_ACCOUNT = 4;
  ORDER_TAG_JUMP_P2P_INVESTMENT = 5;
  // scheduled emi payment of a loan
  ORDER_TAG_LOAN_EMI_PAYMENT = 6;
  // loan prepayment is lump sum payment of part or full loan amount
  ORDER_TAG_LOAN_PREPAYMENT = 7;
  ORDER_TAG_DEBIT_CARD_CHARGES = 8;
}

// Display state enum refers to the state of a display category for a txn. Disabled means that the category is not shown for that txn.
// Enabled means the categories which are shown for the txn. Any includes both the types of categories, enabled and disabled.
enum DisplayState {
  DISPLAY_STATE_UNSPECIFIED = 0;
  DISPLAY_STATE_ENABLED = 1;
  DISPLAY_STATE_DISABLED = 2;
  DISPLAY_STATE_ANY = 3;
}
