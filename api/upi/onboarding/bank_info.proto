// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package upi.onboarding;

option go_package = "github.com/epifi/gamma/api/upi/onboarding";
option java_package = "com.github.epifi.gamma.api.upi.onboarding";

// BankInfo stores various info regarding the bank like the name,ifsc, logo etc
message BankInfo {

  // name denotes the bank name
  string name = 1;

  // logo denotes the image of the bank
  string logo = 2;

  // ifsc_code denotes the ifsc code of the bank
  string ifsc_code = 3;

  // UPIFeature enum represents the various features supported by a bank
  // in the context of the Unified Payments Interface (UPI) system.
  enum UPIFeature {
    UPI_FEATURE_UNSPECIFIED = 0;
    UPI_FEATURE_MANDATE = 1;
    UPI_FEATURE_REFUND = 2;
    UPI_FEATURE_AADHAAR = 3;
  }
  // supported_upi_features contains the list of UPI features supported by the bank
  repeated UPIFeature supported_upi_features = 4;
}
