// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package upi.onboarding.enums;

option go_package = "github.com/epifi/gamma/api/upi/onboarding/enums";
option java_package = "com.github.epifi.gamma.api.upi.onboarding.enums";

// UpiAccountStatus denotes the status of the upi account
enum UpiAccountStatus {
  UPI_ACCOUNT_STATUS_UNSPECIFIED = 0;

  // upi account is created for the user
  UPI_ACCOUNT_STATUS_CREATED = 1;

  // upi account is in active state
  UPI_ACCOUNT_STATUS_ACTIVE = 2;

  // upi account is inactive
  // this can occur in case of device update
  UPI_ACCOUNT_STATUS_INACTIVE = 3;

  // user has delinked the upi account
  UPI_ACCOUNT_STATUS_DELINKED = 4;

  // upi account is deleted
  // this can occur after phone factor update if the account does not comes in list account
  UPI_ACCOUNT_STATUS_DELETED = 5;
}
