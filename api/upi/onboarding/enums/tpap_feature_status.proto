syntax = "proto3";

package upi.onboarding.enums;

option go_package = "github.com/epifi/gamma/api/upi/onboarding/enums";
option java_package = "com.github.epifi.gamma.api.upi.onboarding.enums";

// TpapFeatureStatus: refers to the status of the tpap feature for an actor
enum TpapFeatureStatus {
  TPAP_FEATURE_STATUS_UNSPECIFIED = 0;
  TPAP_FEATURE_STATUS_ACTIVE = 1;
  TPAP_FEATURE_STATUS_INACTIVE = 2;
  TPAP_FEATURE_STATUS_IN_PROGRESS = 3;
  TPAP_FEATURE_STATUS_FAILED = 4;
}
