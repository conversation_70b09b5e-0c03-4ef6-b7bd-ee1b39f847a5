syntax = "proto3";

package upi.onboarding.consumer;

import "api/queue/consumer_headers.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/upi/onboarding/consumer";
option java_package = "com.github.epifi.gamma.api.upi.onboarding.consumer";

service Consumer {
  // MigrateVpa process the event for vpa migration consent
  // User consent is taken for vpa migration and is published, MigrateVpa consumes that and calls LinkInternalAccount RPC
  // That rpc creates a vpa for internal fi account
  rpc MigrateVpa (MigrateVpaRequest) returns (MigrateVpaResponse);

  // During ‘PORT’ (Transfer of Mobile Number from One PSP to Other PSP),
  // the Previous PSP will be notified through ReqMapperConfirmation API.
  rpc ProcessReqMapperConfirmation (ProcessReqMapperConfirmationRequest) returns (ProcessReqMapperConfirmationResponse);
}


message MigrateVpaRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  // actor id of actor whose vpa needs to be migrated
  string actor_id = 2;
  // client req to trigger internal account linking flow
  string client_req_id = 3;
}

message MigrateVpaResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

// Request struct for ProcessReqMapperConfirmation
message ProcessReqMapperConfirmationRequest {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  vendorgateway.Vendor partner_bank = 2;

  // since all the callbacks are received at a particular endpoint. It is the responsibility of the consumers
  // to unmarshal the data.
  bytes raw_data = 3;
}

// Response struct for ProcessReqMapperConfirmation
message ProcessReqMapperConfirmationResponse {
  // common response header across all the consumer grpc services.
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}
