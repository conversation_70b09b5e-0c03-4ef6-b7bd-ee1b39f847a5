syntax = "proto3";

package upi.onboarding;

import "api/accounts/account_type.proto";
import "api/typesv2/account/enums.proto";
import "api/upi/onboarding/enums/upi_account_enums.proto";
import "api/upi/onboarding/enums/upi_account_preference.proto";
import "api/upi/onboarding/enums/upi_account_status.proto";
import "api/upi/onboarding/enums/upi_pin_set_status.proto";
import "api/upi/upi.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/upi/onboarding";
option java_package = "com.github.epifi.gamma.api.upi/onboarding";

message UpiAccount {

  // unique identifier for upi accounts
  string id = 1;

  // id corresponding to the actor
  string actor_id = 2;

  // encrypted account number returned from NPCI
  string account_ref_number = 3;

  // masked account number
  string masked_account_number = 4;

  // ifsc code for the account
  string ifsc_code = 5;

  // id corresponding to an account if it is referenced from an existing account. Eg- Fi Savings account
  string account_ref_id = 6;

  // status of the upi account
  upi.onboarding.enums.UpiAccountStatus status = 7;

  // account type whether savings/tpap
  accounts.Type account_type = 8;

  // pin set status of the upi account
  upi.onboarding.enums.UpiPinSetStatus pin_set_status = 9;

  // account preference, eg- primary
  // if primary,this will be the default account for all the payments
  upi.onboarding.enums.UpiAccountPreference account_preference = 10;

  // time of creation of upi account
  google.protobuf.Timestamp created_at = 11;

  // last updated time of upi account
  google.protobuf.Timestamp updated_at = 12;

  // time of deletion of upi account
  google.protobuf.Timestamp deleted_at = 13;

  // account_meta_info stores the details regarding the account received from NPCI
  // like the cred allowed type and length of the cred allowed
  upi.ControlJson account_meta_info = 14;

  // Bank Name corresponding to the upi account
  string bank_name = 15;

  // upi_controls - represents the list of allowed payments types for a upi account
  repeated upi.onboarding.enums.UpiControl upi_controls = 16;

  // Account Product Offering associated with the AccountType.
  // This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
  api.typesv2.account.AccountProductOffering apo = 17;
}

enum UpiAccountFieldMask {
  UPI_ACCOUNT_FIELD_MASK_UNSPECIFIED = 0;
  UPI_ACCOUNT_FIELD_MASK_ACCOUNT_STATUS = 1;
  UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PREFERENCE = 2;
  UPI_ACCOUNT_FIELD_MASK_ACCOUNT_PIN_STATUS = 3;
  UPI_ACCOUNT_FIELD_MASK_UPI_CONTROLS_INFO = 4;
  UPI_ACCOUNT_FIELD_MASK_ACCOUNT_META_INFO = 5;
}
