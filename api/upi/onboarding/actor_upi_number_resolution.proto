// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package upi.onboarding;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/upi/onboarding";
option java_package = "com.github.epifi.gamma.api.upi.onboarding";

message ActorUpiNumberResolution {
  // unique identifier for the upi number to pi mapping
  string id = 1;
  // actor id of the actor aware of the upi number
  string actor_id = 2;
  // Upi number of which the current actor is aware of
  string upi_number = 3;
  // time of creation of mapping
  google.protobuf.Timestamp created_at = 6;
  // last updated time of mapping
  google.protobuf.Timestamp updated_at = 7;
  // time of deletion of mapping
  google.protobuf.Timestamp deleted_at = 8;
}
