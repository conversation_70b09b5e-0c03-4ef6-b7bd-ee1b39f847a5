// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package upi.txnref;

import "api/typesv2/user.proto";
import "api/upi/complaint/complaint.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/upi/txnref";
option java_package = "com.github.epifi.gamma.api.upi.txnref";

// Reference received in checkTxn ( dispute status but can be extended to other transaction status ref also)
// contains the payer/payee details with dispute settlement flag
message TransactionReference {

  // payment party (payer/payee) of transaction reference
  api.typesv2.PaymentParty payment_party = 1;

  // sequence number
  int32 seq_num = 2;

  // VPA of the Payer/Payee
  string vpa = 3;

  // settlement amount
  google.type.Money sett_amount = 4;

  // original amount
  google.type.Money org_amount = 5;

  // TODO(vivek) description?
  int32 approval_num = 6;

  // action taken on complaint till now.
  upi.complaint.ResponseComplaintAction response_complaint_action = 7;

  // reason for action on complaint. It provide the info regarding specific action reason.
  // This field is deprecated please use complaint_reason instead of this
  upi.complaint.ResponseComplaintReason response_complaint_reason = 8 [deprecated = true];

  // remarks on complaint action and reason from vendor.
  string remarks = 9;

  // timestamp of complaint action
  google.protobuf.Timestamp complaint_action_timestamp = 10;

  // reference id for complaint action
  string complaint_action_ref_id = 11;

  string complaint_reason = 12;
}
