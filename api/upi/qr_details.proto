// protolint:disable MAX_LINE_LENGTH
// Defines proto messages linked to a QR.

syntax = "proto3";

package upi;

import "google/protobuf/timestamp.proto";
import "api/upi/customer.proto";

option go_package = "github.com/epifi/gamma/api/upi";
option java_package = "com.github.epifi.gamma.api.upi";

message QRDetails {
  // Version of the QR
  string qr_version = 1;

  // timestamp when the qr was created (corresponds to ts in NPCI document)
  google.protobuf.Timestamp created_at = 2;

  // medium through which qr was created
  upi.QrMedium qr_medium = 3;

  // timestamp when the qr will expire (corresponds to expireTs in NPCI document)
  google.protobuf.Timestamp expire_at = 4;

  // For future use in JSON format, details still not specified by NPCI
  string query = 5;

  // Todo:(Abhinit) Description to be added based on NPCI doc update
  string version_token = 6;

  string stan = 7;
}

// Types of Medium through which QR can be created
enum QrMedium {
  QR_MEDIUM_UNSPECIFIED = 0;
  QR_MEDIUM_PICK_FROM_GALLERY = 1;
  QR_MEDIUM_APP = 2;
  QR_MEDIUM_POS = 3;
  QR_MEDIUM_PHYSICAL = 4;
  QR_MEDIUM_ATM = 5;
  QR_MEDIUM_WEB = 6;
}

// InternationalQrInfo - object store qrDetails and forex details in Redis as a key-val pair
message UpiInternationalQrInfo {
  upi.QRDetails qr_details = 1;
  repeated upi.FxDetail forex_detail_list = 2;
  upi.Institution institution = 3;
}
