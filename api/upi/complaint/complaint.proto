syntax = "proto3";

package upi.complaint;

import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/upi/complaint";
option java_package = "com.github.epifi.gamma.api.upi.complaint";


// denotes the complaint action for raising a complaint request
enum RequestComplaintAction {
  REQUEST_COMPLAINT_ACTION_UNSPECIFIED = 0;

  // complaint action to raise a complain
  COMPLAINT_RAISED = 1;
}

// denotes the complaint reason for raising a complaint request
enum RequestComplaintReason {
  REQUEST_COMPLAINT_REASON_UNSPECIFIED = 0;

  // Beneficiary account is not credited for a pending / timeout transaction
  ACCOUNT_NOT_CREDITED_FOR_PENDING_TRANSACTION = 1;

  // Goods/services are not provided
  GOODS_NOT_PROVIDED = 2;

  // Credit not processed for cancelled or returned goods and services
  CREDIT_NOT_PROCESSED_FOR_CANCELLED_GOODS = 3;

  // account debited but transaction confirmation not received by merchant
  TRANSACTION_CONFIRMATION_NOT_RECEIVED = 4;

  // Paid by alternate means / Duplicate payment
  DUPLICATE_PAYMENT = 5;

  // Customer account not credited back for failed merchant transaction
  ACCOUNT_NOT_CREDITED_FOR_MERCHANT_TRANSACTION = 6;

  // Customer account not credited back for failed P2P transaction
  ACCOUNT_NOT_CREDITED_FOR_P2P_TRANSACTION = 7;

  //Beneficiary account is not credited for successful pay transaction
  ACCOUNT_NOT_CREDITED_FOR_SUCCESSFUL_TRANSACTION = 8;

  //Customer account not credited back for declined transaction
  ACCOUNT_NOT_CREDITED_FOR_DECLINED_TRANSACTION = 9;

  //Customer account has not yet reversed for a declined pay transaction
  ACCOUNT_NOT_REVERSED_FOR_DECLINED_TRANSACTION = 10;
}

// denotes the complaint action received in complaint response
enum ResponseComplaintAction {
  RESPONSE_COMPLAINT_ACTION_UNSPECIFIED = 0;

  // refund reversal confirmation
  REFUND_REVERSAL_CONFIRMATION = 1;

  CHARGEBACK_ACCEPTANCE = 2;

  ARBITRATION_ACCEPTANCE = 3;

  ARBITRATION_CONTINUATION = 4;

  ARBITRATION_VERDICT = 5;

  ARBITRATION_WITHDRAWN = 6;

  PRE_ARBITRATION_ACCEPTANCE = 7;

  ARBITRATION_RAISE = 8;

  CHARGEBACK_RAISE = 9;

  CREDIT_ADJUSTMENT = 10;

  DEBIT_REVERSAL_CONFIRMATION = 11;

  DIFFERED_CHARGEBACK_ACCEPTANCE = 12;

  DIFFERED_PRE_ARBITRATION_ACCEPTANCE = 13;

  DIFFERED_ARBITRATION_RAISE = 14;

  DIFFERED_CHARGEBACK_RAISE = 15;

  FRAUD_CHARGEBACK_RAISE = 16;

  FRAUD_CHARGEBACK_ACCEPT = 17;

  FRAUD_CHARGEBACK_REPRESENTMENT = 18;

  DIFFERED_PRE_ARBITRATION_RAISE = 19;

  DIFFERED_PRE_ARBITRATION_DECLINED = 20;

  DIFFERED_RE_PRESENTMENT_RAISE = 21;

  MANUAL_ADJUSTMENT = 22;

  PRE_ARBITRATION_RAISE = 23;

  PRE_ARBITRATION_DECLINED = 24;

  RESPONSE_TO_COMPLAINT = 25;

  RE_PRESENTMENT_RAISE = 26;

  RET = 27;

  TCC = 28;

  WRONG_CREDIT_CHARGEBACK_ACCEPTANCE = 29;

  WRONG_CREDIT_REPRESENTMENT = 30;

  WRONG_CREDIT_CHARGEBACK_RAISE = 31;
}

// denotes the complaint reason received in complaint response
enum ResponseComplaintReason {
  RESPONSE_COMPLAINT_REASON_UNSPECIFIED = 0;

  // customer account has been credited
  CUSTOMER_ACCOUNT_CREDITED = 1;
}

message Complaint {

  // defines the complaint action
  upi.complaint.RequestComplaintAction complaint_action = 1;

  // defines the complaint reason
  upi.complaint.RequestComplaintReason complaint_reason = 2;

  // defines the adjustment amount
  google.type.Money complaint_amount = 3;
}

// ComplaintDisputeState is the state of the raised dispute
enum ComplaintDisputeState {
  COMPLAINT_DISPUTE_STATE_UNSPECIFIED = 0;
  // request to raise dispute is initiated and is IN_PROGRESS
  COMPLAINT_DISPUTE_STATE_RAISE_IN_PROGRESS = 1;
  // request to raise dispute failed
  COMPLAINT_DISPUTE_STATE_RAISE_FAILED = 2;
  // current status of raised dispute is in progress
  COMPLAINT_DISPUTE_STATE_IN_PROGRESS = 3;
  // raised dispute is resolved
  COMPLAINT_DISPUTE_STATE_RESOLVED = 4;
}
