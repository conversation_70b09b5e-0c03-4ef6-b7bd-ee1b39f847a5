syntax = "proto3";

package upi.domain_model;

option go_package = "github.com/epifi/gamma/api/upi/domain_model";
option java_package = "com.github.epifi.gamma.api.upi/domain_model";


enum MandateType {
  TYPE_UNSPECIFIED = 0;

  // Allows a customer to create a mandate.
  CREATE = 1;

  // Allows a customer to revoke a mandate.
  REVOKE = 2;

  // Allows a customer to revoke a mandate.
  UPDATE = 3;
}

message Recurrence {

  message RecurrenceRule{

    // mandate recurrence rule value
    string value = 1;

    enum RecurrenceRuleType{
      RECURRENCE_RULE_TYPE_UNSPECIFIED = 0;
      BEFORE = 1;
      ON = 2;
      AFTER = 3;
    }

    // mandate recurrence rule type
    RecurrenceRuleType recurrence_rule_type = 2;
  }

  // mandate recurrence rule
  RecurrenceRule recurrence_rule = 1;

  // mandate recurrence pattern
  domain_model.RecurrencePattern recurrence_pattern = 2;

  string value = 3;
}

enum AmountRule{
  AMOUNT_RULE_UNSPECIFIED = 0;
  MAX = 1;
  EXACT = 2;
}

enum RecurrencePattern {
  RECURRENCE_PATTERN_UNSPECIFIED = 0;
  ONE_TIME = 1;
  DAILY = 2;
  WEEKLY = 3;
  FORTNIGHTLY = 4;
  MONTHLY = 5;
  BI_MONTHLY = 6;
  QUARTERLY = 7;
  HALF_YEARLY = 8;
  YEARLY = 9;
  AS_PRESENTED = 10;
}
