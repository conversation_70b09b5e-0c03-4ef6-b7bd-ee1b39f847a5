syntax = "proto3";

package upi.mandate;

import "api/upi/mandate/mandate_requests.proto";

option go_package = "github.com/epifi/gamma/api/upi/mandate";
option java_package = "com.github.epifi.gamma.api.upi/mandate";

message MandateCreationPayload {
  // denotes if the mandate is revokeable or not
  bool revokeable = 1;
  // if false the payee shouldn't receive notification for the mandate
  // can be false only in case of one time mandate
  bool share_to_payee = 2;
  // denotes if the funds should be blocked in the payer's account
  bool block_fund = 3;
  // unique mandate number of length 32
  // this will be surfaced the user and unique for each mandate
  string umn = 4;
  // mandate request info containing request related params
  .upi.mandate.Payload req_info = 5;
}
