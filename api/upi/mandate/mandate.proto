syntax = "proto3";

package upi.mandate;

import "google/type/money.proto";
import "google/type/date.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/upi/mandate";
option java_package = "com.github.epifi.gamma.api.upi/mandate";

message Mandate{

  // purpose of the mandate
  string name = 1;

  // same as the transaction id
  string transaction_id = 2;

  // unified mandate number
  string umn = 3;

  // mandate revokeablility
  bool revokeable = 4;

  // can be N only for one time mandates
  ShareToPayee share_to_payee = 5;

  // When an authorized create mandate request comes to remitter bank where ‘blockfund’ tag is set,
  // then remitter bank needs to block the specified amount in customers account. This
  // functionality will only be allowed for one-time mandates.
  // relevant for scenarios like IPO and mutual funds.
  bool block_fund = 6;

  Validity validity = 7;

  // mandate amount
  google.type.Money amount = 8;

  AmountRule amount_rule = 9;

  // mandate recurrence
  Recurrence recurrence = 10;

  // time at which the mandate request received
  google.protobuf.Timestamp timestamp = 11;

  // action taken on recurring payment / request type
  string type = 12;
}

enum AmountRule{
  AMOUNT_RULE_UNSPECIFIED = 0;
  MAX = 1;
  EXACT = 2;
}

enum RecurrencePattern {
  RECURRENCE_PATTERN_UNSPECIFIED = 0;
  ONE_TIME = 1;
  DAILY = 2;
  WEEKLY = 3;
  FORTNIGHTLY = 4;
  MONTHLY = 5;
  BI_MONTHLY = 6;
  QUARTERLY = 7;
  HALF_YEARLY = 8;
  YEARLY = 9;
  AS_PRESENTED = 10;
}

message Recurrence {

  message RecurrenceRule{

    // mandate recurrence rule value
    string value = 1;

    enum RecurrenceRuleType{
      RECURRENCE_RULE_TYPE_UNSPECIFIED = 0;
      BEFORE = 1;
      ON = 2;
      AFTER = 3;
    }

    // mandate recurrence rule type
    RecurrenceRuleType recurrence_rule_type = 2;
  }

  // mandate recurrence rule
  RecurrenceRule recurrence_rule = 1;

  // mandate recurrence pattern
  RecurrencePattern recurrence_pattern = 2;

  string text = 3;
}

enum MandateInitiatedBy {
  // unspecified
  MANDATE_INITIATED_BY_UNSPECIFIED = 0;
  // PAYER
  PAYER = 1;
  // PAYEE
  PAYEE = 2;
}

enum MandateType {
  TYPE_UNSPECIFIED = 0;

  // Allows a customer to create a mandate.
  CREATE = 1;

  // Allows a customer to revoke a mandate.
  REVOKE = 2;

  // Allows a customer to revoke a mandate.
  UPDATE = 3;

  // Allows a customer to pause a mandate.
  PAUSE = 4;

  // Allows a customer to unpause a mandate
  UNPAUSE = 5;

  // Mandate Notification sent to payer
  MANDATE_NOTIFICATION = 6;
}

// defines timeframe of vaildity of mandate
message Validity {

  // start time of validity
  google.type.Date start = 1;

  // end time of vailidity
  google.type.Date end = 2;

}

// we cannot use bool as we in some cases we need to send empty
// fields to vendor when this field is not applicable for the flow
enum ShareToPayee {
  SHARE_TO_PAYEE_UNSPECIFIED = 0;
  // the mandate should be shared with payee
  SHARE_TO_PAYEE_TRUE = 1;
  // mandate should not be shared with payee
  SHARE_TO_PAYEE_FALSE = 2;
}
