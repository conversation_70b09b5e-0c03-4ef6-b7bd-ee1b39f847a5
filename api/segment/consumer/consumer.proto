//go:generate gen_queue_pb
syntax = "proto3";

package segment.consumer;

import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/segment/consumer";
option java_package = "com.github.epifi.gamma.api.segment.consumer";

service Consumer {
  // TriggerSegmentExport triggers segment export to s3 bucket and publishes poll id to poll-segment-export queue
  rpc TriggerSegmentExport (TriggerSegmentExportRequest) returns (TriggerSegmentExportResponse);

  // PollSegmentExport polls segment export status and republishes to trigger-segment-export queue if status is failed
  rpc PollSegmentExport (PollSegmentExportRequest) returns (PollSegmentExportResponse);

  // ProcessSegmentExportPartFile processes the part file event as triggered via S3 file creation
  //  and dumps data to redis
  // Deprecated in favour of ProcessSegmentExportPartFileV2.
  rpc ProcessSegmentExportPartFile (ProcessSegmentExportPartFileRequest) returns (ProcessSegmentExportPartFileResponse);

  // UploadSegmentExportPartFileCsv processes the part file event as triggered via S3 file creation
  //  and uploads csv to S3 to be eventually dumped into data lake
  rpc UploadSegmentExportPartFileCsv (ProcessSegmentExportPartFileRequest) returns (ProcessSegmentExportPartFileResponse);

  // ProcessSegmentExportPartFileV2 processes the part file event as triggered via S3 file creation and dumps data to redis.
  // This flow consumes data written to s3 via in house spark job instead of pinpoint.
  rpc ProcessSegmentExportPartFileV2 (ProcessSegmentExportPartFileV2Request) returns (ProcessSegmentExportPartFileV2Response);

  // CompareSegmentInstances compares the segment instance before and the segment instance created after export
  //  and publishes the diff as rudder events (can be extended with other diff processors)
  rpc CompareSegmentInstances (CompareSegmentInstancesRequest) returns (CompareSegmentInstancesResponse);
}

message CompareSegmentInstancesRequest {
  // common request header across all the consumer grpc services
  queue.ConsumerRequestHeader request_header = 1;
  // segment id to process export
  string segment_id = 2;
  // segment instance id created during export
  string segment_instance_id = 3;
  //  timestamp(units) at which instance got exported at
  int64 export_timestamp = 4;
}

message CompareSegmentInstancesResponse {
  // common response header across all the consumer grpc services
  queue.ConsumerResponseHeader response_header = 1;
}

message TriggerSegmentExportRequest {
  // common request header across all the consumer grpc services
  queue.ConsumerRequestHeader request_header = 1;

  // segment id to process export
  string segment_id = 2;

  // aws pinpoint application id
  string application_id = 3;
  // isForcedExport force export of already present static segment.
  // Static segments are exported only once and if we want to update these segments then set this flag to true.
  // Note: This field is only applicable for static segment, dynamic segments are always exported, irrespective of this flag.
  bool is_forced_export = 4;
}

message TriggerSegmentExportResponse {
  // common response header across all the consumer grpc services
  queue.ConsumerResponseHeader response_header = 1;
}

message PollSegmentExportRequest {
  // common request header across all the consumer grpc services
  queue.ConsumerRequestHeader request_header = 1;

  // export job id to poll status
  string job_id = 2;

  // aws pinpoint application id
  string application_id = 3;

  // segment id for tracing purpose
  string segment_id = 4;

  // segment instance id for tracing purpose
  string segment_instance_id = 5;
}

message PollSegmentExportResponse {
  // common response header across all the consumer grpc services
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessSegmentExportPartFileRequest {
  // common request header across all the consumer grpc services
  queue.ConsumerRequestHeader request_header = 1;

  repeated Record records = 2 [json_name = "Records"];
}

message ProcessSegmentExportPartFileResponse {
  // common response header across all the consumer grpc services
  queue.ConsumerResponseHeader response_header = 1;
}

// Record as triggered by S3 events.
// Ref: https://docs.aws.amazon.com/AmazonS3/latest/userguide/notification-content-structure.html
// TODO(kunal): Revisit this and remove params not being used, to do minimal parsing
//  and add json name tag for remaining columns
message Record {
  string eventVersion = 1;
  string eventSource = 2;
  string awsRegion = 3;
  string eventTime = 4;
  string eventName = 5;
  UserIdentity userIdentity = 6;
  RequestParameters requestParameters = 7;
  map<string, string> responseElements = 8;
  S3 s3 = 9;
  GlacierEventData glacierEventData = 10;
  message UserIdentity {
    string principalId = 1;
  }
  message RequestParameters {
    string sourceIPAddress = 1;
  }
  message S3 {
    string s3SchemaVersion = 1;
    string configurationId = 2;
    message Bucket {
      string name = 1;
      message OwnerIdentity {
        string principalId = 1;
      }
      OwnerIdentity ownerIdentity = 2;
      string arn = 3;
    }
    Bucket bucket = 3;
    message Object {
      string key = 1;
      int64 size = 2;
      string eTag = 3;
      string versionId = 4;
      string sequencer = 5;
    }
    Object object = 4;
  }
  message GlacierEventData {
    message RestoreEventData {
      string lifecycleRestorationExpiryTime = 1;
      string lifecycleRestoreStorageClass = 2;
    }
    RestoreEventData restoreEventData = 1;
  }
}

message ProcessSegmentExportPartFileV2Request {
  // common request header across all the consumer grpc services
  queue.ConsumerRequestHeader request_header = 1;

  repeated Record records = 2 [json_name = "Records"];
}

message ProcessSegmentExportPartFileV2Response {
  // common response header across all the consumer grpc services
  queue.ConsumerResponseHeader response_header = 1;
}
