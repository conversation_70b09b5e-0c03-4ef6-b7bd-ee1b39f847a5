//go:generate gen_sql -types=SegmentProvider,SegmentType,Status
syntax = "proto3";

package segment;

option go_package = "github.com/epifi/gamma/api/segment";
option java_package = "com.github.epifi.gamma.api.segment";

enum SegmentStatus {
  SEGMENT_STATUS_UNSPECIFIED = 0;
  // success
  SEGMENT_INSTANCE_FOUND = 1;
  // segment id not found
  SEGMENT_NOT_FOUND = 2;
  // segment id found, but no instance before given timestamp
  SEGMENT_INSTANCE_NOT_FOUND = 3;
  // internal error
  INTERNAL_ERROR = 4;
}

// contains enum representation of segment fields to perform update on database
enum SegmentUpdateFieldMask {
  SEGMENT_UPDATE_FIELD_MASK_UNSPECIFIED = 0;
  SEGMENT_UPDATE_FIELD_MASK_EXPORT_TILL = 1;
}

enum SegmentExpressionStatus {
  SEGMENT_EXPRESSION_STATUS_UNSPECIFIED = 0;
  // success
  OK = 1;
  // segment id not found, segment instance not found, or internal error
  ERROR = 2;
}

enum SegmentProvider {
  SEGMENT_PROVIDER_UNSPECIFIED = 0;
  AWS = 1;
  GCP = 2;
}

enum SegmentType {
  SEGMENT_TYPE_UNSPECIFIED = 0;
  STATIC = 1;
  DYNAMIC = 2;
  // static segment on the basis of dynamic attribute filters
  DYNAMIC_SINGLE_EXPORT = 3;
}

enum Status {
  Status_UNSPECIFIED = 0;
  Status_ACTIVE = 1;
  Status_INACTIVE = 2;
}

// contains enum representation of segment metadata fields to perform update on database
enum SegmentMetadataUpdateFieldMask {
  SEGMENT_METADATA_UPDATE_FIELD_MASK_UNSPECIFIED = 0;
  SEGMENT_METADATA_UPDATE_FIELD_MASK_SEGMENT_NAME = 1;
  SEGMENT_METADATA_UPDATE_FIELD_MASK_QUERY_FILTER = 2;
  SEGMENT_METADATA_UPDATE_FIELD_MASK_SEGMENT_TYPE = 3;
  SEGMENT_METADATA_UPDATE_FIELD_MASK_STATUS = 4;
  SEGMENT_METADATA_UPDATE_FIELD_MASK_OWNER = 5;
  SEGMENT_METADATA_UPDATE_FIELD_MASK_APPROVAL_STATUS = 6;
  SEGMENT_METADATA_UPDATE_FIELD_MASK_REVIEWED_BY = 7;
  SEGMENT_METADATA_UPDATE_FIELD_MASK_EXPIRES_AT = 8;
  SEGMENT_METADATA_UPDATE_FIELD_MASK_SEGMENT_PATH = 9;
}

// contains enum representation of segmentation metadata possible actions in dev action
// dev actions are: segment_metadata_actions and set_segment_metadata_approval_status
enum SegmentMetadataAction {
  SEGMENT_METADATA_ACTION_UNSPECIFIED = 0;
  SEGMENT_METADATA_ACTION_VALIDATE_SEGMENT = 1;
  SEGMENT_METADATA_ACTION_CREATE_SEGMENT = 2;
  SEGMENT_METADATA_ACTION_UPDATE_SEGMENT = 3;
  SEGMENT_METADATA_ACTION_VIEW_SEGMENT_METADATA = 4;
  SEGMENT_METADATA_ACTION_APPROVE_SEGMENT = 5;
  SEGMENT_METADATA_ACTION_GET_SEGMENT_BY_ID = 6;
}
