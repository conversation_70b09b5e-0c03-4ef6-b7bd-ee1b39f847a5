syntax = "proto3";

package vendorgateway.onsurity;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/vendorgateway/onsurity";
option java_package = "com.github.epifi.gamma.api.vendorgateway.onsurity";

message DownloadDocumentsApiRequest{
  vendorgateway.RequestHeader header = 1;

  oneof contact {
    string phone_number = 2; // Member’s phone number ex:7676767673
    string email_id = 3;  // Member’s email address  ex:<EMAIL>
  }

  string plan_name=4 [(validate.rules).string.min_len = 1]; // Member’s plan (get this from member details API) // Opal, Ruby, etc
  string policy_type=5 [(validate.rules).string.min_len = 1];  // Member’s plan type (get this from member details API) ex:1A, 2A%2B2C, 4A%2B4C
}


message DownloadDocumentsApiResponse {
  rpc.Status status = 1;
  Data data = 2;
  Meta meta = 3;

  message Data {
    string file_url = 1;
  }
  message Meta {
    bool is_success = 1;
    string status_code=2;
    string response_message=3;
    string display_message = 4;
    repeated error errors= 5;
  }
  message error{
    string message=1;
    string action=2;
    string value=3;
    string row=4;
  }

}



