syntax = "proto3";

package vendorgateway.onsurity;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/vendorgateway/onsurity";
option java_package = "com.github.epifi.gamma.api.vendorgateway.onsurity";


message MemberInvitationAPIRequest {

  vendorgateway.RequestHeader header = 1; // Request header containing metadata

  EmployeeDetails employee = 2; // Details of the employee

  repeated Dependent dependents = 3; // Dependent details

  HealthPlan health_plan = 4;  // Health plan details

  repeated EmployerPurchaseDetail employer_purchase_details = 5; // Employer purchase details

}

message EmployeeDetails {


  string phone_number = 1; // Member’s phone number (Example: "**********")
  string email_id = 2; // Member’s email address (Example: "<EMAIL>")

  string name = 3 [(validate.rules).string.min_len = 1]; // Member’s name (Example: "<PERSON><PERSON><PERSON><PERSON>")
  string dob = 4 [(validate.rules).string.min_len = 1]; // Member’s date of birth (DD/MM/YYYY) (Example: "25/02/1993")
  string gender = 5 [(validate.rules).string.min_len = 1]; // Member’s gender ("Male" or "Female") (Example: "Female")
  string partner_user_id = 6; // Unique user id for the client provided by Onsurity (Example: "OSHE-111111888888")
  string employee_no = 7; // Unique employee number or id stored by the client (Example: "E12345")
  NomineeDetails nominee_details = 8; // Details of the nominee

  string govt_id_name = 9; // Any government ID (Example: "PAN")
  string govt_id_value = 10; // Government id value (Example: "**********")
  string city = 11; // Member’s city (Example: "Bangalore")
  string state = 12; // Member’s state (Example: "Karnataka")
  string addr_line = 13; // Member’s address (Example: "B33, 3rd Cross, HSR layout, Bangalore")
  string personal_mobile_number = 14; // Member’s personal mobile number (Example: "5555566666")
  string personal_email = 15; // Member’s personal email address (Example: "<EMAIL>")
  string marital_status = 16; // Member’s marital status ("Married" or "Unmarried") (Example: "Married")
  string employment_type = 17; // Member’s employment type (Example: "Permanent")
  string employee_status = 18; // Member’s employment status (Example: "Active")
  string joining_date = 19; // Member’s joining date (Example: "23/01/1993")

  string esic = 21; // Member’s esic (Example: "UIG")
  string department = 22; // Member’s department (Example: "Technology")
  string designation = 23; // Member’s designation (Example: "Software Engineer")
  string division = 24; // Member’s division (Example: "Engineering")
  string branch = 25; // Member’s branch (Example: "Sarjapur road")
  string location = 26; // Member’s location (Example: "Bangalore")
  string pay_grade = 27; // Member’s paygrade (Example: "A")
  string annual_income = 28; // Member’s salary (Example: "2300000")

}

message NomineeDetails {
  string name = 1 [(validate.rules).string.min_len = 1]; // Nominee’s name (Example: "Smriti Mishra")
  string dob = 2 [(validate.rules).string.min_len = 1]; // Nominee’s date of birth (DD/MM/YYYY) (Example: "25/02/1993")
  string relation = 3 [(validate.rules).string.min_len = 1]; // Nominee’s relation with primary member (Example: "Spouse")
}

message Dependent {
  string name = 1 [(validate.rules).string.min_len = 1]; // Dependent’s name (Example: "Smriti Mishra")
  string gender = 2 [(validate.rules).string.min_len = 1]; // Dependent’s gender ("Male" or "Female") (Example: "Female")
  string dob = 3 [(validate.rules).string.min_len = 1]; // Dependent’s date of birth (DD/MM/YYYY) (Example: "25/02/1993")
  string relation = 4 [(validate.rules).string.min_len = 1]; // Dependent’s relation with primary member (Example: "Spouse")
}

message HealthPlan {
  string plan_name = 1 [(validate.rules).string.min_len = 1]; // Healthcare plan name on which we want to add the member (Example: "Opal")
  string policy_type = 2 [(validate.rules).string.min_len = 1]; // Healthcare policy type on which we want to add the member (Example: "1A")
}


message EmployerPurchaseDetail {
  string name = 1 [(validate.rules).string.min_len = 1]; // Dependent’s name (Example: "Smriti Mishra")
  string dob = 2 [(validate.rules).string.min_len = 1]; // Dependent’s date of birth (DD/MM/YYYY) (Example: "25/02/1993")
  string gender = 3 [(validate.rules).string.min_len = 1]; // Dependent’s gender ("Male" or "Female") (Example: "Female")
  string relation = 4 [(validate.rules).string.min_len = 1]; // Dependent’s relation with primary member (Example: "Spouse")
  string plan_name = 5 [(validate.rules).string.min_len = 1]; // Healthcare plan name on which we want to add the member (Example: "Opal")
  string policy_type = 6 [(validate.rules).string.min_len = 1]; // Healthcare policy type on which we want to add the member (Example: "1A")
}

message MemberInvitationApiResponse {

  rpc.Status status = 1;
  Data data = 2;
  message Data {
    string request_id = 1;
  }
  Meta meta = 3;

  message Meta {
    bool is_success = 1;
    string status_code = 2;
    string response_message = 3;
    string display_message = 4;
    repeated Error errors = 5;
  }
  message Error {
    string message = 1;
    string action = 2;
    string value = 3;
    string row = 4;
  }

}
