syntax = "proto3";

package vendorgateway.onsurity;

import "api/vendorgateway/onsurity/member_invitation.proto";
import "api/vendorgateway/onsurity/member_details.proto";
import "api/vendorgateway/onsurity/download_document.proto";
import "api/vendorgateway/onsurity/deactivate_member.proto";


option go_package = "github.com/epifi/gamma/api/vendorgateway/onsurity";
option java_package = "com.github.epifi.gamma.api.vendorgateway.onsurity";

// https://epifi.slack.com/files/U048R2T4NQK/F06UDP8P0M7/partner_api_doc_3__1_.pdf
service OnSurity
{
  // This RPC is used to add members to Onsurity plans
  rpc MemberInvitationApi(MemberInvitationAPIRequest) returns (MemberInvitationApiResponse);

  rpc MemberDetailsApi(MemberDetailsApiRequest) returns (MemberDetailsApiResponse);

  rpc DownloadDocumentsApi(DownloadDocumentsApiRequest) returns (DownloadDocumentsApiResponse);

  rpc DeactivateMemberApi(DeactivateMemberApiRequest) returns (DeactivateMemberApiResponse);

}









