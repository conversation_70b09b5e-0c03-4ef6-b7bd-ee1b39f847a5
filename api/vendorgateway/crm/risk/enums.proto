syntax = "proto3";

package vendorgateway.crm.risk;

option go_package = "github.com/epifi/gamma/api/vendorgateway/crm/risk";
option java_package = "com.github.epifi.gamma.api.vendorgateway.crm.risk";

// We can assign priority for a ticket based on different factors like source, rule, score etc of an alert
// based on priority of a ticket we can assign them to different groups or prioritise the review within the same group
enum Priority {
  PRIORITY_UNSPECIFIED = 0;

  PRIORITY_CRITICAL = 1;

  PRIORITY_HIGH = 2;

  PRIORITY_MEDIUM = 3;

  PRIORITY_LOW = 4;
}

// A ticket undergoes multiple states based on actions taken by multiple stakeholders
// `Status` enum will contain list of all possible such states
// The enum may be used as a filter to limit the type of tickets that appears in a queue
enum Status {
  STATUS_UNSPECIFIED = 0;

  STATUS_CREATED = 1;

  STATUS_ASSIGNED = 2;

  STATUS_IN_REVIEW = 3;

  STATUS_IN_QA_REVIEW = 4;

  // Case review is completed and a verdict is assigned to it
  STATUS_DONE = 5;

  // Case can be marked as invalid at times without any verdict or before review starts
  // due to various reasons e.g., re-prioritisation, wrong upload, bugs in the code
  STATUS_WONT_REVIEW = 6;

  // review action in process for case
  STATUS_REVIEW_ACTION_IN_PROGRESS = 7;
  // case needs human intervention, can be due to retry exhausted, bank rejection's etc.
  STATUS_MANUAL_INTERVENTION = 8;
  // more information is required from user on the case and it is currently pending on agent to collect additional user information.
   // Agent intervention is required and case will be assigned to an agent.
  STATUS_PENDING_USER_INFO = 9;
  // case is marked for auto action and will consolidate all the suggested actions for x period of time
  STATUS_MARKED_FOR_AUTO_ACTION = 10;
  // Ticket is pending on user to share information regarding their account and no agent intervention is required.
  // e.g., It can be used if outcall form is sent to the user and awaiting user response.
  STATUS_PENDING_ON_USER = 11;
}

// In CRM we can have multiple groups for agents, these groups can be created based on one or combination of more that one of these factors
// Agent levels like L1, L2 etc
// Agent expertise like user review vs txn review Or
// based on case priority i.e we might have different queues based on priority and different group might look at different queues
enum AgentGroup {
  AGENT_GROUP_UNSPECIFIED = 0;
  // group primarily working on transaction review cases
  AGENT_GROUP_TRANSACTION_REVIEW = 1;
  // group working on user outcall for getting more information
  AGENT_GROUP_USER_OUTBOUND_CALL = 2;
  // group primarily working on user review cases
  AGENT_GROUP_USER_REVIEW = 3;
  AGENT_GROUP_L1 = 4;
  AGENT_GROUP_L2 = 5;
  // QA agent group
  AGENT_GROUP_QA = 6;
  // Escalations agent group
  AGENT_GROUP_ESCALATION = 7;
  // Group for agents working on multiple type of case review
  AGENT_GROUP_MULTI_REVIEW = 8;
}

// Based on review type we might assign a case to different group
// Also review type will decide what information will be available to analysts during review process
enum ReviewType {
  REVIEW_TYPE_UNSPECIFIED = 0;

  REVIEW_TYPE_USER_REVIEW = 1;

  REVIEW_TYPE_TRANSACTION_REVIEW = 2;

  REVIEW_TYPE_AFU_REVIEW = 3;

  REVIEW_TYPE_LEA_COMPLAINT_REVIEW = 4;

  REVIEW_TYPE_ESCALATION_REVIEW = 5;

  REVIEW_TYPE_MULTI_REVIEW = 6;
}

// Each review or investigation of a case needs to result in one of the given verdict values
enum Verdict {
  VERDICT_UNSPECIFIED = 0;
  // Entity is deemed to be non-fraudulent and will chose to have similar access
  VERDICT_PASS = 1;
  // Entity is deemed to be fraudulent and will have restricted access
  VERDICT_FAIL = 2;
}
