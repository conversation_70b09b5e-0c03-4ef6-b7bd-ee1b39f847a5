syntax = "proto3";

package vendorgateway.crm;

import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/crm/crm.proto";
import "api/vendorgateway/crm/risk/ticket.proto";
import "api/vendorgateway/request_header.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/crm";
option java_package = "com.github.epifi.gamma.api.vendorgateway.crm";

/*
Customer Relationship management(CRM) service enables different use cases to register their case
on the vendor tool.

All the rpcs will require CRM request header to be present in request.
RequestHeader contains:
- UseCase enum:
  rpc wll return appropriate CRM object depending on the UseCase given
  EX: if UseCase passed is RISK for get ticket rpc it will return RiskTicket object and if CX is passed it will return CxTicket object
- Ownership enum for entity segregation:
  Based on Ownership we will decide which account(or product within account) we need to use for the given action.
  This will be used for entity segregation, EX. we might want to have separate accounts/product for epifi_tech vs epifi_wealth vs FEDERAL_BANK within RISK UseCase.

In all rpc's, for vendor failures we will return approapriate grpc status code mapped for the http code returned by vendor
If no mapped code is found will return INTERNAL status code by default
*/
service CRM {
  // Get ticket information from CRM by passing ticket identifier
  rpc GetTicket (GetTicketRequest) returns (GetTicketResponse) {}

  // Get list of tickets from CRM
  // returns list of ticket objects and total count
  // expects filters on ticket fields to be passed
  // throws error if filters are invalid
  // at least one filter is needed
  rpc GetTicketList (GetTicketListRequest) returns (GetTicketListResponse) {}

  rpc CreateTicket (CreateTicketRequest) returns (CreateTicketResponse) {}

  rpc UpdateTicket (UpdateTicketRequest) returns (UpdateTicketResponse) {}
  // Rpc to fetch CRM agent details
  // we will need this rpc to fetch assigned agents details against a ticket at run time since ticket object only contains assigend agent id
  rpc GetAgent (GetAgentRequest) returns (GetAgentResponse) {}
  // Rpc to fetch customer contact
  rpc GetCustomerContact (GetCustomerContactRequest) returns (GetCustomerContactResponse) {}

  // Rpc to fetch sorted list of tickets for input filters.
  // Returns Record Not Found status if tickets are not found
  rpc GetSortedTickets (GetSortedTicketsRequest) returns (GetSortedTicketsResponse);

  // Rpc to fetch job status by job ID
  rpc GetJob (GetJobRequest) returns (GetJobResponse);

  // Rpc to bulk update tickets
  // Note: This RPC must be used once per session as bulk update happens in background
  // and progress can be checked using the job_id returned in the response
  rpc BulkUpdateTickets (BulkUpdateTicketsRequest) returns (BulkUpdateTicketsResponse);
}

message GetTicketRequest {
  vendorgateway.RequestHeader header = 1;

  RequestHeader crm_request_header = 2;

  // identifier using which we need to fetch the ticket
  oneof identifier {
    // ticket id in CRM
    uint64 ticket_id = 3;
  }
}

message GetTicketResponse {
  enum Status {
    OK = 0;
    // If identifier is missing in request
    INVALID_ARGUMENT = 3;
    // no ticket found with given identifier
    RECORD_NOT_FOUND = 5;
    // server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  Ticket ticket = 2;
}

message GetTicketListRequest {
  vendorgateway.RequestHeader header = 1;

  RequestHeader crm_request_header = 2;
  // set of filters to be applied on ticket
  // by default if no filter is passed it will fetch latest n tickets
  // filter list can grow to a larger size and we might only support combinations of filters, refer to comments on individual filter fields for these details
  // based on filters passed we will choose appropriate underlying vendor api out of multiple one's available
  oneof filters {
    risk.RiskFilters risk_filters = 3;
  }

  rpc.PageContextRequest page_context = 4;
}

message GetTicketListResponse {
  enum Status {
    OK = 0;
    // If request parameters are invalid
    INVALID_ARGUMENT = 3;
    // server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  repeated Ticket tickets = 2;

  rpc.PageContextResponse page_context = 3;
}

message CreateTicketRequest {
  vendorgateway.RequestHeader header = 1;

  RequestHeader crm_request_header = 2;

  Ticket ticket = 3;
}

message CreateTicketResponse {
  enum Status {
    OK = 0;
    // If ticket object is invalid
    INVALID_ARGUMENT = 3;
    // server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // ticket object received from vendor in response in case of success status
  Ticket ticket = 2;
}

message UpdateTicketRequest {
  vendorgateway.RequestHeader header = 1;

  RequestHeader crm_request_header = 2;
  // ticket id is mandatory for updating the ticket
  Ticket ticket = 3;
}

message UpdateTicketResponse {
  enum Status {
    OK = 0;
    // If ticket object is invalid
    INVALID_ARGUMENT = 3;
    // no ticket found with given id
    RECORD_NOT_FOUND = 5;
    // server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // ticket object received from vendor in response in case of success status
  Ticket ticket = 2;
}

message GetAgentRequest {
  vendorgateway.RequestHeader header = 1;

  RequestHeader crm_request_header = 2;

  // identifier using which we need to fetch the contact
  oneof identifier {
    // agent id in CRM
    uint64 id = 3;
    // email of agent
    string email = 4;
  }
}

message GetAgentResponse {
  enum Status {
    OK = 0;
    // If identifier is invalid
    INVALID_ARGUMENT = 3;
    // no agent found with given identifier
    RECORD_NOT_FOUND = 5;
    // server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  Agent agent = 2;
}

message GetCustomerContactRequest {
  vendorgateway.RequestHeader header = 1;

  RequestHeader crm_request_header = 2;

  // Identifier to fetch customer contact from CRM
  oneof identifier {
    // Unique ID of customer contact in CRM
    uint64 id = 3;
    // Email address of customer in CRM
    string email = 4;
    // Phone number of customer in CRM
    api.typesv2.common.PhoneNumber phone = 5;
  }
}

message GetCustomerContactResponse {
  enum Status {
    OK = 0;
    // If no contact found with id
    INVALID_ARGUMENT = 3;
    // server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  CustomerContact customer_contact = 2;
}

message GetSortedTicketsRequest {
  vendorgateway.RequestHeader header = 1 [(validate.rules).message.required = true];

  RequestHeader crm_request_header = 2 [(validate.rules).message.required = true];

  oneof filters {
    risk.SortableRiskFilters risk_filters = 3 [(validate.rules).message.required = true];
  }

  rpc.PageContextRequest page_context_request = 4 [(validate.rules).message.required = true];
}

message GetSortedTicketsResponse {
  enum Status {
    OK = 0;
    // If invalid argument
    INVALID_ARGUMENT = 3;
    // If tickets are not found
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  repeated Ticket tickets = 2;

  rpc.PageContextResponse page_context_response = 3;
}

message GetJobRequest {
  vendorgateway.RequestHeader header = 1;

  RequestHeader crm_request_header = 2;

  // Job ID to retrieve status for
  string job_id = 3;
}

message GetJobResponse {
  enum Status {
    OK = 0;
    // If job_id is invalid or missing
    INVALID_ARGUMENT = 3;
    // no job found with given ID
    RECORD_NOT_FOUND = 5;
    // server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  Job job = 2;
}

message BulkUpdateTicketsRequest {
  vendorgateway.RequestHeader header = 1;

  RequestHeader crm_request_header = 2;

  // Array of ticket IDs to be updated (mandatory)
  repeated uint64 ids = 3;

  // Ticket object containing all fields to be updated (mandatory)
  // This will be applied to all ticket IDs specified in the ids array
  Ticket ticket = 4;
}

message BulkUpdateTicketsResponse {
  rpc.Status status = 1;

  // Bulk update job information
  BulkUpdateJob bulk_update_job = 2;
}
