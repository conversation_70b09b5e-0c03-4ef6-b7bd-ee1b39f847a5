syntax = "proto3";

package cx.freshdesk;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/cx/freshdesk";
option java_package = "com.github.epifi.gamma.api.vendorgateway.cx.freshdesk";

message TicketContact {
  // mandatory
  string name = 1 [json_name="name"];

  // email of contact
  //unique
  string email = 2 [json_name="email"];

  // phone number of contact
  string phone = 3 [json_name="phone"];

  // mobile number of contact
  string mobile = 4 [json_name="mobile"];

  // unique external id of contact
  string unique_external_id = 5 [json_name="unique_external_id"];

  // description of the contact
  string description = 6 [json_name="description"];

  // tags associated with the contact
  repeated string tags = 7 [json_name="tags"];

  // creation timestamp
  google.protobuf.Timestamp created_at = 8 [json_name="created_at"];

  // last updated timestamp
  google.protobuf.Timestamp updated_at = 9 [json_name="updated_at"];

  // set to true if contact is verified
  bool active = 10 [json_name="active"];

  // set to true if contact is soft deleted
  bool deleted = 11 [json_name="deleted"];

  // address of contact
  string address = 12 [json_name="address"];

  // id of contact
  int64 id = 13 [json_name="id"];

  // id of the primary company to which this contact belongs
  int64 company_id = 14 [json_name="company_id"];

  // set to true if the contact can see all tickets that are associated with the company to which he belong
  bool view_all_tickets = 15 [json_name="view_all_tickets"];

  // job title of contact
  string job_title = 16 [json_name="job_title"];

  // language of contact
  string language = 17 [json_name="language"];

  // Additional emails associated with the contact
  repeated string other_emails = 18 [json_name="other_emails"];

  // Time zone in which the contact resides
  string time_zone = 19 [json_name="time_zone"];

  // twitter handle of contact
  string twitter_id = 20 [json_name="twitter_id"];
}

// enum to represent state of a ticket contact in freshdesk
enum ContactState {
  CONTACT_STATE_UNSPECIFIED = 0;

  CONTACT_STATE_BLOCKED = 1;

  CONTACT_STATE_DELETED = 2;
  
  CONTACT_STATE_UNVERIFIED = 3;

  CONTACT_STATE_VERIFIED = 4;
}
