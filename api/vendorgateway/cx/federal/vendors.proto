syntax = "proto3";

package cx.escalation.federal;

option go_package = "github.com/epifi/gamma/api/vendorgateway/cx/federal";
option java_package = "com.github.epifi.gamma.api.vendorgateway.cx.federal";

// BulkFetchSearchRequestPayload represents the search criteria for bulk fetch API
message BulkFetchSearchRequestPayload {
  // Creation date of the service request
  string creation_date = 1 [json_name = "CreationDate"];

  // Start date for creation date range
  string creation_date_from = 2 [json_name = "CreationDate_from"];

  // End date for creation date range
  string creation_date_to = 3 [json_name = "CreationDate_to"];

  // Customer Information File ID
  string cif_id = 4 [json_name = "CIF ID"];

  // Account number
  string account_number = 5 [json_name = "Account Number"];

  // Application reference ID
  string application_ref_id = 6 [json_name = "FBOtherApplicationRefID_c"];

  // Channel type code
  string channel_type = 7 [json_name = "ChannelTypeCd"];
}

// BulkFetchSearchResponse represents the successful response for bulk fetch API
message BulkFetchSearchResponse {
  // List of service requests
  repeated ServiceRequestItem items = 1 [json_name = "items"];

  // Total count of items
  int32 count = 2 [json_name = "count"];
}

message ServiceRequestItem {
  // Application reference ID
  string application_ref_id = 1 [json_name = "FBOtherApplicationRefID_c"];

  // Status code
  string status_code = 2 [json_name = "StatusCd"];

  // IO Disposal Status
  string io_disposal_status = 3 [json_name = "IODisposalStatus_c"];

  // IO Comments
  string io_comments = 4 [json_name = "IOComments_c"];

  // IO Disposal Remarks
  string io_disposal_remarks = 5 [json_name = "IODisposalRemarks_c"];

  // Creation date
  string creation_date = 6 [json_name = "CreationDate"];

  // Created by
  string created_by = 7 [json_name = "CreatedBy"];
}

// BulkFetchSearchError represents the error response for bulk fetch API
message BulkFetchSearchError {
  // Total count (usually 0 for errors)
  int32 count = 1 [json_name = "count"];

  // Status code
  int32 status_code = 2 [json_name = "Statuscode"];

  // Status message
  string status_message = 3 [json_name = "Statusmessage"];
}


// EscalationCreationRequest represents the request payload for creating a service request
message EscalationCreationRequest {
  // Title of the service request (max 240 characters)
  string title = 1 [json_name = "Title"];

  // Problem description (max 1000 characters)
  string problem_description = 2 [json_name = "ProblemDescription"];

  // Category code from the predefined list
  string category_code = 3 [json_name = "CategoryCode"];

  // SR Type (e.g., FB_COMPLAINT)
  string sr_type = 4 [json_name = "FBSRType_c"];

  // Whether the user is an existing customer
  string existing_customer = 5 [json_name = "FB_SR_ExistingCustomer_c"];

  // Type of customer (e.g., FB_INDIVIDUAL)
  string customer_type = 6 [json_name = "FB_SR_TypeOfCustomer_c"];

  // Federal Bank Customer ID (max 80 characters)
  string cif_id = 7 [json_name = "FBCIFId_c"];

  // Channel code email (14 digits)
  string channel_code_email = 8 [json_name = "FBChannelCodeEMail_c"];

  // Escalation status
  string escalation_status = 9 [json_name = "EscalationStatus_c"];

  // Queue ID (numeric)
  int64 queue_id = 10 [json_name = "QueueId"];

  // L1 decision
  string l1_decision = 11 [json_name = "L1Decision_c"];

  // L1 updated by
  string l1_updated_by = 12 [json_name = "L1UpdatedBy_c"];

  // L1 updated time in format YYYY-MM-DDTHH:MM:SS
  string l1_updated_time = 13 [json_name = "L1UpdatedTime_c"];

  // Application reference ID (max 80 characters)
  string application_ref_id = 14 [json_name = "FBOtherApplicationRefID_c"];

  // Channel type code
  string channel_type = 15 [json_name = "ChannelTypeCd"];

  // Complaint acceptance status
  string complaint_status = 16 [json_name = "FBComplaintAcceptedRejected_c"];

  // Product name
  string product_name = 17 [json_name = "ProductName_c"];

  // L1 recommendation (max 1000 characters)
  string l1_recommendation = 18 [json_name = "L1Recommendation_c"];

  // Branch SOL ID (4 digits)
  string branch_sol = 19 [json_name = "FBBranchSOL_c"];

  // Optional attachment file
  bytes attachment = 20 [json_name = "files"];
}

// EscalationCreationResponse represents the response for service request creation
message EscalationCreationResponse {
  // Service request number
  string sr_number = 1 [json_name = "SrNumber"];

  // Status code
  string status_code = 2 [json_name = "StatusCd"];

  // L1 decision
  string l1_decision = 3 [json_name = "L1Decision_c"];

  // Application reference ID
  string application_ref_id = 4 [json_name = "FBOtherApplicationRefID_c"];

  // Attachment document ID (if any)
  int64 attached_document_id = 5 [json_name = "AttachedDocumentId"];

  // Status code
  int32 status = 6 [json_name = "Status"];

  // Status text
  string status_text = 7 [json_name = "statusText"];

  // Additional data
  string data = 8 [json_name = "data"];
}

// EscalationCreationError represents error response for service request creation
message EscalationCreationError {
  // Status code
  int32 status = 1 [json_name = "Status"];

  // Status text
  string status_text = 2 [json_name = "statusText"];

  // Error details
  string data = 3 [json_name = "data"];
}

message Payload {
  oneof payload {
    BulkFetchSearchRequestPayload bulk_fetch_search_request = 1;
    BulkFetchSearchResponse bulk_fetch_search_response = 2;
    BulkFetchSearchError bulk_fetch_search_error = 3;
    EscalationCreationRequest escalation_creation_request = 4;
    EscalationCreationResponse escalation_creation_response = 5;
    EscalationCreationError escalation_creation_error = 6;
  }
}
