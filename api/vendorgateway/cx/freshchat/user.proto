// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package cx.freshchat;

option go_package = "github.com/epifi/gamma/api/vendorgateway/cx/freshchat";
option java_package = "com.github.epifi.gamma.api.vendorgateway.cx.freshchat";

message User {
  // Email id of the user.
  string email = 1;
  // Image associated with the user. The attribute value is as an image object. The image object consists of the url attribute.
  Image avatar = 2;
  // Time when the user information is created, specified in the date-time format.
  // The time of creation is returned as part of the user object in response to a successful user creation request.
  string created_time = 3;
  // Time when the user information is last updated, specified in the date-time format.
  // The time of last update is returned as part of the user object in response to a successful user creation request.
  string updated_time = 4;
  // Identifier of the user information. The id value is returned as part of the user object in response to a successful user creation request.
  // The (user) id is used to identify and retrieve user information in API calls.
  string id = 5;
  // User’s phone number.
  string phone = 6;
  // Attributes of a user, specified as an array. Each array entry is a property object.
  repeated Property properties = 7;
  // Business name of the user.
  string first_name = 8;
  // Last name of the user.
  string last_name = 9;
  // epifi created id passed from sdk for user identification
  string reference_id = 10;
  // This will be created by Freshchat when a logged in user (from the customer’s website) initiates a conversation on Freshchat messenger for the first time.
  // Restore ID is also unique to each created user. This ID will be passed to your website on the user creation callback function. You have to store this ID in your database, once it is passed by Freshchat.
  // The next time the website user pings using the Freshchat messenger from a different browser or mobile app, this restore ID, along with the external ID must be passed from your side to Freshchat for the user conversation to be restored. Otherwise every conversation initiated by the user on a new / fresh browser session will create a new user and the conversation history will be lost.
  string restore_id = 11;
}

message Property {
  string name = 1;
  string value = 2;
}

// Image part of the message posted to a conversation.
message Image {
  // url (string): Location of the image file.
  string url = 1;
}

// This is being used to unmarshal response in bytes to proto
message getFreshchatUserResponse {
  repeated User users = 1;
}

// This is being used to unmarshal response in bytes to proto
message getFreshchatConvForUserResponse {
  repeated Conv conversations = 1;
}

// This is being used to unmarshal response in bytes to proto
// since comversation proto is already defined renaming it to conv
message Conv {
  string id = 1;
}
