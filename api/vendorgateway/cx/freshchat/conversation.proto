// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package cx.freshchat;

import "api/vendorgateway/cx/freshchat/user.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/cx/freshchat";
option java_package = "com.github.epifi.gamma.api.vendorgateway.cx.freshchat";

message Conversation {
  // Identifier of the conversation.
  // The conversation_id value is returned as part of the conversation object in response to a successful conversation creation request.
  // The conversation_id is used to identify a conversation and post a message to the conversation or update the conversation.
  string conversation_id  = 1;

  // All businesses that use Freshchat are assigned an app_id that is used to integrate with Freshchat.
  // The app_id is used to identify the Freshchat account under which the conversation is created.
  // To know your app_id, navigate to https://web.freshchat.com and log in with your account credentials.
  // Navigate to Settings > Account Settings > Integration Settings. The App ID is displayed under AGENT WIDGET.
  string app_id = 2;

  // Time when the conversation is created, specified in the date-time format.
  // The time of creation is returned as part of the conversation object in response to a successful conversation creation request.
  string created_time = 3;

  // Identifier of a channel created by the business that uses Freshchat.
  // Channels help users identify the subject in which they have a query; users can create a conversation in the identified channel.
  // Agents can pick conversations in a specific channel and address the queries in them.
  string channel_id = 4;

  // Messages that constitute a conversation, specified as an array. In a conversation, a message can be posted by a user or agent.
  // Each entry of the array is a message object with corresponding attributes.
  repeated Message messages = 5;

  // Specifies the state of a conversation.
  // When you update a conversation, you can use the status attribute in combination with the assigned_agent_id and assigned_group_id attributes
  // to specify if a conversation is resolved, reopened, or assigned and to assign the conversation for further action to an agent or group.
  // If a conversation is assigned to an agent, the assigned_agent_id is a mandatory parameter and the status must be assigned.
  // If a conversation is assigned to a group alone, the status must be new.
  // Possible values (enum): new, resolved, reopened, assigned
  string status = 6;

  // List of all users involved in the conversation, specified as an array.
  // Each array value is a user object containing information that pertains to a user.
  repeated User users = 7;

  // List of all agents involved in the conversation, specified as an array.
  // Each array value is an agent object containing information that pertains to an agent.
  repeated User agents = 8;

  // Identifier of the agent to whom a conversation is assigned for resolution.
  string assigned_agent_id = 9;

  // Identifier of the group to which a conversation is assigned for resolution.
  // An agent in the group can pick up the conversation, assign it to self, and resolve the conversation.
  string assigned_group_id = 10;
}

message Message {
  // Different parts of a message posted to the conversation - plain text, images, or url buttons.
  // The message can be a combination of these attributes.
  repeated MessagePart message_parts = 1 [json_name = "message_parts"];

  // The message an agent posts to a conversation can contain segments (response enablers) that enable a user to respond to the message.
  // The reply_parts attribute contains these segments.
  // The segments can be a text, image, url button, quick reply button, or a collection of these attributes.
  //A reply_part attribute can exist only if message_part is present.
  repeated MessagePart reply_parts = 2 [json_name = "reply_parts"];

  // Identifier of the Freshchat account.
  // To know your app_id, navigate to https://web.freshchat.com and log in with your account credentials.
  // Navigate to Settings > Account Settings > Integration Settings. The App ID is displayed under AGENT WIDGET.
  string app_id = 3 [json_name = "app_id"];

  // Identifier of the person who posted the message to the conversation. Must be a valid user.id or agent.id value.
  string actor_id = 4 [json_name = "actor_id"];

  // Identifier of the message, auto-generated when a message is successfully posted to a conversation.
  string id = 5 [json_name = "id"];

  // Identifier of the topic under which the message is posted. Must be a valid channel.id value.
  string channel_id = 6 [json_name = "channel_id"];

  // Identifier of the conversation object. This is an auto-generated value.
  string conversation_id = 7 [json_name = "conversation_id"];

  // Specifies whether the message posted to the conversation is a private message.
  // Possible values (enum): normal, private
  string message_type = 8 [json_name = "message_type"];

  // Descriptive identifier specifying the person who posted the message in the conversation.
  // Possible values (enum): user, agent
  string actor_type = 9 [json_name = "actor_type"];

  // Timestamp of when the message is posted, specified in the date-time format.
  // The timestamp is returned as part of the response conversation object, when a message is successfully posted.
  string created_time = 10 [json_name = "created_time"];
}

// The great message part object!!!!!!!!
message MessagePart {
  Text text = 1;

  Image image = 2;

  UrlButton url_button = 3;

  // valid only for reply_part
  Collection collection = 4;

  // valid only for reply_part
  QuickReplyButton quick_reply_button = 5;

  // valid only for reply_part
  TemplateContent template_content = 6;

  Callback callback = 7;
}

// Text part of the message posted to a conversation.
message Text {
  // Actual content of the text message.
  string content = 1;
}

// Links in a message.
message UrlButton {
  // The actual hyperlink that is accessed by clicking the corresponding label.
  string url = 1;

  // Text (in the message) that is used as the connector to the hyperlink.
  string label = 2;

  // Location where the link is opened.
  // Possible values (enum):
  // _self: Link is opened within the conversation widget.
  // _blank: Link is opened in a new tab.
  // Default value: _blank
  string target = 3;
}

// The response enabler an agent posts to the conversation can be a quick reply button.
// The quick_reply_button attribute contains the different components of the button.
message QuickReplyButton {
  // Custom text that is used as reply text.
  string custom_reply_text = 1;

  // Label on the button that is used to give a quick reply.
  string label = 2;

  // Data that is passed to the Freshchat system when a user responds using the quick reply button.
  string payload = 3;
}

// Helps compose a response enabler that is a combination of text, image, url button, and quick reply buttons.
message Collection {
  // Different parts of a response enabler posted to a conversation.
  // In a response object, the collection attribute contains the different segments of the response enabler posted to the conversation.
  repeated MessagePart sub_parts = 1;
}

// The response enabler an agent posts to the conversation can be a templated message such as a carousel or drop-down list.
// The template_content attribute helps compose such a message.
// In a response object, the template_content attribute contains different segments of the templated response enabler posted to the conversation.
message TemplateContent {
  // Identifier of the type of templated message.
  // Possible values (enum):
  // carousel: A scrollable list of carousel cards are displayed as response enablers.
  // The template_content.sections values are used to populate the carousel cards. See example 7 for reference.
  // carousel_card_default: A single carousel card is displayed as a response enabler.
  // The template_content.sections values are used to populate the carousel card.
  // quick_reply_dropdown: A drop-down list is displayed as the response enabler.
  // The template_content.sections values are used to populate the drop-down list. See example 8 for reference.
  string type = 1;

  // Segments of the carousel card or drop-down list.
  repeated Sections sections = 2;
}

message Sections {
  // Title of the carousel card or drop-down list displayed in the conversation window.
  string name = 1;

  // Segments of the carousel card or values for the drop-down list.
  repeated MessagePart parts = 2;
}

// In a conversation, the reply message can be a carousel card which has the callback attribute.
message Callback {
  // Data that is passed to the Freshchat system when a user responds using the carousel card.
  string payload = 1;

  // Text that is displayed on the button.
  string label = 2;
}


