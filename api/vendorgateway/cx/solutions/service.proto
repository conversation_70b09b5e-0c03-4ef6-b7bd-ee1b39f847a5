syntax = "proto3";

package cx.solutions;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/cx/solutions/category.proto";
import "api/vendorgateway/cx/solutions/folder.proto";
import "api/vendorgateway/cx/solutions/article.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/cx/solutions";
option java_package = "com.github.epifi.gamma.api.vendorgateway.cx.solutions";

message GetAllCategoriesRequest {
  vendorgateway.RequestHeader header = 1;
}

message GetAllCategoriesResponse {
  rpc.Status status = 1;

  repeated Category category_list = 2;
}

message GetAllFoldersRequest {
  vendorgateway.RequestHeader header = 1;

  int64 category_id = 2;
}

message GetAllFoldersResponse {
  rpc.Status status = 1;

  repeated Folder folder_list = 2;
}

message GetAllArticlesRequest {
  vendorgateway.RequestHeader header = 1;

  int64 folder_id = 2;
}

message GetAllArticlesResponse {
  rpc.Status status = 1;

  repeated Article article_list = 2;
}

message GetArticleRequest {
  vendorgateway.RequestHeader header = 1;

  int64 article_id = 2;
}

message GetArticleResponse {
  rpc.Status status = 1;

  Article article = 2;
}

service Solutions {
  // Returns a list of all the categories we have created in freshdesk solutions
  rpc GetAllCategories(GetAllCategoriesRequest) returns (GetAllCategoriesResponse) {}

  // Returns a list of all the folders in a category
  // throws error if category id is invalid or missing
  rpc GetAllFolders(GetAllFoldersRequest) returns (GetAllFoldersResponse) {}

  // Returns a list of all the articles in a folder
  // throws error if folder id is invalid or missing
  rpc GetAllArticles(GetAllArticlesRequest) returns (GetAllArticlesResponse) {}

  // Returns the details of article with given article_id
  // throws error if article_id is invalid or missing
  rpc GetArticle(GetArticleRequest) returns (GetArticleResponse) {}

}


