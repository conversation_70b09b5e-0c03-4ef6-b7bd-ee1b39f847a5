syntax = "proto3";

package cx.solutions;

import "google/protobuf/timestamp.proto";
import "api/vendorgateway/cx/solutions/category.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/cx/solutions";
option java_package = "com.github.epifi.gamma.api.vendorgateway.cx.solutions";

message Article {
  // id of article
  int64 id = 1 [json_name="id"];

  // title of the category
  string title = 2 [json_name="title"];

  // description of the article
  string description = 3 [json_name="description"];

  // description of the article in plain text
  string description_text = 4 [json_name="description_text"];

  // ID of the category to which the solution article belongs
  int64 category_id = 5 [json_name="category_id"];

  // ID of the folder to which the solution article belongs
  int64 folder_id = 6 [json_name="folder_id"];

  // ID of the agent who created the solution article
  int64 agent_id = 7 [json_name="agent_id"];

  // category creation timestamp
  google.protobuf.Timestamp created_at = 8 [json_name="created_at"];

  // category last updated timestamp
  google.protobuf.Timestamp updated_at = 9 [json_name="updated_at"];

  CategoryVisibility article_visibility = 10;
}
