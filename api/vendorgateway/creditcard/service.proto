syntax = "proto3";

package vendorgateway.creditcard;

import "api/rpc/status.proto";
import "api/typesv2/common/device.proto";
import "api/vendorgateway/request_header.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "google/type/money.proto";
import "google/type/date.proto";
import "api/firefly/v2/enums/enums.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/creditcard";
option java_package = "com.github.epifi.gamma.api.vendorgateway.creditcard";

// VG service to enable credit card related services for Epifi users.
service CreditCard {
  // RPC to generate an authentication token for client to auth with credit card SDK
  rpc GenerateCreditCardSdkAuthToken(GenerateCreditCardSdkAuthTokenRequest) returns (GenerateCreditCardSdkAuthTokenResponse);
  // RPC to update credit card delivery info at credit card vendor.
  rpc UpdateCreditCardDeliveryState(UpdateCreditCardDeliveryStateRequest) returns (UpdateCreditCardDeliveryStateResponse);
  // Updates customer information such as device details for credit card users.
  // This RPC is typically called when customer AFU changes and needs
  // to be synchronized with the credit card vendor.
  rpc UpdateCustomerInfo(UpdateCustomerInfoRequest) returns (UpdateCustomerInfoResponse);
  // Updates credit card onboarding request details.
  // This RPC handles various onboarding details update  (during onboarding in_progress states) use-cases,
  // that need to be communicated to credit card vendor(SAVEN)
  //
  // Use cases:
  // - Pre-approved offer updates
  // - Workflow termination/discard scenarios
  rpc UpdateCreditCardOnboarding(UpdateCreditCardOnboardingRequest) returns (UpdateCreditCardOnboardingResponse);
}

// Request to update customer information for credit card users.
message UpdateCustomerInfoRequest {
  vendorgateway.RequestHeader header = 1;

  // Unique identifier for the user which is between across Epifi and vendors.
  // This ID is used to identify the customer across internal systems.
  string user_local_id = 2 [(validate.rules).string = {min_len: 1, max_len: 100}];

  // Device information that needs to be updated for the customer.
  DeviceInfo device_info = 4 [(validate.rules).message.required = true];
}

// Response for customer information update operations.
message UpdateCustomerInfoResponse {
  // Possible outcomes for the customer information update request.
  enum Status {
    // Operation completed successfully.
    OK = 0;

    // The specified user could not be found in the system.
    // This may occur if the user_local_id is invalid or the user record doesn't exist.
    USER_DETAILS_NOT_FOUND = 101;

    // The applicant type doesn't match the expected type for this operation.
    // This may occur if applicantType from epifi systems does not match with applicantType at vendor.
    APPLICANT_TYPE_MISMATCH = 102;

    // The update operation is not allowed in the current state.
    // This may occur due to workflow state restrictions or business rule violations at vendor.
    UPDATE_NOT_ALLOWED = 103;

    // Required device details are missing from the request.
    // This indicates that mandatory device information fields are not provided.
    MISSING_DEVICE_DETAILS = 104;
  }

  // Status of the request indicating success or failure reason.
  rpc.Status status = 1;
}

// Request to update credit card onboarding workflow state and details.
message UpdateCreditCardOnboardingRequest {
  // Standard request header containing authentication and tracing information.
  vendorgateway.RequestHeader header = 1;

  // Type of update being performed on the onboarding request.
  // This determines which fields in onboarding_update_details will be populated.
  UpdateCreditCardOnboardingRequestType event_type = 2;

  // Type of credit card applicant (individual, business, etc.).
  // This field ensures that updates are applied to the correct applicant category.
  api.firefly.v2.enums.CreditCardApplicantType applicant_type = 3 [(validate.rules).enum = {not_in: 0}];

  // Unique identifier for the user which is between across Epifi and vendors.
  // This ID is used to identify the customer across internal systems.
  string user_local_id = 4 [(validate.rules).string = {min_len: 1, max_len: 100}];

  // Container for specific onboarding update details based on the event type.
  OnboardingUpdateDetails onboarding_update_details = 5;

  // Contains the specific update information based on the type of onboarding update event.
  // Only one of the fields will be populated based on the event_type.
  message OnboardingUpdateDetails {
    oneof update_details {
      // Information about pre-approved credit card offers.
      // Populated when event_type is PRE_APPROVED_OFFER_UPDATE.
      PreApprovedInfo pre_approved_info = 1;

      // Populated when event_type is DISCARD_WORKFLOW.
      DiscardWorkflowInfo discard_workflow_info = 2;
    }
  }
}

// Contains information for discarding or terminating an onboarding workflow.
message DiscardWorkflowInfo {
  // Human-readable reason for discarding the onboarding process.
  // This field provides context for auditing and customer service purposes.
  string reason_to_discard_onboarding = 2;
}

// Enumeration of supported onboarding update types.
enum UpdateCreditCardOnboardingRequestType {
  UPDATE_CREDIT_CARD_ONBOARDING_REQUEST_TYPE_UNSPECIFIED = 0;

  // Update related to pre-approved credit card offers.
  // Used when offer details change
  PRE_APPROVED_OFFER_UPDATE = 1;

  // Request to discard/terminate the onboarding workflow.
  // Used when the onboarding process needs to be stopped.
  DISCARD_WORKFLOW = 2;
}

message UpdateCreditCardOnboardingResponse {
  // Possible outcomes for the onboarding update request.
  enum Status {
    // Operation completed successfully.
    OK = 0;

    // The specified user could not be found in the system.
    USER_DETAILS_NOT_FOUND = 101;

    // Required pre-approved offer information is missing.
    // This occurs when PRE_APPROVED_OFFER_UPDATE is requested but pre_approved_info is not provided.
    MISSING_PRE_APPROVED_INFO = 102;

    // The applicant type doesn't match the expected type for this operation.
    APPLICANT_TYPE_MISMATCH = 103;

    // The update operation is not allowed in the current workflow state.
    // This may occur due to state machine constraints or business rule violations.
    UPDATE_NOT_ALLOWED = 104;

    // The onboarding request could not be found for the specified user.
    ONBOARDING_REQUEST_NOT_FOUND = 105;
  }

  // Status of the request indicating success or failure reason.
  rpc.Status status = 1;
}

message GenerateCreditCardSdkAuthTokenRequest {
  // Common request header across all vendor gateway APIs.
  // Denotes the vendor that is supposed to process this request.
  vendorgateway.RequestHeader header = 1;
  // Applicant type (ETB, NTB, PRE-APPROVED)
  api.firefly.v2.enums.CreditCardApplicantType applicant_type = 2 [(validate.rules).enum = {not_in: 0}];

  // User information
  UserInfo user_info = 3 [(validate.rules).message.required = true];

  // Device information
  DeviceInfo device_info = 4 [(validate.rules).message.required = true];

  // PAN information (conditional optional)
  // This information is required only when request is for initial onboarding auth token generation,
  // otherwise, populating this field can be omitted.
  PanInfo pan_info = 5;

  // Consent information (optional)
  ConsentInfo consent_info = 6;

  // Pre-approved information (conditional optional)
  // This information is required only when the applicant type is PRE-APPROVED and request is for initial onboarding auth token generation.
  // Otherwise, populating value for this field can be omitted.
  PreApprovedInfo pre_approved_info = 7;
}

message GenerateCreditCardSdkAuthTokenResponse {
  // Status of the request.
  rpc.Status status = 1;
  CreditCardSdkModuleName module_name = 2;
  string auth_token = 3;
  TokenGenerationAdditionalInfo additional_info = 4;
}

message TokenGenerationAdditionalInfo {
  string workflow_id = 1;
  string user_local_id = 2;
  string external_user_id = 3;
  api.firefly.v2.enums.CardRequestStatus workflow_status = 4;
  api.firefly.v2.enums.CardRequestStage workflow_state = 5;
  string workflow_message = 6;
  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
}


enum CreditCardSdkModuleName {
  CREDIT_CARD_SDK_MODULE_NAME_UNSPECIFIED = 0;
  CREDIT_CARD_SDK_MODULE_NAME_ONBOARDING = 1;
  CREDIT_CARD_SDK_MODULE_NAME_CMS = 2;
}

message DeviceInfo {
  api.typesv2.common.Device device = 1;
}

message UserInfo {
  // User's email address.
  string email_address = 1 [(validate.rules).string.min_len = 1];
  // User's phone number.
  api.typesv2.common.PhoneNumber phone_number = 2 [(validate.rules).message.required = true];
  // Optional:
  string phone_type = 3;
  // User's internal user ID at vendor's end.
  string internal_user_id = 4 [(validate.rules).string = {min_len: 1, max_len: 100}];
}

message PanInfo {
  // PAN number.
  string pan_number = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
  // Full name of user associated with the PAN.
  api.typesv2.common.Name user_name = 2 [(validate.rules).message.required = true];
}

message PreApprovedInfo {
  // Pre-approved limit.
  google.type.Money pre_approved_limit = 1 [(validate.rules).message.required = true];
  // Pre-approved expiration date.
  google.type.Date pre_approved_exp = 2 [(validate.rules).message.required = true];
}

message ConsentInfo {
  repeated Consent consents = 1;
}

message Consent {
  // Indicates if the consent is recorded.
  bool is_consent_recorded = 1;
  // Type of the consent.
  string consent_type = 2;
  // Category of the consent.
  string consent_category = 3;
}

message UpdateCreditCardDeliveryStateRequest {
  // Common request header across all vendor gateway APIs.
// Denotes the vendor that is supposed to process this request.
  vendorgateway.RequestHeader header = 1;
  // carrier partner of the tracking request (DELHIVERY, BLUEDART etc)
  string carrier = 2 [(validate.rules).string = {min_len: 1, max_len: 100}];
  // delivery state of the shipment
  DeliveryState delivery_state = 3 [(validate.rules).enum = {not_in: 0}];
  string tracking_url = 4;
  string user_id = 5 [(validate.rules).string = {min_len: 1, max_len: 100}];
}

message UpdateCreditCardDeliveryStateResponse {
  // Status of the request.
  rpc.Status status = 1;
}

enum DeliveryState {
  DELIVERY_STATE_UNSPECIFIED = 0;
  DELIVERED = 1;
  RETURNED_TO_ORIGIN = 2;
}
