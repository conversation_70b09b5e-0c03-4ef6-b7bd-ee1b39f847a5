// protolint:disable MAX_LINE_LENGTH

// A service for handling communication with users through firebase messaging.
syntax = "proto3";

package vendorgateway.fcm;

import "api/comms/notification.proto";
import "api/comms/enums.proto";
import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";

// go_package needs to be fully qualified for protoc to work from the workspace
// directory.
option go_package = "github.com/epifi/gamma/api/vendorgateway/fcm";
option java_package = "com.github.epifi.gamma.api.vendorgateway.fcm";

// Send message request represents the fcm attributes which will be passed via the comms service
// For now we have only the title body and device token which are mandatory to be passed
// attributes such as priority, time to live etc can also be passed, else will be set by the vendorgateway itself
message SendMessageRequest {
  // Header which contains the vendor information ex, FEDERAL, EXOTEL, etc
  vendorgateway.RequestHeader header = 1;

  // Notification message as received from the client service on comms server
  comms.NotificationMessage notification_message = 2;

  // registration token or the device token of the device on which we want to send the message
  string device_token = 3;

  // analytics label
  // Firebase groups notification based on this label on it's dashboard
  // This label will help the analytics team to get better insights on type of notifications being sent
  string analytics_label = 4;
}

message SendMessageResponse {
  // Represents message status codes
  rpc.Status status = 1;

  // The message id that is returned by the cloud communication service
  // Used for getting the status of the message in the future
  string notification_id = 2;

  // FCM notifications error code statuses for failure cases.
  comms.CommsMessageSubStatus sub_status = 3;
}

service FCM {
  // Send message is the service exposed in VG which clients (comms service) will call to send push notifications
  rpc SendMessage (SendMessageRequest) returns (SendMessageResponse);
}
