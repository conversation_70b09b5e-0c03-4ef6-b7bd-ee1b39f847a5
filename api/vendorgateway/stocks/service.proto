syntax = "proto3";

package vendorgateway.stocks;

import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/bank_account_details.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/sort.proto";
import "api/typesv2/country_code.proto";
import "api/typesv2/nationality.proto";
import "api/typesv2/politically_exposed_status.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/stocks/enums.proto";
import "api/vendorgateway/stocks/stocks.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "google/type/postal_address.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/stocks";
option java_package = "com.github.epifi.gamma.api.vendorgateway.stocks";

// service to enable our domain service to connect with vendors for stock investments
service Stocks {
  // CreateAccount rpc to Create account with vendor for investment
  // All field mandatory
  rpc CreateAccount (CreateAccountRequest) returns (CreateAccountResponse);

  // UploadKYCDetails rpc to add KYC information to vendor
  // All field mandatory
  rpc UploadKYCDetails (UploadKYCDetailsRequest) returns (UploadKYCDetailsResponse);

  // CreateOrder rpc is used to create order with vendor
  // For every order of market type
  // Symbol,OrderType,(Qty or Notion),account_id (Vendor specific Account_id) and Side are mandatory field
  rpc CreateOrder (CreateOrderRequest) returns (CreateOrderResponse);

  // GetAccountDetails rpc is used  to get account details for vendor accountId
  rpc GetAccountDetails (GetAccountDetailsRequest) returns (GetAccountDetailsResponse);

  // GetOrderDetails rpc is used to get order details for given vendor accountId and Epifi orderId
  rpc GetOrderDetails (GetOrderDetailsRequest) returns (GetOrderDetailsResponse);

  // GetAllOpenPosition rpc is used to fetch all the open position of given user
  rpc GetAllOpenPositions (GetAllOpenPositionsRequest) returns (GetAllOpenPositionsResponse);

  // GetPositionForSymbol rpc is used to fetch Position data for given symbol
  rpc GetPositionForSymbol (GetPositionForSymbolRequest) returns (GetPositionForSymbolResponse);

  // UpdateTradeConfiguration rpc is used for updating trading configuration for given user
  rpc UpdateTradeConfiguration (UpdateTradeConfigurationRequest) returns (UpdateTradeConfigurationResponse);

  // Get Report File from vendor for given date and file type
  // The reports communicate transaction-level details as well as overall settlement amounts, transfer direction, and payment timing.
  // For example: for a report_type `DETAILs`, following fields are present
  // account_id, account_no, symbol, system_date, trade_date, trade_time, settle_date,
  // entry_type, side, qty, price,status, order_id, client_order_id
  // This detailed report file contains all the trades executed for epiFi users. The report is available on T+2.
  // We use the individual trade in this file to notify the partner bank how to distribute the money in IN-pool account to user's savings account.
  // This file is used for signaling sell workflow and change stage
  rpc GetReportFile (GetReportFileRequest) returns (GetReportFileResponse);

  // CancelOrder is used to cancel given order with the vendor
  // If the order is filled then canceling the order is not applicable
  rpc CancelOrder (CancelOrderRequest) returns (CancelOrderResponse);

  // Rpc is used to fetch status of exchange whether it is available for trade or not
  // Rpc returns next market session open and close timestamp, status of exchange in current timestamp
  rpc GetExchangeStatus (GetExchangeStatusRequest) returns (GetExchangeStatusResponse);

  // RPC is used to fetch activities of specific account or whole account
  // This RPC is used to track activities of user such as fee charge, dividend allocated..etc
  // pls refer to request for details
  rpc GetNonTradeActivities (GetNonTradeActivitiesRequest) returns (GetNonTradeActivitiesResponse);

  // connects to upstream vendor websockets
  // and receives stream of prices(trades) and minute bars (price-changes-over-one-minute) for all the subscribed symbols
  // updates are send to caller via stream
  // In case the vendor connection is broken, this RPC will return error
  // It is caller's responsibility to call the RPC again if upstream connection is broken
  rpc GetPriceUpdates (GetPriceUpdatesRequest) returns (stream GetPriceUpdatesResponse);

  // GetDocuments is used to get account statement and trade confirmation document for given account id
  // if DocumentStatementType is not provided, all type of documents are returned
  rpc GetDocuments (GetDocumentsRequest) returns (GetDocumentsResponse);

  // DownloadDocument is used to download document for given account id and document id(that we received in GetDocuments rpc)
  rpc DownloadDocument (DownloadDocumentRequest) returns (DownloadDocumentResponse);

  // GetStocksPriceSnapshots is used to get properties for trade, quote, and bar objects for given symbols.
  // in response returns a map where key is equal to symbol and value is properties for that symbol
  rpc GetStocksPriceSnapshots (GetStocksPriceSnapshotsRequest) returns (GetStocksPriceSnapshotsResponse);

  // GetHistoricalStockBars returns aggregated historical data for the stock over a specified time period
  rpc GetHistoricalStockBars (GetHistoricalStockBarsRequest) returns (GetHistoricalStockBarsResponse);

  // Streaming rpc to create broker account with vendor. This rpc serves converts the previous CreateAccount rpc into a client streaming call.
  rpc CreateAccountWithStream (stream CreateAccountWithStreamRequest) returns (CreateAccountWithStreamResponse);

  // SendBankDetails rpc sends the bank details to the stock broker. The broker requires the bank account details to
  // credit the investor's account.
  rpc SendBankDetails (SendBankDetailsRequest) returns (SendBankDetailsResponse);

  // GetBankDetails rpc retrieves the bank account details, status of request made via SendBankDetails rpc
  rpc GetBankDetails (GetBankDetailsRequest) returns (GetBankDetailsResponse);

  // connects to upstream vendor with persistent http connection
  // and receives sse stream of order(trades updates)
  // updates are send to caller via stream
  // In case the vendor connection is broken, this RPC will return error
  // It is caller's responsibility to call the RPC again if upstream connection is broken
  // GetOrderUpdates rpc is used to get order details
  rpc GetOrderUpdates (GetOrderUpdatesRequest) returns (stream GetOrderUpdatesResponse);

  // connects to upstream vendor with persistent http connection
  // and receives sse stream of account updates
  // updates are send to caller via stream
  // In case the vendor connection is broken, this RPC will return error
  // It is caller's responsibility to call the RPC again if upstream connection is broken
  // GetAccountUpdates rpc is used to get account update details via vendor sse
  rpc GetAccountUpdates (GetAccountUpdatesRequest) returns (stream GetAccountUpdatesResponse);

  // Journaling is moving CASH or SECURITIES from one trading account to another
  // On successful journal transaction creation, Journals entity is returned
  rpc CreateJournal (CreateJournalRequest) returns (CreateJournalResponse);

  // GetJournal returns corresponding Journal entity for the requested Id
  // RPC can be used to poll Journal transaction status
  rpc GetJournal (GetJournalRequest) returns (GetJournalResponse);

  // Batch journaling is only supported for CASH journaling at present
  // returns list of Journals entities
  rpc CreateBatchJournal (CreateBatchJournalRequest) returns (CreateBatchJournalResponse);

  // InitiateFundTransfer initiates a fund transfer from an account setup at vendor's end
  // and a bank account setup with partner bank(federal bank) and returns created FundTransfer entity
  rpc InitiateFundTransfer (InitiateFundTransferRequest) returns (InitiateFundTransferResponse);

  // GetAllFundTransfers rpc retrieves all the fund transfers requested via InitiateFundTransfer rpc
  rpc GetAllFundTransfers (GetAllFundTransfersRequest) returns (GetAllFundTransfersResponse);

  // GetTradingAccount returns trading account information like buying power, cash, withdrawable amount etc for a broker account
  rpc GetTradingAccount (GetTradingAccountRequest) returns (GetTradingAccountResponse);

  // GetAllJournals returns all the Journal entries present with vendor
  rpc GetAllJournals (GetAllJournalsRequest) returns (GetAllJournalsResponse);

  // connects to upstream vendor with persistent http connection
  // and receives sse stream of journal updates
  // updates are send to caller via stream
  // In case the vendor connection is broken, this RPC will return error
  // It is caller's responsibility to call the RPC again if upstream connection is broken
  // GetJournalUpdates rpc is used to get journal updates
  rpc GetJournalUpdates (GetJournalUpdatesRequest) returns (stream GetJournalUpdatesResponse);

  // connects to upstream vendor with persistent http connection
  // and receives sse stream of journal updates
  // updates are sent to caller via stream
  // In case the vendor connection is broken, this RPC will return error
  // It is caller's responsibility to call the RPC again if upstream connection is broken
  // GetFundTransferUpdates rpc is used to get transfer order updates
  rpc GetFundTransferUpdates (GetFundTransferUpdatesRequest) returns (stream GetFundTransferUpdatesResponse);

  // UploadDocuments RPC is used to upload documents for an already created account
  rpc UploadDocuments (UploadDocumentsRequest) returns (UploadDocumentsResponse);

  // GetAccountActivities is used to fetch trade and non trade activities of specific account
  // Few example activities include Fill (buy side), Fill (sell side), Dividend, Fee etc
  rpc GetAccountActivities (GetAccountActivitiesRequest) returns (GetAccountActivitiesResponse);

  // GetAllAssets returns all the Assets entries present with vendor if no filter is specified
  // should use filters in request as size of response is quite high and can cause issue
  // currently supports us_equity asset class only
  // e.g: response size without filter is around 10mb, with filter asset_class: us_equity & status: active is around 4mb
  // with filter asset_class: us_equity & status: active & exchange: NYSE is around 1mb
  rpc GetAllAssets (GetAllAssetsRequest) returns (GetAllAssetsResponse);

  // GetAsset returns the Asset entry present with vendor from one of the provided identifier
  // if no asset found for provided identifier then rpc would return not found status in response
  rpc GetAsset (GetAssetRequest) returns (GetAssetResponse);

  // GetCorporateActions gives the latest corporate actions announced by any company, actions include mergers, stock splits, spin offs, dividends
  // Api doc https://docs.alpaca.markets/reference/corporateactions-1
  rpc GetCorporateActions (GetCorporateActionsRequest) returns (GetCorporateActionsResponse);

  // deletes Bank Relationship for US Stock account
  // Api doc https://docs.alpaca.markets/reference/deleterecipientbank
  rpc DeleteBankDetails (DeleteBankDetailsRequest) returns (DeleteBankDetailsResponse);
}

message GetCorporateActionsRequest {
  vendorgateway.RequestHeader header = 1;
  // The symbol of the company initiating the announcement.
  // Optional
  repeated string symbols = 2;
  //  A comma-delimited list of ENUM.CorporateActionType values
  // Optional
  repeated CorporateActionType types = 3;
  // The start (inclusive) of the date range when searching corporate action announcements
  google.type.Date start = 4;
  // The end (inclusive) of the date range when searching corporate action announcements.
  google.type.Date end = 5;
  // The maximum number of data points to return in the response
  // Optional
  int64 limit = 6;
  // Pagination token to continue from
  // Optional
  string page_token = 7;
  // Sort data in ascending or descending order. asc , desc
  // Optional
  api.typesv2.common.SortOrder sort = 8;

}

message GetCorporateActionsResponse {
  enum Status {
    Ok = 0;

    // Internal for server errors
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  CorporateActions corporate_actions = 2;
  string next_page_token = 3;
}

message UploadDocumentsRequest {
  vendorgateway.RequestHeader header = 1;
  string vendor_account_id = 2;
  repeated DocumentUpload document_list = 3;
}

message UploadDocumentsResponse {
  // Status of the request
  rpc.Status status = 1;
}

message GetFundTransferUpdatesRequest {
  vendorgateway.RequestHeader header = 1;
}

message GetFundTransferUpdatesResponse {
  // Status of the request
  rpc.Status status = 1;

  // FundTransferUpdate from the vendor
  FundTransferUpdate fund_transfer_update = 2;
}

message FundTransferUpdate {
  // firm account id created at vendor's end
  string account_id = 1;
  // time at which event was generated
  google.protobuf.Timestamp event_time = 2;
  // event_id for uniquely identifying the event
  string event_id = 3;
  // old status
  FundTransferStatus status_from = 4;
  // new status
  FundTransferStatus status_to = 5;
  // unique identifier for fund transfer request generated at vendor
  string fund_transfer_id = 6;
}

message GetJournalUpdatesRequest {
  vendorgateway.RequestHeader header = 1;
}

message GetJournalUpdatesResponse {
  // Status of the request
  rpc.Status status = 1;

  // JournalUpdate from the vendor
  JournalUpdate journal_update = 2;
}

message JournalUpdate {
  // time at which event was generated
  google.protobuf.Timestamp event_time = 1;
  // cash or security
  JournalType journal_type = 2;
  // event_id for uniquely identifying the event
  string event_id = 3;
  // unique identifier for journal request
  string journal_id = 4;
  // old status
  JournalStatus status_from = 5;
  // new status
  JournalStatus status_to = 6;
}

message GetAllJournalsRequest {
  vendorgateway.RequestHeader header = 1;
  // one of from account or to account is mandatory in request
  // account id from which money is moved
  string from_account = 2;
  // one of from account or to account is mandatory in request
  // account id to which money is moved
  string to_account = 3;
  // status of journal, response will be filtered with JournalStatus
  // if journal status is UNSPECIFIED, response will not be filtered by JournalStatus
  JournalStatus journal_status = 4;
  // type of journal
  // can be cash transfer or securities
  // if journal type is UNSPECIFIED, response will not be filtered by JournalType
  JournalType journal_type = 5;
  // journal records created after this date will be fetched
  // if nil all the records will be fetched
  google.type.Date created_after = 6;
  // journal records created before this date will be fetched
  // if nil all the records will be fetched
  google.type.Date created_before = 7;
  // field which should be used to order journals entries
  JournalsOrderField order_field = 8;
  // order specification for ordering journals, order_field will be used to apply the sort ordering specified in sort_order
  api.typesv2.common.SortOrder sort_order = 9;
}

enum JournalsOrderField {
  JOURNALS_ORDER_FIELD_UNSPECIFIED = 0;
  JOURNALS_ORDER_FIELD_CREATED_AT = 1;
}
message GetAllJournalsResponse {
  rpc.Status status = 1;
  repeated Journal journals = 2;
}

message GetAllFundTransfersRequest {
  vendorgateway.RequestHeader header = 1;
  // account id from which money needs to be moved
  // firm account's account id in case money needs to be moved from firm account
  string account_id = 2;
  // direction of money movement from firm account
  FundTransferDirection direction = 3;
  // number of transactions to fetch if sorted in descending order of txn creation time
  int32 limit = 4;
  // number of transactions to leave from starting if sorted in descending order of txn creation time
  int32 offset = 5;
}

message GetAllFundTransfersResponse {
  rpc.Status status = 1;
  repeated FundTransfer fund_transfers = 2;
}

message InitiateFundTransferRequest {
  // AdditionalInfo holds transfer request related additional information
  message AdditionalInfo {
    // since a transfer request corresponds to one or more journal requests
    // below fields are used to identify the funds transfer is for which accounts
    // journal_id is not needed if we transfer money to non-federal bank account then these fields are not needed
    //
    // holds the identifier of the first journal corresponding to the transfer
    string from_journal_id = 1;
    // holds the identifier of the last journal corresponding to the transfer
    string to_journal_id = 2;
  }

  vendorgateway.RequestHeader header = 1;
  // account id from which money needs to be moved
  // firm account's account id in case money need to be moved from firm account
  string account_id = 2 [(validate.rules).string = {min_len: 1}];
  // amount to be transferred in dollars
  google.type.Money amount = 3 [(validate.rules).message.required = true];
  // direction of money movement from an account
  FundTransferDirection direction = 4 [(validate.rules).enum = {not_in: [0]}];
  // relationship id for an account at vendor's end and a bank account
  string bank_relationship_id = 5 [(validate.rules).string = {min_len: 1}];
  // transfer mode for the requested fund transfer
  FundTransferMode transfer_mode = 6;
  // transfer specific information required by vendor
  AdditionalInfo additional_info = 7;
  // fee payment method
  FundTransferFeePaymentMode fee_payment_method = 8;
}

message InitiateFundTransferResponse {
  rpc.Status status = 1;
  FundTransfer fund_transfer = 2;
}

message FundTransfer {
  string id = 1;
  // bank_relationship created for the firm account at vendor's end and pool account
  string bank_id = 2;
  // firm account id created at vendor's end
  string account_id = 3;
  // we will support wire transfer only
  FundTransferMode fund_transfer_mode = 4;
  // status of fund transfer
  FundTransferStatus status = 5;
  // reason for failure if any
  string reason = 6;
  // amount the recipient will receive after any applicable fees are deducted
  google.type.Money amount = 7;
  // Funds are incoming or outgoing
  FundTransferDirection transfer_direction = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp expires_at = 11;
  // actual amount requested to be transferred
  google.type.Money requested_amount = 12;
  // dollar amount of any applicable fees
  google.type.Money fee = 13;
  // how any applicable fees is/will be paid
  string fee_payment_method = 14;
}

message CreateJournalRequest {
  vendorgateway.RequestHeader header = 1;
  // requested journal entity
  // vendor account Id of source account
  string from_account = 2 [(validate.rules).string = {min_len: 1}];
  // vendor account Id of destination account
  string to_account = 3 [(validate.rules).string = {min_len: 1}];
  // type of journal
  // can be cash transfer or securities
  JournalType journal_type = 4 [(validate.rules).enum = {not_in: [0]}];
  // Journal type specific data
  oneof journal_data {
    // making one of the below fields as mandatory
    option (validate.required) = true;
    CashJournalData cash_journal_data = 5;
    SecuritiesJournalData securities_journal_data = 6;
  }
  // currency code in which Journaling is done
  // eg: for us stocks broker currency will be always USD
  string currency = 7;
  // description is a remark associated with the journal request
  string description = 8;
}

message CreateJournalResponse {
  enum Status {
    OK = 0;

    // insufficient balance to create a journal entry, e.g., entry to withdraw funds from wallet
    // This may happen if there are parallel withdrawal requests
    // which may happen due situations like delays in withdrawal workflow execution.
    INSUFFICIENT_BALANCE = 100;
  }

  rpc.Status status = 1;

  // Journal entity created at vendor
  Journal journal = 2;
}

message GetJournalRequest {
  vendorgateway.RequestHeader header = 1;
  string journal_id = 2;
}

message GetJournalResponse {
  rpc.Status status = 1;
  Journal journal = 2;
}

message CreateBatchJournalRequest {
  message DestinationDetail {
    // destination vendor account Id for which journal is requested
    string to_account = 1;
    // amount to be transferred
    google.type.Money amount = 2;
  }

  vendorgateway.RequestHeader header = 3;
  // type of journal at present can only be CASH
  // RPC impl can be extended to SECURITIES when use case arises
  JournalType journal_type = 4 [(validate.rules).enum = {in: [1]}];
  // source account from which cash is expected to be moved
  string from_account = 5;
  // list of destination account details for journaling
  repeated DestinationDetail destination_details = 6;
}

message CreateBatchJournalResponse {
  rpc.Status status = 1;
  // list of journal entities with status
  repeated Journal journals = 2;
}

message CreateOrderRequest {
  vendorgateway.RequestHeader header = 1;
  // Represent unique identifier for  Stock
  // example AAPL(for Apple)
  string symbol = 2 [(validate.rules).string = {min_len: 1}];
  // Represent Qty to trade
  double qty = 3;
  // Represent amount worth of symbol to trade
  google.type.Money notional = 4;
  // it can take value like sell or buy
  Side side = 5;
  // it define trade behaviour to execute
  // refer enum definition for more details
  OrderType order_type = 6;
  // time in force at which the order
  // refer enum definition for more details
  TimeInForce time_in_force = 7;
  // It represent waiting for right buy/sell opportunity
  google.type.Money limit_price = 8;
  // It represent execute a trade when a stock reaches a price beyond which the investor is unwilling to sustain losses
  // For buy orders, this means buying as soon as the price climbs above the stop price.
  // For sell orders, this means selling as soon as the price drops below the stop price.
  google.type.Money stop_price = 9;
  // Represent commission to be charge by vendor for this order
  // [deprecated] use order_commission instead
  google.type.Money commission = 10 [deprecated = true];
  // Vendor account_id
  string account_id = 11 [(validate.rules).string = {min_len: 1}];
  // Represent UUID for each order at our side
  string client_order_id = 12 [(validate.rules).string = {min_len: 1, max_len: 64}];
  // brokerage to be deducted for the order
  Brokerage brokerage = 13;
}

message Brokerage {
  // brokerage could be either a flat amount or percentage of notional amount
  oneof brokerage {
    // flat amount to be charged as brokerage
    google.type.Money brokerage_amount = 27;
    // percentage of the trade amount that should be charged as brokerage
    double brokerage_in_percentage = 28;
  }
}

// Represent Order for trade
// This is response object from vendor
message Order {
  // Represent UUID for current order wrt to vendor
  string id = 1;
  // Represent UUID for each order at our side
  string client_order_id = 2 [(validate.rules).string.uuid = true];
  // The order ID that this order was replaced
  // It occur while replace order
  string order_replaced_by = 3;
  // The order ID that this order replaces
  // It occur while replace order
  string order_replaced_with = 4;
  // Represent vendor UUID for given symbol
  string asset_id = 5 [(validate.rules).string.uuid = true];
  // Represent unique identifier for asset Symbol
  // example AAPL(for Apple)
  string symbol = 6;
  // The Amount worth of stock user trade
  google.type.Money notional = 7;
  // The Qty of side during the trade
  double qty = 8;
  // the minimum quantity by which an order should be filled
  // an order of 1000 units with minimum fill 200 will require that each trade is for at least 200 units
  double filled_qty = 9;
  // The average fill price is the price at which you bought (or sold) the stock.
  // So, in case of a buy order, if the stock price goes above your average fill price,
  // you will make money, otherwise, you will lose money
  google.type.Money filled_avg_price = 10;
  // Valid values: market, limit, stop, stop_limit, trailing_stop
  OrderType order_type = 11;
  // Valid values: buy and sell
  Side side = 12;
  // Represent Time at which order need to placed And price it might take
  // Refer enum definition for more details
  TimeInForce time_in_force = 13;
  // It represent waiting for right buy/sell opportunity
  google.type.Money limit_price = 14;
  // It represent execute a trade when a stock reaches a price beyond which the investor is unwilling to sustain losses
  // For buy orders, this means buying as soon as the price climbs above the stop price.
  // For sell orders, this means selling as soon as the price drops below the stop price.
  google.type.Money stop_price = 15;
  // Represent state of Order status
  OrderStatus order_status = 16;
  // If true,
  // eligible for execution outside regular trading hours.
  api.typesv2.common.BooleanEnum extended_hours = 17;
  // The highest (lowest) market price seen since the trailing stop order was submitted
  string hwm = 18;
  // A trailing stop is a modification of a typical stop order that can be set
  // at a defined dollar or percentage amount away from a security's current market
  google.type.Money trail_price = 19;
  double trail_percent = 20;
  // The commission charged for order
  google.type.Money commission = 21;
  google.protobuf.Timestamp created_at = 22;
  google.protobuf.Timestamp updated_at = 23;
  google.protobuf.Timestamp submitted_at = 24;
  google.protobuf.Timestamp filled_at = 25;
  google.protobuf.Timestamp expired_at = 26;
  google.protobuf.Timestamp cancelled_at = 27;
  google.protobuf.Timestamp failed_at = 28;
  google.protobuf.Timestamp replaced_at = 29;
}

message CreateOrderResponse {
  enum Status {
    Ok = 0;

    // Internal for server errors
    INTERNAL = 13;

    // the order is already created with given client order id
    DUPLICATED_CLIENT_ORDER_ID = 115;

    // fractional order cant be placed for non fractional symbol
    FRACTIONAL_ORDER_PLACED_FOR_NON_FRACTIONABLE_SYMBOL = 116;

    // given symbol for order is inactive for trade at exchange
    SYMBOL_INACTIVE_FOR_TRADE = 117;

    // if request sell qty is less than portfolio qty for that symbol
    // then order is not placed with vendor
    INSUFFICIENT_SELL_QTY_REQUESTED = 118;

    // primarily impacts non-US Persons who invest in US PTP Securities
    // With effect from 1 January 2023, non-US Persons will incur a 10% withholding tax on gross proceeds from sales or trading of US PTP securities.
    // PTP symbols with no exceptions are by default blocked from being purchased.
    // but it is a configuration that can be modified as well
    // ref: https://www.irs.gov/individuals/international-taxpayers/partnership-withholding
    // PTP list: https://ibkr.info/node/4706
    SYMBOL_IS_PTP_AND_ACCOUNT_IS_NOT_CONFIGURED = 119;

    // given symbol is not found at vendors end and is not available for trade
    SYMBOL_NOT_FOUND_AT_VENDOR = 120;

    // given symbol is not tradable at vendor end
    SYMBOL_NON_TRADABLE_AT_VENDOR = 121;

    // if the user is trying to short selling and we dont support short sell
    // https://www.investopedia.com/terms/s/shortselling.asp
    SHORT_SELLING_IS_NOT_ALLOWED = 122;

    // user doesn't have enough buying power
    // example scenario: user has placed an order to withdraw all funds from their wallet, but then tries to buy stocks.
    INSUFFICIENT_BUYING_POWER = 123;

    // user has recently placed an opposite side order
    // example scenario: an opposite side order might get flagged as a potential wash trade.
    OPPOSITE_SIDE_ORDER_EXISTS = 124;

    // if order is rejected by vendor due to 'trade denied due to pattern day trading protection'
    // max limit on number of intraday trades imposed by US government
    DAY_TRADING_PROTECTION = 125;
  }
  rpc.Status status = 1;
  Order order = 2;
}

message CreateAccountRequest {
  vendorgateway.RequestHeader header = 1;
  PersonDetails identity = 2;
  Disclosure disclosures = 3;
  repeated AgreementsDetails agreement_detail_list = 4;
  repeated DocumentUpload document_list = 5;
  TrustedContact trusted_contact = 6;
  ContactDetails contact_details = 7;
}

message CreateAccountResponse {
  rpc.Status status = 1;
  Account account = 2;
}

// Represent basic details of User
message PersonDetails {
  api.typesv2.common.Name name = 1;
  google.type.Date date_of_birth = 2;
  api.typesv2.CountryCode country_of_citizenship = 3;
  api.typesv2.CountryCode country_of_birth = 4;
  repeated IncomeSource income_source = 5;
  TaxDetails tax_details = 6;
  EmploymentDetails employment_details = 7;
  AnnualIncomeRange annual_income_range = 8;
  AnnualIncomeRange liquid_income_range = 9;
}

// Represent all tax related information
message TaxDetails {
  string tax_id = 1;
  TaxIdType tax_id_type = 2;
  api.typesv2.CountryCode country_of_tax_residence = 3;
}

// Represent information about Employment
message EmploymentDetails {
  string company_name = 1;
  string company_address = 2;
  string current_role = 3;
  EmploymentStatus employment_status = 4;
}

// Represent Annual income of user
message AnnualIncomeRange {
  google.type.Money min = 1;
  google.type.Money max = 2;
}

// Contains data with respect to nature of user whether political or control person
message Disclosure {
  DisclosureStatus is_control_person = 1;
  DisclosureStatus is_affiliated_exchange_or_finra = 2;
  DisclosureStatus is_politically_exposed = 3;
  DisclosureStatus immediate_family_exposed = 4;
}

// Represent Agreement which are accepted by user
// while Creating account with vendor
message AgreementsDetails {
  AgreementType type = 1;
  google.protobuf.Timestamp signed_at = 2;
  string ip_address = 3;
}

// Represent a series of documents based on Account Creation provided by vendor
// Documents are binary objects whose contents are encoded in base64.
// Note for W8Ben we share data using w8ben_data proto
message DocumentUpload {
  // this represent category of proof
  // EX: IdentityVerification
  DocumentType document_type = 1;
  // This represent name of specific document as proof
  // EX: pan card/passport..etc
  // Note: This field is not required for w8ben document
  DocumentSubType document_sub_type = 2;
  // contents are encoded in base64.
  string content = 3;
  // Represent format of data being send in content
  string mime_type = 4;
  // Used for creation of w8ben form using details
  W8BENData w8ben_data = 5;
}

// Represent Contact information of user
message ContactDetails {
  string email_address = 1;
  string phone_number = 2;
  // mandatory field, used during w8ben form
  string street_address = 3;
  string unit = 4;
  // mandatory field, used during w8ben form
  string city = 5;
  // mandatory field, used during w8ben form
  string state = 6;
  string postal_code = 7;
}

// Represent details related trust contact provided by user
message TrustedContact {
  // send only first and last name
  api.typesv2.common.Name name = 1;
  api.typesv2.CountryCode country = 2;
  ContactDetails contact_details = 3;
}

// contains Mandatory Field need for W8ben Form creation
message W8BENData {
  api.typesv2.CountryCode country_citizen = 1;
  google.type.Date current_date = 2 [deprecated = true];
  // represent form format Code/ID
  string revision = 3;
  google.protobuf.Timestamp timestamp = 4;
  string ip_address = 5;
  // contact_details of the user
  ContactDetails contact_details = 6;
  // personal_details of the user
  PersonDetails person_details = 7;
}

// Represent Account Info with vendor
// This is response object from vendor
message Account {
  // Represent AccountId wrt to Vendor
  string account_id = 1 [(validate.rules).string.min_len = 1];
  // Represent human-readable account number that can be shown to the end user
  string account_number = 2;
  google.type.Money last_equity = 3;
  // Represent Trade config for user
  TradeConfigurations trade_configuration = 4;
  // Represent Account Status with vendor
  AccountStatus account_status = 5;
  // Represent Account Type user had with vendor
  // Refer definition for more details
  AccountType account_type = 6;
  // Timestamp this account was created at
  google.protobuf.Timestamp created_at = 7;
}

// Represent Trade config for user
message TradeConfigurations {
  // both, entry, or exit. Controls Day Trading Margin Call checks.
  DayTradingMarginCallCheckType day_trading_margin_call_check = 1;
  // If true, account is able to participate in fractional trading
  api.typesv2.common.BooleanEnum enable_fraction_trading = 2;
  // Margin Multiplier
  // the number by which a Margin Requirement is multiplied to increase the amount you are required to hold as security for a Trade.
  string max_margin_multipler = 3;
  // Short selling involves borrowing a security whose price you think is going to fall from your brokerage
  // and selling it on the open market
  api.typesv2.common.BooleanEnum no_short_selling = 4;
  // A pattern day trader (PDT) is a trader who executes four or more day trades within five business days using the same account
  // If this occurs, the trader's account will be flagged as a PDT by their broker.
  string pattern_day_trade_check = 5;
  // If true, new orders are blocked
  api.typesv2.common.BooleanEnum suspend_trades = 6;
  // emails for order fills are sent/not.
  EnableEmailUpdate enable_email_update = 7;
}

message UploadKYCDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  // CKYC, Digilocker are providers
  repeated string provider_name = 2;
  DetailsVerification details_verification = 3;
  DocumentVerification document_verification = 4;
  FaceVerification face_verification = 5;
  IdentityVerification identity_verification = 6;
  BackgroundVerification background_verification = 7;
  // Vendor AccountId
  string account_id = 8;
}

message UploadKYCDetailsResponse {
  rpc.Status status = 1;
  string kyc_id = 2 [(validate.rules).string.min_len = 1];
}

// Contain kyc general details
message DetailsVerification {
  string id = 1 [(validate.rules).string.min_len = 1];
  int32 risk_score = 2;
  string risk_level = 3;
  string risk_categories = 4;
  string applicant_name = 5;
  string email_address = 6;
  api.typesv2.Nationality nationality = 7;
  // Number of their government issued ID.
  string id_number = 8;
  google.type.Date date_of_birth = 9;
  string address = 10;
  string postal_code = 11;
  api.typesv2.CountryCode country_of_residency = 12;
  google.protobuf.Timestamp kyc_completed_at = 13;
  string ip_address = 14;
  google.protobuf.Timestamp check_initiated_at = 15;
  google.protobuf.Timestamp check_completed_at = 16;
  KYCStatus approval_status = 17;
  // name of the CX agent or security ahead of epifi has to be passed in every request
  string approved_by = 18;
  string approval_reason = 19;
  google.protobuf.Timestamp approved_at = 20;
}

// KYC status about document and details
message DocumentVerification {
  string id = 1 [(validate.rules).string.min_len = 1];
  VerificationResult result = 2;
  VerificationStatus status = 3;
  // represent nationality extracted from document
  api.typesv2.Nationality nationality = 4;
  google.type.Date date_of_birth = 5;
  // Number listed on the document. It should be
  // all numbers or a mix of numbers and letters.
  // Should be extracted from the document.
  repeated string document_numbers = 6;
  // national_id_card, passport, Visa or driver_license
  DocumentSubType document_type = 7;
  // This are specific to document fields
  string first_name = 8;
  string last_name = 9;
  // Extracted from document
  api.typesv2.CountryCode issuing_country = 10;
  // Checks whether the age calculated from the
  // document’s date of birth data point is
  // greater than or equal to the minimum
  // accepted age
  VerificationResult age_validation = 11;
  // This checks whether data on the document is
  // consistent with data provided by the user
  VerificationResult data_comparison = 12;
  // Checks whether the document was of
  // sufficient quality to verify.
  VerificationResult image_integrity = 13;
  // This breaks down the document_comparison
  // field in more detail.
  DataComparisonDetails data_comparison_details = 14;
  // This breaks down the image_integrity field in
  // more detail
  ImageIntegrityDetails image_integrity_details = 15;
  // Checks whether visual (non-textual)
  // elements are correct given the document type.
  VisualAuthenticity visual_authenticity = 16;
  google.protobuf.Timestamp created_at = 17;
}

// Represent data verification at field level
message DataComparisonDetails {
  VerificationResult date_of_birth = 1;
  VerificationResult date_of_expiry = 2;
  VerificationResult first_name = 3;
  VerificationResult last_name = 4;
  VerificationResult age_validation = 5;
}

// Contains information about image quality
message ImageIntegrityDetails {
  // Checks whether the image was in color
  VerificationResult is_colour_picture = 1;
  // Checks if the document was of enough quality to be able to perform a fraud
  VerificationResult document_quality = 2;
  // Checks whether the quality of the image was sufficient for processing.
  VerificationResult image_quality = 3;
  // Checks whether the submitted document is supported.
  VerificationResult supported_document = 4;
}

// Compare if document is original or fake
message VisualAuthenticity {
  // If there is an indication of digital
  // tampering, this would result in a “MANUAL_INTERVENTION”
  VerificationResult digital_tampering = 1;
  // If no face was detected on the document,
  // this would result in a “MANUAL_INTERVENTION”
  VerificationResult face_detection = 2;
  // If fonts in the document don’t match the
  // expected ones,
  VerificationResult fonts_compare = 3;
  // Checks whether the document was present
  // or not when the photo was taken.
  VerificationResult original_document_provided = 4;
  // If the pictures of the person identified on the document
  VerificationResult compare_photo = 5;
  // If security features expected on the
  // document are missing or wrong,
  // this would result in a “MANUAL_INTERVENTION”
  VerificationResult security_features = 6;
  // check whether document is in standard format
  VerificationResult standard_format = 7;
}

// verify Disclosure term
message BackgroundVerification {
  string id = 1 [(validate.rules).string.min_len = 1];
  VerificationResult result = 2;
  VerificationStatus status = 3;
  VerificationResult politically_exposed_person = 4;
  // Government and International Organizations Sanctions Lists
  VerificationResult sanction = 5;
  // Negative events reported by publicly and generally
  // available media sources.
  VerificationResult adverse_media = 6;
  // Law-enforcement and Regulatory bodies Monitored
  // Lists (including Terrorism, Money Laundering and Most
  // Wanted lists)
  VerificationResult monitored_lists = 7;
  google.protobuf.Timestamp created_at = 8;
  // "If any of those fields are “consider” this is a space to
  // provide the breakdown of those Matches or Hits that
  // your KYC provider finds. It should include as much
  // detail as possible broken down for each match or hit
  // such as: name, aliases, addresses, DOB, type of hit
  // (PEP, adverse media, etc.) and any accompanying
  // details depending on the type of hit such as positions
  // held, lists found on, summary of and URL to adverse
  // media article, explanation of the monitored list, etc."
  BackgroundVerificationRecord records = 9;
}

message BackgroundVerificationRecord {
  // status of the screening request
  AmlScreeningStatus screening_status = 2 [deprecated = true];
  // in case match is found, relevant data is populated here
  repeated MatchData match_data = 4;
  // match found in the screening attempt
  AmlMatch match = 5 [deprecated = true];
  // AML Check is approved or rejected
  AmlAction action = 6;
  // name of person who took action when match is found
  string action_by = 7;
  // reason for approval or rejection aml check
  string action_reason = 8;
  // timestamp when action was taken
  google.protobuf.Timestamp action_taken_at = 9;
  // politically exposed person. Possible values - PEP, Not a PEP, Related to PEP
  api.typesv2.PoliticallyExposedStatus pep_status = 10;
  // comma separated values of different categories of pep like bureaucrat, civil servant etc
  string pep_classification = 11;
  // comma separated values of categories of adverse media found for user, ex. bank fraud, corruption etc.
  string adverse_media_classification = 12;
}

// represents the status of the screening attempt
enum AmlScreeningStatus {
  AML_SCREENING_STATUS_UNSPECIFIED = 0;
  // screening attempt raised successfully
  AML_SCREENING_STATUS_INITIATED = 1;
  // screening attempt failed
  AML_SCREENING_STATUS_FAILED = 2;
  // screening is completed
  AML_SCREENING_STATUS_SUCCESS = 3;
}

// MatchData has the details of the match to be sent to the calling service
message MatchData {
  // watchlist category against which match is found
  AmlWatchlistCategory matching_watchlist_category = 1;
  // comma separated watchlist names for which match is found
  string matching_watchlist = 2;
  // name of the customer in the watchlist record
  string matching_record_name = 3;
  // parameter for which the match is found
  AmlParameter matching_parameter = 4;
  // applicable when fuzzy matching is enabled
  double matching_percentage = 5;
  // whether the match is confirm or probable
  MatchType match_type = 6;
  // remarks given by epifi ops agent on the match
  string remarks = 7;
  // ops agent decision for this match
  bool is_match = 8;
  // aliases present in the match
  string watchlist_aliases = 9;
  // any notices issued by governments or agencies like interpol, fbi etc
  string notice_details = 10;
  // address details present in the watchlist
  string watchlist_address = 11;
  // associated countries present in the watchlist
  string watchlist_associated_countries = 12;
  // dates of birth present in the watchlist
  string watchlist_dob = 13;
  // any media articles or government website links containing information about the person
  string external_source_links = 14;
  // identity numbers present in the watchlist
  string watchlist_identity_numbers = 15;
  // any other information present in the watchlist
  string additional_info = 16;
}

enum MatchType {
  MATCH_TYPE_UNSPECIFIED = 0;
  MATCH_TYPE_CONFIRM = 1;
  MATCH_TYPE_PROBABLE = 2;
}

// action taken by risk ops in case match is found
enum AmlAction {
  AML_ACTION_UNSPECIFIED = 0;
  // user's application is approved by AML risk ops
  AML_ACTION_APPROVED = 1;
  // user's application is reject by AML risk ops
  AML_ACTION_REJECTED = 2;
}

// This describes the parameter match based on which case is created
enum AmlParameter {
  AML_PARAMETER_UNSPECIFIED = 0;
  // PAN number matching
  AML_PARAMETER_PAN = 1;
  // Passport number matching
  AML_PARAMETER_PASSPORT = 2;
  // Driving license number matching
  AML_PARAMETER_DRIVING_LICENSE = 3;
  AML_PARAMETER_NAME = 4 [deprecated = true];
  // Exact name matching in any order
  // e.g. Jolly Joseph and Joseph Jolly
  AML_PARAMETER_EXACT_NAME = 5;
  // Name matching with spaces removed
  // e.g. Jolly Joseph and JollyJoseph
  AML_PARAMETER_BINDING_NAME = 6;
  // Fuzzy name matching using a matching algorithm
  // a matching percentage is also returned for this case
  AML_PARAMETER_FUZZY_NAME = 7;
  // First name matching
  // e.g. Jolly Joseph and Jolly Kent
  AML_PARAMETER_INITIAL_NAME = 8;
  // Vowels in the name matching
  // e.g. Jolly Joseph and Jolly Yosef
  AML_PARAMETER_VOWEL_NAME = 9;
  // Alias name matching
  AML_PARAMETER_ALIAS_NAME = 10;
  // DIN number matching
  AML_PARAMETER_DIN = 11;
  // Date of birth fuzzy matching
  AML_PARAMETER_FUZZY_DOB = 12;
  // State in address matching
  AML_PARAMETER_STATE = 13;
  // Country in address matching
  AML_PARAMETER_COUNTRY = 14;
  // City in address matching
  AML_PARAMETER_CITY = 15;
}

enum AmlWatchlistCategory {
  AML_WATCHLIST_CATEGORY_UNSPECIFIED = 0;
  // national and international sanction lists
  AML_WATCHLIST_CATEGORY_SANCTIONS = 1;
  // law enforcement public domain data
  AML_WATCHLIST_CATEGORY_LAW_ENFORCEMENT = 2;
  // regulatory enforcement public domain data
  AML_WATCHLIST_CATEGORY_REGULATORY_ENFORCEMENT = 3;
  // Politically exposed persons list
  AML_WATCHLIST_CATEGORY_PEP = 4;
  // Adverse media coverage
  AML_WATCHLIST_CATEGORY_ADVERSE_MEDIA = 5;
  // Any other watchlist
  AML_WATCHLIST_CATEGORY_OTHERS = 6;
}

// Enum represents if a match is found or not after screening is done by vendor
enum AmlMatch {
  AML_MATCH_UNSPECIFIED = 0;
  // Match found in screening
  AML_MATCH_FOUND = 1;
  // Match not found in screening
  AML_MATCH_NOT_FOUND = 2;
  // Error in creating screening attempt
  AML_MATCH_ERROR = 3;
}

// KYC of identity details provided during account creation
message IdentityVerification {
  string id = 1 [(validate.rules).string.min_len = 1];
  VerificationResult result = 2;
  VerificationStatus status = 3;
  VerificationResult tax_id = 4;
  google.protobuf.Timestamp created_at = 5;
  TaxIdBreakdown tax_id_breakdown = 6;
}

message TaxIdBreakdown {
  string verification_message = 1;
}

// Checks whether the face in the document matches the live photo
message FaceVerification {
  string id = 1 [(validate.rules).string.min_len = 1];
  VerificationResult result = 2;
  VerificationStatus status = 3;
  // Checks whether the face in the document
  // matches the face in the live photo.
  VerificationResult face_comparison = 4;
  // Checks whether the quality and integrity of
  // the selfie was sufficient to perform a face
  // comparison.
  VerificationResult image_integrity = 5;
  // Checks whether the person in the live
  // photo is real
  VerificationResult visual_authenticity = 6;
  google.protobuf.Timestamp created_at = 7;
}

message GetAccountDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  // Represent Vendor AccountId
  // use Identifier oneof field instead
  string account_id = 2 [deprecated = true];
  // one of identifier field to get account based on multiple Identifiers
  oneof Identifier {
    // account identifier at vendor system
    string vendor_account_id = 3;
    // email id associated with the account
    string email_id = 4;
  }
}

// Get Account details for given AccountId
message GetAccountDetailsResponse {
  rpc.Status status = 1;
  Account account_details = 2;
}

message GetOrderDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  // Represent Vendor AccountId
  string account_id = 2;
  // Represents epifi us stocks service order id with vendor
  string order_id = 3;
}

// Get Order details for given AccountId and OrderId
message GetOrderDetailsResponse {
  rpc.Status status = 1;
  Order order_details = 2;
}

// Represent Position of specific stock
message Position {
  // Represent vendor specific asset id
  string asset_id = 1;
  string symbol = 2;
  string exchange = 3;
  // Avg price symbol
  google.type.Money avg_entry_price = 4;
  // qty of share user hold
  double qty = 5;
  // the current value of asset
  google.type.Money market_value = 6;
  // the price you paid for each share.
  google.type.Money cost_basis = 7;
  // the expected profit and loss
  google.type.Money unrealized_pl = 8;
  // the expected profit and loss percentage
  double unrealized_pl_percentage = 9;
  // the expected profit and loss intraday
  google.type.Money unrealized_intraday_pl = 10;
  // the expected profit and loss intraday percentage
  double unrealized_intraday_pl_percentage = 11;
  // Percent change from last day price
  double percentage_change_today = 12;
  // Current asset price per share
  google.type.Money current_price = 13;
  // last trading day price
  google.type.Money last_day_price = 14;
}

message GetAllOpenPositionsRequest {
  vendorgateway.RequestHeader header = 1;
  // Represent Vendor AccountId
  string account_id = 2;
}

message GetAllOpenPositionsResponse {
  rpc.Status status = 1;
  // represent list of open position for  user
  // if no open position then it will be empty
  repeated Position positions = 2;
}

message GetPositionForSymbolRequest {
  vendorgateway.RequestHeader header = 1;
  // Represent Vendor AccountId
  string account_id = 2;
  // Represent symbol for which position is required
  string symbol = 3;
}

message GetPositionForSymbolResponse {
  enum Status {
    // request was successful
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Position Not found for given symbol
    RECORD_NOT_FOUND = 5;
  }
  rpc.Status status = 1;
  Position position = 2;
}

message UpdateTradeConfigurationRequest {
  vendorgateway.RequestHeader header = 1;
  TradeConfigurations trade_configurations = 2;
  string account_id = 3;
}

message UpdateTradeConfigurationResponse {
  rpc.Status status = 1;
  TradeConfigurations updated_trade_configurations = 2;
}

message CancelOrderRequest {
  vendorgateway.RequestHeader header = 1;

  // Represent order_id with vendor
  string order_id = 2;

  string account_id = 3;
}

message CancelOrderResponse {
  enum Status {
    Ok = 0;

    // Unable to find order with given parameter
    ORDER_NOT_FOUND = 101;

    // Order reached a state in which cancel order is not applicable
    UNPROCESSABLE = 102;

    // Internal for server errors
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message GetReportFileRequest {
  vendorgateway.RequestHeader header = 1;

  // Date for which we need to fetch the report
  google.type.Date date = 2;

  ReportFileType file_type = 3;
}

message GetReportFileResponse {
  enum Status {
    Ok = 0;

    // Internal for server errors
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // Represent csv,pdf file data from vendor
  bytes file = 2;
}

message GetExchangeStatusRequest {
  vendorgateway.RequestHeader header = 1;
}

message GetExchangeStatusResponse {
  enum Status {
    Ok = 0;

    // Internal for server errors
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  bool is_market_open = 2;
  // represent the next market open time
  // Note: if the market is open then it refers to the next valid trade session
  // if the market is closed then it refers to the next immediate trade session
  google.protobuf.Timestamp next_market_open_at = 3;
  // represent the next market close time
  google.protobuf.Timestamp next_market_close_at = 4;
}

message GetNonTradeActivitiesRequest {
  vendorgateway.RequestHeader header = 1;
  // represent account_id for particular user
  // depend on use case if we want to get activity related to particular user
  string account_id = 2;
  // represent order in which data is fetched
  bool in_ascending_order = 3;

  // Represent page_token of for next page
  // Eg: help during getting next batch info via jenkins job
  rpc.PageContextRequest page_context = 4;
  // activity type filter for getting activities list
  vendorgateway.stocks.NonTradeActivityType activity_type = 5;

  // Note: Although both from and to are timestamps, Alpaca API responses are only differentiated on dates
  google.protobuf.Timestamp from_ts = 6;

  google.protobuf.Timestamp to_ts = 7;
}

message GetNonTradeActivitiesResponse {
  rpc.Status status = 1;

  // List of non-trade activities
  repeated NonTradeActivity activities = 2;

  // Page context for fetching the next page
  rpc.PageContextResponse page_context = 3;

  // Info about the token in page context
  PageTokenInfo page_token_info = 4;
}

message PageTokenInfo {
  // The date on which the activity occurred or on which the transaction associated with the activity settled.
  google.type.Date date = 1;
}

message NonTradeActivity {
  string id = 1;
  // vendor specific account Id
  string account_id = 2;
  NonTradeActivityType type = 3;
  // date at which activities occurred
  google.type.Date executed_date = 4;
  // represent amount either positive or negative according to activity
  google.type.Money net_amount = 5;
  // Represent description for activity
  string description = 6;
  // Represent symbol for activity
  string symbol = 7;
  DividendMetaInfo dividend_meta_info = 8;
  // Represent status of activity
  NonTradeActivityStatus status = 9;
}

message TradeActivity {
  string id = 1;

  // Stockbroker-specific account ID
  string account_id = 2;

  // The time and date of when this trade was processed
  google.protobuf.Timestamp transaction_time = 3;

  TradeActivitySubType trade_activity_sub_type = 4;

  // The per-share price that the trade was executed at.
  google.type.Money per_share_price = 5;

  // The number of shares involved in the trade execution.
  double qty_executed = 6;

  // Can be Buy or Sell
  Side side = 7;

  // symbol/ticker of the asset, e.g. AAPL for Apple
  string symbol = 8;

  // For partially_filled orders, the quantity of shares that are left to be filled
  double qty_remaining = 9;

  // The stockbroker-side ID for the order filled
  string order_id = 10;

  // The cumulative quantity of shares involved in the execution.
  double qty_cumulative = 11;

  // status of the order
  OrderStatus order_status = 12;

  TradeActivityType trade_activity_type = 13;
}

message DividendMetaInfo {
  // If activity is dividend then it is used to represent qty for which dividend allocated
  double qty = 1;
  // represent dividend allocated for each qty
  google.type.Money per_share_amount = 2;
}

message GetPriceUpdatesRequest {
  vendorgateway.RequestHeader header = 1;
  // list of symbol_ids for which caller wants to receive price (trade) updates and bars
  // expects at least 1 symbol
  repeated string symbol_ids = 2 [(validate.rules).repeated.min_items = 1];
}

message GetPriceUpdatesResponse {
  // Status of the request
  rpc.Status status = 1;

  // contains price update for a particular symbol
  PriceUpdates price_updates = 2 [deprecated = true];

  StockPriceUpdateDatapoint stock_price_update_datapoint = 3;
}

// PriceUpdates message contains price updates for a particular symbol
// This message will be sent via stream after price updates is received
message PriceUpdates {
  option deprecated = true;

  string symbol = 1;
  // updated price, price is updated as a result of trades happening for the symbol
  double updated_price = 2;
  // timestamp at which the price was updated
  string updated_at = 3;
}

message StockPriceUpdateDatapoint {
  // symbol for which price update datapoint was received
  string symbol = 3;

  oneof datapoint {
    Trade trade = 4;

    // stock bar for a 1-min interval
    Bar minute_bar = 5;
  }
}

message GetDocumentsRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  // start date and end date are inclusive
  google.type.Date start_date = 3;
  google.type.Date end_date = 4;
  DocumentStatementType type = 5;
}

message GetDocumentsResponse {
  message DocumentDetails {
    string id = 1;
    DocumentStatementType type = 2;
    google.type.Date date = 3;
  }
  // Status of the request
  rpc.Status status = 1;
  repeated DocumentDetails details = 2;
}

message DownloadDocumentRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
  string document_id = 3;
}

message DownloadDocumentResponse {
  rpc.Status status = 1;
  bytes document_data = 2;
}

message GetStocksPriceSnapshotsRequest {
  vendorgateway.RequestHeader header = 1;
  // eg. AAPL, TSLA
  repeated string symbols = 2;

  MarketDataSource market_data_source = 3;
}

message GetStocksPriceSnapshotsResponse {
  message StockPriceInfo {
    Trade latest_trade = 1;
    Quote latest_quote = 2;
    Bar minute_bar = 3;
    Bar daily_bar = 4;
    Bar prev_daily_bar = 5;
  }

  rpc.Status status = 1;
  // this map contains properties for trade, quote, and bar objects for each symbol.
  map<string, StockPriceInfo> stock_price_snapshots = 2;
}

message GetHistoricalStockBarsRequest {
  vendorgateway.RequestHeader header = 1;

  // eg. AAPL, TSLA
  string symbol = 2 [(validate.rules).string.min_len = 1];

  // timeframe for aggregation
  // values can be in Min, Hour, Day, Week and Month time window sizes with a maximum constraints of: 59Min, 23Hour, 1Day, 1Week, 12Month
  string time_frame = 3 [(validate.rules).string.min_len = 1];

  // to filter data equal to or after this time, defaults to beginning of current day in USA
  google.protobuf.Timestamp start_time = 4;

  // to filter data equal to or before this time, defaults to current time
  google.protobuf.Timestamp end_time = 5;

  // [optional] data points to return, must be in range 1-10000, defaults to 1000
  int32 limit = 6;

  // pagination token to get next page of data
  string page_token = 7;

  MarketDataSource market_data_source = 8;
}

message GetHistoricalStockBarsResponse {
  rpc.Status status = 1;

  // list of historical stock bars
  repeated Bar bars = 2;

  // pagination token to get next page of data
  string next_page_token = 3;
}

// A Bar object contains information such as open, high, low, and closing price for a stock.
message Bar {
  // start-time of the time-interval trades are aggregated over to form the bar
  // e.g. in case of minute bars if the timestamp is 2020-04-01T23:37:00Z, it means the bar is for all trades happened b/w 23:37 and 23:38
  // in case of hourly bars, the timestamp denotes the start of the hour
  // in case of daily bars, the timestamp denotes the market day in EST5EDT
  // in case of weekly bars, the timestamp denotes Monday for the week
  google.protobuf.Timestamp ts = 1;

  // Stock price at which the first trade in the time-interval happened
  double open_price = 2;

  // Highest price at which stock was traded in the time-interval
  double high_price = 3;

  // Lowest price at which stock was traded in the time-interval
  double low_price = 4;

  // Stock price at which the last trade in the time-interval happened
  double close_price = 5;

  // Number of stock units traded in the time-interval
  int64 volume = 6;

  // Number of trades that happened in the time-interval
  int64 number_of_trades = 7;

  // Volume-weighted average price of stock based on trades in the time-interval
  double vol_wt_avg_price = 8;
}

// A Trade object contains the details of one trade, such as the time of the trade, trade price, trade size, and trade exchange.
message Trade {
  google.protobuf.Timestamp ts = 1;
  string exchange = 2;
  double trade_price = 3;
  int64 trade_size = 4;
  repeated string trade_conditions = 5;
  int64 trade_id = 6;
  string tape = 7;
}

// A Quote object contains the National Best Bid and Offer (NBBO) data for a security.
message Quote {
  google.protobuf.Timestamp ts = 1;
  string ask_exchange = 2;
  double ask_price = 3;
  int64 ask_size = 4;
  string bid_exchange = 5;
  double bid_price = 6;
  int64 bid_size = 7;
  repeated string quote_conditions = 8;
  string tape = 9;
}

message CreateAccountWithStreamRequest {
  bytes data = 1;
}

message CreateAccountWithStreamResponse {
  rpc.Status status = 1;
  Account account = 2;
}

message SendBankDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  // Represent Vendor AccountId
  string broker_account_id = 2;
  // bank account details of the investor
  api.typesv2.BankAccountDetails bank_account_details = 3;
  // address of the investor's bank from where funds from wallet can be withdrawn to or loaded from
  google.type.PostalAddress address = 4;
  // bank identifier code/swift code of the bank account, used to send money between banks globally
  // It is an internationally standardized code that uniquely identifies a bank or financial institution for
  // the purpose of international transactions. The BIC/SWIFT code is used to ensure that money is sent to the
  // correct bank when making cross-border payments.
  string bank_identification_code = 5;
}

message SendBankDetailsResponse {
  rpc.Status status = 1;
}

message GetBankDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  // Represent Vendor AccountId
  string broker_account_id = 2;
}

message GetBankDetailsResponse {
  rpc.Status status = 1;
  repeated BankDetail bank_details = 2;
}

message BankDetail {
  // BankDetailsStatus denotes the status of bank details we sent to the vendor
  BankDetailsStatus bank_details_status = 1;
  // bank account details of the investor
  api.typesv2.BankAccountDetails bank_account_details = 2;
  // address of the investor
  google.type.PostalAddress address = 3;
  // time when bank relationship was created
  google.protobuf.Timestamp created_at = 4;
  // time when bank relationship was updated
  google.protobuf.Timestamp updated_at = 5;
  // any error messages when bank relationship was created
  string error_message = 6;
  string bank_code = 7;
  // Bank identifier. ABA for domestic or BIC for international
  string bank_code_type = 8;
  // Bank relationship id
  string bank_relationship_id = 9;
}

// BankDetailsStatus denotes the status of bank details we sent to the vendor. The vendor requires the bank details
// to add to the MT199 files generated during Wire transfer for sell orders.
enum BankDetailsStatus {
  BANK_DETAILS_STATUS_UNSPECIFIED = 0;
  // broker has received the bank details.
  BANK_DETAILS_STATUS_INITIATED = 1;
  // broker has rejected the bank details of the user.
  // NOTE: we do not know in what cases can the bank details be rejected. This is not reachable for now.
  BANK_DETAILS_STATUS_REJECTED = 2;
  // broker has approved the bank details of the user.
  BANK_DETAILS_STATUS_APPROVED = 3;
  // the bank details were deleted by the user. NOTE: this is not reachable currently.
  BANK_DETAILS_STATUS_DELETED = 4;
  // broker cancelled the bank relationship
  BANK_DETAILS_STATUS_CANCELED = 5;
}

message GetOrderUpdatesRequest {
  vendorgateway.RequestHeader header = 1;
}

message GetOrderUpdatesResponse {
  // Status of the request
  rpc.Status status = 1;

  // TradeUpdate from the vendor
  OrderUpdate order_update = 2;
}

// OrderUpdate contains updated trade object updates received over SSE endpoints
message OrderUpdate {
  // account id for which event is generated
  string account_id = 1;

  // time at which event was generated
  google.protobuf.Timestamp event_time = 2;

  // Deprecated: Use event_ulid instead.
  // event_id for uniquely identifying the event
  // it is a ULID, which can be alphanumeric, but for now we are taking it as int64 because we are getting integer values
  int64 event_id = 3 [deprecated = true];

  // order details
  Order order = 4;

  /*
timestamp at which the corresponding transition of state happened at vendor
eg:
1. if order transition to `filled` then value of timestamp is the transition time or filled at time
2. If order is canceled through user action, the value of timestamp is the time at which vendor processed the cancelation request successfully
*/
  google.protobuf.Timestamp timestamp = 5;

  // ULID for uniquely identifying the event
  // Lexically sortable, monotonically increasing character array
  string event_ulid = 6;
}

// AccountUpdate contains updated account object updates received over SSE endpoints
message AccountUpdate {
  string account_id = 1;
  string account_number = 2;
  // time at which event was generated
  google.protobuf.Timestamp event_time = 3;
  // event_id for uniquely identifying the event
  // it is monotonically increasing 64bit integer
  int64 event_id = 4;
  // Account status before update
  AccountStatus status_from = 5;
  // Account status after update
  AccountStatus status_to = 6;
  // Optional reason text in form of string
  string reason = 7;
  // If true the pattern_day_trader flag was set for the account, if false, the flag was reset.
  bool pattern_day_trader = 8;
  // If true the account was blocked, if false, the account got unblocked
  bool account_blocked = 9;
  // If true the account cannot trade going forward, if false, the ban has been lifed
  bool trading_blocked = 10;
  // Changed administrative flags
  AdminConfigurations admin_configurations = 11;
}

message AdminConfigurations {
  // Wire-out transfers blocked for the account if false
  bool outgoing_transfers_blocked = 1;
  // Deposits are blocked for the account if false
  bool incoming_transfers_blocked = 2;
  // If true the account is not allowed to create short position orders
  bool disable_shorting = 3;
  // If true, the account cannot create orders for fractional share positions
  bool disable_fractional = 4;
  // If true, the account is not allowed to trade cryptos
  bool disable_crypto = 5;
  // If true, the account is not allowed to day trade (e.g. buy and sell the same security on th
  bool disable_day_trading = 6;
  // Max margin multipler is set by admin to this value
  int64 max_margin_multiplier = 7;
  // Override the correspondent level daily transfer limits
  int64 acct_daily_transfer_limit = 8;
  // Reasons why the liquidation only flag was set
  RestrictToLiquidationReasons restrict_to_liquidation_reasons = 9;
}

message RestrictToLiquidationReasons {
  // Set when the trading account is marked as a PDT, but its equity falls below the $25k treshold
  bool pattern_day_trading = 1;
  // Set when an incoming ACH transfer gets rejected
  bool ach_return = 2;
  // Set when the position to equity ration exceeds the maximum limit
  bool position_to_equity_ratio = 3;
  // Default value for unknown reason
  bool unspecified = 4;
}

message GetAccountUpdatesRequest {
  vendorgateway.RequestHeader header = 1;
}

message GetAccountUpdatesResponse {
  // Status of the request
  rpc.Status status = 1;

  // AccountUpdate from the vendor
  AccountUpdate account_update = 2;
}

message GetTradingAccountRequest {
  vendorgateway.RequestHeader header = 1;
  string account_id = 2;
}

message GetTradingAccountResponse {
  // Status of the request
  rpc.Status status = 1;
  TradingAccount trading_account = 2;
}

message TradingAccount {
  // account id of account at vendor's end
  string account_id = 1;
  // amount in USD user can use to purchase stocks
  // cash - (sum of amount of unfulfilled orders)
  // e.g.  if your buying power is $10,000 and you submit a limit buy order with an order value of $3,000,
  // your order will be accepted and your remaining available buying power will be $7,000.
  // Even if this order is unfilled, as long as it is open and has not been cancelled, it will count against your available buying power.
  // If you then submitted another order with an order value of $8,000, it would be rejected.
  google.type.Money buying_power = 2;
  // amount in USD user have in his trading account
  google.type.Money cash = 3;
  // amount in USD available for withdrawal
  google.type.Money withdrawable_amount = 4;
  // If user is flagged for pattern day trading or not
  // An account is designated as a Pattern Day Trader if it makes four (4) day trades within five (5) business days.
  // Day trades less than this criteria will not flag the account for PDT.
  api.typesv2.common.BooleanEnum pattern_day_trader = 6;
  // Cash + long_market_value + short_market_value in USD
  google.type.Money equity = 7;
  // equity at the end of previous day in USD
  google.type.Money last_equity = 8;
  // current number of day-trades that have been made in the last 5 trading days (inclusive of today)
  uint32 day_trade_count = 9;
  // value of all cash(in USD) as of previous trading day at 01:30:00 AM IST
  google.type.Money last_cash = 10;
}

message GetAccountActivitiesRequest {
  vendorgateway.RequestHeader header = 1;
  // represent account_id for particular user
  // depend on use case if we want to get activity related to particular user
  string account_id = 2;
  // represent order in which data is fetched
  bool in_ascending_order = 3;

  // Represent page_token for next page
  // Eg: help during getting next batch info via jenkins job
  rpc.PageContextRequest page_context = 4;

  // Note: Although both from and to are timestamps, Alpaca API responses are only differentiated on dates
  google.protobuf.Timestamp from_ts = 5;

  google.protobuf.Timestamp to_ts = 6;
}

message GetAccountActivitiesResponse {
  rpc.Status status = 1;

  // List of account activities
  repeated AccountActivity account_activities = 2;

  // Page context for fetching the next page
  rpc.PageContextResponse page_context = 3;

  // Info about the token in page context
  PageTokenInfo page_token_info = 4;
}

message AccountActivity {
  oneof activity {
    TradeActivity trade_activity = 1;
    NonTradeActivity non_trade_activity = 2;
  }
}

message GetAllAssetsRequest {
  vendorgateway.RequestHeader header = 1;
  // status of asset, response will be filtered with AssetStatus
  // if asset status is UNSPECIFIED, response will not be filtered by AssetStatus
  AssetStatus asset_status = 2;
  // class of asset, response will be filtered with AssetClass
  // if asset class is nil, response will not be filtered by asset class
  AssetClass asset_class = 3;
  // exchange on which asset is listed e.g: AMEX, ARCA, BATS. Will be filtered with AssetExchange
  // if asset exchange is nil, response will not be filtered by asset exchange
  AssetExchange asset_exchange = 4;
}

message GetAllAssetsResponse {
  rpc.Status status = 1;
  repeated Asset assets = 2;
}

message GetAssetRequest {
  vendorgateway.RequestHeader header = 1;
  oneof asset_identifier {
    // asset identifier at vendor
    string asset_id = 2;
    // asset identifier at vendor
    string asset_symbol = 3;
  }
}

message GetAssetResponse {
  rpc.Status status = 1;
  Asset asset = 2;
}


message ReverseSplit {
  google.type.Date ex_date = 1;
  double new_rate = 2;
  double old_rate = 3;
  google.type.Date payable_date = 4;
  google.type.Date process_date = 5;
  google.type.Date record_date = 6;
  string symbol = 7;
}


message ForwardSplit {
  google.type.Date due_bill_redemption_date = 1;
  google.type.Date ex_date = 2;
  double new_rate = 3;
  double old_rate = 4;
  google.type.Date payable_date = 5;
  google.type.Date process_date = 6;
  google.type.Date record_date = 7;
  string symbol = 8;
}

message UnitSplit {
  double alternate_rate = 1;
  string alternate_symbol = 2;
  google.type.Date effective_date = 3;
  double new_rate = 4;
  string new_symbol = 5;
  double old_rate = 6;
  string old_symbol = 7;
  google.type.Date process_date = 8;
  google.type.Date payable_date = 9;
}

message CashDividend {
  google.type.Date ex_date = 1;
  bool foreign = 2;
  google.type.Date payable_date = 3;
  google.type.Date process_date = 4;
  double rate = 5;
  google.type.Date record_date = 6;
  bool special = 7;
  string symbol = 8;
  google.type.Date due_bill_on_date = 9;
  google.type.Date due_bill_off_date = 10;
}

message StockDividend {
  google.type.Date ex_date = 1;
  google.type.Date payable_date = 2;
  google.type.Date process_date = 3;
  double rate = 4;
  google.type.Date record_date = 5;
  string symbol = 6;
}

message SpinOff {

  google.type.Date ex_date = 1;
  double new_rate = 2;
  string new_symbol = 3;
  google.type.Date payable_date = 4;
  google.type.Date process_date = 5;
  google.type.Date record_date = 6;
  double source_rate = 7;
  string source_symbol = 8;
  google.type.Date due_bill_redemption_date = 9;
}

message CashMerger {
  string acquiree_symbol = 1;
  google.type.Date effective_date = 2;
  google.type.Date payable_date = 3;
  google.type.Date process_date = 4;
  double rate = 5;
  string acquirer_symbol = 6;
}

message StockMerger {
  double acquiree_rate = 1;
  string acquiree_symbol = 2;
  double acquirer_rate = 3;
  string acquirer_symbol = 4;
  google.type.Date effective_date = 5;
  google.type.Date payable_date = 6;
  google.type.Date process_date = 7;
}

message StockAndCashMerger {
  double acquiree_rate = 1;
  string acquiree_symbol = 2;
  double acquirer_rate = 3;
  string acquirer_symbol = 4;
  double cash_rate = 5;
  google.type.Date effective_date = 6;
  google.type.Date payable_date = 7;
  google.type.Date process_date = 8;
}

message Redemption {
  google.type.Date payable_date = 1;
  google.type.Date process_date = 2;
  double rate = 3;
  string symbol = 4;
}

message NameChange {
  string new_symbol = 1;
  string old_symbol = 2;
  google.type.Date process_date = 3;
}

message WorthLessRemoval {
  google.type.Date process_date = 1;
  string symbol = 2;
}
message RightsDistribution {
  google.type.Date ex_date = 1;
  google.type.Date expiration_date = 2;
  string new_symbol = 3;
  google.type.Date payable_date = 4;
  google.type.Date process_date = 5;
  double rate = 6;
  google.type.Date record_date = 7;
  string source_symbol = 8;
}

message CorporateActions {
  // A decrease in the number of shares outstanding with an increase in the dollar value of each share.
  repeated ReverseSplit reverse_splits = 1;
  // An increase in the number of shares outstanding with a decrease in the dollar value of each share
  repeated ForwardSplit forward_splits = 2;
  // An increase in the number of shares outstanding with a decrease in the dollar value of each share
  repeated UnitSplit unit_splits = 3;
  // A cash payment based on the number of shares the account holds on the record date.
  repeated CashDividend cash_dividends = 4;
  // A cash payment based on the number of shares the account holds on the record date.
  repeated StockDividend stock_dividends = 5;
  // A disbursement of a newly tradable security when the intiating_symbol creates the target_symbol.
  repeated SpinOff spin_offs = 6;
  // intiating_symbol will acquire the target_symbol, the buying company buys the other company's shares with cash
  repeated CashMerger cash_mergers = 7;
  // intiating_symbol will acquire the target_symbol, the buying company buys the other company's shares with stock
  repeated StockMerger stock_mergers = 8;
  // intiating_symbol will acquire the target_symbol, the buying company buys the other company's shares with stock and cash
  repeated StockAndCashMerger stock_and_cash_mergers = 9;
  // when a company requires shareholders to sell a portion of their shares back to the company
  repeated Redemption redemptions = 10;
  // initial name changes to different name
  repeated NameChange name_changes = 11;
  // Worthless securities have a market value of zero and, along with any securities that an investor has abandoned
  repeated WorthLessRemoval worthless_removals = 12;
  // A rights issue gives preferential treatment to existing shareholders
  repeated RightsDistribution rights_distributions = 13;
}

message DeleteBankDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  // Represent Vendor AccountId
  string broker_account_id = 2 [(validate.rules).string = {min_len: 1}];
  // relationship id for an account at vendor's end and a bank account
  string bank_relationship_id = 3 [(validate.rules).string = {min_len: 1}];
}

message DeleteBankDetailsResponse {
  enum Status {
    Ok = 0;

    // No Bank Relationship with the id specified by bank_id was found for this Account
    NOT_FOUND = 101;

    // wire bank already canceled
    UNPROCESSABLE = 102;
  }
  rpc.Status status = 1;
}

