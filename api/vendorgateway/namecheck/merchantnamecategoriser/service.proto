syntax = "proto3";

package vendorgateway.namecheck.merchantnamecategoriser;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/namecheck/merchantnamecategoriser";
option java_package = "com.github.epifi.gamma.api.vendorgateway.namecheck.merchantnamecategoriser";

service MerchantNameCategoriser {
  // inhouse name categoriser API to check whether the passed name is a merchant or not
  rpc MerchantNameCategoriser (MerchantNameCategoriserRequest) returns (MerchantNameCategoriserResponse);
}

message MerchantNameCategoriserRequest {
  // contains vendor to be used
  vendorgateway.RequestHeader header = 1;

  // unique id against which each request is made
  string request_id = 2;

  // name that is to be categorised as merchant or not
  string name = 3 [(validate.rules).string.min_len = 3];
}

message MerchantNameCategoriserResponse {
  rpc.Status status = 1;

  // probability that the given name is of a merchant, values between 0 and 1
  float prob = 2;

  // final decision that if the given name is a merchant values: 0(fail), 1(pass)
  int32 decision = 3;

  // version of the model used for this prediction
  string model_version = 4;
}
