//go:generate gen_sql -types=AtlasResponseStatus
syntax = "proto3";

package vendorgateway.credit_report;


option go_package = "github.com/epifi/gamma/api/vendorgateway/credit_report";
option java_package = "com.github.epifi.gamma.api.vendorgateway.credit_report";

// PresenceStatus inferred from vendor response
enum PresenceStatus {
  REPORT_PRESENCE_STATUS_UNSPECIFIED = 0;
  // denotes credit report is present at vendor
  REPORT_PRESENCE_STATUS_PRESENT = 1;
  // denotes credit report is not present at vendor
  REPORT_PRESENCE_STATUS_NOT_PRESENT = 2;
  // denotes that report is available. The input mobile number matches with at least one in the trade lines.
  REPORT_PRESENCE_STATUS_AVAILABLE_AND_DELIVERABLE = 3;
  // denotes that mobile no is part of only credit and non credit inquiry only.(not of part of tradeline/Report).
  // Mobile mismatch - Prompt the consumer for an alternate mobile number - and check 1st API again
  REPORT_PRESENCE_STATUS_AVAILABLE_BUT_UNAUTHENTICATED = 4;
  // denotes there are no trade lines in the report
  // There could be credit enquiries or soft enquiries only - with default data or sometimes junk data could be available.
  REPORT_PRESENCE_STATUS_ONLY_CREDIT_ENQUIRY_AVAILABLE = 5;
}

enum AtlasResponseStatus {
  ATLAS_RESPONSE_STATUS_UNSPECIFIED = 0;
  ATLAS_RESPONSE_STATUS_IN_PROGRESS = 1;
  ATLAS_RESPONSE_STATUS_SUCCESS = 2;
  ATLAS_RESPONSE_STATUS_FAILURE = 3;
  ATLAS_RESPONSE_STATUS_PENDING = 4;
}

enum AtlasAuthQueue {
  ATLAS_AUTH_QUEUE_UNSPECIFIED = 0;
  // 6 numeric characters as input
  ATLAS_AUTH_QUEUE_OTP_IDM_EMAIL = 1;
  // Numeric characters max length 10
  ATLAS_AUTH_QUEUE_ALTERNATE_EMAIL_ENTRY = 2;
  // 6 numeric characters
  ATLAS_AUTH_QUEUE_IDM_ALTERNATE_EMAIL = 3;
  // Radio button selections for answers. The customer
  // has to select an answer for each question
  ATLAS_AUTH_QUEUE_IDM_KBA = 4;
}

enum AtlasFailureEnum {
  ATLAS_FAILURE_ENUM_UNSPECIFIED = 0;
  // (fulfill offer) invalid offer id sent -> permanent failure
  ATLAS_FAILURE_ENUM_INVALID_OFFER_ID = 1;
  // (fulfill offer) duplicate calls -> permanent failure
  ATLAS_FAILURE_ENUM_DUPLICATE_PARTNER_CUST_CODE = 2;
  // (fulfill offer) customer already exists with different client_user_key, use that
  ATLAS_FAILURE_ENUM_SSN_EXISTS = 3;
  // (fulfill offer) report already fetched for this customer -> permanent failure
  ATLAS_FAILURE_ENUM_SSN_EXISTS_ACTIVE_ONLINE = 4;
  // (fulfill offer) customer blacklisted -> permanent failure
  ATLAS_FAILURE_ENUM_BLACKLISTED_CUSTOMER = 5;
  // (all APIs) customer blacklisted / auth failed -> permanent failure
  ATLAS_FAILURE_ENUM_FAILURE = 6;
  // (fulfill offer) customer failed authentication -> failure, show IV failed message to user
  ATLAS_FAILURE_ENUM_FATAL = 7;
  // (fulfill offer) dob in wrong format -> permanent failure
  ATLAS_FAILURE_ENUM_AGE_RESTRICTION = 8;
  // (fulfill offer, get customer assets) report not found for user -> permanent failure
  ATLAS_FAILURE_ENUM_NO_HIT = 9;
  // (all APIs) generic service error and validation errors (with message) in case of fulfill offer.
  // consumer service error object. In failure response object -> permanent failure
  ATLAS_FAILURE_ENUM_SERVICE_ERROR = 10;
  // (fulfill offer) different customer code used in journey -> permanent failure
  ATLAS_FAILURE_ENUM_PARTNER_CUST_CODE_NOT_FOUND = 11;
  // (fulfill offer) in case "Accept" is not sent -> permanent failure
  ATLAS_FAILURE_ENUM_LEGAL_COPY_NOT_FOUND = 12;
  // (fulfill offer) in case Aadhar is sent as customer identifier -> permanent failure
  ATLAS_FAILURE_ENUM_INVALID_AUTH_DATA = 13;
  // (fulfill offer) in case PII is different from cibil system -> permanent failure
  ATLAS_FAILURE_ENUM_SSN_MISMATCH = 14;
  // (submit auth answer) in case no more questions can be sent back in authentication
  ATLAS_FAILURE_ENUM_NO_QUESTIONS_REMAINING = 15;
}

enum AtlasErrorStatus {
  ATLAS_ERROR_STATUS_UNSPECIFIED = 0;
  // cibil internal failure -> transient
  ATLAS_ERROR_STATUS_INTERNAL = 1;
  // validation error will require a bug fix -> permanent failure
  ATLAS_ERROR_STATUS_VALIDATION = 2;
  // cibil internal failure -> transient
  ATLAS_ERROR_STATUS_VENDOR_UNAVAILABLE = 3;
  // cibil internal failure -> transient
  ATLAS_ERROR_STATUS_VENDOR_TIMEOUT = 4;
  // cibil internal failure -> transient
  ATLAS_ERROR_STATUS_INVALID_VENDOR_REQUEST = 5;
}
