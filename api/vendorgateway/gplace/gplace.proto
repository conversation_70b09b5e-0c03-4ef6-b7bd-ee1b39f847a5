syntax = "proto3";

package vendorgateway.gplace;

option go_package = "github.com/epifi/gamma/api/vendorgateway/gplace";
option java_package = "com.github.epifi.gamma.api.vendorgateway.gplace";

import "google/type/latlng.proto";

message IpBiasLocationIdentifier {
  string ip_bias = 1;
}

message CircleLocationIdentifier {
  double radius = 1;
  google.type.LatLng centre = 2;
}

message RectangleLocationIdentifier {
  google.type.LatLng south_west = 1;
  google.type.LatLng north_east = 2;
}

message PointIdentifier {
  google.type.LatLng point = 1;
}

message LocationBias {
  oneof LocationBiasIdentifier {
    IpBiasLocationIdentifier ip_bias_location_identifier = 1;
    PointIdentifier point_identifier = 2;
    CircleLocationIdentifier circle_location_identifier = 3;
    RectangleLocationIdentifier rectangle_location_identifier = 4;
  }
}

// Geometry object describes the location of place returned by gplace (can be visualized in google maps).
message Geometry {
  // location is point inside the place
  PointIdentifier location = 1;
  // viewport is 2 diagonal points of rectangle enclosing the place
  RectangleLocationIdentifier ViewPort = 2;
}
