// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway;

option go_package = "github.com/epifi/gamma/api/vendorgateway";
option java_package = "com.github.epifi.gamma.api.vendorgateway";


// CircuitCommand is curated list of unique circuit breaker commands
// in order to avoid collisions please follow below naming convention
// `<grpc_package_path_relative_to_vg>_<grpc_service_name>_<api_name>`
// e.g. an api in upi service named CheckTxnStatus will have command name
// 		OPENBANKING_UPI_UPI_CHECK_TXN_STATUS
//    since, the package path for the API is openbanking.upi.Upi
enum CircuitCommand {
  CIRCUIT_COMMAND_UNSPECIFIED = 0;

  OPENBANKING_UPI_UPI_CHECK_TXN_STATUS = 1;

  OPENBANKING_SAVINGS_SAVINGS_GET_OPENING_BALANCE = 2;

  OPENBANKING_ACCOUNTS_ACCOUNTS_GET_ACCOUNT_STATEMENT = 3;

  OPENBANKING_SAVINGS_SAVINGS_GET_BALANCE = 4;

  OPENBANKING_UPI_UPI_VALIDATE_ADDRESS = 5;

  NAMECHECK_NAMECHECK_UN_NAME_CHECK = 6;

  OPENBANKING_PAYMENT_PAYMENT_PAY_DEPOSIT_ADD_FUNDS = 7;

  PAN_PAN_VALIDATE = 8;

  EKYC_EKYC_NAME_DOB_VALIDATION_FOR_EKYC = 9;

  OPENBANKING_CUSTOMER_CUSTOMER_DEDUPE_CHECK = 10;

  CX_FRESHDESK_FRESHDESK_GET_TICKET_BY_TICKET_ID = 11;

  CX_FRESHDESK_FRESHDESK_GET_ALL_TICKETS = 12;

  CX_FRESHDESK_FRESHDESK_UPDATE_TICKET = 13;

  CX_FRESHDESK_FRESHDESK_GET_CONTACTS = 14;

  CX_FRESHDESK_FRESHDESK_CREATE_TICKET = 15;

  CX_FRESHDESK_FRESHDESK_GET_AGENT = 16;

  CX_FRESHDESK_FRESHDESK_ADD_PRIVATE_NOTE_IN_TICKET = 17;

  CX_FRESHDESK_FRESHDESK_FETCH_TICKET_CONVERSATIONS = 18;

  CX_FRESHDESK_FRESHDESK_UPDATE_TICKET_RAW = 19;

  CX_FRESHDESK_FRESHDESK_GET_TICKET_FIELD = 20;
}
