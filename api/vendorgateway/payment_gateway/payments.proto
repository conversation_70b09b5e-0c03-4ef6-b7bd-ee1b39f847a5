syntax = "proto3";

package vendorgateway.pg;

import "api/typesv2/card_type.proto";
import "api/typesv2/money.proto";
import "api/vendorgateway/payment_gateway/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/pg";
option java_package = "com.github.epifi.gamma.api.vendorgateway.pg";

// PayerContactInfo stores the email/phone info entered by the payer
message PayerContactInfo {
  // email address used for the payment
  string email = 1;
  // contact number used for the payment
  string number = 2;
}

// PaymentFee stores charges for payment facilitation
message PaymentFee {
  api.typesv2.Money fee_and_taxes = 1;
  api.typesv2.Money taxes = 2;
}

// PaymentFailureDetails stores the vendor error code and error description
message PaymentFailureDetails {
  string error_code = 1;
  string error_description = 2;
  // The exact error reason. Can be handled programmatically. Ex: "insufficient_funds", "payment_cancelled", etc.
  string error_reason = 3;
}

// AcquirerDetails are reference numbers provided by payment service facilitators like banks, RuPay etc. for the
// payment transfer
message AcquirerDetails {
  // A unique bank reference number provided by the banking partner when a refund is processed. This reference
  // number can be used by the customer to track the status of the refund with the bank.
  string rrn = 1;
  // A unique reference number generated for RuPay card payments.
  string authentication_reference_number = 2;
  // A unique reference number provided by the banking partner in case of netbanking payments.
  string bank_transaction_id = 3;
}

// UpiPaymentDetails like type, vpa transaction id
message UpiPaymentDetails {
  // mode of UPI payment: account/ CC/ wallet
  UPIPaymentType upi_payment_type = 1;
  // VPA (Virtual Payment Address) or UPI id used to make the payment
  string vpa = 2;
  string upi_transaction_id = 3;
}

// CardPaymentDetails stores card id used in transaction and if it was internation card
message CardPaymentDetails {
  // unique identifier of the card used to make the payment
  string card_id = 1;
  // Indicates whether the payment is done via an international card.
  bool international = 2;
  // This is a 4-character code denoting the issuing bank. For example, KARB.
  //This attribute will not be set for international cards, that is, for cards issued by foreign banks.
  string card_issuer_code = 3;
  // The card type used to process the payment
  api.typesv2.CardType card_type = 4;
  // network provider of the card using which the payment was done
  CardNetworkType card_network_type = 5;
}

// WalletPaymentDetails stores the wallet name which was used for payment
message WalletPaymentDetails {
  string wallet_name = 1;
}

// payment refund details
message RefundDetails {
  api.typesv2.Money amount_refunded = 1;
  RefundStatus refund_status = 2;
  // vendor side identifier of the refund payment that has been done for an original
  // payment
  string vendor_refund_id = 3;
  // internal id to identify refunds. This will help to check if a
  // refund has already been created for an internal id before
  // refund creation
  string internal_refund_id = 4;
  // status of the refund process on the vendor's end
  RefundProcessStatus refund_process_status = 5;
  // speed at which the refund is being processed/was processed
  RefundSpeed output_refund_speed = 6;
  // time at which refund was created on vendor's end.
  google.protobuf.Timestamp refund_creation_time = 7;
  // UTR of the refund transaction
  string refund_utr = 8;
}

// Payment message is used for handling the responses of:
// - Payment creation API (Returns payment entity)
// - Settlements recon API (Returns one of payment, refund, transfer, adjustment entities, refer docs https://razorpay.com/docs/api/settlements/fetch-recon/)
// - Refunds API (Returns a refund entity, created on vendor side)
//
// So this single message type is used to represent multiple different entities created on vendor side.
// the payment_type field in the Payment message represents the vendor entity type, as documented below:
// 1. PAYMENT_TYPE_P2M: payment
// 2. PAYMENT_TYPE_P2P: transfer
// 3. PAYMENT_TYPE_REFUND: refund
// 4. PAYMENT_TYPE_ADJUSTMENT/PAYMENT_TYPE_NEGATIVE_ADJUSTMENT: adjustment
//
// Based on the payment_type, different fields of the payment message are populated as documented below:
//
// 1. For PAYMENT_TYPE_P2M, the top level fields consist of the details regarding the user initiated payment.
//
// 2. For PAYMENT_TYPE_P2P, the top level fields contain details of the user initiated payment
// (populated on a best-effort basis, based on the vendor API response), that is associated with
// the transfer entity. Additional details regarding the transfer entity is contained in the fund_transfer_details field.
//
// 3. For PAYMENT_TYPE_REFUND, the top level fields contain details of the user initiated payment
// (populated on a best-effort basis, based on the vendor API response), that is associated with
// the refund entity. Additional details regarding the refund entity is contained in the refund_details field.
//
// 4. For PAYMENT_TYPE_ADJUSTMENT & PAYMENT_TYPE_NEGATIVE_ADJUSTMENT, the top level fields contain the details regarding
// the adjustment entity, since adjustments are not associated with any user initiated payment.
message Payment {
  // Identifier for payment as provided by vendor in the cases of response of payments API (PaymentType P2M).
  // For PaymentType_ADJUSTMENT/NEGATIVE_ADJUSTMENT it stores the adjustment payment entity's id.
  // For PaymentType_REFUND/PaymentType_P2P, it denotes the original user initiated payment
  // for which the refund/transfer is being made.
  string vendor_payment_id = 1;
  // vendor order identifier for which payment is intended
  string vendor_order_id = 2;
  // Amount of payment
  api.typesv2.Money amount = 3;
  // Method of payment
  PaymentMethod method = 4;
  // Fee charged for payment facilitation
  PaymentFee payment_fee = 5;
  // Status of payment as reported by vendor
  VendorPaymentStatus status = 6;
  // Boolean flag if payment is successfully captured by vendor
  // is_payment_captured_by_vendor flag helps identify the cause of a Refunded payment.
  // If the flag is false, the refund is due to failure at capture/settlement.
  bool is_payment_captured_by_vendor = 7;
  // bank's name whose account was used for payment
  string bank = 8;
  // This is populated only for Refund Payment types.
  RefundDetails refund_details = 9;
  // AcquirerDetails are reference numbers provided by payment service facilitators like banks, RuPay etc. for the
  // payment transfer
  AcquirerDetails acquirer_details = 10;
  // email and phone contact details entered by payer
  PayerContactInfo payer_contact_info = 11;
  // payment details for upi payment
  UpiPaymentDetails upi_payment_details = 12;
  // payment details for card payment
  CardPaymentDetails card_payment_details = 13;
  // payment details for wallet payment
  WalletPaymentDetails wallet_payment_details = 14;
  // details for failed payment
  PaymentFailureDetails payment_failure_details = 15;
  // time when payment was created at vendor's end
  google.protobuf.Timestamp created_at = 16;
  // the type of payment that is being recorded. It will help to determine if
  // its a direct p2m, refund, p2p, etc.
  PaymentType payment_type = 17;
  // information specific to the settlement of the payment. A settlement represents the final transfer of money
  // from the Intermediate PG Account to the Destination Pool Account. This field is populated when fetching the
  // settlement details associated with the payment.
  PaymentSettlementDetails payment_settlement_details = 18;
  // identifier of dispute, if any raised for that payment
  string dispute_id = 19;
  // any notes or text to identify about the payment reasons, beneficiary details, etc. provided by vendor
  string payment_description = 20;
  // this is populated only for PaymentType_P2P and contains the vendor side details related to the fund transfer.
  // A fund transfer can represent the money transfer between two entities within the paymentgateway.
  FundTransferDetails fund_transfer_details = 21;
}

message FundTransferDetails {
  // The vendor side id corresponding to the fund transfer entity.
  string transfer_id = 1;
  // The amount transferred
  api.typesv2.Money amount_transferred = 2;
}

// TransactionSettlementInfo will contain txn settlement specific details for a txn
message PaymentSettlementDetails {
  // Indicates whether the payment has been settled or not.
  bool settled = 1;
  // Indicates whether the account settlement for transfer is on hold.
  bool on_hold = 2;
  // The unique identifier of the settlement transaction.
  string settlement_id = 3;
  // The unique reference number linked to the settlement. For example, KKBKH14156891582.
  string settlement_utr = 4;
  // Unix timestamp when the transaction was settled.
  google.protobuf.Timestamp settled_at = 5;
}
