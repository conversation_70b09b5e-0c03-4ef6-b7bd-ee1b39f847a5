// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.moengage;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendors/moengage/user.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/moengage";
option java_package = "com.github.epifi.gamma.api.vendorgateway.moengage";

service MoEngage {
  // This API facilitates the retrieval of information of users by specifying the user ids.
  rpc GetUser (GetUserRequest) returns (GetUserResponse);
  // The Merge User API merges two users in MoEngage based on their ID.
  // ID is a client-defined identifier for a user.
  // This API can be used when multiple profiles have been created for a single user.
  // For example, a user registered once with a mobile number and once with an email id can be merged.
  rpc MergeUser (MergeUserRequest) returns (MergeUserResponse);
  // This API can be used to delete users in MoEngage. Users cannot be retrieved once deleted.
  // Users deleted (hard delete) using this API will be deleted after a default buffer of 24 hours.
  // During this buffer period, the user will still be active in MoEngage and will be visible in Segments, Analytics, and Campaigns.
  // Users can be updated in the buffer period. After the buffer elapses, the user is hard-deleted from MoEngage.
  // If a user with the same unique identifiers as the deleted one is created in MoEngage, they will be created again in MoEngage.
  rpc DeleteUser (DeleteUserRequest) returns (DeleteUserResponse);
}

message MergeUserRequest {
  vendorgateway.RequestHeader header = 1;
  // This field contains the list of UID pairs of the users who are to be merged
  repeated vendors.moengage.MergeUserRequest.MergeData merge_data = 2;
}

message MergeUserResponse {
  rpc.Status status = 1;
}

message GetUserRequest {
  vendorgateway.RequestHeader header = 1;
  // This field is used to specify the identifiers for the users for whom the data needs to be fetched.
  vendors.moengage.GetUserRequest.Data data = 2;
  // This field is used to specify the fields that need to be fetched for the user specified in Identifiers.
  repeated string user_fields_to_export = 3;
}

message GetUserResponse {
  rpc.Status status = 1;
  // This field contains the list of users -
  // 1. who were not found in MoEngage
  // 2. who were found in MoEngage.
  vendors.moengage.GetUserResponse.Data data = 2;
}

message DeleteUserRequest {
  vendorgateway.RequestHeader header = 1;
  // Allowed Values: moengage_id (the MoEngageID of the user in the User Profile), customer_id (ID field in the User Profile)
  string identity_type = 2;
  // This field specifies the unique identifier that identifies the user.
  string identity_value = 3;
}

message DeleteUserResponse {
  rpc.Status status = 1;
  // This field contains information about whether the response is being processed, the user is deleted, and so on.
  string message = 2;
}
