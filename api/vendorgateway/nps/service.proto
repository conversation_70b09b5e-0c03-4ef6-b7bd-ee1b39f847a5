syntax = "proto3";

package vendorgateway.nps;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "api/typesv2/money.proto";
import "api/typesv2/date.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/nps";
option java_package = "com.github.epifi.gamma.api.vendorgateway.nps";

// Service to handle NPS related operations through vendor gateway APIs
service NPS {
  // FetchNAVData downloads and processes NAV data from NSDL
  // Returns an array of schemes with their NAV details
  rpc FetchNAVData(FetchNAVDataRequest) returns (FetchNAVDataResponse);
}

// Scheme represents an NPS scheme with its NAV details
message SchemeDayNAVInfo {
  api.typesv2.Date nav_date = 6;
  string pfm_id = 1;
  string pfm_name = 2;
  string scheme_id = 3;
  string scheme_name = 4;
  api.typesv2.Money nav = 5;

}

message FetchNAVDataRequest {
  vendorgateway.RequestHeader header = 1;
  // Date for which NAV data needs to be fetched (format: DDMMYYYY)
  api.typesv2.Date date = 2;
}

message FetchNAVDataResponse {
  rpc.Status status = 1;
  // List of schemes with their NAV details
  repeated SchemeDayNAVInfo scheme_day_nav_infos = 2;
}
