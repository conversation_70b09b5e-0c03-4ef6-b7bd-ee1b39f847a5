// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.aa;

import "api/rpc/status.proto";
import "api/vendorgateway/aa/aa.proto";
import "api/vendorgateway/aa/consent.proto";
import "api/vendorgateway/aa/data.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendors/aa/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/aa";
option java_package = "com.github.epifi.gamma.api.vendorgateway.aa";

// The AccountAggregator Service is the medium between our backend and AA vendor. It facilitates:
//   * Post Consent to user/AA for access to data.
//   * Check Consent Status to fetch the data.
//   * Request Data to get session IDs to fetch the data
//   * Fetch Data to fetch Financial data of the user
//
service AccountAggregator {
  // Post Consent to user/AA for access to data.
  rpc RequestConsent (RequestConsentRequest) returns (RequestConsentResponse);

  // Check Consent Handle to check if the consent is ready.
  rpc GetConsentStatus (GetConsentStatusRequest) returns (GetConsentStatusResponse);

  // Check Consent Status to fetch the data.
  rpc GetConsentArtefact (GetConsentArtefactRequest) returns (GetConsentArtefactResponse);

  // Request Data to get session IDs to fetch the data
  rpc RequestData (RequestDataRequest) returns (RequestDataResponse);

  // Fetch Data to fetch Financial data of the user
  rpc FetchData (FetchDataRequest) returns (FetchDataResponse);

  // Generate access token for all AA APIs including central registry
  // Client id and client secret are present in VG secret manager
  // other details are fixed and hence not passed in request while generating token
  rpc GenerateAccessToken (GenerateAccessTokenRequest) returns (GenerateAccessTokenResponse);

  // Fetch entity details from central registry
  rpc FetchCrEntityDetails (FetchCrEntityDetailsRequest) returns (FetchCrEntityDetailsResponse);

  rpc GetAccountLinkStatus (GetAccountLinkStatusRequest) returns (GetAccountLinkStatusResponse);

  // Internal RPC to update consent state in simulator in non prod only
  rpc UpdateConsentStatus (UpdateConsentStatusRequest) returns (UpdateConsentStatusResponse);

  // Internal RPC to delink account in simulator in non prod only
  rpc DeLinkAccount (DeLinkAccountRequest) returns (DeLinkAccountResponse);

  // RPC to Get Account Link Status for Finvu. Accepts link reference ids in multiples.
  rpc GetAccountLinkStatusBulk (GetAccountLinkStatusBulkRequest) returns (GetAccountLinkStatusBulkResponse);

  // RPC to get hearbeat status of AA
  rpc GetHeartbeatStatus (GetHeartbeatStatusRequest) returns (GetHeartbeatStatusResponse);

  // RPC to get consent status for bulk consent ids
  rpc GetBulkConsentStatus (GetBulkConsentStatusRequest) returns (GetBulkConsentStatusResponse);

  // RPC to generate jwt token for Finvu client binding for resilient connections
  rpc GenerateFinvuJwtToken (GenerateFinvuJwtTokenRequest) returns (GenerateFinvuJwtTokenResponse);

  // RPC to fetch fip metrics given by finvu AA
  rpc GetFipMetrics (GetFipMetricsRequest) returns (GetFipMetricsResponse);
}

message RequestConsentRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // The unique transaction identifier used for providing an end to end traceability.
  string txn_id = 2;
  vendorgateway.aa.ConsentDetail consent_detail = 3;
  vendorgateway.aa.AaEntity aa_entity = 4;
}

message RequestConsentResponse {
  rpc.Status status = 1;
  // A Unique ID generated by AA’s to handle the consent request.
  string consent_handle = 2;
}

message GetConsentStatusRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // A Unique ID generated by AA’s to handle the consent request.
  string consent_handle = 2;
  vendorgateway.aa.AaEntity aa_entity = 3;
  // The unique transaction identifier used for providing an end to end traceability.
  string txn_id = 4;
}

message GetConsentStatusResponse {
  rpc.Status status = 1;
  // Optional
  string consent_id = 2;
  // mandatory
  ConsentHandleStatus consent_handle_status = 3;
}

message GetConsentArtefactRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  string consent_id = 2;
  vendorgateway.aa.AaEntity aa_entity = 3;
}

message GetConsentArtefactResponse {
  rpc.Status status = 1;
  ConsentStatus consent_status = 2;
  google.protobuf.Timestamp create_timestamp = 3;
  ConsentUse consent_use = 4;
  ConsentDetail consent_detail = 5;
  string consent_detail_digital_signature = 6;
}

message RequestDataRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  // The unique transaction identifier used for providing an end to end traceability.
  string txn_id = 2;
  FIDataRange fi_data_range = 3;
  string consent_id = 4;
  string digital_signature = 5;
  KeyMaterial key_material = 6;
  vendorgateway.aa.AaEntity aa_entity = 7;
}

message RequestDataResponse {
  rpc.Status status = 1;
  string session_id = 2;
}

message FetchDataRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  string session_id = 2;
  vendorgateway.aa.AaEntity aa_entity = 3;
  // Backend can pass fip id to fetch data for a particular fip in consent
	// OPTIONAL
  string fip_id = 4;
  // If backend passes list of link reference number, Vendor gateway api will return data for only those accounts
	// OPTIONAL
  repeated string link_ref_number = 5;
}

message FetchDataResponse {
  rpc.Status status = 1;
  repeated FI fi_list = 2;
}

message GenerateAccessTokenRequest {
  vendorgateway.RequestHeader header = 1;
  vendors.aa.RebitApiVersion api_version = 2;
}

message GenerateAccessTokenResponse {
  rpc.Status status = 1;
  // access token to be used in central registry apis
  string access_token = 2;
  // expiry time of token
  int64 expires_in = 3;
  int64 refresh_expires_in = 4;
  string id_token = 5;
  int64 not_before_policy = 6;
  string scope = 7;
}

message FetchCrEntityDetailsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  string identity_token = 2;
  // entity id of the AA for which details are to be fetched
  string entity_id = 3;
  vendors.aa.RebitApiVersion api_version = 4;
}

message FetchCrEntityDetailsResponse {
  rpc.Status status = 1;
  string ver = 2;
  string timestamp = 3;
  // unique transaction id of the request
  string txn_id = 4;
  // information of the requester
  Requester requester = 5;
  // information of the entity
  EntityInfo entity_info = 6;
}

message GetAccountLinkStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string link_reference_number = 2;
  vendorgateway.aa.AaEntity aa_entity = 3;
  string txn_id = 4;
}

message GetAccountLinkStatusResponse {
  rpc.Status status = 1;
  // Link status of the account with AA
  AccountLinkStatus account_link_status = 2;
  // VUA of the customer
  string vua = 3;
}

message UpdateConsentStatusRequest {
  vendorgateway.RequestHeader header = 1;
  string consent_handle = 2;
  string consent_status = 3;
  vendorgateway.aa.AaEntity aa_entity = 4;
}

message UpdateConsentStatusResponse {
  rpc.Status status = 1;
}

message DeLinkAccountRequest {
  vendorgateway.RequestHeader header = 1;
  string link_reference_number = 2;
  vendorgateway.aa.AaEntity aa_entity = 3;
}

message DeLinkAccountResponse {
  rpc.Status status = 1;
}

message Requester {
  string name = 1;
  string id = 2;
}

message EntityInfo {
  // name of entity in CR
  string name = 1;
  // unique id of entity in CR
  string id = 2;
  string code = 3;
  // handle of entity in CR
  string entity_handle = 4;
  repeated Identifier identifiers = 5;
  string base_url = 6;
  string webview_url = 7;
  // fi types supported
  repeated string fi_types = 8;
  // certificate info
  Certificate certificate = 9;
  TokenInfo token_info = 10;
  string gsp = 11;
  Signature signature = 12;
  repeated string inbound_ports = 13;
  repeated string outbound_ports = 14;
  repeated string ips = 15;
  string credentialsPk = 16;
}

message Identifier {
  string category = 1;
  string type = 2;
}

message Certificate {
  string alg = 1;
  string e = 2;
  string kid = 3;
  string kty = 4;
  string n = 5;
  string use = 6;
}
message TokenInfo {
  string url = 1;
  int64 maxcalls = 2;
  string desc = 3;
}

message Signature {
  string sign_value = 1;
}

message GetAccountLinkStatusBulkRequest {
  vendorgateway.RequestHeader header = 1;
  string txn_id = 2;
  string customer_vua = 3;
  repeated FipLinkedAccounts fip_linked_accounts = 4;
  vendorgateway.aa.AaEntity aa_entity = 5;
}

message FipLinkedAccounts {
  string fip_id = 1;
  repeated string link_ref_numbers = 2;
}

message GetAccountLinkStatusBulkResponse {
  rpc.Status status = 1;
  // Link status of the account with AA
  repeated AccountLinkStatusBulk account_link_status_bulk = 2;
  // VUA of the customer
  string customer_vua = 3;
}

message AccountLinkStatusBulk {
  string fip_id = 1;
  // link statuses under a single fip
  repeated FipLinkStatus fip_link_status = 2;
}

message FipLinkStatus {
  string link_ref_number = 1;
  AccountLinkStatus account_link_status = 2;
}

message GetHeartbeatStatusRequest {
  vendorgateway.RequestHeader header = 1;
  // Aa Entity to check for heartbeat
  vendorgateway.aa.AaEntity aa_entity = 2;
}

message GetHeartbeatStatusResponse {
  // sahamati server status
  rpc.Status status = 1;
  enum AaStatus {
    UNSPECIFIED = 0;
    // UP if AaEntity status is UP
    UP = 1;
    // DOWN if AaEntity status is DOWN
    DOWN = 2;
  }
  // Aa entity server status
  AaStatus aa_status = 2;
  // Error if status is DOWN
  Error error = 3;
}

// gonna get from vendor response
message Error {
  int32 code = 1;
  string msg = 2;
  string detail = 3;
}

message GetBulkConsentStatusRequest {
  vendorgateway.RequestHeader header = 1;
  // The version of the API
  string ver = 2;
  // Creation timestamp of the message which will be updated at each leg
  string timestamp = 3;
  // The unique transaction identifier used for providing an end to end traceability.
  string txnId = 4;
  repeated string consent_id_list = 5;
  vendorgateway.aa.AaEntity aa_entity = 6;
}

message GetBulkConsentStatusResponse {
  rpc.Status status = 1;
  // The version of the API
  string ver = 2;
  // The unique transaction identifier used for providing an end to end traceability.
  string txnid = 3;
  repeated ConsentStatusList consent_status_list = 4;
}

message ConsentStatusList {
  // consent id
  string consent_id = 1;
  // consent usage details
  ConsentUse consent_use = 2;
  // consent status
  ConsentStatusListStatus status = 3 [deprecated = true];
  enum ConsentStatusListStatus {
    CONSENT_STATUS_LIST_STATUS_UNSPECIFIED = 0;
    CONSENT_STATUS_LIST_STATUS_ACTIVE = 1;
    CONSENT_STATUS_LIST_STATUS_REVOKED = 2;
    CONSENT_STATUS_LIST_STATUS_EXPIRED = 3;
  }
  // error code and error message if any
  string error_code = 4;
  string error_msg = 5;
  ConsentStatus consent_status = 6;
}

message GenerateFinvuJwtTokenRequest {
  vendorgateway.RequestHeader header = 1;
  vendorgateway.aa.AaEntity aa_entity = 2;
}

message GenerateFinvuJwtTokenResponse {
  rpc.Status status = 1;
  // jwt token
  string token = 2;
}

message GetFipMetricsRequest {
  vendorgateway.RequestHeader header = 1;
  vendorgateway.aa.AaEntity aa_entity = 2;
}

message GetFipMetricsResponse {
  rpc.Status status = 1;
  google.protobuf.Timestamp timestamp = 2;
  repeated FipMetric fip_metric_list = 3;
}
