// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.offers.loylty;


option go_package = "github.com/epifi/gamma/api/vendorgateway/offers/loylty";
option java_package = "com.github.epifi.gamma.api.vendorgateway.offers.loylty";


message LoyltyProductListResponse {
  int32 code = 1 [json_name = "Code"];
  string message = 2 [json_name = "Message"];
  bool success = 3 [json_name = "Success"];

  message Data {
    string id = 1 [json_name = "Id"];
    string sku = 2 [json_name = "Sku"];
    string name = 3  [json_name = "Name"];
  }
  repeated Data data = 4 [json_name = "Data"];
}

message LoyltyEGVProductDetailResponse {
  int32 code = 1 [json_name = "Code"];
  string message = 2 [json_name = "Message"];
  bool success = 3 [json_name = "Success"];

  message Data {
    string id = 1 [json_name = "Id"];
    string sku = 2 [json_name = "Sku"];
    string name = 3  [json_name = "Name"];
    string description = 4 [json_name = "Description"];
    string tncMobile = 5 [json_name = "TncMobile"];
    string tncWeb = 6 [json_name = "TnWeb"];
    string tncMail = 7 [json_name = "TncMail"];

    message Image {
      string url = 1 [json_name = "Url"];
      string type = 2 [json_name = "Type"];
    }
    repeated Image images = 8 [json_name = "Images"];

  }
  Data data = 4 [json_name = "Data"];
}

