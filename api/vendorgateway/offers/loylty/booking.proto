// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.offers.loylty;

import "api/vendorgateway/offers/loylty/address.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/offers/loylty";
option java_package = "com.github.epifi.gamma.api.vendorgateway.offers.loylty";

// LoyltyBookingRequest denotes a request for creating an egv booking for a product in Loylty's system.
message LoyltyEGVBookingRequest {
  // identifier for the product that has to be booked
  string product_id = 1 [json_name = "ProductId"];
  // value of EGV eg Rs 500 Flipkart EGV.
  int32 amount = 2 [json_name = "Amount"];
  int32 quantity = 3 [json_name = "Quantity"];

  // dummy values to be passed in following fields
  // mandatory for loyalty but not required by epiFi
  string message = 4 [json_name = "Message"];
  string theme = 5 [json_name = "Theme"];
  Address billing_address = 6 [json_name = "BillingAddress"];
  Address shipping_address = 7 [json_name = "ShippingAddress"];
}

// LoyltyBookingResponse denotes response by <PERSON><PERSON><PERSON> to an egv booking request.
message LoyltyEGVBookingResponse {
    int32 code = 1 [json_name = "Code"];
    string message = 2 [json_name = "Message"];
    bool success = 3 [json_name = "Success"];
    string booking_id = 4 [json_name = "Data"];
}

/*
  Sample Request
{
 "TotalAmount": 300,
 "UserId": "1603438246359",
 "Source": "Portal",
 "ReferenceNo": "",
 "BookingParameters": {
  "ProductName": "Save our Animals",
  "CharityCode": "GI",
  "Category": "Environment",
  "CharityName": "Give India"
 }
}
 */

message LoyltyCharityBookingRequest {
  string userId = 1 [json_name = "UserId"];
  int32 charity_amount = 2 [json_name = "TotalAmount"];
  // todo (utkarsh) : what should we provide here ?
  string source = 3 [json_name = "Source"];
  string reference_no = 4 [json_name = "ReferenceNo"];

  message BookingParameters {
    string product_name = 1 [json_name = "ProductName"];
    string charity_code = 2 [json_name = "CharityCode"];
    string category = 3 [json_name = "Category"];
    string charity_name = 4 [json_name = "CharityName"];
  }
  BookingParameters booking_parameters = 5 [json_name = "BookingParameters"];
}

/*
Sample Response
{
  "Code": 200,
  "Message": "",
  "Success": true,
  "Data": {
    "Message": "Your request is successful.",
    "RequestId": "4f9ec511-7367-4d9b-9b3f-cd7775f0e238",
    "RequestNo": 24473,
    "StatusFlag": true
  },
}
 */
message LoyltyCharityBookingResponse {
  int32 code = 1 [json_name = "Code"];
  bool success = 2 [json_name = "Success"];
  string message = 3 [json_name = "Message"];
  message Data {
    string message = 1 [json_name = "Message"];
    string request_id = 2 [json_name = "RequestId"];
    int32 request_no = 3 [json_name = "RequestNo"];
    bool status_flag = 4 [json_name = "StatusFlag"];
  }
  Data data = 4 [json_name = "Data"];
}


