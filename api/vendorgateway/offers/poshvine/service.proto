// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.offers.poshvine;

import "api/rpc/status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/request_header.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/offers/poshvine";
option java_package = "com.github.epifi.gamma.api.vendorgateway.offers.poshvine";

service Poshvine {
  // UserRegistration rpc is used for create a Fi User on PoshVine platform.
  // If already exists returns the sso validation token with redirection url.
  // Except CardAttributes, if we pass any updated user details, it will update in poshVine system as well.
  rpc UserRegistration(UserRegistrationRequest) returns (UserRegistrationResponse);

  // UpdateUserDetails rpc is used for update user's details on PoshVine platform.
  // Will call PoshVine's Update API each time to update each card attributes passed in request
  // as PoshVine's Update API support only update of one card attribute at a time.
  rpc UpdateUserDetails(UpdateUserDetailsRequest) returns (UpdateUserDetailsResponse);
}

message UserRegistrationRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  /* mandatory details fields for creating a user on poshVine platform. */
  // vendor mapping id of the user
  string vendor_user_id = 2;
  // name of the user
  api.typesv2.common.Name name = 3;
  // phone number of the user
  api.typesv2.common.PhoneNumber phone_number = 4;
  // email of the user
  string email = 5;
  // Optional card details of user.
  CardAttributes card_attributes = 6;
}

message UserRegistrationResponse {
  // rpc status
  rpc.Status status = 1;
  // redirection url with token returned by poshvine api
  string redirection_url = 2;
}

// CardAttributes are optional fields.
// only needed if we are performing experimentation on cards or show any card specific offers.
message CardAttributes {
  // bin type format eg: first4_last4
  string bin_type = 1;
  // bin value eg: '4015_1234'
  string bin = 2;
  // unique card ID.
  string unique_external_id = 3;
  // Unique Card Segment Name
  CardSegment segment = 4;
}

message UpdateUserDetailsRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // name of the user
  api.typesv2.common.Name name = 2;
  // phone number of the user
  api.typesv2.common.PhoneNumber phone_number = 3;
  // email of the user
  string email = 4;
  // array of card attributes details of user.
  // will be calling PoshVine's Update API for each card attribute details
  // as Update API is supported with updating one card attribute at a time.
  repeated CardAttributes card_attributes = 5;
  // vendor mapping id of the user
  string vendor_user_id = 6;
}

message UpdateUserDetailsPayloadRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // name of the user
  api.typesv2.common.Name name = 2;
  // phone number of the user
  api.typesv2.common.PhoneNumber phone_number = 3;
  // email of the user
  string email = 4;
  // type of bin - first6/first8/first4_last4
  string bin_type = 9;
  // bin value - First 4 digits and Last 4 digits  or First8 of the card (ex: 4321_1234, 43211234)
  string bin = 5;
  // Unique Card Segment Name
  CardSegment segment = 6;

  /* mandatory details fields for updating a user on poshVine platform. */
  // unique card ID.
  string card_id = 7;
  // vendor mapping id of the user
  string vendor_user_id = 8;
}

message UpdateUserDetailsResponse {
  // rpc status
  rpc.Status status = 1;
  // list of errors eg: ['Access Forbidden', 'invalid mobile number'] etc...
  repeated string errors = 2;
}

// CardSegment will then mapped to unique segment code while passing to vendor.
enum CardSegment {
  CARD_SEGMENT_UNSPECIFIED = 0;
  // 001 will be mapped if user presence of UPI.
  UPI = 1;
  // 002 will be mapped if user presence of DEBIT_CARD.
  DEBIT_CARD = 2;
  // 003 will be mapped if user presence of AMPLIFI_CREDIT_CARD.
  AMPLIFI_CREDIT_CARD = 3;
  // 004 will be mapped if user presence of SIMPLIFI_CREDIT_CARD.
  SIMPLIFI_CREDIT_CARD = 4;
  // 005 will be mapped if user presence if MAGNIFI_CREDIT_CARD.
  MAGNIFI_CREDIT_CARD = 5;
  // 006 will be mapped if user is of FI_LITE type and have non of the above cards.
  FI_LITE = 6;
}
