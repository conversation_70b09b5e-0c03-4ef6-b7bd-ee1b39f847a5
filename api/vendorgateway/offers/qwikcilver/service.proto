// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.offers.qwikcilver;

import "api/vendorgateway/request_header.proto";
import "api/rpc/status.proto";
import "api/vendorgateway/offers/qwikcilver/image.proto";
import "api/vendorgateway/offers/qwikcilver/card.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/name.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/offers/qwikcilver";
option java_package = "com.github.epifi.gamma.api.vendorgateway.offers.qwikcilver";

service Qwikcilver {
  // GetCategoryDetails to fetch details of all or a particular category.
  rpc GetCategoryDetails(GetCategoryDetailsRequest) returns (GetCategoryDetailsResponse);
  // GetProductList to fetch all the products of a particular category.
  rpc GetProductList(GetProductListRequest) returns (GetProductListResponse);
  // GetProductList to fetch details of a particular product.
  rpc GetProductDetails(GetProductDetailsRequest) returns (GetProductDetailsResponse);
  // CreateOrder to create an order for a product.
  rpc CreateOrder(CreateOrderRequest) returns (CreateOrderResponse);
  // GetOrderStatus to fetch the status of an already created order.
  rpc GetOrderStatus(GetOrderStatusRequest) returns (GetOrderStatusResponse);
  // GetActivatedCardDetails to fetch the details of an EGV card for which an order was placed before.
  // Card is generated only when/if an order reached a terminal success state.
  rpc GetActivatedCardDetails(GetActivatedCardDetailsRequest) returns (GetActivatedCardDetailsResponse);
}

// request to fetch oauth2 authorization code.
message GetAuthorizationCodeRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
}

message GetAuthorizationCodeResponse {
  // rpc status
  rpc.Status status = 1;
  // authorization token for oauth2
  string authorization_code = 2;
}

// request to fetch vendor access token.
message GetAccessTokenRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // oauth2.0 authorization code
  string authorization_code = 2;
}

message GetAccessTokenResponse {
  // rpc status
  rpc.Status status = 1;
  // access token
  string access_token = 2;
}

message GetCategoryDetailsRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // field is set by vendorgateway.
  string access_token = 2;
  // optional field to get details of a particular category,
  // if empty then details of all the available categories would be fetched.
  string category_id = 3;
}

message GetCategoryDetailsResponse {
  // rpc status
  rpc.Status status = 1;
  // category id
  string id = 2;
  // category name
  string name = 3;
  // images
  Images images = 4;

  message SubCategory {
    // sub category id
    string id = 1;
    // sub category name
    string name = 2;
    // images
    Images images = 3;
  }
  repeated SubCategory subcategories = 5;
}

message GetProductListRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // field is set by vendorgateway.
  string access_token = 2;
  // category whose products are to be fetched.
  string category_id = 3;

  // fields required for pagination
  int32 offset = 4;
  int32 limit = 5;
}

message GetProductListResponse {
  // rpc status
  rpc.Status status = 1;

  message Product {
    string sku = 1;
    string name = 2;
    string min_price = 3;
    string max_price = 4;
    Images images = 5;
  }
  // product list
  repeated Product products = 2;
}

message GetProductDetailsRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // field is set by vendorgateway.
  string access_token = 2;
  // product sku whose details are to be fetched.
  string sku = 3;
}

message GetProductDetailsResponse {
  // rpc status
  rpc.Status status = 1;

  string sku = 2;
  string name = 3;
  string desc = 4;
  // type of product i.e DIGITAL or PHYSICAL
  string type = 5;

  message Price {
    string type = 1;
    string min = 2;
    string max = 3;
    repeated string denominations = 4;
  }
  Price price = 6;

  Images images = 7;

  message Tnc {
    string link = 1;
    string content = 2;
  }
  Tnc tnc = 8 [json_name = "tnc"];
}

message CreateOrderRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // field is set by vendorgateway.
  string access_token = 2;
  // ref_number useful for idempotency purposes.
  // also useful to fetch the order status in case of api timeouts.
  string ref_number = 3;
  // product for which order is to be placed
  string product_sku = 4;
  // product amount like for a Rs 500 flipkart Coupon amount would be 500
  int32 amount = 5;
  // details of user for whom order is to be placed
  UserDetails user_details = 6;

  message UserDetails {
    // name of the user for whom order is to be placed
    api.typesv2.common.Name name = 1;
    // phone number of the user for whom order is to be placed
    api.typesv2.common.PhoneNumber phone_number = 2;
  }
}

message CreateOrderResponse {
  // rpc status
  rpc.Status status = 1;
  // id of order created.
  string order_id = 2;
  string order_status = 3;
  // list of egv details
  repeated Card cards = 4;
}

message GetOrderStatusRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // field is set by vendorgateway.
  string access_token = 2;
  // ref_number that was used for creating the order.
  string ref_number = 3;
}

message GetOrderStatusResponse {
  // rpc status
  rpc.Status status = 1;
  // id of order created.
  string order_id = 2;
  string order_status = 3;
}

message GetActivatedCardDetailsRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;
  // field is set by vendorgateway.
  string access_token = 2;
  // order for which card details are to be fetched.
  string order_id = 3;
}

message GetActivatedCardDetailsResponse {
  // rpc status
  rpc.Status status = 1;
  string order_status = 2;
  // list of egv card details
  repeated Card cards = 4;
}
