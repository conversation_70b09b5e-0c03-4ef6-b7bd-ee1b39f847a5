syntax = "proto3";

package vendorgateway.lending.collections.credgenics;

import "google/type/date.proto";
import "google/type/money.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.collections.credgenics";

// PaymentDetails contains information about all the payment that are recovered on the loan.
message PaymentDetails {
  // e.g: 123
  string id = 1;
  // e.g: 2000
  google.type.Money amount_recovered = 2;
  // e.g: "Communications"
  RecoveryMethod recovery_method = 3;
  // the day value in google.type.Date is ignored
  google.type.Date allocation_month = 4;
  // e.g: "Online"
  PaymentMethod payment_method = 5;
  // e.g: "UPI"
  PaymentMode mode = 6;
  string payment_reference_number = 7;
  // e.g: "demo.admin@credgenics"
  string author = 8;
  // e.g: "2021-05-15 18:44:15"
  google.protobuf.Timestamp created_at = 9;
}

enum RecoveryMethod {
  RECOVERY_METHOD_UNSPECIFIED = 0;
  RECOVERY_METHOD_COMMUNICATION = 1;
  RECOVERY_METHOD_FIELD_COLLECTION = 2;
  RECOVERY_METHOD_LEGAL = 3;
}

enum PaymentMethod {
  PAYMENT_METHOD_UNSPECIFIED = 0;
  PAYMENT_METHOD_ONLINE = 1;
  PAYMENT_METHOD_CASH = 2;
  PAYMENT_METHOD_CHEQUE = 3;
  PAYMENT_METHOD_BANK_ACCOUNT_TRANSFER = 4;
  PAYMENT_METHOD_APPLICATION = 5;
  PAYMENT_METHOD_PAYMENT_LINK = 6;
  PAYMENT_METHOD_UPI = 7;
}

enum PaymentMode {
  PAYMENT_MODE_UNSPECIFIED = 0;
  PAYMENT_MODE_UPI = 1;
  PAYMENT_MODE_NEFT = 2;
  PAYMENT_MODE_RTGS = 3;
  PAYMENT_MODE_BILLZY = 4;
}

message UpdatePaymentDetails {
  // loan id for which the recovery was made
  string loan_id = 1;
  // allocation month for which the recovery was made
  // the day value in google.type.Date is ignored
  google.type.Date allocation_month = 2;
  // total amount received in a given payment
  // (required)
  google.type.Money amount_recovered = 3;
  // the date on which the recovery was made by the agent/channel i.e. it has been updated on CG system that the recovery has been done. this date might differ from the actual date on which the payment was received from the borrower.
  google.type.Date recovery_date = 4;
  // the recovery method that was used to recover a given payment from the borrower (options: field collection, communication, legal)
  RecoveryMethod recovery_method = 5;
  // the name of the user that collected the payment, in case of agent it will be the name, otherwise it will be name of the api
  string recovered_by = 6;
  // the method of payment that was used to complete the transfer of money (options: online, cash, cheque, bank account transfer, application, payment link)
  // (required)
  PaymentMethod payment_method = 7;
  // the mode of payment that was used to complete the transfer of money
  // (required)
  PaymentMode payment_mode = 8;
  // the unique reference number generated by the payment processor at the time of execution of payment (upi transaction id, ...)
  // e.g: rfn123456
  string payment_reference_number = 9;
  // the date on which the payment was actually received in the lenders account after processing of payment by the payment processor
  google.type.Date payment_received_date = 10;
  // TODO(mounish): add closure_with, Recovery Amount Bifurcation variables
}

