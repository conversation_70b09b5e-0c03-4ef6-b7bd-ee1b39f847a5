syntax = "proto3";

package vendorgateway.lending.collections.credgenics;

import "google/protobuf/timestamp.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/collections/credgenics";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.collections.credgenics";

// Notice contains information about the legal activities performed on the loan account.
// It includes legal notice and physical notice tracking.
message Notice {
  // e.g: "125792"
  string id = 1;
  // e.g: "lrn"
  // TODO(mounish): possible to use enum?
  string case_type = 2;
  // e.g: "Notice", "Speedpost"
  NoticeDocumentType document_type = 3;
  string s3_link = 4;
  // e.g: "2021-04-15 06:43:50"
  google.protobuf.Timestamp created_at = 5;
  // as the data is dynamic and we don't have clarity on the structure from the vendor, we use google.protobuf.Struct
  google.protobuf.Struct data = 6;
}

enum NoticeDocumentType {
  NOTICE_DOCUMENT_TYPE_UNSPECIFIED = 0;
  NOTICE_DOCUMENT_TYPE_NOTICE = 1;
  NOTICE_DOCUMENT_TYPE_SPEEDPOST = 2;
}
