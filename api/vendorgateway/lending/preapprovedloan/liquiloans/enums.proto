//go:generate gen_sql -types=PaymentStatus,Status
syntax = "proto3";

package vendorgateway.lending.preapprovedloan.liquiloans;

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/liquiloans";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.preapprovedloan.liquiloans";

enum FeeType {
  FEE_TYPE_UNSPECIFIED = 0;
  FEE_TYPE_STANDARD = 1;
  FEE_TYPE_PERCENTAGE = 2;
  FEE_TYPE_FIXED_AMOUNT = 3;
}

enum FeeAppliedOn {
  FEE_APPLIED_ON_UNSPECIFIED = 0;
  FEE_APPLIED_ON_LOAN_AMOUNT = 1;
  FEE_APPLIED_ON_PRODUCT_AMOUNT = 2;
}

enum RoiType {
  ROI_TYPE_UNSPECIFIED = 0;
  ROI_TYPE_FLAT = 1;
  ROI_TYPE_DECLINING = 2;
  ROI_TYPE_BALLOON = 3;
}

enum TenureFrequency {
  TENURE_FREQUENCY_UNSPECIFIED = 0;
  TENURE_FREQUENCY_DAILY = 1;
  TENURE_FREQUENCY_WEEKLY = 2;
  TENURE_FREQUENCY_MONTHLY = 3;
}

enum PfType {
  PF_TYPE_UNSPECIFIED = 0;
  PF_TYPE_PERCENTAGE = 1;
  PF_TYPE_STANDARD = 2;
  PF_TYPE_FIXED_AMOUNT = 3;
}

enum MandateStatus {
  MANDATE_STATUS_UNSPECIFIED = 0;
  MANDATE_STATUS_SUCCESS = 1;
  MANDATE_STATUS_INITIATED = 2;
  MANDATE_STATUS_FAILED = 3;
  MANDATE_STATUS_PENDING = 4;
}

enum Status {
  STATUS_UNSPECIFIED = 0;
  STATUS_CREATED = 1;
  STATUS_CLOSED = 2;
  STATUS_DISBURSED = 3;
  STATUS_READY_TO_DISBURSED = 4;
  STATUS_WITHDRAWN = 5;
  STATUS_APPROVED = 6;
  STATUS_REJECTED = 7;
  STATUS_PENDING = 8;
}

enum AuthFactorType {
  AUTH_FACTOR_TYPE_UNSPECIFIED = 0;
  AUTH_FACTOR_TYPE_DOB = 1;
  AUTH_FACTOR_TYPE_PINCODE_YOB = 2;
  AUTH_FACTOR_TYPE_MOBILE_NO = 3;
}

enum ApplicantStatus {
  APPLICANT_STATUS_UNSPECIFIED = 0;
  APPLICANT_STATUS_APPROVED = 1;
  APPLICANT_STATUS_CREATED = 2;
  APPLICANT_STATUS_REJECTED = 3;
  APPLICANT_STATUS_PENDING = 4;
}

enum PaymentStatus {
  PAYMENT_STATUS_UNSPECIFIED = 0;
  PAYMENT_STATUS_UNPAID = 1;
  PAYMENT_STATUS_PAID = 2;
  PAYMENT_STATUS_PARTIALLY_PAID = 3;
  PAYMENT_STATUS_PRE_CLOSURE = 4;
}

enum LoanProgram {
  LOAN_PROGRAM_UNSPECIFIED = 0;
  LOAN_PROGRAM_PRE_APPROVED_LOAN = 1;
  LOAN_PROGRAM_EARLY_SALARY = 2;
  LOAN_PROGRAM_FLDG = 3;
  LOAN_PROGRAM_ACQ_TO_LEND = 4;
  // small token personal loan
  LOAN_PROGRAM_STPL = 5;
  LOAN_PROGRAM_REALTIME_SUBVENTION = 6;
  LOAN_PROGRAM_FI_LITE_PL = 7;
  LOAN_PROGRAM_REALTIME_STPL = 8;
  LOAN_PROGRAM_NON_FI_CORE_STPL = 9;
  LOAN_PROGRAM_NON_FI_CORE_SUBVENTION = 10;
}

enum DocumentType {
  DOCUMENT_TYPE_UNSPECIFIED = 0;
  KFS = 1;
  AGREEMENT_LETTER = 2;
}

enum PaymentMode {
  PAYMENT_MODE_UNSPECIFIED = 0;
  PAYMENT_MODE_CASH = 1;
  PAYMENT_MODE_IMPS = 2;
  PAYMENT_MODE_UPI = 3;
  PAYMENT_MODE_NET_BANKING = 4;
  PAYMENT_MODE_CHEQUE = 5;
  PAYMENT_MODE_POS = 6;
  PAYMENT_MODE_CASHFREE = 7;
  PAYMENT_MODE_NACH = 8;
  PAYMENT_MODE_EXCESS_NACH = 9;
  PAYMENT_MODE_TOP_UP = 10;
  PAYMENT_MODE_QR_CODE = 11;
  PAYMENT_MODE_NEFT = 12;
}

enum RepaymentScheduleApiVersion {
  REPAYMENT_SCHEDULE_API_VERSION_UNSPECIFIED = 0;
  // old repayment schedule details api of liquiloans
  REPAYMENT_SCHEDULE_API_VERSION_V2 = 1;
  // new repayment schedule details api of liquiloans which gives near real time details after posting charges etc.
  // will also give additional details like paid_lpi, paid_other_charges and paid_bounce_charges
  REPAYMENT_SCHEDULE_API_VERSION_V4 = 2;
}

// SchemeVersion to be used when the same program has multiple scheme ids live at Liquiloans side
// This will identify the SID to be used in requests to Liquiloans
enum SchemeVersion {
  SCHEME_VERSION_V1 = 0;
  SCHEME_VERSION_V2 = 1;
}
