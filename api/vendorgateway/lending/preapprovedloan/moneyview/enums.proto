syntax = "proto3";

package vendorgateway.lending.preapprovedloan.moneyview;

option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/preapprovedloan/moneyview";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.preapprovedloan.moneyview";

enum EmploymentType {
  EMPLOYMENT_TYPE_UNSPECIFIED = 0;
  EMPLOYMENT_TYPE_SALARIED = 1;
  EMPLOYMENT_TYPE_SELF_EMPLOYED = 2;
}

enum AddressType {
  ADDRESS_TYPE_UNSPECIFIED = 0;
  ADDRESS_TYPE_CURRENT = 1;
  ADDRESS_TYPE_WORK = 2;
}

enum LeadStatus {
  LEAD_STATUS_UNSPECIFIED = 0;
  // denotes that lead is created
  LEAD_STATUS_CREATED = 1;
  // denotes that a loan offer is created for the lead.
  LEAD_STATUS_OFFERED = 2;
  // denotes that the loan application is in pending state.
  LEAD_STATUS_PENDING = 3;
  // denotes that nach stage has just started for the user in loan application flow.
  LEAD_STATUS_NACH_READY = 4;
  // denotes that loan application has expired.
  LEAD_STATUS_EXPIRED = 5;
  // denotes that loan application was rejected.
  LEAD_STATUS_REJECTED = 6;
  // denotes that the loan application was approved.
  LEAD_STATUS_APPROVED = 7;
  // denotes that the loan amount is disbursed.
  LEAD_STATUS_DISBURSED = 8;
  // denotes that loan account is closed.
  LEAD_STATUS_CLOSED = 9;
}

enum MvUserType {
  MV_USER_TYPE_UNSPECIFIED = 0;
  MV_USER_TYPE_WHITELISTED_OFFER = 1;
  MV_USER_TYPE_OPEN_MARKET = 2;
}

enum DeDupeStatus {
  DEDUPE_STATUS_UNSPECIFIED = 0;
  // denotes that shared pan passes the dedupe check i.e currently user does not has any active loan application at moneyview's end.
  DEDUPE_STATUS_DEDUPE_PASSED = 1;
  // denotes that shared pan does NOT passed the dedupe check i.e currently user does has an active loan application at moneyview's end.
  DEDUPE_STATUS_DEDUPE_FAILED = 2;
}

enum BlackBoxOfferGenerationStatus {
  BLACK_BOX_OFFER_GENERATION_STATUS_UNSPECIFIED = 0;
  BLACK_BOX_OFFER_GENERATION_STATUS_SUCCESS = 1;
  BLACK_BOX_OFFER_GENERATION_STATUS_REJECTED = 2;
}
