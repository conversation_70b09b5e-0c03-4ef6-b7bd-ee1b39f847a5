syntax = "proto3";

package vendorgateway.lending.lms.finflux;

import "api/order/payment/payment_protocol.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/vendorgateway/lending/lms/finflux/types/client.proto";
import "api/vendorgateway/lending/lms/finflux/types/enum.proto";
import "api/vendorgateway/lending/lms/finflux/types/loan.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/vendorgateway/lending/lms/finflux";
option java_package = "com.github.epifi.gamma.api.vendorgateway.lending.lms.finflux";

// TODO: improve comments for all RPCs
service Finflux {
  // CreateClient creates a new client (user) in the Finflux system
  rpc CreateClient (CreateClientRequest) returns (CreateClientResponse);
  // FetchClient fetches the details of a client (user) in the Finflux system by client_id or external_id
  rpc FetchClient (FetchClientRequest) returns (FetchClientResponse);
  // CreateLoan creates a new loan account in the Finflux system
  rpc CreateLoan (CreateLoanRequest) returns (CreateLoanResponse);
  // ApproveLoan approves a loan application in the Finflux system
  rpc ApproveLoan (ApproveLoanRequest) returns (ApproveLoanResponse);
  // DisburseLoan marks a loan as disbursed in the Finflux system
  rpc DisburseLoan (DisburseLoanRequest) returns (DisburseLoanResponse);
  // FetchLoanDetails fetches the details of a loan in the Finflux system by loan_id
  rpc FetchLoanDetails (FetchLoanDetailsRequest) returns (FetchLoanDetailsResponse);
  // CalculateLoanSchedule calculates the repayment schedule (without a loan id) in the Finflux system
  rpc CalculateLoanSchedule (CalculateLoanScheduleRequest) returns (CalculateLoanScheduleResponse);
  // FetchLoanSchedule fetches the repayment schedule of a loan in the Finflux system by loan_id
  rpc FetchLoanSchedule (FetchLoanScheduleRequest) returns (FetchLoanScheduleResponse);
  // RejectLoan rejects a loan application in the Finflux system and usually applicable when the
  // current state of loan is "Submitted and Pending approval" in the Finflux system
  rpc RejectLoan (RejectLoanRequest) returns (RejectLoanResponse);
  // PostLoanRepayment rpc is useful to post loan repayment information to Finflux LMS.
  rpc PostLoanRepayment (PostLoanRepaymentRequest) returns (PostLoanRepaymentResponse);
  // ForecloseLoan rpc is used to foreclose a loan account
  rpc ForecloseLoan (ForecloseLoanRequest) returns (ForecloseLoanResponse);
  // GetForeclosureDetails rpc is useful to fetch loan foreclosure details from Finflux LMS
  rpc GetForeclosureDetails (GetForeclosureDetailsRequest) returns (GetForeclosureDetailsResponse);
  // GetRepaymentAllocationDetails is useful to fetch the payment allocation (emi level PIC breakup) of an already posted repayment from Finflux LMS.
  rpc GetRepaymentAllocationDetails (GetRepaymentAllocationDetailsRequest) returns (GetRepaymentAllocationDetailsResponse);
  // GetTransactionSummary provides details of a payment transaction (repayment or foreclosure) and overall breakup into P,I,penalty,fees,OverPayment,ExcessPayment buckets
  rpc GetTransactionSummary (GetTransactionSummaryRequest) returns (GetTransactionSummaryResponse);
}

message GetTransactionSummaryRequest {
  vendorgateway.RequestHeader header = 1;
  string txn_external_id = 2 [(validate.rules).string.min_len = 1];
  string loan_id = 3 [(validate.rules).string.min_len = 1];
}

message GetTransactionSummaryResponse {
  rpc.Status status = 1;
  // actual payment date
  google.type.Date txn_date = 2;
  // txn submission date
  google.type.Date submitted_on_date = 3;
  google.type.Money principal_portion = 4;
  google.type.Money interest_portion = 5;
  google.type.Money fee_charges_portion = 6;
  google.type.Money penalty_charges_portion = 7;
  // over payment is when user has paid more than the foreclosure/closure amount
  google.type.Money overpayment_portion = 8;
  // excess payment is applicable when user has paid more than what is currently due and excess bucket allocation is configured for the product
  google.type.Money excess_payment_portion = 9;
}

message RejectLoanRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  google.type.Date rejected_on = 3 [(validate.rules).message.required = true];
}

message RejectLoanResponse {
  rpc.Status status = 1;
  types.LoanStatus loan_status = 2;
  string loan_id = 3;
  string client_id = 4;
  google.type.Date rejected_on = 5;
  google.type.Date closed_on = 6;
}

message FetchLoanScheduleRequest {
  vendorgateway.RequestHeader header = 1;
  oneof identifier {
    option (validate.required) = true;
    // unique identifier of the loan in the Finflux system
    string loan_id = 2;
    // external id using which we've created the loan in the Finflux system
    string external_id = 3;
  }
}

message FetchLoanScheduleResponse {
  rpc.Status status = 1;
  types.LoanDetails loan = 2;
  repeated types.LoanRepaymentPeriod periods = 3;
}

message CalculateLoanScheduleRequest {
  vendorgateway.RequestHeader header = 1;
  // Finflux's product unique identifier (this is generally unique for combination of lender and loan program)
  int32 product_id = 2 [(validate.rules).int32.gte = 1];
  google.type.Money principal_amount = 3 [(validate.rules).message.required = true];
  int32 number_of_installments = 4 [(validate.rules).int32.gte = 1];
  double interest_rate = 5;
  google.type.Date expected_disbursement_date = 6 [(validate.rules).message.required = true];
  // date when the loan application was submitted
  google.type.Date submitted_on = 7 [(validate.rules).message.required = true];
  // (inclusive of GST)
  // e.g.: 3.54 (for 3% processing fee and 18% GST on it)
  double processing_charge_in_percentage = 8;
}

message CalculateLoanScheduleResponse {
  rpc.Status status = 1;
  google.type.Money total_principal_disbursed = 2;
  google.type.Money total_principal_expected = 3;
  google.type.Money total_principal_paid = 4;
  google.type.Money total_interest_charged = 5;
  google.type.Money total_fee_charges_charged = 6;
  google.type.Money total_penalty_charges_charged = 7;
  google.type.Money total_repayment_expected = 8;
  google.type.Money total_outstanding = 9;
  google.type.Money calculated_emi_amount = 10;
  repeated types.LoanRepaymentPeriod periods = 11;
  google.type.Money net_disbursal_amount = 12;
  google.type.Date expected_maturity_date = 13;
  double annual_percentage_rate = 14;
  int32 number_of_repayments = 15;
  types.FrequencyType repayment_frequency = 16;
  google.type.Money charges_due_at_disbursement = 17;
  google.type.Money broken_period_interest = 18;
}

message FetchLoanDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  oneof identifier {
    option (validate.required) = true;
    // unique identifier of the loan in the Finflux system
    string loan_id = 2;
    // external id using which we've created the loan in the Finflux system
    string external_id = 3;
  }
}

message FetchLoanDetailsResponse {
  rpc.Status status = 1;
  types.LoanDetails loan = 2;
}

message CreateLoanRequest {
  vendorgateway.RequestHeader header = 1;
  // Finflux's product unique identifier (this is generally unique for combination of lender and loan program)
  int32 product_id = 2 [(validate.rules).int32.gte = 1];
  // unique identifier of the customer in the Finflux system
  string client_id = 3 [(validate.rules).string.min_len = 1];
  // vendor unique identifier of the loan at Fi
  string external_id = 4 [(validate.rules).string.min_len = 1];
  google.type.Money principal_amount = 5 [(validate.rules).message.required = true];
  google.type.Date submitted_on = 6 [(validate.rules).message.required = true];
  int32 number_of_repayments = 7 [(validate.rules).int32.gte = 1];
  // (inclusive of GST)
  // e.g.: 3.54 (for 3% processing fee and 18% GST on it)
  double processing_charge_in_percentage = 8;
  // annual interest rate in percentage
  // e.g.: 9.0 (for 9% interest rate per annum)
  double interest_rate = 9;
  google.type.Date expected_disbursement_date = 10 [(validate.rules).message.required = true];
}

message CreateLoanResponse {
  rpc.Status status = 1;
  // unique identifier of the loan in the Finflux system
  string loan_id = 2;
}

message DisburseLoanRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  // it's not the disbursal amount, it's the loan amount excluding the processing charges etc.
  google.type.Money transaction_amount = 3 [(validate.rules).message.required = true];
  google.type.Date actual_disbursement_date = 4 [(validate.rules).message.required = true];
}

message DisburseLoanResponse {
  rpc.Status status = 1;
}

message ApproveLoanRequest {
  vendorgateway.RequestHeader header = 1;
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  google.type.Date approved_on = 3 [(validate.rules).message.required = true];
  google.type.Money approved_loan_amount = 4 [(validate.rules).message.required = true];
  google.type.Date expected_disbursement_date = 5 [(validate.rules).message.required = true];
}

message ApproveLoanResponse {
  rpc.Status status = 1;
}

message CreateClientRequest {
  vendorgateway.RequestHeader header = 1;
  types.Client client = 2 [(validate.rules).message.required = true];
  // in PostalAddress, postal_code should be a valid indian postal code
  repeated api.typesv2.PostalAddress addresses = 3;
}

message CreateClientResponse {
  rpc.Status status = 1;
  // unique identifier of the user in the Finflux system
  string client_id = 2;
}

message FetchClientRequest {
  vendorgateway.RequestHeader header = 1;
  oneof identifier {
    option (validate.required) = true;
    // unique identifier of the customer in the Finflux system
    string client_id = 2;
    // vendor unique identifier of the user at Fi
    string external_id = 3;
  }
}

message FetchClientResponse {
  rpc.Status status = 1;
  types.Client client = 2;
}
message PostLoanRepaymentRequest {
  vendorgateway.RequestHeader header = 1;
  // amount repaid
  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];
  // unique identifier of payment at Fi
  string external_id = 3 [(validate.rules).string.min_len = 1];
  // denotes the repayment txn time.
  google.protobuf.Timestamp transaction_time = 4 [(validate.rules).timestamp.required = true];
  string loan_id = 5 [(validate.rules).string.min_len = 1];
  // this would be used to pass appropriate payment type to Finflux while payment posting.
  order.payment.PaymentProtocol payment_protocol = 6;
}
message PostLoanRepaymentResponse {
  rpc.Status status = 1;
}

message ForecloseLoanRequest {
  vendorgateway.RequestHeader header = 1;
  // amount repaid
  google.type.Money transaction_amount = 2 [(validate.rules).message.required = true];
  // denotes the repayment txn time.
  google.protobuf.Timestamp transaction_time = 3 [(validate.rules).timestamp.required = true];
  // loan request id
  string loan_id = 4 [(validate.rules).string.min_len = 1];
  // denotes the unique external id which was passed to Finflux LMS for ForecloseLoan.
  string external_id = 5 [(validate.rules).string.min_len = 1];
  // this would be used to pass appropriate payment type to Finflux while payment posting.
  order.payment.PaymentProtocol payment_protocol = 6;

}
message ForecloseLoanResponse {
  rpc.Status status = 1;
}
message GetForeclosureDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  // denotes the loan account against which the payment needs to be recorded in Finflux LMS,
  // we need to pass Finflux loan account id here.
  string loan_id = 2 [(validate.rules).string.min_len = 1];
  // denotes the date on which if foreclosure was tried then what should be the foreclosure amount.
  google.protobuf.Timestamp foreclosure_time = 3 [(validate.rules).timestamp.required = true];
}

// https://drive.google.com/file/d/1B-iAYj6l-v2rO5hbNDmNtsglVLW4MjnR/view?usp=drive_link
message GetForeclosureDetailsResponse {
  rpc.Status status = 1;
  // amount is the total amount user has to pay to close the loan (this includes the excess amount as well)
  // net_foreclosure_amount will be 0 if loan is past maturity date but amount will be populated in all cases
  // since we have not configured excess payment for our products, amount always means foreclosure/closure amount for our use cases
  google.type.Money amount = 2;
  google.type.Money net_foreclosure_amount = 3;
  google.type.Money principal_portion = 4;
  google.type.Money interest_portion = 5;
  google.type.Money fee_charges_portion = 6;
  google.type.Money penalty_charges_portion = 7;
  google.type.Money outstanding_loan_balance = 8;
  // extra charges we have to pay in case we foreclose.
  google.type.Money foreclosure_charges = 9;
}
message GetRepaymentAllocationDetailsRequest {
  vendorgateway.RequestHeader header = 1;
  // denotes the unique external id which was passed to Finflux LMS for repayment posting.
  string txn_external_id = 2 [(validate.rules).string.min_len = 1];
}
message GetRepaymentAllocationDetailsResponse {
  message EmiLevelPaymentAllocationInfo {
    // denotes the loan account id in Finflux LMS whose payment allocation is fetched.
    uint32 loan_id = 1;
    // denotes the emi whose payment allocation is present in this info.
    uint32 emi_no = 2;
    // denotes the amount of principal which was marked as paid in this emi.
    google.type.Money principal_amount = 3;
    // denotes the amount of interest which was marked as paid in this emi.
    google.type.Money interest_amount = 4;
    // Todo add charges field
    google.type.Money penalty_amount = 5;
    google.type.Date due_date = 6;
  }
  rpc.Status status = 1;
  repeated EmiLevelPaymentAllocationInfo items = 2;
}

