// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendorgateway.openbanking.deposit;

import "api/typesv2/deposit.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.deposit";

// Account has set of attributes details of deposits plan like type of deposit FD/SD/RD , duration etc.
message SchemeDetails {
  // scheme type is different schemes like Flexi Fixed deposit account, NRO fixed deposit, Tax-saving fixed deposit
  SchemeType scheme_type = 1;

  // amount wish to deposit in the deposit account.
  google.type.Money amount = 2;

  // time duration for deposit
  api.typesv2.DepositTerm deposit_term = 3;

  // TODO(vivek): Reconfirm desc with FED
  // Indicate the type of automatic renewal that can be carried out at the end of deposit.
  // If false then Automatic renewal of fixed deposit is done by providers.
  // This entails re-booking of existing FD for the same tenure but at the prevailing fixed deposit rates.
  bool is_auto_close_maturity = 4;

  // Set of attributes for renewing deposit accounts with new amount config.
  RenewInfo renew_info = 5;
}

// Set of attributes for renewing deposit accounts.
// It contains params related to renewal details of deposit account amount.
// User can increase, decrease amount for renewal. Rest other scheme details will be from matured deposit account.
message RenewInfo {
  // flag for auto renewable of deposit account
  bool is_auto_renewable = 1;

  // Type of renewal that will be carried out while renewal.
  // Based on this renewal options renewal amount will be decided
  // For ex. If Renewal Option is : PRINCIPLE_EXTRA then renewal_addition_amount need to be passed and
  // also provide debit_account_number for debit.
  //
  // FIXED_AMOUNT -> Fixed amount will go for renewal and rest amount
  //  will be credited/redeemed to user repay account number.
  // MATURITY_ONLY -> Complete amount after maturity will go for renewal.
  // PRINCIPLE_ONLY -> Only principle amount will go to renewal.
  // PRINCIPLE_EXTRA -> Renewal amount will be principle amount + extra amount defined by user.
  //   For this extra amount, debit_account_number need to passed.
  RenewOption option = 2;

  // The amount for which the deposit account is renewed.
  // This field is relevant only if the renewal option is FIXED_AMOUNT.
  google.type.Money amount = 3;

  // Account number from which amount will be deducted on renewal if required ex. for PRINCIPLE_EXTRA.
  string debit_account_number = 4;

  // Addition amount if customer wanted to renew with more amount.
  // This option is to be entered if renewal option is renew with PRINCIPLE_EXTRA.
  string additional_amount = 5;
}

// struct for deposit account details
message AccountDetails {
  // Name of FD account
  string account_name = 1;

  // description of scheme
  string scheme_desc = 2;

  // current balance in account(Principle only)
  google.type.Money principle_amount = 3;

  // is account close flag
  bool is_closed = 4;

  // account opening date
  google.protobuf.Timestamp account_opening_date = 5;

  // TODO(vivek): Add more desc after full specs
  string frez_code = 6;

  // Branch name of opened account
  string branch_name = 7;

  // maturity date
  google.protobuf.Timestamp maturity_date = 8;

  // maturity amount
  google.type.Money maturity_amount = 9;

  // previous renewal of account
  google.protobuf.Timestamp previous_renew_date = 10;
}

// Enum of all schemes type
enum SchemeType {
  SCHEME_TYPE_UNSPECIFIED = 0;

  // In scheme type FD_MONTHLY_INTEREST, FD account will be created for customer
  // and interest will be deposited to customer savings bank account monthly.
  FD_MONTHLY_INTEREST = 1;

  // In scheme type FD_QUARTERLY_INTEREST, FD account will be created for customer
  // and interest will be deposited to customer savings bank account quarterly.
  FD_QUARTERLY_INTEREST = 2;

  // Fixed deposit with half yearly interest payout.
  // Interest amount to be credited to operative account on half yearly basis.
  // Maturity amount to be credited to repay account on maturity.
  FD_HALF_YEARLY_INTEREST = 3;

  // Fixed deposit with yearly interest payout.
  // Interest amount to be credited to operative account on yearly basis.
  // Maturity amount to be credited to repay account on maturity.
  FD_YEARLY_INTEREST = 4;

  // Fixed deposit with tax saver benefits.
  // Interest can be withdrawn monthly after TDS deduction.
  // The principal amount will only be returned after lock in period of 5 years.
  // Doesn't have auto renewal facility. Will be automatically closed after 5 years, and money will be credited to repay account.
  // FD_TAX_SAVING is deprecated. Please use FD_TAX_SAVING_MONTHLY_INTEREST instead.
  FD_TAX_SAVING = 5[deprecated = true];

  // In this scheme type, Deposit runs for a period mentioned at contracted interest rate.
  // After the tenure maturity amount will automatically comes to saving bank account.
  // Partial withdrawal of interest is not allowed.
  FD_CASH_CERTIFICATE = 6;

  // Deposit Scheme with interest payout on maturity and of any duration greater than 7 days.
  // Note: Federal doesn't allow any other scheme for FD duration less than 180 days, for all durations less than 180 days
  // this scheme should be used
  FD_INTEREST_ON_MATURITY = 7;

  // In this scheme type, fixed installment to be paid monthly and no change in installment amount or date is permitted.
  RD = 8;

  // In this scheme type, installment to be paid monthly and with flexibility of installment amount or date or
  // number of deposit is permitted.
  // This is also know as smart deposit.
  RD_FLEXI_SMART = 9;

  // skipping fields from 10 to 14(both inclusive) to keep space for new deposit types

  // denotes min_kyc scheme type (scheme_code - 35033)
  SAVINGS_MIN_KYC = 15;

  // denotes full kyc scheme type (scheme_code - 35031)
  SAVINGS_FINITY = 16;

  // denotes full kyc scheme type (scheme_code - 35032)
  SAVINGS_INFINITY = 17;

  // Fixed deposit with tax saver benefits.
  // Interest payout will be on maturity.
  // The principal amount will only be returned after lock in period of 5 years.
  // Doesn't have auto renewal facility. Will be automatically closed after 5 years, and money will be credited to repay account.
  FD_TAX_SAVING_ON_MATURITY = 10;

  // Fixed deposit with tax saver benefits.
  // Interest can be withdrawn monthly after TDS deduction.
  // The principal amount will only be returned after lock in period of 5 years.
  // Doesn't have auto renewal facility. Will be automatically closed after 5 years, and money will be credited to repay account.
  FD_TAX_SAVING_MONTHLY_INTEREST = 11;

  // Fixed deposit with tax saver benefits.
  // Interest can be withdrawn quarterly after TDS deduction.
  // The principal amount will only be returned after lock in period of 5 years.
  // Doesn't have auto renewal facility. Will be automatically closed after 5 years, and money will be credited to repay account.
  FD_TAX_SAVING_QUARTERLY_INTEREST = 12;

}

// Deposit account for renewal options.
enum RenewOption {
  // Unspecified renewal option
  RENEW_OPTION_UNSPECIFIED = 0;

  // fixed amount renewal option
  FIXED_AMOUNT = 1;

  // maturity renewal option
  MATURITY_ONLY = 2;

  // principle only renewal option
  PRINCIPLE_ONLY = 3;

  // principle extra renewal option
  PRINCIPLE_EXTRA = 4;
}
