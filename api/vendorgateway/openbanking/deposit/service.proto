// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendorgateway.openbanking.deposit;

import "api/accounts/account_type.proto";
import "api/rpc/status.proto";
import "api/typesv2/deposit.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/openbanking/deposit/customer.proto";
import "api/vendorgateway/openbanking/deposit/interest_rate.proto";
import "api/vendorgateway/openbanking/deposit/schemes.proto";
import "api/vendorgateway/openbanking/header/auth.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.deposit";

// Deposits service enables to provide epifi save like creating fixed
// deposits (FD), smart deposits (SD) etc. via partner banks.
service Deposit {

  // RPC to create FD(Fixed deposit) account at vendor bank and automatically transfer lump sum FD amount into it
  // from the operative account number passed in request. Account must be with epifi.
  //
  // This is an asynchronous API and FD creation may not be completed immediately.
  // This API, hence, will respond with an Acknowledgement of request received with SUCCESS/FAIL immediately.
  //
  // An updates (SUCCESS/FAILURE) on the FD creation from the vendor bank will be posted on the callback
  // url provided in the request. It is not in scope of this service to post any callback response to caller.
  //
  // RequestId from the CreateFDRequest will be used to map the callback response with the request by the caller.
  //
  // Status of AccountCreation can be queried using `CheckAccountStatus` RPC with request_id used in this request.
  rpc CreateFD (CreateFDRequest) returns (AckResponse);

  // RPC to create RD(Recurring deposit) account at vendor bank and automatically transfer initial installment of
  // RD amount into it from the operative account number passed in request. Account must be with epifi.
  //
  // This is an asynchronous API and RD creation may not be completed immediately.
  // This API, hence, will respond with an Acknowledgement of request received with SUCCESS/FAIL immediately.
  //
  // An updates (SUCCESS/FAILURE) on the RD creation from the vendor bank will be posted on the callback
  // url provided in the request. It is not in scope of this service to post any callback response to caller.
  //
  // RequestId from the CreateRDRequest will be used to map the callback response with the request by the caller.
  //
  // Status of AccountCreation can be queried using `CheckAccountStatus` RPC with request_id used in this request.
  //
  // Currently there is no option to automatically transfer recurring installment from operative bank account.
  rpc CreateRD (CreateRDRequest) returns (AckResponse);

  //The API is used to create a new flexi-RD account for the customer.
  //
  // RPC to create SD(Smart deposit/flexi-RD) account at vendor bank and automatically transfer initial installment of
  // SD amount into it from the operative account number passed in request. Account must be with epifi.
  //
  // This is an asynchronous API and SD creation may not be completed immediately.
  // This API, hence, will respond with an Acknowledgement of request received with SUCCESS/FAIL immediately.
  //
  // An updates (SUCCESS/FAILURE) on the RD creation from the vendor bank will be posted on the callback
  // url provided in the request. It is not in scope of this service to post any callback response to caller.
  //
  // RequestId from the CreateSDRequest will be used to map the callback response with the request by the caller.
  //
  // Status of AccountCreation can be queried using `CheckAccountStatus` RPC with request_id used in this request.
  rpc CreateSD (CreateSDRequest) returns (AckResponse);

  // RPC will be used to close the deposit account at vendor bank and automatically credit the amount from
  // deposit account to customer savings account . If customer have multiple savings account then saving account
  // configured for the purpose will be used.
  //
  // This is an asynchronous API and account closing may not be completed immediately.
  // This API, hence, will respond with an Acknowledgement of request received with SUCCESS/FAIL immediately.
  //
  // An updates (SUCCESS/FAILURE) on the closure of deposit account from the vendor bank will be posted on the callback
  // url provided in the request. It is not in scope of this service to post any callback response to caller.
  //
  // Status of AccountCreation can be queried using `CheckAccountStatus` API with request_id used in this request.
  //
  // Account will be closed by vendor as per their account closure policy.
  // Ex. For Federal account will be closed at EOD.
  rpc CloseAccount (CloseAccountRequest) returns (AckResponse);

  // RPC to check status of create/close account at vendor bank. It will fetch account details on basis of
  // queried_request_id if exist.
  //
  // This is sync API and caller will immediately get response with status and details of account if exist/created/closed.
  rpc CheckAccountStatus (CheckAccountStatusRequest) returns (CheckAccountStatusResponse);

  // RPC to fetch account details for a given account number and account type.
  // Response to this RPC will contain details like maturity date, balance, status of account.
  //
  // This is a sync API and account detail will be returned in response of this API.
  rpc GetAccountDetail (GetAccountDetailRequest) returns (GetAccountDetailResponse);

  // RPC to fetch list of all account from vendor for a given customerId and phoneNumber
  //
  // This is sync API and list of account will be returned in response of this API.
  rpc ListAccount (ListAccountRequest) returns (ListAccountResponse);

  // GetInterestRateInfo RPC will be used to fetch interest rate information for a given deposit scheme from vendor.
  rpc GetInterestRateInfo (GetInterestRateInfoRequest) returns (GetInterestRateInfoResponse);

  // CalculateInterestDetails RPC will calculate and return interest and maturity details for the given input deposit parameters
  rpc CalculateInterestDetails (CalculateInterestDetailsRequest) returns (CalculateInterestDetailsResponse);

  // GetPreClosureDetails RPC will calculate and return interest,penalty and maturity detail for the given input deposit parameters
  // https://docs.google.com/document/d/1I3ofYr_dvAM5zJwKH1VoFUk2-UA4xjgq/edit?usp=drive_link&ouid=106341874717564561814&rtpof=true&sd=true
  rpc GetPreClosureDetails (GetPreClosureDetailsRequest) returns (GetPreClosureDetailsResponse);

  // AutoRenewFd RPC will enable auto renewal feature in a fixed deposit
  rpc AutoRenewFd (AutoRenewFdRequest) returns (AutoRenewFdResponse);
}

message GetInterestRateInfoRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Unique identifier for the request.
  string request_id = 2;

  // Scheme for which interest rate information is required.
  InterestRateCategory interest_rate_category = 3;

  // effective_date is the date for which interest rate is to be fetched.
  google.type.Date effective_date = 4;

  // max_slab_amount is the maximum slab amount for which interest rate is to be fetched.
  google.type.Money max_slab_amount = 5;
}

message GetInterestRateInfoResponse {
  rpc.Status status = 1;
  repeated InterestRateEntry interest_rate_entries = 2;
}

// Request struct to initiate FD creation at vendor bank.
message CreateFDRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique identifier for the request.
  // This request id will be used to map the call back response received by the caller.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in ack response.
  string request_id = 3;

  // Basic info of customer like title,name,contact info
  CustomerInfo customer_info = 4;

  // account info for customer
  CustomerAccountInfo customer_account_info = 5;

  // Scheme details has set of attributes for creating deposit FD/SD/RD , deposit duration, amount etc.
  SchemeDetails scheme_details = 6;

  // Flag to add nominee for user.
  bool has_nominee = 7;

  // Field Number 8 was being used by `NomineeInfo` field which is removed now. So, marking 8 as reserved.
  reserved 8;

  // Customer taxation info
  TaxInfo tax_info = 9;

  // Banks ask their account holders to make nominations which mean that they should nominate persons to whom the money
  // lying in their accounts should go in the event of their death.
  //
  // If has_nominee is true then nominee info need to be passed to user
  // nominee_details defines a set of attributes corresponding to the nominee details needed for creating deposit accounts.
  // It also includes the guardian attributes for a nominee in case the nominee is a minor.
  DepositNomineeDetails nominee_details = 10;
}

// Response struct of Acknowledgment of request received for all async RPC.
message AckResponse {
  // List of status codes returned
  enum Status {
    // Returned a success
    OK = 0;

    // The state will be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller should check the status using deposit enquiry
    UNKNOWN = 2;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // Deposit creation/pre-closure has failed
    // Failure has specific status code so as to differ from system errors like unmarshalling, decryption etc
    // which will be classified as INTERNAL error
    FAILED = 100;

    // This signifies that the request is being processed
    IN_PROGRESS = 101;
  }

  rpc.Status status = 1;

  // response code as sent by the vendor bank
  string raw_response_code = 2;

  // Success text if request received by vendor is successful
  // Otherwise it will contain reason for failure
  string raw_response_description = 3;

  // epifi status code corresponding to the raw status code sent by the vendor
  string status_code = 4;

  // description of the epifi status code
  string status_description = 5;

  // transaction timestamp
  google.protobuf.Timestamp transaction_timestamp = 6;
}

// Request struct to initiate RD creation at vendor bank.
message CreateRDRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique identifier for the request.
  // This request id will be used to map the call back response received by the caller.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in ack response.
  string request_id = 3;

  // Basic info of customer like title,name,contact info
  CustomerInfo customer_info = 4;

  // account info for customer
  CustomerAccountInfo customer_account_info = 5;

  // Scheme details has set of attributes for deposit FD/SD/RD account , deposit duration, amount etc.
  SchemeDetails scheme_details = 6;

  // Flag to add nominee for user.
  bool has_nominee = 7;

  // Field Number 8 was being used by `NomineeInfo` field which is removed now. So, marking 8 as reserved.
  reserved 8;

  // Customer taxation info
  TaxInfo tax_info = 9;

  // Banks ask their account holders to make nominations which mean that they should nominate persons to whom the money
  // lying in their accounts should go in the event of their death.
  //
  // If has_nominee is true then nominee info need to be passed to user
  // nominee_details defines a set of attributes corresponding to the nominee details needed for creating deposit accounts.
  // It also includes the guardian attributes for a nominee in case the nominee is a minor.
  //
  // A nominee is minor if age is below 18 years.
  DepositNomineeDetails nominee_details = 10;
}

// Request struct to initiate SD/flexi-RD creation at vendor bank.
message CreateSDRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique identifier for the request.
  // This request id will be used to map the call back response received by the caller.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in ack response.
  string request_id = 3;

  // Basic info of customer like title,name,contact info
  CustomerInfo customer_info = 4;

  // account info for customer
  CustomerAccountInfo customer_account_info = 5;

  // scheme details has set of attributes for requested deposit FD/SD/RD account , deposit duration, amount etc.
  SchemeDetails scheme_details = 6;

  // Flag to add nominee for user.
  bool has_nominee = 7;

  // Field Number 8 was being used by `NomineeInfo` field which is removed now. So, marking 8 as reserved.
  reserved 8;

  // Customer taxation info
  TaxInfo tax_info = 9;

  // Banks ask their account holders to make nominations which mean that they should nominate persons to whom the money
  // lying in their accounts should go in the event of their death.
  //
  // If has_nominee is true then nominee info need to be passed to user
  // nominee_details defines a set of attributes corresponding to the nominee details needed for creating deposit accounts.
  // It also includes the guardian attributes for a nominee in case the nominee is a minor.
  //
  // A nominee is minor if age is below 18 years.
  DepositNomineeDetails nominee_details = 10;
}

// Request struct for close deposit account
message CloseAccountRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique identifier for the request.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in response.
  string request_id = 3;

  // Unique identifier for customer.
  string customer_id = 4;

  // Customer bank account number from which money will be deducted to create deposit account.
  string customer_bank_account_number = 5;

  // Deposit account account number (FD/SD/RD account number)
  string deposit_account_number = 6;

  // registered phone number of user
  api.typesv2.common.PhoneNumber phone_number = 7;
}

// request struct to check create/close account status
message CheckAccountStatusRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique identifier for the request.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in response.
  string request_id = 3;

  // Request id of the original request (Create/Close FD,SD,RD account request) for which Status check is called.
  string queried_request_id = 4;

  // Unique identifier for customer.
  string customer_id = 5;

  // mobile number of customer
  api.typesv2.common.PhoneNumber phone_number = 6;
}

// response struct for check account status
message CheckAccountStatusResponse {
  enum Status {
    // Returned a success
    OK = 0;

    // The state will be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller should re-enquire after sometime, to resolve the ambiguity
    UNKNOWN = 2;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // Deposit creation/pre-closure has failed
    // Failure has specific status code so as to differ from system errors like unmarshalling, decryption etc
    // which will be classified as INTERNAL error
    FAILED = 100;

    // This signifies that the request is being processed
    // `SUSPECT` responseAction from federal is mapped to in progress
    IN_PROGRESS = 101;

    // Request Id was not present at vendor's end.
    // This could be because request couldn't reach to vendor's servers and hence can be safely retried
    ORIGINAL_REQUEST_ID_NOT_FOUND = 102;

    // Business failure at vendor
    // Due to on-going issues with vendor there could be use-cases where we might have to suppress alerts for specific
    // failure error codes sent from vendor. Suppression of such alerts is needed as the fix from vendor side could take
    // some sufficient time, hence adding noise to the alerts.
    // All such response codes will be mapped to this status, and hence alerts for those will be suppressed.
    BUSINESS_FAILURE = 103;

    // as part of deposit creation at partner bank, KYC data need to add to there banking portal
    // this is a manual process done by the user via a link sent by the partner bank
    KYC_STEP_PENDING_ON_USER = 104;

    // When a lien exists against a deposit, it cannot be closed
    // Such a request to close is marked as failed and the alert suppressed using special status code
    EXISTING_LIEN = 105;

    // If user changes their primary mobile number externally from fed Netbanking or any other platforms
    // Federal will deactivate the Fi device for making further device specific txns like create or close Deposit
    DEVICE_DEACTIVATED = 106;

    // If user is an existing Federal bank customer before opening an account with Fi, occupation details of the user might not
    // be updated at bank side when opening an account with Fi, using profile update API we can update the details.
    OCCUPATION_DETAILS_NOT_UPDATED_AT_BANK = 107;

  }

  rpc.Status status = 1;

  // response code as sent by the vendor bank
  string raw_response_code = 2;

  // Success text if request received by vendor is successful
  // Otherwise it will contain reason for failure
  string raw_response_description = 3;

  // epifi status code corresponding to the raw status code sent by the vendor
  string status_code = 4;

  // description of the epifi status code
  string status_description = 5;

  // transaction timestamp
  google.protobuf.Timestamp transaction_timestamp = 6;

  // info of the queried requestId ( transaction ) of close/create account if any
  TransactionDetails transaction_details = 7;

  // in case of failure, reason for failure
  api.typesv2.DepositFailureReason failure_reason = 8;

  // struct to hold details on maturity of deposit account
  message TransactionDetails {
    // account number of created/closed deposit account
    string account_number = 1;

    // amount after maturity of account
    google.type.Money maturity_amount = 2;

    // date of maturity of account
    google.protobuf.Timestamp maturity_date = 3;

    // response code for request
    string response_code = 4;

    // `SUCCESS` if deposit account creation is success
    // failure reason if failed to create deposit account
    string response_reason = 5;

    // Action to perform after on this response
    string response_action = 6;

    // deposit closure attributes
    // Actual Deposit Amount
    google.type.Money original_deposit_balance = 7;
    // interest earned on the deposit amount
    google.type.Money interest_amount = 8;
    // OriginalDepositBalance + InterestAmount
    google.type.Money gross_amount = 9;
    // TDS on interest earned
    google.type.Money tds_amount = 10;
    // GrossAmount-TDSAmount-PreClosureAmount
    google.type.Money net_amount = 11;
    // Pre Closure Charge Amount (Amount charged for closing the deposit before maturity)
    google.type.Money pre_closure_amount = 12;
    // Date of deposit pre/auto closure
    google.protobuf.Timestamp close_value_date = 13;
  }
}

// request struct for GetAccountDetails
message GetAccountDetailRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique identifier for the request.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in response.
  string request_id = 3;

  // unique identifier of customer
  string customer_id = 4;

  // type of account like saving account, deposit, loan account etc.
  accounts.Type account_type = 5;

  // deposit account number
  string deposit_account_number = 6;

  // mobile number of customer
  api.typesv2.common.PhoneNumber phone_number = 7;
}

message GetAccountDetailResponse {
  // Unique identifier of the request. This is same id from the request to map response.
  string request_id = 1;

  // `SUCESS` for success response otherwise it will contain failure reason.
  string response_reason = 2;

  // Account details with complete info of account
  AccountDetails account_details = 3;

  // TODO(vivek): Add complete list of enum returned for vendor.
  enum Status {
    // Returned an success
    OK = 0;
    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // The request does not have valid authentication credentials for the
    // operation.
    // Reason: Invalid cred block - Either card details or Otp or both
    // that are part of cred block are wrong
    UNAUTHENTICATED = 16;
  }
  rpc.Status status = 4;
}

// request struct to request list of deposit account
message ListAccountRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique identifier for the request.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in response.
  string request_id = 3;

  // Unique identifier for customer.
  string customer_id = 4;

  // registered phone number of customer
  api.typesv2.common.PhoneNumber phone_number = 5;
}

// response struct for list of account request
message ListAccountResponse {
  // rpc status
  rpc.Status status = 1;

  // Unique identifier for the request.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in response.
  string request_id = 2;

  // list of account
  repeated Account accounts = 3;

  // List of status codes returned
  // TODO(vivek): Add complete list of enum returned for vendor.
  enum Status {
    // Returned an success
    OK = 0;

    // unknown rpc status.
    // One of the reason could be vendor returned unknown error code in response
    UNKNOWN = 2;

    // Indicates that arguments are problematic
    INVALID_ARGUMENT = 3;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // The request does not have valid authentication credentials for the
    // operation.
    // Reason: Invalid cred block - Either card details or Otp or both
    // that are part of cred block are wrong
    UNAUTHENTICATED = 16;
  }

  // Account details for list account
  message Account {
    // account number of created deposit accounts
    string account_number = 1;

    // name of customer attached to account
    string customer_name = 2;

    // type of account
    accounts.Type account_type = 3;

    // bool to represent if account is still active
    // true -> account is still active
    // false -> inactive account
    bool is_active = 4;

    // account auto close flag
    // true -> account will be automatically close after validity periods
    // false -> account will not be automatically close
    bool is_auto_close = 5;

    // Every deposit account is created with a scheme.
    // SchemeType is the type of scheme for the account.
    vendorgateway.openbanking.deposit.SchemeType schemeType = 6;
  }
}

message CalculateInterestDetailsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique identifier for customer.
  string customer_id = 3;

  // Unique identifier for the request.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in response.
  string request_id = 4;

  // creation date of the deposit
  google.type.Date creation_date = 5;

  // amount for which the deposit is created
  google.type.Money amount = 6;

  // term of the deposit
  DepositTerm term = 7;

  // scheme type the deposit
  SchemeType scheme_type = 8;
}

message DepositTerm {
  int32 months = 1;
  int32 days = 2;
}

message CalculateInterestDetailsResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }

  // rpc status
  rpc.Status status = 1;

  google.type.Money maturity_amount = 2;
  google.type.Date maturity_date = 3;

  double interest_rate = 4;
  google.type.Money interest_amount = 5;

  string interest_code = 6;
}

message GetPreClosureDetailsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Unique identifier for the request.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in response.
  //  it is alphanumeric value
  string request_id = 2;

  string account_id = 3;
  // represent time stamp at which request was initiated
  google.protobuf.Timestamp requested_at = 4;
}

message GetPreClosureDetailsResponse {
  rpc.Status status = 1;
  PreClosureFinancialInfo pre_closure_financial_info = 2;
}

message PreClosureFinancialInfo {
  // Represents the amount that the user will receive after pre-closure
  google.type.Money maturity_amount = 2;
  // this the interest rate of effective interest rate of user receiving amount
  double interest_rate_after_penalty = 3;
  // represent interest paid to user
  google.type.Money interest_paid = 4;
  // represent the total interest gain by user via deposit
  google.type.Money total_interest = 5;
}

message AutoRenewFdRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // registered device info of user/customer
  header.Auth auth = 2;

  // Unique identifier for the request.
  // This request id will be used to map the call back response received by the caller.
  // If same requestId will be passed two times then second request will
  // get duplicate requestId error in ack response.
  string request_id = 3;

  // Basic info of customer like title,name,contact info
  CustomerInfo customer_info = 4;

  // account info for customer
  CustomerAccountInfo customer_account_info = 5;

  // Scheme details has set of attributes for auto Renewal of FD, deposit duration, amount etc.
  SchemeDetails scheme_details = 6;
}

message AutoRenewFdResponse {
  // List of status codes returned
  enum Status {
    // Returned a success
    OK = 0;

    // The state will be returned in case epiFi gets some unexpected
    // response code from partner bank.
    //
    // The caller should check the status using deposit enquiry
    UNKNOWN = 2;

    // System faced internal errors while processing the request
    INTERNAL = 13;

    // Auto Renewal has failed
    // Failure has specific status code so as to differ from system errors like unmarshalling, decryption etc
    // which will be classified as INTERNAL error
    FAILED = 100;

    // This signifies that the request is being processed
    IN_PROGRESS = 101;
  }

  rpc.Status status = 1;

  // response code as sent by the vendor bank
  string raw_response_code = 2;

  // Success text if request received by vendor is successful
  // Otherwise it will contain reason for failure
  string raw_response_description = 3;

  // epifi status code corresponding to the raw status code sent by the vendor
  string status_code = 4;

  // description of the epifi status code
  string status_description = 5;

  // transaction timestamp
  google.protobuf.Timestamp transaction_timestamp = 6;
}
