syntax = "proto3";

package vendorgateway.openbanking.deposit;

import "google/type/money.proto";
import "api/typesv2/deposit.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/deposit";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.deposit";

enum InterestRateCategory {
  INTEREST_RATE_CATEGORY_UNSPECIFIED = 0;
  INTEREST_RATE_CATEGORY_GENERAL_PUBLIC = 1;
  INTEREST_RATE_CATEGORY_SENIOR_CITIZEN = 2;
}

message InterestRateEntry {
  api.typesv2.DepositTerm end_period = 1;
  google.type.Money max_slab_amount = 2;
  double interest_rate = 3;
}
