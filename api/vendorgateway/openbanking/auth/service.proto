// protolint:disable MAX_LINE_LENGTH

// Vendor gateway APIs for registering user's device with a partner bank as a
// first factor for authentication.

syntax = "proto3";

package vendorgateway.openbanking.auth;

import "api/rpc/status.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/openbanking/header/auth.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vendor_status.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/auth";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.auth";


// A service for authentication related APIs for partner integrations
service VendorAuth {
  // API for registering a device for a user as a first factor for auth with
  // a partner. This registration follows a 2 step process.
  // 1. Application initiates a SMS from the user's mobile number to a partner
  //    phone number. A payload is included in the SMS.
  // 2. After sending the SMS, the application sends the same payload to epiFi
  //    server to be forwarded to the partner via the backend. Partner systems
  //    match the payload and the phone number in this API and the phone number
  //    and payload in the SMS and return back a device token that can be sent
  //    in subsequent API calls to the partner as a first factor of
  //    authentication.
  rpc RegisterDevice (RegisterDeviceRequest) returns (RegisterDeviceResponse);

  // AuthFactorUpdate updates a user's auth factors
  // (which are phone number, email and device) with the partner bank.
  // [Doc](https://docs.google.com/document/d/1V83WIwb8JDQyVfmvfbEm9RstciDwvmxG4XTmdbVYAKk)
  rpc AuthFactorUpdate (AuthFactorUpdateRequest) returns (AuthFactorUpdateResponse);

  // API for de-registering user's registered device Temporarily or Permanently
  rpc DeRegisterDevice (DeRegisterDeviceRequest) returns (DeRegisterDeviceResponse);

  // API for sending otp to registered mobile number
  rpc GenerateOTP (GenerateOTPRequest) returns (GenerateOTPResponse);

  // API for reactivating a temporarily deactivate user profile/device.
  rpc ReactivateDevice (ReactivateDeviceRequest) returns (ReactivateDeviceResponse);

  rpc GetAuthFactorUpdateStatus (GetAuthFactorUpdateStatusRequest) returns (GetAuthFactorUpdateStatusResponse);

  // GetRegisterDeviceStatus is the status check API for RegisterDevice.
  rpc GetRegisterDeviceStatus (GetRegisterDeviceStatusRequest) returns (GetRegisterDeviceStatusResponse);
}


// Request for registering a device for a user as a first factor for auth.
message RegisterDeviceRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Id/fingerprint of the device that needs to be registered.
  string device_id = 2;

  // The phone number used by the user for sending the SMS to the bank.
  api.typesv2.common.PhoneNumber phone = 3;

  // A payload that is sent by the mobile application in an SMS to a partner
  // mobile number and also send via an API for partner systems to map a user
  // and a device.
  string payload = 4;

  // This is an opaque id from our system that identifies a user and is stored
  // associated with the partner's internal user ids.
  string user_profile_id = 5;

  // email id of the user
  string email_id = 6;

  // vendor API call request identifier to enable tracing
  string request_id = 7;
}

message RegisterDeviceResponse {
  // A device token generated by the partner. Needs to be sent in subsequent
  // requests as the first factor for auth.
  string device_token = 1;

  enum Status {
    OK = 0;
    // TODO(keerthana) : Add status code later once closed on exact codes.
  }

  rpc.Status status = 2;
  vendorgateway.VendorStatus vendor_status = 3;
}

// Request for AuthFactorUpdate API
message AuthFactorUpdateRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // A payload that is sent by the mobile application in an SMS to a partner
  // mobile number and also send via an API for partner systems to map a user
  // and a device.
  string payload = 2;

  // The phone number used by the user for sending the SMS to the bank.
  api.typesv2.common.PhoneNumber phone = 3;

  // email of the user
  string email = 4;

  // list of auth factors that user wants to change
  repeated AuthFactor changing_entities = 5;

  // device details of the customer
  header.Auth device_details = 6;

  // string debit card pin cred block
  string cred_block = 7;

  // debit card id corresponding to the cred block
  string vendor_card_id = 8;

  // requestId for the device re-registration request
  string request_id = 9;

  // pin validation flag is processed based on the afu flow type
  AFUFlowType afu_flow_type = 10;
}

// Response for AuthFactorUpdate API
message AuthFactorUpdateResponse {
  enum Status {
    OK = 0;

    CANCELLED = 1;

    INVALID_ARGUMENT = 3;

    DEADLINE_EXCEEDED = 4;

    PERMISSION_DENIED = 7;

    FAILED_PRECONDITION = 9;

    INTERNAL = 13;

    DUPLICATE_REQUEST = 100;

    // Mobile Number mismatch
    MOBILE_NUMBER_MISMATCH = 101;

    // EncryptedPayload mismatch
    ENCRYPTED_PAYLOAD_MISMATCH = 103;

    // No SMS received
    NO_SMS_RECEIVED = 105;

    // Internal error
    // Can be caused due to improper cred block decryption due to parsing errors
    INTERNAL_ERROR_AT_VENDOR = 106;

    // Error parsing cred block at vendor
    INVALID_CRED_BLOCK = 107;

    UNKNOWN_FAILURE = 999;
  }

  rpc.Status status = 1;
}

// Request for DeRegisterDevice API
message DeRegisterDeviceRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Id/fingerprint of the device that needs to be de-registered.
  string device_id = 2;

  // This is an opaque id from our system that identifies a user and is stored
  // associated with the partner's internal user ids.
  string user_profile_id = 3;

  // The phone number used by the user for sending the SMS to the bank.
  api.typesv2.common.PhoneNumber phone = 4;

  // email of the user
  string email = 5;

  // type of de-registration the user wants
  Type de_registration_type = 6;

  // device token generated by the partner.
  string device_token = 7;

  // Type here enumerates the types of de-registrations that a user can do
  enum Type {
    TYPE_UNSPECIFIED = 0;
    PERMANENT = 1;
    TEMPORARY = 2;
  }
}

// Response for DeRegistration API
message DeRegisterDeviceResponse {
  enum Status {
    OK = 0;

    INVALID_ARGUMENT = 3;

    PERMISSION_DENIED = 7;

    INTERNAL = 13;

    UNAUTHENTICATED = 16;

    DEVICE_ALREADY_DEACTIVATED = 100;
  }

  rpc.Status status = 1;
}


// AuthFactor represents the credentials that user
// can update with the partner bank.
// Derived from AuthFactor in api/auth/internal/auth_factor_update.proto
enum AuthFactor {
  AUTH_FACTOR_UNSPECIFIED = 0;

  // Device ID mapped to a user's account changed.
  DEVICE = 1;

  // Mobile Number
  PHONE_NUM = 2;

  EMAIL = 3;

  // Sim change on user's mobile device.
  SIM = 4;
}

message GenerateOTPRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // A set of authentication attributes that are common across Open banking requests
  header.Auth auth = 2;

  // Caller function should pass unique value to each call, to identify the request
  // There won't be any explicit uniqueness checks that are performed by the RPC implementation
  // Uniqueness is expected to hold across all the requests to a partner bank
  string request_id = 3;

  // Account number defined by the partner bank
  string account_number = 4;

  // Email ID of the account owner
  string email = 5;

  // Phone number of the account owner where otp is to be sent
  api.typesv2.common.PhoneNumber phone = 6;
}

message GenerateOTPResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  // response code received from vendor
  string vendor_response_code = 3;
  // response reason received from vendor
  string vendor_response_reason = 4;
}

message ReactivateDeviceRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Id/fingerprint of the device that needs to be re-activated.
  string device_id = 2;

  // This is an opaque id from our system that identifies a user and is stored
  // associated with the partner's internal user ids.
  string user_profile_id = 3;

  // The phone number used by the user for sending the SMS to the bank.
  api.typesv2.common.PhoneNumber phone = 4;

  // email of the user
  string email = 5;

  // device token generated by the partner.
  string device_token = 6;
}

message ReactivateDeviceResponse {
  enum Status {
    OK = 0;

    INVALID_ARGUMENT = 3;

    PERMISSION_DENIED = 7;

    INTERNAL = 13;

    UNAUTHENTICATED = 16;

    DEVICE_ALREADY_ACTIVE = 100;

    INVALID_DEVICE_TOKEN = 101;

    // for response code "OBE0170" from vendor, "Device is deactivated temporary by bank"
    DEVICE_TEMPORARILY_DEACTIVATED = 102;
  }

  rpc.Status status = 1;
}

message GetAuthFactorUpdateStatusRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Device ID of the client that has initiated the request
  string device_id = 2;

  string user_profile_id = 3;

  // Device token that is issued by the federal bank at the time of device registration
  string device_token = 4;

  // request id of the auth factor update request
  string request_id = 5;

  // The phone number used by the user for sending the SMS to the bank.
  api.typesv2.common.PhoneNumber phone = 6;
}

message GetAuthFactorUpdateStatusResponse {
  enum Status {
    OK = 0;

    INVALID_ARGUMENT = 3;

    RECORD_NOT_FOUND = 5;

    PERMISSION_DENIED = 7;

    INTERNAL = 13;

    DEVICE_TEMPORARILY_DEACTIVATED = 100;
  }

  enum AFUStatus {
    AFU_OK = 0;

    AFU_INVALID_ARGUMENT = 3;

    AFU_RECORD_NOT_FOUND = 5;

    AFU_PERMISSION_DENIED = 7;

    AFU_FAILED_PRECONDITION = 9;

    AFU_INTERNAL = 13;

    AFU_UNAUTHENTICATED = 16;

    AFU_DUPLICATE_REQUEST = 100;

    AFU_REQUEST_UNPROCESSED = 101;

    AFU_WRONG_ATM_PIN = 102;

    AFU_ATM_PIN_MAX_RETRIES = 103;

    AFU_CARD_TXN_REQUIRED = 104;

    // card not active, pin needs to be set
    AFU_CARD_INACTIVE = 105;
    // user's kyc details not updated
    AFU_KYC_NON_COMPLIANT = 106;
    // card is not linked to old number
    AFU_CARD_NOT_LINKED_TO_OLD_NUMBER = 107;
    // card is hot listed
    AFU_LOST_CARD = 108;
    // card is not linked to customer
    AFU_CARD_NOT_LINKED_TO_CUSTOMER = 109;
    // no card record
    AFU_NO_CARD_RECORD = 110;
    // account is inactive
    AFU_ACCOUNT_INACTIVE = 111;
    // user has changed the mobile number independently with bank/vendor and on Fi we have the old number
    AFU_DEVICE_TEMPORARILY_DEACTIVATED = 112;
    // Internal error at vendor in reregistration enquiry, contact vendor admin
    AFU_ENQUIRY_INTERNAL_ERROR_AT_VENDOR = 113;
    // Permanent genreric failure at vendor
    AFU_FAILED_AT_VENDOR = 114;
  }

  // status of the enquiry request
  rpc.Status status = 1;

  // status of the device re-registration request
  rpc.Status auth_factor_update_status = 2;

  // A device token generated by the partner. Needs to be
  // sent in subsequent requests as the first factor for auth.
  string device_token = 3;

  vendorgateway.VendorStatus vendor_status = 4;
}

message GetRegisterDeviceStatusRequest {
  vendorgateway.RequestHeader header = 1;

  string device_id = 2;

  string user_profile_id = 3;

  api.typesv2.common.PhoneNumber phone_number = 4;

  // request id of the RegisterDevice request
  string original_req_id = 5;
}

message GetRegisterDeviceStatusResponse {
  rpc.Status status = 1;

  // A device token generated by the partner. Needs to be
  // sent in subsequent requests as the first factor for auth.
  string device_token = 3;
  vendorgateway.VendorStatus vendor_status = 4;
}


enum AFUFlowType {
  AFU_FLOW_TYPE_UNSPECIFIED = 0;

  // single auth factor update flow
  AFU_FLOW_TYPE_SINGLE_AUTH_FACTOR_UPDATE = 1 [deprecated = true];

  // when single afu is attempted with ATM pin validation
  AFU_FLOW_TYPE_SINGLE_AUTH_FACTOR_UPDATE_WITH_CREDBLOCK = 2;

  // when single afu is attempted without ATM pin validation
  AFU_FLOW_TYPE_SINGLE_AUTH_FACTOR_UPDATE_WITHOUT_CREDBLOCK = 3;
}
