// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendorgateway.openbanking.header;

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/header";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.header";


// A set of authentication attributes that are common across Open banking requests
message Auth {
  // Device ID or fingerprint of the device that is registered with the partner bank
  string device_id = 1;

  // A device token generated by the partner. Acts as the first factor for auth
  string device_token = 2;

  // Encrypted PIN block that may be required to authorise the transaction
  // This PIN is to be entered by the user at the time of transaction initiation on the client application
  // PIN is then encrypted by Client application and tunnelled all the way from the app to partner bank
  //
  // Partner bank evaluates the risk of transaction based on multiple attributes e.g:
  //  - Amount of the transaction
  //  - Time when the transaction is initiated
  //  - Location from where the transaction is initiated
  // Based on the evaluated risk, `encrypted_pin` may be required to complete the transaction
  // In absence of `encrypted_pin` being set, appropriate error may be returned by the partner bank,
  // if `encrypted_pin` is required
  //
  // Client application has to handle this gracefully and raise a new request including `encrypted_pin`
  string encrypted_pin = 3;

  // User ID assigned to a user by epiFi
  string user_profile_id = 4;

  // customer id of user provided by partner bank
  string customer_id = 5;
}
