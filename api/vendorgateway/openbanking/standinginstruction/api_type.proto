// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendorgateway.openbanking.standinginstruction;


option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/standinginstruction";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.standinginstruction";

// ApiType represents a specific type of API
// This is needed to convert raw status code of an API to epifi status code
enum ApiType {
  API_TYPE_UNSPECIFIED = 0;

  CREATE_SI = 1;

  EXECUTE_SI = 2;

  MODIFY_SI = 3;

  REVOKE_SI = 4;

}
