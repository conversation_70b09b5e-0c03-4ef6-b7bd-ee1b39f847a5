// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package vendorgateway.openbanking.upi;

import "google/protobuf/timestamp.proto";
import "api/upi/qr_details.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/openbanking/upi";
option java_package = "com.github.epifi.gamma.api.vendorgateway.openbanking.upi";


// A set of common transaction attributes shared across all UPI requests
// contains transaction information, carried throughout the system, visible to all parties
message TransactionHeader {

  //  Unique Identifier of the transaction across all entities, created by the originator
  string transaction_id = 1;

  // Description of the transaction(which will be printed on Pass book)
  string note = 2;

  // Consumer reference number to identify a customer(like Loan number, etc.)
  string cust_ref_id = 3;

  // URL for the transaction
  // TODO(nitesh): add more details when we have more clarity over the fields
  string ref_url = 4;

  // Transaction origination time by the creator of the message
  google.protobuf.Timestamp trans_timestamp = 5;

  // TODO(nitesh): add description when we have more clarity
  string initiation_mode = 6;

  string ref_id = 7;

  // purpose of the transaction
  string purpose = 8;

  // umn to be passed in case of mandates
  string umn = 9;

  //Original txn id of the request. Will be populated in case of flows like update/revoke/pause of mandates
  string OriginalTxnId = 10;

  // ref category of the request
  string ref_category = 11;

  .upi.QRDetails qr_details = 12;

  // original rrn for the txn
  // context: received in cases of pre debit notification of mandate.
  // we need to pass it back in the RespAuthValCust req while responding to the vendor.
  string org_rrn = 13;
}

// A set of common response attribute shared across all UPI response interfaces.
message ResponseHeader {
  string req_msg_id = 1;

  enum Result {
    RESULT_UNSPECIFIED = 0;

    SUCCESS = 1;

    FAILURE = 2;
  }

  Result result = 2;
  // err code for the response
  string err_code = 3;
}
