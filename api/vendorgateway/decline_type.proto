syntax = "proto3";

package vendorgateway;

option go_package = "github.com/epifi/gamma/api/vendorgateway";
option java_package = "com.github.epifi.gamma.api.vendorgateway";

// Decline type for the vendor requests
// Deprecated: Use api.typesv2.common.DeclineType instead
enum DeclineType {
  // unspecified
  DECLINE_TYPE_UNSPECIFIED = 0 [deprecated = true];

  // technical decline
  // the vendor operation was declined due to technical error
  // for eg. CBS exception
  TECHNICAL = 1 [deprecated = true];

  // the vendor operation was declined due to a business error
  // which occur mostly due to wrong information or inputs provided by the user.
  // for eg. wrong MPIN entered by user
  BUSINESS = 2 [deprecated = true];
}
