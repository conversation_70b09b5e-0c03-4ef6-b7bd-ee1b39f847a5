syntax = "proto3";

package vendorgateway.esign;

import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/esign/templates.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/esign/enums.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/esign";
option java_package = "com.github.epifi.gamma.api.vendorgateway.esign";

service ESign {
  rpc CreateESign (CreateESignRequest) returns (CreateESignResponse);
  rpc CheckESignStatus (CheckESignStatusRequest) returns (CheckESignStatusResponse);
}

message CreateESignRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;
  string internal_reference_number = 2;
  api.typesv2.esign.Template template = 3;
  EsignRequestClient client = 4;
}

message CreateESignResponse {
  rpc.Status status = 1;
  Data data = 2;

  message Data {
    string document_id = 1;
    string irn = 2;
    Invitee invitee = 3;

    message Invitee {
      string name = 1;
      string email = 2;
      api.typesv2.common.PhoneNumber phone_number = 3;
      string sign_url = 4;
      bool active = 5;
      google.protobuf.Timestamp expiry_timestamp = 6;
    }
  }
}

message CheckESignStatusRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  string document_id = 2;
  EsignRequestClient client = 3;
}

message CheckESignStatusResponse {
  rpc.Status status = 1;
  Data data = 2;
  repeated Messages messages = 3;

  message Data {
    string document_id = 1;
    string irn = 2;
    CustomerDetails customer_details = 3;
    EsignDetails esign_details = 4;
    repeated Signer signers = 5;

    message CustomerDetails {
      string name = 1;
      string email = 2;
      api.typesv2.common.PhoneNumber phone_number = 3;
    }

    message EsignDetails {
      string sign_url = 1;
      google.protobuf.Timestamp expiry_timestamp = 2;
      string sign_type = 3;
      EsignStatus status = 4;
    }

    message Signer {
      string name = 1;
      api.typesv2.Addresses address = 2;
    }
  }

  message Messages {
    Code code = 1;
    string message = 2;
  }

  enum Code {
    CODE_UNSPECIFIED = 0;
    CODE_DOCUMENT_NOT_FOUND = 1;
  }
}

enum EsignRequestClient {
  ESIGN_REQUEST_CLIENT_UNSPECIFIED = 0;
  ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN = 1;
  ESIGN_REQUEST_CLIENT_SAVINGS_ACCOUNT = 2;
  ESIGN_REQUEST_CLIENT_PRE_APPROVED_LOAN_V2 = 3;
}
