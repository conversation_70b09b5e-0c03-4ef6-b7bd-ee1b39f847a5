syntax = "proto3";

package vendorgateway.whatsapp;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";

// go_package needs to be fully qualified for protoc to work from the workspace
// directory.
option go_package = "github.com/epifi/gamma/api/vendorgateway/whatsapp";
option java_package = "com.github.epifi.gamma.api.vendorgateway.whatsapp";


// WhatsApp provides the service of sending a message to a user on whats app
service WhatsApp {
  // Sends a whatsapp message to a provided phone number
  rpc SendMessage (SendMessageRequest) returns (SendMessageResponse);

  // rpc to mark user as Opted in on vendor side for whatsapp
  // returns error if user
  rpc OptInUser (OptInUserRequest) returns (OptInUserResponse);
}

message SendMessageRequest {
  vendorgateway.RequestHeader header = 1;

  // Phone number from which the message is to be sent
  // mandatory
  string from_number = 2;

  // Phone number to which the message is to be sent
  // mandatory
  string phone_number = 3;

  // type of message
  // mandatory
  MessageType message_type = 4;

  oneof message_option {
    // this should be passed if message type is TEXT
    TextMessageOption text_message_option = 5;
    // this should be passed in case of message type MEDIA
    MediaMessageOption media_message_option = 6;
    // this should be passed in case we have template id and param map
    TemplateMessageOption template_message_option = 7;
    // For media template messages
    MediaTemplateMessageOption media_template_message_option = 8;
  }
}

enum MessageType {
  MESSAGE_TYPE_UNSPECIFIED = 0;
  // should be used for text messages
  TEXT = 1;
  // should be used if media content is to be sent
  MEDIA = 2;
  // templated messages
  TEMPLATE = 3;
  // media template message
  MEDIA_TEMPLATE = 4;
}

message TextMessageOption {
  // content of the text message
  string text_content = 1;
  // will indicate if URL link must display as preview on recipient phone in case message contains a valid url
  bool preview_url = 2;
}

message MediaMessageOption {
  // content type
  // mandatory
  // supported values
  // image/jpg, image/jpeg, image/png for image
  // application/pdf, application/msword, application/vnd.ms-powerpoint, application/vnd.ms-excel for documents
  // audio/aac, audio/mp4, audio/mp3, audio/amr, audio/ogg for audio
  string content_type = 1;
  // mandatory
  // url of the media resource
  // should be a public url
  // resource type should match with the content type above
  string media_url = 2;

  // optional
  // caption that needs to be shown with the image or document
  // don't use this with audio content types
  string caption = 3;
}

// To be used in case we have template id and variables
message TemplateMessageOption {
  string template_id = 1;
  map<string, string> param_map = 2;
}

// To be used in case we are sending a Templated media message
// User will receive the media along with template message configured. Template whitelisting is required in advance
message MediaTemplateMessageOption {
  // Location of the media file.
  // The file format should correspond to the content_type
  // While the maximum file size for media that can be uploaded to the media node is 64MB,
  // there are post-processing limits for the various media types outlined in the Post-Processing Media Size table below.
  //   Media Type  |  Size
  //   audio       |  16 MB
  //   document    |  100 MB
  //   image       |  5 MB
  //   sticker     |  100 KB
  //   video       |  16 MB
  string media_url = 1;
  // content type of the media file. For supported types ref MediaContentType enum
  MediaContentType content_type = 2;
  string template_id = 3;
  map<string, string> param_map = 4;
  // File name for the media to be shown on the Whatsapp message to the user
  // This applicable based on the media type.
  // e.g., the name will not be shown for image type media whereas it will be shown for document type like PDF
  string media_file_name = 5;
}

// Enum representing all the supported media types for a whatsapp message
// Media    | Supported File Formats | Associated content Type
// Document | PDF                    | application/pdf
//          | DOC                    | application/msword
//          | PPT                    | application/vnd.ms-pwerpoint
//          | Excel                  | application/vnd.ms-excel
// Image    | PNG                    | image/png
//          | JPEG                   | image/jpeg
// Video    | MP4                    | video/mp4
//          | 3gpp                   | video/3gpp
// Audio    | aac                    | audio/acc
//          | Mp4                    | audio/mp4
//          | amr                    | audio/amr
//          | mpeg                   | audio/mpeg
//          | ogg                    | audio/ogg
// Sticker  | Webp                   | Image/webp
enum MediaContentType {
  MEDIA_CONTENT_TYPE_UNSPECIFIED = 0;
  // application/pdf
  MEDIA_CONTENT_TYPE_DOCUMENT_PDF = 1;
  // application/msword
  MEDIA_CONTENT_TYPE_DOCUMENT_DOC = 2;
  // application/vnd.ms-pwerpoint
  MEDIA_CONTENT_TYPE_DOCUMENT_PPT = 3;
  // application/vnd.ms-excel
  MEDIA_CONTENT_TYPE_DOCUMENT_EXCEL = 4;
  // image/png
  MEDIA_CONTENT_TYPE_IMAGE_PNG = 5;
  // image/jpeg
  MEDIA_CONTENT_TYPE_IMAGE_JPEG = 6;
  // video/mp4
  MEDIA_CONTENT_TYPE_VIDEO_MP4 = 7;
  // video/3gpp
  MEDIA_CONTENT_TYPE_VIDEO_3GPP = 8;
  // audio/acc
  MEDIA_CONTENT_TYPE_AUDIO_AAC = 9;
  // audio/mp4
  MEDIA_CONTENT_TYPE_AUDIO_MP4 = 10;
  // audio/amr
  MEDIA_CONTENT_TYPE_AUDIO_AMR = 11;
  // audio/mpeg
  MEDIA_CONTENT_TYPE_AUDIO_MPEG = 12;
  // audio/ogg
  MEDIA_CONTENT_TYPE_AUDIO_OGG = 13;
  // Image/webp
  MEDIA_CONTENT_TYPE_STICKER_WEBP = 14;
}

message SendMessageResponse {
  rpc.Status status = 1;

  // The message id that is returned by the cloud communication service
  // Used for getting the status of the message in the future
  string message_id = 2;

  // in case of vendor side failure error details will be passed here
  repeated ErrorDetail error_details = 3;
}

message ErrorDetail {
  string error_code = 1 [json_name="errorCode"];
  string error_description = 2 [json_name="errorDescription"];
}

message OptInUserRequest {
  vendorgateway.RequestHeader header = 1;
  // Phone number of user
  // mandatory
  string phone_number = 2;
}

message OptInUserResponse {
  rpc.Status status = 1;
}
