/*
Protos related to the FITTT Cricket api's.
*/
syntax = "proto3";

package vendorgateway.fittt;

import "api/vendorgateway/request_header.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "api/vendorgateway/fittt/cricket.proto";
import "api/vendorgateway/fittt/football.proto";
import "api/vendorgateway/fittt/sports.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/fittt";
option java_package = "com.github.epifi.gamma.api.vendorgateway.fittt";

service Fittt {
  // RPC to fetch list of featured cricket tournaments from vendor
  rpc FetchFeaturedCricketTournaments(FetchFeaturedCricketTournamentsRequest) returns (FetchFeaturedCricketTournamentsResponse) {}
  // RPC to fetch details about a tournament and Teams. Tournament returned from this RPC will not include matches.
  // Use FetchCricketTournamentFixtures RPC to get matches.
  rpc FetchCricketTournament(FetchCricketTournamentRequest) returns (FetchCricketTournamentResponse) {}
  // RPC to fetch details about a tournament including the matches scheduled for the tournament.
  rpc FetchCricketTournamentFixtures(FetchCricketTournamentFixturesRequest) returns (FetchCricketTournamentFixturesResponse) {}
  // RPC to fetch Team participating in a tournament along with player details from vendor
  rpc FetchCricketTeam(FetchCricketTeamRequest) returns (FetchCricketTeamResponse) {}
  // FetchMatchDetails rpc fetches match details from vendor such as match score board,
  // roles and scores of each batter/bowler along with the final results of the match.
  rpc FetchCricketMatchDetails(FetchCricketMatchDetailsRequest) returns (FetchCricketMatchDetailsResponse) {}

  // RPC to fetch list of recent football tournaments from vendor
  rpc FetchRecentFootballTournaments(FetchRecentFootballTournamentsRequest) returns (FetchRecentFootballTournamentsResponse) {}
  // RPC to fetch details about a tournament and rounds. Tournament returned from this RPC will not include matches.
  // Use FetchFootballRoundFixtures RPC to get matches in a round.
  rpc FetchFootballTournament(FetchFootballTournamentRequest) returns (FetchFootballTournamentResponse) {}
  // RPC to fetch details about a tournament including the matches scheduled for the tournament.
  rpc FetchFootballRoundFixtures(FetchFootballRoundFixturesRequest) returns (FetchFootballRoundFixturesResponse) {}
  // RPC to fetch Team participating in a tournament along with player details from vendor
  rpc FetchFootballTeam(FetchFootballTeamRequest) returns (FetchFootballTeamResponse) {}
  rpc FetchFootballMatchDetails(FetchFootballMatchDetailsRequest) returns (FetchFootballMatchDetailsResponse) {}
}



message FetchCricketMatchDetailsRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // unique match identifier for each match
  string match_id = 2;

  // no need to be passed from client as it is getting populated at VG
  string access_token = 3;
}

message FetchCricketMatchDetailsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  // map of team with team id
  map<string, Team> teams = 2;

  google.protobuf.Timestamp match_start_at = 3;

  MatchStatus match_status = 4;

  CricketMatchResult match_result = 5;

  // map of player with player id
  map<string, CricketPlayer> players = 6;

  // estimated time at which match is to be completed
  google.protobuf.Timestamp estimated_match_end = 7;

  // approximate time at which match actually ended
  google.protobuf.Timestamp approximate_match_end = 8;

  // stats that are not entirely associated with single batter or bowler.
  map<uint32, InningsStats> innings_stats = 9;

  bytes raw_vendor_resp = 10;
}

message FetchFootballMatchDetailsRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // unique match identifier for each match
  string match_id = 2;

  // no need to be passed from client as it is getting populated at VG
  string access_token = 3;
}

message FetchFootballMatchDetailsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  // map of team with team id
  map<string, Team> teams = 2;

  google.protobuf.Timestamp match_start_at = 3;

  MatchStatus match_status = 4;

  FootballMatchResult match_result = 5;

  // map of player with player id
  map<string, FootballPlayer> players = 6;

  // approximate time at which match actually ended
  google.protobuf.Timestamp approximate_match_end = 7;

  bytes raw_vendor_resp = 8;
}



message FetchFeaturedCricketTournamentsRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Field is set by Vendorgateway
  string access_token = 2;
}

message FetchFeaturedCricketTournamentsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  repeated Tournament tournaments = 2;
}

message FetchCricketTournamentRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Field is set by Vendorgateway
  string access_token = 2;

  string tournament_key = 3;
}

message FetchCricketTournamentResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  Tournament tournament = 2;
}


message FetchRecentFootballTournamentsRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Field is set by Vendorgateway
  string access_token = 2;
}

message FetchRecentFootballTournamentsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  repeated Tournament tournaments = 2;
}


message FetchFootballTeamRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Field is set by Vendorgateway
  string access_token = 2;

  string team_key = 3;
}

message FetchFootballTeamResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  Team team = 2;
}

message FetchCricketTeamRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Field is set by Vendorgateway
  string access_token = 2;

  string tournament_key = 3;

  string team_key = 4;
}

message FetchCricketTeamResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  Team team = 2;
}


message FetchCricketTournamentFixturesRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Field is set by Vendorgateway
  string access_token = 2;

  string tournament_key = 3;

  int32 page_key = 4;
}

message FetchCricketTournamentFixturesResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  repeated Match matches = 2;
  int32 NextPageKey = 3;
  int32 PrevPageKey = 4;
}


message FetchFootballTournamentRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Field is set by Vendorgateway
  string access_token = 2;

  string tournament_key = 3;
}

message FetchFootballTournamentResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  Tournament tournament = 2;
}


message FetchFootballRoundFixturesRequest {
  // Common request header across all vendorgateway APIs.
  vendorgateway.RequestHeader header = 1;

  // Field is set by Vendorgateway
  string access_token = 2;

  string tournament_key = 3;
  string round_key = 4;
}

message FetchFootballRoundFixturesResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;

  Round round = 2;
}
