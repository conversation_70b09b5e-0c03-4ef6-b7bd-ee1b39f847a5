// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.fennel;


option go_package = "github.com/epifi/gamma/api/vendorgateway/fennel";

// Type of Request Identifier against which features will be extracted example actor_id, account_id
enum IdentifierType {
  IDENTIFIER_TYPE_UNSPECIFIED = 0;
  IDENTIFIER_TYPE_ACTOR_ID = 1;
  IDENTIFIER_TYPE_ACCOUNT_ID = 2;
  IDENTIFIER_TYPE_MODEL_NAME = 3;
  IDENTIFIER_TYPE_CREDIT_REPORT_DATA_RAW = 4;
  IDENTIFIER_TYPE_PHONE_NUMBER = 5;
  IDENTIFIER_TYPE_USER_DEVICE_APPS_JSON = 6;
  IDENTIFIER_TYPE_EMPLOYMENT_TYPE = 7;
}

// Ids of identifier
message Identifier {
  string actor_id = 1;
  string account_id = 2;
  string model_name = 3;
  string credit_report_data_raw = 4;
  string phone_number = 5;
  string user_device_apps_json = 6;
  string employment_type = 7;
}
