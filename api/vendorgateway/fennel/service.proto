// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendorgateway.fennel;

import "api/rpc/status.proto";
import "api/vendorgateway/fennel/feature.proto";
import "api/vendorgateway/request_header.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/fennel";

// Service for Fennel Feature Store via HTTPS Call
service FennelFeatureStore {
  // RPC to send data to feature store via sync call
  // dataset_payload -> array of objects to be added to the dataset, in this event_name will tell to which dataset to add objects to
  // RPC status supported
  // STATUS_OK - Meaning objects got added to the required dataset
  // STATUS_INTERNAL - Internal server error
  rpc LogDatasets (LogDatasetsRequest) returns (LogDatasetsResponse);

  // RPC to fetch features from feature store
  // RPC status supported
  // STATUS_OK - Returns the list of all the account connected for an actor (depend on the filter, returns ALL or only ACTIVE accounts)
  // STATUS_NOT_FOUND - No accounts linked to the actor_id is found
  // STATUS_INTERNAL - Internal server error
  rpc ExtractFeatureSets (ExtractFeatureSetsRequest) returns (ExtractFeatureSetsResponse);

  // RPC to fetch users pd score using their credit report
  rpc GetPdScore (GetPdScoreRequest) returns (GetPdScoreResponse);
}

message LogDatasetsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // Name of the dataset to which data will be added
  string dataset_name = 2;

  // List of stringified objects to be added in specified dataset
  repeated string dataset_payload = 3;

  // Workflow name
  // NOTE : this is for internal purposes only and should be the same as the workflow which will be used while extracting feature sets
  string workflow = 4;
}

message LogDatasetsResponse {
  rpc.Status status = 1;

  // Response coming from vendor or in house feature store after logging
  string response = 2;
}

message ExtractFeatureSetsRequest {
  // Common request header across all vendor gateway APIs
  // Denotes the vendor that is supposed to process this request
  vendorgateway.RequestHeader header = 1;

  // List of features names to extract
  // Ex: \["a", "b"]
  repeated string feature_name_list = 2;

  // Features will be extracted this identifier type can be actor_id or account_id or both ex : \["actor_id", "account_id"]
  repeated vendorgateway.fennel.IdentifierType identifier_type_list = 3;

  // List of ids for the identifier type for which features will be extracted
  // Ex : \[{"actor_id": "AC12", "account_id": "SV12"}, {"actor_id": "AC13", "account_id": "SV13"}]
  repeated Identifier identifier_list = 4;

  // workflow name under which extracted features will be dumped in the s3
  string workflow = 5;
}

message ExtractFeatureSetsResponse {
  rpc.Status status = 1;

  // List of all the output features against an identifier
  // Ex: {"name": "a", "value": \["aa1", ""aa2]}, "name": "b", "value": \["bb", "bb2"]}
  map<string, google.protobuf.Value> feature_map = 2;
}

message GetPdScoreRequest {
  vendorgateway.RequestHeader header = 1;
  string actor_id = 2;
  bytes raw_credit_report = 3;
}

message GetPdScoreResponse {
  rpc.Status status = 1;
  double score = 2;
  string model_context = 3;
  string model_version = 4;
}
