// protolint:disable MAX_LINE_LENGTH


syntax = "proto3";

package vendorgateway.wealth.cvl;

import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/income_slab.proto";
import "api/typesv2/marital_status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/nationality.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/politically_exposed_status.proto";
import "api/typesv2/residential_status.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendors/wealth/cvlkra.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/wealth/cvl";
option java_package = "com.github.epifi.gamma.api.vendorgateway.wealth.cvl";

service Cvl {
  // GetPassword was created to get the latest generated password from CVL, but since CVL doesn't change it, the vendor call isn't made anymore
  // Currently, it retrieves the password required to make CVL API calls from AWS secret manager and caches it for a specific duration
  rpc GetPassword (GetPasswordRequest) returns (GetPasswordResponse);

  // GenPanStatus RPC is used to fetch pan status of a customer
  rpc GetPanStatus (GetPanStatusRequest) returns (GetPanStatusResponse);

  // PanDetailsFetch is used to fetch PAN data of a customer
  // To get the PAN details of a user, their date of birth (DoB) must be sent in the request along with PAN
  // In case DoB sent in request doesn't match with the DoB that CVL has in their KYC records, CVL might respond with an error code or with missing fields in response
  // Some of these error scenarios have been handled specially in this RPC to return InvalidArgument as the status code
  // All other error scenarios result in Internal status code as default
  rpc PanDetailsFetch (PanDetailsFetchRequest) returns (PanDetailsFetchResponse);

  // InsertUpdateKycRecord is used to insert or update kyc record of customer
  rpc InsertUpdateKycRecord (InsertUpdateKycRecordRequest) returns (InsertUpdateKycRecordResponse);

  // UploadFile RPC is used for uploading a file on the sftp server remote path
  rpc UploadFile (UploadFileRequest) returns (UploadFileResponse);

  // DownloadDir RPC is used for downloading file contents of dir from sftp server
  rpc DownloadDir (DownloadDirRequest) returns (DownloadDirResponse);

  // DownloadFile RPC is used for downloading a single file from sftp server
  rpc DownloadFile (DownloadFileRequest) returns (DownloadFileResponse);

  // ListDirFiles RPC is used for listing file names of a dir from sftp server
  rpc ListDirFiles (ListDirFilesRequest) returns (ListDirFilesResponse);

  // DownloadFileWithStream RPC is used for downloading a single file from sftp server using grpc streams
  rpc DownloadFileWithStream (DownloadFileWithStreamRequest) returns (stream DownloadFileWithStreamResponse);
}

message GetPasswordRequest {
  vendorgateway.RequestHeader header = 1;
}


message GetPasswordResponse {
  rpc.Status status = 1;
  // Password generated by CVLKRA application for using further RPCs
  string password = 2;
}


message GetPanStatusRequest {
  vendorgateway.RequestHeader header = 1;
  // PAN number of the customer
  string pan_number = 2;
}


message GetPanStatusResponse {
  rpc.Status status = 1;
  // PAN Enquiry data
  vendors.wealth.PanEnquiry pan_enquiry = 2;
  // PAN Summary data
  vendors.wealth.PanSummary pan_summary = 3;
}

message PanDetailsFetchRequest {
  vendorgateway.RequestHeader header = 1;
  // PAN number of the user
  string pan_number = 2;
  string dob_incorp = 3;
  // KRA vendor code
  vendors.wealth.KraCode kra_code = 6;
  // Type of fetch (Image_And_Data, Image, Data)
  vendors.wealth.KraFetchType fetch_type = 7;
}


message PanDetailsFetchResponse {
  rpc.Status status = 1;
  // KRA vendor code
  vendors.wealth.KraCode kra_info = 2;
  // Details of PAN
  PanDetails pan_details = 3;
  // Fetch folder date
  string folder_date = 4;
  // Fetch folder time
  string folder_time = 5;
  // Total records
  int64 total_no_of_records = 6;
}


message PanDetails {
  // do we need to store APP_INT_CODE?
  vendors.wealth.KraAppUpdtFlg update_flag = 1;
  vendors.wealth.KraAppType type = 2;
  string no = 3;
  string date = 4;
  vendors.wealth.KraAppExmt exmt = 5;
  vendors.wealth.KraAppExmtCat exmt_cat = 6;
  api.typesv2.common.Name name = 7;
  vendors.wealth.KraIdProof id_proof = 8;
  vendors.wealth.KraIpvFlag ipv_flag = 9;
  string ipv_date = 10;
  api.typesv2.Gender gen = 11;
  string pan_no = 12;
  string panex_no = 13;
  vendors.wealth.KraPanCopyFlag pan_copy = 14;
  api.typesv2.common.Name f_name = 16;
  string reg_no = 17;
  string dob_dt = 18;
  string doi_dt = 19;
  string commence_dt = 20;
  api.typesv2.Nationality nationality = 21;
  string oth_nationality = 22;
  vendors.wealth.KraCompanyType comp_status = 23;
  string oth_comp_status = 24;
  api.typesv2.ResidentialStatus res_status = 25;
  string res_status_proof = 26;
  string uid_no = 27;
  string cor_add1 = 28;
  string cor_add2 = 29;
  string cor_add3 = 30;
  string cor_city = 31;
  string cor_pincd = 32;
  string cor_state = 33;
  string cor_ctry = 34;
  string off_no = 35;
  string res_no = 36;
  string mob_no = 37;
  string fax_no = 38;
  string email = 39;

  // correspondence address proof type
  // indicates if the address proof submitted is Aadhaar or Non-Aadhaar based
  vendors.wealth.KraAddressProof cor_add_proof = 40;

  string cor_add_ref = 41;
  string cor_add_dt = 42;
  string per_add1 = 43;
  string per_add2 = 44;
  string per_add3 = 45;
  string per_city = 46;
  string per_pincd = 47;
  string per_state = 48;
  string per_ctry = 49;

  // permanent address proof type
  // indicates if the address proof submitted is Aadhaar or Non-Aadhaar based
  vendors.wealth.KraAddressProof per_add_proof = 50;

  string per_add_ref = 51;
  string per_add_dt = 52;
  string income = 53;
  vendors.wealth.KraOccupation occ = 54;
  string oth_occ = 55;
  string pol_conn = 56;
  string doc_proof = 57;
  string internal_ref = 58;
  string branch_code = 59;
  api.typesv2.MaritalStatus mar_status = 60;
  int64 netwrth = 61;
  string networth_dt = 62;
  string incorp_plc = 63;
  string otherinfo = 64;
  string filler1 = 65;
  string filler2 = 66;
  string filler3 = 67;
  string status = 68;
  string statusdt = 69;
  string error_desc = 70;
  string dump_type = 71;
  string dnlddt = 72;
  vendors.wealth.KraCode kra_info = 73;
  string signature = 74;
  string iop_flg = 75;
  string pos_code = 76;
  SummaryRec summ_rec = 77;
  vendors.wealth.KraKycMode kyc_mode = 78;
  string app_remarks = 79;
  repeated AddData addl_data = 80;
  vendors.wealth.KraKycStatus kyc_status = 81;
  api.typesv2.PostalAddress corr_address = 82;
  api.typesv2.PostalAddress perm_address = 83;
  google.type.Date dob = 84;
  api.typesv2.IncomeSlab income_slab = 85;
  api.typesv2.PoliticallyExposedStatus politically_exposed_status = 86;
  message AddData {
    string addldata_updtflg = 1;
    string entity_pan = 2;
    string addldata_pan = 3;
    string addldata_name = 4;
    string addldata_din_uid = 5;
    string addldata_relationship = 6;
    string addldata_polconn = 7;
    string addldata_resadd_1 = 8;
    string addldata_resadd_2 = 9;
    string addldata_resadd_3 = 10;
    string addldata_rescity = 11;
    string addldata_respincd = 12;
    string addldata_resstate = 13;
    string addldata_rescountry = 14;
    string addldata_filler1 = 15;
    string addldata_filler2 = 16;
    string addldata_filler3 = 17;
  }


  message SummaryRec {
    string othkra_code = 1;
    string othkra_batch = 2;
    string req_date = 3;
    int32 total_rec = 4;
    string response_date = 5;
  }
}

message InsertUpdateKycRecordRequest {
  vendorgateway.RequestHeader header = 1;
  vendors.wealth.KraAppUpdtFlg app_update_flag = 2;
  vendors.wealth.KraAppType app_type = 3;
  google.protobuf.Timestamp app_date = 4;
  string pan_no = 5;
  vendors.wealth.KraPanCopyFlag pan_copy = 6;
  vendors.wealth.KraAppExmt exmt = 7;
  vendors.wealth.KraAppExmtCat exmt_cat = 8;
  vendors.wealth.KraIdProof exmt_proof = 9;
  vendors.wealth.KraIpvFlag ipv_flag = 10;
  google.protobuf.Timestamp app_ipv_date = 11;
  api.typesv2.Gender gender = 12;
  api.typesv2.common.Name name = 13;
  api.typesv2.common.Name father_name = 14;
  google.protobuf.Timestamp dob_incorp = 16;
  api.typesv2.Nationality nationality = 17;
  api.typesv2.ResidentialStatus residential_status = 18;
  api.typesv2.ResidentialStatusProof residential_status_proof = 19;
  string uid_no = 20;
  api.typesv2.PostalAddress current_address = 21;
  api.typesv2.PostalAddress permanent_address = 22;
  api.typesv2.common.Landline tel_office = 23;
  api.typesv2.common.Landline tel_residential = 24;
  api.typesv2.common.PhoneNumber mobile_number = 25;
  api.typesv2.common.Landline fax_number = 26;
  string email_id = 27;
  api.typesv2.MaritalStatus marital_status = 28;
  int64 net_worth = 29;
  string net_worth_dt = 30;
  string app_plc = 31;
  string filler1 = 32;
  string filler2 = 33;
  string filler3 = 34;
  api.typesv2.common.Name ipv_name = 35;
  string ipv_designation = 36;
  string ipv_org = 37;
  vendors.wealth.KraKycMode kyc_mode = 38;
  string app_version = 39;
  string app_uid_token = 40;
  string app_auth_name = 41;
  string app_auth_email1 = 42;
  string app_auth_email2 = 43;
  string app_auth_email3 = 44;
  api.typesv2.common.PhoneNumber app_auth_mobile_number = 45;
  vendors.wealth.KraFpiConsentFlag kra_fpi_flag = 46;
  vendors.wealth.KraUboFlag kra_ubo_flag = 47;
  int64 no_of_kyc_records = 48;
  int64 no_of_addl_records = 49;
  vendors.wealth.DocumentSubmissionDetail document_submission_detail = 50;

  vendors.wealth.KraPanCopyFlag per_add_flag = 51;

  // correspondence address proof type
  // indicates if the address proof submitted is Aadhaar or Non-Aadhaar based
  vendors.wealth.KraAddressProof cor_add_proof = 52;

  // in case none of the KRAs have KYC records of user (i.e. fresh new KYC cases), send KRA code of CVL
  // in case modifying a KYC record already present with one of the KRAs, the KRA code received in the KYC status API response is sent
  vendors.wealth.KraCode kra_code = 53;

  // a numeric identifier of the correspondence address proof, e.g. last 4 digits of Aadhaar, etc.
  // Note: Always ensure Aadhaar is masked when setting this
  string correspondence_address_ref = 54;

  // a numeric identifier of the permanent address proof, e.g. last 4 digits of Aadhaar, etc.
  // Note: Always ensure Aadhaar is masked when setting this
  string permanent_address_ref = 55;

  FatcaDeclaration fatca_declaration = 56;
}

// Investors must fill out a FATCA declaration as part of the KYC process for the Indian securities market.
// This identifies U.S. persons (U.S. citizens, green card holders, or those with substantial U.S. presence).
// Financial institutions report these U.S. persons' accounts to the IRS through Indian tax authorities.
// FATCA applies to all investors to ensure compliance with international tax laws.
// Currently, our platform only supports Indian residents who declare they are not U.S.
// tax residents by agreeing to Epifi Wealth's T&Cs (see https://fi.money/wealth/TnC).
// Non-Indian investors are not supported.
message FatcaDeclaration {
  string place_of_birth = 1;

  // PAN (Permanent Account Number) is a unique 10-character alphanumeric code assigned to all taxpayers in India.
  string pan = 2;

  // Usually, the time at which the user accepted Epifi Wealth's T&Cs.
  google.protobuf.Timestamp declared_at = 3;
}

message InsertUpdateKycRecordResponse {
  rpc.Status status = 1;
  string pan_no = 2;
  google.protobuf.Timestamp pan_dob = 3;
  api.typesv2.common.Name name = 4;
  vendors.wealth.KraStatus kra_status = 5;
  vendors.wealth.KraRejectionReason kra_rejection_reason = 6; // APP_STATUS is populated with error_code if any
  string modf_ack = 7;
  google.protobuf.Timestamp status_dt = 8;
  google.protobuf.Timestamp entry_dt = 9;
  google.protobuf.Timestamp mod_dt = 10;
  string pos_code = 11;
  google.protobuf.Timestamp response_date = 12;
  int64 total_rec = 13;
}

message UploadFileRequest {
  // remote path of file
  string remote_path = 1;
  // file data in bytes
  bytes file_data = 2;
}

message UploadFileResponse {
  rpc.Status status = 1;
}

message DownloadDirRequest {
  string remote_path = 1;
}

message DownloadDirResponse {
  rpc.Status status = 1;
  repeated FileData dir_data = 2;
}

// FileData represents a file by file_name and its content
message FileData {
  string file_name = 1;
  bytes file_data = 2;
}

message DownloadFileRequest {
  string remote_file_path = 1;
}

message DownloadFileResponse {
  rpc.Status status = 1;
  FileData file_data = 2;
}

message ListDirFilesRequest {
  string remote_path = 1;
}

message ListDirFilesResponse {
  rpc.Status status = 1;
  repeated string file_names = 2;
}

message DownloadFileWithStreamRequest {
  string remote_file_path = 1;
}

message DownloadFileWithStreamResponse {
  rpc.Status status = 1;
  bytes file_chunk = 2;
}
