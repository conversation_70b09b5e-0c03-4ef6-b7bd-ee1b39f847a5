syntax = "proto3";

package vendorgateway.wealth.mutualfund.analytics;

import "api/rpc/status.proto";
import "api/vendorgateway/request_header.proto";
import "api/vendorgateway/vendor.proto";
import "api/vendors/mfcentral/mf_central_payload.proto";
import "api/vendors/smallcase/mf_analytics.proto";

option go_package = "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund/analytics";

service MFAnalytics {
  // GetMFAnalytics masks raw mf central data, trigger vendors mf analytics api and returns unmasked computed analytics
  rpc GetMFAnalytics(GetMFAnalyticsRequest) returns (GetMFAnalyticsResponse);
}

message GetMFAnalyticsRequest {
  vendorgateway.RequestHeader header = 1;
  // unique request id for each analytics request
  string request_id = 2;
  string actor_id = 3;
  // summary in mf central format
  repeated vendors.mfcentral.DtSummary summary = 4;
  // transactions in mf central format
  repeated vendors.mfcentral.DtTransaction transactions = 5;
}

message GetMFAnalyticsResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
    // Vendor (smallcase) is using stale nav data for mf analytics
    LIVE_DATA_UNAVAILABLE = 102;
    UNHANDLED_REMARK_RECEIVED = 103;
  }
  rpc.Status status = 1;
  vendorgateway.Vendor vendor = 2;
  // vendor's mf analytics response
  oneof analytics {
    // unmasked analytics response from smallcase
    vendors.smallcase.MFAnalyticsResponseData small_case_mf_analytics = 3;
  }
}
