syntax = "proto3";

package vendorgateway.wealth.mutualfund;

option go_package = "github.com/epifi/gamma/api/vendorgateway/wealth/mutualfund";

import "api/vendorgateway/wealth/mutualfund/error_type.proto";

message ProcessFATCAFileResult {
  string   code = 1 [deprecated=true];
  int32 record_number = 2;
  string message = 3;
  mutualfund.ErrorType error_type = 4;
  enum Status {
    UNSPECIFIED = 0;
    SUCCESS = 1;
    FAILURE = 2;
  }
  Status status = 5;

}
