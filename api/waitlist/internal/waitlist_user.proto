//go:generate gen_sql -types=WaitlistFeature
// protolint:disable MAX_LINE_LENGTH

/*
Protos relating to the Waitlist that are internal to the domain such as data models
*/


syntax = "proto3";

package waitlist;

import "api/comms/email_template.proto";
import "api/comms/enums.proto";
import "api/comms/sms_template.proto";
import "api/comms/whatsapp_template.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/employment/service.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/waitlist";
option java_package = "com.github.epifi.gamma.api.waitlist";

// WaitlistUser stores the information related to the waitlisti attempt.
message WaitlistUsers {
  string user_id = 1;
  // Details of the user
  Profile profile = 2;
  // Request ID for PF linking verification.
  string pf_request_id = 3;
  PFStatus pf_status = 4;
  NonPFStatus non_pf_status = 8;
  LoginStatus login_status = 12;
  // PF Data - Passbook, demographic....
  vendorgateway.employment.EPFGetPassbookResponse pf_data = 5;
  // Non-PF data - Average company PF, Company Data...
  vendorgateway.employment.VerifyEmploymentResponse non_pf_data = 13;
  vendorgateway.employment.EmployeeNameSearchResponse pf_org_data = 17;
  AccessStatus waitlist_status = 6;
  int64 waitlist_rank = 7;
  string pf_otp_token = 9;
  UserType user_type = 10;
  string referral_token = 11;
  string referral_code = 14;
  string prospect_id = 15;
  string rejection_reason = 16;
  WelcomeEmailStatus welcome_email_status = 18;
  CBOData cbo_data = 19;
  int64 cbo_counter = 20;
  string cbo_code = 21;
  FreelancerStatus fl_status = 22;
  string fl_agent_id = 23;
  string fl_reason = 24;
  AppAccessEmailStatus app_access_email_status = 25;
  string app_access_email_version = 35;
  google.protobuf.Timestamp app_access_email_sent_time = 26;
  GmailInputEmailStatus gmail_input_email_status = 27;
  google.protobuf.Timestamp gmail_input_email_sent_time = 28;
  string flow_name = 29;
  DeviceInfo device_info = 30;
  repeated DeviceInfo device_info_history = 31;
  AppAccessSMSStatus app_access_sms_status = 37;
  string app_access_sms_version = 34;
  google.protobuf.Timestamp app_access_sms_sent_time = 36;
  AppAccessWhatsappStatus app_access_whatsapp_status = 32;
  google.protobuf.Timestamp app_access_whatsapp_sent_time = 33;
  string app_access_whatsapp_version = 38;
  string finite_code = 39;
  api.typesv2.common.PhoneNumber onboarded_user_phone_number = 40;
  string onboarded_actor_id = 41;
  FlowDetails flow_details = 42;
  WaitlistCommsDetails comms_details = 43;
}

// CBO data of the user.
message CBOData {
  string relationship_money = 1;
  string salary_answer = 2;
  string why_cbo = 3;
}

// WaitlistUserFieldMask is used to mask columns to update in DB Update call
enum WaitlistUserFieldMask {
  WAITLIST_USER_FIELD_NONE = 0;
  PROFILE = 1;
  PF_STATUS = 2;
  NON_PF_STATUS = 3;
  PF_DATA = 4;
  NON_PF_DATA = 11;
  WAITLIST_STATUS = 5;
  WAITLIST_RANK = 6;
  PF_OTP_TOKEN = 8;
  PF_REQUEST_ID = 9;
  LOGIN_STATUS = 10;
  REJECTION_REASON = 12;
  PF_ORG_DATA = 13;
  WELCOME_EMAIL_STATUS = 14;
  PROSPECT_ID = 15;
  CBO_DATA = 16;
  CBO_COUNTER = 17;
  CBO_CODE = 18;
  FL_STATUS = 19;
  FL_AGENT_ID = 20;
  FL_REASON = 21;
  APP_ACCESS_EMAIL_STATUS = 22;
  APP_ACCESS_EMAIL_SENT_TIME = 23;
  GMAIL_INPUT_EMAIL_STATUS = 24;
  GMAIL_INPUT_EMAIL_SENT_TIME = 25;
  FLOW_NAME = 26;
  DEVICE_INFO = 27;
  DEVICE_INFO_HISTORY = 28;
  APP_ACCESS_SMS_STATUS = 34;
  APP_ACCESS_SMS_SENT_TIME = 35;
  APP_ACCESS_WHATSAPP_STATUS = 29;
  APP_ACCESS_WHATSAPP_SENT_TIME = 30;
  APP_ACCESS_EMAIL_VERSION = 31;
  APP_ACCESS_SMS_VERSION = 32;
  APP_ACCESS_WHATSAPP_VERSION = 33;
  FINITE_CODE = 36;
  ONBOARDED_USER_PHONE_NUMBER = 37;
  ONBOARDED_ACTOR_ID = 38;
  FLOW_DETAILS = 39;
  COMMS_DETAILS = 40;
}

enum GmailInputEmailStatus {
  GMAIL_INPUT_EMAIL_STATUS_UNSPECIFIED = 0;
  // Gmail Input email sent.
  GMAIL_INPUT_EMAIL_STATUS_SENT = 1;
}

enum AppAccessSMSStatus {
  APP_ACCESS_SMS_STATUS_UNSPECIFIED = 0;
  // App Access SMS sent.
  APP_ACCESS_SMS_STATUS_SENT = 1;
}

enum AppAccessWhatsappStatus {
  APP_ACCESS_WHATSAPP_STATUS_UNSPECIFIED = 0;
  // App Access Whatsapp message sent.
  APP_ACCESS_WHATSAPP_STATUS_SENT = 1;
}

enum AppAccessEmailStatus {
  APP_ACCESS_EMAIL_STATUS_UNSPECIFIED = 0;
  // App Access Email sent.
  APP_ACCESS_EMAIL_STATUS_SENT = 1;
}

enum WelcomeEmailStatus {
  WELCOME_EMAIL_STATUS_UNSPECIFIED = 0;
  // Welcome Email sent.
  WELCOME_EMAIL_STATUS_SENT = 1;
}

// Status of Provident Fund linking.
enum PFStatus {
  PF_STATUS_UNSPECIFIED = 0;
  // UAN OTP Sent.
  PF_STATUS_OTP_SENT = 1;
  // UAN OTP Verification request sent.
  PF_STATUS_OTP_VERIFICATION_PENDING = 2;
  // UAN OTP Verification successful and data received.
  PF_STATUS_SUCCESS = 3;
  // PF Attachment failed.
  PF_STATUS_FAILED = 4;
}

// Status of Non-PF Employee verification.
enum NonPFStatus {
  NON_PF_STATUS_UNSPECIFIED = 0;
  // UAN OTP Sent.
  NON_PF_STATUS_OTP_SENT = 1;
  // OTP Verification request sent.
  NON_PF_STATUS_OTP_VERIFICATION_PENDING = 2;
  // OTP Verification successful.
  NON_PF_STATUS_OTP_VERIFIED = 3;
  // Employment verification data recevied and sent to vendor.
  NON_PF_STATUS_EMPLOYMENT_VERIFICATION_PENDING = 5;
  // Received data.
  NON_PF_STATUS_EMPLOYMENT_VERIFICATION_SUCCESS = 6;
  // Data fetch failed
  NON_PF_STATUS_EMPLOYMENT_VERIFICATION_FAILED = 7;
  // Non PF OTP Verification failed.
  NON_PF_STATUS_OTP_FAILED = 4;
}

// Status of Login.
enum LoginStatus {
  LOGIN_STATUS_UNSPECIFIED = 0;
  // UAN OTP Sent.
  LOGIN_STATUS_OTP_SENT = 1;
  // OTP Verification request sent.
  LOGIN_STATUS_OTP_VERIFICATION_PENDING = 2;
  // OTP Verification successful.
  LOGIN_STATUS_OTP_VERIFIED = 3;
  // RELOGIN OTP Verification failed.
  LOGIN_STATUS_OTP_FAILED = 4;
}

enum AccessStatus {
  ACCESS_STATUS_UNSPECIFIED = 0;
  ACCESS_STATUS_EARLY_ACCESS = 1;
  ACCESS_STATUS_WAITLIST = 2;
  ACCESS_STATUS_ONBOARDING_STARTED = 3;
  ACCESS_STATUS_ONBOARDED_WITH_DIFFERENT_PHONE_NUM = 4;
  // users with ios device type who have early access.
  ACCESS_STATUS_IOS_EARLY_ACCESS = 5;
}

enum UserType {
  USER_TYPE_UNSPECIFIED = 0;
  // A select set of users directly given code by Fi.
  USER_TYPE_ORIGINALS = 1;
  // Users invited by Originals.
  USER_TYPE_FIRST_MOVERS = 2;
  // Users invited by First Movers.
  USER_TYPE_ADAPTERS = 3;
  // Users who don’t have the special ticket.
  USER_TYPE_SEEKERS = 4;
}

// User's personally identifying details.
message Profile {
  api.typesv2.common.Name name = 1;
  api.typesv2.common.PhoneNumber phone_number = 2;
  string work_email = 3;
  string comm_email = 4;
  string profession = 5;
  string linkedin_url = 6;
  string company_name = 7;
}

// FreelancerStatus tracks the status of waitlisted user in freelancer flow.
enum FreelancerStatus {
  FREELANCER_STATUS_UNSPECIFIED = 0;
  // Waitlisted user accepted in the freelancer flow.
  FREELANCER_STATUS_ACCEPTED = 1;
  // Waitlisted user rejected in the freelancer flow.
  FREELANCER_STATUS_REJECTED = 2;
  // Waitlisted user on hold in the freelancer flow.
  FREELANCER_STATUS_ON_HOLD = 3;

}


message DeviceInfo {
  // mobile, tablet, desktop
  string device_type = 1;
  // android, ios, mac os etc.
  string device_os = 2;
  // microsoft, apple, etc..
  string device_manufacturer = 3;
  string device_model = 4;
  string device_resolution = 5;
  // chrome, safari, etc..
  string browser_type = 6;
  // if `is_user_choice` flag is true, it would mean that the device_os was chosen by the user
  bool is_user_choice = 7;
}

message FlowDetails {
  string campaign_name = 1;
  CreatedFor created_for = 2;
}

enum CreatedFor {
  CREATED_FOR_UNSPECIFIED = 0;
  INDIVIDUAL = 1;
  INFLUENCER = 2;
}

message WaitlistCommsDetails {
  // count of follow-up emails sent to a user to start onboarding
  int32 followup_early_access_email_count = 1;
  // details of the follow-up early access emails sent to user
  repeated EmailDetails followup_early_access_email_details = 2;
  // count of follow-up emails sent to a user to start onboarding
  int32 followup_early_access_sms_count = 3;
  // details of the follow-up early access emails sent to user
  repeated SmsDetails followup_early_access_sms_details = 4;
  // whatsapp message details
  repeated WhatsAppDetails whatsapp_msg_details = 5;
  // cbo feedback email sent
  bool cbo_feedback_email_sent = 6;
}

message EmailDetails {
  comms.EmailType comms_email_type = 1;
  comms.TemplateVersion template_version = 2;
  google.protobuf.Timestamp sent_at = 3;
  // comms message id
  string message_id = 4;
}

message SmsDetails {
  comms.SmsType comms_sms_type = 1;
  comms.TemplateVersion template_version = 2;
  google.protobuf.Timestamp sent_at = 3;
  // comms message id
  string message_id = 4;
}

message WhatsAppDetails {
  comms.WhatsappType comms_wa_type = 1;
  comms.TemplateVersion template_version = 2;
  google.protobuf.Timestamp sent_at = 3;
  // comms message id
  string message_id = 4;
}

message WaitlistRank {
  string user_id = 1;
  string prospect_id = 2;
  int64 waitlist_rank = 3;
  CkycStatus ckyc_status = 4;
  string device_type = 5;
  AccessStatus waitlist_status = 6;
  google.protobuf.Timestamp created_at = 7;
  SignupStatus signup_status = 8;
  bool is_cbo_user = 9;
  UserSegment user_segment = 10;
}

enum CkycStatus {
  CKYC_STATUS_UNSPECIFIED = 0;
  HIGH_CKYC = 1;
  LOW_CKYC = 2;
  NONE = 3;
}

enum SignupStatus {
  SIGNUP_STATUS_UNSPECIFIED = 0;
  COMPLETE = 1;
  INCOMPLETE = 2;
}

enum UserSegment {
  USER_SEGMENT_UNSPECIFIED = 0;
  HIGH_PRIORITY = 1;
  MEDIUM_PRIORITY = 2;
  LOW_PRIORITY = 3;
}

enum WaitlistFeature {
  WAITLIST_FEATURE_UNSPECIFIED = 0;
  WAITLIST_FEATURE_CREDIT_CARD = 1;
  WAITLIST_FEATURE_CREDIT_CARD_ELIGIBILITY_CHECK = 2;
}

enum WaitlistUserFeatureFieldMask {
  WAITLIST_USER_FEATURE_FIELD_MASK_UNSPECIFIED = 0;
  WAITLIST_USER_FEATURE_FIELD_MASK_USER_ID = 1;
  WAITLIST_USER_FEATURE_FIELD_MASK_FEATURE = 2;
}

message WaitlistUserFeature {
  string user_id = 1;
  WaitlistFeature feature = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  google.protobuf.Timestamp deleted_at = 5;
}
