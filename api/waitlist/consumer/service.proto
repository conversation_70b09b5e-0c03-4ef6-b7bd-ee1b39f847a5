syntax = "proto3";

package waitlist.consumer;

import "api/queue/consumer_headers.proto";
import "api/typesv2/common/phone_number.proto";

option go_package = "github.com/epifi/gamma/api/waitlist/consumer";
option java_package = "com.github.epifi.gamma.api.waitlist.consumer";

service WaitlistConsumer {
  // consumer rpc to grant user access with finite code
  rpc GrantUserAccess(GrantUserAccessRequest) returns (GrantUserAccessResponse) {}
}

message GrantUserAccessRequest {
  queue.ConsumerRequestHeader consumer_request_header = 1;

  // user identifier for whom we need to grant access
  oneof identifier {
    api.typesv2.common.PhoneNumber phone_number = 2;
  }
  Source source = 3;
}

enum Source {
  SOURCE_UNSPECIFIED = 0;

  WHATSAPP_BOT = 1;
}

message GrantUserAccessResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
