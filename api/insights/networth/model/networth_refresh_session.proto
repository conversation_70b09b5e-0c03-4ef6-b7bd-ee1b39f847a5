//go:generate gen_sql -types=AssetRefreshDetail

syntax = "proto3";

package insights.networth.model;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/insights/networth/model";
option java_package = "com.github.epifi.gamma.api.insights.networth.model";

message NetWorthRefreshSession {
    string id = 1;
    // actor id to whom the assets belongs
    string actor_id = 2;
    // ordered list of assets to be refreshed with their refresh info
    repeated AssetRefreshDetail asset_refresh_details = 3;
    google.protobuf.Timestamp created_at = 4;
    google.protobuf.Timestamp updated_at = 5;
    google.protobuf.Timestamp deleted_at = 6;
}

message AssetRefreshDetail {
    // asset name corresponding to particular net worth refresh asset
    NetWorthRefreshAsset asset_name = 1;
    // refresh id corresponding to the particular asset refresh flow
    string refresh_id = 2;
    // status of the refresh to redirect/skip
    NetWorthAssetRefreshStatus refresh_status = 3;
}

enum NetWorthRefreshAsset {
    NET_WORTH_REFRESH_ASSET_UNSPECIFIED = 0;
    NET_WORTH_REFRESH_ASSET_MUTUAL_FUNDS = 1;
    NET_WORTH_REFRESH_ASSET_CREDIT_SCORE = 2;
    NET_WORTH_REFRESH_ASSET_EPF = 3;
    NET_WORTH_REFRESH_ASSET_MANUAL_ASSETS = 4;
}

enum NetWorthAssetRefreshStatus {
    NET_WORTH_ASSET_REFRESH_STATUS_UNSPECIFIED = 0;
    NET_WORTH_ASSET_REFRESH_STATUS_INITIATED = 1;
    NET_WORTH_ASSET_REFRESH_STATUS_SUCCESSFUL = 2;
    NET_WORTH_ASSET_REFRESH_STATUS_SKIP = 3;
}

enum NetWorthRefreshSessionFieldMask {
    NET_WORTH_REFRESH_SESSION_FIELD_MASKS_UNSPECIFIED = 0;
    NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ID = 1;
    NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ACTOR_ID = 2;
    NET_WORTH_REFRESH_SESSION_FIELD_MASKS_ASSET_REFRESH_DETAILS = 3;
}
