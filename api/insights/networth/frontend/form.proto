syntax = "proto3";

package insights.networth.frontend;

import "api/insights/networth/model/investmentdeclaration.proto";

option go_package = "github.com/epifi/gamma/api/insights/networth/frontend";
option java_package = "com.github.epifi.gamma.api.insights.networth.frontend";

// Request to build the form for user to manually declare an asset or liability
message BuildFormRequest {
  string actor_id = 1;

  // Investment declaration is optional if provided it is used to set values for the input fields
  model.InvestmentDeclaration investment_declaration = 2;
}
