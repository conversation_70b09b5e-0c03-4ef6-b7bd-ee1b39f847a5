syntax = "proto3";

package insights.story.model;

import "google/protobuf/timestamp.proto";
import "api/insights/story/model/enums.proto";
import "api/webfe/story/story_data.proto";

option go_package = "github.com/epifi/gamma/api/insights/story/model";
option java_package = "com.github.epifi.gamma.api.insights.story.model";

// A story gorup is created based on some theme, topic etc.
// Each story group will consist a set of stories that are shown together to the user.
message StoryGroup {
  string id = 1;
  StoryGroupName name = 2;
  // time after which story group becomes eligible to be shown to the user
  google.protobuf.Timestamp valid_from = 3;
  // time before which story is eligible to be shown to the user.
  google.protobuf.Timestamp valid_till = 4;
  // display_details will contain config/details related to how
  // a story group will visually appear to the user.
  StoryGroupDisplayDetails display_details = 5;
  // Share related details e.g. story share text
  ShareDetails share_details = 6;
}

message StoryGroupDisplayDetails {
  StoryGroupHeader header = 1;
}

message StoryGroupHeader {
  webfe.story.StoryGroupHeading heading = 1;
}

message ShareDetails {
  // share text required to share with a story
  string story_share_text = 1;
}
