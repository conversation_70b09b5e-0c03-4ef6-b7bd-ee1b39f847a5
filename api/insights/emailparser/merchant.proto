syntax = "proto3";

package insights.emailparser;

option go_package = "github.com/epifi/gamma/api/insights/emailparser";
option java_package = "com.github.epifi.gamma.api.insights.emailparser";

enum Merchant {
  MERCHANT_UNSPECIFIED = 0;

  AMAZON = 1;

  SWIGGY = 2;

  ZOMATO = 3;

  BIGBASKET = 4;

  MYNTRA = 5;

  FLIPKART = 6;
}


// QueryType is used to specify the category of mails a particular query will filter
enum QueryType {
  QUERY_TYPE_UNSPECIFIED = 0;

  // For queries which are to be used for fetching mails containing order amount
  ORDER_AMOUNT = 1;

  // For queries which will be filtering refund mails for a particular merchant
  ORDER_REFUND = 2;

  // For queries which will be filtering mails received on order cancellation
  ORDER_CANCEL = 3;
}

enum MerchantCategory {
  MERCHANT_CATEGORY_UNSPECIFIED = 0;

  FOOD = 1;

  SHOPPING = 2;
}
