//go:generate gen_queue_pb
syntax = "proto3";
package insights.epf.consumer;

import "api/queue/consumer_headers.proto";
import "api/insights/epf/events.proto";

option go_package = "github.com/epifi/gamma/api/insights/epf/consumer";
option java_package = "com.github.epifi.gamma.api.insights.epf.consumer";

service Consumer {
  rpc ProcessEpfPassbookDataFlattening (insights.epf.EpfPassbookImportEvent) returns (ProcessEpfPassbookDataFlatteningResponse);
}

message ProcessEpfPassbookDataFlatteningResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
