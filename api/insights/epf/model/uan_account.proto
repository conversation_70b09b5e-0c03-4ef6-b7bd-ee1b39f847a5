//go:generate gen_sql -types=UANAccount
syntax = "proto3";

package insights.epf.model;

import "api/typesv2/common/phone_number.proto";
import "api/vendors/karza/employment.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/insights/epf/model";
option java_package = "com.github.epifi.gamma.api.insights.epf.model";

// UANAccount defines attributes for Universal Account Number (UAN) issued by Employees' Provident Fund Organization (EPFO)
message UANAccount {
  string id = 1;
  string actor_id = 2;
  // Universal Account Number
  string uan_number = 3;
  // phone number linked to the uan
  api.typesv2.common.PhoneNumber phone_number = 4;
  // is this the primary uan for the user
  bool is_primary = 5;
  // raw parsed epf passbook
  vendors.karza.EPFGetPassbookResponseV2.Result raw_details = 6;

  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  google.protobuf.Timestamp deleted_at = 9;
}

enum UANAccountFieldMask {
  UAN_ACCOUNT_FIELD_MASK_UNSPECIFIED = 0;
  UAN_ACCOUNT_FIELD_MASK_ID = 1;
  UAN_ACCOUNT_FIELD_MASK_ACTOR_ID = 2;
  UAN_ACCOUNT_FIELD_MASK_UAN_NUMBER = 3;
  UAN_ACCOUNT_FIELD_MASK_PHONE_NUMBER = 4;
  UAN_ACCOUNT_FIELD_MASK_IS_PRIMARY = 5;
  UAN_ACCOUNT_FIELD_MASK_RAW_DETAILS = 6;
  UAN_ACCOUNT_FIELD_MASK_CREATED_AT = 7;
  UAN_ACCOUNT_FIELD_MASK_UPDATED_AT = 8;
  UAN_ACCOUNT_FIELD_MASK_DELETED_AT = 9;
}

message UanAccountsList {
  repeated UANAccount uan_accounts = 1;
}
