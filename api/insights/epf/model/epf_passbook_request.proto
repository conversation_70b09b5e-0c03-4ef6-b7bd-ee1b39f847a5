//go:generate gen_sql -types=RequestStatus,FailureReason
syntax = "proto3";
package insights.epf.model;

import "api/frontend/deeplink/deeplink.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/insights/epf/model";
option java_package = "com.github.epifi.gamma.api.insights.epf.model";

// EPFPassbookRequest defines a single epf passbook fetch request for a UAN
message EPFPassbookRequest {
  string id = 1;
  string actor_id = 2;
  // Universal Account Number
  string uan_number = 3;
  // unique id given by client during otp generation
  string client_request_id = 4;
  // unique request id given by vendor after otp generation
  string vendor_request_id = 5;
  RequestStatus request_status = 6;
  FailureReason failure_reason = 7;

  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;
  // exit deeplink for passbook request
  frontend.deeplink.Deeplink exit_deeplink = 11;
}

enum RequestStatus {
  REQUEST_STATUS_UNSPECIFIED = 0;
  REQUEST_STATUS_OTP_GENERATION_INITIATED = 1;
  REQUEST_STATUS_OTP_GENERATION_SUCCESSFUL = 2;
  // otp submission and passbook fetch initiated
  REQUEST_STATUS_PASSBOOK_FETCH_INITIATED = 3;
  // passbook fetched successfully
  REQUEST_STATUS_PASSBOOK_FETCH_SUCCESSFUL = 4;
  // no passbook found at EPFO
  REQUEST_STATUS_NO_PASSBOOK_FOUND = 5;
  REQUEST_STATUS_FAILED = 6;
  // passbook import flow initiated
  REQUEST_STATUS_EPF_PASSBOOK_IMPORT_INITIATED = 7;
}

enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;
  FAILURE_REASON_OTP_GENERATION_FAILED = 1;
  // Incorrect OTP or request ID
  // OTP already used
  // OTP Session timeout (10 mins)
  FAILURE_REASON_PASSBOOK_FETCH_FAILED_INVALID_INPUT = 2;
  // passbook already fetcher or user logged in to EPFO portal on another devide
  FAILURE_REASON_PASSBOOK_FETCH_FAILED_MAX_RETRIES_EXCEEDED = 3;
  // passbook fetch failed due to name mismatch in fi profile data and employee details received in epf response
  FAILURE_REASON_NAME_MISMATCH_USING_DS_RPC = 4;
  // passbook fetch failed due to name mismatch in pkg nameMatch method in fi profile data and employee details received in epf response
  FAILURE_REASON_NAME_MISMATCH_USING_PKG_LIBRARY = 5;
  // passbook fetch failed due to empty member name for name match
  FAILURE_REASON_EMPTY_MEMBER_NAME_FOR_NAME_CHECK = 6;
  // passbook fetch failed due to empty actor name for name check
  FAILURE_REASON_EMPTY_ACTOR_NAME_FOR_NAME_CHECK = 7;
  // passbook fetch failed due to match result error from NameMatch method
  FAILURE_REASON_NAME_MATCH_RESULT_ERROR_FROM_PKG_LIBRARY = 8;
}

enum EPFPassbookRequestFieldMask {
  EPF_PASSBOOK_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_ID = 1;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_ACTOR_ID = 2;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_UAN_NUMBER = 3;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_CLIENT_REQUEST_ID = 4;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_VENDOR_REQUEST_ID = 5;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_REQUEST_STATUS = 6;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_FAILURE_REASON = 7;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_CREATED_AT = 8;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_UPDATED_AT = 9;
  EPF_PASSBOOK_REQUEST_FIELD_MASK_DELETED_AT = 10;
}
