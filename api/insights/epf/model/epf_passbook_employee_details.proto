syntax = "proto3";
package insights.epf.model;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/insights/epf/model";
option java_package = "com.github.epifi.gamma.api.insights.epf.model";

message EpfPassbookEmployeeDetails {
  string id = 1 [(validate.rules).string.min_len = 1];
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  string uan_id = 3 [(validate.rules).string.min_len = 1];
  string uan_number = 4 [(validate.rules).string.min_len = 1];
  string user_name = 5;
  string father_name = 6;
  google.type.Date date_of_birth = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;
}
