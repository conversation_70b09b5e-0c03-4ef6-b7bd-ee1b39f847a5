//go:generate gen_sql -types=EpfPassbookTransactions
syntax = "proto3";
package insights.epf.model;

import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";


option go_package = "github.com/epifi/gamma/api/insights/epf/model";
option java_package = "com.github.epifi.gamma.api.insights.epf.model";

message EpfPassbookTransactions {
  string id = 1 [(validate.rules).string.min_len = 1];
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  string uan_id = 3 [(validate.rules).string.min_len = 1];
  string uan_number = 4 [(validate.rules).string.min_len = 1];
  string employee_details_id = 5 [(validate.rules).string.min_len = 1];
  string est_details_id = 6 [(validate.rules).string.min_len = 1];
  // Date of transaction.
  google.type.Date date_of_transactions = 7;
  // Date of approval of the transaction.
  google.type.Date transaction_approval_date = 8;
  // Employees Share of Contribution to EPF for the month.
  google.type.Money credit_employee_share = 9;
  // Employer's share of contribution to EPF for the month.
  google.type.Money credit_employer_share = 10;
  // Amount credited to Pension Account.
  google.type.Money credit_pension_balance = 11;
  // Transaction Type Debit "D" or Credit "C".
  string debit_credit_flag = 12;
  // Description of the transaction as per EPF Passbook.
  string particular = 13;
  // Month and Year for which the contribution is made "MYYYY" OR "MMYYYY".
  string month_year = 14;
  // Transaction Approval Date.
  google.type.Date transactions_approval_date = 15;
  google.protobuf.Timestamp created_at = 16;
  google.protobuf.Timestamp updated_at = 17;
  google.protobuf.Timestamp deleted_at = 18;
}
