syntax = "proto3";

package insights.model;

import "api/insights/model/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/insights/model";
option java_package = "com.github.epifi.gamma.api.insights.model";

// Represents the state of insight served to the user (served, acted on, seen, etc)
message InsightEngagement {
  // Primary key
  string id = 1;

  // Foreign key of Insight segment table
  string segment_id = 2;

  // Each insight framework can have multiple templates.
  // We provide user with any one to maintain freshness between different segments
  string template_id = 3;

  // foreign key of actor_insight table
  string actor_insight_id = 4;

  // Actor id of user served this insight
  string actor_id = 5;

  // Represents the session the insight is served in to the user
  // Used to make sure we do no send more than a specific threshold number of insights in one session
  string session_id = 6;

  // Defines the state of insight served to user (i.e. served / noticed / acted on)
  EngagementAction engagement_action = 7;

  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;

  // Defines the point where the insight is delivered e.g. on pay screen in app, email, etc
  string destination = 11;

  // metadata for insight served
  EngagementMetadata metadata = 12;
}

enum InsightEngagementFieldMask {
  INSIGHT_ENGAGEMENT_FIELD_MASK_UNSPECIFIED = 0;
  INSIGHT_ENGAGEMENT_ID = 1;
  INSIGHT_ENGAGEMENT_SEGMENT_ID = 2;
  INSIGHT_ENGAGEMENT_TEMPLATE_ID = 3;
  INSIGHT_ENGAGEMENT_ACTOR_INSIGHT_ID = 4;
  INSIGHT_ENGAGEMENT_ACTOR_ID = 5;
  INSIGHT_ENGAGEMENT_SESSION_ID = 6;
  INSIGHT_ENGAGEMENT_ENGAGEMENT_ACTION = 7;
  INSIGHT_ENGAGEMENT_CREATED_AT = 8;
  INSIGHT_ENGAGEMENT_UPDATED_AT = 9;
  INSIGHT_ENGAGEMENT_DELETED_AT = 10;
  INSIGHT_ENGAGEMENT_DESTINATION = 11;
  INSIGHT_ENGAGEMENT_METADATA =  12;
}

message EngagementMetadata {
  string notification_message_id = 1;
}
