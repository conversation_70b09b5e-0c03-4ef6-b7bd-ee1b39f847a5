syntax = "proto3";

package insights.secrets.config;

import "api/frontend/analyser/internal/filter_config.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/insights/secrets/config";
option java_package = "com.github.epifi.gamma.api.insights.secrets.config";

// FilterConfig is a wrapper type over analyser filter config and respective filter preference
message FilterConfig {
  // ToDo: combine these configs once we move away from analyser config
  frontend.analyser.FilterConfig filter_config = 1;
  FilterPreference filter_preference = 2;
}

// FilterPreference defines the high level default value to be used in a filter
message FilterPreference {
  oneof preference {
    TimeFilterPreference time_filter_preference = 1;
  }
}

// copy of api/frontend/analyser/filter_preference.proto
// filter preference params required for FILTER_NAME_TIME_FILTER
message TimeFilterPreference {
  TimeFilterPreferenceType preference_type = 1;
  oneof time_range {
    TimeFilterPreferredRange predefined_range = 2;
    DateRange custom_date_range = 3;
  }
}

// pre-defined time ranges which users can pass in as filter preference
enum TimeFilterPreferredRange {
  TIME_FILTER_PREFERRED_RANGE_UNSPECIFIED = 0;
  TIME_FILTER_PREFERRED_RANGE_CURRENT_MONTH = 1;
  TIME_FILTER_PREFERRED_RANGE_LAST_MONTH = 2;
  TIME_FILTER_PREFERRED_RANGE_CURRENT_YEAR = 3;
}

enum TimeFilterPreferenceType {
  TIME_FILTER_PREFERENCE_TYPE_UNSPECIFIED = 0;
  TIME_FILTER_PREFERENCE_TYPE_PREDEFINED_RANGE = 1;
  TIME_FILTER_PREFERENCE_TYPE_CUSTOM_DATE_RANGE = 2;
}

message DateRange {
  // Inclusive from date.
  // If a complete date is provided like, 12-08-2022 its considered as 12-08-2022
  // If only a month and year is provided, 0-08-2022 its considered as start of the month 01-08-2022
  // If only year is provided, 0-0-2022 its considered as start of the year 01-01-2022
  google.type.Date from_date = 1;

  // Inclusive to date
  // If a complete date is provided like, 12-08-2022 its considered as 12-08-2022
  // If only a month and year is provided, 0-08-2022 its considered as end of the month 31-08-2022
  // If only year is provided, 0-0-2022 its considered as end of the year 31-12-2022
  google.type.Date to_date = 2;
}
