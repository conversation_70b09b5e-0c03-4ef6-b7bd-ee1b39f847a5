syntax = "proto3";

package insights.secrets.enums;

option go_package = "github.com/epifi/gamma/api/insights/secrets/enums";
option java_package = "com.github.epifi.gamma.api.insights.secrets.enums";

// SecretBuilderName defines a unique secret builder
enum SecretBuilderName {
  SECRET_BUILDER_NAME_UNSPECIFIED = 0;
  SECRET_BUILDER_NAME_EPF = 1;
  SECRET_BUILDER_NAME_TRANSACTION_CATEGORY = 2;
  SECRET_BUILDER_NAME_CREDIT_REPORT = 3;
  SECRET_BUILDER_NAME_CREDIT_REPORT_SECRET = 4;
  SECRET_BUILDER_NAME_MF_PORTFOLIO_SCHEME_METRICS = 5;
  SECRET_BUILDER_NAME_ASSETS_SECRET = 6;
  SECRET_BUILDER_NAME_EPF_SMS_SECRET = 7;
  SECRET_BUILDER_NAME_MF_HISTORY = 8;
  SECRET_BUILDER_NAME_MF_PERFORMANCE = 9;
  SECRET_BUILDER_NAME_SALARY_REPORT = 10;
}

// SecretSummariesEntrypoint defines the screen-position for which secret summaries are to be created
enum SecretSummariesEntrypoint {
  SECRETS_SUMMARIES_ENTRYPOINT_UNSPECIFIED = 0;
  SECRETS_SUMMARIES_ENTRYPOINT_HOME = 1;
  SECRETS_SUMMARIES_ENTRYPOINT_SECRET_ANALYSER_LIBRARY = 2;
  SECRETS_SUMMARIES_ENTRYPOINT_NETWORTH_DASHBOARD = 3;
}

enum WealthAnalyserWidgetType {
  WEALTH_ANALYSER_WIDGET_TYPE_UNSPECIFIED = 0;
  WEALTH_ANALYSER_WIDGET_TYPE_SAVINGS_AND_INVESTMENTS = 1;
  WEALTH_ANALYSER_WIDGET_TYPE_MF = 2;
  WEALTH_ANALYSER_WIDGET_TYPE_EPFO = 3;
  WEALTH_ANALYSER_WIDGET_TYPE_CREDIT_REPORT = 4;
  WEALTH_ANALYSER_WIDGET_TYPE_STOCKS = 5;
  WEALTH_ANALYSER_WIDGET_TYPE_SALARY_REPORT = 6;
}
