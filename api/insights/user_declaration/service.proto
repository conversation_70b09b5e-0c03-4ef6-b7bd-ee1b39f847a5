syntax = "proto3";

package api.insights.user_declaration;

import "api/insights/user_declaration/model/user_declaration.proto";
import "api/rpc/status.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/insights/user_declaration";
option java_package = "com.github.epifi.gamma.api.insights.user_declaration";

service Service {
  // CreateUserDeclaration creates a new user declaration
  rpc CreateUserDeclaration(CreateUserDeclarationRequest) returns (CreateUserDeclarationResponse) {}
  // GetUserDeclaration returns the user declaration if it exists
  rpc GetUserDeclaration(GetUserDeclarationRequest) returns (GetUserDeclarationResponse) {}
  // UpdateUserDeclaration updates the user declaration if it exists
  rpc UpdateUserDeclaration(UpdateUserDeclarationRequest) returns (UpdateUserDeclarationResponse) {}
}

message CreateUserDeclarationRequest {
  string actor_id = 1[(validate.rules).string.min_len = 1];
  model.UserDeclarationType user_declaration_type = 2;
  model.Declaration user_declaration = 3;
}

message CreateUserDeclarationResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  model.UserDeclaration user_declaration = 2;
}

message GetUserDeclarationRequest {
  string actor_id = 1[(validate.rules).string.min_len = 1];
  oneof user_declaration {
    model.UserDeclarationType user_declaration_type = 2;
    string external_id = 3;
  }
}

message GetUserDeclarationResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  model.UserDeclaration user_declaration = 2;
}

message UpdateUserDeclarationRequest {
  string actor_id = 1[(validate.rules).string.min_len = 1];
  string external_id = 2[(validate.rules).string.min_len = 1];
  // replaces the existing declaration with the new declaration
  // any missing fields in new declaration will not be set
  model.Declaration user_declaration = 3;
}

message UpdateUserDeclarationResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}
