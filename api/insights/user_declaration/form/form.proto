syntax = "proto3";

package api.insights.user_declaration.form;

import "api/insights/user_declaration/model/user_declaration.proto";

option go_package = "github.com/epifi/gamma/api/insights/user_declaration/form";
option java_package = "com.github.epifi.gamma.api.insights.user_declaration.form";

enum UserDeclarationFromFieldName {
  USER_DECLARATION_FORM_FIELD_NAME_UNSPECIFIED = 0;
  USER_DECLARATION_FORM_FIELD_NAME_MONTHLY_INCOME = 1;
  USER_DECLARATION_FORM_FIELD_NAME_COMPANY_NAME = 2;
}

message UserDeclarationFormIdentifier {
  oneof id {
    user_declaration.model.UserDeclarationType user_declaration_type = 1;
    string external_id = 2;
  }
}
