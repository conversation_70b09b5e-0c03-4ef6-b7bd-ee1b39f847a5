syntax = "proto3";

package recurringpayment.activity;

import "api/celestial/activity/header.proto";
import "api/order/payment/transaction.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment/activity";
option java_package = "com.github.epifi.gamma.api.recurringpayment.activity";

message UpdateTransactionRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // Transaction which needs to be updated
  order.payment.Transaction transaction = 2;

  // Request info for the transaction that needs to be updated
  order.payment.PaymentRequestInformation req_info = 3;

  // Field mask for the fields to be updated
  repeated order.payment.TransactionFieldMask update_mask = 4;

  // Current state of the Transaction
  order.payment.TransactionStatus current_status = 5;

  // State to transition the Transaction to
  order.payment.TransactionStatus next_status = 6;
}

message UpdateTransactionResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}
