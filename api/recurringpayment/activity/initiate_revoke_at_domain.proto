syntax = "proto3";

package recurringpayment.activity;

import "api/celestial/activity/header.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment/activity";
option java_package = "com.github.epifi.gamma.api.recurringpayment.activity";

message InitiateRevokeAtDomainRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // Recurring payment for which domain has to initiate the execution
  string recurring_payment_id = 2;

  // Recurring payment action id is used as client request id for
  // creating the domain entities for execution
  string recurring_payment_action_id = 3;
}

message InitiateRevokeAtDomainResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}
