syntax = "proto3";

package recurringpayment.activity;

import "api/celestial/activity/header.proto";
import "api/order/payment/transaction.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "api/order/payment/payment_protocol.proto";
import "api/recurringpayment/recurring_payment.proto";


option go_package = "github.com/epifi/gamma/api/recurringpayment/activity";
option java_package = "com.github.epifi.gamma.api.recurringpayment.activity";

message GetRecurringPaymentExecutionStatusRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  string original_request_id = 2;

  vendorgateway.Vendor partner_bank = 3;

  string actor_id = 4;

  order.payment.PaymentProtocol payment_protocol = 5;

  recurringpayment.RecurringPaymentType recurring_payment_type = 6;
}

message GetRecurringPaymentExecutionStatusResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;

  order.payment.TransactionStatus transaction_status = 2;

  ExecutedTransactionDetails executed_transaction_details = 3;
}

message ExecutedTransactionDetails {
  string utr = 1;
  string raw_response_code = 2;
  string raw_response_description = 3;
  string status_code = 4;
  string status_description_payer = 5;
  google.protobuf.Timestamp transaction_executed_at = 6;
}
