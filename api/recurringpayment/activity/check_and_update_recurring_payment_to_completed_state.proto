syntax = "proto3";

package recurringpayment.activity;

import "api/celestial/activity/header.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment/activity";
option java_package = "com.github.epifi.gamma.api.recurringpayment.activity";

message CheckAndUpdateRecurringPaymentToCompletedStateRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // unique identifier for the recurring payment
  string recurring_payment_id = 2;
}

message CheckAndUpdateRecurringPaymentToCompletedStateResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}
