// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package recurringpayment.developer;

option go_package = "github.com/epifi/gamma/api/recurringpayment/developer";
option java_package = "com.github.epifi.gamma.api.recurringpayment.developer";

enum RecurringPaymentEntity {
  RECURRING_PAYMENT_ENTITY_UNSPECIFIED = 0;

  RECURRING_PAYMENTS = 1;

  RECURRING_PAYMENT_ACTIONS = 2;

  STANDING_INSTRUCTIONS = 3;

  STANDING_INSTRUCTION_REQUESTS = 4;

  RECURRING_PAYMENT_VENDOR_DETAILS = 5;
}
