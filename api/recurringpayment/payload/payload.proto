syntax = "proto3";

package recurringpayment.payload;

import "api/order/payment/transaction.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment/payload";
option java_package = "com.github.epifi.gamma.api.recurringpayment.payload";

// payload to be sent with auth signal for recurring payment creation
// It is kept empty for now, because the signal itself denotes that some action on auth has been taken
message CreateRecurringPaymentAuthSignal {
}

// payload to be sent with auth signal for recurring payment execution
// It is kept empty for now, because the signal itself denotes that some action on auth has been taken
message ExecuteRecurringPaymentAuthSignal {
}

// payload to be sent with callback signal for recurring payment execution without auth
message ExecuteRecurringPaymentWithoutAuthCallbackSignal {
  order.payment.TransactionStatus transaction_status = 1;
}

// payload to be sent with reversal signal for recurring payment execution without auth
message ExecuteRecurringPaymentWithoutAuthReversalSignal {
  order.payment.TransactionStatus transaction_status = 1;
}

// payload to be sent with callback signal for recurring payment execution with auth
message ExecuteRecurringPaymentWithAuthCallbackSignal {
  order.payment.TransactionStatus transaction_status = 1;
}

// payload to be sent while initiating workflow
message ExecuteRecurringPaymentWithoutAuth {
  // keeping this message empty for now. More fields to be added as and when use-cases comes.
}

// payload to be sent while initiating workflow
message ExecuteRecurringPaymentWithAuth {
  // keeping this message empty for now. More fields to be added as and when use-cases comes.
}

// payload to be sent with auth signal for recurring payment modification
// It is kept empty for now, because the signal itself denotes that some action on auth has been taken
message ModifyRecurringPaymentAuthSignal {
}

// payload to be sent with auth signal for recurring payment revoke
// It is kept empty for now, because the signal itself denotes that some action on auth has been taken
message RevokeRecurringPaymentAuthSignal {
}

// payload to be sent with auth signal for recurring payment pause unpause
// It is kept empty for now, because the signal itself denotes that some action on auth has been taken
message PauseUnpauseRecurringPaymentAuthSignal {
}
