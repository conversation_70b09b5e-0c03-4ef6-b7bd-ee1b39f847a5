// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package standinginstruction;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment/standinginstruction";
option java_package = "com.github.epifi.gamma.api.recurringpayment/standinginstruction";

// StandingInstructionRequest contains details of a standing instruction request and state machine which denotes the
// current of the request.
message StandingInstructionRequest {
  string id = 1;

  string standing_instruction_id = 2;

  string vendor_request_id = 3;

  standinginstruction.RequestType request_type = 4;

  standinginstruction.State state = 5;

  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;
}

enum RequestType {
  REQUEST_TYPE_UNSPECIFIED = 0;

  CREATE = 1;

  MODIFY = 2;

  REVOKE = 3;
}

enum State {
  STATE_UNSPECIFIED = 0;
  // Entry created for a request but not yet initiated at vendor
  QUEUED = 1;
  // Request initiated at vendor
  INITIATED = 2;

  SUCCESS = 3;

  FAILED = 4;
}

enum StandingInstructionRequestFieldMask {
  STANDING_INSTRUCTION_REQUEST_FIELD_MASK_UNSPECIFIED = 0;

  STATE = 1;
}
