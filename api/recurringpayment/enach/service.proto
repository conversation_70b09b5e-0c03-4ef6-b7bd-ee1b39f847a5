// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package recurringpayment.enach;

import "api/recurringpayment/enach/auth_payload.proto";
import "api/recurringpayment/enach/enach_mandate.proto";
import "api/recurringpayment/enach/enums/enums.proto";
import "api/recurringpayment/enach/enach_mandate_action.proto";
import "api/vendorgateway/vendor.proto";
import "api/rpc/status.proto";
import "validate/validate.proto";
import "api/typesv2/bank.proto";
import "api/typesv2/common/ownership.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment/enach";
option java_package = "com.github.epifi.gamma.api.recurringpayment.enach";

service EnachService {
  // InitiateMandateCreation rpc is useful to initiate creation of a new enach mandate
  // Note : this rpc should be used only for initiating on-app mandate creating flow and shouldn't be used for persisting off app mandates,
  rpc InitiateMandateCreation(InitiateMandateCreationRequest) returns (InitiateMandateCreationResponse) {};
  // GetAuthorizationPayloadForMandateCreation rpc is useful to fetch the authorization payload needed for a new mandate creation.
  rpc GetAuthorizationPayloadForMandateCreation(GetAuthorizationPayloadForMandateCreationRequest) returns (GetAuthorizationPayloadForMandateCreationResponse) {};
  // AuthorizeMandateCreation rpc should be used to authorize enach mandate creation
  // for enach, the authorization is done by the payer off app on the destination bank portal, so this api is called when receive a successful authorization callback from the bank.
  rpc AuthorizeMandateCreation(AuthorizeMandateCreationRequest) returns (AuthorizeMandateCreationResponse){};
  // GetMandateActionStatus rpc should be used to get the status of an already initiated action on an enach mandate.
  rpc GetMandateActionStatus(GetMandateActionStatusRequest) returns (GetMandateActionStatusResponse) {};
  // GetRecurringPaymentIdByVendorReqId should be used to fetch the recurring payment id using the enach action type and the corresponding vendor request id with which the action was initiated at vendor's end.
  rpc GetRecurringPaymentIdByVendorReqId(GetRecurringPaymentIdByVendorReqIdRequest) returns (GetRecurringPaymentIdByVendorReqIdResponse) {};
  // CreateOffAppEnachMandate idempotent RPC is used to persist ENACH mandate that is initiated from 3rd party app/off-app like Groww
  // It is also used to persist ENACH mandate entities for in-app mandates created via PaymentGateway, since for Paymentgateway,
  // we don't use the domain entities for orchestrating the flows, rather store these only for book keeping purposes.
  // Hence, we create only the ENACH mandate domain entity and not the ENACH mandate action domain entity.
  rpc CreateOffAppEnachMandate(CreateOffAppEnachMandateRequest) returns (CreateOffAppEnachMandateResponse) {}
  // GetEnachMandate  fetches the enach mandate using the given identifier
  rpc GetEnachMandate(GetEnachMandateRequest) returns (GetEnachMandateResponse){}
  // GetSupportedBanks returns the list of banks that support enach along with their details
  rpc GetSupportedBanks (GetSupportedBanksRequest)returns (GetSupportedBanksResponse){}
  // GetActivationCoolOff returns the cool off duration after the authorisation of recurring payment creation request
  // recurring payment activation should be considered only after cool off duration has lapsed.
  rpc GetActivationCoolOff (GetActivationCoolOffRequest) returns (GetActivationCoolOffResponse){}
  // InitiateExeuction is used to initiate the execution of the already created enach in the system
  // steps followed in the rpc:
  // 1. Create enach_mandate_action_dao
  // 2. Trigger the workflow for the enach execution
  rpc InitiateExecution(InitiateExecutionRequest) returns (InitiateExecutionResponse){};
  // GetExecutionOrderDetailsForInboundNotification returns the details of the execution order for which inbound notification is received on payment notification handler (consumer/reconciler),
  // accepts some notification params (needed to fetch the correct execution order) and returns the client_request_id of the execution order for which inbound notification is received in response.
  rpc GetExecutionOrderDetailsForInboundNotification(GetExecutionOrderDetailsForInboundNotificationRequest) returns (GetExecutionOrderDetailsForInboundNotificationResponse) {};
  // IsEnachMandateExecutionAllowed is useful to check whether execution for a given enach mandate (referenced by recurring payment id) is allowed or not,
  // as of now execution for a enach mandate is not allowed for 3 days if last execution failed due to the txn being rejected by the destination bank due to return reason code.
  rpc IsEnachMandateExecutionAllowed(IsEnachMandateExecutionAllowedRequest) returns (IsEnachMandateExecutionAllowedResponse) {};
}
message InitiateExecutionRequest  {
  // request id passed by the client for initiating mandate creation.
  string client_request_id = 1 [(validate.rules).string.min_len = 1];
  // denotes the recurring payment corresponding to which enach mandate needs to be executed.
  string recurring_payment_id = 2 [(validate.rules).string.min_len = 1];
  // amount of money for the execution
  google.type.Money amount = 3 [(validate.rules).message.required = true];
}
message InitiateExecutionResponse{
  rpc.Status status = 1;
}


message InitiateMandateCreationRequest {
  // request id passed by the client for initiating mandate creation.
  string client_request_id = 1 [(validate.rules).string.min_len = 1];
  // denotes the recurring payment corresponding to which mandate is being created.
  string recurring_payment_id = 2 [(validate.rules).string.min_len = 1];
  // denotes the authorization mode selected by the user for authorizing mandate creation.
  enach.enums.EnachRegistrationAuthMode registration_auth_mode = 3 [(validate.rules).enum = {not_in: [0]}];
  // vendor who is fulfilling the mandate creation e.g FEDERAL_BANK, DIGIO etc
  vendorgateway.Vendor vendor = 4 [(validate.rules).enum = {not_in: [0]}];
}
message InitiateMandateCreationResponse {
  // rpc status
  rpc.Status status = 1;
}

message GetAuthorizationPayloadForMandateCreationRequest {
  // denotes the recurring payment corresponding to the enach mandate for which authorization paylood needs to be fetched
  // todo (utkarsh) : evaluate if we should use client request id for this
  string recurring_payment_id = 1;

  // todo (utkarsh, sidhant) : pass recurring payment fields needed for generating authorization payload like start date, end date, from and to pis etc
}
message GetAuthorizationPayloadForMandateCreationResponse {
  // rpc status
  rpc.Status status = 1;
  // authorization payload
  MandateCreationAuthorizationPayload auth_payload = 2;
}

message GetMandateActionStatusRequest {
  // request id which was used by client to initiate the mandate action e.g
  // to know the creation status, client can pass the client_request_id which was used earlier for initiating create action.
  string client_request_id = 1 [(validate.rules).string.min_len = 1];
}
message GetMandateActionStatusResponse {
  // rpc status
  rpc.Status status = 1;
  // denotes the current status of action
  enach.enums.EnachActionStatus action_status = 2;
  // denotes the current substatus of action
  enach.enums.EnachActionSubStatus action_sub_status = 3;
  // denotes the detailed status of mandate action
  enach.ActionDetailedStatus action_detailed_status = 4;
  // denotes the last updated status of action
  google.protobuf.Timestamp action_updated_at = 5;
}

message AuthorizeMandateCreationRequest {
  // recurring payment for which the corresponding to which the enach mandate needs to be authorized
  string recurring_payment_id = 1 [(validate.rules).string.min_len = 1];
  // todo (utkarsh): validate if have a specific length for umrn as per npci and update the validation accordingly
  // UMRN is a unqiue identifier of the mandate at bank's end and needs to be persisted in our internal enach mandate entity.
  string umrn = 2 [(validate.rules).string.min_len = 1];
  // npci reference id that comes in enach authorization callback, for now only useful for debugging purposes.
  string npci_ref_id = 3 [(validate.rules).string.min_len = 1];
  // destination bank reference number that comes in enach authorization callback, for now only useful for debugging purposes.
  string dest_bank_reference_number = 4 [(validate.rules).string.min_len = 1];
  // merchant reference number that comes in enach authorization callback, useful for now only useful for debugging purposes.
  string merchant_reference_message_id = 5 [(validate.rules).string.min_len = 1];
}
message AuthorizeMandateCreationResponse {
  // rpc status
  rpc.Status status = 1;
}

message GetRecurringPaymentIdByVendorReqIdRequest {
  // denotes the type of enach mandate action corresponding to which the vendorRequestId is passed.
  enums.EnachActionType action_type = 1 [(validate.rules).enum = {not_in: [0]}];
  // denotes the request id which was used initially for initiating the action with the vendor
  string vendor_req_id = 2 [(validate.rules).string.min_len = 1];
}
message GetRecurringPaymentIdByVendorReqIdResponse {
  // rpc status
  rpc.Status status = 1;

  string recurring_payment_id = 2;
}

message CreateOffAppEnachMandateRequest {
  string umrn = 1 [(validate.rules).string.min_len = 1];
  string recurring_payment_id = 2 [(validate.rules).string.min_len = 1];
  enach.enums.EnachRegistrationProvenance provenance = 3 [(validate.rules).enum = {not_in: [0]}];
  vendorgateway.Vendor vendor = 4 [(validate.rules).enum = {not_in: [0]}];
  api.typesv2.common.Ownership ownership = 5;
  enach.enums.EnachRegistrationAuthMode registration_auth_mode = 6;
}
message CreateOffAppEnachMandateResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  enach.EnachMandate enach_mandate = 2;
}

message GetEnachMandateRequest {
  oneof Identifier {
    string umrn = 1;
    string recurring_payment_id = 2;
  }
}
message GetEnachMandateResponse {
  enum Status {
    OK = 0;
    INVALID_ARGUMENT = 3;
    RECORD_NOT_FOUND = 5;
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  enach.EnachMandate enach_mandate = 2;
}

message GetSupportedBanksRequest {}
message GetSupportedBanksResponse {
  rpc.Status status = 1;

  message BankInfo {
    api.typesv2.Bank bank_name = 1;

    string logo_url = 2;

    repeated enach.enums.EnachRegistrationAuthMode supported_auth_modes = 3;
  }
  repeated BankInfo supported_banks = 2;
}

message GetActivationCoolOffRequest {
  // vendor who is fulfilling the mandate creation e.g FEDERAL_BANK, DIGIO etc
  vendorgateway.Vendor vendor = 1 [(validate.rules).enum = {not_in: [0]}];
}
message GetActivationCoolOffResponse {
  rpc.Status status = 1;
  // cooldown duration after which the activation should be considered
  google.protobuf.Duration cool_off_duration = 2;
}

message GetExecutionOrderDetailsForInboundNotificationRequest {
  string enach_mandate_id = 1 [(validate.rules).string.min_len = 1];

  // details received in inbound notification which are needed for fetching the execution order for which the notification is received.
  message InboundNotificationDetails {
    google.type.Money amount = 1;
  }
  InboundNotificationDetails inbound_notification_details = 2 [(validate.rules).message.required = true];
}

message GetExecutionOrderDetailsForInboundNotificationResponse{
  // rpc status
  rpc.Status status = 1;
  // client request id of the order for which inbound notification is received.
  string order_client_request_id = 2;
}

message IsEnachMandateExecutionAllowedRequest {
  // denotes the recurring payment corresponding to the enach mandate for which execution needs to be checked
  string recurring_payment_id = 1 [(validate.rules).string.min_len = 1];
}

message IsEnachMandateExecutionAllowedResponse {
  // rpc status
  rpc.Status status = 1;
  // denotes whether enach mandate execution allowed or not
  bool is_execution_allowed = 2;
}
