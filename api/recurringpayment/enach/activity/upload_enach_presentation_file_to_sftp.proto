syntax = "proto3";

package recurringpayment.enach.activity;

import "api/celestial/activity/header.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment/enach/activity";
option java_package = "com.github.epifi.gamma.api.recurringpayment.enach.activity";

message UploadEnachPresentationFileToSFTPRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // s3 path of the presentation file which needs to be uploaded.
  string presentation_file_s3_path = 2;
  // name of the file to be used when uploading at vendor.
  string presentation_file_name = 3;
}

message UploadEnachPresentationFileToSFTPResponse{
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
}
