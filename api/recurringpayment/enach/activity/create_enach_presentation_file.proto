syntax = "proto3";

package recurringpayment.enach.activity;

import "api/celestial/activity/header.proto";

option go_package = "github.com/epifi/gamma/api/recurringpayment/enach/activity";
option java_package = "com.github.epifi.gamma.api.recurringpayment.enach.activity";

message CreateEnachPresentationFileRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
}

message CreateEnachPresentationFileResponse {
  // Common response header across all the celestial activities
  celestial.activity.ResponseHeader response_header = 1;
  // list of action ids for which the file generation was successful
  repeated string presented_action_execution_ids =2;
  // s3 path of the created presentation file.
  string presentation_file_s3_path = 3;
  // name of the file to be used when uploading at vendor.
  string presentation_file_name = 4;
  // execution id of the batch in which the execution was presented.
  string presentation_batch_execution_id = 5;
}
