syntax = "proto3";

package auth;

option go_package = "github.com/epifi/gamma/api/auth";
option java_package = "com.github.epifi.gamma.api.auth";


// NonceStatus represents the status of a nonce generated for device integrity verification.
// Nonce generated for device verification can only be used once and have a limited validity period after which they can't be used.
// When a nonce is generated, it is in `ACTIVE` state. In case, client uses this nonce during device verification, nonce's state is changed to `USED`
// If a nonce isn't used during its validity, its state is changed to `EXPIRED`
enum NonceStatus {
  NONCE_STATUS_UNSPECIFIED = 0;

  ACTIVE = 1;

  USED = 2;

  EXPIRED = 3;
}
