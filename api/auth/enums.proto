//go:generate gen_sql -types=TokenDeletionReason
syntax = "proto3";

package auth;

option go_package = "github.com/epifi/gamma/api/auth";
option java_package = "com.github.epifi.gamma.api.auth";

enum TokenDeletionReason {
  TOKEN_DELETION_REASON_UNSPECIFIED = 0;
  TOKEN_DELETION_REASON_EXPIRY = 1;
  TOKEN_DELETION_REASON_ACCESS_REVOKED = 2;
  // current token is deleted, to create a new token
  TOKEN_DELETION_REASON_TOKEN_REFRESH = 3;
  TOKEN_DELETION_REASON_SIGN_OUT = 4;
  // current token is deleted to delete the current user
  TOKEN_DELETION_REASON_RESET_USER = 5;
  // Deleting the current token to create a new token with higher access level during onboarding
  TOKEN_DELETION_REASON_ONBOARDING_TOKEN_UPGRADE = 6;
  // Deleting the current token to create a new token with higher access level during re-onboarding
  TOKEN_DELETION_REASON_REONBOARDING_TOKEN_UPGRADE = 7;
  // In case of updating phone number in AFU (phone update, phone + device update), delete refresh token of user with old phone
  // This is handled in all other cases where we use same phone, as old refresh tokens are deleted
  TOKEN_DELETION_REASON_SIGN_OUT_OLD_PHONE_NUM_IN_AFU = 8;
}
