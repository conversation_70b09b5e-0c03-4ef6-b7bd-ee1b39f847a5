syntax = "proto3";

package auth.authorizer;

import "api/auth/internal/token.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/auth_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/device.proto";
import "google/protobuf/any.proto";

option go_package = "github.com/epifi/gamma/api/auth/authorizer";
option java_package = "com.github.epifi.gamma.api.auth.authorizer";


service Authorizer {
  // Authorize RPC is used to authorize FE requests. This RPC is called by auth interceptor for every FE request.
  // It does authorization after token authentication.
  rpc Authorize (AuthorizeRequest) returns (AuthorizeResponse);
}


message AuthorizeRequest {
  // Auth method options set for the RPC
  rpc.AuthOptions auth_options = 1;
  // GRPC request of the FE request under authorization
  google.protobuf.Any rpc_request = 2;

  // Represents token which we want to authenticate and authorize
  string token = 3;

  // Type of the token that is to be created
  TokenType token_type = 4;

  // Mobile Device or Browser Information used by user. Optional.
  api.typesv2.common.Device device = 5;
}

message AuthorizeResponse {
  enum Status {
    OK = 0;
    TOKEN_INVALID = 100;
    // Input token is expired
    TOKEN_EXPIRY = 101;
    // Device Id mismatch
    DEVICE_ID_MISMATCH = 102;
  }
  rpc.Status status = 1;
  // Deeplink to be used if access is denied
  frontend.deeplink.Deeplink deeplink = 2;
  TokenDetails token_details = 3;
}
