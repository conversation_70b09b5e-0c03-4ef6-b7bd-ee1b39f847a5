syntax = "proto3";

package auth.biometrics;

import "api/auth/biometrics/internal/biometrics.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/auth/biometrics";
option java_package = "com.github.epifi.gamma.api.auth.biometrics";

service BiometricsService {
  // GetBiometricsDetails will fetch biometric details for given actor id
  rpc GetBiometricsDetails (GetBiometricsDetailsRequest) returns (GetBiometricsDetailsResponse);
  // UpdateBiometricsDetails updates biometric details for given actor id
  rpc UpdateBiometricsDetails (UpdateBiometricsDetailsRequest) returns (UpdateBiometricsDetailsResponse);
  // SyncBiometricIdentifier: The app will send the biometric identifier generated at the client system
  // This received biometric identifier will be sent to Biometrics service for processing.
  // Biometrics service will publish the biometric app launch event to the queue
  rpc SyncBiometricIdentifier (SyncBiometricIdentifierRequest) returns (SyncBiometricIdentifierResponse);
}

message GetBiometricsDetailsRequest {
  string actor_id = 1;
}

message GetBiometricsDetailsResponse {
  rpc.Status status = 1;
  auth.biometrics.BiometricStatus biometric_status = 2;
  auth.biometrics.BiometricInfo biometric_info = 3;
  // timestamp at which biometric was validated
  google.protobuf.Timestamp verified_at = 4;
  // reason for updating the status
  auth.biometrics.SubStatus sub_status = 5;
  // timestamp at which any column was last updated
  google.protobuf.Timestamp updated_at = 6;
  // biometric table id
  string biometric_table_id = 7;
}

message UpdateBiometricsDetailsRequest {
  string actor_id = 1;
  auth.biometrics.BiometricStatus updated_biometric_status = 2;
  // reason for updating the status
  auth.biometrics.SubStatus sub_status = 3;
}

message UpdateBiometricsDetailsResponse {
  rpc.Status status = 1;
}

message SyncBiometricIdentifierRequest {
  string actor_id = 1;
  auth.biometrics.BiometricInfo biometric_info = 2;
}

message SyncBiometricIdentifierResponse {
  rpc.Status status = 1;
}
