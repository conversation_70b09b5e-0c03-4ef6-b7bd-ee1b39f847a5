//go:generate gen_queue_pb
syntax = "proto3";

package auth.biometrics;

import "api/queue/consumer_headers.proto";
import "api/typesv2/common/device.proto";

option go_package = "github.com/epifi/gamma/api/auth/biometrics";
option java_package = "com.github.epifi.gamma.api.auth.biometrics";


service Consumer {
  // ProcessBiometricEvent consumes the details emitted by the client at the app launch.
  rpc ProcessBiometricEvent (ProcessBiometricEventRequest) returns (ProcessBiometricEventResponse);
}

message ProcessBiometricEventRequest {
  queue.ConsumerRequestHeader request_header = 1;
  string actor_id = 2;
  // biometric identifier received from app
  string biometric_id = 3;
  // app platform IOS or Android
  api.typesv2.common.Platform app_platform = 4;
  // registered customer device ID
  string device_id = 5;
  // app version of the user
  int64 app_version = 6;
}

message ProcessBiometricEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
