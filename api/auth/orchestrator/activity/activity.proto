syntax = "proto3";

package auth.orchestrator.activity;

import "api/auth/biometrics/internal/biometrics.proto";
import "api/auth/orchestrator/internal/auth_request.proto";
import "api/auth/orchestrator/internal/auth_request_stage.proto";
import "api/celestial/activity/header.proto";

option go_package = "github.com/epifi/gamma/api/auth/orchestrator/activity";
option java_package = "com.github.epifi.gamma.api.auth.orchestrator.activity";

message AuthRequestStageRequest {
  celestial.activity.RequestHeader request_header = 1;
  auth.orchestrator.AuthStage stage = 2;
  auth.orchestrator.AuthRequestStageStatus status = 3;
  string auth_ref_id = 4;
}

message AuthRequestStageResponse {
  celestial.activity.ResponseHeader response_header = 1;
}

// Generic activity request and response when no extra data is required
message OrchestratorActivityRequest {
  celestial.activity.RequestHeader request_header = 1;
}

message OrchestratorActivityResponse {
  celestial.activity.ResponseHeader response_header = 1;
  auth.orchestrator.AuthRequest auth_request = 2;
}

message LivenessSummaryRequest {
  celestial.activity.RequestHeader request_header = 1;
}

message LivenessSummaryResponse {
  celestial.activity.ResponseHeader response_header = 1;
  auth.orchestrator.AuthRequest auth_request = 2;
  auth.orchestrator.AuthRequestStage auth_request_stage = 3;
}

message ManualReviewRequest {
  celestial.activity.RequestHeader request_header = 1;
  auth.orchestrator.AuthRequest auth_request = 2;
  auth.orchestrator.AuthRequestStage auth_request_stage = 3;
}

message ManualReviewResponse {
  celestial.activity.ResponseHeader response_header = 1;
}

message CheckBiometricStatusRequest {
  celestial.activity.RequestHeader request_header = 1;
}

message CheckBiometricStatusResponse {
  celestial.activity.ResponseHeader response_header = 1;
  auth.biometrics.BiometricStatus biometric_status = 2;
}

message UpdateBiometricStatusRequest {
  celestial.activity.RequestHeader request_header = 1;
  auth.biometrics.BiometricStatus updated_biometric_status = 2;
}

message UpdateBiometricStatusResponse {
  celestial.activity.ResponseHeader response_header = 1;
}
