syntax = "proto3";

package auth.liveness;

option go_package = "github.com/epifi/gamma/api/auth/liveness";
option java_package = "com.github.epifi.gamma.api.auth.liveness";

import "api/auth/liveness/internal/face_match_annotation.proto";
import "api/auth/liveness/internal/liveness_attempt.proto";
import "google/protobuf/timestamp.proto";

message LivenessFMAnnotation {
  string id = 1;
  string actor_id = 2;
  //  ID of the attempt which is being annotated. Eg. request id of liveness
  string req_id = 3;
  //  type of attempt which is being annotated. Eg. liveness, facematch
  AnnotationType annotation_type = 4;
  //  annotation provided by ops team
  LivenessFMAnnotationPayload annotation = 5;
  // timestamp when annotations were recorded
  google.protobuf.Timestamp created_at = 6;
  // timestamp when annotations were updated
  google.protobuf.Timestamp updated_at = 7;
}

enum AnnotationType {
  ANNOTATION_TYPE_UNSPECIFIED = 0;
  ANNOTATION_TYPE_LIVENESS = 1;
  ANNOTATION_TYPE_FACE_MATCH = 2;
}

message LivenessFMAnnotationPayload {
  oneof Payload{
    auth.liveness.Annotation liveness_annotation = 1;
    auth.liveness.FaceMatchAnnotation face_match_annotation = 2;
  }
}
