syntax = "proto3";

package api.quest.types;

import "api/quest/types/experiment.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/be-common/api/quest/types";
option java_package = "com.github.epifi.be-common.api.quest.types";

message ExperimentVersion {
  string id = 1;
  string description = 2;
  ExperimentVersionStatus status = 3;
  Experiment exp_data = 4;
  repeated string requested_reviewers = 5;
  string created_by = 6;
  string processed_by = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;
}

enum ExperimentVersionStatus {
  EXPERIMENT_VERSION_STATUS_UNSPECIFIED = 0;
  EXPERIMENT_VERSION_STATUS_DRAFT = 1;
  EXPERIMENT_VERSION_STATUS_WAITING_FOR_APPROVAL = 2;
  EXPERIMENT_VERSION_STATUS_APPROVED = 3;
  EXPERIMENT_VERSION_STATUS_DECLINED = 4;
}
