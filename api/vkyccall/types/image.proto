syntax = "proto3";

// api.vkyccall.types will hold common message definition that can be reused in both backend and frontend [cx/datacollector].
// For this reason, this package should not import any other backend [api/vkycxall**] or frontend[cx/datacollector/vkyccall**] specific packages.
package api.vkyccall.types;

option go_package = "github.com/epifi/gamma/api/vkyccall/types";
option java_package = "com.github.epifi.gamma.api.vkyccall.types";


message ImageDetails {
  // image_identifier is a unique identifier for the image. ex: s3 path
  string image_identifier = 1;
  // metadata can be additional data associated with the image
  // in case of PAN it can represent front or back side of PAN
  optional string metadata = 2;
  // presigned url to fetch the image. ex: s3 presigned url.
  optional string presigned_read_url = 3;
}
