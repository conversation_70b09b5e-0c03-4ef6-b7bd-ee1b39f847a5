syntax = "proto3";

// api.vkyccall.types will hold common message definition that can be reused in both backend and frontend [cx/datacollector].
// For this reason, this package should not import any other backend [api/vkycxall**] or frontend[cx/datacollector/vkyccall**] specific packages.
package api.vkyccall.types;

import "api/omegle/service.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/document_details.proto";
import "api/vkyccall/types/image.proto";
import "api/vkyccall/types/onboarding_stage.proto";
import "api/vkyccall/types/questions.proto";
import "google/type/date.proto";
import "google/type/latlng.proto";
import "google/type/postal_address.proto";

option go_package = "github.com/epifi/gamma/api/vkyccall/types";
option java_package = "com.github.epifi.gamma.api.vkyccall.types";

message Report {
  string meeting_id = 1;
  UserImageDetails user_image_details = 2;
  repeated api.vkyccall.types.GenericQuestionAndAnswer question_answers = 3;
  LocationCheckReport location_check_report = 4;
  BrowserAndIPDetails browser_and_ip_details = 5;
  PANDocumentResult pan_document_results = 6;
  PassportDocumentResult passport_document_results = 7;
  EmiratesIDDocumentResult emirates_id_document_results = 8;
  omegle.GetApplicantDetailsResponse applicant_details = 9;
  CKYCDocumentResult ckyc_document_results = 10;
  QatarIDDocumentResult qatar_id_document_results = 11;
  string omegle_call_id = 12;
  string agent_remarks = 13;
  AadharDigilockerDocumentResult aadhar_digilocker_document_result = 14;
}

message AadharDigilockerDocumentResult {
  MatchResult face_match_result = 1;
  api.typesv2.DocumentDetails form_data_document_details = 3;
}

message CKYCDocumentResult {
  MatchResult face_match_result = 1;
  // Displayed Document Type - PAN, Passport, Voter ID, Driving License, etc.
  // Since CKYC document can have multiple document types, we need to store which document type has been displayed.
  // Deprecated - Use DisplayData.doc_type instead.
  string displayed_document_type = 2 [deprecated = true];
  api.typesv2.DocumentDetails form_data_document_details = 3;
  // CKYC document can have multiple document types, only one document is chosen for display during vkyccall
  // This field will hold the document details of the highest priority document selected for display during the call.
  message DisplayData {
    // Displayed Document Type - PAN, Passport, Voter ID, Driving License, etc.
    string doc_type = 1;
    // Ex - Passport Number, PAN Number, Voter ID Number, etc.
    string doc_number = 2;
    google.type.Date doc_expiry_date = 3;
  }
  DisplayData display_data = 4;
}

message PANDocumentResult {
  api.typesv2.DocumentDetails form_data_document_details = 1;
  MatchResult face_match_result = 2;
  // this is the OCR data extracted from the physical pan card
  // if user had uploaded the EPAN, then this will be empty
  api.typesv2.DocumentDetails ocr_document_details = 3;
  // this is the data extracted from the EPAN
  // if user had uploaded EPAN before VKYC
  api.typesv2.DocumentDetails epan_document_details = 8;
  MatchPANResult pan_match_result = 4;
  MatchPassportResult passport_match_result = 5;
  MatchEmiratesIdResult emirates_id_match_result = 6;
  MatchQatarIdResult qatar_id_match_result = 7;
}

message PassportDocumentResult {
  api.typesv2.DocumentDetails form_data_document_details = 1;
  MatchResult face_match_result = 2;
  api.typesv2.DocumentDetails ocr_document_details = 3;
  MatchPassportResult passport_match_result = 4;
}

message EmiratesIDDocumentResult {
  api.typesv2.DocumentDetails form_data_document_details = 1;
  MatchResult face_match_result = 2;
  api.typesv2.DocumentDetails ocr_document_details = 3;
  MatchEmiratesIdResult emirates_id_match_result = 4;
}

message QatarIDDocumentResult {
  api.typesv2.DocumentDetails form_data_document_details = 1;
  MatchResult face_match_result = 2;
  api.typesv2.DocumentDetails ocr_document_details = 3;
  MatchQatarIdResult qatar_id_match_result = 4;
}

message UserImageDetails {
  ImageDetails captured_user_image_url = 1;
  ImageDetails pan_user_image_url = 2;
  ImageDetails passport_user_image_url = 3;
  ImageDetails emirates_id_user_image_url = 4;
  ImageDetails passport_rear_image_url = 5;
  ImageDetails emirates_id_front_image_url = 6;
  ImageDetails emirates_id_back_image_url = 7;
  // CKYC record can have multiple images.
  // This field will hold the image details of all the combined images of CKYC.
  ImageDetails ckyc_record_image_url = 8;
  ImageDetails ckyc_user_image_url = 9;
  ImageDetails qatar_id_front_image_url = 10;
  ImageDetails qatar_id_back_image_url = 11;
  ImageDetails qatar_id_user_image_url = 12;
  ImageDetails digilocker_user_image_url = 13;
}

message LocationCheckReport {
  google.type.PostalAddress address = 1;
  string ip_address = 2;
  double distance_in_km = 3;
  bool is_distance_within_range = 4;
  repeated types.LocationDetail location_details = 5;
  google.type.LatLng user_coordinates_at_call_start = 6;
}

message BrowserAndIPDetails {
  string internet_service_provider = 1;
  string ip_country_code = 2;
}


message MatchResult {
  // Match score in %. The value is between 0-100.
  float match_score_percent = 1;
}

message MatchPANResult {
  MatchResult pan_number = 1;
  MatchResult name = 2;
  MatchResult date_of_birth = 3;
  MatchResult face = 4;
  MatchResult parent_name = 5;
}

message MatchPassportResult {
  MatchResult passport_number = 1;
  MatchResult name = 2;
  MatchResult date_of_birth = 5;
  MatchResult face = 4;
  MatchResult nationality = 3;
  MatchResult is_indian_passport = 6 [deprecated = true];
  MatchResult date_of_expiry = 7;
  MatchResult address = 8;
}

message MatchEmiratesIdResult {
  MatchResult id = 1;
  MatchResult name = 2;
  MatchResult date_of_birth = 3;
  MatchResult face = 4;
  MatchResult nationality = 5;
  MatchResult date_of_expiry = 6;
}

message MatchQatarIdResult {
  MatchResult id = 1;
  MatchResult name = 2;
  MatchResult date_of_birth = 3;
  MatchResult face = 4;
  MatchResult nationality = 5;
  MatchResult date_of_expiry = 6;
}

message MatchLocationResult {
  MatchResult city = 1;
  double distance_in_km = 2;
  api.typesv2.common.BooleanEnum overall_match = 3;
}
