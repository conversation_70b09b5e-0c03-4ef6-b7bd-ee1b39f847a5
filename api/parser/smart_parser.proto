syntax = "proto3";

package parser;

option go_package = "github.com/epifi/gamma/api/parser";
option java_package = "com.github.epifi.gamma.api.parser";

import "api/typesv2/bank.proto";
import "api/typesv2/actor.proto";

import "api/order/order.proto";
import "api/order/payment/payment_protocol.proto";
import "api/order/payment/accounting_entry_type.proto";
import "api/payment_instruments/payment_instrument.proto";

// Smart Parser is developed by the in-house data science team to parse raw transaction data and extract
// relevant entities like UTR, payment protocol, etc.
// * If the smart parser is unable to parse a particular transaction, then `templateId` would be `null`.
// * Transactions may not be parsed to all the fields in the response. Few fields may be empty which is depended on
//   the template used.
// * Currently used by transactions sourced from Account Aggregator (AA).
// Ref: https://github.com/epiFi/smart-parser/blob/d8e66adf214122423596e088e564e2041a54b5cd/smart_parser/models.py#L395

message SmartParserInput{
  // raw transaction data identifier which would be used for parsing
  string raw_transaction_id = 1;
  // narration string describing the transaction
  string narration = 2;
  // source bank of the transaction
  api.typesv2.Bank bank = 3;
  // type of transaction e.g. Debit or Credit
  order.payment.AccountingEntryType transaction_type = 4;
}

message SmartParserOutput{
  // smart parser version maintained by the data science team
  string version = 1;
  // id of the template used by the smart parser to parse
  // this would be empty if parsing failed
  string template_id = 2;
  // UTR reference for the transaction
  string utr = 3;
  // protocol at bank through which transaction is made e.g. CARD, UPI, NEFT, RTGS, etc
  order.payment.PaymentProtocol payment_protocol = 4;
  // type of the payment instrument for eg. bank account, credit card, debit card, UPI, etc.
  paymentinstrument.PaymentInstrumentType pi_type = 5;
  // address identifier for the destination account, e.g. euronetgpay.pay@icici, **************, 541729******9214
  string payment_address = 6;
  // name of the destination bank for transaction
  api.typesv2.Bank bank = 7;
  // ifsc of the destination bank
  string ifsc = 8;
  // actor name for source of transaction
  string self_actor_name = 9;
  // actor name for destination of transaction
  // name of other actor with which transaction was processed
  // can be company's registered name in case of P2M payment
  // can be a person's name in case P2P payment
  string other_actor_name = 10;
  // type of other actor with which transaction was processed e.g. merchant or user
  api.typesv2.ActorType actor_type = 11;
  // provenance of the transaction e.g. ATM, POS, ECOM, etc.
  order.OrderProvenance provenance = 12;
  // transaction keyword
  string keyword = 13;
  // name of the ATM
  string atm_name = 14;
  // transaction remark (description) entered by the user
  string transaction_remark = 15;
  // city in which transaction was done
  string city = 16;
  // merchant category code
  string mcc = 17;
}
