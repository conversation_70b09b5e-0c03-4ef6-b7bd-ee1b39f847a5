syntax = "proto3";

package parser.payload;

import "api/celestial/workflow_req.proto";
import "api/parser/backfill/transaction/transaction.proto";
import "api/parser/enums/enums.proto";
import "api/pay/transaction_backfill/transaction_backfill.proto";
import "api/typesv2/common/ownership.proto";

option go_package = "github.com/epifi/gamma/api/parser/payload";
option java_package = "com.github.epifi.gamma.api.parser.payload";

// Payload for transaction backfill temporal workflow
message TxnBackfillPayload {
  repeated parser.backfill.transaction.RawTxnPayload txn_backfill_batch = 1;
  api.typesv2.common.Ownership data_entity = 2;
  parser.enums.DataOwnerService data_owner_service = 3;
  // counter for each workflow retries
  int32 current_workflow_retry_count = 4;
  // request id to be sent by caller in case idempotency around
  // Client details corresponding to the service initiating request. The combination of client enum and client_req_id must be unique.
  celestial.ClientReqId client_id = 5;
  // unique id for scoop job to identify which scoop job created this payload and use it to create directory in S3 buckets
  // for dumping data
  string scoop_job_id = 6;
}

// Payload for remitter info backfill temporal workflow
message RemitterInfoBackfillPayload{
  string client_req_id = 1;
  pay.transaction_backfill.OrderTxnPayData order_txn_payload = 2;
  // Maximum pause time for remitter info backfill workflow in minutes before it calls GetPiDetailsFromVendorByTxnId RPC
  int32 maximum_pause_time_in_minutes = 3;
}

