syntax = "proto3";

package deposit;

import "api/order/order.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/deposit";
option java_package = "com.github.epifi.gamma.api.deposit";

// Defines the GRPC service to process updates to deposit orders
// due to other events in the system.
// This GRPC service is registered with queue subscriber and
// RPC method will be invoked by the consumer on receiving an event
service Consumer {

  // UpdateDepositBalance invoked by queue subscriber to consume order completed state events for deposits
  // On receiving completed message for a deposit order, call vg to get the latest balance
  // and updates the balance in db
  rpc UpdateDepositBalance(order.OrderUpdate) returns (UpdateDepositBalanceResponse);
}

message UpdateDepositBalanceResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}

