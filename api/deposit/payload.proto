syntax = "proto3";

package deposit;

import "api/deposit/deposit_account.proto";
import "api/deposit/template.proto";
import "api/typesv2/deposit.proto";
import "api/deposit/enums.proto";
import "api/deposit/fittt.proto";
import "api/frontend/deeplink/deeplink.proto";
import "google/type/money.proto";
import "api/vendorgateway/vendor.proto";
import "api/accounts/account_type.proto";

option go_package = "github.com/epifi/gamma/api/deposit";
option java_package = "com.github.epifi.gamma.api.deposit";

// Deposit Details is the payload message created at FE deposit service and sent across for processing at Deposit BE.
message DepositInfo {
  // The actor id to which the deposit account belongs to
  string actor_id = 1;

  // Name of the deposit account provided at the time of creation. e.g. "Travel funds"
  string name = 2;

  // Type of the deposit account. Can be FD, RD, or SD.
  accounts.Type type = 3;

  // Amount for which to open the deposit account for.
  google.type.Money amount = 4;

  // Deposit duration
  api.typesv2.DepositTerm term = 5;

  // Renewal information of the deposit accounts.
  // For deposit accounts of type SD this will be empty as they don't support auto renewal.
  RenewInfo renew_info = 6;

  // Field Number 7 was being used by `NomineeInfo` field which is removed now. So, marking 7 as reserved.
  reserved 7;

  // Savings account number associated with the deposit account
  string operative_account_number = 8;

  // Savings account to which deposit account money is to be credited on maturity.
  string repay_account_number = 9;

  // vendor to which the deposit account is associated with
  vendorgateway.Vendor vendor = 10;

  // Type of interest payout, this filed defines the period when interest will be compounded.
  // Will be UNSPECIFIED for SD accounts
  InterestPayout interest_payout = 11;

  // deposit account id in case of preclose deposit request
  string account_id = 12;

  // request type for which payload is prepared.
  RequestType request_type = 13;

  DepositNomineeDetails nominee_details = 14;

  // deposit request Id for the previous request in case the current request is raised from retry of a failed one.
  // Optional: Only needed if current deposit creation request is raised after retry from a failed one.
  string failed_deposit_request_id = 15;

  DepositAccountProvenance deposit_account_provenance = 16;

  // template id to uniquely identify a deposit template
  // will be used to identify if a deposit account is being created using deposit templates or not
  string template_id = 17;

  // defines the goal's unique identifier linked with the deposit.
  // if empty, then user didn't opt for goal inclusion.
  string goal_id = 18;

  // defines the scheme type of the deposit account created using the template
  // We have shifted our logic to determine scheme_code based on interest payout, so deprecating this.
  DepositScheme scheme_code = 19[deprecated = true];

  // contains the auto save details if the user opted for setting up auto save rules while creating the deposit account.
  // This field will be empty if the user didn't opt for auto save.
  DepositAutoSaveParams auto_save_params = 20;
}

// Defines the type of request
enum RequestType {
  REQUEST_TYPE_UNSPECIFIED = 0;

  // Create signifies request used to create a deposit account
  CREATE = 1;

  // Preclose signifies request used to preclose a deposit account
  PRECLOSE = 2;

  // Maturity Action request is used to change the deposit auto-renew status
  MATURITY_ACTION = 3;
}

// Defines a deposit template for both SD and FD accounts.
message DepositTemplate {
  // name of the deposit account
  string name = 1;

  // amount for which to create deposit account
  google.type.Money amount = 2;

  // flag to specify is amount for this template is editable
  bool is_amount_editable = 3;

  // Type of the deposit account. Can be FIXED DEPOSIT, RECURRING DEPOSIT, or SMART DEPOSIT.
  accounts.Type type = 4;

  // description of the template
  string description = 5;

  // Term of the deposit account
  api.typesv2.DepositTerm term = 6;

  // flag to specify is term for this template is editable
  bool is_term_editable = 7;

  // Renew Option in case the deposit type is FD
  RenewOption renew_option = 8;

  // Type of interest payout, this filed defines the period when interest will be compounded.
  // Mainly for FD, will be UNSPECIFIED for SD accounts
  InterestPayout interest_payout = 9[deprecated = true];

  // template tag, example: popular, trending, in use by X people
  string tag = 10;

  // url for the deposit icon
  string icon_url = 11;

  // specifies the type of a deposit template, e.g. CUSTOM, GENERIC
  DepositTemplateType deposit_template_type = 12;

  // template id to uniquely identify a template
  string id = 13;

  // flag to specify is name for this template is editable
  bool is_name_editable = 14;

  // bonus template fields in case of bonus template types
  BonusTemplateFields bonus_template_fields = 15;

  message BonusTemplateFields {
    // Extra interest for bonus templates
    string extra_interest = 1;
  }

  bool is_interest_payout_editable = 16;

  // defines the scheme type of the deposit account created using the template
  // We have shifted our logic to determine scheme_code based on interest payout, so deprecating this.
  DepositScheme scheme_code = 17[deprecated = true];

  // disclaimer/note of the deposit template
  // e.g: "Get tax deductions under Section 80C in this tax-saver deposit with lock-in period of 5 years."
  string disclaimer_text = 18;

  // e.g: deeplink for "Learn more" in note text
  frontend.deeplink.Cta details_cta = 19;

  // contains the consent details for the deposit template
  // if empty, then the deposit template doesn't require any consent
  TemplateConsentDetails consent_details = 20;

  // if true, "Upon Maturity" option won't be editable in the creation flow
  // note: this field is only applicable for FD templates
  bool is_maturity_action_disabled = 21;

  // the interest payouts that are supported for this template. Only applicable for FDs now
  repeated InterestPayout interest_payouts = 22;
}

enum DepositTemplateType {
  DEPOSIT_TEMPLATE_TYPE_UNSPECIFIED = 0;

  CUSTOM = 1;

  GENERIC = 2;

  BONUS = 3;

  TAX_SAVING = 4;

  DEPOSIT_TEMPLATE_TYPE_CREDIT_CARD = 5;
}


// MaturityAction is the action to be taken after deposit matures
enum MaturityAction {
  MATURITY_ACTION_UNSPECIFIED = 0;
  // auto renews the deposit after maturity
  MATURITY_ACTION_AUTO_RENEW = 1;
  // credit the maturity amount to savings account after maturity
  MATURITY_ACTION_CREDIT_TO_SAVINGS = 2;
}
