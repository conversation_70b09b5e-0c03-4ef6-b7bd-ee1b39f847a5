syntax = "proto3";

package deposit.workflow;

import "api/deposit/interest_rate.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "api/accounts/account_type.proto";

option go_package = "github.com/epifi/gamma/api/deposit/workflow";
option java_package = "com.github.epifi.gamma.api.deposit.workflow";


// Request message for DepositReconciliation workflow
message DepositReconciliationRequest {
  // actor id for user for which reconciliation is being done.
  string actor_id = 1;
  // list of deposit_account_id belonging to the actor. If an empty array is passed, then all deposit accounts of the actor is evaluated.
  repeated string deposit_account_ids = 2;
}


// Request message for DepositReconciliation workflow
message MonitorDepositInterestRateRequest {
  // represent the interest rate category (Senior citizen/general category) to monitor
  deposit.InterestRateCategory interest_rate_category = 1;
  vendorgateway.Vendor vendor = 2;
  // expected date when interest rate is enquired from vendor
  google.protobuf.Timestamp interest_enquiry_at = 3;
  accounts.Type account_type = 4;
}
