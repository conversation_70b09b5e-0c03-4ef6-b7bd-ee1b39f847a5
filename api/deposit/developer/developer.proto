// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package deposit.developer;

import "api/deposit/deposit_request.proto";

option go_package = "github.com/epifi/gamma/api/deposit/developer";
option java_package = "com.github.epifi.gamma.api.deposit.developer";

enum DepositEntity {
  ACTOR_ENTITY_UNSPECIFIED = 0;

  ALL_DEPOSITS_ACTOR = 1;

  SINGLE_DEPOSIT = 2;

  All_DEPOSIT_REQUESTS_ACTOR = 3;

  DEPOSIT_TXNS_FOR_ACCOUNT = 4;

  // fetches interest rates for a given effective date
  // both past and future effective dates are supported
  // by default for now, it'll return 4 entries
  // 1) general public - fixed deposit - federal
  // 2) general public - smart deposit - federal
  // 3) senior citizen - fixed deposit - federal
  // 4) senior citizen - smart deposit - federal
  // if optional filter, effective date is not provided, it'll return all the interest rates sorted by start_date DESC
  DEPOSIT_GET_INTEREST_RATES = 5;
}

message DepositRequestList {
  repeated deposit.DepositRequest deposit_request_list = 1;
}
