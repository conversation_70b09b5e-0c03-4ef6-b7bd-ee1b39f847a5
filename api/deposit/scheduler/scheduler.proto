syntax = "proto3";

package api.deposit.scheduler;

import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "api/accounts/account_type.proto";

option go_package = "github.com/epifi/gamma/api/deposit/scheduler";
option java_package = "com.github.epifi.gamma.api.deposit.scheduler";


service Scheduler {
// Invoking this rpc triggers events for all the deposit accounts for which a transaction was done in the given time period.
rpc TriggerDepositTemplateTransactionEvent(TriggerDepositTemplateTransactionEventRequest) returns (TriggerDepositTemplateTransactionEventResponse) {}
}

message TriggerDepositTemplateTransactionEventRequest {
  repeated string template_ids = 1;
  accounts.Type type = 2;
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
}

message TriggerDepositTemplateTransactionEventResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}
