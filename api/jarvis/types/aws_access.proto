syntax = "proto3";

package api.jarvis.types;

option go_package = "github.com/epifi/gamma/api/jarvis/types";
option java_package = "com.github.epifi.gamma.api.jarvis.types";

// Form holds all the information to serve a request
message AwsAccessTemplate {
  // unique id
  string id = 1;
  // Role name
  string role = 2;
  // reason for requesting the role
  string reason = 3;
  // environment
  string env = 4;
  // first name of user
  string first_name = 5;
  // last name of user
  string last_name = 6;
}