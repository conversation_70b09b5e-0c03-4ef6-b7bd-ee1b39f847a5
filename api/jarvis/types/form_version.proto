syntax = "proto3";

package api.jarvis.types;

import "api/jarvis/types/form.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/jarvis/types";
option java_package = "com.github.epifi.gamma.api.jarvis.types";

//go:generate gen_sql -types=Form,FormVersionStatus
message FormVersion {
  string id = 1;
  string description = 2;
  FormVersionStatus status = 3;
  Form form_data = 4;
  repeated string requested_reviewers = 5;
  string created_by = 6;
  string processed_by = 7;
  string deleted_by = 8;
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp processed_at = 10;
  google.protobuf.Timestamp deleted_at = 11;
}

enum FormVersionStatus {
  Form_VERSION_STATUS_UNSPECIFIED = 0;
  Form_VERSION_STATUS_DRAFT = 1;
  Form_VERSION_STATUS_WAITING_FOR_APPROVAL = 2;
  Form_VERSION_STATUS_APPROVED = 3;
  Form_VERSION_STATUS_DECLINED = 4;
}
