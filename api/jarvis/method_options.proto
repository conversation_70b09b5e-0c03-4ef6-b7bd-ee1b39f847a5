syntax = "proto3";
package api.jarvis.method_options;

option go_package = "github.com/epifi/gamma/api/jarvis/method_options;methodoptionspb";
option java_package = "com.github.epifi.gamma.api.jarvis.method_options";

import "google/protobuf/descriptor.proto";

extend google.protobuf.MethodOptions {
  // This is a formalization to authenticate a RPC method
  // By default all authentication for all the RPCs are enabled and
  // this flag needs to be set to true explicitly to disable the check.
  bool disable_auth = 28001888;
  // Name of the resource to perform authorization with the allowed_roles.
  string resource_name = 28001889;
  // List of roles to authorize an RPC.
  // Any one role of the user must match any one role in this list to authorize the RPC.
  repeated string allowed_roles = 28001890;
}
