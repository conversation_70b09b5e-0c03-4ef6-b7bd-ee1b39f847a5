syntax = "proto3";

package api.jarvis.frontend;

import "api/nebula/method_options/method_options.proto";
import "api/nebula/request/header.proto";
import "api/pkg/web/components.proto";
import "api/pkg/web/enums.proto";
import "api/jarvis/frontend/form_ticket.proto";
import "api/jarvis/types/ticket.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/jarvis/frontend";
option java_package = "com.github.epifi.gamma.api.jarvis/frontend";

// Jarvis Frontend manages the content to show on the quest web platform.
service Forntend {
  // RPC to fetch information to display on the home page load.
  // which includes, List of the logged user's forms and
  rpc HomePage (HomePageRequest) returns (HomePageResponse) {
    option (api.nebula.method_options.resource_name) = "epifi:quest";
    option (api.nebula.method_options.allowed_roles) = "view";
  }
  // RPC to get forms by filtering with optional attributes of the form.
  // ex: Tickets for the logged-in user can be fetched by using filter created_by:<user>
  rpc ListTicketsPage (ListTicketsPageRequest) returns (ListTicketsPageResponse) {
    option (api.nebula.method_options.resource_name) = "epifi:quest";
    option (api.nebula.method_options.allowed_roles) = "view";
  }

  // RPC to fetch data for New Ticket creation page
  // Gives the list of area, metrics, user groups to choose from.
  // Gives the percent of segment that is taken and remaining.
  rpc NewTicketPage (NewTicketPageRequest) returns (NewTicketPageResponse) {
    option (api.nebula.method_options.resource_name) = "epifi:quest";
    option (api.nebula.method_options.allowed_roles) = "edit";
  }

  // RPC to create a new form with the data provided in the forms
  rpc CreateNewTicket (CreateNewTicketRequest) returns (CreateNewTicketResponse) {
    option (api.nebula.method_options.resource_name) = "epifi:quest";
    option (api.nebula.method_options.allowed_roles) = "edit";
  }

  // RPC to fetch details of a single form
  // Will return responses based on form status
  rpc GetTicket (GetTicketRequest) returns (GetTicketResponse) {
    option (api.nebula.method_options.resource_name) = "epifi:quest";
    option (api.nebula.method_options.allowed_roles) = "view";
  }

  // RPC to fetch data for edit form page
  rpc EditTicketPage (EditTicketPageRequest) returns (EditTicketPageResponse) {
    option (api.nebula.method_options.resource_name) = "epifi:quest";
    option (api.nebula.method_options.allowed_roles) = "edit";
  }

  // RPC to edit form values based on data provided in the form
  // if form is in running / Approved / Paused state, new form version is created
  // if form is not approved, existing form version is updated
  rpc EditTicket (EditTicketRequest) returns (EditTicketResponse) {
    option (api.nebula.method_options.resource_name) = "epifi:quest";
    option (api.nebula.method_options.allowed_roles) = "edit";
  }

  rpc UpdateTicketStatus (UpdateTicketStatusRequest) returns (UpdateTicketStatusResponse) {
    option (api.nebula.method_options.resource_name) = "epifi:quest";
    option (api.nebula.method_options.allowed_roles) = "approve";
  }

  rpc GetUserBucketProgressBarValues (GetUserBucketProgressBarValuesRequest) returns (GetUserBucketProgressBarValuesResponse) {
    option (api.nebula.method_options.resource_name) = "epifi:quest";
    option (api.nebula.method_options.allowed_roles) = "edit";
  }
}


message HomePageRequest {
  api.nebula.request.Header header = 1;
  // user_name of the logged user
  string user_name = 2;
}

message HomePageResponse {
  rpc.Status status = 1;
  // hello username text
  pkg.web.Text hello_text = 2;
  // link to user manual
  pkg.web.Link user_manual_link = 3;
  // my form title with count of forms created by logged user
  pkg.web.Text my_ticket_title = 4;
  // list of top 5 forms of the logged user
  pkg.web.Table my_tickets_display_data = 5;
  // button to display all my forms
  pkg.web.ActionButton all_my_tickets_btn = 6;
  // all form title with count of all forms
  pkg.web.Text all_tickets_title = 7;
  // list of top 5 forms
  pkg.web.Table all_tickets_display_data = 8;
  // button to display all forms
  pkg.web.ActionButton all_tickets_btn = 9;
  // button to go to create form layout
  pkg.web.ActionButton new_ticket_btn = 10;
}

message ListTicketsPageRequest {
  api.nebula.request.Header header = 1;
  rpc.PageContextRequest page_context = 2;
  // takes created_by, requested_reviewer, created_at_start, created_at_end, status, area, form_id as filter values in key value pair
  // eg: created_by:<EMAIL>
  pkg.web.TextInput search_filter = 3 [deprecated = true];
  string search_query = 4;
  string ticket_status = 5;
  string ticket_type = 6;
}

message ListTicketsPageResponse {
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  // all forms title with count of forms created
  pkg.web.Text all_ticket_title = 3;
  // search bar and dropdown to take filters as input
  pkg.web.MultiInputElements filter_input = 4;
  // link that takes to user manual to create, run and analyse an form
  pkg.web.Link user_manual_link = 6;
  // list of all forms in pages, contains form version id as a column
  pkg.web.Table all_tickets_display_data = 7;
  // button to create new form
  pkg.web.ActionButton new_ticket_btn = 8;
}

message NewTicketPageRequest {
  api.nebula.request.Header header = 1;
  // upload form form
  Ticket ticket_form = 2;
  // type of ticket page 
  string ticket_type = 3;
}

message NewTicketPageResponse {
  rpc.Status status = 1;
  // search bar and dropdown to take filters as input
  pkg.web.MultiInputElements filter_input = 2;
  // new ticket form
  Ticket new_ticket_form = 3;
}

message CreateNewTicketRequest {
  api.nebula.request.Header header = 1;
  // details of forms to be created
  Ticket ticket_form = 2;
  // type of ticket to be created 
  string ticket_type = 3;
}

message CreateNewTicketResponse {
  rpc.Status status = 1;
  // newly created form with version id
  Ticket ticket_form = 2;
}

message GetTicketRequest {
  api.nebula.request.Header header = 1;
  // Entity id of the selected form
  // this detail is provided while listing forms
  EntityId entity_id = 2;
}

message GetTicketResponse {
  rpc.Status status = 1;
  TicketView ticket_view = 2;
  // boolean flag to show fetching of metrics is required
  bool fetch_metrics = 3;
}

message EditTicketPageRequest {
  api.nebula.request.Header header = 1;
  // entity id to carry information to next flow
  EntityId entity_id = 2;
}

message EditTicketPageResponse {
  rpc.Status status = 1;
  // returns label and current form values as default value of Input Component
  Ticket edit_form_ticket = 2;
}

message EditTicketRequest {
  api.nebula.request.Header header = 1;
  // edited form details
  Ticket form_ticket = 2;
}

message EditTicketResponse {
  rpc.Status status = 1;
  // edited form response with the form vers id
  Ticket form_ticket = 2;
}

// message UpdateTicketStatusRequest {
//   api.nebula.request.Header header = 1;
//   string ticket_id = 2;
//   jarvis.types.FormStatus status = 3;
// }

// message UpdateTicketStatusResponse {
//   rpc.Status status = 1;
// }


message UpdateTicketStatusRequest {
  api.nebula.request.Header header = 1;
  string ticket_vers_id = 2;
  jarvis.types.TicketStatus status = 3;
}

message UpdateTicketStatusResponse {
  rpc.Status status = 1;
}

message GetUserBucketProgressBarValuesRequest {
  api.nebula.request.Header header = 1;
  string layer_name = 2;
}

message GetUserBucketProgressBarValuesResponse {
  rpc.Status status = 1;
  int64 min_value = 2;
  int64 max_value = 3;
  int64 value = 4;
  repeated pkg.web.Mark marks = 5;
}
