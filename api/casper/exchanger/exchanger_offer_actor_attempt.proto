syntax = "proto3";

package casper.exchanger;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/casper/exchanger";
option java_package = "com.github.epifi.gamma.api.casper.exchanger";

// ExchangerOfferActorAttempt stores each attempt for a given actor to redeem an ExchangerOffer
message ExchangerOfferActorAttempt {
  // unique id of an exchanger offer actor attempt.
  string id = 1;

  // actor who attempted to redeem the offer
  string actor_id = 2;

  // exchanger offer for which redemption was attempted.
  string exchanger_offer_id = 3;

  // unique requestId as sent by client for idempotency
  string request_id = 4;

  // date on which redemption was attempted in IST
  google.protobuf.Timestamp attempted_at_date = 5;

  // standard timestamp fields
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;

  // soft delete the attempt entry if corresponding ExchangerOfferOrder fails
  google.protobuf.Timestamp deleted_at = 12;
}
