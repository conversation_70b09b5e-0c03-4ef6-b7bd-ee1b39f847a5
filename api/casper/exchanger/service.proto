syntax = "proto3";

package casper.exchanger;

import "api/casper/catalog_filters.proto";
import "api/casper/exchanger/exchanger_offer.proto";
import "api/casper/exchanger/exchanger_offer_actor_attempt.proto";
import "api/casper/exchanger/exchanger_offer_group.proto";
import "api/casper/exchanger/exchanger_offer_inventory.proto";
import "api/casper/exchanger/exchanger_offer_listing.proto";
import "api/casper/exchanger/exchanger_offer_order.proto";
import "api/casper/exchanger/exchanger_offer_reward_units_actor_utilisations.proto";
import "api/casper/exchanger/reward.proto";
import "api/casper/tag.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "google/type/postal_address.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/casper/exchanger";
option java_package = "com.github.epifi.gamma.api.casper.exchanger";

// ExchangerOfferService service provides methods related to exchanger offers.
service ExchangerOfferService {
  // CreateExchangerOffer rpc is useful for creating a new ExchangerOffer.
  rpc CreateExchangerOffer (CreateExchangerOfferRequest) returns (CreateExchangerOfferResponse);
  // CreateExchangerOfferListing rpc is useful for creating listing for an ExchangerOffer.
  rpc CreateExchangerOfferListing (CreateExchangerOfferListingRequest) returns (CreateExchangerOfferListingResponse);
  // CreateExchangerOfferInventory creates a new inventory entry to be used in exchanger-offers;
  rpc CreateExchangerOfferInventory (CreateExchangerOfferInventoryRequest) returns (CreateExchangerOfferInventoryResponse);
  // UpdateExchangerOfferDisplay updates the display details of the exchanger offer
  rpc UpdateExchangerOfferDisplay (UpdateExchangerOfferDisplayRequest) returns (UpdateExchangerOfferDisplayResponse);
  // UpdateExchangerOfferStatus updates the status of the exchanger offer
  rpc UpdateExchangerOfferStatus (UpdateExchangerOfferStatusRequest) returns (UpdateExchangerOfferStatusResponse);
  // UpdateExchangerOfferListing updates the listing of exchanger offer
  rpc UpdateExchangerOfferListing (UpdateExchangerOfferListingRequest) returns (UpdateExchangerOfferListingResponse);
  // DeleteExchangerOfferListing deletes the listing of exchanger offer
  rpc DeleteExchangerOfferListing (DeleteExchangerOfferListingRequest) returns (DeleteExchangerOfferListingResponse);
  // GetExchangerOffers rpc is useful to fetch active exchanger offers for an actor.
  rpc GetExchangerOffers (GetExchangerOffersRequest) returns (GetExchangerOffersResponse);
  // GetExchangerOffersByIds rpc is used to fetch exchanger-offers using ids
  rpc GetExchangerOffersByIds (GetExchangerOffersByIdsRequest) returns (GetExchangerOffersByIdsResponse);
  // RedeemExchangerOffer rpc is useful for redeeming an ExchangerOffer for an actor.
  rpc RedeemExchangerOffer (RedeemExchangerOfferRequest) returns (RedeemExchangerOfferResponse);
  // GetExchangerOfferOrdersForActor rpc is useful to fetch ExchangerOfferOrders for an actor.
  rpc GetExchangerOfferOrdersForActor (GetExchangerOfferOrdersForActorRequest) returns (GetExchangerOfferOrdersForActorResponse);
  // GetExchangerOfferOrders rpc is useful to fetch ExchangerOfferOrders.
  rpc GetExchangerOfferOrders (GetExchangerOfferOrdersRequest) returns (GetExchangerOfferOrdersResponse);
  // GetExchangerOrderById is used for getting exchanger order by exchangerOrderId
  rpc GetExchangerOrderById (GetExchangerOrderByIdRequest) returns (GetExchangerOrderByIdResponse);
  // GetExchangerOfferActorAttempts returns attempts made by actor to redeem an exchange offer,
  // within a given time period.
  // TODO(kunal): Add pagination on need basis later.
  rpc GetExchangerOfferActorAttempts (GetExchangerOfferActorAttemptsRequest) returns (GetExchangerOfferActorAttemptsResponse);
  // GetExchangerOffersActorAttemptsCount returns the count of attempts made by an actor to redeem the exchanger-offers
  rpc GetExchangerOffersActorAttemptsCount (GetExchangerOffersActorAttemptsCountRequest) returns (GetExchangerOffersActorAttemptsCountResponse);
  // ChooseExchangerOrderOption rpc chooses the option and proceeds for its fulfilment
  rpc ChooseExchangerOrderOption (ChooseExchangerOrderOptionRequest) returns (ChooseExchangerOrderOptionResponse);
  // SubmitUserInputForChosenOption submits the user-input needed for fulfilling the chosen exchanger offer option.
  // For e.g., shipping address for physical merchandise
  rpc SubmitUserInputForChosenOption (SubmitUserInputForChosenOptionRequest) returns (SubmitUserInputForChosenOptionResponse);
  // GetExchangerOffersSummary rpc returns the summary of exchanger offer cash/fi-coins similar to rewards summary
  rpc GetExchangerOffersOrdersSummary (GetExchangerOffersOrdersSummaryRequest) returns (GetExchangerOffersOrdersSummaryResponse);
  // CreateExchangerOfferGroup rpc creates a new exchangerOfferGroup
  rpc CreateExchangerOfferGroup (CreateExchangerOfferGroupRequest) returns (CreateExchangerOfferGroupResponse);
  // GetExchangerOfferGroupsByIds fetches the exchanger-offer groups by ids
  rpc GetExchangerOfferGroupsByIds (GetExchangerOfferGroupsByIdsRequest) returns (GetExchangerOfferGroupsByIdsResponse);
  // GetEOGroupsRewardUnitsActorUtilisation returns the utilisation of reward units by an actor under exchanger-offer groups
  rpc GetEOGroupsRewardUnitsActorUtilisation (GetEOGroupsRewardUnitsActorUtilisationRequest) returns (GetEOGroupsRewardUnitsActorUtilisationResponse);
  // IncrementExchangerOfferInventory increments the exchanger offer inventory by id
  rpc IncrementExchangerOfferInventory (IncrementExchangerOfferInventoryRequest) returns (IncrementExchangerOfferInventoryResponse);
  // rpc to decrypt offer order details for given exchanger offer orders, example EGV details like pin, number, etc.
  rpc DecryptExchangerOfferOrdersDetails (DecryptExchangerOfferOrdersDetailsRequest) returns (DecryptExchangerOfferOrdersDetailsResponse);
  // rpc to fetch monthly redemption counts for given actor and offerIds, for the given month
  rpc GetRedemptionCountsForActorOfferIdsInMonth (GetRedemptionCountsForActorOfferIdsInMonthRequest) returns (GetRedemptionCountsForActorOfferIdsInMonthResponse) {};
  // GetExchangerOffersByFilters rpc is useful to fetch all exchanger offers for given filters.
  rpc GetExchangerOffersByFilters (GetExchangerOffersByFiltersRequest) returns (GetExchangerOffersByFiltersResponse);
}

message SubmitUserInputForChosenOptionRequest {
  // actor for whom the input is being submitted
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // corresponding id of the order created for the exchanger_offer after redeeming
  string exchanger_order_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // shipping address for physical merchandise
  google.type.PostalAddress shipping_address = 3;
  // todo(rohanchougule): add more fields if different types of user-inputs come in
}

message SubmitUserInputForChosenOptionResponse {
  // rpc response status
  rpc.Status status = 1;
  // updated exchanger-offer-order after submitting the user-input
  ExchangerOfferOrder exchanger_offer_order = 2;
}

message CreateExchangerOfferInventoryRequest {
  // description of the inventory
  string description = 1;
  // denotes the type of reward being managed by the inventory
  RewardType reward_type = 2;
  // initialized count of the inventory
  int32 total_count = 3;
  // max number of inventory items that can be given to a single user
  int32 per_user_limit = 4;
}

message CreateExchangerOfferInventoryResponse {
  rpc.Status status = 1;

  // exchanger-offer-inventory
  ExchangerOfferInventory exchanger_offer_inventory = 2;
}

message GetExchangerOfferGroupsByIdsRequest {
  repeated string group_ids = 1;
}

message GetExchangerOfferGroupsByIdsResponse {
  rpc.Status status = 1;

  // mappinng of exchanger-offer group_id to group
  map<string, ExchangerOfferGroup> group_id_to_group_map = 2;
}

message GetEOGroupsRewardUnitsActorUtilisationRequest {
  string actor_id = 1;
  repeated string group_ids = 2;
}

message GetEOGroupsRewardUnitsActorUtilisationResponse {
  rpc.Status status = 1;

  // mapping of exchanger-offer group_id and reward-units utilisation by the actor
  map<string, ExchangerOfferGroupRewardUnitsActorUtilisation> group_id_to_utilisation_map = 2;
}

message GetExchangerOffersOrdersSummaryRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  message Filters {
    string offer_id = 1;
    repeated ExchangerOfferOrderState states = 2;
    repeated RewardType reward_types = 3;
  }
  Filters filters = 2;
}

message GetExchangerOffersOrdersSummaryResponse {
  // rpc status
  rpc.Status status = 1;

  // total cash reward amount earned.
  google.type.Money cash_earned = 2;
  // total ficoins  earned
  int32 fi_coins_earned = 3;
  // total cash amount that is in_processing state.
  // i.e claimed but haven't been credited yet.
  google.type.Money in_process_cash_reward_amount = 4;
  // total fi coins that are in_processing
  // i.e claimed but haven't been credited yet.
  int32 in_process_fi_coins = 5;
  // total count of cash rewards.
  int32 cash_reward_count = 6;
  // total count of fi-coins rewards.
  int32 fi_coins_reward_count = 7;
}

message CreateExchangerOfferRequest {
  // currency through which given ExchangerOffer can be redeemed like FI_COINS.
  ExchangerOfferRedemptionCurrency redemption_currency = 1 [(validate.rules).enum = {not_in: [0]}];
  // price of redeeming the ExchangerOffer.
  float redemption_price = 2;
  // display details of the offer
  ExchangerOfferDisplayDetails offer_display_details = 3 [(validate.rules).message.required = true];
  // config to generate reward options on redeeming the ExchangerOffer.
  ExchangerOfferOptionsConfig offer_options_config = 4 [(validate.rules).message.required = true];
  // stores aggregates related config for an ExchangerOffer.
  ExchangerOfferAggregatesConfig offer_aggregates_config = 5 [(validate.rules).message.required = true];
  // exchanger-offer group id
  string group_id = 6;
  // additional details related to offer
  ExchangerOfferAdditionalDetails additional_details = 7 [(validate.rules).message.required = true];
  // manually applied tags
  repeated TagName manual_tags = 16;
  // category tag of the offer
  CategoryTag category_tag = 17;
  // sub category tag of the offer
  SubCategoryTag sub_category_tag = 18;
}

message CreateExchangerOfferResponse {
  // rpc response status
  rpc.Status status = 1;
  // exchanger offer
  ExchangerOffer exchanger_offer = 2;
}

message CreateExchangerOfferListingRequest {
  // exchanger_offer for which listing is created
  string exchanger_offer_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // since when listing is available for redemption
  string active_since = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // till when listing is available for redemption
  string active_till = 3 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // till when listing is available for display
  string display_since = 4 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // till when listing is available for display
  string display_till = 5 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message CreateExchangerOfferListingResponse {
  // rpc response status
  rpc.Status status = 1;
  // exchanger offer listing
  ExchangerOfferListing exchanger_offer_listing = 2;
}

message UpdateExchangerOfferDisplayRequest {
  // id of the offer which needs to be updated
  string offer_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // display details of the offer
  ExchangerOfferDisplayDetails new_display_details = 2;
  // additional details of the offer
  ExchangerOfferAdditionalDetails new_additional_details = 3;
  // updated list of tags applied to the offer
  repeated TagName new_manual_tags = 4;
  // updated category tag of the offer
  CategoryTag category_tag = 5;
  // updated sub category tag of the offer
  SubCategoryTag sub_category_tag = 6;
}

message UpdateExchangerOfferDisplayResponse {
  // rpc response status
  rpc.Status status = 1;
  // updated exchanger offer
  ExchangerOffer exchanger_offer = 2;
}

message UpdateExchangerOfferStatusRequest {
  // exchanger offer id
  string offer_id = 1;
  // new status to update to
  ExchangerOfferStatus new_status = 2;
}

message UpdateExchangerOfferStatusResponse {
  // rpc response status
  rpc.Status status = 1;
  // updated exchanger offer
  ExchangerOffer exchanger_offer = 2;
}

message UpdateExchangerOfferListingRequest {
  // listing id of the offer which needs to be updated
  string offer_listing_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // since when listing is available for redemption
  string active_since = 2;
  // till when listing is available for redemption
  string active_till = 3;
  // till when listing is available for display
  string display_since = 4;
  // till when listing is available for display
  string display_till = 5;
}

message UpdateExchangerOfferListingResponse {
  // rpc response status
  rpc.Status status = 1;
  // updated exchanger offer listing
  ExchangerOfferListing offer_listing = 2;
}

message DeleteExchangerOfferListingRequest {
  // listing id of the offer which needs to be deleted
  string offer_listing_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message DeleteExchangerOfferListingResponse {
  // rpc response status
  rpc.Status status = 1;
}

message GetExchangerOffersRequest {
  // actor for whom the offers need to be fetched.
  // optional
  string actor_id = 1;

  // filters to decide which offers to return in response
  // deprecated: Use FiltersV2
  CatalogFilters filters = 4 [deprecated = true];

  // filters to decide which offers to return in response
  FiltersV2 filters_v2 = 2;

  message FiltersV2 {
    // filters specific to the catalog screen (and can also include common filters across offers for the catalog screen)
    CatalogFilters catalog_filters = 1;
    // status of exchanger offer
    ExchangerOfferStatus status = 2;
  }
}

message GetExchangerOffersResponse {
  // rpc response status
  rpc.Status status = 1;
  // list of active exchanger offers
  repeated ExchangerOffer exchanger_offers = 2;
  // exchanger offer id to offer listing mapping
  map<string, ExchangerOfferListing> offer_id_to_listing_map = 3;
}

message GetExchangerOffersByIdsRequest {
  repeated string ids = 1;
  // filters to decide which offers to return in response
  Filters filters = 2;

  message Filters {
    // status of exchanger offer
    ExchangerOfferStatus status = 1;
  }
}

message GetExchangerOffersByIdsResponse {
  // rpc response status
  rpc.Status status = 1;
  // requested exchanger-offers
  repeated ExchangerOffer exchanger_offers = 2;
}

message RedeemExchangerOfferRequest {
  // unique id of an exchanger offer that is to be redeemed.
  string exchanger_offer_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // actor who is redeeming the exchanger offer
  string actor_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // (client) request id for idempotency check
  string request_id = 3 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // todo (utkarsh) : add any other necessary details needed for redeeming an exchanger offer
}

message RedeemExchangerOfferResponse {
  // rpc response status
  rpc.Status status = 1;
  // order created for exchanger offer for the actor
  ExchangerOfferOrder exchanger_offer_order = 2;

  enum Status {
    OK = 0;

    // this status means that user's account is under credit freeze, and redemption is not possible.
    CREDIT_FROZEN = 100;
    SAVING_ACCOUNT_NOT_FOUND = 101;
  }
}

message GetExchangerOfferOrdersForActorRequest {
  // actor for who exchanger offer orders need to be fetched
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  message Filters {
    reserved 6;
    string offer_id = 1;
    repeated ExchangerOfferOrderState states = 2;
    google.protobuf.Timestamp from_date = 3;
    google.protobuf.Timestamp upto_date = 4;
    repeated casper.exchanger.RewardType reward_types = 5;
    // filter based on expiry status of the reward
    // REWARD_EXPIRY_STATUS_EXPIRED is currently only supported for EGV reward type and this filter will return only EGV type (chosen only) expired rewards
    // REWARD_EXPIRY_STATUS_NOT_EXPIRED returns all exchanger rewards which are not EGV type or EGV exchanger rewards which are not expired
    RewardExpiryStatus reward_expiry_status = 7;
    // filters present in WithinOrFilters are ORed together but ANDed with other filters present in Filters
    OrFilters within_or_filters = 8;
  }
  Filters filters = 2;

  // page context to help server fetch the page
  rpc.PageContextRequest page_context = 3;

  message OrFilters {
    repeated casper.exchanger.RewardType reward_types = 1;
    // filter based on tags present in the ExchangerOffer
    // if ANY of the passed tags is present in the ExchangerOffer, corresponding ExchangerOfferOrder will be returned
    repeated casper.TagName or_offer_tags = 2;
  }
}

message GetExchangerOfferOrdersForActorResponse {
  // rpc response status
  rpc.Status status = 1;
  // exchanger offer orders list
  repeated ExchangerOfferOrder exchanger_offer_orders = 2;
  // page context to help client fetch the next page
  rpc.PageContextResponse page_context = 3;
}

message GetExchangerOfferOrdersRequest {
  // actor for who exchanger offer orders need to be fetched
  // optional
  string actor_id = 1;

  message Filters {
    string offer_id = 1;
    repeated ExchangerOfferOrderState states = 2;
    google.protobuf.Timestamp from_date = 3;
    google.protobuf.Timestamp upto_date = 4;
    repeated casper.exchanger.RewardType reward_types = 5;
  }
  Filters filters = 2;

  // page context to help server fetch the page
  rpc.PageContextRequest page_context = 3;
}

message GetExchangerOfferOrdersResponse {
  // rpc response status
  rpc.Status status = 1;
  // exchanger offer orders list
  repeated ExchangerOfferOrder exchanger_offer_orders = 2;
  // page context to help client fetch the next page
  rpc.PageContextResponse page_context = 3;
}

message GetExchangerOrderByIdRequest {
  string exchanger_order_id = 1;
}

message GetExchangerOrderByIdResponse {
  // rpc response status
  rpc.Status status = 1;

  ExchangerOfferOrder exchanger_order = 2;
}

message GetExchangerOfferActorAttemptsRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // Exchanger offer which was redeemed
  string exchanger_offer_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // From & To timestamp between which redemptions needs to be fetched,
  // including fromAttemptedTime but excluding toAttemptedTime,
  // which is [fromAttemptedTime, toAttemptedTime)
  google.protobuf.Timestamp fromAttemptedTime = 3 [(validate.rules).timestamp.required = true];
  google.protobuf.Timestamp toAttemptedTime = 4 [(validate.rules).timestamp.required = true];
}

message GetExchangerOfferActorAttemptsResponse {
  // rpc response status
  rpc.Status status = 1;

  // exchanger offer actor attempts list
  repeated ExchangerOfferActorAttempt exchanger_offer_actor_attempts = 2;
}

message GetExchangerOffersActorAttemptsCountRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // list of exchanger-offers for which the attempts count is required
  repeated string exchanger_offer_ids = 2 [(validate.rules).repeated.items.string = {min_len: 4, max_len: 100}];

  // From & To timestamp between which redemptions/attempts needs to be fetched,
  // including fromAttemptedTime but excluding toAttemptedTime,
  // which is [fromAttemptedTime, toAttemptedTime)
  google.protobuf.Timestamp fromAttemptedTime = 3 [(validate.rules).timestamp.required = true];
  google.protobuf.Timestamp toAttemptedTime = 4 [(validate.rules).timestamp.required = true];
}

message GetExchangerOffersActorAttemptsCountResponse {
  // rpc response status
  rpc.Status status = 1;

  // mapping of exchanger-offer-ids and the attempts count
  map<string, int32> offer_id_to_attempts_count_map = 2;
}

message ChooseExchangerOrderOptionRequest {
  // actor who is choosing the option
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // corresponding id of the order created for the exchanger_offer after redeeming
  string exchanger_order_id = 3 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // id of the option selected under the exchanger_order
  string option_id = 4 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message ChooseExchangerOrderOptionResponse {
  // rpc response status
  rpc.Status status = 1;
  // exchanger-offer-order whose option was chosen
  ExchangerOfferOrder exchanger_offer_order = 2;
}

message CreateExchangerOfferGroupRequest {
  // description of group
  string description = 1;
  // rewardType units caps config for an actor across all exchanger offers in a group
  RewardUnitsCapAggregate EO_group_reward_units_cap_user_aggregate = 2;
}

message CreateExchangerOfferGroupResponse {
  rpc.Status status = 1;
  // exchangerOffer group
  ExchangerOfferGroup exchanger_offer_group = 2;
}

message IncrementExchangerOfferInventoryRequest {
  // exchanger offer inventory id
  string exchanger_offer_inventory_id = 1;
  // count by which inventory should be increased
  int32 increment_count = 2 [(validate.rules).int32.gt = 0];
}

message IncrementExchangerOfferInventoryResponse {
  // rpc response status
  rpc.Status status = 1;
  // updated exchanger offer inventory
  ExchangerOfferInventory exchanger_offer_inventory = 2;
}

message DecryptExchangerOfferOrdersDetailsRequest {
  // exchanger offer orders with encrypted offer details
  repeated ExchangerOfferOrder exchanger_offer_orders = 1;
}

message DecryptExchangerOfferOrdersDetailsResponse {
  // rpc status
  rpc.Status status = 1;
  // exchanger offer orders with decrypted offer details
  repeated ExchangerOfferOrder exchanger_offer_orders = 2;
}

message GetRedemptionCountsForActorOfferIdsInMonthRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  repeated string offer_ids = 2 [(validate.rules).repeated.min_items = 1];
  // timestamp from the month for which we want to fetch count of redemptions.
  // only the month/year will be used from the given timestamp.
  google.protobuf.Timestamp month_timestamp = 3 [(validate.rules).timestamp.required = true];
}

message GetRedemptionCountsForActorOfferIdsInMonthResponse {
  // rpc status
  rpc.Status status = 1;
  // exchanger offer orders with decrypted offer details
  map<string, uint32> offer_id_to_redemptions_count_in_month_map = 2;
}

message GetExchangerOffersByFiltersRequest {
  // actor for whom the offers need to be fetched.
  // optional
  string actor_id = 1;

  // filters to decide which offers to return in response
  ExchangerOfferFilters filters = 2;
}

message ExchangerOfferFilters {
  // filters specific to the catalog screen (and can also include common filters across offers for the catalog screen)
  CatalogFilters catalog_filters = 1;
  // status of exchanger offer
  ExchangerOfferStatus status = 2;
  // time window in which we want to fetch exchanger offers
  google.protobuf.Timestamp from_time = 3;
  google.protobuf.Timestamp till_time = 4;
}

message GetExchangerOffersByFiltersResponse {
  // rpc response status
  rpc.Status status = 1;
  // list of active exchanger offers
  repeated ExchangerOffer exchanger_offers = 2;
  // exchanger offer id to offer listing mapping
  map<string, ExchangerOfferListing> offer_id_to_listing_map = 3;
}
