syntax = "proto3";

package casper.exchanger;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/casper/exchanger";
option java_package = "com.github.epifi.gamma.api.casper.exchanger";

message ExchangerOfferUserLevelInventoryUnitsUtilisation {
  // unique id of the entry
  string id = 1;

  string actor_id = 2;

  string inventory_id = 3;

  // current utilisation of inventory by actor
  int32 utilisation = 4;

  // timestamps
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
}
