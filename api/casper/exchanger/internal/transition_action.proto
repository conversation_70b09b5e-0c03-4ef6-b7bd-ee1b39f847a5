syntax = "proto3";

package casper.exchanger;

import "google/type/postal_address.proto";

option go_package = "github.com/epifi/gamma/api/casper/exchanger";
option java_package = "com.github.epifi.gamma.api.casper.exchanger";

// To redeem an exchangerOffer, the state processor goes transitions
// across multiple states. TransitionActionType represents the transition
// between states which requires payload to transition to next step.
enum TransitionActionType {
  TRANSITION_ACTION_TYPE_UNSPECIFIED = 0;
  CHOOSE_REWARD_OPTION = 1;
  ADD_USER_INPUT = 2;
}

// Payload need to transition from CHOOSE_REWARD_OPTION
message ChooseRewardOptionTransitionActionPayload {
  // id of option that is to be chosen.
  string chosen_option_id = 1;
}

// Payload need to transition from USER_INPUT_PENDING
message AddUserInputTransitionActionPayload {
  // needed for physical merchandise
  google.type.PostalAddress shipping_address = 1;
  // todo(rohanchougule): add more fields if different types of user-inputs come in
}

// TransitionAction stores action data needed for performing state transitions in redemption state machine.
message TransitionAction {
  // type of transition action
  TransitionActionType action_type = 1;
  // transition action payload
  oneof action_payload {
    ChooseRewardOptionTransitionActionPayload choose_reward_option_action_payload = 2;
    AddUserInputTransitionActionPayload add_user_input_action_payload = 3;
  }
}
