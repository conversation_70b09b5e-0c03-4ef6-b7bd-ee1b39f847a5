//go:generate gen_queue_pb
syntax = "proto3";

package casper.external_vendor_redemption.notification;

import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/casper/external_vendor_redemption/notification";
option java_package = "com.github.epifi.gamma.api.casper.external_vendor_redemption.notification";

service Consumer {
  // consumes fi store order notification event to send comms to user.
  rpc ProcessFiStoreOrderNotificationEvent(OrderNotificationEvent) returns (ConsumerResponse);
}

message ConsumerResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message OrderNotificationEvent {
  // common request header across all the consumer grpc services.
  queue.ConsumerRequestHeader request_header = 1;

  // actor for whom we should send comms
  string actor_id = 2;

  // vendor redemption id
  string redemption_id = 3;

  // type of notification to trigger
  NotificationTrigger notification_trigger = 4;

  // category of notification to trigger
  NotificationCategory notification_category = 5;

  // type of medium to send comms.
  Medium medium = 6;

  // time at which event triggered
  google.protobuf.Timestamp event_timestamp = 7;
}

// Specifies the category of the notification to be used.
enum NotificationCategory {
  NOTIFICATION_CATEGORY_UNSPECIFIED = 0;
  // Specifies that notification is sent for ecom store category.
  NOTIFICATION_CATEGORY_ECOM_STORE = 1;
  // Specifies that notification is sent for flights category.
  NOTIFICATION_CATEGORY_FLIGHTS = 2;
  // Specifies that notification is sent for hotels category.
  NOTIFICATION_CATEGORY_HOTELS = 3;
  // Specifies that notification is sent for gift cards category.
  NOTIFICATION_CATEGORY_GIFT_CARDS = 4;
  // Specifies that notification is sent for ecom discounts category.
  NOTIFICATION_CATEGORY_ECOM_DISCOUNTS = 5;
  // Specifies that notification is sent for miles exchange category
  NOTIFICATION_CATEGORY_MILES_EXCHANGE = 6;
}

//  Specifies the medium on which the message is to be sent
enum Medium {
  MEDIUM_UNSPECIFIED = 0;
  // Specifies that a notification has to send to user's device
  NOTIFICATION = 1;
  // Specifies that the message must be sent through an SMS
  SMS = 2;
}

// NotificationTrigger helps us determine which notification template is to be used while sending comms
enum NotificationTrigger {
  NOTIFICATION_TRIGGER_UNSPECIFIED = 0;
  // when order is confirmed
  NOTIFICATION_TRIGGER_ORDER_CONFIRMATION = 1;
  // when order status is updated can be shipped, delivered etc..
  NOTIFICATION_TRIGGER_ORDER_STATUS_UPDATE = 2;
  // when order status is cancelled
  NOTIFICATION_TRIGGER_ORDER_CANCELLATION = 3;
  // when order is refunded for any reason like not fulfilled by vendor etc..
  NOTIFICATION_TRIGGER_ORDER_REFUND = 4;
  // when order is placed and fi-coins have been debited.
  NOTIFICATION_TRIGGER_ORDER_PLACED = 5;
}
