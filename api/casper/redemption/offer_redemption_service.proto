syntax = "proto3";

package casper;

import "api/casper/offer_catalog.proto";
import "api/casper/redemption/redeemed_offer.proto";
import "api/casper/redemption/redemption_request.proto";
import "api/casper/tag.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/casper/redemption";
option java_package = "com.github.epifi.gamma.api.casper.redemption";

service OfferRedemptionService {
  // useful to initiate a new offer redemption.
  rpc InitiateRedemption (InitiateRedemptionRequest) returns (InitiateRedemptionResponse) {};
  // useful to confirm an already initiated offer redemption.
  rpc ConfirmRedemption (ConfirmRedemptionRequest) returns (ConfirmRedemptionResponse) {};
  // useful for fetching the current state of a given redeemed offer.
  rpc GetOfferRedemptionState (GetOfferRedemptionStateRequest) returns (GetOfferRedemptionStateResponse) {};
  // useful to fetch redeemed offers for actor with filters.
  rpc GetRedeemedOffersForActor (GetRedeemedOffersForActorRequest) returns (GetRedeemedOffersForActorResponse) {};
  // useful for decrypting encrypted offer details for given redeemed offers.
  rpc DecryptRedeemedOffersDetails (DecryptRedeemedOffersDetailsRequest) returns (DecryptRedeemedOffersDetailsResponse) {};
  // useful for fetching a redeemed offer by id.
  rpc GetRedeemedOfferById (GetRedeemedOfferByIdRequest) returns (GetRedeemedOfferByIdResponse) {};
  // useful for getting a url where the user can be re-directed to view the redeemed offers which were fulfilled by the given vendor,
  // useful for offers where the post the offer redemption on the Fi App, the voucher details are visible on vendor app only like thriwe benefits package offers.
  rpc GetRedeemedOfferVendorRedirectionUrl (GetRedeemedOfferVendorRedirectionUrlRequest) returns (GetRedeemedOfferVendorRedirectionUrlResponse);
}


message InitiateRedemptionRequest {
  // identifier of offer that is to be redeemed.
  string offer_id = 1;

  // actor whose is redeeming the offer.
  string actor_id = 2;

  // request metadata
  RedemptionRequestMetadata request_metadata = 3;
}

message InitiateRedemptionResponse {
  // rpc status
  rpc.Status status = 1;

  // request id for redemption
  // to be used for confirming redemption and redemption status check
  string redemption_request_id = 2;

  // failure reason. will be unspecified for success and generic failure cases
  FailureReason failure_reason = 3;

  // reason for failure of initiation of redemption
  enum FailureReason {
    FAILURE_REASON_UNSPECIFIED = 0;
    // indicates that the CV ID contained in request metadata is invalid. Only applicable to Vistara Air miles redemptions
    FAILURE_REASON_INVALID_CV_ID = 1;
    // indicates that validation of ClubVistara membership failed, i.e.,
    // given emailId isn't linked to given CV ID
    FAILURE_REASON_CV_MEMBERSHIP_VALIDATION_FAILURE = 2;
    // indicates that vendor api failed unexpectedly
    FAILURE_REASON_VENDOR_API_FAILURE = 3;
    // indicates that no user exists with given emailId
    FAILURE_REASON_VISTARA_USER_DOES_NOT_EXIST = 4;
    // indicates that the offer is not redeemable
    FAILURE_REASON_NON_REDEEMABLE_OFFER = 5;
  }
}

message ConfirmRedemptionRequest {
  // request id returned by initiate redemption
  string redemption_request_id = 1;
  // time duration till which the RPC will poll the state machine before returning with latest status (in seconds)
  // todo: remove this field and create another RPC for polling status
  uint32 polling_time_duration_in_secs = 2;
}

message ConfirmRedemptionResponse {
  // rpc status
  rpc.Status status = 1;

  // collected/redeemed offer details
  RedeemedOffer redeemed_offer = 2;
}

message GetOfferRedemptionStateRequest {
  // request id returned by initiate redemption
  string redemption_request_id = 1;
}

message GetOfferRedemptionStateResponse {
  // rpc status
  rpc.Status status = 1;

  OfferRedemptionState redemption_state = 2;
}

message GetRedeemedOffersForActorRequest {
  message Filters {
    string redeemed_offer_id = 1;
    casper.OfferType offer_type = 2;
    casper.OfferVendor vendor = 3;
    // Deprecated : Please use redemption_states
    OfferRedemptionState redemption_state = 4;
    google.protobuf.Timestamp from_date = 5;
    google.protobuf.Timestamp upto_date = 6;
    // some offers like EGV can expire.
    enum ExpiryStatus {
      UNSPECIFIED_EXPIRY_STATUS = 0;
      // denotes offer has expired
      EXPIRED = 2;
      // denotes ofer is still active
      NOT_EXPIRED = 3;
    }
    // if expiry_status is not specified both expired
    // and not expired offers will be fetched.
    ExpiryStatus expiry_status = 7;
    repeated OfferRedemptionState redemption_states = 8;
    // offer id which we want to filter results for
    string offer_id = 9;
    // filter based on tags present in the casper.Offer
    // if ANY of the passed tags is present in the offer, corresponding redeemed offer will be returned
    repeated casper.TagName or_offer_tags = 10;
  }
  // id of actor
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // page context to help server fetch the page
  rpc.PageContextRequest page_context = 2;
  // filters
  Filters filters = 3;
}

message GetRedeemedOffersForActorResponse {
  // rpc status
  rpc.Status status = 1;

  repeated RedeemedOffer redeemed_offers = 2;

  // page context to help client fetch next page
  rpc.PageContextResponse page_context = 4;
}

message DecryptRedeemedOffersDetailsRequest {
  // redeemed offers with encrypted offer details
  repeated RedeemedOffer redeemed_offers = 1;
}

message DecryptRedeemedOffersDetailsResponse {
  // rpc status
  rpc.Status status = 1;
  // redeemed offers with decrypted offer details
  repeated RedeemedOffer redeemed_offers = 2;
}

message GetRedeemedOfferByIdRequest {
  string redeemed_offer_id = 1;
}

message GetRedeemedOfferByIdResponse {
  // rpc status
  rpc.Status status = 1;

  RedeemedOffer redeemed_offer = 2;
}

message GetRedeemedOfferVendorRedirectionUrlRequest {
  // actor for whom redirection url needs to be fetched.
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // offer vendor for which redirection url needs to be fetched.
  // todo (utkarsh) : add proto validate on this after fixing the proto pkg structure
  casper.OfferVendor offer_vendor = 2;
}

message GetRedeemedOfferVendorRedirectionUrlResponse {
  // rpc status
  rpc.Status status = 1;
  // url where the user needs to be redirected to view their redeemed offer details
  string redirection_url = 2;
}
