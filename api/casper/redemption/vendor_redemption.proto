//go:generate gen_sql -types=VendorRedemptionMetadata
syntax = "proto3";

package casper;

import "api/casper/offer_catalog.proto";
import "api/casper/offer_vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/casper/redemption";
option java_package = "com.github.epifi.gamma.api.casper.redemption";

message VendorRedemption {
  string id = 1;

  // fi internal id of redeemed offer
  string ref_id = 2;

  // unique vendor ref id used for linking our redemption to redemption on vendor side
  string vendor_ref_id = 3;

  // actor who performed the redemption
  string actor_id = 4;

  // vendor of offer redeemed
  OfferVendor offer_vendor = 5;

  // type of offer redeemed
  OfferType offer_type = 6;

  VendorRedemptionMetadata metadata = 7;

  // source of request
  RequestSource request_source = 8;

  // time at which vendor redemption entry was created in system.
  google.protobuf.Timestamp created_at = 10;

  // time at which vendor redemption entry was updated in system.
  google.protobuf.Timestamp updated_at = 11;

  // time at which vendor redemption entry was deleted in system.
  google.protobuf.Timestamp deleted_at = 12;
}

message VendorRedemptionMetadata {
  oneof vendor_redemption_metadata {
    ClubItcGreenPointsMetadata club_itc_green_points_metadata = 1;
  }
}

message ClubItcGreenPointsMetadata {
  // name of the user to which points are issued
  string user_name = 1;
  // club itc id of the user to which points are issued
  string club_itc_id = 2;
  // number of green points issued to the user
  int32 club_itc_green_points = 3;
}
