syntax = "proto3";

package casper;


option go_package = "github.com/epifi/gamma/api/casper";
option java_package = "com.github.epifi.gamma.api.casper";

// page context to set offset, and limit in the request
// 1st request will have *limit* flag to specify number of elements from backend
// backend will send `PageContext` -- setting before_token and after_token
// next paginated request will send the same PageContext which will have 2 tokens and 2 flags
// to identify `next` and `before` token to paginate forward and backward
// read more on this - "https://hackernoon.com/guys-were-doing-pagination-wrong-f6c18a91b232"
message PageContextResponse {
  // token to paginate backward in the list
  string before_token = 1;
  // if items are present before before_token
  bool has_before = 2;
  // token to paginate forward in the list
  string after_token = 3;
  // if items are present after after_token
  bool has_after = 4;
}

message PageContextRequest {
  oneof token {
    string before_token = 1;
    string after_token = 2;
  }
  // page size
  uint32 page_size = 3;
}



