syntax = "proto3";

package whatsapp_bot;

option go_package = "github.com/epifi/gamma/api/whatsapp_bot";
option java_package = "com.github.epifi.gamma.api.whatsapp_bot";

// represents users intent i.e information he is trying to get or action he is trying to perform using whatsapp bot
// this will contain exhaustive list of all the things we are supporting via whatsapp bot
enum UserIntent {
  USER_INTENT_UNSPECIFIED = 0;
  // user wants to check his status
  CHECK_STATUS = 1;
  // user wants to get help via whatsapp
  GET_HELP = 2;
  // user wants to stop receiving communications via whatsapp
  STOP = 3;
  // user wants to start receiving communications via whatsapp
  START = 4;
  // users intent doesn't match with any of the options given
  // this is default fallback intent in cases where we are not able to understand user intent
  GENERIC = 5;
  // users sends some intial hi, hello, hey etc msg(this will be configured by default in the whatsapp link we will send to users)
  HELLO = 6;
  // users wants to check fi benefits
  BENEFITS = 7;
  // users wants to check fi features
  FEATURES = 8;
}
