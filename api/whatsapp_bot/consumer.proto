syntax = "proto3";

package whatsapp_bot;

import "api/queue/consumer_headers.proto";
import "api/typesv2/common/phone_number.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/whatsapp_bot";
option java_package = "com.github.epifi.gamma.api.whatsapp_bot";

service WhatsappBotConsumer {
  // this method will consume incoming whatsapp messages from users and send appropriate response to user
  // it will process the message to figure out user intent
  // and then follow appropriate steps for the given intent and send user a reply
  rpc ProcessUserMessage(ProcessUserMessageRequest) returns (ProcessUserMessageResponse);
}

message ProcessUserMessageRequest {
  // A set of all the common attributes to be contained in a queue consumer request
  queue.ConsumerRequestHeader request_header = 1;

  // phone number from which message was sent
  // mandatory
  api.typesv2.common.PhoneNumber phone_number = 2;

  // content of message
  // mandatory
  string message = 3;

  // time at which message was sent by user
  // can be timestamp included in vendor callback or
  // timestamp at which we received callback in case no timestamp is sent by vendor
  google.protobuf.Timestamp message_timestamp = 4;
}

message ProcessUserMessageResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}
