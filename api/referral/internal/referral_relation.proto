// protolint:disable MAX_LINE_LENGTH

/*
Protos relating to the Referral that are internal to the domain such as data models
*/


syntax = "proto3";

package referral;

import "api/referral/service.proto";

option go_package = "github.com/epifi/gamma/api/referral";
option java_package = "com.github.epifi.gamma.api.referral";

// ReferralRelation stores edege between referrer and referee.
message ReferralRelation {
  // Actor who referred in this relationship.
  string referrer_actor_id = 1;
  // Actor who was referred in this relationship.
  string referee_actor_id = 2;
  // Referral code in app scenario, golden ticket code in case of early access.
  string ticket_code = 3;
  // Referral code for what type of referral: Waitlist, App....
  ReferralType referral_type = 4;
}


