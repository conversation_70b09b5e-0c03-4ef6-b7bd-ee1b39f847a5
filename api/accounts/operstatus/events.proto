//go:generate gen_queue_pb
syntax = "proto3";

package accounts.operstatus;

import "api/accounts/operstatus/operational_status.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/accounts/operstatus";
option java_package = "com.github.epifi.gamma.api.accounts.operstatus";


// notification that is published when there is any operational status change
message OperationalStatusUpdateEvent {
  queue.ConsumerRequestHeader request_header = 1;
  OperationalStatusInfo operational_status_info = 2;
}
