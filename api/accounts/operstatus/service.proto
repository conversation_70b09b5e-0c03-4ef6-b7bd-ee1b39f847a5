syntax = "proto3";

package accounts.operstatus;

import "api/accounts/operstatus/operational_status.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/accounts/operstatus";
option java_package = "com.github.epifi.gamma.api.accounts/operstatus";

service OperationalStatusService {
  rpc GetOperationalStatus (GetOperationalStatusRequest) returns (GetOperationalStatusResponse);
}

message GetOperationalStatusRequest {
  enum DataFreshness {
    DATA_FRESHNESS_UNSPECIFIED = 0;
    // makes a fresh vendor call
    DATA_FRESHNESS_REAL_TIME = 1;
    // data is fetched from db if it is not older than 1 week, otherwise makes a vendor call
    DATA_FRESHNESS_RECENT = 2;
    // data is fetched from db if it not older than 10 min, otherwise makes a vendor call
    DATA_FRESHNESS_10_MIN_STALE = 3;
    // data will be fetched from db only, return record not exist if data unavailable
    DATA_FRESHNESS_LAST_KNOWN = 4;
  }
  DataFreshness data_freshness = 1;
  oneof AccountIdentifier {
    string savings_account_id = 2;
  }
}

message GetOperationalStatusResponse {
  enum Status {
    OK = 0;
    // Request parameters invalid
    INVALID_ARGUMENT = 3;
    // No data found for request params
    NOT_FOUND = 5;
    // Internal Error
    INTERNAL = 13;
    // Deadline Exceeded
    DEADLINE_EXCEEDED = 4;
    // Resource Exhausted
    RESOURCE_EXHAUSTED = 8;
    // Status Code for mismatch in account number - phone number combination in Request vs the combination at CBS
    ACC_NUMBER_PHONE_MISMATCH = 106;
  }
  rpc.Status status = 1;
  OperationalStatusInfo operational_status_info = 2;
}
