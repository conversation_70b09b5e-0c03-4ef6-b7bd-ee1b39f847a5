//go:generate gen_sql -types=OperationalStatusFieldMask
syntax = "proto3";

package accounts.enums;

option go_package = "github.com/epifi/gamma/api/accounts/enums";
option java_package = "com.github.epifi.gamma.api.accounts/enums";

enum OperationalStatusFieldMask {
  OPERATIONAL_STATUS_FIELD_MASK_UNSPECIFIED = 0;
  OPERATIONAL_STATUS_FIELD_MASK_OPEN_DATE = 1;
  OPERATIONAL_STATUS_FIELD_MASK_CLOSE_DATE = 2;
  OPERATIONAL_STATUS_FIELD_MASK_OPERATIONAL_STATUS = 3;
  OPERATIONAL_STATUS_FIELD_MASK_FREEZE_STATUS = 4;
  OPERATIONAL_STATUS_FIELD_MASK_LIEN = 5;
  OPERATIONAL_STATUS_FIELD_MASK_VENDOR_RESPONSE = 6;
  OPERATIONAL_STATUS_FIELD_MASK_KYC_COMPLIANCE = 7;
}
