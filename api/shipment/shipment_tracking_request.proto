syntax = "proto3";

package shipment;

import "api/shipment/shipment.proto";
import "api/shipment/enums.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/service_name.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/shipment";
option java_package = "com.github.epifi.gamma.api.shipment";

message ShipmentTrackingRequest {
  string id = 1;
  // ActorId represents unique identifier for each user
  string actor_id = 2;
  // ShipmentType represent type of shipment registering at vendor
  ShipmentType shipment_type = 3;
  // Client represent the service which register the shipment at vendor
  api.typesv2.ServiceName client = 4;
  // Id represents unique identifier for each shipment register at vendor
  string client_request_id = 5;
  // Vendor responsible for tracking the shipment
  vendorgateway.Vendor vendor = 6;
  // ShipmentStatus represent the delivery state of shipment
  ShipmentStatus shipment_status = 7;
  // air_waybill represent the tracking id of shipment
  string air_waybill = 8;
  // CourierPartner represent carrier delivering the shipment
  CourierPartner courier_partner = 9;
  // VendorResponseData represents the vendor specific data related to shipment
  VendorResponseData vendor_resp_data = 10;
  google.protobuf.Timestamp expected_delivery_date = 11;
  google.protobuf.Timestamp pickup_date = 12;
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  int64 deleted_at_unix = 15;
}
