//go:generate gen_sql -types=VendorResponseData
syntax = "proto3";

package shipment;

import "api/shipment/enums.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/service_name.proto";
import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/shipment";
option java_package = "com.github.epifi.gamma.api.shipment";

message ShipmentRequest {
  string actor_id = 1;
  // air_waybill represents the tracking_id of shipment
  string air_waybill = 2;
  // Courier partner for delivery
  CourierPartner courier_partner = 3;
  // client_request_id will be unique for each shipment
  string client_request_id = 4;
  // client initiating the request
  api.typesv2.ServiceName client = 5;
  // ShipmentType represents type of product eg. Cards, Chequebook
  ShipmentType shipment_type = 6;
  // vendor who is responsible for tracking the shipment
  vendorgateway.Vendor vendor = 7;
}

message VendorResponseData {
  oneof ResponseData {
    ShipwayResponseData shipway_response_data = 1;
  }
}

message ShipwayResponseData {
  string tracking_url = 1;
  string from = 2;
  string to = 3;
  string recipient = 4;
  repeated ScanDetail scans = 5;
  // current_status_code is the current status code we will receive in GetOrderShipmentDetails vendor api, eg. DEL, INT
  string current_status_code = 6;
  // current_state_description is description for current_status_code
  string current_state_description = 7;
  // Reference id of courier partner
  string reference = 8;
}

// ScanDetail represents the shipment location and status at time timestamp
message ScanDetail {
  string location = 1;
  google.protobuf.Timestamp timestamp = 2;
  string status_detail = 3;
}
