syntax = "proto3";

package sherlock.dev.dbstate;

import "api/sherlock/dev/db_state/enums.proto";

option go_package = "github.com/epifi/be-common/api/sherlock/dev/dbstate";
option java_package = "com.github.epifi.be-common.api.sherlock.dev.dbstate";

message ParameterMeta {
  // name or key or identifier of the parameter
  // UI will pass this back to backend as identifier for parameter
  string name = 1;

  // label to be shown on UI for the parameter
  string label = 2;

  // data type of parameter
  // UI will show appropriate input type for the given parameter based on this property
  ParameterDataType type = 3;

  // applicable only in case of type DROPDOWN or MULTI_SELECT_DROPDOWN
  // list of options to be shown in dropdown for the parameter
  repeated string options = 4;

  // this field indicates whether the parameter is optional or not in the data request
  // using enum for this because bool defaults to false in go which could cause unexpected behaviour
  ParameterOption parameter_option = 5;
}
