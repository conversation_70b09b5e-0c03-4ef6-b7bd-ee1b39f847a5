//go:generate gen_queue_pb
syntax = "proto3";

package analyser.investment.consumer;

import "api/analyser/investment/model/task.proto";
import "api/investment/aggregator/events/events.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/analyser/investment/consumer";
option java_package = "com.github.epifi.gamma.api.analyser.investment.consumer";

service Consumer {
  // ProcessInvestmentEvent consumer will listen to the InvestmentEvent
  // Events will be processed by investment instrument specific processors
  rpc ProcessInvestmentEvent(api.investment.aggregator.events.InvestmentEvent) returns (ProcessInvestmentEventResponse);

  // ProcessAnalysisTaskEvent processes a list of analysis tasks and persist the results to respective entities
  rpc ProcessAnalysisTasksEvent (investment.model.AnalysisTasksEvent) returns (ProcessAnalysisTaskEventResponse);

  // RefreshMFPortfolioAnalytics refreshes the MF portfolio analytics incrementally for the given list of actors
  rpc RefreshMFPortfolioAnalytics(RefreshMFPortfolioAnalyticsRequest) returns (RefreshMFPortfolioAnalyticsResponse);
}

message ProcessInvestmentEventResponse {
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessAnalysisTaskEventResponse {
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message RefreshMFPortfolioAnalyticsRequest {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;
  // Refresh job will run for all the given actors
  repeated RefreshDetails refresh_details = 2;
}

message RefreshDetails {
  string actor_id = 1;
}

message RefreshMFPortfolioAnalyticsResponse {
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}
