syntax = "proto3";

package api.analyser.dynamicelements;

import "api/dynamic_elements/dynamic_elements.proto";

option go_package = "github.com/epifi/gamma/api/analyser/dynamicelements";
option java_package = "com.github.epifi.gamma.api.analyser.dynamicelements";

// Service dealing with dynamic elements fetch from analysers
service DynamicElements {
  // RPC invoked by the Dynamic elements service to fetch the list of dynamic elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {}

  // RPC used by the Dynamic Elements service to callback on user action on a dynamic element
  // ActorId and ElementId are mandatory parameters in the Request
  // Response contains status code
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no element exists with the given ElementId
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the callback is registered successfully
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse) {}
}
