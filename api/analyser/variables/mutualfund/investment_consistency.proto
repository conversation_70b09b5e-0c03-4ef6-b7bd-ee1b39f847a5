syntax = "proto3";

package api.analyser.variables.mutualfund;

import "api/analyser/investment/service.proto";
import "api/investment/mutualfund/mutual_fund.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/analyser/variables/mutualfund";
option java_package = "com.github.epifi.gamma.api.analyser.variables.mutualfund";

message MfInvestmentActivities {
  // list of investment activities for mutually exclusive time periods
  repeated MfInvestmentActivity investment_activities = 1;
}

// MfInvestmentActivity details aggregate mf investment activity for a given time period
message MfInvestmentActivity {
  google.type.Money invested_amount = 1;
  google.type.Money redeemed_amount = 2;
  google.protobuf.Timestamp from_time = 3;
  google.protobuf.Timestamp to_time = 4;
  // portfolio value at the end of the time period
  google.type.Money portfolio_value = 5;
}

// MfMonthlyInvestmentStatistics contains aggregate statistics about users and peer groups monthly mf investments
message MfMonthlyInvestmentStatistics {
  double users_investment_to_salary_ratio = 1;
  double peers_investment_to_salary_ratio = 2;
  double benchmark_investment_to_salary_rate = 3;
  google.type.Money users_average_monthly_investment = 4;
  google.type.Money users_monthly_salary = 5;
}

// MfPortfolioPerformanceDetails contains details about mutual fund portfolio performance
// e.g. users percentage returns, category average, peers returns etc.
message MfPortfolioPerformanceDetails {
  double user_percentage_returns = 1;
  double category_average_returns = 2;
  double peers_percentage_returns = 3;
  double nifty50_percentage_returns = 4;
  double fixed_deposit_percentage_returns = 5;
}

message MfMarketCapDetails {
  double large_cap_allocation_percentage = 1;
  double mid_cap_allocation_percentage = 2;
  double small_cap_allocation_percentage = 3;
  double others_allocation_percentage = 4;
}

message MfAssetCategoryDetails {
  repeated MfAssetCategoryDetail asset_category_details = 1;
}

message MfAssetCategoryDetail {
  api.investment.mutualfund.AssetClass asset_class = 1;
  double allocation_percentage = 2;
}

message MfSchemeAnalytics {
  // list of mutual fund funds breakdown details
  repeated SchemeAnalytics scheme_analytics = 1;
  .analyser.investment.EnrichedMFPortfolioAnalytics enriched_mf_portfolio_analytics = 2;
}

// FundsBreakdown gives details for specific mf funds breakdown ex. includes amount etc.
message SchemeAnalytics {
  api.investment.mutualfund.MutualFund scheme_detail = 1;
  .analyser.investment.EnrichedMfSchemeAnalytics enriched_analytics = 2;
  api.investment.mutualfund.MutualFundCategoryAverage category_avg_details = 3;
}

message MfSectorDetails {
  // list of mf sector details for any sector (Equity, bond etc.)
  repeated MfSectorDetail sector_details = 1;
}

message MfSectorDetail {
  oneof sector_detail {
    MfEquitySectorDetail equity_sector_detail = 1;
    MfBondSuperSectorDetail bond_super_sector_detail = 2;
  }
}

message MfEquitySectorDetail {
  api.investment.mutualfund.GlobalEquitySector sector_name = 1;
  // equity sector allocation percentage in users portfolio
  double equity_sector_allocation_percentage = 2;
}

message MfBondSuperSectorDetail {
  api.investment.mutualfund.GlobalbondSector super_sector_name = 1;
  // bond sector allocation percentage in users portfolio
  double bond_sector_allocation_percentage = 2;
}

message MfStocksDetails {
  repeated MfStockDetail stock_details = 1;
}

message MfStockDetail {
  string name = 1;
  // ISIN stands for International Securities Identification Numbering system (defined by ISO 6166) and is the global ISO
	// standard for unique identification of financial and referential instruments, including equity, debt, derivatives and indices.
  string isin = 2;
  // allocation percentage represents the percentage share held in the overall portfolio.
  double allocation_percentage = 3;
  // current_value represent the total value of the stock in the overall mf portfolio
  google.type.Money current_value = 4;
}
