syntax = "proto3";

package api.analyser.variables;

import "api/analyser/variables/mutualfund/investment_consistency.proto";
import "api/analyser/variables/networth/portfolio_summary.proto";
import "api/analyser/variables/raw_details.proto";
import "api/analyser/variables/userdeclarations/companydetails.proto";
import "api/analyser/variables/userdeclarations/monthlyincome.proto";
import "api/analyser/variables/userdeclarations/user_declared_data.proto";
import "api/insights/networth/service.proto";

option go_package = "github.com/epifi/gamma/api/analyser/variables";
option java_package = "com.github.epifi.gamma.api.analyser.variables";

enum AnalysisVariableName {
  ANALYSIS_VARIABLE_NAME_UNSPECIFIED = 0;
  ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES = 1;
  ANALYSIS_VARIABLE_NAME_MF_MARKET_CAP_DETAILS = 2;
  ANALYSIS_VARIABLE_NAME_MF_MONTHLY_INVESTMENT_STATISTICS = 3;
  ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE = 4;
  ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS = 5;
  ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS = 6;
  ANALYSIS_VARIABLE_NAME_MF_SECTOR_DETAILS = 7;
  ANALYSIS_VARIABLE_NAME_MF_STOCKS_DETAILS = 8;
  ANALYSIS_VARIABLE_NAME_USER_DECLARED_MONTHLY_INCOME = 9;
  ANALYSIS_VARIABLE_NAME_USER_DECLARED_COMPANY_DETAILS = 10;
  ANALYSIS_VARIABLE_NAME_USER_DOB = 11;
  ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY = 12;
  ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO = 13;
  ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS = 14;
  ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION = 15;
  ANALYSIS_VARIABLE_NAME_NPS_ASSETS_DISTRIBUTION = 16;
  ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION = 17;
  ANALYSIS_VARIABLE_NAME_GOLD_ASSETS_DISTRIBUTION = 18;
  // Analysis variable for Indian stocks weekly distribution
  ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION = 19;
}

// AnalysisVariable contains any of the available analysis variables
message AnalysisVariable {
  AnalysisVariableName analysis_variable_name = 1;
  oneof variable {
    // ANALYSIS_VARIABLE_NAME_MF_INVESTMENT_ACTIVITIES
    api.analyser.variables.mutualfund.MfInvestmentActivities mf_investment_activities = 2;
    // ANALYSIS_VARIABLE_NAME_MF_MARKET_CAP_DETAILS
    api.analyser.variables.mutualfund.MfMarketCapDetails mf_market_cap_details = 3;
    // ANALYSIS_VARIABLE_NAME_MF_MONTHLY_INVESTMENT_STATISTICS
    api.analyser.variables.mutualfund.MfMonthlyInvestmentStatistics mf_monthly_investment_statistics = 4;
    // ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE, ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO
    // we will be using the same variable object for both the enums, diff is
    // ANALYSIS_VARIABLE_NAME_PORTFOLIO_PERFORMANCE -> percentage change for overall portfolio till date,
    // ANALYSIS_VARIABLE_NAME_MF_DAILY_MARKET_COMPARISON_WITH_PORTFOLIO -> daily percentage change for portfolio compared to market daily
    api.analyser.variables.mutualfund.MfPortfolioPerformanceDetails mf_portfolio_performance_details = 5;
    // ANALYSIS_VARIABLE_NAME_MF_ASSET_CATEGORY_DETAILS
    api.analyser.variables.mutualfund.MfAssetCategoryDetails mf_asset_category_details = 6;
    // ANALYSIS_VARIABLE_NAME_MF_SCHEME_ANALYTICS
    api.analyser.variables.mutualfund.MfSchemeAnalytics mf_secrets_scheme_analytics = 7;
    // ANALYSIS_VARIABLE_NAME_MF_SECTOR_DETAILS
    api.analyser.variables.mutualfund.MfSectorDetails mf_sector_details = 8;
    // ANALYSIS_VARIABLE_NAME_MF_STOCKS_DETAILS
    api.analyser.variables.mutualfund.MfStocksDetails mf_stocks_details = 9;
    // ANALYSIS_VARIABLE_NAME_USER_DECLARED_MONTHLY_SALARY
    api.analyser.variables.userdeclarations.MonthlyIncome user_declaration_monthly_income = 10;
    // ANALYSIS_VARIABLE_NAME_USER_DECLARED_COMPANY_DETAILS
    api.analyser.variables.userdeclarations.CompanyDetails user_declaration_company_details = 13;
    // ANALYSIS_VARIABLE_NAME_USER_DOB
    api.analyser.variables.userdeclarations.UserDob user_dob = 14;
    // ANALYSIS_VARIABLE_NAME_PORTFOLIO_SUMMARY
    api.analyser.variables.networth.PortfolioSummary portfolio_summary = 15;
    // ANALYSIS_VARIABLE_NAME_NETWORTH_DETAILS
    api.analyser.variables.networth.NetworthDetails networth_details = 16;
    // ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_ASSETS_DISTRIBUTION
    AssetTypeDayChangeResponseWrapper indian_stocks_distribution = 17;
    // ANALYSIS_VARIABLE_NAME_NPS_ASSETS_DISTRIBUTION
    AssetTypeDayChangeResponseWrapper nps_distribution = 18;
    // ANALYSIS_VARIABLE_NAME_MF_WEEKLY_DISTRIBUTION
    AssetTypeDayChangeResponseWrapper mf_weekly_distribution = 19;
    // ANALYSIS_VARIABLE_NAME_GOLD_ASSETS_DISTRIBUTION
    AssetTypeDayChangeResponseWrapper gold_distribution = 20;
    // ANALYSIS_VARIABLE_NAME_INDIAN_STOCKS_WEEKLY_DISTRIBUTION
    AssetTypeDayChangeResponseWrapper indian_stocks_weekly_distribution = 21;
  }
  // raw details which doesnt exist with epifi system so telling the client to handling this accordingly
  repeated RawDetailsName unavailable_raw_details_name = 11;
  AnalysisVariableState analysis_variable_state = 12;
}

enum AnalysisVariableState {
  ANALYSIS_VARIABLE_STATE_UNSPECIFIED = 0;
  ANALYSIS_VARIABLE_STATE_AVAILABLE = 1;
  ANALYSIS_VARIABLE_STATE_DATA_MISSING = 2;
}

message AssetTypeDayChangeResponseWrapper {
  .insights.networth.AssetTypeDayChangeResponse day_change_response = 1;
  // security metadata map for securities/assets in the day changes response
  // key is security_id(internal to us)
  map<string, SecurityMetadata> security_metadata_map = 2;
}
