syntax = "proto3";

package api.analyser.variables.userdeclarations;

import "api/insights/epf/model/employer_pf_history_details.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/analyser/variables/userdeclarations";
option java_package = "com.github.epifi.gamma.api.analyser.variables.userdeclarations";

message CompanyDetails {
  // this will help during edit of the user declared company details
  string company_details_form_external_id = 1;
  // this will be populated only when we have any company details user declaration present
  string company_name = 2;
  // past epf history of company if available
  insights.epf.model.MonthlyPfHistory employer_pf_history = 3;
  // average salary of the company after calculations from epf history of the company
  AverageSalaryRange avg_salary_range = 4;
  // indicates whether the allowed limit for company changes has been exceeded
  bool is_company_change_limit_exceeded = 5;
}

message AverageSalaryRange {
  // lower bound of the average salary range
  google.type.Money low_salary = 1;
  // upper bound of the average salary range
  google.type.Money high_salary = 2;
}
