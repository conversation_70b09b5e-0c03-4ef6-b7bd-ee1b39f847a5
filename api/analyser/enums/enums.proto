syntax = "proto3";

package analyser.enums;

option go_package = "github.com/epifi/gamma/api/analyser/enums";
option java_package = "com.github.epifi.gamma.api.analyser.enums";

//go:generate gen_sql -types=TimeDurationType
enum AggregateType {
  AGGREGATE_TYPE_UNSPECIFIED = 0;
  AGGREGATE_TYPE_SUM = 1;
  AGGREGATE_TYPE_COUNT = 2;
  AGGREGATE_TYPE_ABSOLUTE_MAX = 3;
  AGGREGATE_TYPE_MIN = 4;
  AGGREGATE_TYPE_MAX = 5;
}

enum TimeDurationType {
  TIME_DURATION_TYPE_UNSPECIFIED = 0;
  TIME_DURATION_TYPE_DAY = 1;
  TIME_DURATION_TYPE_WEEK = 2;
  TIME_DURATION_TYPE_MONTH = 3;
  TIME_DURATION_TYPE_QUARTER = 4;
  TIME_DURATION_TYPE_YEAR = 5;
}
