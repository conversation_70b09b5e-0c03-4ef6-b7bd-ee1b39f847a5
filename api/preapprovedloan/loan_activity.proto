syntax = "proto3";

package preapprovedloan;

import "api/preapprovedloan/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

//go:generate gen_sql -types=LoanActivity,LoanActivityDetails
message LoanActivity {
  string id = 1;
  string loan_account_id = 2;
  LoanActivityType type = 3;
  LoanActivityDetails details = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp deleted_at = 7;
  string reference_id = 8;
}

message LoanActivityDetails {
  string transaction_id = 1;
  google.type.Money amount = 2;
  string txn_particulars = 4;
  google.protobuf.Timestamp txn_time = 5;
  string utr = 6;

  oneof activity_type_details {
    InstallmentPayoutDetails installment_payout_details = 3 [deprecated = true];
    EmiActivityDetails emi_activity_details = 7;
  }

  message InstallmentPayoutDetails {
    string id = 1;
  }
  message EmiActivityDetails {
    // denotes the due date of the emi for which the activity is created.
    google.type.Date emi_due_date = 1;
  }
}
