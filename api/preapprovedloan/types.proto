syntax = "proto3";

package preapprovedloan;

import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

message CsvRow {
  repeated CsvCell row = 1;
}

message CsvCell {
  string cell = 1;
}

// this will used to represent possible emi and tenure options for a loan, user can choose from it accordingly
message LoanPlan {
  google.type.Money emi_amount = 1;
  int32 tenure_in_months = 2;
}
