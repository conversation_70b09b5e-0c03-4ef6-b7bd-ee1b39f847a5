//go:generate gen_sql -types=LoanOffer,OfferConstraints,OfferProcessingInfo,OfferDisplayInfo
syntax = "proto3";

package preapprovedloan;

import "api/preapprovedloan/enums.proto";
import "api/preapprovedloan/enums/lms.proto";
import "api/preapprovedloan/enums/program.proto";
import "api/preapprovedloan/mutual_fund.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

message LoanOffer {
  string id = 1;
  string actor_id = 2;
  string vendor_offer_id = 3;
  Vendor vendor = 4;
  OfferConstraints offer_constraints = 5;
  OfferProcessingInfo processing_info = 6;
  google.protobuf.Timestamp valid_since = 7;
  google.protobuf.Timestamp valid_till = 8;
  google.protobuf.Timestamp deactivated_at = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  google.protobuf.Timestamp deleted_at = 12;
  // reference id to loan offer eligibility criteria table
  string loan_offer_eligibility_criteria_id = 13;
  LoanProgram loan_program = 14;
  // denotes the Fi lms partner who is managing the loan product linked to this offer.
  enums.LmsPartner lms_partner = 15;
  // denotes the product in partner lms system with which the loan offer is associated.
  string lms_partner_product_id = 16;
  // most recent time at which the offer was viewed by the user either on S1 or S2 screen only
  // not mandatory, can be present only for specific programs and vendors based on business logic
  google.protobuf.Timestamp last_viewed_at = 17;
  // true if the discount is applied at the time of fetching the offer from DB
  // NOTE: this is a dynamic field computed based on current time, this is not part of the DB model
  bool is_discount_applied = 18;
  OfferDisplayInfo offer_display_info = 19;
  // Categorising loan_offers in soft, hard and pre qual. By default, it'll be soft.
  LoanOfferType loan_offer_type = 20;
}

message RangeData {
  int64 start = 1;
  int64 end = 2;
  oneof Value {
    double percentage = 3;
    google.type.Money absolute_value = 4;
  }
  // min value denotes minimum value for given start and end range
  // if percentage is populated in value then not necessarily min percentage would be populated in min value
  // min value only defines floor value which can be considered for that range
  // interpretation of min value is purely by use case basis
  oneof min_value {
    // prefixing percentage and absolute value with min
    // since protobuf does not allow same var name in two different one of fields
    double min_percentage = 5;
    google.type.Money min_absolute_value = 6;
  }
  oneof max_value {
    double max_percentage = 7;
    google.type.Money max_absolute_value = 8;
  }
}

message OfferConstraints {
  google.type.Money max_loan_amount = 1;
  google.type.Money max_emi_amount = 2;
  int32 max_tenure_months = 3;
  google.type.Money min_loan_amount = 4;
  int32 min_tenure_months = 5;
  // this field will contain additional constraint details
  oneof additional_constraints {
    // constraints specified by FIFTYFIN vendor for LAMF loan program
    FiftyFinLamfConstraintInfo fiftyfin_lamf_constraint_info = 6;

    // Constraints for loan offers from Lenden Club (a P2P lending platform)
    LendenConstraintInfo lenden_constraint_info = 8;
  }
  google.type.Money min_loan_amount_step_size = 7;
  repeated OfferConstraintsSlab offer_constraints_slabs = 9;
}

message OfferConstraintsSlab {
  google.type.Money loan_amount = 1;
  google.type.Money loan_emi = 2;
  google.type.Money processing_fee = 3;
  int32 tenure_in_months = 4;
  float rate_of_interest = 5;
}

message OfferProcessingInfo {
  double gst = 1;
  repeated RangeData interest_rate = 2;
  repeated RangeData processing_fee = 3;
  string application_id = 4;
  enums.LoanProgramVersion loan_program_version = 5;
  repeated OfferDiscount offer_discounts = 6;
  float rate_of_interest = 7;
  repeated float modify_roi_list = 8;
  InterestType interest_type = 9;
  Duration tenure_type = 10;
  Duration interest_rate_frequency = 11;
  repeated ApplicableTenure applicable_tenures = 12;

  // eg 5000-6000:[1,2,3,5],60000-80000:[4,5,9]
  message ApplicableTenure {
    int32 min_value = 1;
    int32 max_value = 2;
    repeated int32 tenures = 3;
  }

  enum DiscountedParameter {
    DISCOUNTED_PARAMETER_UNSPECIFIED = 0;
    DISCOUNTED_PARAMETER_INTEREST_RATE = 1;
  }

  message OfferDiscount {
    DiscountedParameter discounted_parameter = 1;
    // discount value is the absolute number to be reduced from the discounted parameter
    // e.g. if discounted parameter is interest rate (which is given as percentage) lets say 18,
    // and discount value is 1, interest rate will be reduced by 1 and will become 17
    double discount_value = 2;
    google.protobuf.Timestamp discount_valid_since = 3;
    google.protobuf.Timestamp discount_valid_till = 4;
  }
}

enum Duration {
  DURATION_UNSPECIFIED = 0;
  DURATION_MONTHLY = 1;
}

enum InterestType {
  INTEREST_TYPE_UNSPECIFIED = 0;
  INTEREST_TYPE_FLAT = 1;
}

message FiftyFinLamfConstraintInfo {
  MfPortfolioConstraint mf_portfolio_constraint = 1;
  FiftyFinLamfOfferSource source = 2;
}

message LendenConstraintInfo {
  // A list of rate of interest values that the user is allowed to select from
  // when modifying the ROI before disbursal of loan.
  repeated double allowed_roi_values = 1;

  // A list of loan slabs that the user is allowed to choose the loan amount and tenure from.
  repeated LendenLoanSlab loan_slabs = 2;

  // AA bank data required for loan mandate creation
  AaAnalysisBankDetails bank_details = 3;
}

message AaAnalysisBankDetails {
  string account_holder_name = 1;
  string account_number = 2;
  string ifsc = 3;
  string type = 4;
  string bank_name = 5;
}

// A loan slab defines the valid tenures for which a lender can disburse a loan within a specific loan amount range.
// For example, a lender may offer loans between ₹21,000-₹25,000 with tenures ranging from 1 to 6 months,
// whereas for amounts between ₹25,001-₹30,000, they may allow tenures between 3 to 9 months.
// This distinction arises because lenders assess a borrower's income and expenses to determine the maximum EMI they can afford.
// For higher loan amounts, shorter tenures may be restricted to minimize the risk of EMI defaults.
// Amount ranges in two loan slabs are non-overlapping, i.e., if one loan slab has min-max amount ₹21,000-₹25,000,
// then another slab should have min amount greater than ₹25,000 or max amount less than ₹21,000.
message LendenLoanSlab {
  google.type.Money min_amount = 1;
  google.type.Money max_amount = 2;

  // A list of tenure ranges that the user is allowed to choose the tenure from for the given loan slab.
  LendenLoanSlabTenureRange tenure_range = 3;
}

// A tenure range defines the valid tenures for a loan slab.
// The applicable tenure values are expected to be contiguous for a slab. Hence just the min and max tenures are sufficient
// to represent the valid tenures for a slab.
// E.g. for slab with min-max amount ₹21,000-₹25,000, the tenures can be [1, 2, 3, 4, 5, 6] but not [1, 3, 5, 6].
message LendenLoanSlabTenureRange {
  int32 min_tenure_in_months = 3;
  int32 max_tenure_in_months = 4;
}

message OfferDisplayInfo {
  Probability funding_probability = 1;
  string expected_time_to_get_funding = 2;
  bool is_recommended = 3;
}

enum Probability {
  PROBABILITY_UNSPECIFIED = 0;
  PROBABILITY_LOW = 1;
  PROBABILITY_HIGH = 2;
  PROBABILITY_MEDIUM = 3;
}
