syntax = "proto3";

package preapprovedloan.enums;

option go_package = "github.com/epifi/gamma/api/preapprovedloan/enums";
option java_package = "com.github.epifi.gamma.api.preapprovedloan.enums";


// represents different sources of lms details like lenders (liquiloans, federal...) or partner lms or db
enum LmsDataSource {
  LMS_DATA_SOURCE_UNSPECIFIED = 0;
  LMS_DATA_SOURCE_PARTNER_LMS_FINFLUX = 1;
  LMS_DATA_SOURCE_LENDER_LIQUILOANS = 2;
  // e.g.: loans_idfc db for idfc lender
  LMS_DATA_SOURCE_DB = 3;
}

enum LmsDataDifferenceStatus {
  LMS_DATA_DIFFERENCE_STATUS_UNSPECIFIED = 0;
  // differences found between the provided sources
  LMS_DATA_DIFFERENCE_STATUS_FOUND = 1;
  // no differences found between the provided sources
  LMS_DATA_DIFFERENCE_STATUS_NOT_FOUND = 2;
}
