syntax = "proto3";

package lendability;

option go_package = "github.com/epifi/gamma/api/preapprovedloan/lendability";
option java_package = "com.github.epifi.gamma.api.preapprovedloan.lendability";

enum Flow {
  FLOW_UNSPECIFIED = 0;
  FLOW_SAVINGS_ACCOUNT = 1;
  FLOW_LOAN_ELIGIBILITY = 2;
  FLOW_WEALTH_BUILDER = 3;
}

enum LoanAffinityCategory {
  LOAN_AFFINITY_CATEGORY_UNSPECIFIED = 0;
  LOAN_AFFINITY_CATEGORY_LOW = 1;
  LOAN_AFFINITY_CATEGORY_MEDIUM = 2;
  LOAN_AFFINITY_CATEGORY_HIGH = 3;
}

enum PDCategory {
  PD_CATEGORY_UNSPECIFIED = 0;
  PD_CATEGORY_LOW = 1;
  PD_CATEGORY_MEDIUM = 2;
  PD_CATEGORY_HIGH = 3;
}
