// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package preapprovedloan.developer;

option go_package = "github.com/epifi/gamma/api/preapprovedloan/developer";
option java_package = "com.github.epifi.gamma.api.preapprovedloan.developer";

enum PreApprovedLoanEntity {
  PRE_APPROVED_LOAN_UNSPECIFIED = 0;

  LOAN_REQUESTS = 1;

  LOAN_ACCOUNTS = 2;

  LOAN_OFFERS = 3;

  LOAN_STEP_EXECUTIONS = 4;

  LOAN_OFFER_ELIGIBILITY_CRITERIA = 5;

  LOAN_ACTIVITY = 6;

  LOAN_INSTALLMENT_INFO = 7;

  LOAN_INSTALLMENT_PAYOUT = 8;

  LOAN_PAYMENT_REQUEST = 9;

  LOAN_APPLICANTS = 10;

  PARTNER_LMS_USER=11;

  FETCHED_ASSET=12;

  MANDATE_REQUESTS = 13;

  PRE_ELIGIBILITY_OFFER = 14;

  LOANS_MASTER = 15;
}

enum DeveloperActionsABFL {
  DEVELOPER_ACTIONS_ABFL_UNSPECIFIED = 0;
  DEVELOPER_ACTIONS_ABFL_DIGI_SIGN_APPROVE = 1;
  DEVELOPER_ACTIONS_ABFL_EMANDATE_APPROVE = 2;
  DEVELOPER_ACTIONS_ABFL_DIGILOCKER_APPROVE = 3;
}
