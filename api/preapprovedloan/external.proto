//go:generate gen_sql -types=LoanDetail, LoanApplicationInfo, AmountInfo, InstallmentDetail, VendorDetail, Offer, Application, Transaction
syntax = "proto3";

package preapprovedloan;

import "api/preapprovedloan/enums.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/address.proto";
import "api/typesv2/bank_account_details.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/preapprovedloan";
option java_package = "com.github.epifi.gamma.api.preapprovedloan";

// LoanDetail represents a common external structure Loan Account info
message LoanDetail {
  // Loan Account Id
  string id = 1;
  LoanAccountStatus status = 2;
  string name = 3;
  VendorDetail vendor_details = 4;
  AmountInfo amount_info = 5;
  google.protobuf.Timestamp creation_time = 6;
  // Loan End/Maturity Date
  google.protobuf.Timestamp closure_time = 7;
  InstallmentDetail installment_detail = 8;
  api.typesv2.BankAccountDetails bank_account_details = 9;
}

message LoanApplicationInfo {
  google.type.Money amount = 1;
  int32 tenure_in_months = 2;
  google.type.Money disbursal_amount = 3;
  double interest_rate = 4;
  google.type.Money emi_amount = 5;
  Deductions deductions = 6;
  google.type.Money total_payable = 7;

  message Deductions {
    google.type.Money total_deductions = 1;
    google.type.Money gst = 2;
    google.type.Money processing_fee = 3;
    google.type.Money advance_interest = 4;
  }
}

message AmountInfo {
  google.type.Money loan_amount = 1;
  google.type.Money disbursed_amount = 2;
  google.type.Money outstanding_amount = 3;
  google.type.Money total_payable_amount = 4;
}

message InstallmentDetail {
  google.type.Money next_emi_amount = 1;
  google.protobuf.Timestamp prev_paid_installment_time = 2;
  google.protobuf.Timestamp next_due_installment_time = 3;
}

message VendorDetail {
  Vendor name = 1;
  string icon = 2;
}

message Offer {
  Constraints constraints = 1;
  ProcessingInfo processing_info = 2;
  VendorDetail vendor_detail = 3;
  // This represents offer validity time
  google.protobuf.Timestamp valid_till = 4;
  // true if loan is active for the user at present
  // i.e. user hasn't applied for this offer yet and offer is not expired/deactivated
  bool is_active = 5;
  message Constraints {
    int32 min_tenure_months = 1;
    int32 max_tenure_months = 2;
    google.type.Money min_loan_amount = 3;
    google.type.Money max_loan_amount = 4;
    google.type.Money max_emi_amount = 5;
  }
  message ProcessingInfo {
    double gst_percentage = 1;
    double interest_rate_percentage = 2;
    double processing_fee_percentage = 3;
  }
}

message Application {
  VendorDetail vendor_detail = 1;
  LoanApplicationInfo loan_info = 2;
  google.protobuf.Timestamp completed_at = 3;
  google.protobuf.Timestamp created_at = 4;
  // high level application status
  LoanApplicationStatus status = 5;
  // ongoing application current stage at which the user is present
  LoanApplicationSubStatus sub_status = 6;
}

message Transaction {
  string loan_name = 1;
  string loan_account_id = 2;
  google.protobuf.Timestamp txn_time = 3;
  // amount credited in savings if type disbursed, else debit
  LoanActivityType txn_type = 4;
  LoanPaymentRequestStatus txn_status = 5;
  google.type.Money amount = 7;
  VendorDetail vendor_details = 8;
  // transaction identity, utr or transaction id which is shown to the user
  string utr = 9;
  // loan activity table identifier
  string loan_activity_id = 10;
}

message LoanUserKycDetails {
  string pan = 1;
  google.type.Date dob = 2;
  api.typesv2.common.Name pan_name = 3;
  api.typesv2.common.Name fathers_name = 4;
  api.typesv2.common.Name mothers_name = 5;
  api.typesv2.PostalAddress permanent_address = 6;
}
