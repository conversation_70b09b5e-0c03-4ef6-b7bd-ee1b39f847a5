syntax = "proto3";

package preapprovedloan.comms;

option go_package = "github.com/epifi/gamma/api/preapprovedloan/comms";
option java_package = "com.github.epifi.gamma.api.preapprovedloan.comms";

enum PreApprovedLoanNotificationType {
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_UNSPECIFIED = 0;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_SUCCESSFUL_VKYC = 1;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_SUCCESSFUL_MANUAL_REVIEW = 2;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_FAILED_VKYC = 3;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_FAILED_MANUAL_REVIEW = 4;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LOAN_ACCOUNT_CREATION = 5;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LOAN_ACCOUNT_CLOSURE = 6;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_PRE_PAY = 7;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_VKYC_PENDING = 8;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_ESIGN_PENDING = 9;
  PRE_APPROVED_LOAN_NOTIFICATION_TYPE_LIVENESS_PENDING = 10;
  PAL_NOTIFICATION_TYPE_T_MINUS_EMI_PAYMENT = 11;
  PAL_NOTIFICATION_TYPE_T_MINUS_LOW_SAVINGS_BAL_EMI_PAYMENT = 12;
  PAL_NOTIFICATION_TYPE_T_ZERO_EMI_PAYMENT = 13;
  PAL_NOTIFICATION_TYPE_T_PLUS_EMI_PAYMENT = 14;
  PAL_NOTIFICATION_TYPE_T_PLUS_LOW_SAVINGS_BAL_EMI_PAYMENT = 15;

}
