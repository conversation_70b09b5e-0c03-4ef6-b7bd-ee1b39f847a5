syntax = "proto3";

package omegle.matcher;

import "api/omegle/matcher/matcher.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/image.proto";

option go_package = "github.com/epifi/gamma/api/omegle/matcher";
option java_package = "com.github.epifi.gamma.api.omegle.matcher";

service Matcher {
  rpc MatchPAN (MatchPANRequest) returns (MatchPANResponse);
  rpc MatchPassport (MatchPassportRequest) returns (MatchPassportResponse);
  rpc MatchEmiratesId (MatchEmiratesIdRequest) returns (MatchEmiratesIdResponse);
  rpc MatchQatarId (MatchQatarIdRequest) returns (MatchQatarIdResponse);
  // MatchLocation rpc can be used to match two locations given in request, and returns the distance between them.
  rpc MatchLocation (MatchLocationRequest) returns (MatchLocationResponse);
  rpc MatchFace (MatchFaceRequest) returns (MatchFaceResponse);
}

message MatchPANRequest {
  PANMatchData got_data = 1;
  PANMatchData expected_data = 2;
  string call_id = 3;
}

message MatchPANResponse {
  message MatchResults {
    MatchResult pan_number = 1;
    MatchResult name = 2;
    MatchResult date_of_birth = 3;
    MatchResult face = 4;
    MatchResult parent_name = 5;
  }

  rpc.Status status = 1;
  MatchResults match_results = 2;
}

message MatchPassportRequest {
  PassportMatchData got_data = 1;
  PassportMatchData expected_data = 2;
  string call_id = 3;
}

message MatchPassportResponse {
  message MatchResults {
    MatchResult passport_number = 1;
    MatchResult name = 2;
    MatchResult date_of_birth = 5;
    MatchResult face = 4;
    MatchResult nationality = 3;
    MatchResult is_indian_passport = 6 [deprecated = true];
    MatchResult date_of_expiry = 7;
    MatchResult address = 8;
  }
  rpc.Status status = 1;
  MatchResults match_results = 2;
}

message MatchEmiratesIdRequest {
  EmiratesIdMatchData got_data = 1;
  EmiratesIdMatchData expected_data = 2;
  string call_id = 3;
}

message MatchEmiratesIdResponse {
  message MatchResults {
    MatchResult id = 1;
    MatchResult name = 2;
    MatchResult date_of_birth = 5;
    MatchResult face = 4;
    MatchResult nationality = 6;
    MatchResult date_of_expiry = 7;
  }
  rpc.Status status = 1;
  MatchResults match_results = 2;
}

message MatchLocationRequest {
  LocationMatchData got_data = 1;
  LocationMatchData expected_data = 2;
  string call_id = 3;
}

message MatchLocationResponse {
  message MatchResults {
    MatchResult city = 1;
    double distance_in_km = 2;
  }
  rpc.Status status = 1;
  api.typesv2.common.BooleanEnum overall_match = 2;
  MatchResults match_results = 3;
}

message MatchFaceRequest {
  api.typesv2.common.Image got_face = 1;
  api.typesv2.common.Image expected_face = 2;
  string call_id = 3;
}

message MatchFaceResponse {
  rpc.Status status = 1;
  MatchResult face_match_result = 3;
}

message MatchQatarIdRequest {
  QatarIdMatchData got_data = 1;
  QatarIdMatchData expected_data = 2;
  string call_id = 3;
}

message MatchQatarIdResponse {
  message MatchResults {
    MatchResult id = 1;
    MatchResult name = 2;
    MatchResult date_of_birth = 5;
    MatchResult face = 4;
    MatchResult nationality = 6;
    MatchResult date_of_expiry = 7;
  }
  rpc.Status status = 1;
  MatchResults match_results = 2;
}