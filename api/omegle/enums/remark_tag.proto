//go:generate gen_sql -types=RemarkTag
syntax = "proto3";

package omegle.enums;

option go_package = "github.com/epifi/gamma/api/omegle/enums";
option java_package = "com.github.epifi.gamma.api.omegle.enums";

enum RemarkTag {
  REMARK_TAG_UNSPECIFIED = 0;
  // Remarks from client for which no tag is found is tagged as OTHER
  REMARK_TAG_OTHER = 1;


  // Question remarks
  REMARK_TAG_INCOME_QUESTION = 2;
  REMARK_TAG_OCCUPATION_QUESTION = 3;
  REMARK_TAG_LIVENESS_QUESTION = 4;

  // Location remarks
  REMARK_TAG_LOCATION_QUESTION = 5;

  // Face capture remarks
  REMARK_TAG_FACE_CAPTURE = 6;

  // Pan stage remarks
  REMARK_TAG_PAN_CAPTURE = 7;
  REMARK_TAG_PAN_OCR_EXTRACTION = 8;
  REMARK_TAG_PAN_FACE_MATCH = 9;
  REMARK_TAG_PAN_OCR_VALIDATION = 10;

  // <PERSON><PERSON><PERSON><PERSON> remarks
  REMARK_TAG_CKYC_DOCUMENT_DETAILS_CONFIRMATION = 11;
  REMARK_TAG_CKYC_FACE_MATCH = 12;
}
