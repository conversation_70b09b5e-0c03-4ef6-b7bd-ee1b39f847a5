//go:generate gen_queue_pb
syntax = "proto3";

package omegle;

import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/omegle/consumer";
option java_package = "com.github.epifi.gamma.api.omegle.consumer";

service Consumer {
  // consumer to process agent and auditor response and sends data to vendor
  rpc ProcessAgentAndAuditorResponse(ProcessAgentAndAuditorResponseRequest) returns (ProcessAgentAndAuditorResponseResponse);
}

message ProcessAgentAndAuditorResponseRequest {
  queue.ConsumerRequestHeader request_header = 1;
  enum ResponseType {
    RESPONSE_TYPE_UNSPECIFIED = 0;
    RESPONSE_TYPE_AGENT_RESPONSE = 1;
    RESPONSE_TYPE_AUDITOR_RESPONSE = 2;
  }
  ResponseType response_type = 2;
  string call_id = 3;
}

message ProcessAgentAndAuditorResponseResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
