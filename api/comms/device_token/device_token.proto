syntax = "proto3";

package comms.device_token;

import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/comms/device_token";
option java_package = "com.github.epifi.gamma.api.comms/device_token";

message RegisterFCMDeviceTokenRequest {
  // actor id from where the request is initiated
  string actor_id = 1;
  // device token against this actor id
  string device_token = 2;
}

message RegisterFCMDeviceTokenResponse {
  // Represents message status codes
  rpc.Status status = 1;
}

message DeregisterFCMDeviceTokenRequest {
  // actor id from where the request is initiated
  string actor_id = 1;
}

message DeregisterFCMDeviceTokenResponse {
  // Represents message status codes
  rpc.Status status = 1;
}

message GetFCMDeviceTokenRequest {
  // actor id for which the device token is to be fetched
  string actor_id = 1;
}

message GetFCMDeviceTokenResponse {
  // Represents message status codes
  rpc.Status status = 1;

  // device token of the actor id get from db
  string device_token = 2;
}

service FCMDeviceToken {
  // service to register a device token against an actor id
  rpc RegisterFCMDeviceToken(RegisterFCMDeviceTokenRequest) returns (RegisterFCMDeviceTokenResponse) {}
  // service to fetch device token from the database against the actor id
  rpc GetFCMDeviceToken(GetFCMDeviceTokenRequest) returns (GetFCMDeviceTokenResponse) {}
  // service to de register FCM device token stored for an actor id
  rpc DeregisterFCMDeviceToken(DeregisterFCMDeviceTokenRequest) returns (DeregisterFCMDeviceTokenResponse) {}
}
