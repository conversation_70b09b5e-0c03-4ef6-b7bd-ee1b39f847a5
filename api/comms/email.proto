// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package comms;

import "api/comms/email_template.proto";
import "api/comms/enums.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/comms";
option java_package = "com.github.epifi.gamma.api.comms";

// Specifies the proto for the Email message request
message EmailMessage {
  // Email ID from the the email has to be sent
  // Mandatory
  string from_email_id = 1;

  // Name of the from email example : epifi
  string from_email_name = 2;

  // Email ID of the customer on which the message is to be sent
  // This will be taken from request itself so no need to pass explicitly here
  // If request has user id, this is determined from user's profile by calling user service
  string to_email_id = 3 [deprecated = true];

  // Name of to email actor
  // use ToAddresses inside the destination instead of this
  string to_email_name = 4 [deprecated = true];

  // Subject of the email that is to be sent to the customer
  // Avoid using this and get your template added in email option
  string subject = 5 [deprecated = true];

  // Add multiple email parts as array objects here
  // Each email part can have different content type and content
  // Avoid using this and get your template added in email option
  repeated Body body = 6 [deprecated = true];

  message Attachment {
    // content of the attachment in bytes
    bytes file_content = 1;

    // name of the file/image
    string file_name = 2;

    // whether to attach or embed in email
    Disposition disposition = 3;

    // type of attachment whether application/pdf or image/jpeg
    // https://cloud.google.com/appengine/docs/standard/php/mail/mail-with-headers-attachments
    string attachment_type = 4;
  }

  // supports multiple attachment. Due to grpc limitations, maximum email size allowed is 4MB
  // Total size of attachments sent should be passed keeping in mind the limit
  repeated Attachment attachment = 7;

  // Email option is to be used when the body of the email is present at comms end
  // Comms will identify the type of template passed and replace variables in body with those passed in request
  EmailOption email_option = 8;

  // If we want the replies to the emails we send to be routed to a specific email id of our choice we can set this
  // This is not a mandatory parameter. If this is not passed replies will go to email passed as from email id
  // the name parameter is the how the name appears in reply to email header in email clients
  string reply_to_name = 9;
  // If we want the replies to the emails we send to be routed to a specific email id of our choice we can set this
  // This is not a mandatory parameter. If this is not passed replies will go to email passed as from email id
  // the email id parameter is the actual email which received the emails for incoming replies
  string reply_to_email_id = 10;

  // Information required to add unsubscribe option in an email
  // Ref: https://docs.google.com/document/d/16iuVnvjzPF81mzpi5_yWfSyu-QSQ_g1MsF9HTCzpKm4/edit#
  message UnsubscribeOptionInfo {
    // whether or not the email should have an unsubscribe option
    bool allow_user_to_unsubscribe = 1;
    // the topic to which this email belongs to (for subscription management)
    string topic_name = 2;
  }

  UnsubscribeOptionInfo unsubscribe_option_info = 11;

  // To define the destination of the email including all To addresses, cc and bcc addresses
  message Destination {
    message EmailAddress {
      string email_id = 1;
      string name = 2;
    }
    // The email Id from the Send Message request will be considered as To address. No need to add it explicitly here.
    // [If request has user id, email Id determined from user's profile by calling user service will be considered as To address]
    repeated EmailAddress to_addresses = 1;
    repeated EmailAddress cc_addresses = 2;
    repeated EmailAddress bcc_addresses = 3;
  }
  Destination destination = 12;
}

// Represents the body of the message.
message Body {
  // Content that is to be sent to the customer
  string content = 1;

  // content type of the message
  ContentType content_type = 2;
}

// email callback db table
message EmailCallback {
  // Primary key of the table
  string id = 1;
  // Message id of the original email sent
  string message_id = 2;
  // Type of event
  EventType event_type = 3;
  // Vendor for which this event is stored
  vendorgateway.Vendor vendor = 4;
  // Timestamp at which event occurred
  google.protobuf.Timestamp event_timestamp = 5;
  // meta data for event
  EventMeta event_meta = 6;
  // standard timestamp fields
  google.protobuf.Timestamp CreatedAt = 7;
  google.protobuf.Timestamp UpdatedAt = 8;
}

// Broad level events which we understand, the name of events is used differently by vendors hence we have mapped those
// to our standard names and understanding
enum EventType {
  EVENT_TYPE_UNSPECIFIED = 0;

  // PROCESSED, DELIVERED, DELAY_DELIVERY
  // Message has been successfully delivered to the receiving server.
  DELIVERY = 1;

  // REJECTED, RENDERING FAILURE, DEFERRED
  // Receiving server temporarily rejected the message.
  FAILURE = 2;

  // OPENED
  // Recipient has opened the HTML message. Open Tracking needs to be enabled for this type of event.
  OPEN = 3;

  // CLICKED
  // Recipient clicked on a link within the message. Click Tracking needs to be enabled for this type of event.
  CLICK = 4;

  // COMPLAINT, SPAM_REPORT
  // Recipient marked message as spam.
  COMPLAINT = 5;

  // DROPPED, BOUNCED, BlOCKED
  // You may see the following drop reasons: Invalid SMTPAPI header, Spam Content (if Spam Checker app is enabled),
  // Unsubscribed Address, Bounced Address, Spam Reporting Address, Invalid, Recipient List over Package Quota
  // Receiving server could not or would not accept mail to this recipient permanently.
  // If a recipient has previously unsubscribed from your emails, the message is dropped.
  BOUNCE = 6;

  // PROCESSED, SEND
  // Message has been received and is ready to be delivered.
  SEND = 7;
}

// Meta data associated with email event. We are storing only a few today. a lot of other fields also come
// can be added need basis from here :
// https://docs.aws.amazon.com/ses/latest/DeveloperGuide/event-publishing-retrieving-sns-examples.html#event-publishing-retrieving-sns-bounce
// https://sendgrid.com/docs/for-developers/tracking-events/event/#json-objects
message EventMeta {
  // list of email recipients
  repeated string destination = 1;
  // reason for occurrence of event
  string reason = 2;
  // error codes or error messages generated for event
  repeated string error_messages = 3;
  // the user agent responsible for the event.
  string user_agent = 4;
  // number of times vendor attempted to send the email
  string attempt = 5;
}
