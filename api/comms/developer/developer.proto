// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package comms.developer;

import "api/comms/message.proto";
import "api/comms/user_preference/user_preference.proto";
import "api/comms/inapptargetedcomms/message.proto";
import "api/comms/whatsapp.proto";

option go_package = "github.com/epifi/gamma/api/comms/developer";
option java_package = "com.github.epifi.gamma.api.comms.developer";

enum CommsEntity {
  COMMS_ENTITY_UNSPECIFIED = 0;

  // to fetch irrespective of medium, ie for sms, email and notification
  MESSAGE = 1;

  // to fetch sms templates
  SMS_TEMPLATE = 2;

  // to fetch FCM device token for an actor
  FCM_DEVICE_TOKEN = 3;

  // Fetch messages sent to user via phone/email/user_id
  USER_COMMUNICATIONS = 4;

  SMS_CALLBACKS = 5;

  // Whatsapp user replies
  WHATSAPP_USER_REPLIES = 6;

  // Whatsapp callbacks
  WHATSAPP_CALLBACKS = 7;

  // user preferences
  USER_COMMS_PREFERENCE = 8;

  // to fetch email templates
  EMAIL_TEMPLATE = 9;

  // email callbacks
  EMAIL_CALLBACKS = 10;

  // to fetch notifications sent to user
  APP_NOTIFICATIONS = 11;

  // In app targeted comms elements table
  IN_APP_TARGETED_COMMS_ELEMENT = 12;

  // In app targeted comms mapping table
  IN_APP_TARGETED_COMMS_MAPPING = 13;

  // Element to app details mapping table
  ELEMENT_TO_APP_DETAILS_MAPPING = 14;

  // Whatsapp message history table
  WHATSAPP_MESSAGE_HISTORY = 15;

  // In app targeted comms callback table
  IN_APP_TARGETED_COMMS_CALLBACK = 16;
}

message UserCommunicationResponse {
  message Response {
    DetailedMessage detailed_message = 1;
    SmsVendorData sms_vendor_data = 2;
  }
  repeated Response response_list = 3;
}

message UserCommsPreferenceResponse {
  repeated user_preference.UserCommsPreference user_comms_preference_list = 1;
}

message InAppTargetedCommsElementResponse {
  repeated inapptargetedcomms.InAppTargetedCommsElement targeted_comms_element_list = 1;
}

message InAppTargetedCommsMappingResponse {
  repeated inapptargetedcomms.InAppTargetedCommsMapping targeted_comms_mapping_list = 1;
}

message ElementToAppDetailsMappingResponse {
  repeated inapptargetedcomms.ElementToAppDetailsMapping app_details_mapping_list = 1;
}

message WhatsappMessageHistoryResponse {
  repeated comms.WhatsappMessageHistory whatsapp_message_history_list = 1;
}
