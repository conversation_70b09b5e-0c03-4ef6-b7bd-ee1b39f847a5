syntax = "proto3";
package comms.inapptargetedcomms;

import "api/rpc/status.proto";
import "api/comms/inapptargetedcomms/message.proto";
import "api/comms/inapptargetedcomms/enums.proto";
import "api/dynamic_elements/dynamic_elements.proto";
import "api/typesv2/common/device.proto";

option go_package = "github.com/epifi/gamma/api/comms/inapptargetedcomms";
option java_package = "com.github.epifi.gamma.api.comms.inapptargetedcomms";

// Service to serve targeted communications to users inside the Fi App
// The communications are through banners, bottom sheets etc. [doesn't include in app notifications]
// Can be used for giving out alerts at system level(eg: server under maintenance), marketing etc.
service InAppTargetedComms {
  // RPC invoked by the Dynamic elements service to fetch the list of targeted comms elements relevant for the given user
  // ActorId is a mandatory parameter in the Request
  // Response contains status code and list of relevant dynamic elements(banners, bottom sheets etc.)
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no elements found for the given user on this screen
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the list of targeted comms elements is fetched successfully
  rpc FetchDynamicElements (dynamic_elements.FetchDynamicElementsRequest) returns (dynamic_elements.FetchDynamicElementsResponse) {}

  // RPC used by the Dynamic Elements service to callback on user action on a targeted comms element
  // ActorId and ElementId are mandatory parameters in the Request
  // Response contains status code
  // INVALID ARGUMENT if any mandatory param is missing
  // RECORD NOT FOUND if no element exists with the given ElementId
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the callback is registered successfully
  rpc DynamicElementCallback (dynamic_elements.DynamicElementCallbackRequest) returns (dynamic_elements.DynamicElementCallbackResponse) {}

  // RPC to add a new targeted comms element in the DB
  // Request should contain details like element type, content(title, body, icon etc.)
  // Response contains a status code and targeted comms element Id
  // INVALID ARGUMENT if any mandatory param is missing
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the targeted comms element is added successfully
  rpc AddTargetedCommsElement (AddTargetedCommsElementRequest) returns (AddTargetedCommsElementResponse) {}

  // RPC to update an existing targeted comms element in the DB
  // TargetedCommsElementFieldMask determines the fields to be updated and is mandatory in the Request
  // Response contains status code
  // INVALID ARGUMENT if any mandatory param is missing
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the targeted comms element is updated successfully
  rpc UpdateTargetedCommsElement (UpdateTargetedCommsElementRequest) returns (UpdateTargetedCommsElementResponse) {}

  // RPC to batch add mapping of a targeted comms element with users or user_tags or screen
  // Request must contain one targeted comms element and list of mapping details {mapping_type(users or user_tags or screen) and mapped_values}
  // Response contains status code
  // INVALID ARGUMENT if any mandatory param is missing
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the targeted comms mapping is added successfully
  rpc AddTargetedCommsMapping (AddTargetedCommsMappingRequest) returns (AddTargetedCommsMappingResponse) {}

  // RPC to batch delete mappings of targeted comms element with users or user_tags or screen
  // Request contains row ids of the mapping entry and/or element ids and/or MappingDetails list.
  // At least one of these three lists must be non empty
  // all mappings matching rowId list OR elementId list OR MappingDetails list will be deleted
  // Response contains status code
  // INVALID ARGUMENT if all the three lists are empty
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the requested mappings are deleted successfully
  rpc DeleteTargetedCommsMapping (DeleteTargetedCommsMappingRequest) returns (DeleteTargetedCommsMappingResponse) {}

  // RPC to fetch mapping of a targeted comms element with users or user_tags or screen
  // Request must contain one mapping details {mapping_type(users or user_tags or screen) and mapped_values}
  // Response contains status code
  // INVALID ARGUMENT if any mandatory param is missing
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the targeted comms mapping is added successfully
  rpc GetTargetedCommsMappings (GetTargetedCommsMappingsRequest) returns (GetTargetedCommsMappingsResponse) {}

  // RPC to update an existing targeted comms mapping in the DB
  // InAppTargetedCommsMappingFieldMask determines the fields to be updated and is mandatory in the Request
  // Response contains status code
  // INVALID ARGUMENT if any mandatory param is missing
  // INTERNAL SERVER ERROR if any error in processing
  // OK if the targeted comms element is updated successfully
  rpc UpdateTargetedCommsMapping (UpdateTargetedCommsMappingRequest) returns (UpdateTargetedCommsMappingResponse) {}
}

message AddTargetedCommsElementRequest {
  // must contain the required details(type, content) to render the comms element
  InAppTargetedCommsElement targeted_comms_element = 1;
}

message AddTargetedCommsElementResponse {
  rpc.Status status = 1;

  // id generated on successful insertion of the comms element to DB
  string targeted_comms_element_id = 2;
}

message UpdateTargetedCommsElementRequest {
  InAppTargetedCommsElement targeted_comms_element = 1;

  // specifies the fields which are to be updated for this comms element
  repeated InAppTargetedCommsElementFieldMask targeted_comms_element_field_mask = 2;

  repeated ElementContentFieldMask element_content_field_mask = 3;

  repeated ElementMetaDataFieldMask element_meta_data_field_masks = 4;
}

message UpdateTargetedCommsElementResponse {
  rpc.Status status = 1;
}

message AddTargetedCommsMappingRequest {
  string targeted_comms_element_id = 1;

  // each item of the list must contain the type of the mapping and mapped_value
  // the mapped_value is actor_id in case of user, names in case of user_tags and screen
  repeated MappingDetails mapping_details_list = 2;

  repeated ElementToAppDetailsMapping app_details_mapping_list = 3;
}

message AddTargetedCommsMappingResponse {
  rpc.Status status = 1;
}

// Filters will be applied using AND on given set of filters on each fo the
// two mapping tables in_app_targeted_comms_mappings and element_to_app_details_mappings
message DeleteTargetedCommsMappingRequest {
  // list of rowIds from in_app_targeted_comms_mappings table.
  // mappings matching these row Ids in in_app_targeted_comms_mappings table will be deleted
  repeated string mapping_row_id_list = 1;
  // list of elementIds. mappings matching these elements
  // from in_app_targeted_comms_mappings and element_to_app_details_mappings tables will be deleted
  repeated string element_id_list = 2;
  // list of MappingDetails i.e {mapping_type + mapped_value}
  // mappings matching these mapped_values in in_app_targeted_comms_mappings will be deleted
  repeated MappingDetails mapping_details_list = 3;
  // list of rowIds from element_to_app_details_mappings table
  repeated string app_details_row_id_list = 4;
  // list of app platforms. Mappings from element_to_app_details_mappings table will be deleted
  repeated api.typesv2.common.Platform app_platforms_list = 5;
}

message DeleteTargetedCommsMappingResponse {
  rpc.Status status = 1;
}

message GetTargetedCommsMappingsRequest {
  MappingDetails mapping_details = 1;
}

message GetTargetedCommsMappingsResponse {
  rpc.Status status = 1;
  repeated InAppTargetedCommsMapping targeted_comms_mapping_list = 2;
}

message UpdateTargetedCommsMappingRequest {
  InAppTargetedCommsMapping targeted_comms_mapping = 1;
  repeated InAppTargetedCommsMappingFieldMask targeted_comms_field_mask = 2;
}

message UpdateTargetedCommsMappingResponse {
  rpc.Status status = 1;
}

