syntax = "proto3";

package comms.inapptargetedcomms;

import "api/comms/inapptargetedcomms/enums.proto";
import "api/dynamic_elements/dynamic_elements.proto";
import "api/dynamic_elements/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/device.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/comms/inapptargetedcomms";
option java_package = "com.github.epifi.gamma.api.comms.inapptargetedcomms";

// InAppTargetedCommsElement represents the object through which the
// in app targeted communications are displayed to the user
message InAppTargetedCommsElement {
  string id = 1;
  // A comms element can be used for alert, marketing, insights etc.
  // Not used by the client as of now.
  dynamic_elements.ElementUtilityType utility_type = 2;
  // A comms element can be a banner, bottom sheet, pop up etc.
  dynamic_elements.ElementStructureType structure_type = 3;
  // the content is dependent on the structure_type
  dynamic_elements.ElementContent content = 4;
  // start time of the duration in which the banner is to be shown
  // Also the banner will shown in this duration only if its visibility_status is ACTIVE
  google.protobuf.Timestamp start_time = 5;
  // end time of the duration in which the banner is to be shown
  google.protobuf.Timestamp end_time = 6;
  // the current visibility state of the banner
  VisibilityState visibility_state = 7;
  // timestamp at which this row was created in DB
  google.protobuf.Timestamp created_at = 8;
  // timestamp at which this row was last updated
  google.protobuf.Timestamp updated_at = 9;
  // biz meta data for analytics purpose
  ElementMetaData meta_data = 10;
  // user type to which this banner is to be shown
  repeated UserTag user_tags = 11;
  // segment expression condition to which this banner is to be shown
  string segment_expression = 12;
  // screens that are supported. eg: Home, Pay etc.
  repeated frontend.deeplink.Screen screens = 13;
  // screen additional info
  repeated string screens_meta = 14;
  // app platform & version conditions
  AppDetails app_details = 15;
}

// InAppTargetedCommsMapping represents the mapping object in the database
// An InAppTargetedCommsElement can be mapped to users, user_tags and Screens
// We store all such mappings in the same table in the database
// A mapping row in the table will have the ID of InAppTargetedCommsElement and
// a mapped value which can be ActorId of the user (OR) User_tag (OR) Screen name
message InAppTargetedCommsMapping {
  string id = 1;
  // the id of the in app targeted comms elements which is being mapped
  string element_id = 2;
  // Details on to which value a targeted comms element is mapped in this row
  // the mapping value can be a user(actor_id) or User_tag or Screen
  MappingDetails mapping_details = 3;
  // visibility state of the element particular to this user(actor_id)
  VisibilityState mapped_visibility_state = 4;
  // timestamp at which this row was created in DB
  google.protobuf.Timestamp created_at = 5;
  // timestamp at which this row was last updated
  google.protobuf.Timestamp updated_at = 6;
  // timestamp at which last callback received
  google.protobuf.Timestamp last_callback_time = 7;
}

// Details on to which value a targeted comms element is mapped in a given row
message MappingDetails {
  // the type of mapping stored in this row
  // an element can be mapped to user OR user_tag OR Screen
  MappingType mapping_type = 1;
  // for user - the mapped_value is actor_id
  // for user_tags and Screens - the mapped_value is the enum as string
  string mapped_value = 2;
  // additional info related to mapped_value
  // eg: for FAQ_CATEGORY screens it is the category ID
  string mapped_value_meta = 3;
  // visibility state of the element particular to this user(actor_id)
  VisibilityState mapped_visibility_state = 4;
}

// biz meta data for analytics purpose
message ElementMetaData {
  // To identify specific campaign this notification was sent as part of
  string campaign_name = 1;
  // used for identifying notifications for test like A/B testing.
  // Same element can have different test cases
  string treatment_id = 2;
  // what type of notification is being sent to user - PERSIST/DISMISS
  NotificationType notification_type = 3;
  // experiment id used for identifying notifications for quest experimented users
  string experiment_id = 4;
  // variant id of the experiment
  string variant_id = 5;
  // area of the element for analytics
  ElementArea area = 6;
  // sub area of the element for analytics
  ElementSubArea sub_area = 7;
  // conversion event name for analytics
  string conversion_event = 8;
}

// to map an InAppTargetedCommsElement with an app platform and a range of app versions for that platform.
message ElementToAppDetailsMapping {
  // Primary id for the mapping table
  string id = 1;
  // the id of the in app targeted comms elements which is being mapped
  string element_id = 2;
  // app platform IOS or Android
  api.typesv2.common.Platform app_platform = 3;
  // minimum app version for this app platform
  int32 min_app_version = 4;
  // minimum app version for this app platform
  int32 max_app_version = 5;
}

message FetchElementsFilters {
  // app platform for which the elements must be fetched
  api.typesv2.common.Platform app_platform = 1;
  // app version for which the elements must be fetched
  int32 app_version = 2;
  // list of screens for which the elements must be fetched
  // for now used only for single screen i.e. as single element list
  repeated MappingDetails screens = 3;
  // list of user attributes include the actor_id and use_tags applicable to the user.
  repeated MappingDetails user_attributes = 4;
}

// In app targeted comms element with Mapping details
// used to fetch data across the tables in join queries
message ElementWithMappingDetails {
  // the details of the element
  InAppTargetedCommsElement element = 1;
  // a mapping for this element
  MappingDetails mapping_details = 2;
}

message InAppTargetedCommsCallback {
  string id = 1;
  // element id to reference element details
  string element_id = 2;
  // actor id for which the element got call back
  string actor_id = 3;
  // timestamp at which this row was created in DB
  google.protobuf.Timestamp created_at = 4;
  // timestamp at which this row was last updated
  google.protobuf.Timestamp updated_at = 5;
  // timestamp at which this row was deleted
  google.protobuf.Timestamp deleted_at = 6;
}

message AppDetails {
  // minimum app version for app platform map
  map<string, int32> platform_to_min_supported_app_version_map = 1;
  // maximum app version for app platform map
  map<string, int32> platform_to_max_supported_app_version_map = 2;
}

// ElementsFilters to filter elements based on below fields.
message ElementsFilters {
  dynamic_elements.ElementUtilityType utility_type = 1;
  dynamic_elements.ElementStructureType structure_type = 2;
  VisibilityState visibility_state = 3;
  google.protobuf.Timestamp start_time = 4;
  google.protobuf.Timestamp end_time = 5;
  repeated UserTag user_tags = 6;
  repeated frontend.deeplink.Screen screens = 7;
  repeated string screens_meta = 8;
}

// CallbackFilters to filter callbacks based on below fields.
message CallbackFilters {
  repeated string element_ids = 1;
  string actor_id = 2;
}

// InAppTargetedCommsElements is a list of in app targeted comms elements for easy proto marshalling and unmarshalling
message InAppTargetedCommsElements {
  repeated InAppTargetedCommsElement in_app_targeted_comms_elements = 1;
}
