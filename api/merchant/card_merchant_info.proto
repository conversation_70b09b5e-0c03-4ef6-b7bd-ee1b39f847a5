syntax = "proto3";

package merchant;

import "api/typesv2/address.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/merchant";
option java_package = "com.github.epifi.gamma.api.merchant";

// Card merchant info is stored by merchant service for all card transactions done at merchants
message CardMerchantInfo {
  // card merchant info id
  string id = 1;
  // payment instrument id of the card merchant
  // We have decided to create payment instrument of type PARTIAL_BANK_ACCOUNT for card merchants
  // where we will use combination of name + mid for uniqueness of a pi related to merchant
  string pi_id = 2;
  // mcc code of the merchant
  // MCCs are used to identify the type of business in which a merchant is engaged. Payment brands use merchant
  // category codes (MCCs) to classify merchants and businesses by the type of goods or services provided.
  // Often, MCCs are recognized by all payment brands; however, not all codes are recognized by all brands.
  string mcc = 3;
  // mid of the card merchant
  // A merchant ID is a unique code provided to merchants by their payment processor. Often abbreviated as MID,
  // this code is transmitted along with cardholder information to involved parties for transaction reconciliation.
  // The MID can help identify a merchant when communicating with their processor and other parties.
  // But in case of ATM withdrawal or ATM deposit mid is not always given as part of notification
  string mid = 4;
  // address for merchant received in inbound notification for transactions
  api.typesv2.PostalAddress address = 5;
  // time of creation of card merchant info entry
  google.protobuf.Timestamp created_at = 6;
  // time of update of card merchant info entry
  google.protobuf.Timestamp updated_at = 7;

  // A Terminal ID (TID) is a unique identifier that can be used to identify the source of the transaction. It will be clubbed with merchant-id to get a unique identification for a merchant and it's terminal.
  string tid = 8;

  // Acquirer is a unique identifier that can be used  to identify who has issued the source of the transaction to the merchant.
  string acquirer = 9;
}
