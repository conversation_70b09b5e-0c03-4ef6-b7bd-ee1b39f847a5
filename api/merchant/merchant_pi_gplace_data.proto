syntax = "proto3";

package merchant;

import "api/vendorgateway/gplace/enums.proto";
import "api/vendorgateway/gplace/gplace.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/merchant";
option java_package = "com.github.epifi.gamma.api.merchant";


message MerchantPiGplaceData {
  // uuid - unique identifier
  string id = 1;
  string pi_id = 2;
  // Defines the types of business for a place in GPlace Response. e.g. meal_delivery, car_dealer etc.
  // More at https://developers.google.com/maps/documentation/places/web-service/supported_types
  repeated vendorgateway.gplace.GPlaceType gplace_types = 3;
  // Defines the business status of place returned by gplace api
  vendorgateway.gplace.BusinessStatus business_status = 4;
  // formatted address of place
  string formatted_address = 5;
  // Geometry object describes the location of place returned by gplace (can be visualized in google maps).
  vendorgateway.gplace.Geometry geometry = 6;
  // Contains the URL of a suggested icon which may be displayed to the user when indicating this result on a map.
  string icon_url = 7;
  // Contains the URL of a recommended icon, minus the .svg or .png file type extension.
  string icon_mask_base_uri = 8;
  // Contains the default HEX color code for the place's category.
  string icon_background_color = 9;
  // clean place name returned by gplace api
  string place_name = 10;
  // place id used by gplace api to determine each place uniquely
  string place_id = 11;

  // standard timestamp fields
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp deleted_at = 14;
}
