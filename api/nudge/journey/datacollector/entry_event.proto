//go:generate gen_queue_pb
syntax = "proto3";

package journey.datacollector;

import "api/event/rudder_event.proto";
import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";
import "api/nudge/journey/enums.proto";

option go_package = "github.com/epifi/gamma/api/nudge/journey/datacollector";
option java_package = "com.github.epifi.gamma.api.nudge.journey.datacollector";

// CollectedData defines the payload of all collected data between data collector and journey entry evaluator.
message CollectedData {
  queue.ConsumerRequestHeader request_header = 1;

  // unique id corresponding to the collected data
  string id = 2;

  // actor id corresponding to the collected data
  string actor_id = 3;

  // user action time associated with the event for which collected data is created.
  google.protobuf.Timestamp action_time = 4;

  // type of the collected data
  journey.JourneyEntryEvent event_data_type = 5;

  // one of field to contain collected event data
  oneof event_data {
    event.RudderEvent rudder_event = 6;
  }
}
