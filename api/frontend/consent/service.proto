syntax = "proto3";

package frontend.consent;

import "api/frontend/consent/consent_type.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";

option go_package = "github.com/epifi/gamma/api/frontend/consent";
option java_package = "com.github.epifi.gamma.api.frontend.consent";

// Frontend's Consent Service which manages actor consents
service Consent {
  // Record the actor's consent with timestamp
  rpc RecordConsent (RecordConsentRequest) returns (RecordConsentResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  rpc RecordWAConsent (RecordWAConsentRequest) returns (RecordWAConsentResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.location_required) = LOCATION_CHECK_REQUIRED;
  };
}

message RecordConsentRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // Type of Consent that the actor has provided
  ConsentType consentType = 3 [deprecated = true];

  // Version of Consent, if applicable Ex: Terms and condition
  int32 version = 4 [deprecated = true];

  // Consent type list
  repeated ConsentType consentTypeList = 5 [deprecated = true];

  // list of consents types to be recorded
  repeated string consents = 6;

  // [optional]: derived_account_id id of the user.
  // required in flows like upi pin set/reset
  string derived_account_id = 7;

  // [optional] - for some flows like upi pin set we need to store consent at a request level
  string client_req_id = 8;
}

message RecordConsentResponse {
  rpc.Status status = 1;
  deeplink.Deeplink next_action = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message RecordWAConsentRequest {
  frontend.header.RequestHeader req = 1;

  // WA consent was given or not
  api.typesv2.common.BooleanEnum consent_given = 2;
}

message RecordWAConsentResponse {
  rpc.Status status = 1;
  // next action after recording consent
  deeplink.Deeplink next_action = 2;
  frontend.header.ResponseHeader resp_header = 3;
}
