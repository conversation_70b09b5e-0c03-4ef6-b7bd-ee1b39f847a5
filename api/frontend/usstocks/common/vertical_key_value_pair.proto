syntax = "proto3";

package frontend.usstocks.common;

import "api/typesv2/ui/vertical_key_value_pair.proto";

option go_package = "github.com/epifi/gamma/api/frontend/usstocks/common";
option java_package = "com.github.epifi.gamma.api.frontend.usstocks.common";

// figma: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=10307-27706&mode=design&t=jS5We2UPrUnFZh7f-0
message VerticalKeyValuePair {
  // key value details
  api.typesv2.ui.VerticalKeyValuePair key_value = 1;
  // whether to give option to copy the value
  bool is_value_copiable = 2;
}

