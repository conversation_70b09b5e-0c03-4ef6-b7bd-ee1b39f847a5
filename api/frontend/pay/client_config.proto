syntax = "proto3";

package frontend.pay;

option go_package = "github.com/epifi/gamma/api/frontend/pay";
option java_package = "com.github.epifi.gamma.api.frontend.pay";

/*
  Contains configs related to pay that are consumed on client
  The configs are migrated from Firebase Remote Config to be sent via FetchConfig rpc but if the use case evolves we can
  send the same object in a new frontend service
 */
message PayClientConfig {
  // timeout for screens in intent based payment flow in milliseconds
  // Firebase default: 60000
  uint64 intent_screen_timeout_in_millis = 1;
  // apps that we DO NOT show to users in add funds & transfer-in flow or intent based payments from Fi
  // There are certain apps where we have faced failures at the 3rd party psp end, hence we block them until the issues are resolved
  // For eg: ["CRED", "Fi", "WhatsApp", "WhatsApp Messenger", "whatsapp", "iMobile Pay"]
  repeated string blocked_apps_for_intent_payment = 2;
  // list of popular UPI handles for use cases such as VPA search for suggesting VPA handles
  // For eg: ["@fifederal", "@fbl", "@ybl", "@paytm", "@okhdfcbank", "@okicici", "@okaxis", "@oksbi"]
  repeated string popular_upi_handles = 3;
  // priority for apps to be shown in add Funds & transfer-in flow
  // use case being we want to show apps like GPay, PhonePe, etc on the top
  // The priority is based on order of apps in the list
  // For eg: ["Gpay", "PayTm", "PhonePe", "Google Pay"], here Gpay has the highest priority and Google Pay has the lowest
  repeated string priority_for_intent_payment_apps = 4;
  // Timeout for transaction status screen during status polling in :
  // - Pay
  // - AutoPay
  // Firebase default: 10000
  uint64 pay_status_screen_timeout_in_millis = 5;
  // Timeout for Transaction status screen after transaction succeeds
  // Firebase default: 2000
  uint64 pay_screen_success_visibility_timeout_in_millis = 6;
  // Timeout for transaction status polling screen for Onboarding Add funds
  // Firebase default: 60000
  uint64 onboarding_add_funds_status_screen_timeout_in_millis = 7;
  // Timeout for transaction status screen for Transfer In flow or non onboarding add funds
  // Firebase default: 30000
  uint64 transfer_in_status_screen_timeout_in_millis = 8;
  // Time gap between successive API calls for next action polling in Add funds
  uint64 add_funds_next_action_polling_delay_in_millis = 9;
  // Maximum per transaction amount limit for app or maximum amount allowed to be transacted on app in a single transaction
  // For eg: 1500000 denotes 15 lakh as the max transaction amount
  uint64 max_transaction_amount = 10;
  // validity duration in days for NPCI token to be passed to NPCI-CL, new token needs to be fetched post expiry
  // Firebase default: 87
  uint32 npci_token_validity_duration_in_days = 11;
  // validity duration in days for PartnerSDK token to be passed to Partner SDK, new token needs to be fetched post expiry
  // Firebase default: 27
  uint32 partner_sdk_token_validity_duration_in_days = 12;
  // play audio when an upi transaction is success, if true play audio on UPI transaction success
  // Firebase default: true
  bool is_upi_transaction_success_audio_enabled = 13;
  // play audio when user lands on upi id creation success screen, if true play audio on UPI ID creation
  // Firebase default: true
  bool is_upi_id_creation_success_audio_enabled = 14;
  // control the visibility of create auto-pay entry-point, if true show create AutoPay entry points
  // Firebase default: false
  bool is_create_auto_pay_enabled = 15;
  // control the visibility of edit auto-pay entry-point, if true show edit AutoPay entry points
  // Firebase default: false
  bool is_edit_auto_pay_enabled = 16;
  // enable/disable UDIR based raise dispute flow, fetch dispute details if flag is true
  // Firebase default: false, true for internal user
  bool is_udir_enabled = 17;
  // Record entry for vpa migration takes almost 10 seconds
  // If you start polling by then, you get record_not_found
  // To deal with this delay, we manually add a delay before starting polling
  // Firebase default: true, this field was a boolean on Firebase
  uint64 delay_for_vpa_migration_polling_in_millis = 18;
  // Indicates if location v2 is enabled and what protocols have location permission as optional
  // Firebase default: {"is_location_v2_enabled":true,"protocols":["UPI"], "popup_delay": 1000}
  PayLocationConfig pay_location_config = 19;
  // list of video urls and languages for UPI safety tips videos
  UpiTipsConfig upi_safety_tips_config = 20;
  // control visibility of recent contacts suggestion in Pay Home Screen, section is visible if true
  // Firebase default: true
  bool show_recent_contacts = 21;
  // control visibility of previous transfer chatheads on Bank Transfer screen, section is visible if true
  // Firebase default: true for iOS version > 344 and true for android version > 253, false by default
  bool pay_bank_transfer_previous_chatheads_enabled = 22;
  // control the count of min, max and new users on your contacts on fi chat heads in pay landing screen
  // Firebase default: {"new_fi_user_count":3,"max_chat_head_count":10,"min_chat_head_count":5}
  YourContactsOnFi your_contacts_on_fi = 23;
  // Info text to be displayed in a banner in Bank Transfer screen
  // Money will be credited based solely on the payee account number and the payee name will not be used.
  string bank_transfer_info_text = 24;
  // referrals banner on PayHome, can be used to serve other static banners as long as the layout remains the same
  // Use this to serve STATIC BANNERS only.
  // "title": "Invite your friends!",
  // "subtitle": "Win ₹300 in smart deposits when your friends add ₹3000 to their Fi account.",
  // "icon_url": "Icon URL here",
  // "cta_text": "INVITE",
  // "deeplink": "CFU="
  PayHomeBottomBanner pay_home_referrals_banner = 25;
}

/*
  Config for fetching location during payment flows
  More ref regarding v1 & v2 in individual fields
 */
message PayLocationConfig {
  // location v2 makes location permission optional for certain protocols. In v1 location permission was mandatory for
  // all protocols
  bool is_location_v2_enabled = 1;
  // protocols for which location permission has been made optional
  // For eg: ["UPI"]
  repeated string optional_protocols = 2;
  // [android-specific] android doesn't pass a callback when GPS is enabled and it takes some delay for Android system
  // to register the GPS state once it is enabled, to account for that delay a delay value is configured here, after this
  // delay we check the GPS state again to see if GPS is enabled
  uint64 popup_delay_in_millis = 3;
}

/*
  Config for UPI Tips videos in multiple languages
 */
message UpiTipsConfig {
  // UPI tips configs in different languages
  repeated UpiTip upi_tips = 1;
}

/*
  Individual UPI tip config containing info about UPI Tip videos for one language
 */
message UpiTip {
  // UPI tips language
  string language = 1;
  // URL for the preview thumbnail
  string preview_url = 2;
  // UPI tips video URL
  string video_url = 3;
}

/*
  Config object for Your Contact on Fi section on Pay Landing
 */
message YourContactsOnFi {
  // count of new user chat-heads to be displayed on your contacts on Fi section
  // Default firebase value: 3
  uint32 new_fi_user_count = 1;
  // max count of chat-heads to display on your contacts on Fi section
  // Default firebase value: 10
  uint32 max_chat_head_count = 2;
  // min count of chat-heads to display on your contacts on Fi section
  // Default Firebase value: 5
  uint32 min_chat_head_count = 3;
}

/*
  Static banner at the bottom after pay entry points on pay home
  Served banners that serve as entry point to use-cases such as Referrals
 */
message PayHomeBottomBanner {
  string title = 1;
  string subtitle = 2;
  string icon_url = 3;
  string cta_text = 4;
  string deeplink = 5;
}
