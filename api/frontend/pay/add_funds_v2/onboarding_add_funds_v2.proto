syntax = "proto3";

package frontend.pay.add_funds_v2;

import "api/frontend/pay/add_funds_v2/add_funds_v2.proto";
import "api/frontend/pay/add_funds_v2/onboarding/bottom_sheet.proto";
import "api/frontend/pay/add_funds_v2/onboarding/footer.proto";
import "api/frontend/pay/add_funds_v2/onboarding/header.proto";
import "api/frontend/pay/add_funds_v2/onboarding/main_component.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/frontend/pay/add_funds_v2";
option java_package = "com.github.epifi.gamma.api.frontend.pay.add_funds_v2";

message OnboardingAddFundsDetails {
  // header of the page
  frontend.pay.add_funds_v2.onboarding.Header header_component = 1;
  // title of the page - "Make first deposit into your new Federal Bank Account **3948"
  api.typesv2.common.Text page_title = 2;
  // subtitle of the page - "It’s your money! Withdraw it instantly, at no cost"
  api.typesv2.common.Text page_subtitle = 3;
  // main component where the amount is entered, suggestions are made and benefits are displayed
  frontend.pay.add_funds_v2.onboarding.MainComponent main_component = 4;
  // Primary cta to be shown in case user is trying to add >= the minimum amount of funds
  api.typesv2.ui.IconTextComponent primary_cta = 6;
  // Secondary cta to be shown in case user is trying to add less than minimum amount of funds, or in some special cases.
  // Note: there are special cases wherein we'd use this cta over the primary cta, for e.g.
  // 1. if the max allowed amount is hard-restricted, i.e. we wouldn't want to show any payment options if user
  // enters amount above the max-amount. Refer `is_input_above_max_amount_restricted` in `ManualAmountInputComponent`
  api.typesv2.ui.IconTextComponent fallback_cta = 7;
  // Skip cta to be shown in case user wants to skip
  // Note: can be nil
  api.typesv2.ui.IconTextComponent skip_cta = 8;
  // footer of the page
  frontend.pay.add_funds_v2.onboarding.Footer footer = 9;
  // joining bonus info bottom sheet options
  // this is to be shown only when the amount being entered lies between the min and max amount
  frontend.pay.add_funds_v2.onboarding.JoiningBonusInfoBottomSheet joining_bonus_info_bottom_sheet = 10;
  // bottom sheet for payment app selection
  // consists list of upi apps and details required to pay via other payment methods
  // This will also get referred as intent flow in some places
  frontend.pay.add_funds_v2.onboarding.PaymentAppSelectionBottomSheet payment_app_selection_bottom_sheet = 11;
  // Collect flow bottomsheet for v2.2 flow
  // https://www.figma.com/file/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?type=design&node-id=1586-63113&mode=design&t=x4uoemlNigT4wMXl-0
  // Client need to show this when entered amount is outside intent_threshold(>intent_threshold)
  // Otherwise show PaymentAppSelectionBottomSheet
  // Note: V2.1 is the base version. i.e. any fields added for V2.1 should be present for V2.2. And any field added for V2.2 should be present for V2.3 and so on.
  // And if the new incremental change can't include previous minor-version changes, then it probably doesn't belong as a minor-version update. Then it should have its own major version, i.e. V3, V4.
  CollectFlowBottomSheet collect_flow_bottom_sheet_v2_2 = 15;
  // already added funds CTA, i.e. to open the bottom-sheet which takes the user via the flow of manually checking/updating balance
  // Note: can be nil.
  api.typesv2.ui.IconTextComponent check_for_already_added_balance_cta = 12;
  // bottom-sheet to be opened upon tapping `check_for_already_added_balance_cta`.
  frontend.pay.add_funds_v2.onboarding.RefreshAccountBalanceBottomSheet refresh_account_balance_bottom_sheet = 13;
  // transition page details for add funds v2.1
  TransitionPageDetailsV2_1 transition_page_details_v2_1 = 14;
  // Threshold till which intent flow is supported. After this threshold redirect to collect flow
  // NOTE: We will fall back to intent flow if amount exceeds max_amount(frontend.pay.add_funds_v2.onboarding.ManualAmountInputComponent.max_amount) for NEFT/RTGS option
  // Needed for v2.2 flow
  api.typesv2.Money intent_threshold = 16;
  // Important info to be shown on the page - will be shown only for v2.3
  // https://www.figma.com/design/zf3R089ZoP7a7XnnzLS4Fd/Central-Growth---Workfile?node-id=6911-67660&t=83dTIbTqvUp7iO9H-4
  api.typesv2.common.ui.widget.VisualElementTitleSubtitleElement important_info = 17;
}

message TransitionPageDetailsV2_1 {
  // title of the page - "Your bank account is up and running. Let's get started!"
  api.typesv2.common.Text title = 1;
  // subtitle of the page - "Put money in your account to do seamless payments and investments on Fi"
  api.typesv2.common.Text subtitle = 2;
  // bank logo to be used in the header of the amount component
  api.typesv2.common.Image bank_logo = 3;
  // component to display the account number of the user
  api.typesv2.ui.IconTextComponent account_number_display_component = 4;
  // component to display the vpa of the user
  api.typesv2.ui.IconTextComponent upi_display_component = 5;
}
