syntax = "proto3";

import "api/frontend/pay/add_funds_v2/onboarding/benefit.proto";
import "api/frontend/pay/add_funds_v2/onboarding/amount.proto";

package frontend.pay.add_funds_v2.onboarding;

option go_package = "github.com/epifi/gamma/api/frontend/pay/add_funds_v2/onboarding";
option java_package = "com.github.epifi.gamma.api.frontend.pay.add_funds_v2.onboarding";

message MainComponent {
  // component where amount details are entered and suggested amount is shown
  frontend.pay.add_funds_v2.onboarding.AmountInputComponent amount_input_info = 1;
  // component having the list of benefits
  frontend.pay.add_funds_v2.onboarding.BenefitsComponent benefits_info = 2;
}