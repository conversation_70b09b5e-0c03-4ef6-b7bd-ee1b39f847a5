syntax = "proto3";

import "api/typesv2/common/text.proto";
import "api/typesv2/common/image.proto";

package frontend.pay.add_funds_v2.onboarding;

option go_package = "github.com/epifi/gamma/api/frontend/pay/add_funds_v2/onboarding";
option java_package = "com.github.epifi.gamma.api.frontend.pay.add_funds_v2.onboarding";

message Footer {
  // Footer image url
  api.typesv2.common.Image left_image = 1;
  // Footer text - bg colour is same as the main component
  api.typesv2.common.Text text = 2;
  // Footer bank image
  api.typesv2.common.Image right_image = 3;
}
