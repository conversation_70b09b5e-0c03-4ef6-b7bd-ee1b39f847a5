syntax = "proto3";

import "api/typesv2/common/text.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";

package frontend.pay.add_funds_v2.onboarding;

option go_package = "github.com/epifi/gamma/api/frontend/pay/add_funds_v2/onboarding";
option java_package = "com.github.epifi.gamma.api.frontend.pay.add_funds_v2.onboarding";

// Benefit component to be shown
// Example - "200 Joining Bonus", "2x Rewards on Spends", "Debit card with 0 forex markup", "2% cashback on spends"
// These 4 benefits consist of a benefit component
message BenefitsComponent {
  // title of the benefits component
  api.typesv2.ui.IconTextComponent title = 1;
  // info details to be shown when user clicks on info icon
  InfoPopUpDetails info_popup = 3;
  // A range based benefit list is the benefits to be shown for a certain amount range
  // Example - Amount 0-10000 will be shown benefits Ben_1, Ben_2, Ben_3
  // Similarly, Amount 10001-20000 will be shown benefits Ben_2, Ben_4, Ben_5
  // This list of benefit range list make up a benefit component
  // Client can use the entered amount to search within the list of benefit range list and find out -
  // - the applicable benefits for this entered amount
  repeated RangeBasedBenefitsInfo range_based_benefits = 4;
  // background colour
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 5;
}

message InfoPopUpDetails {
  // title image
  api.typesv2.common.Image title_image = 1;
  // title text, example - "Benefits details"
  api.typesv2.common.Text title = 2;
  // subtitle text, example - "How do I activate these benefits?"
  api.typesv2.common.Text subtitle = 3;
  // description text
  api.typesv2.common.Text description = 4;
  // background colour
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 5;
}

// List of benefits applicable for a given amount range
// Example - lets say for the amount range 0-10000, a particular set of benefits will be applicable
// Based on the entered amount, client will fetch the applicable benefits list and display that
message RangeBasedBenefitsInfo {
  // Min Amount for which the benefit list is to be displayed - ex for the range 0-10000, this is 0 [inclusive]
  api.typesv2.Money min_amount = 1;
  // Max Amount for which the benefit list is to be displayed - ex for the range 0-10000, this is 10000 [inclusive]
  api.typesv2.Money max_amount = 2;
  // List of benefits applicable for this range
  repeated BenefitInfo benefit_infos = 3;
}

message BenefitInfo {
  // Min amount for which the benefit line item is enabled [inclusive]
  api.typesv2.Money min_amount = 1;
  // Max amount for which the benefit line item is enabled [inclusive]
  api.typesv2.Money max_amount = 2;
  // icon to be shown to the left
  api.typesv2.common.Image left_icon = 3;
  // icon to be shown to the left when the benefit is disabled
  api.typesv2.common.Image left_icon_disabled = 4;
  // title to be shown - "4x Rewards on Spends"
  api.typesv2.common.Text title = 5;
  // title to be shown when benefit is disabled
  api.typesv2.common.Text title_disabled = 6;
  // icon/text to be shown to the right when the amount being entered is eligible for this benefit
  api.typesv2.ui.IconTextComponent right_icon_text = 7;
  // icon/text to be shown to the right when the amount being entered is not eligible for this benefit - "Add 10,000"
  api.typesv2.ui.IconTextComponent right_icon_text_disabled = 8;
}
