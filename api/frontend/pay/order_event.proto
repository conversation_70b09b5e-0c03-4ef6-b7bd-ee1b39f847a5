// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package frontend.pay;

import "api/typesv2/money.proto";
import "api/typesv2/common/text.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/frontend/pay";
option java_package = "com.github.epifi.gamma.api.frontend.pay";

enum OrderStatus {
  ORDER_STATUS_UNSPECIFIED = 0;

  // Payment for order is being processed by the system
  // An actor must not retry payment, unless payment reaches an terminal state. Retrying of an order
  // IN_PROGRESS state can lead to double deduction of amount
  PAYMENT_IN_PROGRESS = 1;

  // Order payment has completed
  PAYMENT_SUCCESS = 2;

  // Order payment has failed.
  // Payment failure can be due to either permanent failures like account suspended, insufficient balance, etc.
  // or due to some transient failure like CBS systems down, etc.
  PAYMENT_FAILED = 3;

  // Order collect request has been registered successfully
  COLLECT_REGISTERED = 4;

  // Order collect request registration is getting processed by the system
  // Client must fetch the status of the order until it gets a terminal collect status
  COLLECT_REGISTRATION_IN_PROGRESS = 5;

  // Collect request couldn't be registered in the system. It can be due to various reasons
  // like invalid payer VPA, etc.
  COLLECT_REGISTRATION_FAILED = 6;

  // Collect request is declined by the payer.
  COLLECT_DECLINED = 7;

  // Collect request is cancelled by the payee who initiated the call.
  COLLECT_CANCELLED = 8;

  // Collect request has expired and there is no action taken by either payer or payee
  // on the collect request
  COLLECT_EXPIRED = 9;
}

// Frontend definition an order event. This struct is used to store order details in the client APP.
// An order event is a type of timeline event representing a workflow of financial exchanges between
// two actors which results in a transaction.
// A single order can have multiple transactions.
message OrderEvent {
  // A unique identifier to represent a payment request.
  // Order ID is a concept between client and Epifi and is abstracted
  // from the partner banks. It helps to manage the request
  // 1. initiate the transaction for the request
  // 2. Get status of the request
  string id = 1;

  // Status only returns order's payment status currently.
  // Even if an order is FULFILLMENT/SETTLEMENT at backend, client will get
  // order as PAYMENT_SUCCESS as there is no actionable on client side beyond payment
  // w.r.t. timeline.
  OrderStatus status = 2;

  // Amount of money sent/received by the actor
  api.typesv2.Money amount = 3;

  // Remarks added on the transaction made by the payer.
  // In some cases remarks can be system generated.
  string remarks = 4;

  // timestamp when an order was last updated.
  // This timestamp helps the client to dedupe the order events.
  google.protobuf.Timestamp last_updated_at = 5;

  enum Type {
    TYPE_UNSPECIFIED = 0;
    // A order of type PAY, where an actor sends money to another actor.
    PAY = 1;

    // A order of type COLLECT, where an actor sends or receives a payment request from another actor.
    COLLECT = 2;
  }

  // type of order event
  Type type = 6;

  // Image url for representing the order status. It will have image for success/fail/pending.
  // This will be empty in case of COLLECT request.
  string order_status_icon_url = 7;

  // the tag that needs to be displayed for a timeline event
  // For eg: displaying the "Charges" tag in case of chequebook and ENach charges
  // NOTE: This is api.typesv2.common.Text so that the configurability of the component resides with backend
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=17716%3A105826
  api.typesv2.common.Text order_tag = 8;

  // NOTE : This might be empty in some scenarios,
  // like collect request raised, collect request cancelled, etc
  string transaction_id = 15;
}
