syntax = "proto3";

package frontend.pay.transaction;

option go_package = "github.com/epifi/gamma/api/frontend/pay/transaction";
option java_package = "com.github.epifi.gamma.api.frontend.pay.transaction";

enum AvailabilityState {
  AVAILABILITY_STATE_UNSPECIFIED = 0;
  A<PERSON><PERSON><PERSON>ILITY_STATE_AVAILABLE = 1;
  A<PERSON>IL<PERSON>ILITY_STATE_UNAVAILABLE = 2;
  // Inactive availability state for accounts which got deactivated due to some reason(device change etc)
  AVAILABILITY_STATE_INACTIVE = 3;
}

enum ExpandCollapseState {
  EXPAND_COLLAPSE_STATE_UNSPECIFIED = 0;
  EXPAND_COLLAPSE_STATE_EXPANDED = 1;
  EXPAND_COLLAPSE_STATE_COLLAPSED = 2;
  EXPAND_COLLAPSE_STATE_PERMANENTLY_EXPANDED = 3;
  EXPAND_COLLAPSE_STATE_PERMANENTLY_COLLAPSED = 4;
}

enum PaymentOptionType {
  PAYMENT_OPTION_TYPE_UNSPECIFIED = 0;
  // UPI accounts / TPAP option which shows the list of accounts for the payment
  PAYMENT_OPTION_TYPE_TPAP = 1;
  // intent flow, i.e. Recommended Apps section to initiate the intent flow
  PAYMENT_OPTION_TYPE_INTENT = 2;
  // manual UPI ID input flow, i.e. collect flow
  PAYMENT_OPTION_TYPE_COLLECT = 3;
  // NEFT/IMPS option which shows how a user can transfer money via other means to their fi-federal account.
  // Note: This is a NO_OP option which is only for info-display purposes.
  PAYMENT_OPTION_TYPE_NEFT_IMPS = 4;
  // Debit/Credit card option, shows input fields to enter card details
  PAYMENT_OPTION_TYPE_CARD = 5;
  // Shows list of banks for the user to initiate payment via Netbanking
  PAYMENT_OPTION_TYPE_NETBANKING = 6;
}
