syntax = "proto3";
package frontend.analyser;

import "api/frontend/analyser/internal/analyser_name.proto";
import "api/frontend/analyser/internal/banner_config.proto";
import "api/frontend/analyser/internal/filter_config.proto";
import "api/frontend/analyser/internal/lens_name.proto";

option go_package = "github.com/epifi/gamma/api/frontend/analyser";
option java_package = "com.github.epifi.gamma.api.frontend.analyser";

// Config for an analyser screen. It contains info about
// different lenses and filters that are part of the analyser screen.
message AnalyserConfig {
  // unique identifier for the screen
  AnalyserName name = 1;
  string display_name = 2;
  repeated LensConfig lenses = 3;
  repeated FilterConfig filter_configs = 4;
  BannersConfig banners_config = 5;
  BannersConfig zero_state_banners_config = 6;
  HeaderConfig header_config = 7;
  ZeroStateConfig zero_state_config = 8;
  PreviewConfig preview_config = 9;
}

// LensConfig contains config for specific lens
message LensConfig {
  LensName lens_name = 1;
  string display_name = 2;
}

// HeaderConfig config for analyser header
message HeaderConfig {
  bool is_enabled = 1;
  string display_name = 2;
}

message ZeroStateConfig {
  string default_image_url = 1;
}

message PreviewConfig {
  bool is_enabled = 1;
  LensName preview_lens = 2;
  string card_icon_url = 3;
}
