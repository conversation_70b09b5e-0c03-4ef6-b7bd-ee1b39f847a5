syntax = "proto3";
package frontend.analyser;

import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/frontend/analyser";
option java_package = "com.github.epifi.gamma.api.frontend.analyser";

// Header is component that gives context on various lenses that the analyser has.
// It can have interactions such as via tabs etc, by which the user can choose that specific lens of analyser.
// Figma to show how header looks like: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=4581%3A12712&t=3qJNR2JNL7I5PruX-4
message Header {
  // This label will give information on what the various parameters/lens shown are for.
  api.typesv2.common.Text label = 1;
  DisplayType display_type = 2;
  oneof params {
    TabsParams tabs = 3;
    // A sticky header which will be on top regardless of the scroll state
    StickyHeaderBar sticky_header_bar = 4;
  }
}

enum DisplayType {
  DISPLAY_TYPE_UNSPECIFIED = 0;
  DISPLAY_TYPE_TABS = 1;
  DISPLAY_TYPE_STICKY_HEADER = 2;
}

message TabsParams {
  repeated TabDetails tab_details = 1;
}

message TabDetails {
  // The display name to show in the tab
  api.typesv2.common.Text display_name = 1;
  // Lens name to which this tab points to. Client should render the mentioned lens when the tab is clicked.
  string lens_name = 2;
}

// Header Bar on top, each header bar
// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=5354-19038&t=t0T2GJHZ2ZoZ6P94-4
// When there is more than one item in this list,
// we will show an cta to scroll through the header.
message StickyHeaderBar {
  repeated StickyHeader sticky_header = 1;
}

// Single Header Bar
message StickyHeader {
  repeated InfoHeader info_header = 1;
}

// Single header view
message InfoHeader {
  api.typesv2.common.Text primary_value = 1;
  api.typesv2.common.Text secondary_value = 2;
}
