syntax = "proto3";
package frontend.analyser;

option go_package = "github.com/epifi/gamma/api/frontend/analyser";
option java_package = "com.github.epifi.gamma.api.frontend.analyser";

import "api/frontend/analyser/icon.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/month.proto";

// DetailsModal can be used to show detailed info about a particular datapoint/metric
// to a user.
// It contains a list of detail modal items which should be displayed to the user in the order
// they are present in the array.
message DetailsModal {
  api.typesv2.common.Text title = 1;
  DetailsModalType display_type = 2;
  repeated DetailsModalItem items = 3;
}

// view type to be used for displaying DetailsModal to the user.
enum DetailsModalType {
  DETAILS_MODAL_TYPE_UNSPECIFIED = 0;
  // display data as bottom sheet
  DETAILS_MODAL_TYPE_BOTTOM_SHEET = 1;
}

// DetailsModalItem represents an item that can be shown in the DetailsModal.
message DetailsModalItem {
  DetailsModalItemType type = 1;
  oneof item {
    DetailsLineItems line_items = 2;
    StepFuncVisualisation step_func_visualisation = 3;
    InfoQnAs question_answers = 4;
    CircleImageVisualisation circle_visualisation = 5;
    SemiCircleImageVisualisation semi_circle_visualisation = 6;
    CalendarImageVisualisation calendar_visualisation = 7;
    Paragraph paragraph = 8;
    MonthActivityCalendar monthly_activity_calendar = 9;
    RectangleImageVisualisation rectangle_visualisation = 10;
    Grid grid = 11;
  }
}

enum DetailsModalItemType {
  DETAILS_MODAL_ITEM_TYPE_UNSPECIFIED = 0;
  DETAILS_MODAL_ITEM_TYPE_LINE_ITEMS = 1;
  DETAILS_MODAL_ITEM_TYPE_STEP_FUNC_VISUALISATION = 2;
  DETAILS_MODAL_ITEM_TYPE_INFO_QnA = 3;
  DETAILS_MODAL_ITEM_TYPE_CIRCLE_IMAGE_VISUALISATION = 4;
  DETAILS_MODAL_ITEM_TYPE_SEMI_CIRCLE_IMAGE_VISUALISATION = 5;
  DETAILS_MODAL_ITEM_TYPE_CALENDAR_IMAGE_VISUALISATION = 6;
  DETAILS_MODAL_ITEM_TYPE_PARAGRAPH = 7;
  DETAILS_MODAL_ITEM_TYPE_MONTHLY_ACTIVITY_CALENDAR = 8;
  DETAILS_MODAL_ITEM_TYPE_RECTANGLE_VISUALISATION = 9;
  DETAILS_MODAL_ITEM_TYPE_GRID = 10;
}

// figma link: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=697%3A3419&t=sOzBtMJE2Kh3KHkv-0
message DetailsLineItems {
  // OPTIONAL. header of the line items object
  // line items should be shown without header if `header` is empty/nil.
  DetailsLineItemsHeader header = 1;
  // list of line items to be shown
  repeated DetailsLineItem line_items = 2;
}

message DetailsLineItemsHeader {
  // title present on left side
  api.typesv2.common.Text title = 1;
  // info icon present on right side. OPTIONAL
  // icon should be shown only if info object is present.
  api.typesv2.common.Text info = 2;
}

message DetailsLineItem {
  // OPTIONAL. icon to be shown.
  Icon icon = 1 [deprecated = true];
  // text to be shown on left side
  api.typesv2.common.Text title = 2;
  // text to be shown on right side
  api.typesv2.common.Text value = 3;
  // OPTIONAL. details modal to be shown on clicking the line item.
  // If more_details is present then show a '>' icon on right-end of the line item.
  DetailsModal more_details = 4;
  // OPTIONAL. icon to be shown.
  IconV2 iconV2 = 5;
}

// List of QnAs to be shown.
message InfoQnAs {
  repeated InfoQnA questions = 1;
}

message InfoQnA {
  // question text
  api.typesv2.common.Text question = 1;
  // answer text
  api.typesv2.common.Text answer = 2;
}

// figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=697%3A3419&t=sOzBtMJE2Kh3KHkv-0
message StepFuncVisualisation {
  // type of step function to be shown.
  // placement of the labels will depend on this.
  StepFunVisType type = 1;
  // color to be filled in the step func chart.
  ColorGradient color = 2;
  // list of labels to be shown
  repeated DetailsModalVisualisationLabel labels = 3;
}

// figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=697%3A3419&t=sOzBtMJE2Kh3KHkv-0
// label to be shown on the visualisations.
message DetailsModalVisualisationLabel {
  api.typesv2.common.Text primary_text = 1;
  api.typesv2.common.Text secondary_text = 2;
}

enum StepFunVisType {
  STEP_FUNC_VIS_TYPE_UNSPECIFIED = 0;
  STEP_FUNC_VIS_TYPE_ONE_STEP_INCREASE = 1;
  STEP_FUNC_VIS_TYPE_ONE_STEP_DECREASE = 2;
  STEP_FUNC_VIS_TYPE_FLAT = 3;
}

// figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=697%3A3547&t=sOzBtMJE2Kh3KHkv-0
message CircleImageVisualisation {
  // color to be filled inside circle.
  PartialColorGradient color = 1;
  DetailsModalVisualisationLabel left_label = 2;
  DetailsModalVisualisationLabel right_label = 3;
}

// figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=697%3A3906&t=sOzBtMJE2Kh3KHkv-0
message SemiCircleImageVisualisation {
  // color to be filled inside the semi-circle vis.
  ColorGradient color = 1;
  DetailsModalVisualisationLabel label = 2;
}

// figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=697%3A4336&t=sOzBtMJE2Kh3KHkv-0
message CalendarImageVisualisation {
  api.typesv2.common.Image image = 1;
  DetailsModalVisualisationLabel label = 2;
}

// figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=697%3A4126&t=sOzBtMJE2Kh3KHkv-0
message RectangleImageVisualisation {
  // color to be filled inside circle.
  PartialColorGradient color = 1;
  DetailsModalVisualisationLabel left_label = 2;
  DetailsModalVisualisationLabel right_label = 3;
}

// regular paragraph containing text.
// figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=697%3A3789&t=sOzBtMJE2Kh3KHkv-0
message Paragraph {
  api.typesv2.common.Text title = 1;
  api.typesv2.common.Text body = 2;
  api.typesv2.common.Text footer = 3;
}

// figma: https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=697%3A3789&t=sOzBtMJE2Kh3KHkv-0
message MonthActivityCalendar {
  // list of month activity data. client will have to go through the
  // array and group the months lying in the same year.
  repeated MonthActivity monthly_activity = 1;
}

message MonthActivity {
  uint32 year = 1;
  api.typesv2.Month month = 2;
  MonthActivityValue value = 3;
}

enum MonthActivityValue {
  MONTH_ACTIVITY_VALUE_UNSPECIFIED = 0;
  MONTH_ACTIVITY_VALUE_DONE = 1;
  MONTH_ACTIVITY_VALUE_NOT_DONE = 2;
}

// object is filled with a color gradient from transitioning from start_color to end_color
message ColorGradient {
  string start_color = 1;
  string end_color = 2;
}

// we divide the object into 3 parts.
// 1. 0-`gradient_start`                : object is filled with `start_color`
// 2. `gradient_start` - `gradient_end` : object is filled with a color gradient from start_color to end_color
// 3. 0-`gradient_end`                  : object is filled with `end_color`
message PartialColorGradient {
  ColorGradient gradient = 1;
  // percentage (as decimal) of the object's length after which gradient starts. value lies in [0,1]
  float gradient_start = 2;
  // percentage (as decimal) of the object's length at which gradient ends. value lies in [0,1]
  float gradient_end = 3;
}

// 2D grid of icons.
// figma link - https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=4723%3A12865&t=zpDuYejodR7pojzY-0
message Grid{
  // cols present in the grid. cols should be shown in the same order in which they're present in this list.
  repeated string columns = 1;
  // list of rows present in the grid. Each row contains list of all cell values present in that row.
  repeated GridRow rows = 2;
}

message GridRow {
  // OPTIONAL. icon to be shown.
  Icon icon = 1 [deprecated = true];
  // text to be shown for the row
  api.typesv2.common.Text title = 2;
  // value of all the cells present in the grid row
  repeated RowCellValue cells = 3;
  // OPTIONAL. icon to be shown.
  IconV2 iconV2 = 4;
  // OPTIONAL. details modal to be shown on clicking the row.
  // If more_details is present then show a '>' icon on right-end of the line item.
  DetailsModal more_details = 5;
}

message RowCellValue {
  string column_name = 1;
  GridCellValue value = 2;
}

enum GridCellValue {
  GRID_CELL_VALUE_UNSPECIFIED = 0;
  GRID_CELL_VALUE_GREEN_TICK = 1;
  GRID_CELL_VALUE_YELLOW_EXCLAMATION = 2;
}
