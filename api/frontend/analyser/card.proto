syntax = "proto3";

package frontend.analyser;

import "api/frontend/analyser/icon.proto";
import "api/frontend/analyser/text.proto";
import "api/frontend/analyser/visual_components.proto";
import "api/frontend/analyser/visualisation.proto";
import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/frontend/analyser";
option java_package = "com.github.epifi.gamma.api.frontend.analyser";

// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=3976-10847&t=Gl7ASS2Syj5EX9ZX-4
message ExpandableCard {
  // View to be displayed when the card is in expanded state.
  ExpandedCard expanded_view = 1;
  // View to be displayed when the card is in collapsed state.
  CollapsedCard collapsed_view = 2;

  // Default state of the card, when the card will be displayed.
  bool is_expanded = 3;
  // Sometimes a card can be added as a placeholder for
  // features in future. Such cards will be disabled by
  // default.
  bool is_enabled = 4;
}

// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=3976-10852&t=Gl7ASS2Syj5EX9ZX-4
message ExpandedCard {
  Icon icon = 1 [deprecated = true];
  api.typesv2.common.Text title = 2;
  Cta cta = 3;
  repeated CardComponent card_components = 4;
  IconV2 iconV2 = 5;
}

// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=3976-10853&t=Gl7ASS2Syj5EX9ZX-4
message CollapsedCard {
  Icon icon = 1 [deprecated = true];
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text sub_title = 3;

  // tag/chips to be displayed on the card
  repeated api.typesv2.common.Text tag = 5;

  IconV2 iconV2 = 6;
}

message CardComponent {
  CardComponentType type = 1;
  oneof component {
    Visualisation visualisation = 2;
    StaticVisualisation static_visualisation = 3;
  }
}

enum CardComponentType {
  CARD_COMPONENT_TYPE_UNSPECIFIED = 0;
  CARD_COMPONENT_TYPE_VISUALISATION = 1;
  CARD_COMPONENT_TYPE_STATIC_VISUALISATION = 2;
}

enum WidgetCardName {
  WIDGET_CARD_NAME_UNSPECIFIED = 0;
  WIDGET_CARD_NAME_CREDIT_SCORE = 1;
  WIDGET_CARD_NAME_MUTUAL_FUND_GROWTH = 2;
  WIDGET_CARD_NAME_TOP_CATEGORIES = 3;
  WIDGET_CARD_NAME_MONEY_OUT = 4;
  WIDGET_CARD_NAME_USSTOCKS_GROWTH = 5;
}
