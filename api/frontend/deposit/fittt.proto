syntax = "proto3";

package frontend.deposit;

import "api/frontend/fittt/fittt.proto";

option go_package = "github.com/epifi/gamma/api/frontend/deposit";
option java_package = "com.github.epifi.gamma.api.frontend.deposit";

// DepositAutoSaveParams contains the auto save details if the user opted for setting up auto save rules while creating the deposit account.
message DepositAutoSaveParams {
  string rule_id = 1;
  // rule param values of the auto save suggestion
  fittt.RuleParamValues rule_param_values = 2;
}
