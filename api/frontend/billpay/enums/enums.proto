syntax = "proto3";

package frontend.billpay.enums;

option go_package = "github.com/epifi/gamma/api/frontend/billpay/enums";
option java_package = "com.github.epifi.gamma.api.frontend.billpay.enums";
option java_multiple_files = true;

// Enum for RechargeOrder status (mirrored from billpay.RechargeOrderStatus)
enum RechargeOrderStatus {
  RECHARGE_ORDER_STATUS_UNSPECIFIED = 0;
  // Recharge process has started at our end but user is yet to complete the payment
  RECHARGE_ORDER_STATUS_INITIATED = 1;
  // Payment is complete. Vendor fulfilment is in progress.
  RECHARGE_ORDER_STATUS_IN_PROGRESS = 2;
  // Recharge completed successfully with vendor.
  RECHARGE_ORDER_STATUS_SUCCESS = 3;
  // Permanent failure during payment or fulfillment - no retry possible.
  RECHARGE_ORDER_STATUS_FAILED = 4;
  // Transient error requiring manual review/intervention by internal team.
  RECHARGE_ORDER_STATUS_MANUAL_INTERVENTION = 5;
  // Order expired due to user inaction (payment auth timeout after 20 minutes).
  RECHARGE_ORDER_STATUS_EXPIRED = 6;
}
