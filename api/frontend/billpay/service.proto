syntax = "proto3";

package frontend.billpay;

import "api/frontend/billpay/enums/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/typesv2/billpay/layout.proto";
import "api/typesv2/billpay/billpay.proto";

option go_package = "github.com/epifi/gamma/api/frontend/billpay";
option java_package = "com.github.epifi.gamma.api.frontend.billpay";
option java_multiple_files = true;

// Service to handle bill payments for frontend
service BillPay {
  // Creates a new recharge order
  // Acts as a messenger to the billpay.BillPay.CreateRechargeOrder RPC
  rpc CreateRechargeOrder (CreateRechargeOrderRequest) returns (CreateRechargeOrderResponse);

  // Gets the status and next action for a recharge order
  // Client should start polling after CreateRechargeOrder success response is received
  // Client will continue to poll as long as RECHARGE_POLLING_SCREEN is received in deeplink
  rpc GetRechargeOrderStatus (GetRechargeOrderStatusRequest) returns (GetRechargeOrderStatusResponse);

  // Retrieves intro screen for the recharges feature.
  rpc GetRechargeIntroScreen (GetRechargeIntroScreenRequest) returns (GetRechargeIntroScreenResponse);

  // Retrieves the screen that displays available recharge plans.
  rpc GetRechargePlansScreen (GetRechargePlansScreenRequest) returns (GetRechargePlansScreenResponse);

  // Retrieves the screen that displays the bill details and confirmation for payment.
  rpc GetBillDetailsConfirmationScreen (GetBillDetailsConfirmationScreenRequest) returns (GetBillDetailsConfirmationScreenResponse);
}

// Request/Response for CreateRechargeOrder
message CreateRechargeOrderRequest {
  frontend.header.RequestHeader req = 1;
  // expected to be of RechargeAccountType enum
  string account_type = 2;
  // account identifier of corresponding account_type e.g. mobile number for MOBILE account
  string account_identifier = 3;
  // operator for the recharge e.g. Jio/Airtel/Vi for mobile recharges
  string operator_id = 4;
  // unique identifier for plans provided by an operator
  string plan_id = 5;
  string client_request_id = 6;
}

message CreateRechargeOrderResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Deeplink that client should honour when success response is received
  frontend.deeplink.Deeplink next_action = 2;
}

// Request/Response for GetRechargeOrderStatus
message GetRechargeOrderStatusRequest {
  frontend.header.RequestHeader req = 1;
  string client_request_id = 2;
}

message GetRechargeOrderStatusResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Status of the recharge order
  frontend.billpay.enums.RechargeOrderStatus order_status = 2;
  // Deeplink that client should honour. If RECHARGE_POLLING_SCREEN is received, client will keep polling
  frontend.deeplink.Deeplink next_action = 3;
}

message GetRechargeIntroScreenRequest {
  frontend.header.RequestHeader req = 1;
  // expected to be of RechargeAccountType enum
  string account_type = 2;
  // account identifier of corresponding account_type e.g. mobile number for MOBILE account
  string account_identifier = 3 [deprecated = true];
}

message GetRechargeIntroScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.billpay.RechargeIntroScreen layout = 2;
}

// Request/Response for GetRechargePlans
message GetRechargePlansScreenRequest {
  frontend.header.RequestHeader req = 1;
  // expected to be of RechargeAccountType enum
  string account_type = 2;
  // account identifier of corresponding account_type e.g. mobile number for MOBILE account
  string account_identifier = 3;
  // operator for the recharge e.g. Jio/Airtel/Vi for mobile recharges
  string operator_id = 4 [deprecated = true];
}

message GetRechargePlansScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.billpay.RechargePlansScreen layout = 2;
  // operator for the recharge e.g. Jio/Airtel/Vi for mobile recharges
  string operator_id = 3;
}

message GetBillDetailsConfirmationScreenRequest {
  frontend.header.RequestHeader req = 1;
  oneof params {
    api.typesv2.billpay.RechargeParams recharge_params = 2;
  }
}

message GetBillDetailsConfirmationScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.billpay.BillDetailsConfirmationScreen layout = 2;
  // unique id to be used for the payment flow
  string client_request_id = 3;
  // one-of field indicating which payment flow should be initiated by the client.
  // Contains necessary parameters for the selected flow.
  // e.g., if `recharge_params` is received, start the `CreateRechargeOrder` flow.
  oneof payment_params {
    api.typesv2.billpay.RechargeParams recharge_params = 4;
  }
}
