syntax = "proto3";

package frontend.pan;

import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/deeplink/deeplink.proto";
import "google/type/date.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/polling_request_info.proto";
option go_package = "github.com/epifi/gamma/api/frontend/pan";
option java_package = "com.github.epifi.gamma.api.frontend.pan";

service Pan {
  // InitiateEPAN is first rpc call in epan flow. It is called right before initiating epan sdk
  // InitiateEPAN return karza token and client request id
  // client request id is also known as case id.
  rpc InitiateEPAN(InitiateEPANRequest) returns (InitiateEPANResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // client update event received from karza using this api
  // it can return next action on basis of event received from karza
  rpc PushEPANEvent(PushEPANEventRequest) returns (PushEPANEventResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetEPANNextStatus is polling api used after sdk exit event to fetch latest status for epan
  rpc GetEPANStatus(GetEPANStatusRequest) returns (GetEPANStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetEPANNextAction to fetch next action after epan flow terminated
  // EPAN_FE_STATE_SUCCESS, EPAN_FE_STATE_FAILED indicates EPAN terminal state
  rpc GetEPANNextAction(GetEPANNextActionRequest) returns (GetEPANNextActionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // UploadEPAN allows clients to upload an EPAN document.
  // EPAN documents are typically in PDF format and require a password to open.
  // Client will send binary data of document and password
  rpc UploadEPAN(UploadEPANRequest) returns (UploadEPANResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetPanUpdateNextAction will be used to return next action for PAN Update flow
  rpc GetPanUpdateNextAction (GetPanUpdateNextActionRequest) returns (GetPanUpdateNextActionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message GetPanUpdateNextActionRequest {
  frontend.header.RequestHeader req = 1;
  string client_last_state = 2;
  api.typesv2.PollingRequestInfo polling_request_info = 3;
}

message GetPanUpdateNextActionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
  api.typesv2.PollingRequestInfo polling_request_info = 3;
}


message UploadEPANRequest {
  frontend.header.RequestHeader req = 1;
  // Binary data of the EPAN document in PDF format.
  bytes data = 2;
  // Password required to open the EPAN document.
  string password = 3;
  // client will sent client_req_id sent by backend in EPANUploadDeeplink
  string client_req_id = 4;
}

message UploadEPANResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message InitiateEPANRequest {
  frontend.header.RequestHeader req = 1;
  // client request id passed to client as part of epan screen options
  string client_request_id = 2;
}

message InitiateEPANResponse {
  enum status {
    OK = 0;
    CALL_NEXT_ACTION_API = 101;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // token generated by backend using karza api
  // client use this token while initiate epan sdk
  string token = 2;
  // client request id used to identify user from karza callbacks aka case id
  // client should inject this as case id while initiating epan flow
  string client_request_id = 3;
  // client have to inject in sdk so that it can be prefilled
  string pan_number = 4;
  // client have to inject in sdk so that it can be prefilled
  google.type.Date date_of_birth = 5;
  // having constant value as epifi
  // client should inject this as client id while initiating epan flow
  string client_id = 6;
  // env used by client to pass in karza sdk
  string env = 7;
}

message PushEPANEventRequest {
  frontend.header.RequestHeader req = 1;
  // common identifier between karza, FE and BE
  // client received client_request_id in InitiateEPAN rpc response
  string client_request_id = 2;
  // during epan process client receives event from karza stating epan status
  string event = 3;
}

message PushEPANEventResponse {
  enum status {
    OK = 0;
    CALL_NEXT_ACTION_API = 101;
  }
  frontend.header.ResponseHeader resp_header = 1;
}

message GetEPANStatusRequest {
  frontend.header.RequestHeader req = 1;
  // common identifier between karza, FE and BE
  string client_request_id = 2;
}

message GetEPANStatusResponse {
  enum status {
    OK = 0;
    // client is expected to call GetEPANNextAction
    CALL_NEXT_ACTION_API = 101;
    // client is expected to call
    CALL_STATUS_API = 102;
  }
  frontend.header.ResponseHeader resp_header = 1;
}

message GetEPANNextActionRequest {
  frontend.header.RequestHeader req = 1;
  // attempt id passed in user waiting screen to client
  string client_request_id = 2;
  // entry point received in screen options of EPANEntry screen options
  bytes blob = 3;
}

message GetEPANNextActionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // next action contains deeplink for action to be taken by client
  frontend.deeplink.Deeplink next_action = 2;
}
