// protolint:disable MAX_LINE_LENGTH

// Frontend RPC to sync contacts from client application

syntax = "proto3";
package frontend.contacts;

import "api/frontend/header/auth.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/phone_number.proto";

option go_package = "github.com/epifi/gamma/api/frontend/contacts";
option java_package = "com.github.epifi.gamma.api.frontend.contacts";

// Request to Sync Contacts of an User
message ContactSyncRequest {

  frontend.header.AuthHeader auth_header = 1;

  // List of Contacts
  repeated Contact contacts = 2;
}

// Response for Sync Contacts
message ContactSyncResponse {
  //Status Code
  rpc.Status status = 1;
  frontend.header.ResponseHeader resp_header = 15;
}

// Contact details of a person. May or May not be an user.
message Contact {
  ContactInfo info = 1;
  repeated api.typesv2.common.PhoneNumber phone_number = 2;
}

//TODO(Rohan): Need to get details from Product.
// Unique information of the person.
message ContactInfo {
  string name = 1;
}

// Frontend API for Contacts Sync
service Contacts {

  // RPC to Sync Contacts of an User
  rpc SyncContacts (ContactSyncRequest) returns (ContactSyncResponse) {
    option (rpc.auth_required) = true;
  };
}
