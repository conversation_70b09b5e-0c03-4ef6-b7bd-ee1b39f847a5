syntax = "proto3";

package frontend.nudge;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/frontend/nudge";
option java_package = "com.github.epifi.gamma.api.frontend.nudge";

// Figma: https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4020-9184&t=g3DjDNXrkWhQvmN2-1
message Journey {
  string actor_journey_id = 1;

  // title of the journey card
  // eg- Personalise my experience
  api.typesv2.common.Text title = 2;

  // image inside the journey card
  api.typesv2.common.VisualElement visual_element = 3;

  // background colour of the journey card
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 4;

  // call to action of the journey card
  api.typesv2.ui.IconTextComponent cta = 5;

  // for redirection when journey card is clicked
  frontend.deeplink.Deeplink deeplink = 6;

  // optional: progress percentage of the journey
  ProgressBar progress_bar = 7;
  message ProgressBar {
    // percentage progress of the journey
    // eg- 30 implies journey is 30% completed
    int32 percentage_progress = 1;

    // colour of the completed part of the progress bar
    api.typesv2.common.ui.widget.BackgroundColour completed_bar_colour = 2;

    // colour of the uncompleted part of the progress bar
    api.typesv2.common.ui.widget.BackgroundColour uncompleted_bar_colour = 3;
  }

  // description of the journey card
  // eg- Takes 3 mins to complete
  api.typesv2.common.Text description = 8;

  // border color for each journey
  api.typesv2.common.ui.widget.BackgroundColour border_colour = 9;
}

// Figma - https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-60286&p=f&t=ihSf7PIRxUbO2WIj-0
message ActivationWidget {
  // Unique identifier for the actor's journey, used to track the widget's progress.
  string actor_journey_id = 1;
  // Sections of the activation widget for rendering in vertical order of the array.
  repeated ActivationWidgetSection sections = 2;
  // Additional metadata for analytics tracking.
  map<string, string> analytics_metadata = 3;
  // border color for the activation widget
  // Figma -> https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28885-38203&t=7vPgmdhO2Y6CaXj3-4
  api.typesv2.common.ui.widget.BackgroundColour border_color = 4;
}

message ActivationWidgetSection {
  enum SectionType {
    SECTION_TYPE_UNSPECIFIED = 0;
    SECTION_TYPE_VISUAL_HEADER = 1;
    SECTION_TYPE_PROGRESS_SUMMARY = 2;
    SECTION_TYPE_MILESTONE_TRACKER = 3;
    SECTION_TYPE_MILESTONE_WIDGET_BANNER = 4;
  }

  SectionType section_type = 1;
  oneof section {
    VisualHeaderSection visual_header_section = 2;
    ProgressSummarySection progress_summary_section = 3;
    MilestoneTrackerSection milestone_section = 4;
    MilestoneWidgetBanner banner_section = 5;
  }
}

// https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-88528&t=7iQfPEzkgmo5aqkt-0
message VisualHeaderSection {
  // Primary visual element, such as an image or Lottie animation, used to enhance the widget's visual appeal.
  api.typesv2.common.VisualElement visual_element = 1;
  // Deeplink for redirection when the visual element is clicked, allowing users to navigate to relevant sections.
  frontend.deeplink.Deeplink deeplink = 2;
  // Call-to-action button displayed upon completing all milestones.
  // Clicking this button triggers the final completion flow, where the client sends an
  // `PerformCompletionAction` request with the `actor_journey_id` to mark the journey as complete.
  // Once the backend processes this state update, subsequent API calls will no longer include this section.
  // https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-60286&p=f&t=fL4rwdZX6XDkzYGW-0
  api.typesv2.ui.IconTextComponent perform_completion_action = 3;
}


// https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-88528&t=fL4rwdZX6XDkzYGW-0
message ProgressSummarySection {
  // Displays the current progress towards the reward or milestone completion.
  api.typesv2.ui.IconTextComponent progress_summary = 1;
  // Optional: Additional information or context about the campaign.
  api.typesv2.ui.IconTextComponent info_text = 2;
}

// https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-88528&t=fL4rwdZX6XDkzYGW-0
message MilestoneTrackerSection {
  // Section header for the milestone tracker (e.g., "6 days left").
  MilestoneTrackerHeader tracker_component = 1;

  message MilestoneTrackerHeader {
    // Title of the milestone tracker (e.g., Make a payment greater than 100).
    api.typesv2.ui.IconTextComponent tracker_title = 1;
    // Background color of the milestone tracker.
    api.typesv2.common.ui.widget.BackgroundColour tracker_background = 2;
  }

  // List of milestone steps required for completion.
  repeated Milestone milestone_steps = 2;

  message Milestone {
    // Left-aligned progress indicator for the milestone step (e.g., "Earn ₹500").
    // Component representing visual indicators like checkmarks, icons, or progress markers.
    // https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-88528&t=7iQfPEzkgmo5aqkt-0
    MilestoneBadge milestone_badge = 1;

    message MilestoneBadge {
      // Icon with associated text.
      api.typesv2.ui.VerticalKeyValuePair badge_icon_text = 1;
      // Background color for the indicator.
      api.typesv2.common.ui.widget.BackgroundColour badge_background = 2;
      // Corner radius for UI styling.
      int32 corner_radius = 3;
    }

    // Title or description of the milestone step.
    api.typesv2.common.Text title = 2;
    // Right-aligned action button (e.g., "Pay Now").
    // This button is shown when the milestone step is in progress.
    api.typesv2.ui.IconTextComponent action_button = 3;
    // Background color of the milestone step card.
    api.typesv2.common.ui.widget.BackgroundColour card_background = 4;
    // Background color of the next-step indicator.
    api.typesv2.common.ui.widget.BackgroundColour step_indicator_color = 5;

    // Current state of the milestone step (e.g., "Completed," "In Progress").
    MilestoneState step_state = 6;
  }
}

// https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-89851&t=fL4rwdZX6XDkzYGW-0
message MilestoneWidgetBanner {
  // Left-aligned indicator icon (e.g., Amazon gift card).
  api.typesv2.common.VisualElement left_icon = 1;
  // Description of the next activation campaign (e.g., "Your next reward unlocks in").
  api.typesv2.common.Text description = 2;
  // Background color for the component.
  api.typesv2.common.ui.widget.BackgroundColour background_color = 3;
  // shows the time left for the next activation campaign.
  Timer time_left = 4;

  message Timer {
    // Time unit (e.g., "Days," "Hours").
    api.typesv2.common.Text time_unit = 1;
    // Time value (e.g., "06" days left).
    api.typesv2.common.Text time_value = 2;
    // Background color for the countdown timer.
    api.typesv2.common.ui.widget.BackgroundColour background_color = 3;
    // Background image for the timer.
    api.typesv2.common.VisualElement timer_bg_image = 4;
  }
}

// Enum representing the state of a milestone step.
enum MilestoneState {
  MILESTONE_STATE_UNSPECIFIED = 0;
  MILESTONE_STATE_NOT_STARTED = 1;
  MILESTONE_STATE_IN_PROGRESS = 2;
  MILESTONE_STATE_COMPLETED = 3;
}

