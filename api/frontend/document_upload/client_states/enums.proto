syntax = "proto3";

package frontend.document_upload.clientstate;

option go_package = "github.com/epifi/gamma/api/frontend/document_upload/client_states";
option java_package = "com.github.epifi.gamma.api.frontend.document_upload.client_states";

// represents types of documents which can be uploaded
enum DocumentType {
  DOCUMENT_TYPE_UNSPECIFIED = 0;
  DOCUMENT_TYPE_PAN_CARD = 1;
  DOCUMENT_TYPE_FI_BANK_ACCOUNT_STATEMENT = 2;
  DOCUMENT_TYPE_CONNECTED_ACCOUNT_STATEMENT = 3;
}

// represents status for document generation
enum DocumentGenerationStatus {
  DOCUMENT_GENERATION_STATUS_UNSPECIFIED = 0;
  DOCUMENT_GENERATION_STATUS_IN_PROGRESS = 1;
  DOCUMENT_GENERATION_STATUS_COMPLETED = 2;
  DOCUMENT_GENERATION_STATUS_FAILED = 3;
}

// client consuming the document
enum Client {
  CLIENT_UNSPECIFIED = 0;
  CLIENT_US_STOCKS = 1;
  CLIENT_SALARY_PROGRAM = 2;
}
