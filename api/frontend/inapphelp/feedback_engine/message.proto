syntax = "proto3";

package frontend.inapphelp.feedback_engine;

import "api/frontend/inapphelp/feedback_engine/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/inapphelp_feedback_engine.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/frontend/inapphelp/feedback_engine";
option java_package = "com.github.epifi.gamma.api.frontend.inapphelp.feedback_engine";


// Represents the content of a feedback question, including heading and text.
// It also supports displaying contextual cards for engagement-type feedback content,
// such as promotional or informational bottom sheets, based on the `feedback_content_type`.
message FeedbackQuestionContent {
  // Heading of the feedback question (used for standard question-answer content).
  api.typesv2.common.Text question_heading = 1;
  // Text content of the feedback question (used for standard question-answer content).
  api.typesv2.common.Text question_text = 2;

  // Contextual card to provide user engagement content when `feedback_content_type` is set to ENGAGEMENT.
  // This field is utilized to display bottom sheet content in scenarios where the feedback answer type
  // does not require user input (FEEDBACK_ANSWER_TYPE_NO_ANSWER).
  // Designed to show dynamic content, such as images, animations, or promotional offers, as a temporary solution
  // until a dedicated system for managing engagement content is implemented.
  //
  // Reference document for approach rationale:
  // https://docs.google.com/document/d/1fDJrG0CXOakoY_N4lVEU3hMB1QtA2wgLzkwqzQk5Dg8/edit?tab=t.0
  oneof contextual_card {
    // Card driven entirely by a visual element.
    FullVisualElementCard full_visual_element_card = 3;
    // Card driven by text and visual elements.
    TextVisualElementCard text_visual_element_card = 4;
  }
}

// Card content driven entirely by image/lottie.
// Figma - https://www.figma.com/design/5TZH6I0awKw8DMtj3aMzrC/Referrals-%E2%80%A2-FFF?node-id=12101-5707&node-type=frame&t=E2GIRbWcPUD0vb3R-0
message FullVisualElementCard {
  // Visual element to be shown on the entire card
  api.typesv2.common.VisualElement visual_element = 1;
  // List of vertical CTA buttons for user interaction
  repeated frontend.deeplink.Cta ctas = 2;
  // Background color of the CTA section
  api.typesv2.ui.BackgroundColour cta_container_bg_color = 3;
}

// Card content driven by a combination of text and visuals
// Figma - https://www.figma.com/design/5TZH6I0awKw8DMtj3aMzrC/Referrals-%E2%80%A2-FFF?node-id=12101-5707&node-type=frame&t=E2GIRbWcPUD0vb3R-0
message TextVisualElementCard {
  // Visual content displayed at the top
  api.typesv2.common.VisualElement visual_element = 1;
  // Heading text of the card
  api.typesv2.common.Text heading = 2;
  // Sub heading text of the card
  api.typesv2.common.Text sub_heading = 3;
  // Expandable section with additional info details
  InfoPointsSection info_points_section = 4;
  // List of vertical CTA buttons for user interaction
  repeated frontend.deeplink.Cta ctas = 5;
  // Background color of the CTA section
  api.typesv2.ui.BackgroundColour cta_container_bg_color = 6;
}

// Dynamic information for expandable/collapse section
message InfoPointsSection {
  // Title of the expandable section, for e.g. "See how it works ?"
  api.typesv2.common.Text title = 1;
  // boolean flag to indicate if the section should be in expanded or collapsed state
  bool is_collapsed = 2;
  // List of detailed points
  repeated api.typesv2.ui.IconTextComponent info_points = 3;
}

// Contains the details related to feedback question, including ID, answer options, content type etc
message FeedbackQuestion {
  // unique identifier for a question
  string question_id = 1;
  // question content like question text, heading and contextual details.
  FeedbackQuestionContent question_content = 2;
  // type of the feedback answer ex. text, dropdown
  // client is expected to show appropriate input to user based on this type
  FeedbackAnswerType feedback_answer_type = 3;
  // in case of questions like checkbox, dropdown this field will be utilised to provide these details to client
  AnswerOptions answer_options = 4;
  // indicates if the user can skip the given question
  bool is_optional = 5;
  // Differentiates between a standard question-answer flow and engagement-type content
  // (e.g., promotional or contextual cards shown as a bottom sheet).
  FeedbackContentType feedback_content_type = 6;
}

// used to populate possible answer options if required by for a feedback response type
message AnswerOptions {
  oneof answerOptions {
    CheckboxOptions checkbox_options = 1;
    DropDownOptions drop_down_options = 2;
    EmojiOptions emoji_options = 3;
    ScaleOptions scale_options = 4;
    RadioButtonOptions radio_button_options = 5;
    RatingSliderOptions rating_slider_options = 6;
    ThumbsUpThumbsDownOptions thumbs_up_thumbs_down_options = 7;
  }
}

// options to populate possible answer options for checkbox
message CheckboxOptions {
  // to show empty text field or not for providing feedback
  bool is_additional_answer_asked = 1;
  // to specify if user can select multiple checkboxes
  bool is_multi_selection_allowed = 2;
  // contains list of checkbox options
  repeated string checkbox_options = 3;
}

// options to populate possible answer options for dropdown
message DropDownOptions {
  // used to show empty text field or not for providing feedback
  bool is_additional_answer_asked = 1;
  // to specify if User can select multiple dropdowns
  bool is_multi_selection_allowed = 2;
  // contains list of dropdown options
  repeated string dropdown_options = 3;
}

// options to populate possible answer options for emoji
message EmojiOptions {
  // range over which the emoji response is to be asked
  repeated string emoji_urls = 1;
}

// options to populate possible scale options
message ScaleOptions {
  // to show empty text field or not for providing feedback
  bool is_additional_answer_asked = 1;
  // image url for filled state of all items on the scale
  string filled_state_url = 2;
  // image url for unfilled state of all items on the scale
  string unfilled_state_url = 3;
  // number of items on the scale
  int32 number_of_items = 4;
}


// options to populate possible radio button options for radio buttons
message RadioButtonOptions {
  // used to show empty text field or not for providing feedback
  bool is_additional_answer_asked = 1;
  // contains list of radio button options
  repeated string radio_button_options = 3;
}

// details that would be sent to client for a particular rating slider value
message RatingSliderValueDetails {
  // url of the image to show for a rating slider value
  string rating_slider_value_url = 1;
  // label of the image to show for a rating slider value
  api.typesv2.common.Text rating_slider_value_label = 2;
}


message RatingSliderOptions {
  // to show empty text field or not for providing feedback
  bool is_additional_answer_asked = 1;
  // minimum possible numeric value possible for the slider
  int32 minimum_numeric_value = 2;
  // maximum possible numeric value possible for the slider
  int32 maximum_numeric_value = 3;
  // rating slider step value indicating the number of steps
  // between minimum and maximum numeric value
  int32 rating_slider_step_value = 4;
  // labels to show for rating values
  map<int32, RatingSliderValueDetails> rating_value_to_details_mapping = 5;
  // default value to be set in the slider
  int32 default_numeric_value = 6;
}

message ThumbsUpThumbsDownOptions {
  // image url for thumbs up option
  string thumbs_up_img_url = 1;
  // image url for thumbs down option
  string thumbs_down_img_url = 2;
}

// FeedbackResponse contains response type and details related to response for a feedback question
message FeedbackAnswer {
  FeedbackAnswerType answer_type = 1;
  oneof answer {
    TextBoxAnswer text_box_answer = 2;
    // contains answer for checkbox based responses
    CheckBoxAnswer check_box_answer = 3;
    // contains answer for thumbs up and thumbs down responses
    ThumbsUpThumbsDownAnswer thumbs_up_thumbs_down_answer = 4;
    // contains answer for emoji response over a range
    EmojiAnswer emoji_answer = 5;
    // contains answer for dropdown based responses
    DropdownAnswer dropdown_answer = 6;
    // contains answer of a scale over a range, for ex. a scale of stars
    ScaleAnswer scale_answer = 7;
    // contains answer of rating slider
    RatingSliderAnswer rating_slider_answer = 8;
    // contains answer of radio button based responses
    RadioButtonAnswer radio_button_answer = 9;
  }
}

message TextBoxAnswer {
  string textbox_response = 1;
}

message CheckBoxAnswer {
  // User can select multiple checkboxes for providing feedback
  repeated string selected_checkboxes = 1;
  // signifies the text field below checkboxes where user can provide additional/other feedback
  string additional_answer = 2;
}

message ThumbsUpThumbsDownAnswer {
  api.typesv2.common.BooleanEnum is_thumbs_up = 1;
}

message EmojiAnswer {
  // integer value in one based indexing denoting the emoji response
  // for ex. for a emoji corresponding to least rating selected by user, value 1 is expected
  int32 emoji_answer = 1;
  // total number of emojis out of which a particular emoji was answered
  int32 total_emojis = 2;
}

message DropdownAnswer {
  repeated string selected_dropdowns = 1;
}

message ScaleAnswer {
  // the range of scale, for ex. selection out of three items, five items etc
  int32 number_of_items = 1;
  // integer value in one based indexing denoting the number of items selected on the scale
  int32 selected_item = 2;
  // url for filled state of the selected item
  string filled_state_url = 3;
  // url for unfilled state of the selected item
  string unfilled_state_url = 4;
  // signifies the text field below the scale where user can provide additional/other feedback
  string additional_answer = 5;
}

// We are storing other values like minimum, maximum and default value for slider answer
// so that this data can be utilized for normalization and analytics purpose
message RatingSliderAnswer {
  // minimum possible value for the slider
  int32 minimum_rating_value = 1;
  // maximum possible value for the slider
  int32 maximum_rating_value = 2;
  // rating value answered by the user
  int32 answered_rating_value = 3;
  // rating slider step value indicates the number of steps
  // between minimum and maximum numeric value
  int32 rating_slider_step_value = 4;
  // label of the rating selected by user
  api.typesv2.common.Text answered_rating_label = 5;
  // default numeric value of the slider
  int32 default_numeric_value = 6;
}

message RadioButtonAnswer {
  // ideally for a radio button only a single option is selected
  // but accepting a list in case some use case arises in future for
  // selection of multiple radio buttons
  repeated string selected_radio_buttons = 1;
  // contains response of the text field (if any) below radio buttons where user can provide additional/other feedback
  string additional_answer = 2;
}

// FlowIdentifierDetails is received by clients from various backend RPC calls
// to initiate trigger for Feedback flow.
// When this is received by any client, it is expected to be passed in
// the subsequent `GetFirstFeedbackQuestion` RPC request
// flow_identifier_type is kept as enum as client is required to compare it to do certain actions
// flow_identifier is explicitly kept as string to avoid client releases in case new type or value of flow ids are added in backend
// flow_invocation_identifier is a string used to identify a particular invocation of feedback flow
message FlowIdentifierDetails {
  api.typesv2.FeedbackFlowIdentifierType flow_identifier_type = 1;
  string flow_identifier = 2;
  string flow_invocation_identifier = 3;
}
