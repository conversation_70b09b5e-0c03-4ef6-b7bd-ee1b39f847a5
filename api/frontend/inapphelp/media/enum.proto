syntax = "proto3";
package frontend.inapphelp.media;

option go_package = "github.com/epifi/gamma/api/frontend/inapphelp/media";
option java_package = "com.github.epifi.gamma.api.frontend.inapphelp.media";

// visibility identifier on what platform content should be surfaced on
// ALL denotes that content has to be surfaced on all platforms
// HIDDEN denotes content should not be displayed on any of the platform, acts like soft delete
enum ContentVisibility {
  CONTENT_VISIBILITY_UNSPECIFIED = 0;
  // Mobile client: Android, iOS, etc
  CONTENT_VISIBILITY_ANDROID = 1;
  // Mobile client: IOS
  CONTENT_VISIBILITY_IOS = 2;
  // Web client
  CONTENT_VISIBILITY_WEBSITE = 3;
  // all platform
  CONTENT_VISIBILITY_ALL = 4;
  // only mobile
  CONTENT_VISIBILITY_ANDROID_AND_IOS = 5;
  // Hides the content from all the sources, can be used as soft delete / hide flag
  CONTENT_VISIBILITY_HIDDEN = 6;
}

// content type denotes what type of content is being stored
// it can be stories
// it can be youtube video
// tells the client on how it should render the content
// currently only stories is being considered
// example: https://preview.nws.ai/amp/1288508279/fi-2/
enum ContentType {
  CONTENT_TYPE_UNSPECIFIED = 0;
  // story
  CONTENT_TYPE_STORY = 1;
}

// acts as a common identifier between client and server
// denotes a granular section of the screen on the app where media content has to be rendered.
// example: HELP_HOME, CARD_OFFERS, etc
enum UIContext {
  UI_CONTEXT_UNSPECIFIED = 0;
  // to show stories on Help screen
  UI_CONTEXT_HELP_HOME_POPULAR_STORIES = 1;
  // stories on FAQ category screens
  UI_CONTEXT_FAQ_CATEGORY_STORIES = 2;
  // stories on Pay (Transaction receipt) screen
  UI_CONTEXT_TXN_RECEIPT_STORIES = 3;
  // stories on MutualFund Collections screen
  UI_CONTEXT_MUTUAL_FUND_STORIES = 4;
  // stories on US Stocks landing page screen
  UI_CONTEXT_US_STOCKS_LANDING_PAGE_STORIES = 5;
  // stories on Investment Landing page screen
  UI_CONTEXT_INVESTMENT_LANDING_PAGE_STORIES = 6;
  // stories on Mandates Hub screen
  UI_CONTEXT_MANDATES_HUB_STORIES = 7;
}
