// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package faq;

import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/inapphelp/faq/faq.proto";
import "api/frontend/inapphelp/faq/help_context.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/frontend/inapphelp/faq";
option java_package = "com.github.epifi.gamma.api.frontend.inapphelp.faq";

// API's for standard navigation
// API's for specific faq search
// API's for contextual faq search
service FAQ {
  // Does not require mandatory input parameters
  // Returns list of all categories, map of grouped categories and enum that specify whether to display list or grouped categories
  // Both category list and map will be sent in response for backward compatibility, hence client should use version enum to identify what to display
  // Internal server error if backend fails to fetch FAQs
  rpc GetAllCategories (GetAllCategoriesRequest) returns (GetAllCategoriesResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Returns a list of all the folders in the category
  // category id is mandatory for this call
  // INVALID ARGUMENT ERROR if category id is missing
  // INTERNAL SERVER ERROR if backend fails to fetch FAQs or id is invalid
  rpc GetAllFoldersInCategory (GetAllFoldersInCategoryRequest) returns (GetAllFoldersInCategoryResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Returns a list of all the articles in the folder
  // folder id is mandatory for this call
  // INVALID ARGUMENT ERROR if folder id is missing
  // INTERNAL SERVER ERROR if backend fails to fetch FAQs or id is invalid
  rpc GetAllArticlesInFolder (GetAllArticlesInFolderRequest) returns (GetAllArticlesInFolderResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Returns either cateory object or folder object or article object based on which id is passed
  // id is mandatory along with FAQ type
  // INVALID ARGUMENT ERROR if id is missing or type is missing
  // INTERNAL SERVER ERROR if backend fails to fetch FAQs or id is invalid
  rpc GetFAQById (GetFAQByIdRequest) returns (GetFAQByIdResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetFAQByContext returns a list of Faq mapped to the given context
  // Request contains FaqContextType and FaqContextValue both are mandatory parameters
  // Response contains list of Faqs mapped against given context
  // Before using this RPC we must create a mapping of context value to list of Faqs
  // this can be done using CREATE_FAQ_CONTEXT_MAPPING dev action
  rpc GetFAQByContext (GetFAQByContextRequest) returns (GetFAQByContextResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch related articles for any article shown on app
  // client needs to pass article id and get list of all related article's
  // INVALID ARGUMENT ERROR if id is missing is missing
  // INTERNAL SERVER ERROR if backend fails to fetch FAQs or id is invalid
  // Empty list in case no related FAQ's are found in backend
  rpc GetRelatedArticles (GetRelatedArticlesRequest) returns (GetRelatedArticlesResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // API to submit feedback against a faq article
  // actor id, article id and response are mandatory
  // API returns a feedback ID which will be used in further flows to update feedback
  // API also returns bottom sheet with list of options for negative feedback
  rpc SubmitArticleFeedback (SubmitArticleFeedbackRequest) returns (SubmitArticleFeedbackResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // API to update feedback reason against a feedback id
  rpc UpdateFeedbackReason (UpdateFeedbackReasonRequest) returns (UpdateFeedbackReasonResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // rpc to fetch list of popular articles from backend
  // request contains fe req header
  // response contains fe resp header and list of article objects
  // status code:
  // OK if call is successful
  // NotFound if data is not found
  rpc GetPopularFAQList (GetPopularFAQListRequest) returns (GetPopularFAQListResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

// Request proto to get all categories
// Auth header is mandatory parameter for frontend requests
message GetAllCategoriesRequest {
  // auth header to be passed in request by the client
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

// Response proto for all categories
// List of category objects
message GetAllCategoriesResponse {
  rpc.Status status = 1;
  // list of all categories that which will be, always populated to maintain backward compatibility
  repeated Category categories = 2;
  // map where key represents the group and value has list of categories belonging to that group, this will be used by the client in case of new UI
  map<string, CategoryList> grouped_categories = 3;
  // which version is to be displayed grouped one or list one
  CategoryScreenVersion version = 4;
  frontend.header.ResponseHeader resp_header = 15;
}

// Request proto to get all folders in a category
// Auth header is mandatory parameter for frontend requests
// Category id is a mandatory parameter
message GetAllFoldersInCategoryRequest {
  // auth header to be passed in request by the client
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // category id for which the folders are to be fetched
  int64 category_id = 2;
}

// Response proto for get all folders in a category
// returns list of folder objects
message GetAllFoldersInCategoryResponse {
  rpc.Status status = 1;

  repeated Folder folders = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

// Request proto to get all articles in a folder
// Auth header is mandatory parameter for frontend requests
// folder id is mandatory parameter
message GetAllArticlesInFolderRequest {
  // auth header to be passed in request by the client
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // folder id
  int64 folder_id = 2;
}

// response proto for all articles in a folder
// contains a list of article objects
message GetAllArticlesInFolderResponse {
  rpc.Status status = 1;

  repeated Article articles = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

// Request which the client makes when they have the help context with them
// Help context will return the associated category/folder/article along with the type
message GetFAQByContextRequest {
  // auth header to be passed in request by the client
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // [Mandatory] FAQContext contains type of context and its corresponding value
  // both are mandatory
  FAQContext faq_context = 2;
}

// Response when the client specifically passes help context
// Returns type of FAQ : CATEGORY/FOLDER/ARTICLE along with respective object linked to that help context
message GetFAQByContextResponse {
  rpc.Status status = 1;

  // List of Faqs mapped against given context
  // FAQContent will contain faq_type and the actual content
  repeated FAQContent faq_list = 2;

  frontend.header.ResponseHeader resp_header = 15;
}

// Request when the client specifically passes id of either a category, folder or article
// Pass type of FAQ : CATEGORY/FOLDER/ARTICLE along with respective id
message GetFAQByIdRequest {
  // auth header to be passed in request by the client
  // mandatory parameter
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // mandatory parameter
  // This FAQ id can be of category, article or folder
  // The FAQ type will determine what is the response expected
  int64 faq_id = 2;

  // mandatory parameter
  FAQType faq_type = 3;
}

// Response when the client specifically passes id of either a category, folder or article
// Returns type of FAQ : CATEGORY/FOLDER/ARTICLE along with respective object
message GetFAQByIdResponse {
  rpc.Status status = 1;

  FAQType faq_type = 2;

  oneof faq {
    Category category = 3;

    Folder folder = 4;

    Article article = 5;
  }
  frontend.header.ResponseHeader resp_header = 15;
}

message GetRelatedArticlesRequest {
  // auth header to be passed in request by the client
  // mandatory parameter
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // mandatory parameter
  int64 article_id = 2;
}

message GetRelatedArticlesResponse {
  rpc.Status status = 1;

  // list of all related article for the article shown
  repeated faq.Article related_article_list = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message SubmitArticleFeedbackRequest {
  // auth header to be passed in request by the client
  // mandatory parameter
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // Replaced by source_id
  int64 article_id = 2 [deprecated = true];

  // Mandatory
  faq.FeedbackResponse feedback_response = 3;

  // Mandatory
  SourceIdentifier source_id = 4;
}

message SubmitArticleFeedbackResponse {
  rpc.Status status = 1;

  string feedback_id = 2;

  faq.BottomSheet bottom_sheet = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message UpdateFeedbackReasonRequest {
  // auth header to be passed in request by the client
  // mandatory parameter
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // Mandatory
  string feedback_id = 2;

  // Mandatory
  string reason = 3;
}

message UpdateFeedbackReasonResponse {
  rpc.Status status = 1;
  frontend.header.ResponseHeader resp_header = 15;
}

message GetPopularFAQListRequest {
  frontend.header.RequestHeader req = 1;
}

message GetPopularFAQListResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // list of popular articles
  repeated Article article_list = 2;
}

message SourceIdentifier {
  oneof id {
    string issue_id = 1;
    int64 article_id = 2;
  }
}
