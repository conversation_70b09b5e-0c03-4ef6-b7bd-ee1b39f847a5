syntax = "proto3";
package frontend.inapphelp.app_feedback;

import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/inapphelp/app_feedback/app_feedback.proto";
import "api/frontend/inapphelp/app_feedback/enums.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/common/boolean.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/inapphelp/app_feedback";
option java_package = "com.github.epifi.gamma.api.frontend.inapphelp.app_feedback";

service AppFeedback {
  // rpc to check if we should ask for feedback from user in a given app flow
  // we will return flag as true and feedback attempt id if the user is eligible
  // we will return flag as false if user is not eligible
  // will return status
  // OK for success
  // Internal for server errors
  rpc IsEligibleForFeedback(IsEligibleForFeedbackRequest) returns (IsEligibleForFeedbackResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to update attempt if feedback was actually asked to user on the app
  // this rpc is idempotent and multiple calls with same attempt_id will update the same attempt record in db
  // this rpc is needed because there can be cases where user was eligible for feedback but for some reason,
  // we were not able to ask for feedback(user dropped out of flow, app crashed/closed etc)
  // this api call will be an ack from client if the feedback was asked and we will update the details in db accordingly
  // will return status
  // OK for success
  // Internal for server errors
  rpc UpdateFeedbackAttempt(UpdateFeedbackAttemptRequest) returns (UpdateFeedbackAttemptResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to get the questions that needs to be asked on a particular app screen
  // we will return the list of questions depending on the screen
  // will return status
  // OK for success
  // Internal for server errors
  // Already Exits if user has already feedback on the given screen and we don't allow repeated feedback on the screen
  rpc GetFeedbackQuestions(GetFeedbackQuestionsRequest) returns (GetFeedbackQuestionsResponse) {
    option (rpc.auth_required) = true;
    // we are setting device registration option as false because
    // feedback option will be shown in earlier onboarding stages as well
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // rpc to submit and store user feedback in db
  // we will store question and user response to each question and meta information related to the screen
  // will return status
  // OK for success
  // Internal for server errors
  // Already Exits if user has already feedback on the given screen and we don't allow repeated feedback on the screen
  rpc SubmitUserFeedback(SubmitUserFeedbackRequest) returns (SubmitUserFeedbackResponse) {
    option (rpc.auth_required) = true;
    // we are setting device registration option as false because
    // feedback option will be shown in earlier onboarding stages as well
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message IsEligibleForFeedbackRequest {
  frontend.header.RequestHeader req = 1;
  // app flow which user is in currently, for ex: make payment flow, claim reward flow etc
  // conditions for this flow will be checked specifically to figure out if feedback should be asked
  AppFlow app_flow = 2 [(validate.rules).enum = {not_in: [0]}];;
}

message IsEligibleForFeedbackResponse {
  // will return status
  // OK for success
  // Internal for server errors
  frontend.header.ResponseHeader resp_header = 1;

  // will return true if ratings feedback should be asked to user
  // will return false if feedback can't be asked due to cool off or conditions not met or tries exhausted etc.
  api.typesv2.common.BooleanEnum can_ask_feedback = 2;
  // unique id for each eligible feedback attempt
  // client need to pass this id back to backend when updating attempt status in case feedback was actually asked to user
  string feedback_attempt_id = 3;
  // delay in seconds after which the user will be nudged for app review on a screen
  uint32 nudge_delay_in_seconds = 4;
}

message UpdateFeedbackAttemptRequest {
  frontend.header.RequestHeader req = 1;
  // attmept id for the feedback
  // this will be returned as part of IsEligibleForFeedback rpc response if user is eligible for feedback
  string feedback_attempt_id = 2[(validate.rules).string.min_len = 1];

  // flag to indicate whether the feedback was actually asked on the app
  api.typesv2.common.BooleanEnum feedback_asked = 3[(validate.rules).enum = {not_in: [0]}];
}

message UpdateFeedbackAttemptResponse {
  // will return status
  // OK for success
  // Internal for server errors
  // NotFound if attempt_id is not found
  frontend.header.ResponseHeader resp_header = 1;
}

message GetFeedbackQuestionsRequest {
  frontend.header.RequestHeader req = 1;
  // app screen on which feedback needs to be asked
  // AppScreen enum defined in api/inapphelp/in_app_feedback.proto should be passed as string here
  // not enforcing the enum type for field to avoid need for client and frontend server updates every time new app screen is added
  string app_screen = 2[deprecated = true];
  // unique identifier for a specific feedback flow
  // Questions to be asked in the feedback flow will be linked to this identifier
  FeedbackSurveyFlow feedback_survey_flow = 3;
  // unique identification and mapping of individual client side requests.
  string client_request_id = 4;
}

message GetFeedbackQuestionsResponse {
  // will return status
  // OK for success
  // Internal for server errors
  // Already Exits if user has already feedback on the given screen and we don't allow repeated feedback on the screen
  frontend.header.ResponseHeader resp_header = 1;
  // list of questions and information related to the question
  // this list is dependent on the app screen passed in request
  repeated QuestionInfo questions = 2;
}

message SubmitUserFeedbackRequest {
  frontend.header.RequestHeader req = 1;
  // app screen on which feedback was asked
  // AppScreen enum defined in api/inapphelp/in_app_feedback.proto should be passed as string here
  // not enforcing the enum type for field to avoid need for client and frontend server updates every time new app screen is added
  string app_screen = 2[deprecated = true];
  // feedback response will contain details of all the questions asked and answers to those questions
  FeedbackResponse feedback_response = 3;
  // key value pair of some meta information for the current screen
  // For ex. for screener terminal stage we can add current stage, failure reason etc.
  map<string, string> screen_meta = 4;
  // unique identifier for a specific feedback flow
  // questions to be asked in the feedback flow will be linked to this idnetifier
  FeedbackSurveyFlow feedback_survey_flow = 5;
  // reference id will be used for linking the feedback with specific ids in different services
  // EX: could be insight id for insights flow to link feedback to a particular type of insight
  // we are not enforcing any constraints on the reference id, will just support filtering on it
  // this fields is critical for analytics hence recommended to passed in all the flows
  string reference_id = 6;
  // client request id, is generated by client for unique identifier
  string client_request_id = 7;
}

message SubmitUserFeedbackResponse {
  // will return status
  // OK for success
  // Internal for server errors
  // Already Exits if user has already feedback on the given screen and we don't allow repeated feedback on the screen
  frontend.header.ResponseHeader resp_header = 1;
}
