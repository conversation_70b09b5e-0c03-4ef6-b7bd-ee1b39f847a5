// Frontend definition for timeline
syntax = "proto3";

package frontend.timeline;

import "api/frontend/timeline/timeline_action.proto";
import "api/frontend/timeline/enums/enums.proto";
import "api/typesv2/common/phone_number.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/frontend/deeplink/deeplink.proto";

option go_package = "github.com/epifi/gamma/api/frontend/timeline";
option java_package = "com.github.epifi.gamma.api.frontend.timeline";

// A timeline is a relationship between 2 actors,
// who share a time-ordered series of events.
//
// Since this is a FE timeline proto,
// one of the actor is the currently authenticated actor
message Timeline {
  // Unique identifier for Timeline
  string id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  Chathead chathead = 2;

  // Description to show in header, just below the name in the timeline UI
  // This will be the other actor's mobile number if the other actor is internal,
  // else it'll contain the PI details of the other actor.
  // If current actor doesn't know of any PI for the other actor,
  // then this will be empty
  string description = 3;

  // Whether pay button is enabled or not
  // This will be enabled only if we have at least 1 PI of payee
  // DEPRECATED - Use timeline_option to specify what all options to show
  bool is_pay_button_enabled = 4 [deprecated = true];

  // Whether the request button is enabled or not
  // This will be enabled only if we have at least 1 PI of type UPI of payee
  // DEPRECATED - Use timeline_option to specify what all options to show
  bool is_request_button_enabled = 5 [deprecated = true];

  // options to show to user on timeline e.g. pay, request, self transfer
  repeated frontend.timeline.enums.TimelineOption timeline_options = 8;

  // Represents the timestamp of latest message belonging to this timeline
  // Set to current timestamp when timeline is created
  google.protobuf.Timestamp last_event_updated_at = 6;

  // state of the timeline w.r.t to an actor
  // the UI on timeline changes based on different states of the timeline
  TimelineState timeline_state = 7;

  // First time payment confirmation prompt
  PaymentPrompt payment_prompt = 9;

}

// Payment prompts to be shown in enter amount screen via Timeline request or Screen options for the same
// Figma : https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=17669-104137&mode=design&t=oDmgD9D1inMRVd0o-0
message PaymentPrompt {

  // title for the prompt
  api.typesv2.common.Text title = 1;

  // description for the prompt
  api.typesv2.common.Text description = 2;

  // payment details metadata to be displayed in the prompt
  // may contain details of the bank/upi account
  PaymentDetailMetadata payment_details_metadata = 3;

  // list of CTAs to be displayed to further take actions
  repeated frontend.deeplink.Cta details_cta = 4;

}


message PaymentDetailMetadata {

  // background color
  string bgColor = 1;

  // visual element icon, using for bank/app etc icons to be shown
  api.typesv2.common.VisualElement icon = 2;

  // could be user-name/bank-name etc depending on the intent of the flow
  repeated PaymentDetails payment_details = 3;

}

message PaymentDetails {

  // user interpretable title
  // Fi Mandate Number, Unique Mandate Reference Number etc
  api.typesv2.common.Text title = 1;

  // actual value w.r.t title
  api.typesv2.ui.IconTextComponent value = 2;

}

// Chathead contains a subset of Timeline details,
// and only contains the required data to show a chathead
message Chathead {
  // Unique identifier for Timeline
  string timeline_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // The other actor forming a timeline
  string second_actor_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // Name of the other actor
  string second_actor_name = 3;

  // Profile image of the other actor.
  // The value will be populated only if the other actor is internal.
  // If the value is not present the client needs to generate the image
  // using name initials with color_code as background
  string second_actor_profile_url = 4;

  // Color code of the chat head
  string color_code = 5;

  // Represents the timestamp of latest message belonging to this timeline/chathead
  // Set to current timestamp when timeline/chathead is created
  google.protobuf.Timestamp last_event_updated_at = 6;

  // phone of the other actor involved in the timeline
  // optional field, will be present depending on the other actor discoverability
  api.typesv2.common.PhoneNumber phone_number = 7 [deprecated = true];

  // hashed phone number of the other actor involved in the timeline/chat head
  // optional field: will be present only if the other actor belongs to fi
  string hashed_phone_number = 8;
}

// A timeline card carries a special message for the user on timeline
// The user must take one of specified TimelineAction in order to proceed/perform
// any action on the timeline.
// Some of the use case are, but not limited to-
// 1) blocked actor on the timeline
// 2) special promotional messages on the timeline
// 3) payment reminders on the timeline
message TimelineCard {
  // short description of timeline card
  string title = 1;

  // human readable description of timeline card
  string description = 2;

  // list of actions that can be taken on the timeline card
  repeated frontend.timeline.TimelineAction timeline_actions = 3;

  // url of the image to be displayed on the timeline card
  string image_url = 4;
}

// state of the timeline w.r.t to an actor
// the UI on timeline changes based on different states of the timeline
enum TimelineState {
  TIMELINE_STATE_UNSPECIFIED = 0;
  // the default timeline state for actor
  // i.e., All the features of a timeline are accessible
  DEFAULT = 1;
  // second actor is blocked by the current actor
  BLOCKED = 2;
  // second actor is blocked and reported by the current actor
  REPORTED = 3;
  // actor received first collect request from the second actor.
  // The client is supposed to show an alert pop-up to the actor in this state
  FIRST_COLLECT = 4;
  // actor received first collect request from the second unknown actor.
  // An actor is unknown if there is no transaction history shared among two users.
  // The client is supposed to show an alert pop-up to the actor in this state
  FIRST_COLLECT_WITH_NO_HISTORY = 5;
}


// ImmutableShadowedFields represents the fields that will not be changed in an rpc lifecycle
// Immutable: it will not be altered/changed
// Shadowed: same object will be passed as it is in the response
// It will be taken in the request and as it is will be passed in the response
// for e.g.:This is required to carry forward response from one rpc to another. Why? client can also handle the same but it has chances of dropping off of the data since multiple screens are involved in the same flow
// todo(Harleen Singh): evaluate using oneOf if the request gets bloated
message ImmutableShadowedFields {
  string payee_pi_id = 1;
}
