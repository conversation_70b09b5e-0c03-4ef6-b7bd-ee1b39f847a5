syntax = "proto3";

package frontend.timeline.enums;

option go_package = "github.com/epifi/gamma/api/frontend/timeline/enums";
option java_package = "com.github.epifi.gamma.api.frontend.timeline.enums";

// source of timeline
enum TimelineSource {
  // unspecified
  TIMELINE_SOURCE_UNSPECIFIED = 0;

  // timeline is resolved using vpa
  VPA = 1;

  // timeline is resolved using account number
  ACCOUNT_NUMBER = 2;

  // timeline is resolved using phone number
  PHONE_NUMBER = 3;
  // timeline is resolved using actor id
  ACTOR_ID = 4;
}

enum TimelineOption {
  // unspecified
  TIMELINE_OPTION_UNSPECIFIED = 0;
  // option to pay to other actor
  TIMELINE_OPTION_PAY = 1;
  // option to request payment to other actor
  TIMELINE_OPTION_REQUEST = 2;
  // option to transfer money from one account to another
  TIMELINE_OPTION_SELF_TRANSFER = 3;
}
