// protolint:disable MAX_LINE_LENGTH

// Frontend definition a timeline event
syntax = "proto3";

package frontend.timeline;

import "api/frontend/pay/order_event.proto";
import "api/frontend/timeline/timeline_action.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/frontend/timeline";
option java_package = "com.github.epifi.gamma.api.frontend.timeline";

// A timeline event represents an event between two actors.
// Any form of interaction between the actors can be defined as a timeline event.
// Events can be broadly categorised as-
// 1. Events involving both actors of the timeline, these events are visible to both actors in a timeline.
// 2. Events that doesn't involve both actors. e.g. REMINDERS, REWARDS, etc. These events are not visible to both actors.
message TimelineEvent {
  // Unique id representing an unique event in the system.
  // event_id can have different meaning in the system based on event_type.
  // e.g. for an event of type ORDER event_id represents an order id.
  string event_id = 1;

  // Signifies the role of an actor in the event.
  EventRole role = 2;

  // timestamp of the execution of the event.
  // for event of type ORDER event_timestamp represents the time when the money got deducted from payer account.
  google.protobuf.Timestamp event_timestamp = 3;

  // String to be used to display an event information on the timeline.
  // This string contains HTML tags so that client can render the texts for the display purpose.
  string formatted_description = 4;

  TimelineEventType event_type = 5;

  // based on the event type one of the payload structure will be populated.
  oneof payload{
    frontend.pay.OrderEvent order_event = 6;
  }

  // display timestamp to be shown as it is to user
  // by the client
  string event_timestamp_display = 7;

  // all the field values from 6-19 are reserved for forward compatibility of different timeline events
  // payload.
  // a list of timeline event actions that a user can take on a given timeline.
  // in case a timeline event is non actionable then empty list is returned
  repeated frontend.timeline.TimelineAction actions = 20;

  // contains information to be displayed below a timeline event
  // its an optional field and will be populated based on various parameters
  // governed by backend service's business logic.
  // e.g. for a collect request event backend returns the expected expiry time of the collect request
  //
  // Note: the string will contain emoji unicode
  string bottom_line = 21;

  // next action for the user when user click on an timeline event
  // if no next action is passed we will redirect user to order receipt as per the current timeline event handling
  frontend.deeplink.Deeplink next_action = 22;

  // background for the timeline event
  string background_colour = 23;

  // icon url for showing if its a debit/credit txn
  string icon_url = 24;

  // text colour for the timeline event
  string text_colour = 25;

  // stroke colour for the timeline event
  // outline for each event
  string stroke_colour = 26;

  // additional actions to be displayed outside the timeline event bubble
  // Used for deeplink driven CTAs such as "Why did I get charged?" and "Watch a video"
  // https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=17716%3A105826
  repeated api.typesv2.ui.IconTextComponent additional_actions = 27;
}

// A timeline event can be of different types in the system, based on various possible ways an actor can interact with
// the other actor.
// Some of them are- MESSAGE, ORDER, COLLECT_REQUEST, REMINDER, etc.
// To begin with we will only have ORDER events in a timeline.
enum TimelineEventType {
  TIMELINE_EVENT_TYPE_UNSPECIFIED = 0;

  // An order is a workflow of financial exchanges between two actors which results in a transaction.
  ORDER = 1;

  // Timeline events for credit card
  CREDIT_CARD = 2;
}

// Defines role of an actor in timeline event.
// Events can be broadly categorised as-
// 1. Events involving both actors of the timeline, where an actor can be either SENDER or RECEIVER.
// 2. Events that doesn't involve both actors. e.g. REMINDERS, REWARDS, etc. In this case role is marked
// as EVENT_ROLE_UNSPECIFIED
enum EventRole {
  EVENT_ROLE_UNSPECIFIED = 0;

  // An actor who initiates the event
  SENDER = 1;

  // An actor who is recipient.
  RECEIVER = 2;
}

// A group of timeline events to be shown together
//  on the client
// the timeline events may be grouped
// based on a common aspect e.g., creation day of the events
message TimelineEventGroup {
  // title of the timeline group
  // eg. 'APR 24'
  string title = 1;
  // list of timeline events that belong to the same group
  // for eg. all the events for 'APR 24' will belong to a single group
  repeated TimelineEvent timeline_events = 2;
}
