syntax = "proto3";

package api.frontend.salaryestimation;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/salaryestimation/source_flow_params.proto";


option go_package = "github.com/epifi/gamma/api/frontend/salaryestimation";
option java_package = "com.github.epifi.gamma.api.frontend.salaryestimation";

service SalaryEstimation {
  rpc RecordConsents (RecordConsentsRequest) returns (RecordConsentsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  rpc ComputeSalary (ComputeSalaryRequest) returns (ComputeSalaryResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // user action to cancel any salary estimation attempt flow
  rpc CancelAttempt (CancelAttemptRequest) returns (CancelAttemptResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };
}

message RecordConsentsRequest {
  .frontend.header.RequestHeader req = 1;

  repeated string consent_types = 2;
}

message RecordConsentsResponse {
  .frontend.header.ResponseHeader resp_header = 1;

  repeated string consent_ids = 2;
}

message ComputeSalaryRequest {
  .frontend.header.RequestHeader req = 1;

  // Stringified form of Client enum in api.salaryestimation, to avoid client changes
  // To be sent unmodified as received by client in deeplink params
  string client = 2;

  string client_req_id = 3;

  string source = 4;

  typesv2.salaryestimation.SourceFlowParams source_flow_params = 5;
}

message ComputeSalaryResponse {
  .frontend.header.ResponseHeader resp_header = 1;

  .frontend.deeplink.Deeplink next_action = 2;
}

message CancelAttemptRequest {
  .frontend.header.RequestHeader req = 1;
  string client_req_id = 2;
}

message CancelAttemptResponse {
  .frontend.header.ResponseHeader resp_header = 1;
  .frontend.deeplink.Deeplink next_action = 2;
}
