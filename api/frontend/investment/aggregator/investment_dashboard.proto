syntax = "proto3";

package frontend.investment.aggregator;

import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/widget_themes.proto";
import "api/frontend/investment/aggregator/investment_summary_component.proto";

option go_package = "github.com/epifi/gamma/api/frontend/investment/aggregator";
option java_package = "com.github.epifi.gamma.api.frontend.investment.aggregator";

/*
  Figma: https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=7920-16589&mode=dev
  Only shown when the user has made some investment.
  Responsible showing the overall investment summary of the user
  Client to render the component with the content present no additional rpc call
*/
message InvestmentDashboard {
  // Investments
  api.typesv2.common.Text title = 1;
  // ₹2,45,051.05
  api.typesv2.ui.IconTextComponent invested_amount = 2;
  /*
    Comprises of
    - Invested summary - "INVESTED 50000"
    - Separator - "|"
    - Return Summary - "RETURNS ₹62,205 3.2%"
    - <toggle icon> [Optional]
   */
  InvestmentSummaryBody body = 3;

  // background color for component
  string bg_color = 6;
  // shadow for component
  repeated api.typesv2.ui.Shadow shadows = 7;
  //  TODO: Not planning to implement in P0, remove todo after implementation is done

  // default selected type of the investment source
  InvestmentSource default = 8;
  // since this is a toggle only 2 is allowed.
  repeated InvestmentSourceToggle investment_source_values = 9;
}

message InvestmentSourceToggle {
  // eg. Fi, All
  api.typesv2.common.Text title = 1;
  InvestmentSource source_type = 2;
}

enum InvestmentSource {
  INVESTMENT_SOURCE_FI = 0;
  INVESTMENT_SOURCE_ALL = 1;
}
