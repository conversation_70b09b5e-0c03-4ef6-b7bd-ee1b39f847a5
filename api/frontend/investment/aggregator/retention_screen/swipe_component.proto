syntax = "proto3";

package frontend.investment.aggregator.retention_screen;

import "api/typesv2/common/text.proto";
import "api/typesv2/ui/swipe_button.proto";

option go_package = "github.com/epifi/gamma/api/frontend/investment/aggregator/retention_screen";
option java_package = "com.github.epifi.gamma.api.frontend.investment.aggregator.retention_screen";

message SwipeComponent {
  // For eg: Amount will be credited to your Federal Bank a/c
  api.typesv2.common.Text display_text = 1;
  api.typesv2.ui.SwipeButton swipe_button = 2;
}

