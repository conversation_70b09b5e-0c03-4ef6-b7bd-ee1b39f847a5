syntax = "proto3";

package frontend.investment.aggregator.retention_screen;

import "api/typesv2/common/text.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/widget_themes.proto";
import "api/frontend/investment/aggregator/retention_screen/swipe_component.proto";

option go_package = "github.com/epifi/gamma/api/frontend/investment/aggregator/retention_screen";
option java_package = "com.github.epifi.gamma.api.frontend.investment.aggregator.retention_screen";

// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-48088&mode=design&t=FfLeU9YHbe1R5ek9-0
// Screenshot: https://drive.google.com/file/d/1jOkMC3Wzq5HGmAcFbKGNy3owiO48tEEA/view
message ClosureChoicesScreen {
  api.typesv2.common.VisualElement title_visual_element = 1;
  // For eg: Close Emergency deposit?
  api.typesv2.common.Text title = 2;
  // For eg: Before you go... Here are the benefits your deposit comes with
  api.typesv2.common.Text subtitle = 3;

  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-48088&mode=design&t=FfLeU9YHbe1R5ek9-0
  repeated ClosureChoiceComponent choice_components = 4;
  SwipeComponent swipe_component = 5;
}

// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-48088&mode=design&t=FfLeU9YHbe1R5ek9-0
// ScreenShot: https://drive.google.com/file/d/1jOkMC3Wzq5HGmAcFbKGNy3owiO48tEEA/view
message ClosureChoiceComponent {
  // For eg: Close now & get interest worth
  api.typesv2.common.Text header = 1;
  // For eg: ₹320 at 4.2% 3.2% p.a
  api.typesv2.ui.IconTextComponent body = 2;
  // 1% of the interest will be deducted as a pre-closure penalty
  api.typesv2.ui.IconTextComponent footer = 3;
  // deeplink to which user should be redirected to on selection this choice
  frontend.deeplink.Deeplink navigation_deeplink = 4;
  api.typesv2.common.VisualElement next_icon = 5;
  // background color for the choice
  string bg_color = 6;
  // background shadow of the nudge card
  repeated api.typesv2.ui.Shadow shadows = 7;
}
