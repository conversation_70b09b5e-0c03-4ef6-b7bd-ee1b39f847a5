syntax = "proto3";

package frontend.investment.aggregator.retention_screen;

import "api/frontend/deeplink/deeplink.proto";

import "api/frontend/investment/aggregator/retention_screen/closure_choice_screen.proto";
import "api/frontend/investment/aggregator/retention_screen/closure_summary_screen.proto";
import "api/frontend/investment/aggregator/retention_screen/retention_benefits_screen.proto";
import "api/frontend/investment/aggregator/retention_screen/retention_exit_screen.proto";
import "api/frontend/investment/aggregator/retention_screen/retention_rewards_and_offers_screen.proto";

option go_package = "github.com/epifi/gamma/api/frontend/investment/aggregator/retention_screen";
option java_package = "com.github.epifi.gamma.api.frontend.investment.aggregator.retention_screen";

message RetentionScreenParams {
  // InstrumentData represents meta data for a specific instrument. This is needed by BE to identify parameters related to an instrument.
  InstrumentData instrument_data = 1;

  // list of components already viewed by the user. when ever user visits a screen, BE will append the InvestmentRetentionScreen to this array
  // this helps in giving previous/back button feature.
  repeated RetentionScreen visited_screens = 2;
}

// InstrumentData represents meta data for a specific instrument. This is needed by BE to identify parameters related to
// an instrument.
message InstrumentData {
  oneof Data {
    FixedDepositInstrumentData fixed_deposit_instrument_data = 1;
    SmartDepositInstrumentData smart_deposit_instrument_data = 2;
  }
}

// InstrumentData represents meta data for smart deposits.
message SmartDepositInstrumentData {
  string deposit_account_id = 1;
}

// InstrumentData represents meta data for fixed deposits.
message FixedDepositInstrumentData {
  string deposit_account_id = 1;
}

// InvestmentRetentionScreen is an enum that represents any screen that come up in investment retention flow.
// If custom screens needs to be supported for an instrument, prefix it with the instrument name.
// For eg: if we need to build a custom screen that is only applicable to fixed deposit, then enum name should be InvestmentRetentionScreen_FIXED_DEPOSIT_CLOSURE_CHOICES_SCREEN
enum RetentionScreen {
  RetentionScreen_UNSPECIFIED = 0;
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-47466&mode=design&t=FfLeU9YHbe1R5ek9-0
  // ScreenShot: https://drive.google.com/file/d/1jOkMC3Wzq5HGmAcFbKGNy3owiO48tEEA/view
  RetentionScreen_CLOSURE_CHOICES_SCREEN = 1;
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-46530&mode=design&t=FfLeU9YHbe1R5ek9-0
  // ScreenShot: https://drive.google.com/file/d/1VLKWn2vqy9fUWGW4zUkv1Rex9579VwpO/view
  RetentionScreen_CLOSURE_SUMMARY_SCREEN = 2;
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-47507&mode=design&t=FfLeU9YHbe1R5ek9-0
  // ScreenShot: https://drive.google.com/file/d/1VLKWn2vqy9fUWGW4zUkv1Rex9579VwpO/view
  RetentionScreen_Retention_BENEFITS_SCREEN = 3;
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14276-48654&mode=design&t=FfLeU9YHbe1R5ek9-0
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14276-48624&mode=design&t=FfLeU9YHbe1R5ek9-0
  // ScreenShot: https://drive.google.com/drive/folders/1E8QZkJS93LXYvMEGt98Mqmj44H3zkUPc
  RetentionScreen_Retention_EXIT_SCREEN = 4;
  // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14276-48705&mode=design&t=FfLeU9YHbe1R5ek9-0
  // ScreenShot: https://drive.google.com/file/d/13NYt6iOjLs3A1vD8WS7T17DSZDiWSir1/view
  RetentionScreen_Retention_REWARDS_AND_OFFERS_SCREEN = 5;
}

message RetentionScreenDisplayData {
  // deeplink that represents the back button
  deeplink.Deeplink back_button = 1;
  oneof RetentionScreen {
    // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-47466&mode=design&t=FfLeU9YHbe1R5ek9-0
    // ScreenShot: https://drive.google.com/file/d/1jOkMC3Wzq5HGmAcFbKGNy3owiO48tEEA/view
    ClosureChoicesScreen closure_choices_screen = 2;
    // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-46530&mode=design&t=FfLeU9YHbe1R5ek9-0
    // ScreenShot: https://drive.google.com/file/d/1VLKWn2vqy9fUWGW4zUkv1Rex9579VwpO/view
    ClosureSummaryScreen closure_summary_screen = 3;
    // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-47507&mode=design&t=FfLeU9YHbe1R5ek9-0
    // https://drive.google.com/drive/folders/1E8QZkJS93LXYvMEGt98Mqmj44H3zkUPc
    RetentionBenefitsScreen retention_benefits_screen = 4;
    // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14276-48654&mode=design&t=FfLeU9YHbe1R5ek9-0
    // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14276-48624&mode=design&t=FfLeU9YHbe1R5ek9-0
    // ScreenShot: https://drive.google.com/file/d/1UybelXZUiudDGcI09UlzGelGGqsPQnOe/view
    RetentionExitScreen retention_exit_screen = 5;
    // https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14276-48705&mode=design&t=FfLeU9YHbe1R5ek9-0
    // ScreenShot: https://drive.google.com/file/d/13NYt6iOjLs3A1vD8WS7T17DSZDiWSir1/view
    RetentionRewardsAndOffersScreen retention_rewards_and_offers_screen = 6;
  }
}

