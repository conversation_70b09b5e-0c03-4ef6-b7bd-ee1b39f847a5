syntax = "proto3";

package frontend.investment.aggregator.retention_screen;

import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/frontend/investment/aggregator/retention_screen/swipe_component.proto";

option go_package = "github.com/epifi/gamma/api/frontend/investment/aggregator/retention_screen";
option java_package = "com.github.epifi.gamma.api.frontend.investment.aggregator.retention_screen";

// https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?type=design&node-id=14250-47507&mode=design&t=FfLeU9YHbe1R5ek9-0
// ScreenShot: https://drive.google.com/drive/folders/1E8QZkJS93LXYvMEGt98Mqmj44H3zkUPc
message RetentionBenefitsScreen {
  api.typesv2.common.VisualElement title_visual_element = 1;
  // For eg: Close Emergency deposit?
  api.typesv2.common.Text title = 2;
  // For eg: Before you go... Here are the benefits your deposit comes with
  api.typesv2.common.Text subtitle = 3;

  repeated api.typesv2.ui.IconTextComponent benefits = 4;
  SwipeComponent swipe_component = 5;
}
