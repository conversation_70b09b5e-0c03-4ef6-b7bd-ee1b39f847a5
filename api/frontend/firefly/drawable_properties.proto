syntax = "proto3";

package frontend.firefly;

import "api/typesv2/common/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/frontend/firefly";
option java_package = "com.github.epifi.gamma.api.frontend.firefly";


message DrawableProperties {
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
  BorderProperty border_property = 2;
  CornerProperty corner_property = 3;
  api.typesv2.common.ui.widget.Shadow shadow = 4;
}

message BorderProperty {
  int32 border_thickness = 1;
  string border_color = 2;
}

message CornerProperty {
  int32 top_start_corner_radius = 1;
  int32 top_end_corner_radius = 2;
  int32 bottom_start_corner = 3;
  int32 bottom_end_corner = 4;
}
