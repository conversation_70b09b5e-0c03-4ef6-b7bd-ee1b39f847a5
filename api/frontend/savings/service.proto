// protolint:disable MAX_LINE_LENGTH

// Frontend RPC for operations involving operations on a savings account.

syntax = "proto3";

package frontend.savings;

import "api/accounts/account_type.proto";
import "api/frontend/card/card.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/account/enums.proto";
import "api/typesv2/common/phone_number.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/money.proto";

option go_package = "github.com/epifi/gamma/api/frontend/savings";
option java_package = "com.github.epifi.gamma.api.frontend.savings";

// Frontend RPC service associated with operations on a savings account including provisioning.
service Savings {
  // Get account details aggregating response from savings and payment instrument.
  rpc GetAccountDetails (GetAccountDetailsRequest) returns (GetAccountDetailsResponse) {
    option (rpc.auth_required) = true;
  };

  // Get balance tracker view based on entry point of user.
  // Figma: https://www.figma.com/file/mOAgJBEZ0dXhwJBR9HydJu/Account-Balances-%7C-Workfile?node-id=1512%3A39000&t=wRCTeaOXfjBsN5Hl-1
  rpc GetBalanceTrackerView (GetBalanceTrackerViewRequest) returns (GetBalanceTrackerViewResponse) {
    option (rpc.auth_required) = true;
  };

  // RPC to get savings account balance of the user based on APO and partner bank.
  rpc GetAccountBalance (GetAccountBalanceRequest) returns (GetAccountBalanceResponse) {
    option (rpc.auth_required) = true;
  };

  rpc SavingsAccountNomineeRedirectionInfo (SavingsAccountNomineeRedirectionInfoRequest) returns (SavingsAccountNomineeRedirectionInfoResponse) {
    option (rpc.auth_required) = true;
  };
}

message SavingsAccountNomineeRedirectionInfoRequest {
  // request header for the request
  frontend.header.RequestHeader req = 1;
}

message SavingsAccountNomineeRedirectionInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Deeplink for transferring the user to w.r.t. the EKYC Data
  frontend.deeplink.Deeplink deeplink = 2;
}

message GetAccountDetailsRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // Identifier of the account for which the request is initiated
  string account_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];

}

message GetAccountDetailsResponse {
  enum Status {
    // Returned an success
    OK = 0;
    // Requested entity not found i.e. no such account exist
    NOT_FOUND = 5;
    // The actor does not have permission to access given account
    PERMISSION_DENIED = 7;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // Status of the request
  rpc.Status status = 1;

  // Account holder name
  string account_holder_name = 2;

  // External account number that's visible to the user.
  string account_no = 3;

  // Account type
  accounts.Type account_type = 4 [(validate.rules).enum = {not_in: [0]}];

  // IFSC code corresponding to the bank
  string ifsc_code = 5;

  // Phone number associated with a savings account
  api.typesv2.common.PhoneNumber phone_number = 6;

  // Email id associated with a savings account
  string email_id = 7;

  // Timestamp of the moment account was created
  google.protobuf.Timestamp created_at = 8;

  // Timestamp of the moment account was last updated
  google.protobuf.Timestamp updated_at = 9;

  // upi id
  repeated string upi_id = 10;

  // Card id
  card.BasicCardInfo card = 11;

  // Current address of the user
  string current_address = 12;

  //is primary account will be true if this account is primary account for payment
  bool is_primary_account = 13;

  // associated bank name for the account
  string bank_name = 14;

  // bank logo url for account
  string bank_logo_url = 15;
  frontend.header.ResponseHeader resp_header = 16;
}

// Request to get the balance tracker view based on user's entry point
message GetBalanceTrackerViewRequest {
  // auth header
  frontend.header.RequestHeader req = 1;
  // UI entry point
  BalanceUIEntryPoint entry_point = 2;
}

message GetBalanceTrackerViewResponse {
  // Title
  api.typesv2.common.Text title = 1;
  // Message Banner
  MessageBanner message_banner = 2;
  // Calendar view
  CalendarView calendar_view = 3;
  // Ledger
  CalendarLedger ledger = 4;
  // cta
  frontend.deeplink.Cta cta = 5;
  // response header
  frontend.header.ResponseHeader resp_header = 10;
}

// Calendar colour mapping
message CalendarLedger {
  map<string, string> colour_to_definition = 1;
}

message CalendarView {
  repeated CalendarViewByMonth calendar_view_by_month = 1;
  // Colour mapping
  // Key values of the map with be one of date_colour_type
  map<string, string> colour_mapping = 3;
}

message CalendarViewByMonth {
  // User will be shown their balance maintained in the last 30 days which can be upto 3 months.
  api.typesv2.common.Text month_title = 1;
  repeated DateSegment date_segments = 2;
}

message MessageBanner {
  // image to be shown along with text in the banner
  api.typesv2.common.Image image = 1;
  // message to be shown in the banner above calendar view
  api.typesv2.common.Text msg = 2;
  // bg colour
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 3;
}

enum BalanceUIEntryPoint {
  BALANCE_UI_ENTRY_POINT_UNSPECIFIED = 0;
  // When user is accessing the balance tracker fi savings landing screen
  BALANCE_UI_ENTRY_POINT_SAV_LANDING = 1;
  // When the user is accessing the tracker from lucky draw screen
  BALANCE_UI_ENTRY_POINT_LUCKY_DRAW = 2;
}

// Representation of a collection of dates together.
// Why a single protos for multiple dates ?
// In a calendar view its possible that a set of adjacent dates have a same ui properties.
// This way we can reduce the object size sent to the client
message DateSegment {
  // start date of the month with a single colour
  int32 start_count = 1;
  // end date of the month with a single colour
  int32 end_count = 2;
  enum DateColourType {
    DATE_COLOUR_TYPE_UNSPECIFIED = 0;
    // Colour type 1
    DATE_COLOUR_TYPE_ABOVE_THRESHOLD = 1;
    // Colour type 2
    DATE_COLOUR_TYPE_UNDER_THRESHOLD = 2;
    // Colour type 3
    DATE_COLOUR_TYPE_DATE_AHEAD = 3;
    // Colour type 4
    DATE_COLOUR_TYPE_NO_OP = 4;
  }
  DateColourType date_colour_type = 3;
  // Whether to display the count on the date
  bool display_count = 4;
  // date colour
  string date_colour = 5;
}

message GetAccountBalanceRequest {
  frontend.header.RequestHeader req = 1;
  // Request param to control which account (Regular, NRE or NRO) balance to return
  api.typesv2.account.AccountProductOffering account_product_offering = 2;
  // Request param to control which Partner Bank account to return
  vendorgateway.Vendor partner_bank = 3;
}

message GetAccountBalanceResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // available balance of the account
  api.typesv2.Money available_balance = 2;
}
