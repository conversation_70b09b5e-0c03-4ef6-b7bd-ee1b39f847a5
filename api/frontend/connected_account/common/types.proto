// All protos which are of primitive data type and generic across frontend/connected_account will reside here.
// Note: To avoid cyclic dependency, this file should not contain custom use-case specific protos and should import
// files from `types` package ONLY.

syntax = "proto3";

package frontend.connected_account.common;

option go_package = "github.com/epifi/gamma/api/frontend/connected_account/common";
option java_package = "com.github.epifi.gamma.api.frontend.connected_account.common";

// TextDisplayInfo represents contains title and description displayed to the user.
// used primitive data type(string) for title and description as this entity is going to be used within AA SDK
message TextDisplayInfo {
  string title = 1;
  string description = 2;
}
