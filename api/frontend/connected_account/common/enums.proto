// All enums related to frontend connectedaccount service.
// Note: To avoid cyclic dependency, this file should not contain custom use-case specific protos and should import
// files from `types` package ONLY.

syntax = "proto3";

package frontend.connected_account.common;

option go_package = "github.com/epifi/gamma/api/frontend/connected_account/common";
option java_package = "com.github.epifi.gamma.api.frontend.connected_account.common";

// Status of connected account of a user
enum AccountStatus {
  ACCOUNT_STATUS_UNSPECIFIED = 0;
  // Account has atleast once active consent and data pull from AA is happening periodically
  ACCOUNT_STATUS_DATA_SYNC_ON = 1;
  // Account has atleast one consent in paused state and no consent in active state. Data pull can be resumed by just
  // resuming the paused consent
  ACCOUNT_STATUS_DATA_SYNC_OFF_PAUSED = 2;
  // Account has data sync turned off due to consent expiration or in short no active/paused consent for that account
  ACCOUNT_STATUS_DATA_SYNC_OFF_STOPPED = 3;
  // Account is disconnected by the user ie all the consents for this account are in revoked state
  ACCOUNT_STATUS_DISCONNECTED = 4;
  // Account was deleted by the user
  ACCOUNT_STATUS_DELETED = 5;
  // Account's data sync is pending and hence some account specific information will not be present
  ACCOUNT_STATUS_DATA_SYNC_PENDING = 6;
}

// Account controls to denote whether a particular feature is enabled for an account
enum AccountControls {
  ACCOUNT_CONTROLS_UNSPECIFIED = 0;
  // transactions enabled for account
  ACCOUNT_CONTROLS_TRANSACTIONS_ENABLED = 1;
  // balance enabled for account
  ACCOUNT_CONTROLS_BALANCE_ENABLED = 2;
}

// The status of consent handle artifact, these statuses are received from AA Consent status API '/Consent/handle/{consentHandle}'.
enum ConsentHandleStatus {
  CONSENT_HANDLE_STATUS_UNSPECIFIED = 0;
  //  consent handle status is received as ready from AA ready once consent is active and ready to use.
  CONSENT_HANDLE_STATUS_READY = 1;
  //  consent handle status is received as failed from AA, can be due to failure received from bank or some issue at AA end.
  CONSENT_HANDLE_STATUS_FAILED = 2;
  //  consent handle status is received as pending from AA when status verdict is still awaited.
  CONSENT_HANDLE_STATUS_PENDING = 3;
  // CONSENT_HANDLE_STATUS_REJECTED will be marked only in case of consent handle status received as REJECTED in case of consent callbacks
  // all the above statuses are received from AA API, but REJECTED status is received from banks.
  CONSENT_HANDLE_STATUS_REJECTED = 4;
}
