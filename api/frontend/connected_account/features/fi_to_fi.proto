// Define all custom protos related to frontend connectedaccount service -> Fi to Fi feature goes here

syntax = "proto3";

package frontend.connected_account.features;

import "api/frontend/connected_account/common/account.proto";
import "api/frontend/connected_account/common/types.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/aa.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "google/protobuf/duration.proto";

option go_package = "github.com/epifi/gamma/api/frontend/connected_account/features";
option java_package = "com.github.epifi.gamma.api.frontend.connected_account.features";


// Figma: https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=10808-134368&t=uleugm8zbg7K1Ilb-0
//  FiToFiDiscoveryBottomSheet provides the display texts for account discovery bottom sheet
message FiToFiDiscoveryBottomSheet {
  frontend.connected_account.common.TextDisplayInfo discovery_in_progress_info = 1;
  // on encountering discovery failures, we will perform retires equal to num_retries
  uint32 num_retries = 2;
}

//  FiToFiAaLoginBottomSheet provides the display texts for AA login bottom sheet
message FiToFiAaLoginBottomSheet {
  frontend.connected_account.common.TextDisplayInfo aa_login_otp_info = 1;
  frontend.connected_account.common.TextDisplayInfo aa_already_logged_in_info = 2;
  repeated frontend.connected_account.common.AccountDetails display_account_details = 3;
}

//  FiToFiLinkFipBottomSheet provides the display texts for Bank OTP bottom sheet
message FiToFiLinkFipBottomSheet {
  frontend.connected_account.common.TextDisplayInfo bank_otp_info = 1;
}

//  InitiateFiToFiFlowBottomSheet provides bottom sheet information to be displayed to the user
// for connecting Federal saving bank account
message InitiateFiToFiFlowBottomSheet {
  // title text to be displayed on connecting Fi to Fi bottom sheet
  api.typesv2.common.Text title = 1;
  // description of connecting Fi Federal saving bank account
  api.typesv2.common.Text description = 2;
  // Epifi Wealth and AA TnC in bottom sheet
  api.typesv2.common.Text wealth_and_aa_tnc = 3;
  // aa_entity to be used and displayed at time of connecting fi to fi.
  api.typesv2.AaEntity aa_entity = 4;
  // Cta on click of which, RPC to initiate SDK for Fi to Fi flow will be called
  frontend.deeplink.Cta cta = 5;
  // secure spends icon to be shown for fi to fi bottom sheet.
  // deprecating since this will be a common bottom sheet, changing the name so that it remains semantically correct
  api.typesv2.common.VisualElement secure_spends_icon = 6[deprecated = true];
  // max_retry_allowed_failure_case signifies the number of times user has tried Fi to Fi flow in case of failures cases.
  // if these retries are exhausted, fi to fi bottom sheet is dismissed and
  // user is eligible to see unlocked spends txns, analyser, etc.
  uint32 max_retry_allowed_failure_case = 7;
  // invalidate_num_retry_duration is the duration after which the num of times used has tried fi to fi flow is invalidated.
  google.protobuf.Duration invalidate_num_retry_duration = 8;
  // will replace the secure_spends_icon that was being used before
  // refer: https://www.figma.com/design/ff6C5LsDIkwpPfw84k43rM/FFF-%E2%80%A2%C2%A0Connected-Accounts?node-id=5018-7851&t=jjbTUBwXfMNsLkGv-0
  api.typesv2.common.VisualElement primary_icon = 9;
}

//  FiToFiFlowTerminalScreenPurpose represents the terminal screen status/purpose that screen `CA_FI_TO_FI_FLOW_TERMINAL_SCREEN` can have
enum FiToFiFlowTerminalScreenPurpose {
  FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_UNSPECIFIED = 0;
  FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_CONSENT_STATUS_SUCCESS = 1;
  FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_CONSENT_STATUS_FAILURE = 2;
  FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_CONSENT_STATUS_REJECTED = 3;
  FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_ACC_DISCOVERY_FAILURE = 4;
  FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_ACC_DISCOVERY_TIMEOUT = 5;
  FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_NO_ACC_DISCOVERED = 6;
  FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_REQUEST_CONSENT_FAILURE = 7;
  FI_TO_FI_FLOW_TERMINAL_SCREEN_PURPOSE_APPROVE_CONSENT_FAILURE = 8;
}
