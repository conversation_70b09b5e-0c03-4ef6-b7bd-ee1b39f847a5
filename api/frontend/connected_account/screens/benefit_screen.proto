syntax = "proto3";

package frontend.connected_account.screens;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/connected_account/coming_soon.proto";

option go_package = "github.com/epifi/gamma/api/frontend/connected_account/screens";
option java_package = "com.github.epifi.gamma.api.frontend.connected_account.screens";

// Figma :- https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=14154-30066&mode=design&t=xaQ0HBcPEBQZ470u-4
message BenefitScreen {
  api.typesv2.common.VisualElement close_icon = 1;
  string background_color = 2;
  // Animated Fip Container
  // Figma :- https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=14412-23217&mode=design&t=xaQ0HBcPEBQZ470u-4
  message BenefitsFipContainer {
    string background_color = 1;
    api.typesv2.ui.IconTextComponent title = 2;
    api.typesv2.ui.IconTextComponent subtitle = 3;
    repeated api.typesv2.ui.VerticalIconTextComponent fips = 4;
  }
  BenefitsFipContainer fip_container = 3;
  api.typesv2.ui.IconTextComponent title = 4;
  api.typesv2.ui.IconTextComponent desc = 5;
  repeated api.typesv2.ui.IconTextComponent benefits = 6;
  api.typesv2.common.ui.widget.CheckboxItem wealth_tnc = 7;
  api.typesv2.common.ui.widget.CheckboxItem fi_lite_tnc = 8;
  deeplink.Cta cta = 9;
  api.typesv2.common.VisualElement top_icon = 10;
  // Figma : https://www.figma.com/file/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?type=design&node-id=20632-18714&mode=design&t=e3P5mBoQohZhpKgq-4
  message BenefitsV2Component {
    api.typesv2.common.ui.widget.BackgroundColour background_colour = 1;
    api.typesv2.common.Text title = 2;
    repeated api.typesv2.ui.IconTextComponent benefits = 3;
  }
  BenefitsV2Component benefits_v2 = 11;
  api.typesv2.connected_account.ComingSoonComponent bottom_view = 12;
}
