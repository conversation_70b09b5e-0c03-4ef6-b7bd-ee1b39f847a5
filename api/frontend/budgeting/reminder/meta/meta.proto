syntax = "proto3";

package frontend.budgeting.reminder.meta;

option go_package = "github.com/epifi/gamma/api/frontend/budgeting/reminder/meta";
option java_package = "com.github.epifi.gamma.api.frontend.budgeting.reminder.meta";


enum ReminderConfigType {
  REMINDER_CONFIG_TYPE_UNSPECIFIED = 0;
  CONFIG_CREATE = 1;
  CONFIG_UPDATE = 2;
}


enum ReminderSubscriptionFieldMask {
  REMINDER_SUBSCRIPTION_FIELD_UNSPECIFIED = 0;
  MASK_STATUS = 1;
  MASK_CATEGORY = 2;
  MASK_AMOUNT = 3;
  MASK_COMMS = 4;
  MASK_DAY_OF_MONTH = 5;
  MASK_REMINDER_PARAMS = 6;
}

enum ReminderType {
  REMINDER_TYPE_UNSPECIFIED = 0;
  AMOUNT_SPENDS = 1;
  CATEGORY_SPENDS = 2;
  MERCHANT_SPENDS = 3;
  CREDIT_CARD_DUE_DATE = 4;
}

enum ReminderStatus {
  REMINDER_STATUS_UNSPECIFIED = 0;
  ACTIVE = 1;
  PAUSED = 2;
  EXPIRED = 3;
  DELETED = 4;
}

enum ReminderMedium {
  MEDIUM_UNSPECIFIED = 0;
  SMS = 1;
  EMAIL = 2;
  NOTIFICATION = 3;
  WHATSAPP = 4;
}

enum ReminderFrequency {
  REMINDER_FREQUENCY_UNSPECIFIED = 0;
  MONTH = 1;
  YEAR = 2;
}

enum SupportedTopicProvenance {
  SUPPORTED_TOPIC_PROVENANCE_UNSPECIFIED = 0;
  CONFIG = 1;
  OVER_SPENDS = 2;
}

enum PostReminderScreenType {
  POST_REMINDER_SCREEN_TYPE_UNSPECIFIED = 0;
  CREATE = 1;
  UPDATE = 2;
  DELETE = 3;
  EXCEED_REMINDER = 4;
}
