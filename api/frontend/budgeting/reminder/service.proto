syntax = "proto3";

package frontend.budgeting.reminder;

import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/typesv2/ui/widget_themes.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/money.proto";
import "validate/validate.proto";
import "api/frontend/budgeting/reminder/meta/meta.proto";

option go_package = "github.com/epifi/gamma/api/frontend/budgeting/reminder";
option java_package = "com.github.epifi.gamma.api.frontend.budgeting.reminder";

// List of RPCs used in the Reminder service
// Figma: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=55%3A2002&t=V0ESRxeUQdchup8N-0
service ReminderService {
  // GetReminderSubscriptions gets you list of all the reminder subscriptions
  // ReminderFilters are to be set in the request. Filters field is not optional.
  rpc GetReminderSubscriptions(GetReminderSubscriptionsRequest) returns (GetReminderSubscriptionsResponse);

  // GetLandingPage fetches the layout of the Reminders landing page
  // This page will have reminder recommendations as well.
  rpc GetLandingPage(GetLandingPageRequest) returns (GetLandingPageResponse);

  // CreateReminderSubscription is called when an user tries to create a reminder subscription.
  rpc CreateReminderSubscription(CreateReminderSubscriptionRequest) returns (CreateReminderSubscriptionResponse);

  //  UpdateReminderSubscriptionState is called when an user tries to update the state of reminder.
  rpc UpdateReminderSubscriptionState(UpdateReminderSubscriptionStateRequest) returns (UpdateReminderSubscriptionStateResponse);

  // UpdateReminderSubscription is called when an user tries to update the config of the reminder subscription.
  rpc UpdateReminderSubscription(UpdateReminderSubscriptionRequest) returns (UpdateReminderSubscriptionResponse);

  // GetReminderEntryPoint is the entry point RPC which either has a banner or the list of reminder subscriptions.
  // Banner is shown in the case when the user has no reminder subscriptions.
  rpc GetReminderEntryPoint(GetReminderEntryPointRequest) returns (GetReminderEntryPointResponse);

  // GetSupportedTopicsPage RPC is called when user clicks on any of the widget on the landing page. Recommended topics
  // are shown in this screen. Distinct topics are separated by the widget. Each widget has multiple tiles.
  // Each tile has heading, sub_heading and a primary image.
  rpc GetSupportedTopicsPage(GetSupportedTopicsPageRequest) returns (GetSupportedTopicsPageResponse);

  // FetchSupportedTopicsList RPC fetches list of all supported topics for the following page
  // Figma: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=1%3A3115&t=Jn3HhgY9fB4aCnvu-0
  rpc FetchSupportedTopicsList(FetchSupportedTopicsListRequest) returns (FetchSupportedTopicsListResponse);

  // GetReminderConfig RPC fetches CreateReminder / UpdateReminder config page which has multiple widgets
  // Each widget can be either input widget type or comms prompt.
  // Input Widget Type: Input widget type has multiple rows and each row has multiple sections.
  // Each section can either be of text type of input field type.
  // Comms Widget Type: Comms widget type can have multiple notification types (Ex: Email, SMS, etc.,)
  rpc GetReminderConfig(GetReminderConfigRequest) returns (GetReminderConfigResponse);

  // GetDatesForCCDueDateReminder provides the dates for which user can set reminder on credit card bill payments.
  rpc GetDatesForCCDueDateReminder(GetDatesForCreditCardDueDateReminderRequest) returns (GetDatesForCreditCardDueDateReminderResponse);

  rpc GetDeleteReminderScreen(GetDeleteReminderScreenRequest) returns (GetDeleteReminderScreenResponse);

  rpc GetFallBackCcReminderScreen(GetFallBackCcReminderScreenRequest) returns (GetFallBackCcReminderScreenResponse);
}

message GetFallBackCcReminderScreenRequest {
  frontend.header.RequestHeader req = 1;
}

message GetFallBackCcReminderScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  PostReminderScreen unavailable_cc_screen = 2;
}

message GetDeleteReminderScreenRequest {
  frontend.header.RequestHeader req = 1;
}
message GetDeleteReminderScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  PostReminderScreen warning_screen = 2;
}

message FetchSupportedTopicsListRequest {
  frontend.header.RequestHeader req = 1;
  repeated meta.ReminderType reminder_type = 2;
  meta.SupportedTopicProvenance supported_topic_provenance = 3;
}

message FetchSupportedTopicsListResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated SupportedTopicsTile supported_topics = 2;
}

// PostReminderScreen will be a part of multiple RPC responses.
// Create, Update and Delete Reminder RPCs will have this message in them.
message PostReminderScreen {
  api.typesv2.common.Text title = 1;
  repeated PostReminderScreenRows rows = 2;
  PostReminderScreenCta primary_cta = 3;
  PostReminderScreenCta secondary_cta = 4;
  api.typesv2.common.Image primary_img = 5;
  meta.PostReminderScreenType screen_type = 6;
}

message PostReminderScreenCta {
  api.typesv2.common.Text cta_text = 1;
  deeplink.Deeplink deeplink = 2;
  api.typesv2.ui.BackgroundColour bg_color = 3;
}

// PostReminderScreenRows will be either a simple text or an alert card
message PostReminderScreenRows {
  oneof row {
    api.typesv2.common.Text text_row = 1;
    PostReminderScreenRowAlertCard alert_card = 2;
  }
}

// PostReminderScreenRowAlertCard is the alert card that you see post the reminder creation, updation
// or deletion. This Alert card will have a small primary_icon
// Refer: https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=1%3A1094&t=xtHHGc8z8r7arQyd-0
message PostReminderScreenRowAlertCard {
  // Example: Fi icon in the above figma link
  api.typesv2.common.Image primary_icon = 1;
  // The title beside the primary_icon
  // Example: Fi Money . now
  api.typesv2.common.Text title = 2;
  // Credit card repayment alert
  api.typesv2.common.Text message_heading = 3;
  // 5th of March is the last date to repay the bill
  api.typesv2.common.Text message_sub_heading = 4;
}

message GetDatesForCreditCardDueDateReminderRequest {
  frontend.header.RequestHeader req = 1;
}

message GetDatesForCreditCardDueDateReminderResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated DateList date_list = 2;
  HighlightedText highlighted_text = 3;
  api.typesv2.common.Text heading = 4;
}

message DateList {
  api.typesv2.common.Text heading = 1;
  Tag tag = 2;
  uint32 day_of_month = 3;
}

message Tag {
  api.typesv2.common.Text display_text = 1;
  api.typesv2.ui.BackgroundColour bg_color = 3;
}

// GetReminderConfigRequest is the request object of GetReminderConfig RPC
// This requires ReminderType in the request
message GetReminderConfigRequest {
  frontend.header.RequestHeader req = 1;
  frontend.budgeting.reminder.meta.ReminderConfigType config_type = 2;
  oneof config_flow {
    CreateReminderConfig create_config = 4;
    UpdateReminderConfig update_config = 5;
  }
  string source = 6;
}

message CreateReminderConfig {
  frontend.budgeting.reminder.meta.ReminderType type = 1;
  oneof create_reminder_config_params {
    CategoryReminderConfig category_config = 2;
    AmountReminderConfig amount_config = 3;
    CreditCardDueDateReminderConfig credit_card_due_date_config = 8;
  }
}

message UpdateReminderConfig {
  string reminder_subscription_id = 1;
}

message GetReminderConfigResponse {
  frontend.header.ResponseHeader resp_header = 1;
  ReminderForm reminder_form = 2;
}

message ReminderForm {
  api.typesv2.common.Image heading_img = 1;
  repeated ReminderFormWidget widgets = 2;
  ReminderFormCta primary_cta = 3;
  ReminderFormCta secondary_cta = 4;
  frontend.budgeting.reminder.meta.ReminderConfigType reminder_config_type = 5;
  string reminder_subscription_id = 6;
  api.typesv2.common.Text heading_text = 7;
}

message ReminderFormCta {
  api.typesv2.common.Text cta_text = 1;
  deeplink.Deeplink deeplink = 2;
  api.typesv2.ui.BackgroundColour bg_color = 3;
  api.typesv2.common.Image img = 4;
  api.typesv2.ui.Shadow shadow = 5;
}

message ReminderFormWidget {
  oneof widget {
    ReminderFormInput reminder_form_input = 1;
    ReminderFormComms reminder_form_comms = 2;
    ReminderFormStatusToggle reminder_form_status_toggle = 3;
    ReminderFormInfo reminder_form_info = 4;
  }
}

message ReminderFormInfo {
  api.typesv2.common.Text heading = 1;
  repeated api.typesv2.common.Text reminder_info_text = 2;
  api.typesv2.ui.BackgroundColour bg_color = 3;
  api.typesv2.common.Image right_img = 4;
}

message ReminderFormStatusToggle {
  api.typesv2.common.Text text_content = 1;
  bool is_toggle_enabled = 2;
}

message ReminderFormInput {
  repeated ReminderFormInputRow rows = 1;
  HighlightedText highlighted_text = 2;
}

message HighlightedText {
  api.typesv2.common.Text content = 1;
  api.typesv2.ui.BackgroundColour bg_color = 2;
}

message ReminderFormInputRow {
  repeated ReminderFormInputRowSection sections = 1;
}

message ReminderFormInputRowSection {
  oneof content {
    api.typesv2.common.Text text_content = 1;
    ReminderFormInputRowSectionField input_field = 2;
  }
}

message ReminderFormInputRowSectionField {
  api.typesv2.ui.IconTextComponent icon_text_component = 1;
  // ReminderSubscriptionFieldMask will have mask api.typesv2. i.e.,
  // This field will denote the type of value that we are displaying in this
  // input field.
  frontend.budgeting.reminder.meta.ReminderSubscriptionFieldMask field_type = 3;
  // oneof field_val should be picked up from here to pass it on to further
  // requests
  oneof field_val {
    // amount to be used when the field mask type is MASK_AMOUNT
    int32 amount = 4;
    // display_category to be used when the field mask type is MASK_CATEGORY
    string display_category = 5;
    // day_of_month denotes the day of month that this reminder should be triggered
    int32 day_of_month = 6;
  }
}

message ReminderFormComms {
  api.typesv2.common.Text heading = 1;
  repeated ReminderFormCommTile tiles = 2;
}

message ReminderFormCommTile {
  api.typesv2.common.Image primary_img = 1;
  api.typesv2.common.Text caption = 2;
  frontend.budgeting.reminder.meta.ReminderMedium medium = 3;
  bool is_selected = 4;
}

message GetSupportedTopicsPageRequest {
  frontend.header.RequestHeader req = 1;
}

message GetSupportedTopicsPageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  SupportedTopicsPageLayout layout = 2;
}

message SupportedTopicsPageLayout {
  api.typesv2.common.Text title = 1;
  repeated SupportedTopicsPageWidget widgets = 2;
}

message SupportedTopicsPageWidget {
  api.typesv2.common.Text title = 1;
  repeated SupportedTopicsTile tiles = 2;
}

message SupportedTopicCategoryTile {
  // Display category enum to string field
  string display_category = 1;
  api.typesv2.Money amount = 2;
}

message SupportedTopicAmountSpendsTile {
  api.typesv2.Money amount = 1;
}

message SupportedTopicsTile {
  api.typesv2.common.Image primary_img = 1;
  api.typesv2.common.Text heading = 2;
  api.typesv2.common.Text sub_heading = 3;
  frontend.budgeting.reminder.meta.ReminderType reminder_type = 4;
  api.typesv2.common.Text config_highlighted_text = 7;
  oneof topic {
    SupportedTopicCategoryTile category = 5;
    SupportedTopicAmountSpendsTile amount_spends = 8;
  }
  deeplink.Deeplink deeplink = 6;
}

message GetReminderEntryPointRequest {
  frontend.header.RequestHeader req = 1;
}

message GetReminderEntryPointResponse {
  frontend.header.ResponseHeader resp_header = 1;
  oneof entry_point {
    ReminderEntryPointBannerLayout banner = 2;
    ReminderSubscriptionsLayout reminder_subscriptions_layout = 3;
  }
}

message ReminderEntryPointBannerHeader {
  api.typesv2.common.Text heading = 1;
  api.typesv2.ui.BackgroundColour bg_color = 2;
}

message ReminderEntryPointBannerLayout {
  ReminderEntryPointBannerHeader header_section = 1;
  ReminderEntryPointBanner banner_section = 2;
}

message ReminderEntryPointBanner {
  api.typesv2.common.Image primary_img = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text sub_heading_1 = 3;
  api.typesv2.common.Text sub_heading_2 = 4;
  api.typesv2.ui.BackgroundColour bg_color = 5;
  ReminderEntryPointCTA primary_cta = 6;
}

message ReminderEntryPointCTA {
  deeplink.Deeplink deeplink = 1;
  api.typesv2.common.Text cta_text = 2;
  api.typesv2.ui.BackgroundColour bg_color = 3;
  api.typesv2.ui.Shadow shadow_color = 4;
}

message UpdateReminderSubscriptionRequest {
  frontend.header.RequestHeader req = 1;
  // ReminderSubscription is the object that is being modified.
  oneof config {
    CategoryReminderConfig category_config = 2;
    AmountReminderConfig amount_config = 3;
    CreditCardDueDateReminderConfig credit_card_due_date_config = 9;
  }
  // ReminderMedium contains the list of comms that this reminder needs to be notified via.
  repeated frontend.budgeting.reminder.meta.ReminderMedium comm_mediums = 4;
  // ReminderType is set to identify the type of reminder that is being set.
  frontend.budgeting.reminder.meta.ReminderType type = 5;
  string reminder_subscription_id = 6;
  // update_mask will have list of fields that needs to be modified in this update request.
  repeated frontend.budgeting.reminder.meta.ReminderSubscriptionFieldMask update_mask = 7 [(validate.rules).repeated.min_items = 1];
  frontend.budgeting.reminder.meta.ReminderStatus reminder_status = 8;
}

message UpdateReminderSubscriptionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  PostReminderScreen post_reminder_screen = 2;
}

message GetReminderSubscriptionsRequest {
  frontend.header.RequestHeader req = 1;
  ReminderFilters filters = 2;
}

message GetReminderSubscriptionsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  ReminderSubscriptionsLayout reminder_subscriptions_layout = 2;
}

message ReminderSubscriptionsHeader {
  api.typesv2.common.Text heading = 1;
  api.typesv2.ui.BackgroundColour bg_color = 2;
}

// ReminderSubscriptionWidgetTopSection represents top section inside ReminderSubscriptionWidget
// ReminderSubscriptionWidgetTopSection will have all the content that is to be shown in the top section
// This section will also have a CTA to the right (placed on the top right of this section)
message ReminderSubscriptionWidgetTopSection {
  api.typesv2.common.Image primary_img = 1;
  // widget_title (Ex; REMIND ME IF I SPEND)
  api.typesv2.common.Text widget_title = 2;
  // heading (Ex; More than 1500 on cabs)
  api.typesv2.common.Text heading = 3;
  // sub_heading (Ex; INITIATED 3 TIMES THIS MONTH)
  api.typesv2.common.Text sub_heading = 4;
  api.typesv2.ui.IconTextComponent right_cta = 5;
  api.typesv2.ui.BackgroundColour bg_color = 6;
}

// ReminderSubscriptionWidgetBottomSection represents bottom section inside ReminderSubscriptionWidget
// ReminderSubscriptionWidgetBottomSection will have left, center and right text
// This section also has a cta to the right.
// Text components in this section can sometimes be only in one line. In those cases, client will
// vertical center align as well as horizontal center align.
message ReminderSubscriptionWidgetBottomSection {
  // left_text (Ex; SPENDS ON CABS)
  api.typesv2.common.Text left_text = 1;
  // center_text (Ex; LAST MONTH 2300)
  api.typesv2.common.Text center_text = 2;
  // right_text (Ex; THIS MONTH 2000)
  api.typesv2.common.Text right_text = 3;
  api.typesv2.ui.IconTextComponent cta = 4;
  api.typesv2.ui.BackgroundColour bg_color = 5;
}

// https://www.figma.com/file/Ev9oRdMy3IVdtb8XetuUXX/Reminders-%7C-FFF?node-id=144%3A2167&t=FLS3BPNcGEJgYhde-0
// ReminderSubscriptionWidget represents each widget that is shown in the GetReminderSubscriptions page.
// Each widget has a top section and a bottom section
message ReminderSubscriptionWidget {
  ReminderSubscriptionWidgetTopSection top_section = 1;
  ReminderSubscriptionWidgetBottomSection bottom_section = 2;
  api.typesv2.ui.Shadow shadow_color = 3;
  // reminder_data field will have data related to the reminder subscription
  ReminderSubscription reminder_data = 4;
  frontend.deeplink.Deeplink widget_deeplink = 5;

}

// ReminderSubscriptionWidgetCollection contains a heading and list of widgets.
message ReminderSubscriptionWidgetCollection {
  api.typesv2.common.Text heading = 1;
  repeated ReminderSubscriptionWidget widgets = 2;
  api.typesv2.ui.BackgroundColour bg_color = 3;
}

message ReminderSubscriptionsPageCTA {
  api.typesv2.common.Text cta_text = 1;
  api.typesv2.ui.BackgroundColour bg_color = 2;
  deeplink.Deeplink deeplink = 3;
}

// ReminderSubscriptionsLayout is the layout that is to be shown when user lands on reminder subscriptions list page
// This page has a header section, widget collection and a CTA at the bottom.
// Each widget has a reminder type (Active, Paused, etc.,), basis on which Paused reminders are seperated in the layout.
// Widgets are passed in the order so that client doesn't have to worry about the order of widgets to be shown.
message ReminderSubscriptionsLayout {
  ReminderSubscriptionsHeader header_section = 1;
  // reminder_collections will have multiple collections
  // Active and Paused reminder collection will be separated and sent
  repeated ReminderSubscriptionWidgetCollection reminder_collections = 2;
  ReminderSubscriptionsPageCTA primary_cta = 3;
  api.typesv2.ui.BackgroundColour bg_color = 4;
}

message ReminderSubscription {
  string reminder_id = 1[deprecated = true];
  frontend.budgeting.reminder.meta.ReminderStatus status = 2;
  frontend.budgeting.reminder.meta.ReminderType reminder_type = 3;
  string reminder_subscription_id = 4;
}

message ReminderFilters {
  repeated frontend.budgeting.reminder.meta.ReminderStatus reminder_statuses = 1;
}

message UpdateReminderSubscriptionStateRequest {
  frontend.header.RequestHeader req = 1;
  string reminder_subscription_id = 2;
  frontend.budgeting.reminder.meta.ReminderStatus reminder_status = 3;
}

message UpdateReminderSubscriptionStateResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

// CreateReminderSubscriptionRequest is the request object for CreateReminderSubscription RPC
message CreateReminderSubscriptionRequest {
  frontend.header.RequestHeader req = 1;
  // oneof the below config needs to be set while create a reminder subscription.
  oneof config {
    CategoryReminderConfig category_config = 2;
    AmountReminderConfig amount_config = 3;
    CreditCardDueDateReminderConfig credit_card_due_date_config = 4;
  }
  // ReminderMedium contains the list of comms that this reminder needs to be notified via.
  repeated frontend.budgeting.reminder.meta.ReminderMedium comm_mediums = 5;
  // ReminderType is set to identify the type of reminder that is being set.
  frontend.budgeting.reminder.meta.ReminderType type = 6;
  string reminder_id = 7;
}

message CategoryDetail {
  // Display category enum to string field
  string display_category = 1;
}

message CategoryReminderConfig {
  CategoryDetail category = 1;
  api.typesv2.Money amount = 2;
  frontend.budgeting.reminder.meta.ReminderFrequency frequency = 3;
}

message AmountReminderConfig {
  api.typesv2.Money amount = 1;
  frontend.budgeting.reminder.meta.ReminderFrequency frequency = 2;
}

message CreditCardDueDateReminderConfig {
  int64 configured_date = 1;
}

message CreateReminderSubscriptionResponse {
  frontend.header.ResponseHeader resp_header = 1;
  string reminder_subscription_id = 2;
  PostReminderScreen post_reminder_screen = 3;
}

message GetLandingPageRequest {
  frontend.header.RequestHeader req = 1;
}

message GetLandingPageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  LandingPageLayout layout = 2;
}

message LandingPageLayout {
  LandingPageHeader header_section = 1;
  repeated LandingPageWidget widgets = 2;
  ReminderFormCta cta = 3;
}

message LandingPageHeader {
  api.typesv2.common.Text title = 1;
  api.typesv2.ui.BackgroundColour bg_color = 2;
}

message LandingPageWidget {
  enum WidgetType {
    WIDGET_TYPE_UNSPECIFIED = 0;
    VERTICAL_TIPS = 1;
    HORIZONTAL_TIPS = 2;
  }
  WidgetType widget_type = 1;
  // This oneof widget will be identified by the WidgetType
  oneof widget {
    VerticalTipsWidget vertical_tips_widget = 2;
    HorizontalTipsWidget horizontal_tips_widget = 3;
  }
  api.typesv2.ui.BackgroundColour widget_bg = 6;
}

message VerticalTipsWidget {
  api.typesv2.common.Text title = 1;
  repeated VerticalTipsWidgetTile tiles = 2;
  string source = 3;
}

message VerticalTipsWidgetTile {
  api.typesv2.common.Image primary_img = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.common.Text sub_title = 3;
  api.typesv2.ui.BackgroundColour widget_bg = 4;
  api.typesv2.ui.Shadow shadow = 5;
  deeplink.Deeplink deeplink = 6;
  // field is used to combine different type of reminder.
  // e.g category spends, overall spends, merchant spends this will come under OVERSPENDS_REMINDER;
  string reminder_group = 7;
}

message HorizontalTipsWidget {
  api.typesv2.common.Text title = 1;
  repeated HorizontalTipsWidgetTile tiles = 2;
  string source = 3;
}

message HorizontalTipsWidgetTile {
  api.typesv2.common.Image primary_img = 1;
  api.typesv2.common.Text heading = 2;
  api.typesv2.common.Text sub_heading_1 = 3;
  api.typesv2.common.Text sub_heading_2 = 4;
  api.typesv2.ui.BackgroundColour widget_bg = 5;
  api.typesv2.ui.Shadow shadow = 6;
  deeplink.Deeplink deeplink = 7;
  oneof config {
    CategoryReminderConfig category_config = 8;
    AmountReminderConfig amount_config = 9;
    CreditCardDueDateReminderConfig credit_card_due_date_config = 10;
  }
  meta.ReminderType reminder_type = 11;
}
