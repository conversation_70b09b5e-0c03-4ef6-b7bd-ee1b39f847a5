syntax = "proto3";

package frontend.insights.networth;

import "api/frontend/insights/networth/asset_dashboard.proto";
import "api/frontend/insights/networth/enums/enums.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "google/protobuf/wrappers.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/networth";
option java_package = "com.github.epifi.gamma.api.frontend.insights.networth";

message NetWorthManualForm {
  api.typesv2.ui.IconTextComponent header = 1;
  repeated NetWorthManualFormComponentsSection components_sections = 2;
  api.typesv2.ui.IconTextComponent submit_cta = 3;
  // action on top right of asset page
  //  figma: https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=9219-99222&t=RyiP1mogrTUAyCLB-4
  repeated AssetActionButton action_buttons = 4;
}

// Asset action to be shown on top right of edit asset page
message AssetActionButton {
  api.typesv2.ui.IconTextComponent display_text = 1;
  AssetActionType action_type = 2 [(validate.rules).enum = {not_in: [0, 1]}];
}

message NetWorthManualFormComponentsSection {
  api.typesv2.ui.IconTextComponent header = 1;
  repeated NetWorthManualFormInputComponent input_components = 2;
}

message NetWorthManualFormInputComponent {
  api.typesv2.ui.IconTextComponent title = 1;

  // A hint to the user to enter input, e.g. "AMC name"
  // When modifying a previously submitted form, an existing input may be shown instead of this hint
  api.typesv2.ui.IconTextComponent placeholder_text = 2;

  frontend.insights.networth.enums.NetworthManualFormInputStyle input_style = 3 [(validate.rules).enum = {not_in: [0]}];

  // True, if an input from user is necessary for this field, to submit the form successfully
  bool is_mandatory = 4;

  NetWorthManualInputData input_data = 5;
}

message NetWorthManualInputData {
  // The internal identifier of a field in the form, e.g. "AIF NAME", etc.
  string field_name = 1 [(validate.rules).string.min_len = 1];

  oneof input {
    MultiInputOptions multi_options = 2;
    SingleInputOption single_option = 3;
    MultipleEditableOptions multi_editable_options = 4;
    SearchableSelectionInput searchable_selection_input = 5;
  }

  frontend.insights.networth.enums.NetworthManualFormInputDataType data_type = 10;
}

message MultiInputOptions {
  repeated MultiInputOption options = 1;
}

message MultiInputOption {
  api.typesv2.ui.IconTextComponent display_value = 1;
  // unique id for each option
  string id = 2;
  bool is_selected = 3;
}

message SingleInputOption {
  InputOptionValue input_value = 1 [deprecated = true];
  InputOptionData input_option_data = 2;
}

message StringType {
  // wrapper type is used to differentiate between empty string and no value
  google.protobuf.StringValue data = 1;
  StringValidation validation = 2;
  // ToDo: move to a list of one of validations instead of having multiple fields which will lead to confusion
  message StringValidation {
    int32 min_len = 1;
    int32 max_len = 2;
    string validation_failure_msg = 3;
    string regex = 4;
  }
}

message MultipleEditableOptions {
  repeated MultiEditOption options = 1;
  repeated MultiEditOptionValidation multi_edit_validations = 2;
}

// https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=7680-27536&t=YjpeeWmyB2Vls2y4-4
message MultiEditOption {
  api.typesv2.ui.IconTextComponent placeholder_text = 1;
  // unique id for each option
  string id = 2;
  bool is_mandatory = 3;
  // User input
  InputOptionData input_option_data = 4;
  // To be used for any extra details to be specified for the option
  api.typesv2.common.Text option_desc = 5;
}

message MultiEditOptionValidation {
  oneof validations {
    // this validations will be done only if all the input option in list
    // 1. should be of numeric data type e.g. int or double
    // 2. all the input option in list should be of same data type
    TotalSumValidation sum_validation = 1;
  }
}

message TotalSumValidation {
  double total_sum = 1;
  // there may be acceptable diff of say (0.00001) to rule out double precision errors
  // 1.0 +2.1 may become 3.099999999
  double possible_abs_diff = 2;
  string validation_failure_msg = 3;
}

// An input type for user to search and choose an option from a list.
// If none of the options in the list are relevant, the user is expected to find their choice
// by searching, and if not present, add a custom input.
message SearchableSelectionInput {
  // A list of preset choices to show when the user has not entered any search-text or search is not supported.
  // If search is supported but there are no preset choices, clients should show the search results placeholder text.
  // This list should be updated with the list received from search results API when a user modifies the search-text.
  repeated PresetChoice preset_choices = 1;

  // Parameters to allow/disallow a user to search and get relevant choices
  SearchParams search_params = 2;

  // Parameters to allow a user to submit a custom input
  // as choice instead of selecting from the preset choices.
  // If not present, then custom input is not supported.
  CustomChoiceParams custom_choice_params = 3;

  // A previous selection or an empty selection for a new form with validations to perform before submitting it.
  PresetOrCustomSelection preset_or_custom_selection = 4;

  // disclaimer text to be shown below the search input
  api.typesv2.common.Text disclaimer_text = 5;
}

message PresetOrCustomSelection {
  // Field for submitting a user's selection or getting the selection from the previous form submission.
  // Clients can use the display name of a preset choice or a custom choice
  // to show previously submitted data, when the user is modifying the form.
  // Details like font types, etc. can be decided by clients.
  oneof selection {
    // The ID of the selected choice when submitting the input.
    PresetChoice selected_preset_choice = 4;

    // The value field inside should contain the text of the custom choice added by user when submitting the input.
    // This should be populated by clients only when a custom choice allowed.
    // Note: Validations inside this may not be used by clients as they would not be present
    // when a preset choice was submitted previously.
    InputOptionData selected_custom_choice = 5;
  }

  // Note: Validations are needed regardless of user's previous selection type,
  // hence are kept separate instead of combining with the choices.
  StringType.StringValidation custom_choice_string_validation = 1;
}

message SearchParams {
  // True, if a user should be allowed to enter search-text to get relevant choices
  bool is_searchable = 1;

  // The identifier to use when searching for relevant choices based on user's search-text for the field.
  // Distinguishes one form field from another when both may have the same name.
  // This is a string form of the AssetFormFieldSearchIdentifier enum
  // kept client-independent for backward compatibility.
  string search_identifier = 2;

  // A placeholder text to show when user has not entered any search-text.
  // E.g., "Search name of company"
  api.typesv2.common.Text search_box_placeholder_text = 3;

  // A placeholder text to show when user has not entered any search-text and there are no default choices either.
  // E.g., "Type the name of the company which has issued the fund"
  // [Deprecated] Use empty_state instead
  api.typesv2.common.Text search_results_placeholder_text = 4 [deprecated = true];

  // Minimum length of the search text to get relevant search results
  int32 min_search_text_len = 5;

  SearchEmptyState empty_state = 6;

  repeated SearchParamsValidation search_params_validations = 7;
}

message SearchParamsValidation {
  oneof validations {
    // this validations will be used to check regex for search text
    SearchTermValidation search_term_validation = 1;
  }
}

message SearchTermValidation {
  string regex = 1;
  string validation_failure_msg = 2;
}

// An input choice for user to choose from.
message PresetChoice {
  // A unique ID of choice, e.g., a stock ID for Apple or an AMC ID for PMS.
  // This is the value that clients should send when submitting a form.
  string id = 1;

  // Display name of a stock, AMC, AIF, etc.
  api.typesv2.common.Text display_name = 2;

  // Subtitle for the choice, e.g. the sector of the stock.
  api.typesv2.common.Text subtitle = 3;
}

// Parameters to hint the user to add a custom choice.
// E.g., for a user to add a custom AMC name when there are no relevant choices to choose from
// Should not be used if custom choice is not allowed
message CustomChoiceParams {
  // If there are no results for a search text, the display template should be used to
  // hint user to add a custom choice.
  // E.g., the display text can be 'Add the company name ABC' with the replaceable text being 'ABC'
  // The replaceable text should be replaced by clients with the search text entered by user.
  DisplayTemplate add_choice_template = 1;

  // The CTA to set a custom user input as the selected choice
  api.typesv2.ui.IconTextComponent add_choice_cta = 2;
}

// https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=5227-43714&t=8sJhIbtfnUbUAtZg-1
message SearchEmptyState {
  api.typesv2.common.VisualElement image = 1;
  api.typesv2.ui.VerticalKeyValuePair key_value_pair = 2;
  // redirection to bottom sheet explaining about the error occurred
  // e.g. for employer not found error screen
  api.typesv2.ui.IconTextComponent info_redirection = 3;
}

message Int64Type {
  // wrapper type is used to differentiate between zero value and no value
  google.protobuf.Int64Value data = 1;
  Int64Validation validation = 2;
  message Int64Validation {
    int64 min_val = 1;
    int64 max_val = 2;
    string validation_failure_msg = 3;
  }
}

message DoubleType {
  // wrapper type is used to differentiate between zero value and no value
  google.protobuf.DoubleValue data = 1;
  DoubleValidation validation = 2;
  message DoubleValidation {
    double min_val = 1;
    double max_val = 2;
    string validation_failure_msg = 3;
  }
}

message DateType {
  // nil value means no date is set
  google.type.Date data = 1;
  DateValidation validation = 2;
  message DateValidation {
    string validation_failure_msg = 1;
  }
}

message InputOptionValue {
  oneof value {
    StringType string_data = 1;
    Int64Type int64_data = 2;
    DoubleType double_data = 3;
    DateType date_data = 4;
  }
}

message InputOptionData {
  // Holds the form value entered by user for a particular component
  InputOptionValue input_value = 1;
  // Will be used on client to display the input entries.
  DisplayTemplate display_template = 2;
}

// It is mandatory that the template always has only one replaceable text
// E.g. for string 25% display text is 'input%' and replaceable text is 'input'
message DisplayTemplate {
  string display_text = 1;
  string replaceable_text = 2;
}
