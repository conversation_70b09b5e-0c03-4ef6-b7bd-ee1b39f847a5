syntax = "proto3";

package frontend.insights.networth;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/insights/networth/manual_form.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/manual_asset_form_idenitifer.proto";
import "api/typesv2/ui/header_bar.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/networth";
option java_package = "com.github.epifi.gamma.api.frontend.insights.networth";

// Figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12516-8011&t=vjav20xKlpC3t1ui-4
message MagicImportedAssetsListScreen {
  // Toolbar
  api.typesv2.HeaderBar header_bar = 1;
  AssetsSummary assets_summary = 2;
  repeated ImportedAssetsListComponent imported_assets_list_components = 3;
  FooterComponent footer_component = 4;
}

// AssetsComponentHeader is used to nudge users to connect assets via a different way other than magic lens
// For example, if mutual funds is identified, we nudge users to connect MF via MFImport
message AssetsComponentHeader {
  api.typesv2.common.Text title = 1;
  api.typesv2.ui.IconTextComponent connect_assets_cta = 2;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=12516-7810&t=Xw5rK7HqpCBqDlPi-0
  api.typesv2.common.VisualElement icon = 3;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 4;
}

message AssetsSummary {
  api.typesv2.common.Text title = 1;
  api.typesv2.ui.VerticalKeyValuePair assets_info = 2;
  FeedbackComponent feedback_component = 3;
  api.typesv2.ui.IconTextComponent ai_commentary = 4;
}

message FeedbackComponent {
  FeedbackView thumbs_up = 1 [deprecated = true];
  FeedbackView thumbs_down = 2 [deprecated = true];
  api.typesv2.ui.IconTextComponent thumbs_up_normal_view = 3;
  api.typesv2.ui.IconTextComponent thumbs_down_normal_view = 4;
}

// Deprecated
message FeedbackView {
  api.typesv2.common.VisualElement normal_view = 1;
}

message ImportedAssetsListComponent {
  api.typesv2.common.Text title = 1;
  repeated ImportedAssetsListItem imported_assets_list_items = 2;
  api.typesv2.common.ui.widget.BackgroundColour border_color = 3;
  api.typesv2.common.ui.widget.BackgroundColour background_colour = 4;
  api.typesv2.common.ui.widget.BackgroundColour divider_color = 5;
  AssetsComponentHeader assets_component_header = 6;
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80FFF--Wealth-Builder?node-id=12516-7810&t=Xw5rK7HqpCBqDlPi-0
  // Networth is not evaluated via magic lens if is_disabled is true, we nudge users to use banner to connect that asset instead
  bool is_component_disabled = 7;

}

message ImportedAssetsListItem {
  api.typesv2.common.Text asset_name = 1;
  api.typesv2.ui.IconTextComponent asset_value = 2;
  api.typesv2.ui.IconTextComponent import_error = 3;
  ImportedAssetsListItemEditDetails edit_details = 4;
  bool is_selected = 5;
  bool is_editable = 6;
  // this will tell the client that some information related to asset is missing
  // it can't be added to networth without provided info
  bool has_missing_info = 7;
  // this will be used to identify related image on client side
  string file_name = 8;
  // Few assets cannot be selected for networth addition
  bool is_selectable = 9;
}

// figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=12384-4503&t=vjav20xKlpC3t1ui-4
// these are details to fill in to edit the import asset fetched details
message ImportedAssetsListItemEditDetails {
  api.typesv2.common.Text title = 1;
  frontend.insights.networth.NetWorthManualForm manual_form = 2;
  api.typesv2.ManualAssetFormIdentifier form_identifier = 3;
}

message FooterComponent {
  api.typesv2.ui.IconTextComponent share_button = 1;
  frontend.deeplink.Cta add_to_networth_button = 2;
  api.typesv2.common.Text disclaimer = 3;
  // Default text that needs to be sent with the screenshot
  string share_text = 4;
  // represents the image to be shared after clicking share button
  string share_image = 5;
  // client does the caching based on this version. Ex: v1, v2, v3, etc.
  // if version gets changed, the client will download back from S3 and cache it.
  string share_image_version = 6;
}
