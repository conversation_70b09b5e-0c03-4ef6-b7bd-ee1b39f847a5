syntax = "proto3";

package frontend.insights.secrets;

import "api/frontend/analyser/service.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/insights/secrets/portfolio_tracker.proto";
import "api/frontend/insights/secrets/portfolio_tracker_assets_details.proto";
import "api/frontend/insights/secrets/secret_analyser.proto";
import "api/frontend/insights/secrets/secret_landing_page.proto";
import "api/frontend/insights/secrets/secret_summary.proto";
import "api/frontend/insights/secrets/wealth_analyser_report.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/deeplink_screen_option/insights/secrets/portfoliotracker/request_params.proto";
import "api/typesv2/deeplink_screen_option/insights/secrets/provenance.proto";
import "api/typesv2/ui/floating_action_button.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/insights/wealthanalyser/assets_analysis.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "api/typesv2/ui/widget_themes.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/secrets";
option java_package = "com.github.epifi.gamma.api.frontend.insights.secrets";

service Secrets {
  // GetSecretAnalyser builds a secret on the basis of secret id and the relevant filter values
  rpc GetSecretAnalyser (GetSecretAnalyserRequest) returns (GetSecretAnalyserResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_1fa) = true;
  }
  rpc AddSecretToFavourites (AddSecretToFavouritesRequest) returns (AddSecretToFavouritesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // GetSecretSummaries returns secret summary cards for the secret ids
  rpc GetSecretSummaries (GetSecretSummariesRequest) returns (GetSecretSummariesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // GetSecretLibraryPage returns secret landing page data for user
  rpc GetSecretLibraryPage (GetSecretLibraryPageRequest) returns (GetSecretLibraryPageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // The RPC method for retrieving the Wealth Analyser widget data
  rpc GetWealthAnalyserWidget (GetWealthAnalyserWidgetRequest) returns (GetWealthAnalyserWidgetResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetWealthAnalyserReport returns the report page for requested ReportType
  rpc GetWealthAnalyserReport (WealthAnalyserReportRequest) returns (WealthAnalyserReportResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetSecretsFooter returns footer for navigating between secrets, hide footer in case of rpc failure for both 'Internal' and 'Not Found'
  rpc GetSecretsFooter (GetSecretsFooterRequest) returns (GetSecretsFooterResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to fetch the portfolio tracker landing page
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1566&t=Q9eQjZjJg10nxoIA-4
  rpc GetPortfolioTrackerLandingPage (GetPortfolioTrackerLandingPageRequest) returns (GetPortfolioTrackerLandingPageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // RPC to fetch the Asset Details page
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3721-20157&t=Q9eQjZjJg10nxoIA-4
  rpc GetPortfolioTrackerAssetDetailsPage (GetPortfolioTrackerAssetDetailsPageRequest) returns (GetPortfolioTrackerAssetDetailsPageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message GetSecretAnalyserRequest {
  frontend.header.RequestHeader req = 1;
  string secret_name = 2;
  repeated analyser.SelectedFilterValue filters = 3;
  string secret_collection_name = 4;
  // provenance is used to track the flow of the request
  api.typesv2.deeplink_screen_option.insights.secrets.Provenance provenance = 5;
}

message GetSecretAnalyserResponse {
  frontend.header.ResponseHeader resp_header = 1;
  enum Status {
    OK = 0;
    // The client specified an invalid argument such as incorrect secret id
    INVALID_ARGUMENT = 3;
    // Secret could not be created due to unavailability of data
    NOT_FOUND = 5;
    INTERNAL = 13;
    UNAUTHENTICATED = 16;
  }

  SecretAnalyserResponse secret_analyser_response = 2;
}

// SecretAnalyserResponse is wrapper time to contain the types of responses expected in GetSecretAnalyserResponse
message SecretAnalyserResponse {
  oneof response {
    SecretAnalyser secret_analyser = 2;
    // Redirect deeplink in case no data is found to build the secret
    // deeplink will be sent with either OK or NOT_FOUND status codes
    deeplink.Deeplink redirect_deeplink = 3;
  }
  // send the event properties required for client events
  // this will have secret_name, secret_id, flow_name, state (i.e hidden or visible) and data_available is for if account is connected or not
  map<string, string> event_properties = 4;
}

message AddSecretToFavouritesRequest {
  frontend.header.RequestHeader req = 1;
  AddSecretToFavouritesRequestParams request_params = 2;
}
message AddSecretToFavouritesResponse {
  // In case of failure, clients should show a toast and not update use the 'favourite_icon
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.common.VisualElement favourite_icon = 2;
}

message GetSecretSummariesRequest {
  frontend.header.RequestHeader req = 1;
  // stringified entrypoint at which secret summaries will be shown
  // entrypoints are defined in api/insights/secrets/frontend/entrypoint.proto
  string entrypoint = 2;
}

message GetSecretSummariesResponse {
  frontend.header.ResponseHeader resp_header = 1;

  enum Status {
    OK = 0;

    // The client specified an invalid argument such as incorrect secret id
    INVALID_ARGUMENT = 3;

    // Secret could not be created due to unavailability of data
    NOT_FOUND = 5;

    INTERNAL = 13;

    UNAUTHENTICATED = 16;
  }

  repeated SecretSummary secret_summaries = 2;
  // title for the summaries collection
  // figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16935-8404&t=xwZgiWjJQosW4n0e-1
  api.typesv2.ui.IconTextComponent title = 3;

  // Unlock all Money Secrets
  // figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?node-id=16935-8404&t=xwZgiWjJQosW4n0e-1
  api.typesv2.ui.IconTextComponent cta = 4;

  message ShowHideComponent {
    api.typesv2.ui.IconTextComponent show_button = 1;
    api.typesv2.ui.IconTextComponent hide_button = 2;
  }
  // Show/Hide values button on Money Secrets Section
  // This will only be used for UI and visibility of the button. The state will not be saved on BE and will be completely controlled by client
  // Absence of this field indicate that the show/hide feature is disabled and summary cards should show their values
  // figma: https://www.figma.com/design/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-Money-secrets-%26-Analysers-%E2%80%A2-FFF?node-id=19329-20961&t=JBPAFgn1bNeTSeRX-4
  ShowHideComponent show_hide_button = 5;
  api.typesv2.ui.IconTextComponent subtitle = 6;
}


message GetSecretLibraryPageRequest {
  frontend.header.RequestHeader req = 1;
}

message GetSecretLibraryPageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  SecretLibraryPage secret_landing_page = 2;
}

message WealthAnalyserReportRequest {
  frontend.header.RequestHeader req = 1;
  // Corresponds to 'WEALTH_ANALYSER_REPORT_TYPE' enum in backend
  string report_type = 2;
}

message WealthAnalyserReportResponse {
  frontend.header.ResponseHeader resp_header = 1;
  //[Deprecated] Use the oneof response field instead because we need redirection deeplink as well
  WealthAnalyserReport report = 2 [deprecated = true];
  oneof response {
    WealthAnalyserReport wealth_analyser_report = 3;
    deeplink.Deeplink redirect_deeplink = 4;
  }
}

// Request message for the GetWealthAnalyserWidget RPC method
message GetWealthAnalyserWidgetRequest {
  frontend.header.RequestHeader req = 1;
  // String value of WealthAnalyserWidgetEntrypoint, defined in "api/frontend/insights/enums.proto"
  string entrypoint = 2;
}

// Response message for the GetWealthAnalyserWidget RPC method
message GetWealthAnalyserWidgetResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Example: title - "Know your money better", subtitle - "With expert curated analysis!"
  api.typesv2.ui.VerticalKeyValuePair header = 2;
  // List of asset analysis cards
  repeated api.typesv2.ui.insights.wealthanalyser.AssetsAnalysisCard cards = 3;
  // Linear gradient background for the widget
  api.typesv2.common.ui.widget.LinearGradient bg_linear_gradient = 4;
}

message GetSecretsFooterRequest {
  frontend.header.RequestHeader req = 1;
  // identifies the collection to be navigated in the footer
  string secret_collection_name = 2;
}

message GetSecretsFooterResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // array of secrets to be navigated in the footer
  // used to call 'GetSecretAnalyser' on next/back navigation
  // secret_names is deprecated, use secret_deeplinks instead
  repeated string secret_names = 2 [deprecated = true];
  // back button, hidden on first index
  api.typesv2.ui.IconTextComponent back_button = 3;
  // next button, hidden on last index
  api.typesv2.ui.IconTextComponent next_button = 4;
  // background color
  api.typesv2.ui.BackgroundColour bg_color = 5;
  // used for font properties for page index label, text string will be set on client
  api.typesv2.common.Text index_label = 6;
  // array of secret deeplinks to be used to navigate in the footer
  // deeplinks for GetSecretAnalyser on next/back navigation
  repeated deeplink.Deeplink secret_deeplinks = 7;
  // figma: https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=5580-46284&t=ZEWUctFYgsnUYce5-4
  // deeplink to use in case of next press by user on last secret of collection
  deeplink.Deeplink flow_completion_screen = 8;
}

message GetPortfolioTrackerLandingPageRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
}

// Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1566&t=FKyA38eSynsGDVHi-4
message GetPortfolioTrackerLandingPageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1816&t=FKyA38eSynsGDVHi-4
  PortFolioTrackerLandingPageHeader header = 2;
  // 'fixed_components' would be fixed at the top of the screen and user can scroll the scrollable components
  // Only the last component of the 'fixed_components' list would be stuck once user starts scrolling.
  // Currently only 'PortfolioTrackerTitleComponent' and 'NavigationToggleList' would be sent in this
  repeated PortfolioTrackerComponent fixed_components = 3;
  repeated PortfolioTrackerComponent scrollable_components = 4;
  // https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3546-1799&t=KEfg8KQf8X3VlIa2-4
  // this is to show powered by epifiwealth on the end of the page
  // [Deprecated] in favour of repeated footer_components
  api.typesv2.ui.IconTextComponent footer = 5 [deprecated = true];
  // this is show disclaimer as well as survey component in footer
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=10737-8171&t=HajDK5vYkyuK6nJD-4
  // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=9579-14176&t=HajDK5vYkyuK6nJD-4
  repeated api.typesv2.ui.IconTextComponent footer_components = 6;
  // floating icon on footer of page
  api.typesv2.ui.FloatingActionButton floating_action_button = 7;
}

message GetPortfolioTrackerAssetDetailsPageRequest {
  // standard request header
  frontend.header.RequestHeader req = 1 [(validate.rules).message.required = true];
  // mandatory field
  // Asset for which we need the AssetDistribution page
  api.typesv2.deeplink_screen_option.insights.secrets.portfoliotracker.AssetDetailsPageRequestParams request_params = 2;
}

message GetPortfolioTrackerAssetDetailsPageResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Figma: https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=3721-20157&t=Q9eQjZjJg10nxoIA-4
  PortfolioTrackerAssetDetails asset_details = 2;
}
