syntax = "proto3";

package frontend.insights;

option go_package = "github.com/epifi/gamma/api/frontend/insights";
option java_package = "com.github.epifi.gamma.api.frontend.insights";

// Defines the Rating options for insights served
enum Rating {
  RATING_UNSPECIFIED = 0;
  RATING_POSITIVE = 1;
  RATING_NEGATIVE = 2;
}


// different types of insight cards that can be shown to the user.
enum InsightCardType {
  INSIGHT_CARD_TYPE_UNSPECIFIED = 0;
  // Insight main body will only contain text.
  INSIGHT_CARD_TYPE_ONLY_TEXT = 1;
}

// UanListScreenSource specifies the entry point for this UAN list dashboard
enum UanListScreenSource {
  UAN_LIST_SCREEN_SOURCE_UNSPECIFIED = 0;
  UAN_LIST_SCREEN_SOURCE_REFRESH_ALL = 1;
}

// WealthAnalyserWidgetEntrypoint specifies the entry point where WealthAnalyserWidget is to be created.
enum WealthAnalyserWidgetEntrypoint {
  WEALTH_ANALYSER_WIDGET_ENTRYPOINT_UNSPECIFIED = 0;
  WEALTH_ANALYSER_WIDGET_ENTRYPOINT_HOME = 1;
  //To show all wealth analyser widgets for CoreWealthAnalyser users in wealth builder landing page
  WEALTH_ANALYSER_WIDGET_ENTRYPOINT_WEALTH_BUILDER_LANDING_SCREEN = 2;
}
