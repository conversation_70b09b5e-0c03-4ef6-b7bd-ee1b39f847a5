syntax = "proto3";
package emailparser;

import "api/rpc/status.proto";
import "google/type/date.proto";
import "validate/validate.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/insights/emailparser/merchant.proto";

option go_package = "github.com/epifi/gamma/api/frontend/insights/emailparser";
option java_package = "com.github.epifi.gamma.api.frontend.insights.emailparser";

service EmailParser {
  // RPC method to fetch the amount spent by a given user at the given merchant between a given date range.
  // It takes actor_id, start_date, end_date and merchant as input and outputs the amount spent and the number of orders made by the user at this merchant.
  // In case of error while processing the request, it will return INTERNAL error.
  // TODO(mohit) : add auth method option | https://monorail.pointz.in/p/fi-app/issues/detail?id=1569
  rpc GetUserSpendingData(GetUserSpendingDataRequest) returns (GetUserSpendingDataResponse){
    option (rpc.auth_required) = false;
  }
  // RPC method to track the status of a one-time full sync of user's previous mails.
  // It takes actor_id as input and outputs whether all the mails for the user have been synced or not.
  // In case any error is encountered while finding the status, this method return `INTERNAL` status.
  // Method returns `OK` status if sync status is successfully evaluated.
  rpc GetFullMailSyncProcessStatus(GetFullMailSyncProcessStatusRequest) returns (GetFullMailSyncProcessStatusResponse);

  // UnlinkEmailAccount can be used to revoke read access to an email account.
  // It returns OK status if oauth access is revoked successfully and mail entry is deleted from our DB
  // If any error is encountered in the process, INTERNAL status is returned
  rpc UnlinkEmailAccount(UnlinkEmailAccountRequest) returns (UnlinkEmailAccountResponse);
}

message GetUserSpendingDataRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  string actor_id = 2                         [(validate.rules).string.min_len = 1];

  google.type.Date from_date = 3              [(validate.rules).message.required = true];

  google.type.Date to_date = 4                [(validate.rules).message.required = true];

  insights.emailparser.Merchant merchant = 5;

}

message GetUserSpendingDataResponse {
  rpc.Status status = 1;

  float amount_spent = 2;

  int32 total_orders = 3;

  enum Status {
    // Success
    OK = 0;

    // internal server error. Can be due to various reason e.g. DB unavailable,
    INTERNAL = 1;
  }
  frontend.header.ResponseHeader resp_header = 15;
}

message GetFullMailSyncProcessStatusRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

message GetFullMailSyncProcessStatusResponse {
  enum Status {
    // Success
    OK = 0;

    // internal server error. Can be due to various reason e.g. DB unavailable
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  bool sync_complete = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

message UnlinkEmailAccountRequest {
  frontend.header.RequestHeader req = 15;
  // email id to be unlinked
  string email_id = 1 [(validate.rules).string.min_len = 1];
}

message UnlinkEmailAccountResponse {
  enum Status {
    // Successfully fetched and stored access credentials
    OK = 0;
    // internal server error. Can be due to various reason e.g. DB unavailable, error fetching data from google services
    INTERNAL = 13;
  }
  frontend.header.ResponseHeader resp_header = 15;
}
