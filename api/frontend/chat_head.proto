syntax = "proto3";

package frontend;

import "api/typesv2/common/image.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/phone_number.proto";

option go_package = "github.com/epifi/gamma/api/frontend";
option java_package = "com.github.epifi.gamma.api.frontend";

message ChatHead {
  // name of entity - (user/ merchant/ account/ reward) with which the transaction happened
  string full_title = 1;

  // due to limited real estate on the screen
  // in case the title length is bigger than client
  // can accommodate, in such cases chat head title
  // can be split into two different lines.
  string title_line_1 = 2;

  string title_line_2 = 3;


  // optional: icon image to be displayed with chat head
  // e.g. for a internal user chat head, icon image url can
  // correspond to the profile image url
  api.typesv2.common.Image icon_image = 4;

  // optional: in case icon image is missing
  // then the client needs to generate the image
  // using name full_title and color_code as background
  string colour_code = 5;

  // deeplink to the screen to which user has to be taken
  // on clicking on chatHead
  frontend.deeplink.Deeplink deeplink = 6;

  // optional: badge icon to be displayed with chat head
  // e.g. if the user PSP is paytm , than badge of paytm will be displayed with chat head
  api.typesv2.common.Image badge_image = 7;

  // contains user related params like vpa, phone number etc.
  UserParams user_params = 8;

  // list of badges for the chat head
  repeated Badge badges = 9;
}

// contains user related params like vpa, phone number etc.
message UserParams {
  // upi Id of the user to be displayed in the chat head (Non-Fi user)
  string vpa = 1;
  // phone number of the Fi user (nil for Non-Fi user)
  // will be populated only if the search was initiated via phone number
  api.typesv2.common.PhoneNumber phone_number = 2;
  // Central mapper id from which VPA was resolved
  // This field will be populated when mapper search is performed to get VPA
  string cm_id = 3;
}

message Badge {
  // title of the badge
  string title = 1;
}
