// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package frontend.search.widget;

option go_package = "github.com/epifi/gamma/api/frontend/search/widget";
option java_package = "com.github.epifi.gamma.api.frontend.search.widget";

enum DisplayType {
  DISPLAY_TYPE_UNSPECIFIED = 0;
  // flex display for trending search
  FLEX = 1;
}

// amount badge to be shown on client
// can be Credit / debit / strikethrough
enum AmountBadge {
  AMOUNT_TYP_UNSPECIFIED = 0;
  // credit badge
  CREDIT = 1;
  // debit badge
  DEBIT = 2;
  // strikethrough
  STRIKETHROUGH = 3;
  // savings
  SAVINGS = 4;
}

enum Cta {
  CTA_UNSPECIFIED = 0;
  // link to gmail sync cta
  CTA_SYNC_GMAIL = 1;
}

// top level filter fields for txn page search
// filter fields contain options (see `Option` message) for user to select from
// example: if in `MERCHANT` filter ["ola", "swiggy"] options are selected
//          results will be (transactions_ola) OR (transactions_swiggy)
// if 2 filters are selected i.e. `TIME` filter has values (20-01-2020 - 20-02-2020) along with `MERCHANT` filter
// results will be
// ((transactions_ola) OR (transactions_swiggy)) AND (transactions from 20-01-2020 to 20-02-2020)
enum FilterField {
  FILTER_FIELD_UNSPECIFIED = 0;
  // `MERCHANT` filter field will filter the transactions for selected merchants only
  MERCHANT = 1;
  // this filter tells the user_account for which transactions are to be shown
  ACCOUNT = 2;
  // the `to_people` filter which will filter transactions to specified contact
  PEOPLE = 3;
  // from_time to to_time filter
  TIME = 4;
  // this will filter out transactions for a selected location
  LOCATION = 5;
  // from_amount to to_amount filter
  AMOUNT = 6;
  // this will be applied on the user tagged transactions
  CATEGORY = 7;
  TYPE = 8;
}

// content type to be used in summary
// ContentType to be used to show in summary v2 row.
enum ContentType {
  CONTENT_TYPE_UNSPECIFIED = 0;
  // if content type is qr code -> client will fetch QR code for the user
  QR_CODE = 1;
  // This is text content. Text to show will be sent from the backend
  TEXT = 2;
  // Referral_id, qr code for referral needs to be fetched at client side
  REFERRAL_ID = 3;
  // different handling is needed from client if content type is graph
  GRAPH = 4;
  // When offer code needs to be shown in the summary card
  OFFER_CODE = 5;
  // When image will be displayed in the summary card
  IMAGE = 6;
}

enum CTAType {
  CTA_TYPE_UNSPECIFIED = 0;
  COPY = 1;
  DEEPLINK = 2;
  QR_CODE_SHARE = 3;
}

enum CtaDisplayTheme {
  CTA_DISPLAY_THEME_UNSPECIFIED = 0;
  PRIMARY = 1;
  SECONDARY = 2;
}

enum RelatedQueriesCriteria {
  RELATED_QUERIES_CRITERIA_UNSPECIFIED = 0;
  CRITERIA_TIME = 1;
  CRITERIA_CATEGORY = 2;
  CRITERIA_MERCHANT = 3;
}

enum DeeplinkElementType {
  DEEPLINK_ELEMENT_TYPE_UNSPECIFIED = 0;
  DEEPLINK_ELEMENT_TYPE_PAY = 1;
}
