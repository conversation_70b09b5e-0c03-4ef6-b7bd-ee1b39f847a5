syntax = "proto3";

package frontend.search.preview_page;

option go_package = "github.com/epifi/gamma/api/frontend/search/preview_page";
option java_package = "com.github.epifi.gamma.api.frontend.search.preview_page";

import "api/frontend/search/preview_page/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "api/typesv2/common/text.proto";

// Figma: https://www.figma.com/file/DHriq5fJTvQsm7mvhWjPLL/%F0%9F%94%8D-Search-%E2%80%A2-Workfile?node-id=4515%3A33242&t=qTNTVTFa0RN6tojk-0

message Layout {
  // This will contain the data of each section to be shown on the preview page.
  repeated Component components = 1;
  string sub_title = 2;
}

message Component {
  ComponentType type = 1;
  oneof Content {
    QuickActionWidget quick_action = 2;
    TransactionFetcherWidget transaction_fetcher = 3;
  }
  string kind = 10;
}

message QuickActionWidget {

}

message TransactionFetcherWidget {
  WidgetHeader header = 1;
  repeated TransactionFetcherWidgetView widget_views = 2;
  // This defines the maximum number of widget_view that client will show on the screen.
  // The remaining will be hidden, user will be able to see them on clicking a button at the bottom of the widget.
  uint32 max_views_allowed = 4;
  // When show_all is set, client will show all the views contained inside the widget,
  // irrespective of the value in max_views_allowed field.
  bool show_all = 3;
}

// Contains header details of a widget
message WidgetHeader {
  string title = 1;
  string bg_color = 2;
  string query = 5;
  ChatHeadIcons header_icon = 3;
  frontend.deeplink.Deeplink header_deeplink = 4;
  // Indicates the number of result items available
  // eg) 12
  // figma: https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=6836-32590&mode=design&t=LmovJjjWF4GVsvMO-4
  api.typesv2.common.Text result_count = 6;

  // Accessory view on the right side of the header
  // eg) redirection button (with right chevron icon)
  // figma: https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=6836-32590&mode=design&t=LmovJjjWF4GVsvMO-4
  api.typesv2.ui.IconTextComponent accessory_view = 7;
}

// Chat head icon details
message ChatHeadIcons {
  string primary_icon_url = 1;
  string secondary_icon_url = 2;
  string title = 3;
  string bg_color = 4;
}

// Contains data of a widget row
message TransactionFetcherWidgetView {
  string title = 1;
  string sub_title = 2;
  ChatHeadIcons header_icon = 3;
  frontend.deeplink.Deeplink widget_deeplink = 4;
  // Accessory view on the right side of the WidgetView
  // eg) redirection icon (with right chevron icon) or
  // vertically stacked information like `44.96%` and `In 3 Yrs`
  // figma: https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=6836-32590&mode=design&t=LmovJjjWF4GVsvMO-4
  api.typesv2.ui.VerticalKeyValuePair accessory_view = 7;
}
