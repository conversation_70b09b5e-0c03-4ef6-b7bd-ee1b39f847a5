// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package frontend.search.txn;

import "api/frontend/search/widget/enums.proto";
import "api/frontend/search/widget/widgets.proto";

option go_package = "github.com/epifi/gamma/api/frontend/search/txn";
option java_package = "com.github.epifi.gamma.api.frontend.search.txn";

message RequestFilter {
  // filter field selected by the user
  frontend.search.widget.FilterField filter_field = 1;
  // option that is selected by the user
  repeated frontend.search.widget.FilterValue options = 2;
  // if this is range facet, it will have range values
  frontend.search.widget.Range range = 3;
}
