syntax = "proto3";

package frontend.user;

import "api/typesv2/user.proto";

option go_package = "github.com/epifi/gamma/api/frontend/user";
option java_package = "com.github.epifi.gamma.api.frontend.user";

message UserPreference {
  // unique identifier of type uuid
  string id = 1;
  // type of the user preference
  PreferenceType preference_type = 2;
  // value for this preference_type
  PreferenceValue preference_value = 3;
}

message PreferenceTypeValuePair {
  // type of the user preference
  PreferenceType preference_type = 1;
  // value for this preference_type
  PreferenceValue preference_value = 2;
}

// User preference type
enum PreferenceType {
  PREFERENCE_TYPE_UNSPECIFIED = 0;
  // The language preferred for voice call
  PREFERENCE_TYPE_PREFERRED_CALL_LANGUAGE = 1;
  // The call languages suggested by the user which are currently unsupported by Fi
  PREFERENCE_TYPE_SUGGESTED_CALL_LANGUAGE = 2;
  // Preference type comms for a user
  PREFERENCE_TYPE_COMMS = 3;
}

message PreferenceValue {
  oneof pref_val {
    // call language preference - the list of supported languages preferred by the user
    // There is no order of priority for the list of languages
    // List is used instead of single language to help optimize agent allocation.
    LanguagePreferenceOrder preferred_call_language = 1;
    // call language suggestion - the list of unsupported languages suggested by the user
    // There is no order of priority for the list of languages
    LanguagePreferenceOrder suggested_call_language = 2;
    // These are communication preferences which are set for a user.
    // User shall be able to set the preference from device/UI to receive notification comms.
    CommsPreference comms_preference = 3;
  }
}

// General struct for language preferences.
message LanguagePreferenceOrder {
  // List of languages preferred by the user.
  // Whether or not priority ordering is assumed depends on the use case. eg: for call language there is no order of priority
  repeated api.typesv2.Language preferred_languages = 1;
}

// Preference for user comms
message CommsPreference {
  // Comms preferences list, for example user could have disabled notifications via app settings.
  // This would be a list object specifying Area as ALL, Medium as Notification
  // and comms signal corresponding to this will be OFF
  repeated CommsPreferenceInfo comms_preference_infos = 1;
}

// Detailed information on each preference set by user on their communication preferences
message CommsPreferenceInfo {
  // Area specific communication.
  Area area = 1;
  // Communication Medium - Notification etc...
  Medium medium = 2;
  // Preference signal for an area and medium
  CommsSignal signal = 3;
}

// Comms area specifies the area for which comms is being sent.
enum Area {
  AREA_UNSPECIFIED = 0;
  ALL = 1;
}

// Preference of the user for receiving communication
enum CommsSignal {
  COMMS_SIGNAL_UNSPECIFIED = 0;
  // Signals that user has opted in for receiving comms
  ON = 1;
  // Signals that user has denied for receiving comms
  OFF = 2;
}

// Specifies the medium on which the message is to be sent
enum Medium {
  MEDIUM_UNSPECIFIED = 0;
  // Specifies that a notification has to send to user's device
  NOTIFICATION = 1;
}
