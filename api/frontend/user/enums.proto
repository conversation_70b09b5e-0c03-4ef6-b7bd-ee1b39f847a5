// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package frontend.user;

option go_package = "github.com/epifi/gamma/api/frontend/user";
option java_package = "com.github.epifi.gamma.api.frontend.user";

// AccountValidationFailureReason describes why bank account detail could not be added for user
// frontend equivalent of savings.extacct.FailureReason
enum AccountValidationFailureReason {
  ACCOUNT_VALIDATION_FAILURE_REASON_UNSPECIFIED = 0;
  ACCOUNT_VALIDATION_FAILURE_REASON_API_TIMEOUT = 1;
  ACCOUNT_VALIDATION_FAILURE_REASON_USER_GIVEN_NAME_MISMATCH = 2;
  ACCOUNT_VALIDATION_FAILURE_REASON_NAME_AT_BANK_MISMATCH = 3;
  ACCOUNT_VALIDATION_FAILURE_REASON_INVALID_IFSC = 4;
  ACCOUNT_VALIDATION_FAILURE_REASON_INVALID_ACCOUNT_NUMBER = 5;
  ACCOUNT_VALIDATION_FAILURE_REASON_ACCOUNT_CLOSED = 6;
  ACCOUNT_VALIDATION_FAILURE_REASON_IMPS_NOT_ALLOWED_FOR_BANK = 7;
  // Unknown vendor response, usually Invalid ID Number or Combination of Inputs from Karza
  ACCOUNT_VALIDATION_FAILURE_REASON_UNKNOWN = 8;
  // user entered account number is same as of fi account number
  ACCOUNT_VALIDATION_FAILURE_SAME_ACCOUNT_NUMBER = 9;
}

// AccountAction - defines all the actions a user can take for a given account
enum UserAccountAction {
  USER_ACCOUNT_ACTION_UNSPECIFIED = 0;
  USER_ACCOUNT_ACTION_REQUEST_CHEQUEBOOK = 1;
  USER_ACCOUNT_ACTION_FREEZE_ACCOUNT = 2;
  USER_ACCOUNT_ACTION_CLOSE_ACCOUNT = 3;
  USER_ACCOUNT_ACTION_DISCONNECT_ACCOUNT = 4;
  USER_ACCOUNT_ACTION_SET_AS_PRIMARY_ACCOUNT = 5;
  USER_ACCOUNT_ACTION_DEACTIVATE_UPI_PAYMENTS = 6;
  USER_ACCOUNT_ACTION_DELETE_ACCOUNT = 7;
}

// state of actions a user can take
enum UserAccountActionState {
  USER_ACCOUNT_ACTION_STATE_UNSPECIFIED = 0;
  USER_ACCOUNT_ACTION_STATE_ENABLED = 1;
  USER_ACCOUNT_ACTION_STATE_DISABLED = 2;
  USER_ACCOUNT_ACTION_STATE_COMING_SOON = 3;
}

// status of upi account
enum UpiAccountStatus {
  UPI_ACCOUNT_STATUS_UNSPECIFIED = 0;
  UPI_ACCOUNT_STATUS_ACTIVE = 1;
  UPI_ACCOUNT_STATUS_INACTIVE = 2;
}

// UI entry points on app
enum EntryPoint {
  ENTRY_POINT_UNSPECIFIED = 0;
  ENTRY_POINT_PROFILE = 1;
  ENTRY_POINT_ACCOUNT_MANAGER = 2;
  ENTRY_POINT_USER_SESSION_DETAILS = 3;
}

// state of cta to be shown with upi action
enum UpiActionCtaState {
  UPI_ACTION_CTA_STATE_UNSPECIFIED = 0;
  UPI_ACTION_CTA_STATE_ENABLED = 1;
  UPI_ACTION_CTA_STATE_DISABLED = 2;
}
