// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package frontend.user.user_activity;

option go_package = "github.com/epifi/gamma/api/frontend/user/user_activity";
option java_package = "com.github.epifi.gamma.api.frontend.user.user_activity";

enum UserActivity {
  USER_ACTIVITY_UNSPECIFIED = 0;
  // activity to trigger after user have seen
  // the UPI ETB(existing to bank) pin set message.
  UPI_ETB_MESSAGE_SEEN = 1;
}
