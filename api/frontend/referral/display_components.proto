syntax = "proto3";

package frontend.referral;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/ui/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/frontend/referral";
option java_package = "com.github.epifi.gamma.api.frontend.referral";

/*
  NOTE: PLEASE DON'T ADD NEW COMPONENTS IN THIS FILE.
  THESE WILL BE MOVED TO frontend/referral/component_details.proto
*/

message CTA {
  // text for the CTA
  api.typesv2.common.Text text = 1;
  // any image to be shown for the CTA, for e.g. '<' image for back button
  api.typesv2.common.Image image = 2;
  // field to decide whether the CTA is to be shown or not.
  // Though, the CTA will be still active i.e. the parent proto can utilise the action.
  bool is_visible = 3;
  // deeplink action to be performed
  deeplink.Deeplink deeplink_action = 4;
}

message CtaV1 {
  // text and image UI component for the CTA
  api.typesv2.ui.IconTextComponent icon_text_component = 1;
  // field to decide whether the CTA is to be shown or not.
  // Though, the CTA will be still active i.e. the parent proto can utilise the action.
  bool is_visible = 2;
  // we can have two kinds of CTAs, one that lead to some other screen based on the deeplink provided,
  // and other that will be used for sharing of finite code to other apps. hence oneof is used here.
  oneof action {
    // deeplink action to be performed
    deeplink.Deeplink deeplink_action = 3;
    // Share finite code action
    ShareFiniteCodeAction share_finite_code_action = 4;
  }
  // background color of CTA
  string BgColor = 7;
  // shadow
  api.typesv2.ui.Shadow shadow = 8;
}

message ShareFiniteCodeAction {
  api.typesv2.common.Text share_message = 1;
}
