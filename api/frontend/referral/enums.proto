syntax = "proto3";

package frontend.referral;

option go_package = "github.com/epifi/gamma/api/frontend/referral";
option java_package = "com.github.epifi.gamma.api.frontend.referral";

// ShareActionType: type of share action which can be performed, i.e. via different mediums
enum ShareActionType {
  SHARE_ACTION_TYPE_UNSPECIFIED = 0;
  // open whatsapp chat of the specific/particular contact directly and pre-fill the share message
  WHATSAPP = 1;
  // open message(app) chat of the specific/particular contact directly and pre-fill the share message
  MESSAGE = 2;
  // copy the content to clipboard
  COPY_TO_CLIPBOARD = 3;
  // open the system bottom sheet which has the options to share via any app
  INVITE_FRIENDS = 4;
  // todo: update here once behaviour is defined
  INSTAGRAM = 5;
  // open twitter app to "create a new post" and pre-fill the share message
  TWITTER = 6;
  // open whatsapp with the option to select the contact for sharing, i.e. NO specific/particular contact chat
  // to be opened
  WHATSAPP_NO_CONTACT = 7;
}

// ComponentUiState: enum to describe a UI effect for the component as a whole
enum ComponentUiState {
  // render the component with default UI effects
  COMPONENT_UI_STATE_UNSPECIFIED = 0;

  // fade the component associated with this enum
  FADED = 1;
}

// TabType: enum for various types of tabs for the client to consume
enum TabType {
  TAB_TYPE_UNSPECIFIED = 0;

  // "your referrals" tab on referrals v1 page
  REFERRALS_V1_YOUR_REFERRALS = 1;
  // "invite contacts" tab on referrals v1 page
  REFERRALS_V1_INVITE_CONTACTS = 2;
}

// MediaType: enum for various media types which can be used to specify for the media being
// shared.
// use case: for media to be shared along with share-code message for referral.
enum MediaType {
  // when no media is to be shared with the message
  MEDIA_TYPE_UNSPECIFIED = 0;
  // share the default image which client already has (or can generate at client side)
  DEFAULT_IMAGE = 1;
  // share custom image sent from BE
  CUSTOM_IMAGE = 2;
  // can enrich this tomorrow to add different types of medias
}

// ReferralLinkVendor: enum for vendors which can be used for generating referral links
enum ReferralLinkVendor {
  REFERRAL_LINK_VENDOR_UNSPECIFIED = 0;
  APPSFLYER_ONELINK = 1;
}

// ReferralLandingPageVersion: represents the referral landing pages
enum ReferralLandingPageVersion {
  // note: fallback to latest page if unspecified
  REFERRAL_LANDING_PAGE_VERSION_UNSPECIFIED = 0;
  // old referrals page, i.e. first page
  V0 = 1;
  // new referrals page, referrals-v1
  V1 = 2;
}

// IconAnimationType: type of animation to be shown for the share action icon
// If no animation is required, unspecified should be passed
enum IconAnimationType {
  // To be passed for no animation
  ICON_ANIMATION_TYPE_UNSPECIFIED = 0;
  // Animation type : <link to drive/figma here>
  TYPE_ONE = 1;
}

// This enum denotes the UI entry point from where claim finite code was called
// For example - enter referral code page during onboarding
enum ClaimFiniteCodeEntryPoint {
  CLAIM_FINITE_CODE_ENTRY_POINT_UNSPECIFIED = 0;

  // Entry point to be used when user clicks on submit after entering a referral code
  CLAIM_FINITE_CODE_ENTRY_POINT_ENTER_REFERRAL_CODE_PAGE = 1;

  // Entry point to be used when the client applies referral code for the user when they click on the continue button on popup shown to the user
  CLAIM_FINITE_CODE_ENTRY_POINT_POPUP = 2;
}

// OsShareFlowUseCaseType: enum for different use cases for sharing any content via OS share flow (ex- whatsapp, message etc)
enum OsShareFlowUseCaseType {
  OS_SHARE_FLOW_USE_CASE_TYPE_UNSPECIFIED = 0;
  // Use case for sharing referral code, if referral is not unlocked for the user then we will share generic app download URL
  SHARE_REFERRAL_CODE_WITH_FALLBACK = 1;
}
