syntax = "proto3";

package frontend.referral;

import "api/frontend/referral/display_components.proto";
import "api/frontend/referral/entities.proto";
import "api/frontend/referral/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/referral/share_action.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/frontend/referral";
option java_package = "com.github.epifi.gamma.api.frontend.referral";

// contains display details needed for rendering of home screen referrals widget.
// Since we wanted to support both new and old home screen widgets we have included
// details for both of them in this RPC's response. However, depending on the app version,
// one of them will be nil.
message HomeScreenReferralsWidgetDisplayDetails {
  // old home screen referrals widget. will be nil if client is on newer app version.
  // will be deprecated after new home is launched.
  // todo(divyadeep): deprecate old_home after new home is live
  api.typesv2.ui.IconTextComponent old_home_display_details = 1;
  // new home screen referrals widget. will be nil if client is on older app version.
  ReferralsWidgetV1 home_widget_v1 = 2;
  // [Nullable] Referrals V2 widget for new home screen with lottie support
  // if both home_widget_v1 and home_widget_v2 are present, home_widget_v2 should be used
  // figma: https://www.figma.com/design/yWz3So1ZU41Fv1EuWQNILH/Divyanshu's-Workfile?node-id=15077-6670&t=4qfhfj517jBgjHXk-1
  ReferralsWidgetV2 home_widget_v2 = 3;

  // referral widget to be shown on new home screen.
  message ReferralsWidgetV1 {
    // title of the referrals widget
    api.typesv2.common.Text title = 1;
    // main image to be used for referrals widget
    api.typesv2.common.Image main_image = 2;
    // description text to be shown on the referrals widget, like "Get your friends on Fi and get rewards of upto ₹1000"
    api.typesv2.common.Text description = 3;
    // CTAs to be shown on the widget. can include "Share code" button, and a button to go to the referrals landing screen
    repeated CtaV1 ctas = 4;
    // icon and text for social proofing to be shown under the widget, for e.g. "👥 Over 1.6L people have won over 5Cr already".
    // Note: Can be nil as well
    api.typesv2.ui.IconTextComponent social_proofing = 5;
  }

  message ReferralsWidgetV2 {
    // widget visual element, currently whole widget is shown as visual element
    api.typesv2.common.VisualElement widget_visual_element = 1;
    // deeplink for redirection on tapping the widget
    frontend.deeplink.Deeplink deeplink = 2;
  }
}

message TnC {
  // title like "Rewards TnCs"
  api.typesv2.common.Text title = 1;

  // subtitle like "Refer & earn up to Rs.3000 per referral
  api.typesv2.common.Text sub_title = 2;

  // main image to be shown on top
  api.typesv2.common.Image icon = 3;

  // background color of header section
  string bg_color = 4;

  repeated TncSection tnc_sections = 5;

  // different TnC sections
  message TncSection {
    // can be empty if no section title exists
    api.typesv2.common.Text section_title = 1;

    // tncs list
    repeated api.typesv2.common.Text bullet_points = 2;
  }
}

// share finite code component for V1 referrals page
message ShareCodeV1Info {
  // standalone code that can be used by client
  string finite_code = 1;
  // heading text to be shown which has code in it, for e.g. "Share your code: ABCDEFGH".
  // Note: this can be in HTML format to cater for multi-colored string.
  api.typesv2.common.Text text_with_code = 2;
  // background color of the section
  string bg_color = 3;

  // list of share options (order matters), i.e. Whatsapp, Message, Copy-to-Clipboard, Invite Friends etc
  repeated ShareActionInfo share_action_infos = 4;

  // background color scheme of the section.
  // To support the multi-color share-code section.
  repeated string bg_color_scheme = 5;

  message ShareActionInfo {
    // action to be performed after tapping on the share button
    ShareActionType share_action_type = 1;
    // button details, i.e. icon, name (can be empty), container bg-color etc
    api.typesv2.ui.IconTextComponent button = 2;
    // content to be shared via the action
    ShareContent share_content = 3;
    // Type of animation to be shown for the share action icon
    // Is a string type for the enum api/frontend/referral/enums.proto : IconAnimationType
    // Possible values -
    // ICON_ANIMATION_TYPE_UNSPECIFIED : default, no animation
    // TYPE_ONE : <insert drive/figma link for animation>
    string icon_animation_type = 4;

    message ShareContent {
      // list of messages to be shared.
      // Note: keeping it as a list so that we can share multiple texts/messages in the future*
      repeated string messages = 1;
      // type of media associated with this share action, i.e. default/custom/no image/some-other-media-form
      MediaType media_type = 2;
      // content of the media sent from BE.
      // Note: can be nil as well in cases when we'd want to share no media, or share image which the client already has
      oneof media {
        // custom image media
        api.typesv2.common.Image custom_image = 10;
      }

      enum MediaType {
        // when no media is to be shared with the message
        MEDIA_TYPE_UNSPECIFIED = 0;
        // share the default image which client already has (or can generate at client side)
        DEFAULT_IMAGE = 1;
        // share custom image sent from BE
        CUSTOM_IMAGE = 2;
        // can enrich this tomorrow to add different types of medias
      }
    }
  }
}

// can be used to describe empty section/component such as:
// 1. when no referees exist for a referrer
// 2. No contacts found for the referrer
// can be enriched tomorrow to add CTA etc
message EmptySection {
  // image for the section.
  // Note: can be nil
  api.typesv2.common.Image image = 1;
  // description of the section.
  // Note: can be nil
  api.typesv2.common.Text desc = 2;
  // bg color of the section
  string bg_color = 3;
}

// can be used for Tab functionality, such as Horizontal tabs
message TabsInfo {
  // list of tabs to be shown.
  // Note: behaviour of tapping on the tab will reside at the client side. For e.g., calling a specific RPC
  repeated Tab tabs = 1;
  // background color of the tabs section
  string bg_color = 2;

  message Tab {
    // ui details for the tab when its selected, i.e. icon, text (color), bg-color
    TabStateInfo tab_selected = 1;
    // ui details for the tab when its unselected, i.e. text (color)
    TabStateInfo tab_unselected = 2;
    // tab type, i.e. client can build their logic on this type, for e.g. specific RPC calls
    TabType tab_type = 3;

    message TabStateInfo {
      // name of the tab
      string name = 1;
      // color of the name
      string name_color = 2;
      // background color of the tab.
      // Note: can be empty as well when the background color of the parent is to be used
      string bg_color = 3;
      // icon url for the tab.
      // Note: can be empty as well
      string icon_url = 4;
    }
  }
}

// DialogPopup: can be used to show popups with multiple pairs of title-body stacked vertically.
message DialogPopup {
  // image to be shown on the top
  api.typesv2.common.Image image = 1;
  // infos to be shown in the dialog.
  // list of vertically aligned infos with title and body.
  // Note: If `body` of the info is empty, it shouldn't lead to extra gap/padding for the next `Info`.
  repeated Info infos = 2;

  message Info {
    // title for the info.
    // Note: Can be HTML as well to cater for multi-colored string
    api.typesv2.common.Text title = 1;
    // description/body of the info.
    // Note: Can be empty as well.
    api.typesv2.common.Text body = 2;
  }
}

// ReferralLinkGenerationInfo provides all the information needed to generate unique referral links
// at client side. It also mentions the placeholders to be replaced with the generated link.
message ReferralLinkGenerationInfo {
  // vendor for generation of link, for e.g. Onelink
  ReferralLinkVendor link_vendor = 1;
  // params to be used directly while generating referral link
  map<string, string> url_generation_params = 2;
  // vendor specific params which needs to be used by setter methods while generating link.
  // Note: can be nil as well if no vendor-specific params are required.
  oneof vendor_specific_params {
    OnelinkParams onelink_params = 3;
  }
  // placeholder key which has to be replaced with the referral link for share-code scripts.
  // Note: gap in numbering intentional to accommodate for more vendor specific params in the future.
  string referral_link_placeholder_key = 10;
  // fallback referral link to be used if vendor generation fails
  string fallback_referral_link = 11;

  message OnelinkParams {
    // template-id of the onelink, for e.g. "H5hv"
    string template_id = 1;
    // channel, to be set using the setter `setChannel`
    string channel = 2;
    // campaign, to be set using the setter `setCampaign`
    string campaign = 3;
  }
}

// DialogBoxV1Info component caters to the need of showing a dialog-box/popup/modal for the following:
// 1. making user perform an action before proceeding
// 2. displaying important info to the user
// Note: Any "loader" associated animation to be handled by client separately, for e.g. ● ● ◎
message DialogBoxV1Info {
  // image to be shown on the top of the section.
  // Note: animation (already present at client side) can be shown along with the image.
  api.typesv2.common.Image image = 1;
  // title, for e.g. "Yay! “FI200” applied"
  api.typesv2.common.Text title = 2;
  // text to be shown just below the title. It can have its own container within which this text exists.
  // for e.g. "You get up to ₹200 cashback".
  // Note: can be nil as well. In that case, the gap between the `title` and `desc` should be adjusted accordingly.
  api.typesv2.ui.IconTextComponent sub_title = 3;
  // description, for e.g. "Sign up & add funds to your Savings Account to claim this offer".
  // Note: can be nil. In that case, the padding/gap should be adjusted accordingly.
  api.typesv2.common.Text desc = 4;
  // dismiss/remove action associated with the dialog-box, for e.g. "Remove" button.
  // Note: Can be nil. In that case, the `accept` CTA takes full-width.
  api.typesv2.ui.IconTextComponent dismiss = 5;
  // accept/continue action associated with the dialog-box, for e.g. "Continue" button.
  api.typesv2.ui.IconTextComponent accept = 6;
  // background color
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 7;
}

// DefaultOfferCodeInfo component provides info to render the offer-codes on the referral screen during onboarding.
// These act as alternatives to finite-code which the user can enter.
message DefaultOfferCodeInfo {
  // offer code, for e.g. "<icon> FI200" or "<icon> FI2500"
  api.typesv2.ui.IconTextComponent code = 1;
  // title of the offer, for e.g. "Get up to ₹200 cashback"
  api.typesv2.common.Text offer = 2;
  // description of the offer, for e.g. "When you complete 3 UPI payments using Fi"
  api.typesv2.common.Text desc = 3;
  // "Apply now" CTA. Upon tap, it should open up the dialog-box saying the code is applied
  api.typesv2.common.Text apply_now_cta = 4;
  // dialog box to be shown upon applying the code
  DialogBoxV1Info code_applied_popup = 5;
  // background color
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 6;

  // code displayed to the user.
  // This can be used in properties of client events to convey the code applied by the user.
  string displayed_code = 12;
  // An actual finite-code associated with the offer-code.
  // If present, this can be used to make the ClaimFiniteCode API call when user confirms on the applied code.
  string underlying_finite_code = 13;
}

// ReferralCodeInputBottomSheet
message ReferralCodeInputBottomSheet {
  // title, for e.g. "Have a referral code?"
  api.typesv2.common.Text title = 1;
  // description, for e.g. "If you received an invite to Fi, enter the referral code below"
  api.typesv2.common.Text desc = 2;
  // placeholder text for the referral code input box, for e.g. "Enter code"
  api.typesv2.common.Text input_box_placeholder = 3;
  // icon to be used when submit button is enabled
  api.typesv2.common.Image submit_button_enabled_icon = 4;
  // icon to be used when submit button is disabled
  api.typesv2.common.Image submit_button_disabled_icon = 5;
  // subtext to be shown just below the referral code input box which should disappear as soon as the user enters anything
  // Ex - "Forgot referral code? Enter your friend's number 📞"
  // Note - this can be nil, in which case the subtext should not be shown
  api.typesv2.common.Text input_box_subtext = 6;

  /*
      fields to be used for loader state.
      Note, these fields don't account for the loader animation, i.e. ● ● ◎
   */
  // title for the loader state, for e.g. "Verifying your referral code"
  api.typesv2.common.Text loader_title = 10;
  // text below the title, for e.g. "This takes a few seconds ⏱️"
  api.typesv2.common.Text loader_sub_title = 11;
}

// EarningSummaryInfo describes the earnings from referrals. It can also show the potential earnings possible
// Use cases so far:
// 1. To show overall referrals earnings
// 2. To show the earnings from a particular referee
message EarningSummaryInfo {
  // main text / heading for the summary info.
  // Current use case: to show total earnings so far, for e.g. "You have earned Rs2000 so far!".
  // Note: HTML in response to cater for multi-colored string.
  api.typesv2.common.Text status = 1;
  // sub text for the summary info.
  // current use case: to show potential earnings possible, for e.g. "Upto Rs6000 processing".
  // Note: can be nil as well
  api.typesv2.ui.IconTextComponent sub_status = 2;
}

// RefereeActionInfo describes the action performed / to be performed by the referee. It shows the following:
// 1. Earning details
// 2. Expiry details of the action
// 3. Operation/action that can be performed for the particular action, for e.g. Remind the referee, Claim-reward if any
message RefereeActionInfo {
  // icon for the action
  api.typesv2.common.Image icon = 1;
  // background colour for the action info image
  string icon_bg_colour = 4;
  // title, for e.g. "Add ₹3,000 to account"
  api.typesv2.common.Text action_title = 2;
  // description, for e.g. "They get ₹100, you get ₹150 • 7 days left".
  // Note: can have HTML to cater for multi-colored string.
  // Note: can be nil.
  api.typesv2.common.Text action_desc = 3;
  // reward & reminder related info associated with the referee. currently it can be:
  // 1. action to claim the reward.
  // 2. OR, the claimed reward info and status.
  // 3. OR (if the referee has not yet completed the action), a Remind CTA for the referee
  // Note: Can be nil as well for cases where the Action has expired.
  oneof reward_info {
    // if the referrer is yet to claim the reward earned because of this referee
    api.typesv2.ui.IconTextComponent claim_reward_cta = 11;
    // reward details for the reward claimed by the referee, for e.g. "₹100 Earned"
    RewardV1 reward = 12;
    // remind action for the particular referee
    ShareActionInfo remind_referee_action = 13;
  }
}
