// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package frontend.actoractivity;

import "api/accounts/account_type.proto";
import "api/frontend/connected_account/features/consent_renewal.proto";
import "api/frontend/connected_account/features/fi_to_fi.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/search/widget/widgets.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/visual_element.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/ui/icon_text_component.proto";


option go_package = "github.com/epifi/gamma/api/frontend/actoractivity";
option java_package = "com.github.epifi.gamma.api.frontend.actoractivity";

// Frontend service to manage actor activities for actors
// This can include RPC methods to
//    * Fetch all the actor activities
service ActorActivity {
  // A paginated rpc to fetch the activities belonging to a combination of actor and
  // accountIds present in the request
  // If page size is not passed in the request, the default page size is 30
  //
  // Along with activity response contains
  // before_token, after_token representing the location of page fetched.
  //
  // Currently all the activity details will be fetched from order
  //
  // NOTE - one of the account filter or the card ids should be present
  // if both account filters and card ids are present, all the activities associated with the account
  // and the card will be returned.
  rpc GetActivities (GetActivitiesRequest) returns (GetActivitiesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }


  // rpc to return similar transactions count with text to display
  rpc GetSimilarActivities (GetSimilarActivitiesRequest) returns (GetSimilarActivitiesResponse) {
    option (rpc.auth_required) = true;
  }
}

message GetActivitiesRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // A page token is generated after base64 encoding a JSON serialize string. This can contain any data relevant to the
  // server in order to send the next page.
  // e.g. {last_activity_timestamp: last_order_timestamp: } can be returned as after token
  // and {first_activity_timestamp: first_order_timestamp: } can be returned as before token
  oneof token {
    // For cases when client is un-aware of page token. This can be due to various reasons
    // e.g. DB corruption, app uninstall etc.
    // Client can set this flag to fetch the last page (latest actor activities).
    // In this case activities will be returned in DESCENDING order of activity time from current timestamp.
    // Note- this flag will always return the last page of the activities (latest activities).
    // Further pages has to be fetched using the after_token/before token.
    bool latest_page = 3;

    // before token is to be passed if client wants to fetch activities that happened before previous page.
    // in this case the activities will be ordered in DESCENDING order of activity time.
    string before_token = 4;

    // after token is to be passed if client wants to fetch activities that happened after previous page.
    // in this case the events will be ordered in ASCENDING order of activity time.
    string after_token = 5;
  }

  // number of activities to be returned in the response
  int32 page_size = 6;

  // marking this deprecated because the message name contains "filter" keyword which can be
  // confusing when we actually implement filters.
  message AccountFilter {
    option deprecated = true;
    // account Id to filter the results
    string account_id = 1;
    // type of the account for the account id
    accounts.Type account_type = 2;
  }
  // list of accounts to filter the results
  // only activities associated with the account id in these filters will be returned
  repeated AccountFilter account_filter = 7 [deprecated = true];

  message Account {
    // account Id
    // account_id is deprecated use derived_account_id
    string account_id = 1 [deprecated = true];
    // type of the account for the account id
    accounts.Type account_type = 2;
    // derived_account_id stores base64 encoding for DerivedAccountId which contains different account ids
    // like tpap_account_id, connected_account id etc.
    string derived_account_id = 3;
  }

  message CardCriteria {
    repeated string debit_card_ids = 1;
  }
  message AccountCriteria {
    repeated Account accounts = 1;
  }

  // Search is done as a first step to get all activities
  // TODO: On top of it, FilterCriteria can be applied to return a subset of data.
  // NOTE - one of the account filter or the card ids should be present
  // if both account filters and card ids are present, all the activities associated with the account
  // and the card will be returned.
  oneof SearchCriteria {
    // debit card Ids to identify and return all activities for a debit card.
    CardCriteria debit_card_criteria = 8;
    // account identifiers to identify and return all activities for a given account
    AccountCriteria account_criteria = 9;
  }

  message SearchQueryParams {
    // Search query in string format.
    string query = 1;
  }

  // This works along with [SearchCriteria] for eg: Selecting HDFC Account and
  // searching for swiggy, will retun transactions with swiggy in them from the
  // HDFC Account selected.
  // When no query parmas are provided, it will return all transactions, with the
  // provided [SearchCriteria].
  SearchQueryParams query_search = 10;
}

message GetActivitiesResponse {
  enum Status {
    // rpc successful
    OK = 0;
    // no activity found for the actor
    RECORD_NOT_FOUND = 5;
    // actor does not have access to one or more accounts
    PERMISSION_DENIED = 7;
    // Internal error while processing the request
    INTERNAL = 13;
    // In case page token is corrupted. The server can't determine which page to return.
    PAGE_TOKEN_CORRUPTED = 100;
    // In case AA consent of all accounts requested are expired
    AA_CONSENT_EXPIRED = 101;
    // This status code is returned when user has not connected Fi to Fi via connected account and hence all transactions page is blocked
    // This is deprecated due to non capability of handling custom success code at android side, we will continue to use only
    // OK status code for success cases.
    AA_CONNECT_FI_TO_FI = 102 [deprecated = true];
  }
  // rpc status for the request
  rpc.Status status = 1;

  // each transaction section includes - title, []transaction, link_to_view_all
  repeated frontend.search.widget.TransactionsWidget transactions = 2;

  // Server returns two page tokens/cursors. The client is expected to pass
  // the token in subsequent request without altering it.

  // before_token to be used when client wants to fetch activities that happened before the current page.
  string before_token = 3;

  // after_token to be used when client wants to fetch activities that happened after the current page.
  string after_token = 4;

  // fi_to_fi_bottom_sheet contains all the bottom sheet parameters for initiating Fi to Fi flow to connect Fi Federal
  // bank savings account, this bottom sheet contains arguments used to display bottom sheet info along with CTA which contains
  // screen name that will be used to initialise SDK for Fi to Fi flow
  // figma:https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?type=design&node-id=10505-118757&t=Dofku7gqaVuhUWZu-0
  frontend.connected_account.features.InitiateFiToFiFlowBottomSheet initiate_fi_to_fi_flow_bottom_sheet = 5;

  // https://www.figma.com/file/TGQyVK5oTGx2xqp6s691Ib/%F0%9F%94%97-Workfile-%2F-Connected-Accounts?node-id=9226%3A106587&mode=dev
  // ConsentRenewalWidget is loaded if the aa consent of all the accounts present in the request is expired
  // Transactions field will be empty if this field is populated
  frontend.connected_account.features.AaConsentRenewalWidget consent_renewal_widget = 6;

  // partner tag to be used to show partner info. E.g. POWERED BY UPI BHIM
  api.typesv2.common.VisualElement partner_tag = 7;

  // figma: https://www.figma.com/file/Bc8Y41Lgv4YMmX00n8V9ZU/%F0%9F%8E%81-Rewards-%E2%80%A2-Workfile-2?type=design&node-id=19845-18378&mode=design&t=fy2leCkAT2zYKaeL-0
  // [Optional] Widget which provides information about all the rewards earned for current calendar month
  // to be shown on All transaction page top section.
  // RewardSummaryWidget will be empty if the rewards earned are zero for current calendar month.
  frontend.search.widget.RewardSummaryWidget rewards_summary_widget = 8 [deprecated = true];

  // Banner notification that appears at the top of the screen to display important announcements or updates
  // The banner can include an icon, text, and an optional call-to-action with deeplink
  api.typesv2.ui.IconTextComponent header_banner = 16;

  frontend.header.ResponseHeader resp_header = 15;
}


message GetSimilarActivitiesRequest {
  frontend.header.RequestHeader req = 1;
  string activity_id = 2;

  // A page token is generated after base64 encoding a JSON serialize string. This can contain any data relevant to the
  // server in order to send the next page.
  // e.g. {last_activity_timestamp: last_order_timestamp: } can be returned as after token
  // and {first_activity_timestamp: first_order_timestamp: } can be returned as before token
  oneof token {
    // For cases when client is un-aware of page token. This can be due to various reasons
    // e.g. DB corruption, app uninstall etc.
    // Client can set this flag to fetch the last page (latest actor activities).
    // In this case activities will be returned in DESCENDING order of activity time from current timestamp.
    // Note- this flag will always return the last page of the activities (latest activities).
    // Further pages has to be fetched using the after_token/before token.
    bool latest_page = 3;

    // before token is to be passed if client wants to fetch activities that happened before previous page.
    // in this case the activities will be ordered in DESCENDING order of activity time.
    string before_token = 4;

    // after token is to be passed if client wants to fetch activities that happened after previous page.
    // in this case the events will be ordered in ASCENDING order of activity time.
    string after_token = 5;
  }
  // end timestamp before which the last x duration of months is considered for similar activity
  // NOTE: this needs to be same as the timestamp in the GetSimilarActivitiesCountDetails request
  google.protobuf.Timestamp end_timestamp = 6;
}

message GetSimilarActivitiesResponse {
  frontend.header.ResponseHeader resp_header = 15;
  // each transaction section includes - title, []transaction, link_to_view_all
  repeated frontend.search.widget.TransactionsWidget transactions = 2;

  // Server returns two page tokens/cursors. The client is expected to pass
  // the token in subsequent request without altering it.

  // before_token to be used when client wants to fetch activities that happened before the current page.
  string before_token = 3;

  // after_token to be used when client wants to fetch activities that happened after the current page.
  string after_token = 4;
}
