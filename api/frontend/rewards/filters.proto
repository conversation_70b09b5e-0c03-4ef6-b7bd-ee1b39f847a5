syntax = "proto3";

package frontend.rewards;

import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";

option go_package = "github.com/epifi/gamma/api/frontend/rewards";
option java_package = "com.github.epifi.gamma.api.frontend.rewards";

message CatalogTagFilter {
  string tag_name = 1;

  // display button for tag
  api.typesv2.ui.IconTextComponent inactive_filter_cta = 2;

  // updated text filter is active
  api.typesv2.ui.IconTextComponent active_filter_cta = 3;
}

// Catalog filter based on the tag name in vertical icon text component fashion
// Figma : https://www.figma.com/design/bbZLUNkW0yfw62FRhQHI6x/Rewards-Centre-%E2%80%A2-FFF-%E2%80%A2-v1.0?node-id=11166-9078&node-type=frame&t=atMKeYE21UGC0HUf-0
message VerticalCatalogTagFilter {
  // name of the tag
  string tag_name = 1;

  // cta when tag is inactive
  api.typesv2.ui.VerticalKeyValuePair inactive_filter_cta = 2;

  // cta when tag is active
  api.typesv2.ui.VerticalKeyValuePair active_filter_cta = 3;
}

message CatalogSortOption {
  // name of option to sort on
  string sort_by = 1;

  api.typesv2.ui.IconTextComponent sort_option_cta = 2;
}

enum SortBy {
  UNSPECIFIED_SORT_BY = 0;

  // when we want to sort based on redemption price of an offer
  REDEMPTION_PRICE_ASC = 1;
  REDEMPTION_PRICE_DESC = 2;
}

message CatalogFilters {
  // list of tag names to filter offers on.
  repeated string tags = 1;
  // marked category_tags as reserved in favour of tags as tags and category tags are backend constructs
  // and client can just use tags to pass both
  reserved 2;
}

// offer category, used for fetching offers of any specific category like ALL offers, PROMOTED offers etc.
enum OfferCategory {
  OFFER_CATEGORY_UNSPECIFIED = 0;
  // all offers
  ALL_OFFERS = 1;
  // promoted offers. ex- some special card offers tile shown on credit card landing page
  PROMOTED_OFFERS = 2;
}
