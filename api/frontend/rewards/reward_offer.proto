syntax = "proto3";

package frontend.rewards;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/frontend/rewards";
option java_package = "com.github.epifi.gamma.api.frontend.rewards";

// type to be sent to UI so that offers can be displayed differently
enum DisplayType {
  UNSPECIFIED_DISPLAY_TYPE = 0;
  HEADLINE = 1;
  FRINGE = 2;
}

// this contains display related configs
message DisplayDetails {
  message CTA {
    string name = 1;
    deeplink.Deeplink deeplink = 2;
  }
  DisplayType display_type = 1;
  string title = 2;
  // Deprecated in favour of steps_v1
  repeated string steps = 3 [deprecated = true];
  // Deprecated in favour of tncs_v1
  repeated string tncs = 4 [deprecated = true];
  string icon = 5;
  string bg_color = 6;
  CTA cta = 7;
  // tag text displayed on rewardOffer tile eg 'NEW','COMPLETED' etc.
  string tile_tag_text = 8;
  // background color of tile tag.
  string tile_tag_bg_color = 9;
  // steps and tncs info
  repeated InfoPoint steps_v1 = 10;
  repeated InfoPoint tncs_v1 = 11;
  // tile_bg_color to control the background color of rewardOffer tile.
  // if this field is null a default bg color should be used to ensure backward compatibility.
  string tile_bg_color = 13;

  message InfoPoint {
    // info text
    api.typesv2.common.Text text = 1;
    // optional, for deep-linking text to a screen/flow
    deeplink.Deeplink deeplink = 2;
  }
}

// RewardOfferInventory contains information of total and already claimed count of a reward offer.
message RewardOfferInventory {
  // total number of times the offer is already claimed.
  uint32 claimed_count = 1;
  // total number of offer that were initially added to the inventory.
  uint32 total_count = 2;
}

message RewardOffer {
  // identifier of the offer, this will be referenced to a reward
  string id = 1;
  // display related details
  DisplayDetails display_details = 2;
  // inventory info for the reward offer
  // can be nil in cases where inventory is infinite
  RewardOfferInventory inventory = 3;
}



