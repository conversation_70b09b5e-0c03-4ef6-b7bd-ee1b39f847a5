syntax = "proto3";

package frontend.rewards;

import "api/frontend/rewards/display_components.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/vertical_key_value_pair.proto";
import "google/protobuf/timestamp.proto";
import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/frontend/rewards";
option java_package = "com.github.epifi.gamma.api.frontend.rewards";

// Exchanger Order Status corresponding to backend order status. Used in frontend
enum ExchangerOrderStatus {
  UNSPECIFIED_ORDER_STATUS = 0;
  UNCLAIMED = 1;
  USER_INTERVENTION_REQUIRED = 2;
  IN_PROGRESS = 3;
  FULFILLED = 4;
  FAILED = 5;
}

// ExchangerOfferRedemptionCurrency denotes currency that can be used for redeeming an ExchangerOffer.
enum ExchangerOfferRedemptionCurrency {
  EXCHANGER_OFFER_REDEMPTION_CURRENCY_UNSPECIFIED = 0;
  EXCHANGER_OFFER_REDEMPTION_CURRENCY_FI_COINS = 1;
}

// fields to describe the exchanger-order-option
message ExchangerOrderOption {
  // unique id of the option under the order
  string id = 1;
  message Display {
    // title like "Rs 8"
    string title = 1;
    // subtitle like "Cashback"
    string subtitle = 2;
    string image_url = 3;
    // desc visible on reward details page, e.g. "Cashback won by trading 500 Fi-Coins"
    string desc = 4;
    AdditionalDetails additional_details = 5;
    repeated DynamicFields dynamic_fields = 6;

    // additional details required to display information (like "TnCs", "Next steps", etc)
    message AdditionalDetails {
      string banner_logo_url = 1;
      string banner_image_url = 2;
      // for e.g. "Sony Playstation 5 Console"
      string banner_title = 3;
      // for e.g. "1 Playstation 5 delivered at your doorstep free of charge. Game on!"
      string desc = 4;

      repeated string next_steps = 5;
      repeated string tncs = 6 [deprecated = true];

      // text to show on order-card under "My Orders" page if order is in USER_INTERVENTION_REQUIRED, REWARD_FULFILLMENT_INITIATED state
      string intermediate_state_text = 7;
      string banner_bg_color = 8;

      // tncs with HTML support
      repeated api.typesv2.common.Text tncs_v2 = 9;
    }
    // DynamicFields can be used to send additional information that may be required for this particular order
    // (like shipping address for a physical merchandise order)
    // It helps in showing a new field on the APP for already supported offer types
    // without requiring any client side change.
    message DynamicFields {
      string name = 1;
      string value = 2;
      bool is_copyable = 3;
    }
  }
  Display display_details = 2;
}

message ExchangerOrder {
  // id of the order, i.e. attempted redemption
  string id = 1;

  // FE maintained status of the order
  ExchangerOrderStatus status = 2;

  // all the available options to choose from
  repeated ExchangerOrderOption options = 3;

  // chosen option
  // this won't be present if the option is yet to be chosen
  // use-case: To show this instead of the "options" if its already claimed
  ExchangerOrderOption chosen_option = 4;

  // timer for choosing the default option
  uint32 defaultDecideTimeInSeconds = 5;

  // created_at/added_on date of the order
  google.protobuf.Timestamp created_at = 6;

  // external id for display purposes
  string external_id = 7;

  // currency through which given ExchangerOffer can be redeemed like FI_COINS.
  ExchangerOfferRedemptionCurrency redemption_currency = 8;


  // price of redeeming the ExchangerOffer.
  // deprecated in favour of redemption_value
  float redemption_price = 9 [deprecated = true];

  // todo(rohanchougule): Add tile specific display details if any. Or add it inside ExchangerOrderOption itself

  // list of tags applied to the offer that was redeemed
  repeated api.typesv2.ui.IconTextComponent tags = 10;

  // used to show the sections on offer details page, multiple sections can be used to show data related to different topics, eg. delivery details card, how to redeem section etc.
  // eg.  delivery details card can contain details about delivery address, delivery partner etc.
  repeated OfferDetailsSection redeemed_offer_details_sections = 11;

  // tags displayed on the exchanger order tile e.g "CREDIT CARD REWARD"
  // design : https://www.figma.com/file/ucpjpts4aSlTJWTE0ENU7J/Rewards-Centre-%E2%80%A2-Workfile?node-id=14751-85442&t=vRsbSIR6i2QmpzyV-0
  repeated api.typesv2.common.Text display_tags = 12;

  // flag to decide whether or not to display redemption price,
  // if set to true then redemption price shouldn't be displayed on exchanger offer tile.
  bool should_not_display_redemption_price = 13;

  // description of current status of exchanger order, like "This will be added to your coin balance on 12 May 2021"
  // can be empty
  string status_desc = 14;

  // redemption value of the exchanger order
  api.typesv2.ui.VerticalKeyValuePair redemption_value = 15;
}
