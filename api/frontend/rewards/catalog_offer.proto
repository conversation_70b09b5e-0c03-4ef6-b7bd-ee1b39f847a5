syntax = "proto3";

package frontend.rewards;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/rewards/display_components.proto";
import "api/frontend/rewards/offer.proto";
import "api/frontend/rewards/exchanger_offer.proto";
import "api/typesv2/common/image_type.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/frontend/rewards";
option java_package = "com.github.epifi.gamma.api.frontend.rewards";

// A common entity/wrapper to handle the different offer types for the catalog screen
message CatalogOfferV1 {
  oneof offer_data {
    ExchangerOfferWidget exchanger_offer = 1;
    Offer offer = 2;
  }

  // tag(s) to be shown on the top right of a catalog card
  // figma - https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?node-id=3502%3A15694&mode=dev
  // **NOTE**: it will only contain 1 tag (like CC exclusive), or conversion ratio, or 1 tag and conversion ratio
  // as client will only support a maximum of 2 values.
  repeated CatalogCardTopRightTag top_right_tags = 3;

  // all tags present for the offer
  // contains both manual and automatic tags
  repeated string all_tags = 4;

  // category tag of the offer
  string category_tag = 5;

  // subcategory tag of the offer
  string sub_category_tag = 6;
}

message CatalogOfferHome {
  // text to be displayed on offer card
  api.typesv2.common.Text title = 1;

  // image to be displayed on offer card
  api.typesv2.common.Image image = 2;

  // background color of the offer card
  api.typesv2.ui.BackgroundColour bg_color = 3;

  // background shadows of the offer card
  repeated api.typesv2.ui.Shadow shadows = 4;

  // deeplink to redirect to when offer card is clicked
  deeplink.Deeplink deeplink = 5;

  // tag to be displayed on top of offer card
  // Deprecated in favor of tag_v2 one-of for showing either the Tag or a countdown timer tag
  api.typesv2.ui.IconTextComponent tag = 6 [deprecated = true];

  // brand name to be displayed on offer card
  api.typesv2.common.Text brand_name = 7;

  // content type to decide the positioning of image on tile
  api.typesv2.common.ImageContentType image_content_type = 8;

  // offer id for client events
  string offer_id = 9;

  // offer type for client events
  string offer_type = 10;

  oneof tag_v2 {
    // New field, similar to the deprecated tag field. Either this is rendered in the Home offer card, or the countdown
    // timer when present
    api.typesv2.ui.IconTextComponent applied_tag = 11;
    // Count down timer tag, which shows a timer ticker:
    // https://www.figma.com/file/i6NJvAI9OeQMbbFU229Xg6/%F0%9F%8F%86-Rewards-%E2%80%A2-FFF?type=design&node-id=3502-16063&mode=design&t=MPj1JzzWSewjO4vF-4
    CountdownTimer countdown_timer = 12;
  }
}
