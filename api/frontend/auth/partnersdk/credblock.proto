syntax = "proto3";

package frontend.auth.partnersdk;

option go_package = "github.com/epifi/gamma/api/frontend/auth/partnersdk";
option java_package = "com.github.epifi.gamma.api.frontend.auth.partnersdk";


// An enum to represent the type of PIN
enum CredentialType {
  CREDENTIAL_TYPE_UNSPECIFIED = 0;
  NEW_CARD_PIN = 1;
  CARD_PIN = 2;
  SECURE_PIN = 3;
  OTP = 5;
}

message CredBlock {
    CredentialType credential_type = 1 [json_name="CredentialType"];
    string encrypted_credential = 2 [json_name="EncryptedCredential"];
    // Ephemeral public key that is used in
    // Elliptic curve's <PERSON><PERSON><PERSON> hellman(ECDH) key agreement protocol
    // to generate the keys to encrypt the payload i.e., keys
    string ecdh_pk = 3 [json_name="Ecdh_PK"];
}
