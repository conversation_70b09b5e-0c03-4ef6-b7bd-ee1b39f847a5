// protolint:disable MAX_LINE_LENGTH
// Proto and Service definitions related to auth service
// 		* Device Integrity Nonce generation

syntax = "proto3";

package frontend.auth;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/device_integrity_token_type.proto";

option go_package = "github.com/epifi/gamma/api/frontend/auth";
option java_package = "com.github.epifi.gamma.api.frontend.auth";

service Auth {

  // Creates and sends a nonce which the client can then use to generate attestation.
  rpc GetDeviceIntegrityNonce (GetDeviceIntegrityNonceRequest) returns (GetDeviceIntegrityNonceResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = false;
  };

  // GenerateOtp generates otp corresponding to the given vendor for the given epifi user
  rpc GenerateOtp (GenerateOtpRequest) returns (GenerateOtpResponse) {
    option (rpc.auth_required) = true;
  };

  rpc VerifyDeviceIntegrity (VerifyDeviceIntegrityRequest) returns (VerifyDeviceIntegrityResponse) {
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.auth_required) = false;
  }

  // SyncBiometricIdentifier: The app will send the biometric identifier generated at the client system
  // This received biometric identifier will be sent to Biometrics service for processing
  rpc SyncBiometricIdentifier (SyncBiometricIdentifierRequest) returns (SyncBiometricIdentifierResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

}

message SyncBiometricIdentifierRequest {
  frontend.header.RequestHeader req = 1;
  // Mandatory - Biometric identifier generated at client
  string biometric_id = 2;
  // If this flag is true then we just need to update the current biometric identifier in DB
  // If this flag is false then we need to check if there is an update in the biometric identifier
  bool force_biometric_update = 3;
}

message SyncBiometricIdentifierResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetDeviceIntegrityNonceRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

message GetDeviceIntegrityNonceResponse {
  rpc.Status status = 1;
  string nonce = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

// workflow for which the otp validation is required by the vendor.
// For eg: card pin setup requires OTP based authentication by Federal bank.
enum RequestType {
  REQUEST_TYPE_UNSPECIFIED = 0;
  CARD_PIN = 1;
}

message GenerateOtpRequest {
  // Set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // actor id corresponding to the given user
  string actor_id = 2 [deprecated = true];

  // workflow for which the otp validation is required by the vendor.
  // For eg: card pin setup requires OTP based authentication by Federal bank.
  RequestType request_type = 3;

  // Unique attemptId for additional authentication for card pin actions.
  // auth_attempt_id is generated after successful validation of auth attempt (liveness or secure pin validation).
  // It will be used as a second factor validation while resetting/setting card pin.
  string card_auth_attempt_id = 4;
}

message GenerateOtpResponse {
  enum Status {
    OK = 0;

    // If we cannot initiate otp corresponding to a given actor
    // due to account not existing or the actor not present
    NOT_FOUND = 5;

    // Internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  frontend.header.ResponseHeader resp_header = 15;
}

message VerifyDeviceIntegrityRequest {
  frontend.header.RequestHeader req = 1;

  // attestation token is encrypted block which contains information
  // about the integrity state of the device
  string attestation_token = 2;

  // one time string that was originally generated by the epifi servers
  // and sent to the vendor for attestation token generation
  string nonce = 3;

  // passed TRUE when user acknowledges to allow usage of their
  // high risk device which doesn't pass all device integrity checks
  api.typesv2.common.BooleanEnum allow_high_risk_device = 4;

  api.typesv2.DeviceIntegrityTokenType token_type = 5;
}

message VerifyDeviceIntegrityResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // return device integrity token for cases when attestation is successfully verified and
  // device passes integrity checks
  string device_integrity_token = 2;
}

