// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package frontend.cx.customer_auth;

import "api/frontend/cx/customer_auth/enums.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/cx/customer_auth";
option java_package = "com.github.epifi.gamma.api.frontend.cx.customer_auth";

message VerifyMobilePromptRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // message id for which this callback is being sent
  // client can fetch this from the notification received on the device
  // The message_id will be present in the data map with key prompt_id
  string prompt_id = 2 [(validate.rules).string.min_len = 1];

  MobilePromptResponse mobile_prompt_response = 3;
}

message VerifyMobilePromptResponse {
  rpc.Status status = 1;
  frontend.header.ResponseHeader resp_header = 15;
}

service CustomerAuthCallback {
  // callback service to receive customer's response on sending mobile prompt for verification
  // CX will send notification to customer in critical cases where the customer is asking for a sensitive action to an agent
  // The customer receives a mobile prompt which has a yes/no option
  // If the customer clicks yes, it means the customer is verified and the request sent to agent is from a verified customer
  rpc VerifyMobilePrompt(VerifyMobilePromptRequest) returns (VerifyMobilePromptResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}
