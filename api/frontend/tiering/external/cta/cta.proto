syntax = "proto3";

package frontend.tiering.external.cta;

option go_package = "github.com/epifi/gamma/api/frontend/tiering/external/cta";
option java_package = "com.github.epifi.gamma.api.frontend.tiering.external.cta";

// Custom cta properties to be used internally in frontend gamma service
message CtaProperties {
  bool is_cta_available = 1;
  bool did_tier_criteria_met = 2;
  string reason_if_criteria_not_met = 3;
  string cta_type = 4;
}

enum CriteriaNotMetReason {
  CRITERIA_NOT_MET_REASON_UNSPECIFIED = 0;
  CRITERIA_NOT_MET_REASON_MIN_KYC = 1;
}

enum CtaType {
  // If cta is not available
  CTA_TYPE_UNSPECIFIED = 0;

  // If user is min kyc
  CTA_TYPE_MIN_KYC = 1;

  // If eligiblity criteria is met and "Upgrade for Free" button is available
  CTA_TYPE_FREE_UPGRADE = 2;

  // If user didn't have enough funds and already in current tier
  CTA_TYPE_PSEUDO_GRACE = 3;

  // If user is not eligible for upgrade(cool off etc.,)
  CTA_TYPE_NOT_ELIGIBLE = 4;

  // If user can upgrade by clicking "Join Plus"/"Join Infinite"
  CTA_TYPE_ADD_FUNDS_AND_UPGRADE = 5;

  // If user can upgrade by clicking "Join Salary" in salary tier
  CTA_TYPE_JOIN_SALARY_AND_UPGRADE = 6;

  // If user needs to add fund to stay in current tier
  CTA_TYPE_ADD_FUNDS_PSUEDO_GRACE = 7;

  // If user is in salary lite
  CTA_TYPE_SHARE_ACCOUNT_DETAILS = 8;

  // If user needs to go to AA salary flow
  CTA_TYPE_AA_SALARY = 9;

}
