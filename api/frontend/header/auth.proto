syntax = "proto3";

package frontend.header;

import "api/rpc/request_header.proto";
import "api/typesv2/actor.proto";
import "api/typesv2/common/device.proto";
import "api/frontend/header/device_integrity.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/header";
option java_package = "com.github.epifi.gamma.api.frontend.header";


// A set of authentication attributes that are common across frontend requests
message AuthHeader {
  // Represents Mobile Device or Browser which initiated the request
  api.typesv2.common.Device device = 1 [(validate.rules).message.required = true];

  // Authentication tokens issued by Epifi's Auth service
  //
  // Refresh tokens mitigate the risk of a long-lived authentication token
  // The idea of refresh tokens is that:
  //  * If an access token is compromised, because it is short-lived,
  //    the attacker has a limited window in which to abuse it.
  //  * Scope of refresh token usage is very limited
  //    i.e., Get new access tokens
  //    Hence, chances of compromise of a refresh token is relatively less.
  oneof auth_token {
    // A short lived authentication token
    // Access token serves as authentication token to access a resource
    //
    // Access tokens usually have an expiration date and are short-lived.
    string access_token = 2;

    // Refresh tokens can be used to get a new access token
    // from the authentication server.
    //
    // In other words, whenever an access token is required to
    // access a resource, a client may provide a refresh token
    // and get a new access token from the authentication server
    //
    // Common use cases include:
    // 1. Get access token for the first time
    // 2. Get new access token when old one is expired
    //
    // Refresh tokens can also expire but are quite long-lived.
    // Validity of a refresh token is much longer compared to
    // that of an access token.
    string refresh_token = 3;
  }

  // A Cryptographically signed attestation from the SafetyNet Attestation API
  // which assess the device's integrity.
  //
  // Client has to pass the token in all the sensitive requests.
  // Definition of a sensitive request will be passed to the client in the
  // form of a config file at the time of login
  //
  // Note that this is limited to Android clients and will be extended in the
  // future to support other clients
  string safety_net_token = 4;

  // This field has been deprecated in favour of actor id.
  api.typesv2.Actor actor = 5 [deprecated = true];

  // ID of the actor that has initiated the request
  // This field is not to be populated by the client
  // Any information that is populated by the client will be cleared
  // New actor value will be injected based on the authentication header
  string actor_id = 9;

  // Auth Factor Update ID for identifying the auth factor update record.
  // This value being set represents that the token generated is
  // for auth factor update.
  string auth_factor_update_id = 6;

  bool is_device_registered = 7;

  // This is a temporary fix
  // RequestHeader used to fetch prospect_id, attempt_id, session_id
  // This is populated by the client
  // Use request header directly in request messages
  rpc.RequestHeader request_header = 8 [deprecated = true];

  // Nonce used by iOS devices to create assertion token to verify
  // the integrity of the device. Client has to pass the token in all
  // the sensitive requests along with the assertion token.
  //
  // Android devices do not need to pass this token.
  string device_integrity_nonce = 10;

  // contains info related to device integrity that is passed by the client in request header
  DeviceIntegrity device_integrity = 11;
}
