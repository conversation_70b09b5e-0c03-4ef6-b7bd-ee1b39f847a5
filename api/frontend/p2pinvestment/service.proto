syntax = "proto3";

package frontend.p2pinvestment;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/p2pinvestment/activity.proto";
import "api/frontend/p2pinvestment/activity_order_receipt.proto";
import "api/frontend/p2pinvestment/banner.proto";
import "api/frontend/p2pinvestment/dashboard.proto";
import "api/frontend/p2pinvestment/enums.proto";
import "api/frontend/p2pinvestment/investment_summary.proto";
import "api/frontend/p2pinvestment/jump_client_states/enums.proto";
import "api/frontend/pay/transaction/service.proto";
import "api/rpc/method_options.proto";
import "api/rpc/page.proto";
import "api/typesv2/date.proto";
import "api/typesv2/deeplink_screen_option/p2pinvestment/common/maturity_consent_card.proto";
import "api/typesv2/deeplink_screen_option/p2pinvestment/promotion_loading_screen.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/money.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/frontend/p2pinvestment";
option java_package = "com.github.epifi.gamma.api.frontend.p2pinvestment";

service P2pInvestment {
  // will be used to get get landing info when user clicks on p2p investment tab
  rpc GetLandingInfo (GetLandingInfoRequest) returns (GetLandingInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
  // used to check if user is eligible for p2p investment or not
  rpc CheckEligibility (CheckEligibilityRequest) returns (CheckEligibilityResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to invest money in P2P
  // P2P investment is an asynchronous flow where the investment generally takes T+1 or T+2 days to reach a terminal state.
  // P2P investment includes making payment for the investment via partner bank and settling the investment with vendor.
  //
  // The RPC creates an order with P2P_INVESTMENT workflow.
  // The RPC returns payment data needed for cred block generation along with the client request id for fund transfer order.
  // The RPC returns error view in case the user isn't eligible for investment.
  rpc Invest (InvestRequest) returns (InvestResponse) {}
  // rpc to get all the activities of an actor
  rpc GetAllActivities (GetAllActivitiesRequest) returns (GetAllActivitiesResponse) {}

  // rpc to get recent activities of an actor
  rpc GetRecentActivities (GetRecentActivitiesRequest) returns (GetRecentActivitiesResponse) {}

  // RPC to get order status for either P2P investment order or P2P withdrawal order
  // RPC to be called by client after authenticating an investment payment or after initiating a withdrawal request.
  // The response from RPC will be used to render the investment/withdrawal status to the user. Based on the response,
  // the client can show messages like: Transaction Successful, Payment failed, etc. to the user.
  // The response will also hold multiple tiles that need to be shown to the user.
  rpc GetP2POrderStatus (GetP2POrderStatusRequest) returns (GetP2POrderStatusResponse) {}

  // this rpc will be called on dashboard load
  rpc GetDashboard (GetDashboardRequest) returns (GetDashboardResponse) {}

  // this rpc will be used to get deeplink by screen
  // it will help us avoid creating rpc like GetDashboard which basically returns copy of screen options
  // it will be used as a fallback for deeplink when screen options for the corresponding screen is not present
  rpc GetDeeplink (GetDeeplinkRequest) returns (GetDeeplinkResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to withdraw some or total amount from P2P investments
  // P2P withdrawal is an asynchronous flow where the withdrawal generally takes T+1 or T+2 days to reach a terminal state.
  // P2P withdrawal includes making a withdrawal request to the p2p vendor and settling the withdrawal request with
  // partner bank
  //
  // The RPC creates an order of P2P_WITHDRAWAL workflow and returns the client request id of this order
  // Client can check the status of this order using the GetP2POrderStatus RPC
  rpc Withdraw (WithdrawRequest) returns (WithdrawResponse) {}

  // RPC to get the early withdrawal penalty applicable on the withdrawal amount
  // The RPC will return zero amount if the withdrawal amount is less than the penalty free amount
  rpc GetEarlyWithdrawalCharges (GetEarlyWithdrawalChargesRequest) returns (GetEarlyWithdrawalChargesResponse) {}

  // Rpc to get Activity Details
  // Takes as input external_order_id and returns corresponding Activity Details
  rpc GetActivityDetails (GetActivityDetailsRequest) returns (GetActivityDetailsResponse) {}

  // Rpc called for rendering 'P2P_WITHDRAW_MONEY_SUMMARY' screen
  // Takes requested amount from user and provides scheme level break up of the investment
  rpc GetWithdrawMoneyAttributes (GetWithdrawMoneyAttributesRequest) returns (GetWithdrawMoneyAttributesResponse) {}
  // GetInvestmentSummaryScreen will be used to get investment summary screen details
  // it shows the breakdown of the investment amount and the returns earned on it for each scheme
  rpc GetInvestmentSummaryScreen (GetInvestmentSummaryScreenRequest) returns (GetInvestmentSummaryScreenResponse) {}
  // GetInvestNowScreen will be used to get invest now screen details
  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A12974
  // screen name - P2P_INVESTMENT_INVEST_SCREEN
  rpc GetInvestNowScreen (GetInvestNowScreenRequest) returns (GetInvestNowScreenResponse) {}
  // RPC to withdraw some or total amount from P2P investments
  // P2P withdrawal is an asynchronous flow where the withdrawal generally takes T+1 or T+2 days to reach a terminal state.
  // P2P withdrawal includes making a withdrawal request to the p2p vendor and settling the withdrawal request with
  // partner bank
  //
  // The RPC creates order across different scheme according to amount breakup provided in the request and returns client request id of this order
  // Client can check the status of this order using the GetP2POrderStatus RPC
  rpc WithdrawV2 (WithdrawV2Request) returns (WithdrawV2Response) {}
  // Rpc for getting different kinds of jump banners
  rpc GetBanners (GetBannersRequest) returns (GetBannersResponse) {}
  // use to generate renew investment otp
  rpc GenerateRenewInvestmentOtp (GenerateRenewInvestmentOtpRequest) returns (GenerateRenewInvestmentOtpResponse) {}
  // use to renew investment with the help of otp
  rpc RenewInvestment (RenewInvestmentRequest) returns (RenewInvestmentResponse) {}
  // GetGenerateOTPScreen will be used to get p2p otp screen details
  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11726%3A15473&t=lEXEawGXNcRltsvU-0
  // screen name - P2P_INVESTMENT_OTP_SCREEN
  rpc GetGenerateOTPScreen (GetGenerateOTPScreenRequest) returns (GetGenerateOTPScreenResponse) {}
  // Rpc called for rendering 'P2P_CONFIRM_INVESTMENT_RENEW' screen
  rpc GetRenewInvestmentAttributes (GetRenewInvestmentAttributesRequest) returns (GetRenewInvestmentAttributesResponse) {}
  // Rpc called for rendering 'P2P_INVESTMENT_ALL_UPCOMING_RENEWALS_SCREEN' screen
  rpc GetAllUpcomingRenewals (GetAllUpcomingRenewalsRequest) returns (GetAllUpcomingRenewalsResponse) {}
  // Rpc called for updating maturity consent for an investment
  rpc UpdateMaturityConsentForInvestment (UpdateMaturityConsentForInvestmentRequest) returns (UpdateMaturityConsentForInvestmentResponse) {}
  // Rpc called for calculating maturity amount and getting updated consent form
  rpc GetMaturityAmountAndConsentFormForInvestment (GetMaturityAmountAndConsentFormForInvestmentRequest) returns (GetMaturityAmountAndConsentFormForInvestmentResponse) {}
  // RPC to be called on loading JUMP_PROMOTION_LOADING_SCREEN to get the currently promoted scheme's invest page
  rpc GetInvestPageFromPromotionalUseCase (GetInvestPageFromPromotionalUseCaseRequest) returns (GetInvestPageFromPromotionalUseCaseResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message GetInvestPageFromPromotionalUseCaseRequest {
  frontend.header.RequestHeader req = 1;
  // screen options which contain details to decide the deeplink to return
  api.typesv2.deeplink_screen_option.p2pinvestment.PromotionLoadingScreenOptions promotion_loading_screen_options = 2;
}

message GetInvestPageFromPromotionalUseCaseResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink returned can be of the currently promoted scheme's invest page in happy case
  // can be error screen in case of ineligibility of scheme
  // can also be other deeplinks based on future use cases
  // currently possible screens: P2P_INVESTMENT_INVEST_SCREEN, P2P_INVESTMENT_AVAILABLE_PLANS_INFO, P2P_INVESTMENT_ELIGIBILITY_CHECK_RESULT_SCREEN, P2P_INVESTMENT_ELIGIBILITY_CHECK_SCREEN
  frontend.deeplink.Deeplink deeplink = 2;
}

message GetInvestNowScreenRequest {
  frontend.header.RequestHeader req = 1;
  // scheme_name is the name of the scheme for which the invest now screen is requested
  // using string type instead of enum so that we can add new schemes without client changes
  string scheme_name = 2;
  // options already selected by the user on the invest screen
  // will be used to pre-populate the same options when user changes plan from the invest screen
  InvestScreenSelectedOptions selected_options = 3;
}

// options selected by the user on the invest page
// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?type=design&node-id=18001-25625&mode=design&t=A9ujTzHCXgW67iLZ-0
message InvestScreenSelectedOptions {
  // selected amount
  api.typesv2.Money amount = 1;
  // selected maturity consent type
  api.typesv2.deeplink_screen_option.p2pinvestment.common.MaturityConsentType maturity_consent_type = 2;
}

message GetInvestNowScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // use P2PInvestScreenOptions for the information required to render the screen
  // using Deeplink object instead of directly passing data here as the screen options
  // already exists for the screen
  frontend.deeplink.Deeplink deeplink = 2;
}

message GetInvestmentSummaryScreenRequest {
  frontend.header.RequestHeader req = 1;
}

message GetInvestmentSummaryScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.p2pinvestment.InvestmentSummaryScreen screen = 2;
}

message GetLandingInfoRequest {
  // request header
  frontend.header.RequestHeader req = 1;
}

message GetLandingInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message CheckEligibilityRequest {
  // request header
  frontend.header.RequestHeader req = 1;
}

message CheckEligibilityResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink deeplink = 2;
}

message InvestRequest {
  // request header
  frontend.header.RequestHeader req = 1;
  // Amount to be invested in P2P
  api.typesv2.Money amount = 2;
  // scheme in which user wants to invest
  // if not provided, SCHEME_NAME_LL_DEFAULT scheme will be used
  // supported schemes right now: SCHEME_NAME_LL_FLEXI, SCHEME_NAME_LL_SHORT_TERM, SCHEME_NAME_LL_LONG_TERM
  string scheme_name = 3;
  // maturity consent type selected by the user
  api.typesv2.deeplink_screen_option.p2pinvestment.common.MaturityConsentType maturity_consent_type = 4;
}

message InvestResponse {
  enum Status {
    OK = 0;
    ERROR_CODE_RECEIVED_FROM_PAY = 101;
  }
  // response header
  frontend.header.ResponseHeader resp_header = 1;
  // ETA for investment to get completed
  api.typesv2.Date eta = 2;
  // p2p investment order's client request id
  string order_client_req_id = 3;
  // Transaction attributes are required for the client to:
  //  i. Generate the credentials using a common library i.e., NPCI CL or Partner bank's CL
  //  ii. Present the transaction information e.g., payment protocol to the user
  frontend.pay.transaction.TransactionAttribute transaction_attribute = 4;
  // If pin based authorization is required to execute payment for a given order.
  frontend.pay.transaction.PinRequiredType pin_required = 5;
}

message GetAllActivitiesRequest {
  frontend.header.RequestHeader req = 1;

  // if not provided or "ALL", all activities will be returned
  // SCHEME_NAME_LL_FLEXI - to get all flexi activities
  // SCHEME_NAME_LL_SHORT_TERM - to get all short term activities
  // SCHEME_NAME_LL_LONG_TERM - to get all long term activities
  // using string type instead of scheme name enum on purpose so that new schemes can be added without
  // changing the proto which requires a new app release
  string filter_id = 2;
  // page context for pagination
  rpc.PageContextRequest page_context = 3;
}

// as sorted_activities object will be big in size, over the time we will be sending ActivityStageDetails
// and details nil inside the activity. In those cases app need to call GetActivityDetails api to get
// ActivityStageDetails and activity details
message GetAllActivitiesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // all activities sorted by sort_by_date attribute in activity and filtered by filter_id
  // Deprecated: Use activity_summaries instead
  repeated Activity sorted_activities = 2 [deprecated = true];

  // list of filter options to filter activities
  repeated FilterChip filter_chips = 3;

  message FilterChip {
    // e.g: All, Flexi 7%, Short term 8%, Long term 9%
    string display_name = 1;
    // default filter option
    bool is_selected = 2;
    // ALL - to get all activities
    // SCHEME_NAME_LL_FLEXI - to get all flexi activities
    // SCHEME_NAME_LL_SHORT_TERM - to get all short term activities
    // SCHEME_NAME_LL_LONG_TERM - to get all long term activities
    // using string type instead of scheme name enum on purpose so that new schemes can be added without
    // changing the proto which requires a new app release
    string filter_id = 3;
  }
  // page context response
  rpc.PageContextResponse page_context = 4;
  // Summary of Activities
  repeated ActivitySummary activity_summaries = 5;
}

message GetRecentActivitiesRequest {
  frontend.header.RequestHeader req = 1;
}

message GetRecentActivitiesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  message NudgeInfo {
    string icon_url = 1;
    string title = 2;
    string sub_title = 3;
    string bg_color = 4;
  }
  NudgeInfo nudge_info = 2;
  // Deprecated: Use activity_summaries instead
  repeated Activity recent_activities = 3 [deprecated = true];
  // summary of activities
  repeated ActivitySummary activity_summaries = 4;
  // flag to show recent activity or not
  // in case of false: show all activity button
  // else show recent activities
  bool show_recent_activity = 5;
  // title for the recent activities section
  api.typesv2.common.Text title = 6;
  // cta to view all activities screen
  api.typesv2.ui.IconTextComponent view_all_cta = 7;
}

message GetP2POrderStatusRequest {
  // request header
  frontend.header.RequestHeader req = 1;
  // client request id of P2P Investment or P2P Withdrawal order.
  // This Id is passed to the client in Invest RPC's response
  string order_client_req_id = 2;
}

message GetP2POrderStatusResponse {
  enum Status {
    // Success
    OK = 0;
    // Internal error
    INTERNAL = 13;
    // Payment has failed
    PAYMENT_FAILED = 100;
  }
  // response header
  frontend.header.ResponseHeader resp_header = 1;

  // Information about P2P order to be shown to the user
  // e.g.
  // Title: Transaction Successful
  // Desc: Your Investment request has been sent to partner
  deeplink.InfoItem order_status_info = 2;
  // Order timeline to be shown to the user
  repeated deeplink.InfoItem order_timeline = 3;
  // CTA to be shown to user based on order status
  deeplink.Cta cta = 4;
  // flag to show confetti
  bool show_confetti = 5;
  // will be used to show extra info in case of withdrawal like
  // You will receive 2 separate deposits into your Fi account. Check My Activity for details.
  string bottom_info = 6;
}

message GetDashboardRequest {
  frontend.header.RequestHeader req = 1;
}

message GetDashboardResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // data need to show dashboard
  DashboardInfo dashboard_info = 2;
  // [deprecated] when return value is in negative then negative_return_value would be true
  bool negative_return_value = 3 [deprecated = true];
  // event properties that should be sent by the client along with P2pSummaryLoaded event
  // this will contain prpoerties like unlocked plans, eligibility state etc
  // ref - https://docs.google.com/spreadsheets/d/1UJWX6L5njKdzz-MvL6o18X0GoPDOSitBTAt7B53lNnI/edit#gid=**********
  map<string, string> event_properties = 4;
}

message GetDeeplinkRequest {
  frontend.header.RequestHeader req = 1;
  // screen for which deeplink need to be shared
  deeplink.Screen screen = 2;
}

message GetDeeplinkResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // deeplink for the requested screen
  // this must contain the screen options of the corresponding link screen if any
  deeplink.Deeplink deeplink = 2;
}

message WithdrawRequest {
  // request header
  frontend.header.RequestHeader req = 1;
  // Amount to be withdrawn from P2P investment
  api.typesv2.Money amount = 2;
}

message WithdrawResponse {
  // response header
  frontend.header.ResponseHeader resp_header = 1;
  // p2p withdrawal order's client request id
  string order_client_req_id = 2;
}

message GetEarlyWithdrawalChargesRequest {
  frontend.header.RequestHeader req = 1;
  // Amount user selected to be withdrawn from P2P investment
  api.typesv2.Money amount = 2;
}

message GetEarlyWithdrawalChargesResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Early withdrawal message to be shown to the user
  // Will be empty string in case early withdrawal charge is 0
  string early_withdrawal_message = 2;
  // Penalty amount in case of early withdrawal
  api.typesv2.Money penalty_charge = 3;
}

message GetActivityDetailsRequest {
  frontend.header.RequestHeader req = 1;
  string order_external_id = 2;
  bool show_maturity_consent_form = 3;
}

message GetActivityDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Deprecated: use activity details v2 instead
  Activity activity_details = 2 [deprecated = true];
  ActivityOrderReceipt activity_details_v2 = 3;
}

message GetWithdrawMoneyAttributesRequest {
  // request header
  frontend.header.RequestHeader req = 1;
  // Amount to be withdrawn from P2P investment
  api.typesv2.Money amount = 2;
  // eg: penalty free
  WithdrawalMethod withdrawal_method = 3;
}

message GetWithdrawMoneyAttributesResponse {
  // response header
  frontend.header.ResponseHeader resp_header = 1;
  // tell us if need to show nudge or not
  bool is_nudge_present = 2;
  // eg: Withdraw investment
  api.typesv2.common.Text title = 3;
  // eg : Your money will be deposited in your Fi account ••5656 within 2 business days
  api.typesv2.common.Text operative_account_message = 4;
  // eg: // eg: ₹20,000
  RequestedAmountTile requested_amount = 5;
  AmountBreakUpDetails amount_break_up_details = 6;
  // nudge details
  NudgeDetails nudge_details = 7;
  // represents the i icon on the withdraw screen
  repeated deeplink.InfoItem info_items = 8;
  message RequestedAmountTile {
    // eg: Amount being withdrawn
    api.typesv2.common.Text requested_amount_title = 1;
    api.typesv2.Money amount = 2;
  }
  message AmountBreakUpDetails {
    // eg: This break-up optimises to minimise penalties & maximise your returns
    api.typesv2.common.Text info = 1;
    // break the requested amount across different plans
    repeated AmountBreakUp amount_break_up = 2;
    message AmountBreakUp {
      // eg: Flexi • Up to 7% returns
      repeated string plan_details = 1;
      // eg: ₹10,000
      api.typesv2.Money amount = 2;
      // scheme name
      string scheme_name = 3;
    }
    // this is the icon with info text - https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590%3A12065
    string icon = 3;
    // eg: Early withdrawal charges for Long-term: Up to 2% of returns included
    api.typesv2.common.Text penalty_message = 4;
  }
  message NudgeDetails {
    // eg: You'll miss out on ₹8,000 in returns!
    api.typesv2.common.Text title = 1;
    // background color for nudge title
    string title_bg_color = 2;
    // eg: ₹20,000 in Jump for 1 year will be ₹28,000
    api.typesv2.common.Text desc = 3;
    // nudge graph image
    api.typesv2.common.Image img = 4;
    deeplink.Cta withdraw_later_cta = 5;
    deeplink.Cta proceed_cta = 6;
  }
}

message WithdrawV2Request {
  frontend.header.RequestHeader req = 1;
  // amount user wants to withdraw
  api.typesv2.Money amount = 2;
  // amount break up across different scheme
  repeated AmountBreakUp amount_break_up = 3;
  message AmountBreakUp {
    // eg: ₹10,000
    api.typesv2.Money amount = 1;
    // scheme name
    string scheme_name = 2;
  }
}

message WithdrawV2Response {
  // response header
  frontend.header.ResponseHeader resp_header = 1;
  // p2p withdrawal order's client request id
  string order_client_req_id = 2;
}

message GetBannersRequest {
  frontend.header.RequestHeader req = 1;
  frontend.deeplink.BannerRequestPayload banner_req_payload = 2;
}

message GetBannersResponse {
  // response header
  frontend.header.ResponseHeader resp_header = 1;
  // Banners to be shown between the activity section and summary section
  repeated JumpBanner banners = 2;
}

message GenerateRenewInvestmentOtpRequest {
  frontend.header.RequestHeader req = 1;
  string renewal_investment_request_id = 2;
}

message GenerateRenewInvestmentOtpResponse {
  // response header
  frontend.header.ResponseHeader resp_header = 1;
}

message RenewInvestmentRequest {
  frontend.header.RequestHeader req = 1;
  // otp that comes in the investor mobile or email id use to verify renew investment
  string otp = 2;
  // this id corresponds to a particular investment
  string renewal_investment_request_id = 3;
}

message RenewInvestmentResponse {
  enum Status {
    // Success
    OK = 0;
    // Client specified an invalid argument
    INVALID_ARGUMENT = 3;
    // Internal error
    INTERNAL = 13;
    // Input token is expired and cannot use it
    // Generate new token
    TOKEN_EXPIRY = 100;
    // Otp is not correct
    OTP_INCORRECT = 101;
    // Otp ast attempt
    OTP_INCORRECT_LAST_ATTEMPT = 102;
    // Too many incorrect attempts on the token
    // Generate new token
    OTP_VERIFY_LIMIT_EXCEEDED = 103;
  }
  // response header
  // returning 'inline error view' here for showing
  frontend.header.ResponseHeader resp_header = 1;
  // helps to redirect according to status
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11701%3A15670&t=CQGbtXPd1G8WbTAA-4
  // Would contain P2P_INVESTMENT_CURRENT_STATUS_SCREEN deeplink in success case
  deeplink.Deeplink deeplink = 2;
  string scheme_name = 3;
  // flow_type(renewal/cancellation/cancellation_modification/renewal_modification)
  // required for client event
  string flow_type = 4;
}

message GetGenerateOTPScreenRequest {
  frontend.header.RequestHeader req = 1;
  jump_client_states.RenewalType renewal_type = 2;
  string order_external_id = 3;
}

// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11943%3A15579&t=CQGbtXPd1G8WbTAA-4
message GetGenerateOTPScreenResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // used to title in otp screen
  // eg: "Verify with an OTP"
  api.typesv2.common.Text title = 2;
  // used to show sub title in otp screen
  // eg: "Enter the OTP sent to +91 42832 93290"
  api.typesv2.common.Text sub_title = 3;
  int32 waiting_time_in_secs = 5;
  // renew investment request ID to be used in all BE renew investment calls
  string renewal_investment_request_id = 7;
  // if true: auto read of OTP from SMS will be disable. Default we do the auto read.
  bool skip_auto_read = 8;
  string scheme_name = 9;
  // flow_type(renewal/cancellation/cancellation_modification/renewal_modification)
  // required for client event
  string flow_type = 10;
}

message GetRenewInvestmentAttributesRequest {
  frontend.header.RequestHeader req = 1;
  string order_external_id = 2;
}

message GetRenewInvestmentAttributesResponse {
  // response header
  frontend.header.ResponseHeader resp_header = 1;
  RenewInvestmentData renew_investment_data = 2;
  message RenewInvestmentData {
    // Represents message shown on top of the screen
    // eg: "Renew your investment"
    api.typesv2.common.Text title = 1;
    // badge image url
    // e.g: Long-term 9%
    api.typesv2.common.Image badge_image = 2;
    // e.g: "In 3 months, your money will jump up to"
    api.typesv2.common.Text maturity_amount_title = 3;
    // maturity amount
    api.typesv2.ui.IconTextComponent maturity_amount = 4;
    // e.g: "1 year", "Up to 9% p.a", "Withdraw early with a fee"
    repeated deeplink.P2PInvestInfoChip info_chips = 5;
    // eg: "If not invested back, Amount will be sent to your Fi federal bank account in 3 working days after maturity date"
    api.typesv2.common.Text info = 6;
    // eg: "Maturity amount available to invest"
    api.typesv2.ui.IconTextComponent sub_title = 7;
    // eg: Reinvest amount
    // the amount user will invest
    api.typesv2.ui.IconTextComponent reinvestment_amount = 8;
    // e.g: "Investment details"
    api.typesv2.ui.IconTextComponent investment_details = 9;
    // e.g: "Swipe to Invest back"
    deeplink.Cta reinvestment_button = 10;
    // renewal cancel button
    api.typesv2.ui.IconTextComponent renewal_cancel_button = 11;
  }
  string scheme_name = 3;
  string flow_type = 4;
}

message GetAllUpcomingRenewalsRequest {
  frontend.header.RequestHeader req = 1;
  enum RequestType {
    // Fetches all upcoming renewals for a user
    REQUEST_TYPE_ALL_RENEWALS = 0;
    // Fetches most relevant/upcoming renewals for a user
    // Criteria for relevance is determined by product and is subject to changes
    REQUEST_TYPE_DASHBOARD = 1;
  }
  RequestType request_type = 2;
}

// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=12425%3A17185&t=pK35XeQjNmkZjNiK-4
message GetAllUpcomingRenewalsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Upcoming Renewals
  api.typesv2.common.Text title = 2;
  // 2 UPCOMING
  api.typesv2.common.Text sub_title = 3;
  // View all redirection component
  // Currently Deeplink would be nil as we would be sending all renewal infos
  api.typesv2.ui.IconTextComponent view_all = 4;
  repeated ActivitySummary renewal_infos = 5;
}

message UpdateMaturityConsentForInvestmentRequest {
  frontend.header.RequestHeader req = 1;
  string order_external_id = 2;
  // maturity consent type selected by the user
  api.typesv2.deeplink_screen_option.p2pinvestment.common.MaturityConsentType maturity_consent_type = 4;
}

message UpdateMaturityConsentForInvestmentResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Maturity action updated to renew
  string toast_message = 2;
}

message GetMaturityAmountAndConsentFormForInvestmentRequest {
  frontend.header.RequestHeader req = 1;
  api.typesv2.Money investment_amount = 2;
  // name of the scheme for which maturity amount is to be calculated
  string scheme_name = 3;
  api.typesv2.deeplink_screen_option.p2pinvestment.common.MaturityConsentType maturity_consent_type = 4;
}

message GetMaturityAmountAndConsentFormForInvestmentResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.Money maturity_amount = 2;
  api.typesv2.deeplink_screen_option.p2pinvestment.common.MaturityConsentCard consent_card = 3;
}
