syntax = "proto3";

package frontend.p2pinvestment;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/frontend/p2pinvestment";
option java_package = "com.github.epifi.gamma.api.frontend.p2pinvestment";

// InvestmentSummaryScreen is a screen that shows a summary of the investment
message InvestmentSummaryScreen {
  TotalInvestmentSummarySection total_investment_summary = 1;
  // investment projection graph for total investment
  InvestmentProjectionGraph investment_projection_graph = 2;
  // e.g: "2x returns than a savings account"
  api.typesv2.ui.IconTextComponent info_text = 3;
  // scheme level summary details
  SchemesInvestmentSummarySection schemes_investment_summary = 4;
}

// TotalInvestmentSummarySection is a summary of the total investment
message TotalInvestmentSummarySection {
  // e.g: "CURRENT VALUE
  api.typesv2.common.Text current_value_title = 1;
  // e.g: "₹40,103"
  api.typesv2.common.Text current_value = 2;
  // shows as key value pair separated by a bullet point
  // e.g: "Invested ₹40,000 • Returns ₹103"
  repeated InvestmentDetailsText investment_details = 3;
}

message InvestmentDetailsText {
  // e.g: "Invested", "Returns"
  api.typesv2.common.Text title = 1;
  // e.g: "₹20,000", "₹20,103"
  api.typesv2.common.Text value = 2;
}

// InvestmentProjectionGraph is a graph that shows the investment projection over time for P2P investments
message InvestmentProjectionGraph {
  // line in the graph
  GraphLine line = 1;
  // horizontal labels on the graph x-axis
  repeated GraphLabel labels = 2;
}

// GraphLine is a line in the graph
// it has a list of points and a color
message GraphLine {
  // points representing the line
  repeated GraphPoint points = 1;
  // color of the line in hex
  string color = 2;
}

// GraphPoint represents a point on the graph
message GraphPoint {
  int32 x = 1;
  int32 y = 2;
}

// GraphLabel is a label on the graph that shows the date on x-axis
message GraphLabel {
  int32 x = 1;
  string name = 2;
}

// SchemesInvestmentSummarySection is a section that shows a summary of the schemes investment
message SchemesInvestmentSummarySection {
  // e.g: "Investment Summary"
  api.typesv2.common.Text title = 1;
  // list of investment summaries for each investment scheme
  repeated SchemeInvestmentSummaryCard cards = 2;
}

message SchemeInvestmentSummaryCard {
  // e.g: "Flexi"
  api.typesv2.common.Text title = 1;
  // e.g: "₹20,000"
  api.typesv2.common.Text amount = 2;
  // shows as key value pair separated by a bullet point
  // e.g: "Invested ₹40,000 • Returns ₹103"
  repeated InvestmentDetailsText investment_details = 3;
  // url of scheme image
  string badge_image = 4;
  // on card tap deeplink
  deeplink.Deeplink action = 5;
}
