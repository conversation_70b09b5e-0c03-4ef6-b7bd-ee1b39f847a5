syntax = "proto3";

package frontend.home;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/home/<USER>";
import "api/typesv2/nulltypes.proto";
import "api/typesv2/ui/filter_views.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/components/spacer.proto";
import "api/typesv2/ui/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/frontend/home";
option java_package = "com.github.epifi.gamma.api.frontend.home";

// ---------------------------------------Icons---------------------------------------

message Icon {
  // Image to be shown on page load
  // Deprecated in favour of icon
  api.typesv2.common.Image icon_image = 1 [deprecated = true];
  // Image on toggle
  api.typesv2.common.Image icon_image_on_selection = 2;
  // Title to be used by default
  api.typesv2.common.Text title = 3;
  // Title to be used on icon selection
  api.typesv2.common.Text title_on_selection = 9;
  enum ActionType {
    ACTION_TYPE_UNSPECIFIED = 0;
    // For action type deeplink, client will have to redirect the user to the given deeplink
    ACTION_TYPE_DEEPLINK = 1;
    // For action type ACTION_TYPE_TOGGLE_PRIVACY, the client would just toggle the icon, while performing some icon specific action
    // - enabling/disabling privacy mode
    ACTION_TYPE_TOGGLE_PRIVACY = 2;
    // On selecting the icon, a drop down list of icons would be shown
    ACTION_TYPE_TOGGLE_DROP_DOWN = 3;
    // Share account details
    ACTION_TYPE_SHARE_ACCOUNT_DETAILS = 4;
    // Toast message to be shown on selection
    ACTION_TYPE_TOAST = 5;
  }
  oneof Action {
    // Action to redirect user to the deeplink
    deeplink.Deeplink deeplink = 4;
    // Action to open list of icons on selection
    DropDown drop_down = 5;
    // Message to be shown as toast message
    api.typesv2.ui.IconTextComponent toast_message = 10;
  }
  // Type of Icon
  api.typesv2.home.IconType icon_type = 6;
  // Action type performed on selecting the Icon
  ActionType action_type = 7;
  // Background colour
  // Deprecated but required to be sent for backward compatibility
  // Client will use this as fallback if bg_colour_v2 is not provided
  api.typesv2.ui.BackgroundColour bg_colour = 8 [deprecated = true];
  // Shadow to be shown on this icon
  api.typesv2.ui.Shadow shadow = 11;
  // unique identifier for the icon
  // can be used by client to map to enum name of api.typesv2.IconType to save user preferences in backend
  string id = 12;
  // Image/Lottie to be shown on page load
  api.typesv2.common.VisualElement visual_element = 13;
  // background color of the icon - supports block, radial, linear gradients
  // if not provided, client will use bg_colour field as fallback
  api.typesv2.common.ui.widget.BackgroundColour bg_colour_v2 = 14;
  // border color of the icon - supports block, radial, linear gradients
  api.typesv2.common.ui.widget.BackgroundColour border_color = 15;
  // Represents a highlighted tag or label associated with a feature,
  // such as cashback percentages or interest rates, to provide additional context.
  // Figma - https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28340-17387&t=bgB5yOH1Vfc7JwrV-0
  api.typesv2.ui.IconTextComponent feature_tag = 16;
}

// ---------------------------------------Icon custom actions---------------------------------------

message DropDown {
  repeated Icon icons = 1;
}

// CTA describes a call-to-action button to be used with a deeplink or custom action
// custom-action can be configured with an action or an api call to define cta behaviour
message CTA {
  // text for the CTA
  api.typesv2.common.Text text = 1;

  // any icon to be shown to the left of CTA text
  api.typesv2.common.Image image = 2;

  // field to decide whether the CTA is to be shown or not.
  // Though, the CTA will be still active i.e. the parent proto can utilise the action.
  bool is_visible = 3;

  // action to be performed via the CTA
  oneof action {
    // deeplink action to be performed
    frontend.deeplink.Deeplink deeplink_action = 4;
    // custom action to be performed
    CustomAction custom_action = 5;
  }

  // optional field to denote the background color of the cta
  api.typesv2.ui.BackgroundColour bg_color = 10;

  // optional field to denote the shadow color of the cta
  api.typesv2.ui.Shadow shadow = 11;
}

// CustomAction describes the action to be taken by the parent component using this.
// For e.g.,
// Action -> Open bottom sheet dialog.
// ActionApi -> API to be used for performing the Action. Can be unspecified in case no API is supposed to be made.
message CustomAction {
  // custom action to be performed. Note: can be UNSPECIFIED
  Action action = 1;
  // API to be called for the action.
  // Note: Can exist even if action is UNSPECIFIED. In that case, we just need to make the API call.
  CustomActionApi action_api = 2;
  // data associated with the custom action
  // note: can be empty as well
  oneof action_data {
    // data required to open a bottom sheet dialog
    OpenBottomSheetDialogActionData open_bottom_sheet_dialog = 11;
  }
}

enum Action {
  // no action to be performed
  ACTION_UNSPECIFIED = 0;
  // action to open a bottom sheet dialog.
  OPEN_BOTTOM_SHEET_DIALOG = 1;
  // action to close a bottom sheet dialog.
  DISMISS_BOTTOM_SHEET_DIALOG = 2;
}

// enum to store all the APIs needed to perform some custom actions
enum CustomActionApi {
  CUSTOM_ACTION_API_UNSPECIFIED = 0;
  // set home shortcuts api
  SET_HOME_SHORTCUTS_API = 1;
}

message OpenBottomSheetDialogActionData {
  // optional, dialog icon
  api.typesv2.common.Image icon = 1;
  // dialog title e.g "Policy deactivated due to inactive salary benefits"
  api.typesv2.common.Text title = 2;
  // dialog subtitle e.g "To activate your benefits, make sure your salary arrives in your Fi account"
  api.typesv2.common.Text subtitle = 3;
  // optional, list of cta
  repeated CTA ctas = 4;
}

// Generic Banner Message
message Banner {
  api.typesv2.common.VisualElement icon = 1;
  api.typesv2.common.Text title = 2;
  api.typesv2.ui.IconTextComponent cta = 3;
  api.typesv2.common.ui.widget.BackgroundColour bg_colour = 4;
  // Event properties for analytics (e.g., tracking clicks)
  map<string, string> event_properties = 5;
  frontend.deeplink.Deeplink deeplink = 6;
  api.typesv2.common.ui.widget.BackgroundColour border_colour = 7;
}

// ---------------------------------------Widgets---------------------------------------

// figma: https://www.figma.com/file/qOV7SpYbTdXlpzE1JxHa4I/%F0%9F%9B%A0-Home-Workfile?node-id=6405%3A101450
message HomeWidget {
  enum WidgetType {
    WIDGET_TYPE_UNSPECIFIED = 0;
    // Widget for the top navigation bar on home page
    WIDGET_TYPE_TOP_NAV_BAR = 1;
    // Widget for dashboard views - multiple views (Salary account view, Connected accounts view, Investments view etc will be present)
    WIDGET_TYPE_DASHBOARD = 2;
    // Widget for search bar
    WIDGET_TYPE_SEARCH_BAR = 3;
    // Widget to show promotions on home
    WIDGET_TYPE_PROMOTIONAL_BANNER = 4;
    // Widget to show recent and upcoming activities
    WIDGET_TYPE_RECENT_UPCOMING_ACTIVITIES = 5;
    // Widget to show "suggested for you" section on home
    WIDGET_TYPE_SUGGESTED_FOR_YOU = 6;
    // Widget to show rewards section on home
    WIDGET_TYPE_REWARDS = 7;
    // Widget to show referral section on home
    WIDGET_TYPE_REFERRAL = 8;
    // Widget to show help section on home
    WIDGET_TYPE_HELP = 9;
    // Widget to show maintenance section on home
    WIDGET_TYPE_MAINTENANCE = 10;
    // Widget to show version update section on home
    WIDGET_TYPE_VERSION_UPDATE = 11;
    // Widget to show critical notification
    WIDGET_TYPE_CRITICAL_NOTIFICATION = 12;
    // Salary account widget
    WIDGET_TYPE_SALARY_ACCOUNT = 13;

    // Widget to show the Invest Home element UI in Home V2
    WIDGET_TYPE_INVEST_HOME_ELEMENT = 14;
    // Widget to show loan/credit card banner on home
    WIDGET_TYPE_PROMOTIONAL_BANNER_2 = 15;
    // Widget to display a horizontal list of Analyser cards:
    // https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=2090-102787&t=PDiS35sZZ9esqUK5-4
    WIDGET_TYPE_ANALYSER_CARDS = 16;
    // Widget to show shortcuts for quick actions on home layout
    WIDGET_TYPE_SHORTCUTS = 17;
    // Widget for the bottom navigation bar on home page
    WIDGET_TYPE_BOTTOM_NAV_BAR = 18;
    // Widget for the pay section and it's element on home page
    WIDGET_TYPE_PAY_SECTION = 19;
    // Widget for showing only the User's Rewards/Catalog offer details for user on Home
    WIDGET_TYPE_CATALOG_OFFERS = 20;
    // Widget for showing the various Card offers (e.g. Credit/Debit cards) on Home
    WIDGET_TYPE_CARD_OFFERS = 21;
    // Widget for showing the highest priority feature on home
    WIDGET_TYPE_PRIMARY_FEATURE = 22;
    // Widget for showing all features except the highest priority on home
    WIDGET_TYPE_SECONDARY_FEATURE = 23;
    // Widget for showing trust marker on home at the bottom of screen
    WIDGET_TYPE_TRUST_MARKER = 24;
    // Display content within a card.
    // Optionally include tabs for switching between different tabs content views if available.
    // https://www.figma.com/file/qjp6KhQtDLTFKVVcrIIoy5/%F0%9F%9A%80-Invest-page%2FFFF?type=design&node-id=8693-20723&mode=design&t=Q1lU7tXjuFhkQ8R7-0
    WIDGET_TYPE_TABBED_CARD = 25;
    // https://www.figma.com/design/GNtKeutYAxPhAyBVvFxbMr/D2H-%E2%80%A2-Workfile?node-id=757-88453&t=KWtD042v9O0gTVdD-4
    // Display content for money secrets for the user
    // 'GetSecretSummaries' is called for this with entry point 'SECRETS_SUMMARIES_ENTRYPOINT_HOME'
    WIDGET_TYPE_MONEY_SECRETS = 26;
    // new Upi Widget for D2H users to connect the Upi
    // figma -> https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4076-18358&t=DMD4xbhtxu3RXMmt-4
    WIDGET_TYPE_UPI_CONNECT = 27;
    // https://www.figma.com/design/ai7gHuFQud7VOsPN0z8wh5/D2H-%E2%80%A2-FFF?node-id=4020-9184&t=Ox34SBWdlqyoHzel-1
    // Display content for journeys widget for the user
    // 'GetJourneys' is called for this with screen name 'HOME'
    WIDGET_TYPE_JOURNEYS = 28;
    // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Crore-Club?node-id=737-40289&node-type=frame&t=reSi0EkXbL0ek9xJ-0
    // Display content for wealth analyser widget
    WIDGET_TYPE_WEALTH_ANALYSER = 29;
    // Displays widget for Debit Card International state and settings
    // https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=14541-59029&t=ZjV17B1wjh7jWunC-4
    WIDGET_TYPE_DC_INTERNATIONAL_WIDGET = 30;
    // Displays widget for Activation widget
    // 'GetActivationWidget' is called for this with screen name 'HOME'
    // https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=27488-60286&p=f&t=ihSf7PIRxUbO2WIj-0
    WIDGET_TYPE_ACTIVATION_WIDGET = 31;
    // Display content for Wealth Builder Landing widget
    // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-45899&t=umTMVIxQTnf4RNZU-4
    // Deprecated, use WIDGET_TYPE_WEALTH_BUILDER_NETWORTH_DASHBOARD
    WIDGET_TYPE_WEALTH_BUILDER_LANDING = 32 [deprecated = true];
    // Display widget for Wealth Builder Networth Dashboard
    // https://www.figma.com/design/WPh41NsF1LSOy34eI7tAN6/%F0%9F%9A%80-Wealth-Builder?node-id=7343-45899&t=umTMVIxQTnf4RNZU-4
    WIDGET_TYPE_WEALTH_BUILDER_NETWORTH_DASHBOARD = 33;
  }
  WidgetType widget_type = 1;
  oneof widget {
    TopNavBarWidget top_bar_widget = 2;
    DashboardWidget dashboard_widget = 3;
    RecentAndUpcomingWidget activities_widget = 4;
    NoOpWidget no_op_widget = 5;
    SearchWidget search_widget = 7;
    BottomNavBarWidget bottom_bar_widget = 10;
  }
  api.typesv2.ui.BackgroundColour widget_bg = 6;
  api.typesv2.ui.BackgroundColour widget_bg_separator = 8;
  // [Optional] spacing to be rendered on top of this Home
  api.typesv2.ui.sdui.components.Spacer top_spacer = 9;
  // spacing to be rendered on bottom of each widget
  api.typesv2.ui.sdui.components.Spacer bottom_spacer = 12;
  // id to uniquely identify a home widget
  string id = 11;
}

// Some Home widgets don't require any widget data from layout api. We will share NoOpWidget for those
message NoOpWidget {}

message TopNavBarWidget {
  // Icons that need to be arranged towards the right end - The icons in the list will be indexed in left first order
  repeated Icon right_icons = 1;
  // Icons that need to be arranged towards the left end - The icons in the list will be indexed in left first order
  repeated Icon left_icons = 2;
}

message BottomNavBarWidget {
  // List of Icons in bottom navigation bar
  repeated Icon bottom_bar_icons = 1;
  // Sticky_icon_mapping.
  // Key in the mapping will be Icon 'Id' of bottom bar icons
  map<string, Icon> sticky_icon_mapping = 2;
  // Icon id that would be selected
  string default_selected_icon_id = 3;
  // No of times user will be shown scan and pay
  int32 occurrences_of_scan_pay_text = 4;
  // Background colour of bottom nav bar
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 5;
  // Controls whether to hide or show bottom nav bar, this will be false by default
  bool hide_bottom_nav_bar = 6;
}

message DashboardWidget {
  enum OverridingDashboardPriority {
    DASHBOARD_PRIORITY_UNSPECIFIED = 0;
    // If Overriding priority is low, client will use the priority ordering from the server,
    // and the zero state dashboard views will be sent to the last in order
    // Eg. If the investments view has a zero-state returned from the investment service,
    // but the OverridingDashboardPriority of the view is set to LOW, client will change the positioning of
    // the dashboard view to the end of the list.
    DASHBOARD_PRIORITY_LOW = 1;
    // If Overriding priority is high, client will use the priority ordering from the server,
    // and the zero state dashboard views will be ignored
    // Eg. If the investments view has a zero-state returned from the investment service,
    // but the OverridingDashboardPriority of the view is set to HIGH, client will not change the positioning of the
    // dashboard view.
    DASHBOARD_PRIORITY_HIGH = 2;
  }
  message DashboardView {
    enum DashboardViewType {
      DASHBOARD_VIEW_TYPE_UNSPECIFIED = 0;
      // View will contain the details of user's primary Savings account
      DASHBOARD_VIEW_PRIMARY_SAVINGS = 1;
      // View will contain the details of user's connected accounts
      DASHBOARD_VIEW_CONNECTED_ACCOUNTS = 2;
      // View will contain the details of user's investments
      DASHBOARD_VIEW_INVESTMENTS = 3;
      // View will contain the details of user's credit card
      DASHBOARD_VIEW_CREDIT_CARDS = 4;
      // View will contain the details of user's loans
      DASHBOARD_VIEW_LOANS = 5;
      // New dashboard view for the hey lisa card
      DASHBOARD_VIEW_INTRO = 6;
      // View will contain the details of user's net worth
      DASHBOARD_VIEW_NET_WORTH = 7;
      // View will contain the details of user's NRE Savings account
      DASHBOARD_VIEW_NRE_SAVINGS = 8;
      // View will contain the details of user's NRO Savings account
      DASHBOARD_VIEW_NRO_SAVINGS = 9;
      // View will contain the details of user's mutual funds
      DASHBOARD_VIEW_MUTUAL_FUNDS = 10;
      // View will contain the details of user's EPF
      DASHBOARD_VIEW_EPF = 11;
      // View will contain the details of user's credit score
      DASHBOARD_VIEW_CREDIT_SCORE = 12;
    }
    DashboardViewType dashboard_view_type = 1;
    // Some dashboard views contain watermark in the background - eg. "FEDERAL BANK"
    api.typesv2.common.Image dashboard_water_mark = 3 [deprecated = true];
    // We would use High here if we want to override the priority for zero-states
    OverridingDashboardPriority overriding_priority = 4;
    // Defines the colour theme of dashboard background
    api.typesv2.ui.BackgroundColour background = 5;
    // id to uniquely identify a dashboard view
    string id = 6;
  }
  repeated DashboardView dashboard_views = 1;
  // A field to control which variant of the Dashboard to render. This flag centrally controls all the Dashboard cards UI
  // version. This is to ensure all the cards show the same UI variant across services
  DashboardVersion dashboard_version = 2;
  // [Optional] Animation specs for The Footer ticker. Client will default to the values from Figma, unless overridden
  // Figma: https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=18049%3A148037&mode=dev
  FooterAnimationSpec footer_animation_spec = 3;
  // Message to control the animation parameters for a single ticker content row. E.g. how long to show the ticker,
  // whether to trigger a horizontal swipe on the ticker etc.
  // [Optional] If not set from BE, client will revert to default handling defined in the fields in this proto
  // Optional field to control zero state card variant to be shown for DASHBOARD_VERSION_V2.
  // This may only be set if dashboard_version is set to DASHBOARD_VERSION_V2
  // Even if not set, the base variant of zero state dashboard cards would be shown
  ZeroStateDashboardCardVariant zero_state_dashboard_variant = 4;

  // [Optional] Display component for the underlay of the dashboard. This will be displayed below the dashboard views.
  // Example use case is showing UPI id for savings accounts user.
  // Figma: https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=26641-4771&node-type=frame&t=8pLBxScrYqWWkT7q-0
  UnderlayDisplayComponent underlay_display_component = 5;

  message FooterAnimationSpec {
    // The number of times the vertical carousel scroll will repeat after reaching the last item. Clients will consider
    // infinite ticker scroll, unless this value is set explicitly from backend response, which enforces a maximum
    // repetition count. Can be set to 0, to disable repetition
    api.typesv2.NullInt32 max_repeat_count = 1;
    // A duration for which a ticker content is shown, before it is scrolled up. Client defaults to 3 seconds for normal
    // tickers
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=18049%3A147954&mode=dev
    api.typesv2.NullInt32 normal_ticker_visible_duration_millis = 2;
    // A duration for which a ticker content is shown, before it is scrolled up. Client defaults to 8 seconds for orange
    // warning tickers
    api.typesv2.NullInt32 orange_ticker_visible_duration_millis = 3;
    // A flag to indicate whether clients should trigger the Horizontal swipe animation on a ticker content.
    // Note: This flag should be set for Orange/Red tickers only. By default, Clients will show the horizontal swipe
    // in Alert messages, when the content is more than visible viewport
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=18049%3A147788&mode=dev
    bool is_horizontal_animation_enabled = 5;
    // The duration of the horizontal swipe/scroll animation on a ticker content. Clients will use a default of 4
    // seconds when the horizontal swipe animation needs to be shown
    // Note: This is set for Warning tickers only. Normal ticker content should stick to character limits:
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=18049%3A147788&mode=dev
    api.typesv2.NullInt32 ticker_horizontal_swipe_duration_millis = 6;
    // The initial delay in milliseconds, after which the ticker content shows a horizontal swipe animation.
    // Clients will use a default of 1.5 seconds, when horizontal swipe needs to be shown
    // Note: This should be set for Warning tickers only. Normal ticker content should stick to character limits:
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=18049%3A147788&mode=dev
    api.typesv2.NullInt32 ticker_horizontal_swipe_start_delay_millis = 7;
  }
}

message UnderlayDisplayComponent {
  // Key represents the label or title for the value to be displayed on the dashboard.
  // Example: "UPI ID"
  api.typesv2.common.Text key = 1;
  // Value represents the actual data to be displayed on the dashboard, specific to the user.
  // Example: "abc@fifederal"
  api.typesv2.common.Text value = 2;
  // is_copyable is a flag indicating whether the displayed value can be copied by the user.
  // If true, the value can be copied; otherwise, it cannot.
  bool is_copyable = 3;
}

message RecentAndUpcomingWidget {
  enum ActivityType {
    ACTIVITY_TYPE_UNSPECIFIED = 0;
    ACTIVITY_TYPE_RECENT = 1;
    ACTIVITY_TYPE_UPCOMING = 2;
  }
  repeated ActivityType activity_types = 1 [deprecated = true];
  api.typesv2.ui.Filter filter = 2;
  // Key here will be tab's id
  map<string, ActivityType> tab_to_activity_type = 3;
  // title of the widget
  api.typesv2.common.Text title = 4;
}

message SearchWidget {
  repeated api.typesv2.ui.Shadow shadow = 1;
}

// An Enum to enforce the Ui variant of Dashboard cards to render on Home. This is sent in
// GetHomeLayoutAndWalkthroughResponse, and is used in individual Dashboard card service RPC calls as a request param.
// If this value is not available, clients and individual Dashboard card RPCs should default to the base version of the
// Ui
enum DashboardVersion {
  // Base version of the Ui:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=824-23705&mode=dev
  DASHBOARD_VERSION_UNSPECIFIED = 0;

  // Updated Ui with Compact look and some text ticket/iconography updates:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=17984%3A133766&mode=dev
  DASHBOARD_VERSION_V2 = 1;
}

// This is an enum to specify the zero state card variant to show on DASHBOARD_VERSION_V2.
// This flag will be sent as response in GetHomeLayoutAndWalkthroughResponse, and is used in individual Dashboard card
// service RPC calls as a request param.
// If this value is not available, clients and individual Dashboard card RPCs should default to the base variant of the
// Ui
enum ZeroStateDashboardCardVariant {
  // Base version of the Ui:
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=18224-223863&mode=design&t=V1INpljAT3dsPO9B-0
  ZERO_STATE_DASHBOARD_CARD_VARIANT_UNSPECIFIED = 0;

  // Variant 2
  // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=18224-223863&mode=design&t=V1INpljAT3dsPO9B-0
  ZERO_STATE_DASHBOARD_CARD_VARIANT_V2 = 1;
}

message HomeDashboard {
  // Title to show on the dashboard card
  api.typesv2.common.Text title = 1;
  // Parameters that contribute to the body of the dashboard view
  message Body {
    // Image used in-place of money values in privacy mode
    api.typesv2.common.Image privacy_mode_image = 1;
    // Dashboard icons which are shown near the main content text. For example, Hide balance icon near the savings balance
    repeated Icon dashboard_icons = 2;
    // Money value for each dashboard card with UI elements
    repeated api.typesv2.common.Text money_value = 3 [deprecated = true];
    // Money value to be shown along with any images required.
    repeated api.typesv2.ui.IconTextComponent money_value_v2 = 8;
    // Message to be shown on the dashboard
    // Fi savings summary - will show the last synced message
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=824%3A23705&t=15D5xp1sPyRhSXBN-1
    // Note: this is deprecated in favor of [message_v2]
    string message = 4 [deprecated = true];
    // Show last synced message
    bool show_last_synced_msg = 6;
    // Description to be shown below the body text
    // https://www.figma.com/file/7gQRgxlw1QbNMf3ooZOXMu/Credit-Card-%E2%80%A2%C2%A0Workfile?node-id=17137-74617&t=nlnYO7Fg9WtOLRyL-0
    api.typesv2.common.Text description = 9;
    // State of the dashboard view can be zero or non-zero
    enum State {
      STATE_UNSPECIFIED = 0;
      // Zero state is when the user has not started using the service.
      // If user has not made any investments they would be in zero state for investment dashboard view.
      STATE_ZERO = 1;
      // Non-zero state is when the user has initiated usage of the dashboard service.
      // If the user has linked non-zero number of connected accounts, the state for connected accounts dashboard view would be non-zero.
      STATE_NON_ZERO = 2;
    }
    State dashboard_state = 5;
    // Watermark to be shown on dashboard card
    repeated api.typesv2.common.Image watermark_images = 7;

    Loader loader = 10;
    // [Optional] Message to be shown near the title of the card as per new Dashboard Ui. The icon text component may
    // also send a Icon when necessary:
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=18224-223530&mode=dev
    api.typesv2.ui.IconTextComponent message_v2 = 11;
  }
  // Parameters that contribute to the body of the dashboard view
  Body body = 2;
  // Additional Dashboard icons which can be seen on clicking the 3 dots on the dashboard view
  repeated Icon additional_settings_icons = 4;
  // Footer content in the dashboard view. (Deprecated: We will eventually move to FooterTicker proto. footer and
  // privacy_mode_footer will be initially supported parallely to enable A/B testing of these constructs v.s. the new
  // footer_ticker proto)
  repeated api.typesv2.ui.IconTextComponent footer = 5 [deprecated = true];
  // User will be redirected to the deeplink on clicking the dashboard
  deeplink.Deeplink deeplink = 6;
  // Parameters for shadow in ui
  repeated api.typesv2.ui.Shadow shadow = 7;
  // Footer content in privacy mode of the dashboard view. (Deprecated: We will eventually move to FooterTicker proto.
  // footer and privacy_mode_footer will be initially supported parallely to enable A/B testing of these constructs v.s.
  // the new footer_ticker proto)
  repeated api.typesv2.ui.IconTextComponent privacy_mode_footer = 8 [deprecated = true];
  // Bool to decide whether to show or hide the card
  bool hide_card = 9;
  // Image to be shown when the card is in zero state
  api.typesv2.common.VisualElement zero_state_image = 10; // https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=1741-95964&t=bBH0T1l6cGtjcfIN-4
  // bool flag to hide the three dots which contain account settings
  bool hide_settings_icon = 11;

  // Footer ticker message for this dashboard card
  // Deprecated: We will use dashboard_footer one-of instead to drive various footer designs throughout the dashboards
  FooterTicker footer_ticker = 12 [deprecated = true];

  // Message to define the entire list of Footer items in a dashboard cards along with Animation properties
  message FooterTicker {
    // List of Footer ticker icon rows/items to show one-by-one
    repeated TickerItem ticker_items = 1;
    // [Optional] List of Footer ticker icon rows/items to show when user hides the data using privacy toggle
    // If no privacy items are set, footer will not show any content
    repeated TickerItem ticker_items_privacy = 2;
    // [Optional] An enum specifying the maximum width of the ticket text box
    TickerTextWidth max_ticker_text_width = 3;
    // Object to render a single Footer row item in the Dashboard footer tickers list
    message TickerItem {
      // The ticker Icon text component to display
      api.typesv2.ui.IconTextComponent ticker_content = 1;
      // The type of ticker (whether it is an Alert or Normal text)
      TickerType ticker_type = 2;
      // Enum to specify whether this TicketItem is a normal, or a Red or Orange warning ticker text
      enum TickerType {
        // Normal ticker text
        TICKER_TYPE_UNSPECIFIED = 0;
        // Orange ticker text. The ticker list from BE should start with this orange ticker. Orange ticker behaviour
        // specs: https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=18049%3A147954&mode=dev
        TICKER_TYPE_WARNING_ORANGE = 1;
        // Ticker text with Red warning. Normally, BE should send only the Red ticker text item when applicable:
        // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=18049%3A147955&mode=dev
        TICKER_TYPE_WARNING_RED = 2;
      }
    }
    // Enum to specify the maximum width that the ticket text box will take on clients. This is enum and not Dp/point
    // directly as it will allow clients to tweak the width values without BE adjustments everytime
    enum TickerTextWidth {
      // Text width to allow max text box width to be 164 (points in iOS or dp in Android):
      // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=18049-147759&mode=dev
      TICKER_TEXT_WIDTH_UNSPECIFIED = 0;
      // Text width to allow max text box width to be 134 (points in iOS or dp in Android):
      // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?type=design&node-id=18049-147820&mode=dev
      TICKER_TEXT_WIDTH_COMPACT = 1;
    }
  }
  // Message to define footer in which items are horizontally stacked one after the other
  message FooterHorizontal {
    // footer items to be rendered in normal mode
    repeated FooterItem footer_items = 1;
    // footer items to be rendered in privacy mode
    repeated FooterItem footer_items_privacy = 2;
    // These items are horizontally stacked one after the other
    message FooterItem {
      // header of the footer item, eg- a/c balance
      api.typesv2.common.Text title = 1;
      // value under the item header, eg- Rs 0
      api.typesv2.common.Text value = 2;
      // screen to redirect to when user clicks on the item
      frontend.deeplink.Deeplink deeplink = 3;
    }
  }
  // one of message to identify which type of footer to be rendered on the dashboard
  oneof dashboard_footer {
    FooterTicker dashboard_footer_ticker = 13;
    FooterHorizontal dashboard_footer_horizontal = 14;
  }
  // one-of error message to identify which type of footer error is to be displayed on the dashboard
  // Clients will handle the dashboard response rendering in the following manner:
  // 1. If this field is present in the response, it will mean that BE will only send this field in the dashboard card to indicate an error in fetching the dashboard RPCs from downstream APIs
  // 2. When clients receive this field, they will append this field with previously cached dashboard data (if any). This will indicate to the users that the dashboard state is stale
  // Note: this handling is agnostic of the Retry logic, which solely depends on the RetryStrategy object in  the response. This means that potentally users can see the Stale error state powered by this field, while we also trigger retry in the background
  oneof dashboard_footer_error_message {
    FooterTicker dashboard_footer_ticker_error_message = 16;
    FooterHorizontal dashboard_footer_horizontal_error_message = 17;
  }
  // overlay will be layered above the entirety of the dashboard
  // client keeps a track of overlays and dismisses it permanently if user clicks on the cta
  Overlay overlay = 15;
  message Overlay {
    // to allow client to keep track of overlays
    string id = 1;
    // usually a black blurred colour
    api.typesv2.common.ui.widget.BackgroundColour bg_colour = 2;
    // title to be displayed on top
    api.typesv2.common.Text title = 3;
    // cta to be displayed under the title
    CTA cta = 4;
  }


  // Background component to be rendered on the dashboard.
  // This allows for both static and dynamic visual enhancements, like background images, gradients, and animations.
  oneof dashboard_background {
    // Background image or Lottie animation for the dashboard, enabling dynamic themes for the new home dashboard design.
    // can be used for special occasions like Christmas, festivals, or promotional events.
    // figma - https://www.figma.com/design/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=28045-6174&t=swEiRrQfCadlcZ9A-0
    api.typesv2.common.VisualElement background_visual_element = 18;

    // Background color of the dashboard, supporting solid and gradient styles.
    api.typesv2.common.ui.widget.BackgroundColour background_colour = 19;
  }

  // Border color of the dashboard, supporting solid and gradient styles.
  api.typesv2.common.ui.widget.BackgroundColour border_color = 20;
}

message Loader {
  enum LoaderType {
    LOADER_TYPE_UNSPECIFIED = 0;
    LOADER_TYPE_DOTS = 1; // https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=2287-35590&t=bBH0T1l6cGtjcfIN-4
    LOADER_TYPE_DASH = 2; // https://www.figma.com/file/z14QLvmDFyFCAkJGnOypS6/%E2%9A%A1%EF%B8%8F-Fi-Lite---Review-forum?type=design&node-id=1738-94293&t=bBH0T1l6cGtjcfIN-4
  }

  // Loader to represent feature progress
  // In case BE doesn't choose anything we will consider DASH as the default type
  LoaderType loader = 1;
  int32 total_points = 2;
  float filled_points = 3;
  string filled_colour = 4;
  string unfilled_colour = 5;
}

// Walk through popup with contain the required parameters for each pop that will be shown to the user
message WalkThroughPopUp {
  // Message shown in popup
  string message = 1;
  enum PopUpType {
    POP_UP_TYPE_UNSPECIFIED = 0;
    // pop up for user on nudge section
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=818%3A27746&t=FIm3G8gXr6tZqu3G-1
    POP_UP_TYPE_NUDGE = 1;
    // pop up to show on additional settings in fi savings dashboard
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=829%3A15149&t=FIm3G8gXr6tZqu3G-1
    POP_UP_TYPE_ADDITIONAL_SETTINGS = 2;
    // pop up to show on hide/un-hide settings icon
    POP_UP_TYPE_HIDE_UN_HIDE = 3;
    // pop up that explains on dashboard view
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=1847%3A104416&t=FIm3G8gXr6tZqu3G-1
    POP_UP_TYPE_DASHBOARD_VIEW = 4;
    // pop up that shows where they can find FIT rules
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=4485%3A38889&t=FIm3G8gXr6tZqu3G-1
    POP_UP_TYPE_FIT_RULES = 5;
    // pop up that shows user where they can start saving and invest
    // https://www.figma.com/file/kRieINOll9fKGti3TgfmX7/%F0%9F%9A%80-Home-2.0?node-id=1847%3A110697&t=FIm3G8gXr6tZqu3G-1
    POP_UP_TYPE_SAVE_AND_INVEST = 6;
    // pop up that shows user where they can find their personalised shortcuts on home page
    // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%9B%A0%EF%B8%8F-Home-Workfile-2?type=design&node-id=3360-132953&mode=design&t=GA50B3HeDJOKbjRB-0
    POP_UP_TYPE_HOME_SHORTCUTS = 7;
    // pop up that shows users to tap the plus button to personalise their shortcuts
    // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%9B%A0%EF%B8%8F-Home-Workfile-2?type=design&node-id=3360-132953&mode=design&t=GA50B3HeDJOKbjRB-0
    POP_UP_TYPE_ADD_AND_MANAGE_SHORTCUTS = 8;
    // pop up that shows users how to re-order their shortcuts
    // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%9B%A0%EF%B8%8F-Home-Workfile-2?type=design&node-id=3360-132953&mode=design&t=GA50B3HeDJOKbjRB-0
    POP_UP_TYPE_REORDER_SHORTCUTS = 9;
    // pop up that shows users how to add a new shortcut
    // https://www.figma.com/file/aeOCGwwKLtdAcdFBVHLeBf/%F0%9F%9B%A0%EF%B8%8F-Home-Workfile-2?type=design&node-id=3635-158515&mode=design&t=zum40jxZbqAamBiW-4
    POP_UP_TYPE_ADD_SHORTCUTS = 10;
  }
  PopUpType pop_up_type = 2;
  // Field to show button information. For now its used to show text and its properties on the button.
  api.typesv2.ui.IconTextComponent info_button = 3;
}

message OnboardingWalkthrough {
  bool enabled = 1;
  // List of popups shown to the user explaining components in new home
  repeated WalkThroughPopUp onboarding_walk_through = 2;
  enum Type {
    TYPE_UNSPECIFIED = 0;
    // If user is taken through old user onboarding walk through flow
    TYPE_OLD_USER_WALK_THROUGH = 1;
    // If user is taken through new user onboarding walk through flow
    TYPE_NEW_USER_WALK_THROUGH = 2;
  }
  Type walk_through_type = 3;
}

message PrivacySettingWalkthrough {
  bool enabled = 1;
  // List of popups shown to the user explaining add money flow
  repeated WalkThroughPopUp privacy_setting_walk_through = 2;
}

message DashboardIntroCardDetails {
  // Icon for the user to get started with walk through
  Icon start_icon = 1;
  // Max number of impressions for which the intro card will be shown
  int32 show_card_max_impressions = 2;
  // Title to be shown on intro card
  api.typesv2.common.Text title = 3;
  // Image to be shown on intro card
  api.typesv2.common.Image image = 4;
  // Shadow for home intro card
  repeated api.typesv2.ui.Shadow shadow = 5;
}

message HomeShortcutsWalkthrough {
  bool enabled = 1;
  // List of popups shown to the user explaining how to add shortcuts on home screen
  repeated WalkThroughPopUp home_shortcuts_walk_through = 2;
}
