syntax = "proto3";

package frontend.fittt.clientstate;

option go_package = "github.com/epifi/gamma/api/frontend/fittt/clientstate";
option java_package = "com.github.epifi.gamma.api.frontend.fittt.clientstate";

enum SubscriptionPageType {
  SUBSCRIPTION_PAGE_UNSPECIFIED = 0;
  SUBSCRIPTION_PAGE_NEW = 1;
  SUBSCRIPTION_PAGE_UPDATE = 2;
  // SUBSCRIPTION_PAGE_UPDATE_SD will prioritize updating Smart Deposit(SD) param.
  // SD param will be marked as selected for editing first.
  SUBSCRIPTION_PAGE_UPDATE_SD = 3;
  // page type used to capture the user's choice of the fit rule params
  // note:
  // 1) client will not subscribe to any rule for this page type, it'll just capture the user's choice of rule params
  // 2) client should display the "primary" button instead of "swipe" button for this page type
  // example usage: we use this page type to capture the user's choice of rule params if the user want to customize the
  // suggested rule params in the deposit inline auto-save suggest flow
  SUBSCRIPTION_PAGE_CAPTURE_PARAMS = 4;
}

enum FitScreen {
  NOT_SPECIFIED = 0;
  // screen for collections info page, with a default tag selected
  COLLECTION_PAGE = 1;
  // screen for all collections page
  ALL_COLLECTIONS_PAGE = 2;
  // screen for my rules page
  MY_RULES_PAGE = 3;
  // screen for customise rule page
  CUSTOMISE_RULE_SCREEN = 4;
  // screen for full subscription info
  SUBSCRIPTION_INFO_PAGE = 5;
  // screen displaying subscriptions grouped on tags
  SUBSCRIPTIONS_PREVIEW_PAGE = 6;
  // screen displaying subscriptions grouped on rule
  ALL_SUBSCRIPTIONS_PAGE = 7;
  // subscription success card screen
  SUBSCRIPTION_SUCCESS = 8;
  // screen for Fit rule history
  RULE_HISTORY_SCREEN = 9;
}
