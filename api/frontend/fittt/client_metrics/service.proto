syntax = "proto3";

package frontend.fittt.client_metrics;

import "api/rpc/method_options.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/fittt/fittt.proto";

option go_package = "github.com/epifi/gamma/api/frontend/fittt/client_metrics";
option java_package = "com.github.epifi.gamma.api.frontend.fittt.client_metrics";

// ClientMetricsService will provide RPCs to sync details required to schedule the periodic jobs and track users' activities in client
// It will also provide list of conditions to be evaluated
// The client will also use the RPCs defined here to share condition evaluation results. Since server can't directly execute condition, the client
// will pull requests from all the data required for executing condition and push the result back through another RPC.
service ClientMetricsService {
  // RPC to sync the metrics that needs to be tracked in the client. Server will send the list of all metrics that needs to be tracked in
  // the client. Client has to compare the list returned by the server against the existing metrics
  // tracked in the client and cleanup any metrics that are not listed in the latest result.
  rpc GetClientMetricsAndConditionsToBeEvaluated (GetClientMetricsAndConditionsToBeEvaluatedRequest) returns (GetClientMetricsAndConditionsToBeEvaluatedResponse) {
    option (rpc.auth_required) = true;
  }
  // RPC to share the results evaluated in the client for the conditions returned in the last sync call.
  rpc NotifyConditionResults (NotifyConditionResultsRequest) returns (NotifyConditionResultsResponse) {
    option (rpc.auth_required) = true;
  }
  // returns list of all apps supported by Client rules eg: Zen Mode
  rpc GetClientRulesSupportedApps(GetClientRulesSupportedAppsRequest) returns (GetClientRulesSupportedAppsResponse){
    option (rpc.auth_required) = true;
  }
}

message GetClientRulesSupportedAppsRequest{
  frontend.header.RequestHeader req = 1;
}

message GetClientRulesSupportedAppsResponse{
  enum Status {
    // request was successful.
    OK = 0;
  }
  frontend.header.ResponseHeader resp_header = 1;
  repeated AppValue supported_apps = 2;
}

message GetClientMetricsAndConditionsToBeEvaluatedRequest {
  frontend.header.RequestHeader req = 1;
}

message GetClientMetricsAndConditionsToBeEvaluatedResponse {
  enum Status {
    // request was successful.
    OK = 0;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // List of all metrics that needs to be tracked in the client;
  // server will always return all the metrics that needs to be tracked for the rules
  // subscribed by the user.
  // since we do not require client to enable tracking for app usage, we do not require to provide name of app
  repeated MetricType metrics_to_be_tracked = 2;
  // List of conditions that needs to be evaluated and results
  // should be sent in NotifyConditionResults call.
  map<string, ConditionData>  condition_data = 3;
}

message NotifyConditionResultsRequest {
  frontend.header.RequestHeader req = 1;
  // Results of conditions that are sent by the server in the last Sync call.
  ConditionResult condition_result = 2;
}

message NotifyConditionResultsResponse {
  enum Status {
    // request was successful.
    OK = 0;
  }
  frontend.header.ResponseHeader resp_header = 1;
}
