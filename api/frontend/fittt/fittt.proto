// protolint:disable MAX_LINE_LENGTH

// RPCs related to Fittt FE service .

syntax = "proto3";

package frontend.fittt;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/investment/mutualfund/mutual_fund.proto";
import "api/frontend/recurringpayment/recurring_payment.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/common.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/frontend/fittt";
option java_package = "com.github.epifi.gamma.api.frontend.fittt";

// FitttRule is a single displayable rule card present on Fi-home screen and FiTTT landing page.
//
// A sample rule definition for "When I make a payment on `Swiggy` greater than `500` , save `5`% of the amount to SD `Test`"
//
//  a) name -> `Save the Change`
//  b) description -> {
//      "display_str":"When I make a payment on {configuredMerchantName} greater than Rs.{configuredMinAmount} , save {configuredPercentageVal}% of the amount to SD {configuredSdName}"
//      "inputParams":
//        [
//          {
//            "inputName":"configuredMerchantName"
//            "inputType": 2 {Merchant}
//          },
//          {
//            "inputName":"configuredMinAmount"
//            "inputType": 1 {Decimal}
//          },
//          {
//            "inputName":"configuredPercentageVal"
//            "inputType": 7 {Number}
//          },
//          {
//            "inputName":"configuredSdName"
//            "inputType": 6 {SmartDepositName}
//          }
//        ]
//    }
message FitttRule {
  // every rule has a defined name. For example:
  // a. 'Healthy wealthy and wise'
  // b. 'Keep the change'
  // c. 'Spend responsibly'
  // .... so on
  string name = 1;
  // contains the pre-build default description text to be displayed on landing page
  // replaces the configurable parameters to there default value and uses <u> </u> to denote configurable params
  string description = 2;
  // number of times the current actor has subscribed to this rule
  // If the number of subscriptions are greater than 0, the rule is considered 'Active' for the actor.
  int32 num_subscriptions = 3;
  // for every card, there is a catchy image. Thanks to our talented design team.
  string image_url = 4;
  // a dynamic summary to be displayed on each card. It is an optional parameter and might be absent for some cards.
  // Sample summary:
  // a. 'In use by 234 people'
  // b. 'Active rule'
  // Deprecated: this field is not used anymore on UI, hence not maintained at backend
  string summary = 5 [deprecated = true];
  // background color for the rule card
  string bg_color = 6;
  // Description contains the descriptive string, and configurable parameters
  RuleDescription rule_description = 7;
  // rule state
  RuleState state = 8;
  // helps identify if the rule was of type AUTO_SAVE, REMINDER, AUTO_PAY etc
  RuleCategory category = 9;
  // if its equal to SAVE_TO_SD, UI needs to make an additional call to fetch the list of SDs created by the user already.
  // If the list is non-empty, present the list of SDs to the user to choose from.
  // If the list is empty, need to redirect user to the SD creation.
  FitttRuleActionType rule_action_type = 10 [deprecated = true];
  string ruleId = 11;
  // rule description to be displayed on home screen
  // Deprecated: this field is not used anymore on UI, hence not maintained at backend
  string home_screen_desc_string = 12 [deprecated = true];
  // there can be certain actions which are required to be performed before subscribing to a rule,
  // or there can be certain actions which should be performed in order to make a rule function.
  // To perform these actions, clients might need to use the parameters provided in pre_req_action_type_values field
  repeated SubscriptionPreRequisiteActionType pre_req_action_types = 13;
  // in case of multiple subscriptions of a rule, latest_subscription_id holds the latest created subscription
  string latest_subscription_id = 14;
  // Provide the info about tags
  repeated Tag tags = 15;
  // event type for fittt rule
  EventType event_type = 16;
  // For AUTO_INVEST rules, if the user is not onboarded into FIT investments,
  // SubscriptionPreRequisiteActionType will have COMPLETE_INVESTMENTS_ONBOARDING and
  // next_investment_onboarding_step will have deeplink for the steps that the user has to follow to complete onboarding
  // and then proceed to rule subscription.
  frontend.deeplink.Deeplink next_investment_onboarding_step = 17;
  // info on how the rule works
  HowRuleWorks how_rule_works = 18;
  RuleTypeForSpecialHandling rule_type_for_special_handling = 19;
  // story url and story visibility status for each rule
  // if it is a new subscription, we show the story,
  // else for update or user already has a subscription, we skip showing the story
  Story story = 20;
  // there can be certain actions which is required to be performed before subscribing to a rule
  // for the actions, pre required informations provided in this property
  repeated SubscriptionPreRequisiteActionTypeValue pre_req_action_type_values = 21;
}

message Story {
  message StoryPayLoad {
    string title = 1;
    string description = 2;
    string url = 3;
    string iconUrl = 4;
    string bgColor = 5;
  }
  // id can be used for analytics purposes as well
  string id = 1;
  // if true, we show the story by default when customize rule page opens
  bool show_on_screen_load = 2;
  // story payload
  StoryPayLoad payload = 3;
}

// there are specific handling required for certain set of rules at FE layer mostly.
// Instead of identifying the rule based on name, type enum should be used for identification
// this would remove dependency of having rule name constants in the code
// multiple rules can have same type, if they share common behaviour
enum RuleTypeForSpecialHandling {
  RULE_TYPE_UNSPECIFIED = 0;
  RULE_TYPE_APP_USAGE = 1;
  RULE_TYPE_INVEST_THE_CHANGE = 2;
  RULE_TYPE_AUTO_INVEST_DAILY = 3;
  RULE_TYPE_AUTO_INVEST_WEEKLY = 4;
  RULE_TYPE_AUTO_INVEST_MONTHLY = 5;
  RULE_TYPE_US_STOCKS_SIP = 6;
}

message HowRuleWorks {
  // title for how the rule works bottom-sheet
  Text title = 1;
  // detailed information on how the rule works
  repeated Text info = 2;
}

// Description defines a display string which would be shown on UI
// string contains some input parameters, Value for which will be taken as input from user on subscription
message RuleDescription {
  string display_str = 1;
  repeated RuleParam input_params = 2;
  // misc strings as per the rule UI. Nullable field and might be empty for few rules.
  // For eg: "this rule is applicable on transactions above Rs 50"
  string footer = 3;
  // rule disclaimer
  string disclaimer = 4;
  // display text based on screen type [creating new subscription or updating an existing subscription]
  // ex: "SWIPE TO ACTIVATE" or "SWIPE TO UPDATE" or "Confirm Details" or empty
  string rule_subscription_action_text = 5;
  // `selected_param_index` is index of RuleParam from `input_params` which needs to be selected by default on customization page
  int32 selected_param_index = 6;
  // List of execution infos which may include execution start date, total number of executions.
  // The list can be empty depending on the nature of rule.
  repeated Tag execution_infos = 7;
  // optional checkbox. for eg: I understand that ELSS funds comes with a 3-year lock-in period.
  // https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?type=design&node-id=13447-47683&mode=design&t=TvKuNkG57LoV7PY7-0
  // https://drive.google.com/file/d/1o9lB85iSzTJu035tBk47IYmis-M6lsa9/view?usp=sharing
  api.typesv2.common.ui.widget.CheckboxItem checkbox = 8;

  // Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31722-13935&t=l6yE7iaMcUTmlKOg-0
  FooterComponent footer_component = 9;
}

message FooterComponent {
  oneof footer {
    // Invoice footer is used to display the invoice details of a US stocks SIP and is dependent
    // on rule parameter values like amount, etc.
    // When a user interacts with the rule parameters, clients are expected to get the updated footer again.
    InvoiceFooter invoice_footer = 1;
  }
}

// Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31722-13935&t=5Cxc6pMOW3kPaSlh-0
message InvoiceFooter {
  // Eg: bg color - #EFF2F6
  string bg_color = 1;
  // Eg: Approx. SIP amount in USD
  api.typesv2.common.Text title = 2;
  // Eg: $80.25
  api.typesv2.common.Text value = 3;
  // Navigate to this deeplink on click
  frontend.deeplink.Deeplink deeplink = 4;
}

// RuleParam defines all the configurable parameters which is part of rule definition
// all the RuleParams will be taken as input from user on subscription
// each param defines a name for the variable and type of value that is expected and data type of the value
// for more on RuleParamType read the comments on the enum definition
message RuleParam {
  // name of the variable
  string name = 1;
  // based on this, UI decides how to take input from the user. For example: display a predefined list of merchants,
  // provide a date picker, provide a text box for input
  RuleParamType input_type = 2;
  // The default value of the param to be displayed to the user.
  Value default_value = 3;
  // the list of possible values the param can take.
  repeated Value possible_values = 4;
  // the tip string to be displayed during hover/long-press
  string tool_tip = 5;
  // title of the dialog for displaying possible values
  string possible_values_title = 6;
  // selected_for_edit denotes that this param should be selected for edit first.
  RuleParamEditState edit_state = 7;
  // Optional - Text to display when the possible values list is empty.
  // This will be populated in cases like auto-invest rule where the possible values can only be computed after the selection of mutual fund.
  string empty_possible_values_text = 8;
  // bottom text for a param
  // eg: payee update is not allowed
  Text bottom_text = 9;
  // footer for calender as of now
  // figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=31722-12260&t=qLl9LcLzW73h71MM-0
  // Eg: ⭐ You’ve already set up SIP on the 1st of every month. Choose the same date to avoid recurring GST charges of ₹45 or more.
  api.typesv2.ui.IconTextComponent footer = 10;
}

message MerchantParamValue {
  string icon_url = 1;
  string name = 2;
}

message SdParamValue {
  // name of the SD
  string name = 1;
  // account identifier
  string account_id = 2;
  // masked account number
  string masked_account_number = 3;
}

message MutualFundValue {
  // unique id for the fund
  string mf_id = 1;
  // name of the fund
  string name = 2;
  // payment related metadata required to execute the payment.
  MFPaymentRequestInfo pay_info = 3 [deprecated = true];
  // icon url for mutual fund or AMC
  string icon_url = 4;
  // payment related metadata required to execute the payment.
  frontend.investment.mutualfund.PaymentInfo payment_info = 5;
  // Name of the fund house i.e AMC name.
  string amc_name = 6;
  // eg: Gold, Equity, Debt etc
  Text mf_category_name = 7;
  // eg: Want to beat inflation? Invest in gold; Invest in India's Top 50 companies via Nifty 50
  Text summary = 8;
  // text stating return rate for particular MF
  ReturnRateInfo return_rate_info = 9;
  // order sub type supported by the particular MF
  // eg: SIP, Lump sump etc
  investment.mutualfund.OrderSubType order_sub_type = 10;
  // investment_auth_id is the authentication-id generated after verifying the otp during sip registration.
  string investment_auth_id = 11;
}

message ReturnRateInfo {
  // eg: 12.9%
  Text return_rate = 1;
  // eg: 1Y RETURNS or 5Y RETURNS
  Text rate_details = 2;
}

// Mutual Fund Payment Request Info
// Equivalent of api.investment.mutualfund.order.PaymentRequestInfo
message MFPaymentRequestInfo {
  // mandatory when payment mode is SI
  string recurring_payment_id = 1;
}

// FitttAction helps to configurable the CTAs on FiTTT digest and FiTTT landing page.
message FitttAction {
  // generally the cta on the action.
  // For example:
  // a. 'View my rule'
  // b. 'Explore more rules'
  // c. 'Pause'
  // d. 'Resume'
  string display_str = 1;
  FitttActionType type = 2;
  // if false, CTA is disabled on this action.
  bool enabled = 3;
  // display string during hover/long-press
  string tool_tip = 4;
  // background color for action icon
  string bg_color = 5;
  string id = 6;
  // if cta is defined for a particular FitttAction, corresponding deeplink should be processed
  CTA cta = 7;
}

// RuleSubscription contains user input parameters which are configured by the user while subscribing to a rule
// subscribed rule can be paused/resumed, and the state would be preserved
message RuleSubscription {
  // the actual rule details
  FitttRule rule = 1;
  // input preferences while subscribing the rule
  // for eg: When I make a payment on {confMerchantName} greater than Rs.{minAmount} , save {percentageVal}% of the amount to SD {sdName}
  // here `confMerchantName`, `minAmount`, `percentageVal`, `sdName` are the rule_param_values
  RuleParamValues ruleParamValues = 2;
  // the total amount saved with this rule subscription till now
  api.typesv2.Money amount_saved = 3;
  // timestamp of rule subscription
  google.protobuf.Timestamp valid_from = 4;
  // expiration timestamp of subscription
  // this will be used in rules such as: Transfer `10k` to `MOM` on every `1`st of month for `12` months
  // there can be multiple rules where validTill is not provided or null
  // such rules will be considered as non-expiring rules
  google.protobuf.Timestamp valid_till = 5;
  // defines state of the rule subscription (Active, Inactive, Paused)
  RuleSubscriptionState state = 6;
  // unique identifier of Subscription
  string id = 7;
  // to be displayed when the rule is paused
  // "Rule is temporarily paused" as per the UI mocks
  string paused_display_str = 8;
  // timestamp when this rule was last run(executed)
  google.protobuf.Timestamp last_run = 9;
  // background color for the subscription detail card
  string bg_color = 10;
  // timestamp for next execution of subscription
  google.protobuf.Timestamp next_execution_time = 11;
}

message RuleParamValues {
  // considering above example, {"confMerchantName":"Swiggy", "minAmount":500, "percentageVal":5, "sdName":"Test"} are the rule_param_values
  map<string, Value> rule_param_values = 1;
  // notes can be used in case user wants to add notes which can be tagged to payments on rule execution
  // eg: Rent, iPhone EMI etc
  string notes = 2 [(validate.rules).string.max_len = 100];
}

// Value for the user provided inputs on rule subscription
message Value {
  // since the value field may contain either
  // string eg: actorId, upiId, Merchant name ..
  // integer eg: percentage val, date of month ..
  // double eg: payment_amount ..
  oneof value {
    string str_val = 1;
    int32 int_val = 2;
    double double_val = 3;
    SdParamValue sd_value = 4;
    api.typesv2.Money money_val = 5;
    MerchantParamValue merchant_val = 6;
    CricketTeamValue team_value = 8;
    CricketPlayerValue player_value = 9;
    FootballPlayerValue football_player_val = 10;
    FootballTeamValue football_team_val = 11;
    AppValue app_val = 12;
    Duration duration = 13;
    MutualFundValue mutual_fund_val = 14;
    // CTA to redirect to Investment flow and select mutual fund
    MutualFundSelectorCTAValue mutual_fund_selector_cta_val = 15;
    // CTA to enter custom deposit amount for Money param.
    CustomAmountCTAValue custom_amount_cta_val = 16;
    // param to get date as input from user
    // client should show calendar with dates on UI
    DateVal date_val = 18;
    // field to select payment recipient
    // if CTA is populated, client to show add payee button on UI
    // if selected_val is populated, client to display existing recipient
    RecipientSelector recipient_selector = 19;
    // for recurring rules, frequency for the execution of the rule
    FrequencyVal frequency_val = 20;
    // Custom Amount Selector CTA which behaves like recurring payment
    CustomAmountSelector custom_amount_selector = 21;
    // day of the week
    DayOfWeekVal day_of_week_val = 22;
    // date of a month
    // valid entries can be from 1 to 28
    DateOfMonthVal date_of_month_val = 23;

    USStockValue us_stock_value = 24;
  }
  // Id to identify value uniquely on client
  string id = 7;
  // required for multiple subscription support, applicable for unique params only
  // if the subscription exists with this param, is_selected flag is true
  bool is_selected = 17;
}

message USStockValue {
  // Identifier of a stock / ETF listed in US stock exchanges
  string stock_id = 1;

  // Name of the stock to show to user
  string stock_name = 2;
}

message DayOfWeekVal {
  // eg: Monday, Tuesday etc - parsed from google/type/dayofweek.proto and converted to Titlecase
  string weekday = 1;
  // eg: Mon, Tue, Thu etc
  string abbr_name = 2;
  // bool to check if the day is enabled to select by user
  bool enabled = 3;
}

message DateOfMonthVal {
  // eg: 1, 2, 3 etc
  int32 date = 1;
  // eg: 1st, 2nd, 3rd etc
  string display_string = 2;
  // bool to check if the date is enabled to select by user
  bool enabled = 3;
  // bool to check if date is highlighted
  bool is_highlighted = 4;
  // label to show along with the value in UI
  // Eg: SAVES GST
  api.typesv2.ui.IconTextComponent label = 5;
}

message CustomAmountSelector {
  // amount entered info
  CustomAmountCTAValue custom_amount_cta = 1;
  // selector to show on UI
  SelectorCTA selector = 2;
}

message DateVal {
  // date param selected by the user
  google.protobuf.Timestamp date = 1;
  // constraints set for that date selector
  DateConstraints constraints = 2;
  // icon urls to be set for date - calendar, edit(pencil) icon, etc.
  IconUrls selector_icon_urls = 3;
  // eg: valid from, valid till etc
  DateType date_type = 4;
}

enum DateType {
  UNSPECIFIED = 0;
  VALID_FROM = 1;
  VALID_TILL = 2;
}

message DateConstraints {
  // min allowed date to be selected
  google.protobuf.Timestamp min_allowed_date = 1;
  // max allowed date to be selected
  google.protobuf.Timestamp max_allowed_date = 2;
  // min allowed duration between start and end time
  google.protobuf.Duration min_duration = 3;
}

message SelectorCTA {
  // add amount/payee/date
  Text selector_text = 1;
  // enter amount to auto-pay / auto-invest
  Text display_text = 2;
  // description text from rule description
  Text description_text = 3;
  // deeplink the selector should redirect to
  deeplink.Deeplink deeplink = 4;
  // initial icon('+', '>', etc) urls to show on the selector
  IconUrls icon_urls = 5;
}

message IconUrls {
  // e.g: calendar icon, + icon image url to be sent for each cta
  string left_icon = 1;
  // e.g: pencil icon
  string right_icon = 2;
}

message RecipientSelector {
  // payee info of the Recipient
  RecipientInfo pay_info = 1;
  // selector to show on UI
  SelectorCTA selector = 2;
}

message FrequencyVal {
  // eg: Weekly, Half-Yearly, Fortnightly (Every 15 days) etc
  string display_text = 1;
  // defines the type of frequency
  Frequency frequency = 2;
}

enum Frequency {
  FREQUENCY_UNSPECIFIED = 0;
  FREQUENCY_WEEKLY = 1;
  FREQUENCY_FORTNIGHTLY = 2;
  FREQUENCY_MONTHLY = 3;
  FREQUENCY_QUARTERLY = 4;
  FREQUENCY_HALF_YEARLY = 5;
  FREQUENCY_YEARLY = 6;
}

message RecipientInfo {
  // when there is no SI created, this struct will be nil
  // if SI is created, fields in this struct will be filled as per requirements
  recurringpayment.RecurringPayment recurring_payment_info = 1;
  // payee name to whom payment is to be made
  Text recipient_name = 2;
  // masked account id of the payee
  Text masked_acc_id = 3;
  // bank name of the payee
  Text bank_name = 4;
  // icon urls to show on the selector after we have certain payee info vals
  IconUrls selector_icon_urls = 5;
  // bg color for the payee name icon
  string payee_bg_color = 6;
  // account number of the recipient
  string acc_id = 7;
  // ifsc code for the account for the recipient
  string ifsc_code = 8;
}

message AppValue {
  string app_name = 1;
  string app_type = 2;
  string app_logo = 4;
  oneof platform_specific_information {
    AndroidAppPackageName package = 5;
  }
}

message Duration {
  google.protobuf.Duration value = 1;
  DurationDisplayUnit unit = 2;
}

// AggregationTile provides a dynamic aggregation data for FiTTT rules.
// it takes care of overall rule aggregations (eg: 12 active rules) or
// rule-category aggregations (eg: 200 rs saved via Auto-save rules). More types of aggregation to come in.
message AggregationTile {
  string title = 1;
  string description = 2;
  FitttAction action = 3;
  // background color for the tile
  string bg_color = 4;
  string font_color = 5;
  string id = 6;
}

// AggregationTile provides a dynamic aggregation data for FiTTT rules.
// it takes care of overall rule aggregations (eg: 12 active rules) or
// rule-category aggregations (eg: 200 rs saved via Auto-save rules). More types of aggregation to come in.
message AggregationTileV2 {
  string id = 1;
  Text title = 2;
  Text description = 3;
  CTA cta = 4;
  CardDisplayInfo card_display_info = 5;
}

// ExploreRulesTile is the last constituent of FiTTT digest on home screen.
message ExploreRulesTile {
  string title = 1;
  string sub_title = 2;
  // background color for the tile
  string bg_color = 3;
  string font_color = 4;
  string action_bg_color = 5;
  string id = 6;
  frontend.deeplink.Deeplink deeplink = 7;
}

message CategorisedRuleSubscription {
  CategoryHeader header = 1;
  repeated RuleSubscriptionTile subscription_tile = 3;
}

message CategoryHeader {
  RuleCategory category = 1;
  // a summary can be different for each category. For example:
  // 1. Auto-save: "Rs 3450 saved this week"
  // 2. Auto-pay: "04 upcoming this week"
  // 3. Reminders: "02 reminders this week"
  string summary = 2;
  string category_name = 3;
  SubscriptionTileDisplayType display_type = 4;
}

// RuleSubscriptionTile denotes an individual tile in the MyRules section of the app.
// There is one tile for each subscription of the rule.
message RuleSubscriptionTile {
  // unique identifier of Subscription
  string id = 1;
  // name of the rule
  string rule_name = 2 [deprecated = true];
  // the total amount saved with this rule subscription till now
  api.typesv2.Money amount_saved = 3;
  // a displayable summary string for individual subscription. This is specific to rule. For example:
  // 1. AAP BANOGE CROREPATI": "In Rainy day fund"
  // 2. DIVIDE & CONQUER: "Sent to Anand"
  // 3. SPEND RESPONSIBLY: "If spends > 25k"
  string summary = 4;
  // defines state of the rule subscription (Active, Inactive, Paused)
  RuleSubscriptionState state = 6;
  // background color for the tile
  string bg_color = 7;
  // rule card will contain title and substring in my rules revamp page (https://www.figma.com/file/RPKbVj8CLFrpwKJcjB8JwP/FIT-FFF-24-Feb-2021?node-id=6797%3A181)
  RuleName name = 8;
  // pause/resume actions
  FitttAction action = 9;
  // rule subscription state string, this is to represent the subscription state as here: https://www.figma.com/file/RPKbVj8CLFrpwKJcjB8JwP/FIT-FFF-24-Feb-2021?node-id=6797%3A188
  string state_string = 10;
  //If rule limit reached, populate the object else send it as null or dont send
  RuleLimitInfo ruleLimitInfo = 11;
}

// rule card will contain title and substring in my rules revamp page (https://www.figma.com/file/RPKbVj8CLFrpwKJcjB8JwP/FIT-FFF-24-Feb-2021?node-id=6797%3A181)
message RuleName {
  string title = 1;
  string unique_param = 2;
}

message ExecutionHistory {
  // the type of the individual entry in the history
  HistoryEntryType type = 1;
  string icon_url = 2;
  // eg: "Added to Rainy day fund"
  string title = 3;
  // eg: "Fi Account"
  string sub_title = 4;
  // timestamp when this subscription was last executed
  google.protobuf.Timestamp executed_at = 5;
  // the amount saved if this entry was of ADD_FUND type
  // the amount paid if this entry was of PAY_TO_BENEFICIARY type
  api.typesv2.Money txn_amount = 6;
  // unique identifier on client
  string id = 7;
  // link to redirect to another page with relevant details
  // eg: MF details page
  deeplink.Deeplink link = 8;
}

message ExecutionInsight {
  // Eg: "You have 60% this month with Fi"
  string title = 1;
  // Eg: "Tell your friends how you have changed the way you save"
  string share_display = 2;
  string image_url = 3;
}

message ShareCardData {
  Text title = 1;
  Text share_display = 2;
  string img_url = 3;
}

message CricketTeamValue {
  string team_id = 1;
  // Name of team (Unique)
  string team_name = 2;
  // Team logo
  string team_logo = 3;
  // abbreviated_name will be shortened name of the team which can be used for displaying on UI
  // eg: CSK for Chennai Super Kings
  string abbreviated_name = 4;
}

message CricketPlayerValue {
  string player_id = 1;
  // Legal name of the player. Player name will be used for rule evaluation
  string player_name = 2;
  // Player profile pic
  string player_pic = 3;
  // Current playing team (This will help in GROUP BY team)
  CricketTeamValue team = 4;
  // Type of the player
  CricketPlayerType player_type = 5;
  // Display name for the player will be used for display purpose only
  // this can be name of the player with which he is popular among fans
  // eg: Lokesh Rahul is legal name of KL Rahul's
  // if display name is empty, Client should use player_name for display
  string display_name = 6;
}

message FootballPlayerValue {
  string id = 1;
  string name = 2;
  // Player profile pic
  string player_pic = 3;
  // Current playing team (This will help in GROUP BY team)
  FootballTeamValue team = 4;
  // Type of the player
  FootballPlayerType player_type = 5;
}

message FootballTeamValue {
  string id = 1;
  string name = 2;
  string logo_url = 3;
  string abbreviated_name = 4;
}

//Identifies the rule limit reached
message RuleLimitInfo {
  //eg:Limit reached for this rule
  string title = 1;
  //eg:You have turned this rule on for 3 batsman, which is the limit. Choose a batsman you’d like to switch out for Ambati Rayudu.
  string description = 2;
  //Type of Value
  RuleParamType ruleParamType = 3;
  // List of already subscribed values
  // since UI will also require subscription Id of the subscription which should be PAUSED before subscribing a new one,
  // we introduce a new field for same `ExistingSubscriptionValues`
  // DEPRECATED: DO NOT USE
  repeated Value possible_values = 4 [deprecated = true];
  // List of already subscribed values with their subscriptionId
  repeated ExistingSubscriptionValues existing_values = 5;
}


message ExistingSubscriptionValues {
  // subscription_id will be used in SubscribeRuleRequest, in that case subscription with subscription_id will be paused first
  string subscription_id = 1;
  // unique value is what defines a subscription
  Value unique_value = 2;
}

message MyRulesFooter {
  // title
  string footer_title = 1;
  // string data in footer or substring
  string footer_string = 2;
}

/**
Can we used where ever we want to show a UI component with
Title, Description, Icon along with click action (As of now supporting single click button type)
 */
message MessageBox {
  //Field identify the ui component type
  MessageBoxType message_box_type = 1;
  //Ui component title eg: Your rule has been turned off
  string title = 2;
  // Ui component description eg: ‘Cap your Spends’ is nudging you to slow down your spending! We recommend viewing your spend breakdown in your account summary
  string description = 3;
  //Logo url
  string logo_url = 4;
  //primary action cta name eg: Let start
  string primary_cta_name = 5;
  //Primary action deeplink
  frontend.deeplink.Deeplink primary_cta_deeplink = 6;
}

/**
Can be used where ever we want to show UI component like top or bottom banners
 */
message AlertBar {
  //Field identify the ui component type
  MessageBoxType message_box_type = 1;
  //Bg color of the component
  string bar_bg_color = 2;
  //Text on the component
  string bar_description = 3;
}

// Used to render the UI tag content on rule card
message Tag {
  // Tag color
  string color = 1;
  // Description text
  string description = 2 [deprecated = true];
  // logo url list eg: For test cricket two team logos. if empty hide the tag
  repeated string logos = 3;
  // tag state
  TagState state = 4;
  // identifier for tag on UI
  string id = 5;
  // if primary image is not present, then logos should be used
  string primary_img = 6;
  Text desc = 7;
  // border color of the tag
  string border_color = 8;
}

message ConditionData {
  MetricType metric_type = 1;
  oneof condition {
    AppUsageConditionData app_usage_condition = 2;
  }
}

// Condition for App usage rules will be evaluated at Client
// client would use AppUsageCondition to perform following condition -> `configured_app == app_name && configured_time > usage_time`
// AppConditionData provides all the data and condition for client to evaluate
message AppUsageConditionData {
  // unique identifier
  // app for which usage condition needs to be evaluated
  // this is the type configured by user
  string configured_app = 1;
  // left operand
  // configured usage time by user while subscription
  // hours in microseconds (eg: 7 hours -> 25200000)
  Duration configured_duration = 2;
  // start_time and end_time should be used for querying the data on client
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
  // platform specific information for AppName
  oneof platform_specific_information {
    AndroidAppPackageName package = 5;
  }
}

message AndroidAppPackageName {
  string package_name = 1;
}

message ConditionResult {
  // map <execution_id, condition_bool_result>
  map<string, bool> result = 1 [deprecated = true];
  // key to eval_result is execution Id and
  map<string, ConditionEvalResult> cond_eval_result = 2;
}

message ConditionEvalResult {
  // returns true or false
  bool result = 1;
  // evaluation_metadata will be mainly used for debugging purpose
  // contains map of executionId to string, string can be plain text or json string
  // json may contain information about the expression used for evaluation
  // if the evaluation resulted to false, json may contain information about any permission issue, screen lock etc
  string metadata = 2;
}

message HomeCard {
  // uuid
  string id = 1;
  // constructed description text for card
  Text description = 3;
  // maintains all display specific information for card
  CardDisplayInfo display_info = 4;
  // provides data which the redirected screen would use to invoke API and load the page
  frontend.deeplink.Deeplink redirection_link = 5;
  // home card tags are simple texts displayed in form of chips on card
  repeated Tag tags = 7;
  // provides title for the home card
  Text title = 8;
  string id_for_event_logging = 9;
}

message Text {
  string text = 1;
  TextDisplayInfo display_info = 2;
}

message TextDisplayInfo {
  string font_color = 1;
  string bg_color = 2;
}

message CardDisplayInfo {
  string img_url = 1;
  string bg_color = 2;
}

message Collection {
  string id = 1;
  // Name of the card
  // eg: Football fever, Digital wellness etc
  Text name = 2;
  // tag to be displayed on top of card
  // etc: New Rules added today
  Tag tag = 3;
  // contains card specific display info
  CardDisplayInfo card_display_info = 4;
  // rules count string
  Text rules_count_string = 5;
  // description text will be used on collection page
  Text description = 6;
  // state of the collection
  CollectionState state = 7;
  // category of rules present inside collection
  CollectionType type = 8;
}

message StringArr {
  repeated string list = 1;
}

// lists all the subscriptions for a rule
message SubscriptionsForRule {
  Text title = 1;
  Text sub_title = 2;
  Text amount_saved = 3;
  string rule_id = 4;
  repeated Subscription subscriptions = 5;
  CardDisplayInfo display_info = 6;
  // view more option on card
  CTA view_more = 7;
}

// CompositeCollection will be used for pagination on explore page
// In case there are no collections for collection_type (AutoSave, AutoPay etc) empty should be populated
// at the end of all the collections, more_rules should be populated
message CompositeCollection {
  Text title = 6;
  oneof card {
    EmptyPage empty = 1;
    fittt.Collection collection = 2 [deprecated = true];
    MoreRules more_rules = 3;
    FeaturedRules featured_rules = 4;
    Collections collections = 5;
  }
}

message EmptyPage {
  string img_url = 1;
  Text title = 2;
  Text sub_title = 3;
  repeated Tag tags = 4;
}

message MoreRules {
  string id = 1;
  CardDisplayInfo display_info = 2;
  Text text = 3;
}

message Collections {
  repeated Collection all_collections = 1;
}

message FeaturedRules {
  // featured rules banners for each collection category
  repeated BannerCard featured_rules_banners = 2;
}

// CompositeRule will be used for pagination on Collection page
// server should populate more_collections in the end of the list if there are no further collections present
message CompositeRule {
  oneof card {
    fittt.FitttRule rule = 1;
    MoreCollection more_collections = 2;
    EmptyPage empty = 3;
  }
}

message MoreCollection {
  string id = 1;
  CardDisplayInfo display_info = 2;
  Text title = 3;
  repeated fittt.Collection collection = 4;
}

message FiSavingsLeagueHomeCard {
  // uuid
  string id = 1;
  // provides title for the home card
  Text title = 2;
  // constructed description text for home card
  Text description = 3;
  // maintains all display specific information for card
  CardDisplayInfo display_info = 4;
  // provides data which the redirected screen would use to invoke API and load the page
  frontend.deeplink.Deeplink redirection_link = 5;
  // list of images to be shown on home card
  // eg: for a particular match, show team icons for both the teams
  repeated string img_urls = 6;
  // to identify type of savings league home card
  // alternate solution could have been to keep it as different type altogether
  // using an enum instead as android client is more comfortable in differentiating through an enum than another object
  FiSavingsLeagueHomeCardType savings_league_home_card_type = 7;
}

enum FiSavingsLeagueHomeCardType {
  FI_SAVINGS_LEAGUE_HOME_CARD_UNSPECIFIED = 0;
  // type 1 assumes there will be two images at each side of the card
  // eg: A wicket image on the left and a trophy image on the right
  TYPE_1 = 1;
  // type 2 assumes there can be multiple images on the card
  // eg: For a particular match we can show team1 vs team2
  // https://www.figma.com/file/NwKAZrxP77XbbZH6TR0Z8Q/FIT-%2F-Workfile?node-id=9201%3A159369
  TYPE_2 = 2;
  // type 3 assumes there can be multiple images on the card with larger dimensions
  // https://www.figma.com/file/NwKAZrxP77XbbZH6TR0Z8Q/FIT-%2F-Workfile?node-id=9201%3A160205
  TYPE_3 = 3;
}

message FitCard {
  oneof card {
    // use AggregationTileV2 with deeplink support
    AggregationTile agg_tile = 1 [deprecated = true];
    // home card shows summary of new launches, informs users about new happenings at FIT or rules, presents summary of savings
    // eg:
    // 1. 4 wellness rules live today!
    // 2. 16 EPL matches today
    // 3. You saved 5000 rs this month using FIT
    HomeCard home_card = 2;
    Collection collection = 3;
    ExploreRulesTile explore_rules = 4;
    AggregationTileV2 agg_tile_v2 = 5;
    FiSavingsLeagueHomeCard fsl_home_card = 6;
  }
}

// provides a quick summary for a subscription
// user may choose to pause/edit subscription from using this card
message Subscription {
  // subscription-id
  string id = 1;
  Text title = 2;
  string img_url = 3;
  repeated Tag tags = 4;
  repeated FitttAction actions = 5;
  //If rule limit reached, populate the object else send it as null or dont send
  RuleLimitInfo rule_limit_info = 6;
  RuleSubscriptionState state = 7;
  string paused_text_font_color = 8;
  string rule_id = 9;
  RuleCategory rule_category = 10;
  RuleParamValues param_values = 11;
  RuleTypeForSpecialHandling rule_type = 12;
}

// MyRulesCompositeCard will be used for pagination on my rules page
// In case there are no subscriptions for user for rule_type (AutoSave, AutoPay etc) empty should be populated
message MyRulesCompositeCard {
  oneof card {
    AggregatedSubscriptionsSummary agg_summary = 1;
    EmptyPage empty = 2;
    CTA archived_rules = 3;
  }
}

// AggregatedSubscriptionsSummary displays aggregated summary of subscribed rules
// aggregation can be based on RULE or TAG, this property is defined with rules table
message AggregatedSubscriptionsSummary {
  // uuid
  string id = 1;
  // constructed title text for card
  Text title = 2;
  // constructed sub-title text for card
  Text sub_title = 3;
  // maintains all display specific information for card
  CardDisplayInfo display_info = 4;
  Text amount_saved = 5;
  // tags are simple texts displayed in form of chips on card
  repeated Tag tags = 6;
  // list of quick links to subscription page
  repeated SubscriptionLink quick_links = 7;
  // my rules summary is aggregated based on either tagId (eg: IPL 2021, WC 2021, FIFA 2021 etc) or on rule (Zen Mode, HWW etc)
  // aggregation Id needs to be passed in subsequent API for listing subscriptions
  // for deeplink.Screen.FIT_SUBSCRIPTIONS_FOR_RULE_PAGE client should invoke GetSubscriptionsForRule with relevant rule_id
  // for deeplink.Screen.FIT_SUBSCRIPTIONS_FOR_TAG_PAGE client should invoke GetRecentSubscriptionsForTag with relevant tag_id
  deeplink.Deeplink deeplink = 8;
  // Text to be displayed before amount
  // eg: PAID, SAVED, INVESTED etc
  Text amt_prefix_text = 9;
}

// links are displayed on agg summary card
// clicking on the quick link takes the user to subscriptions page
// subscription_id will be used to load subscriptions page
message SubscriptionLink {
  string id = 1;
  RuleSubscriptionState state = 2;
  // deeplink to subscription page
  frontend.deeplink.Deeplink deeplink = 3;
  CardDisplayInfo display_info = 4;
  // name of unique param for subscription
  // eg: Amazon, Flipkart for Shopping rule
  // Virat Kohli, Chennai Super Kings for cricket rules
  string unique_name = 5;
}

// CTA - click to action
message CTA {
  // text to be displayed on CTA
  Text text = 1;
  // deeplink for the CTA
  deeplink.Deeplink action = 2;
  // uuid
  string id = 3;
  // action can have an image like '>' for better visualization
  string img_url = 4;
  // bg color for the CTA if it a button on UI
  string bg_color = 5;
  // sub title text on CTA
  api.typesv2.common.Text sub_title = 6;
}

message IntroCard {
  // url of the corresponding image of filter
  string img_url = 1;
  // title of the card with appropriate colors
  // e.g: "save" will be orange colour, all else
  // white color when save type is displayed
  Text title = 2;
  Text description = 3;
  Text footer = 4;
  // list of text which would be having a different format than desc and footer
  // it can be shown as bullet points, with different line spacing
  repeated Text terms = 5;
  CardDisplayInfo display_info = 6;
  Tag tag = 7;
}

message CarousalCard {
  // tag can be "coming soon", "trending", etc
  Tag tag = 1;
  // the actual string to be displayed on cards
  Text text = 2;
  // type can be "save", "pay", "invest"
  // not using enum here as string is required as key
  // in the IntroPage message
  string type = 3;
}

message IntroPageData {
  // mapping of type and corresponding (img, title) combination
  map<string, IntroCard> filter_intro_card_map = 1;
  // array of all the carousal cards
  repeated CarousalCard carousal_card = 2;
  // common subtitle string for all intro pages
  Text subtitle = 3;
}

// Defines state of the subscription
enum RuleSubscriptionState {
  SUBSCRIPTION_STATE_UNDEFINED = 0;
  ACTIVE_SUBSCRIPTION = 1;
  // [DEPRECATED]
  // use PAUSED_SUBSCRIPTION instead of INACTIVE
  INACTIVE_SUBSCRIPTION = 2 [deprecated = true];
  PAUSED_SUBSCRIPTION = 3;
  CLOSED_SUBSCRIPTION = 4;
}

// FitttRuleActionType defines the possible actions that can result from the processing of the rule. The `action`
// needs to be executed as the final step in the rule processing.
//
// Note: this is different from `FitttAction` which defines the UI actions a user can perform on the Fittt related screens.
enum FitttRuleActionType {
  RULE_ACTION_UNSPECIFIED = 0;
  SAVE_TO_SD = 1;
}

// SubscriptionPreRequisiteActionType will contain the pre-requisite actions to be performed
enum SubscriptionPreRequisiteActionType {
  SUBSCRIPTION_PRE_REQUISITE_UNSPECIFIED = 0;
  CREATE_SD_ACCOUNT = 1;
  // In case the existing SD is preclosed or inactive and a valid SD is available,
  // CHOOSE_SD_ACCOUNT will be used in update subscription page.
  CHOOSE_SD_ACCOUNT = 2;
  // For AUTO_INVEST rules, if the user is not onboarded into FIT investments,
  // COMPLETE_INVESTMENTS_ONBOARDING will denote that the user has to follow few steps to complete onboarding
  // and proceed to rule subscription.
  COMPLETE_INVESTMENT_ONBOARDING = 3;
  // show FYI message box that existing subscriptions will be paused
  SHOW_FYI_MESSAGE_BOX_FOR_PAUSING_EXISTING_SUBS = 4;
  // show how rule works message for certain rules
  SHOW_HOW_RULE_WORKS = 5;
  // show the dailog where SI amount is to be selected by the user
  SHOW_SI_AMOUNT_DIALOG = 6;

  // Exit from current screen and redirect user to perform a pre-requisite action before subscribing
  EXIT_AND_REDIRECT_ACTION = 7;
}

// values required to render CreateSDAccount bottom sheet
message CreateSDAccountValue {
  // title in the bottom sheet. eg) Open a Smart Deposit.
  api.typesv2.common.Text title = 1;
  // main image in the bottom sheet
  api.typesv2.common.Image icon = 2;
  // description in the bottom sheet.
  // eg) Before you can try FIT auto save rules, you need to open a smart deposit (gives up to 5.1% interest) so you can save money into it. Let’s do that first.
  api.typesv2.common.Text description = 3;
  // Cta with deeplink to redirect to create SD
  deeplink.Cta cta = 4;
}

// Actions like opening a bank account are necessary before subscribing to any
// Auto-Save, Auto-Invest or Auto-Pay FIT rule
message ExitAndRedirectAction {
  deeplink.Deeplink deeplink = 1;
}

// values required to the prerequsite actions
message SubscriptionPreRequisiteActionTypeValue {
  // value required to the prerequsite actions
  oneof ActionType {
    CreateSDAccountValue create_sd_account_value = 1;

    // Any mandatory exiting and redirection, if not already completed by user
    ExitAndRedirectAction exit_and_redirect_action = 2;
  }
}

// Defines state of a rule
enum RuleState {
  RULE_STATE_UNSPECIFIED = 0;
  // rule already subscribed by any user is active rule
  // rule which is subscribed by the user, is active for that particular user
  ACTIVE_RULE = 1;
  // Rule not already subscribed can be an Inactive rule
  INACTIVE_RULE = 2;
  // Rule which has all the subscriptions paused will be paused rule
  PAUSED_RULE = 3;
}

enum RuleParamEditState {
  RULE_PARAM_EDIT_STATE_UNSPECIFIED = 0;
  SELECT_FOR_EDIT = 1; // Param with this state should be allowed to edit first
  EDITABLE = 2; // can be edited
  NOT_EDITABLE = 3; // shouldn't allowed to edit. Ex., unique params are not allowed to edit till M40
  // the param will be merged with the rule description text and will not be clickable
  // example usage: "SD name would be not clickable in deposit inline auto-save flow"
  // e.g: "Vacay fund" in https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?node-id=10401-39305&t=Md6ebKFVVSqpHlY9-4
  NOT_EDITABLE_WITH_HIDDEN_OPTIONS = 4;
}

// RuleParamType will be used by UI to take valid inputs from user.
// eg: For Merchant, list of merchant will be displayed
// For DateOfMonth, only 1-31 will be valid input
// similarly for Double, Integer etc
enum RuleParamType {
  RULE_PARAM_TYPE_UNSPECIFIED = 0;
  INT_INPUT = 1; // integer input by the user
  DOUBLE_INPUT = 2; // double input by the user
  STRING_INPUT = 3;
  MERCHANT = 4; // list of merchant names supported (eg: Swiggy, Zomato, FoodPanda etc)
  DATE_OF_MONTH = 5; // valid values 1-31
  CELEB_NAME = 6; // string values (eg Amitabh bachhan, Virat Kohli etc)
  SMART_DEPOSIT = 7; // list of existing smart deposits
  MONEY = 8;
  CRICKET_TEAM = 9; // list of team
  CRICKET_PLAYER = 10; // list of cricket player
  FOOTBALL_PLAYER = 11;
  FOOTBALL_TEAM = 12;
  DURATION = 13; // eg: 1 hour, 10 minutes
  APP = 14; // mobile app type eg: Instagram, Whatsapp, etc
  MUTUAL_FUND = 15;
  MUTUAL_FUND_SELECTOR_CTA = 16;
  CUSTOM_AMOUNT_SELECTOR_CTA = 17;
  // generic type for capturing date input
  // e.g. Date will be a particular calendar date i.e 5th of August
  DATE = 18;
  // defines type for payment info of the recipient
  PAYMENT_RECIPIENT = 19;
  // type defining frequency of execution of rule
  FREQUENCY = 20;
  // custom amount selector
  CUSTOM_AMOUNT_SELECTOR = 21;
  // day of the week, e.g: Monday, Tuesday, etc
  DAY_OF_WEEK = 22;

  // Identifier of a stock / ETF listed in US stock exchanges
  US_STOCK = 23;
}

// FitttActionType defines all kinds of actions available to the user w.r.t FiTTT.
enum FitttActionType {
  FITTT_ACTION_UNSPECIFIED = 0;
  // all the available rules defined by the Fittt service
  ALL_RULES = 1;
  // rules subscribed by a particular actor
  MY_RULES = 2;
  // PAUSE the active fittt rule subscription
  PAUSE_SUBSCRIPTION = 3;
  // UNPAUSE the paused fittt rule subscription
  UNPAUSE_SUBSCRIPTION = 4;
  // Edit the fittt rule subscription
  EDIT_RULE = 5;
  // CLOSE the active fittt rule subscription
  CLOSE_SUBSCRIPTION = 6;
  // links to mutual funds screen
  VIEW_MUTUAL_FUND = 7;

  VIEW_US_STOCK = 8;
}

enum DurationDisplayUnit {
  UNIT_UNSPECIFIED = 0;
  HOUR = 1;
  MINUTE = 2;
  SECOND = 3;
  MILLISECOND = 4;
}

// Defines the nature of the Rule. i.e the behaviour upon condition satisfaction
enum RuleCategory {
  RULE_CATEGORY_UNDEFINED = 0;
  AUTO_SAVE = 1;
  AUTO_PAY = 2;
  REMINDER = 3;
  AUTO_INVEST = 4;
  US_STOCKS_SIP = 5;
}

enum SubscriptionTileDisplayType {
  TILE_DISPLAY_TYPE_UNSPECIFIED = 0;
  // only display a few(say first 2) tile
  COLLAPSED = 1;
  // display all tiles togther
  EXPANDED = 2;
}

// denotes the type of entry in the history for a subscription
enum HistoryEntryType {
  HISTORY_ENTRY_TYPE_UNSPECIFIED = 0;
  // add funds
  ADD_FUND = 1;
  // an entry in history corresponding to pausing the subscription
  PAUSE = 2;
  // an entry in history corresponding to un-pausing(resuming) the subscription
  UNPAUSE = 3;
  // an entry in history corresponding to the create subscription event. This is always the
  // first entry in the history for every subscription
  SUBSCRIPTION_CREATED = 4;
  // an entry in history corresponding to the update/edit subscription event.
  SUBSCRIPTION_UPDATED = 5;
  // auto-pay payments to beneficiary
  PAY_TO_BENEFICIARY = 6;
  // remind
  REMINDER_SENT = 7;
  // purchase mutual funds
  PURCHASE_MUTUAL_FUND = 8;
  // aggregated purchase mutual fund
  AGGREGATE_PURCHASE_MUTUAL_FUND = 9;

  EXECUTE_US_STOCKS_SIP = 10;
}

enum CricketPlayerType {
  PLAYER_TYPE_UNSPECIFIED = 0;
  BATSMAN = 1;
  BOWLER = 2;
  WICKET_KEEPER = 3;
  ALL_ROUNDER = 4;
}

// Defines the position of player
// this can be used for more specific rules
// eg: When X player protects Y goals, save
// in this case only Goal Keepers needs to be listed
enum FootballPlayerType {
  FOOTBALL_PLAYER_TYPE_UNSPECIFIED = 0;
  GOAL_KEEPER = 1;
  DEFENDER = 2;
  MIDFIELDER = 3;
  FORWARD = 4;
}

enum MessageBoxType {
  MESSAGE_BOX_TYPE_UNSPECIFIED = 0;
  SD_PRE_CLOSURE = 1;
  TEMPORARILY_UNAVAILABLE = 2;
  SI_REVOKED_OR_INVALID = 3;
  ACTOR_INELIGIBLE_FOR_MF_PURCHASE_ORDER = 4;
}

enum TagState {
  TAG_STATE_UNSPECIFIED = 0;
  TAG_ACTIVE = 1;
  TAG_INACTIVE = 2;
  TAG_COMING_SOON = 3;
}

enum EventType {
  EVENT_TAG_UNSPECIFIED = 0;
  // Payment made to a merchant like Swiggy, Zomato, Uber, Netflix etc or
  // Payment made to another actor in epiFi or external actor outside epiFi.
  PAYMENT = 1;
  // A tweet by celebrities like Amitabh Bachchan, Elon Musk and others.
  CELEB_TWEET = 2;
  // A boundary hit in a cricket match by a top class batsman like Virat Kohli.
  CRICKET_BOUNDARY_HIT = 3;
  // contains info about batting stats for all the batsmen in the match. This includes batsman from both the teams.
  CRICKET_BATTING_STATS = 4;
  // contains info about bowling stats for all the bowlers in the match. This includes bowlers from both the teams.
  CRICKET_BOWLING_STATS = 5;
  // contains info about match stats like winning team.
  CRICKET_MATCH_RESULT = 6;
  // contains info about the maiden overs bowled in a match(from both the teams) like the team & bowler details.
  CRICKET_MAIDEN_OVERS = 7;
  // contains info about batting stats for a single batsman
  CRICKET_BATSMAN_STATS = 8;
  // contains info about bowling stats for a single bowler
  CRICKET_BOWLER_STATS = 9;
  // contains info about maiden over for a single bowler
  CRICKET_MAIDEN_OVER_STATS = 10;
  // contains info about partnership stats for both teams
  CRICKET_PARTNERSHIP_STATS = 11;
  // contains info about partnership stats for a single team
  CRICKET_TEAM_PARTNERSHIP_STATS = 12;
  // contains info about goals scored by all players from both teams in a match
  FOOTBALL_GOAL_STATS = 13;
  // contains info about goals scored by a single player in a match
  FOOTBALL_PLAYER_GOAL_STATS = 14;
  // contains info about football match stats like winning team or draw.
  FOOTBALL_MATCH_RESULT = 15;
  // contains info about usage of a particular app
  // This event will be published by FIT periodically everyday
  // condition execution of subscription will happen on client
  // execution data will be provided as a response of SyncRequest API
  APP_USAGE_RULE_INITIATE_EXECUTION_EVENT = 16;
  // Olympic event
  OLYMPIC_INDIA_MEDAL_EVENT = 17;
  // DAILY event is trigger by dkron everyday
  DAILY_EVENT = 19;
  // event received for investment made by a judge in a company
  // this event will be for an episode, where a judge decides to invest in one/multiple companies
  SHARK_TANK_INDIVIDUAL_JUDGE_EVENT = 20;
  // event received for investment made by all judges in a company
  // this event will be for an episode where all judges decide to invest in a one/multiple companies
  SHARK_TANK_ALL_JUDGE_EVENT = 21;
}

enum MetricType {
  METRIC_TYPE_UNSPECIFIED = 0;
  APP_USAGE = 1;
  SCREEN_UNLOCK = 2;
  ALARM_SNOOZE = 3;
  PHONE_USAGE = 4;
}

enum CollectionType {
  COLLECTION_TYPE_UNSPECIFIED = 0;
  // save the amount to deposit account
  COLLECTION_TYPE_AUTO_SAVE = 1;
  // pay to another actor
  COLLECTION_TYPE_AUTO_PAY = 2;
  // invest the amount to mutual funds
  COLLECTION_TYPE_AUTO_INVEST = 3;
  // hybrid collection with rules for both mutual funds and smart deposits
  COLLECTION_TYPE_HYBRID = 4;

  COLLECTION_TYPE_US_STOCKS_SIP = 5;
}

enum CollectionState {
  COLLECTION_STATE_UNSPECIFIED = 0;
  COLLECTION_ACTIVE = 1;
  COLLECTION_INACTIVE = 2;
}

message MutualFundSelectorCTAValue {
  // Param Value to denote a CTA which opens a slider with Mutual Fund selection screen in investments
  string text = 1;
  string icon_url = 2;
  // TODO (sakthi) Check with client team if this deeplink is required for slider
  frontend.deeplink.Deeplink deeplink = 3;
  // if mf_values are present, client should not request investment screen to list all the mutual funds
  // only mf_values in the list are shown for subscription
  repeated MutualFundValue mf_values = 4;
  // selector icons to be set after mf value has been selected
  IconUrls icon_urls = 5;
  // suggestions_bottom_sheet is used to open a bottom sheet with suggested MF list
  // [nullable] if null, client should not open the bottom sheet
  MFSuggestionsBottomSheet suggestions_bottom_sheet = 6;
}

message MFSuggestionsBottomSheet {
  // eg: Select a fund to invest
  api.typesv2.common.Text title = 1;
  // component to show a list of tags with a header
  // eg: tags can be Equity, Index, Manufacturing etc and title INVESTMENT STYLE
  TextWithTags fund_criteria = 2;
  // list of suggested mutual funds
  repeated MutualFundValue mf_values = 3;
  // CTA to redirect user to all MF listing page
  deeplink.Cta select_funds = 4;
  // eg: The Sharks' investment styles inspired the above fund selections & are not their recommendations.
  api.typesv2.common.Text footer = 5;
  // bottom sheet bg color
  string bg_color = 6;
}

message TextWithTags {
  api.typesv2.common.Text title = 1;
  repeated Tag tags = 2;
  CardDisplayInfo card_display_info = 3;
}

message CustomAmountCTAValue {
  // text to be shown in possible values section
  string text_for_possible_values = 1;
  // optional - initial value to be set. If not set, this can be assumed to be Zero
  // Initial value will be greater than or equal to minimum_val
  api.typesv2.Money initial_val = 2;
  // constraint that needs to be applied on the custom amount entered by the user.
  api.typesv2.CustomAmountConstraint constraint = 3;
  // text to be shown in rule description
  string text_for_rule_desc = 4;
  // optional - will be populated for select cases if the CTA is used for MutualFund rule and the selected mutual fund already known.
  investment.mutualfund.BuyConstraints mutual_fund_buy_constraint = 5;
  // icons to be set after amount value has been entered
  IconUrls icon_urls = 6;
}

message SubscriptionInfo {
  // description of subscribed rule (When Rohit Sharma hits a 6 save Rs 50 in Travel savings )
  Text description = 1;
  // statistical information shown with rule description
  repeated Label stats = 2;
  // relevant actions based on current subscription state
  // already paused subscription will see resume option and vice versa
  repeated FitttAction actions = 3;
  // bg color of card, based on bg color of rule
  string bg_color = 4;
  // to be used for adding color to state displayed on Subscription info
  string state_text_color = 6;
  // current subscription state
  RuleSubscriptionState state = 7;
  //If rule limit reached, populate the object else send it as null or dont send
  RuleLimitInfo ruleLimitInfo = 8;
  // Optional - Will be populated only when the next execution for the rule is predictable ex: Recurring save & invest rules.
  // Text includes date at which next execution is expected to happen
  Text next_execution_info = 9;
}

// Provides summary of subscription history
// only limited actions will be displayed
// if more actions than listed are present, then CTA will be point to SubscriptionHistory page
message SubscriptionHistorySummary {
  CTA view_more = 1;
  repeated ExecutionHistory exec_history = 2;
}

message Label {
  string title = 1;
  string bg_color = 2;
  oneof sub_title {
    string str = 3;
    api.typesv2.Money money = 4;
  }
}

// parent of FitttProfile and RewardsProfile
// using oneof to represent any of the two above
message Profile {
  oneof all_profiles {
    FitttProfile fittt_profile = 1;
  }
}

// FiTTT specific user profile data
message FitttProfile {
  // has completed the fittt intro
  bool has_seen_fit_intro_cards = 1;
  // time at which the actor has last accessed fittt
  google.protobuf.Timestamp last_accessed_at = 2;
}

message BannerCard {
  // uuid
  string id = 1;
  // title for the card, if any
  Text title = 2;
  // maintains all display specific information for card
  CardDisplayInfo card_display_info = 3;
  // provides cta which the redirected screen would use to invoke API and load the page
  CTA card_cta = 6;
  // there can be requirement to show stats/fact above title,
  // header can be used for passing such values to client
  Text header = 7;
  // provides short explanation about the card
  Text sub_title = 8;
  // provides detailed description about the card
  Text description = 9;
  // know more CTA should be used in sub_title of the card.
  // at the end of the sub_title, `know more` text should be appended with below CTA
  CTA know_more_cta = 10;
  // tags to be shown on banner
  repeated Tag tags = 11;
  // unique string identifier for the banner, to be used for analytics purposes
  string analytics_id = 13;
}

message ClaimPrizePageData {
  CardDisplayInfo display_info = 1;
  Text title = 2;
  Text sub_title = 3;
  // CTA to my rewards page
  CTA claim_prize_cta = 4;
}

message IntroPage {
  oneof intro_page {
    // provides pages to introduce FIT as a feature to users
    IntroPageData fittt_intro = 1;
    // provides pages to introduces sports challenge in FIT to users
    SportsChallengeIntroPageData challenge_intro = 2;
  }
}

message SportsChallengeIntroPageData {
  // eg: FIT x IPL saveathon
  string title_img_url = 1;
  // 2k users are already playing!
  Text sub_title = 2;
  // cards are scrollable list
  repeated IntroCard cards = 3;
  CardDisplayInfo display_info = 4;
  // CTA to challenge home page
  CTA cta = 5;
}

// coming soon text can be replaced with `please update to latest version`
// when newer apks are added with leaderboard support
message ComingSoon {
  string img_url = 1;
  Text text = 2;
}

// defines card for matches to be shown on challenge home page
message ZeroStateMatchCard {
  // eg: WEEK 1 • MATCH 3 • starts AT 08:30 PM
  Text header = 1;
  // eg: Pick your team to participate in today’s challenge
  Text title = 2;
  SportsTeam team1 = 3;
  SportsTeam team2 = 4;
  CTA deeplink = 5;
}

message PostMatchCard {
  // eg: CSK VS MI •  MATCH ENDED on 10 MAR 8:34PM
  Text header = 1;
  repeated Tag post_match_stats = 2;
}

message SelectionMadeCard {
  // eg: WEEK 1 • MATCH 3 • starts AT 08:30 PM
  Text header = 1;
  // eg: Pick your team to participate in today’s challenge
  Text title = 2;
  // list of unique params subscribed by the user for the particular match
  repeated Value unique_param_values = 3;
  // edit allowed will be returned false, after the match has started,
  // this will restrict user from clicking to this card and editing or adding subscriptions
  bool edit_allowed = 4;
  CTA cta = 5;
}

message LockedCard {
  // eg: WEEK 1 • MATCH 3 • starts AT 08:30 PM
  Text header = 1;
  // eg: Pick your team to participate in today’s challenge
  Text title = 2;
  SportsTeam team1 = 3;
  SportsTeam team2 = 4;
  // if `unlock_text` is not empty, user should not be allowed to make subscriptions for the match
  // and this text should be shown with a lock icon
  Text unlock_text = 5;
}

message SportsTeam {
  string id = 1;
  string img_url = 2;
}

message SportsChallengeRuleCard {
  string id = 1;
  // use this text as title if there are any existing subscriptions
  Text existing_sub_title = 2;
  // use this text as title if there are no existing subscriptions
  Text no_sub_title = 3;
  // use this text as desc if there are no existing subscriptions
  Text no_sub_desc = 4;
  CardDisplayInfo display_info = 5;
  // list of all possible param values to be selected
  PossibleParams possible_params = 6;
}

// pair of unique param to mapped sd account
message UniqueParamToSDMap {
  // eg: player, team etc
  Value unique_param = 1;
  // Sd account configured for the subscription
  Value sd_param = 2;
  string id = 3;
}

message PossibleParams {
  string unique_param_name = 1;
  // list of all the possible unique values which user can choose from by clicking on edit icon
  repeated Value possible_unique_params = 2;

  string sd_param_name = 3;
  // list of all the possible sd values which user can choose from by clicking on edit icon
  repeated Value possible_sd_values = 4;

  string money_param_name = 5;
  repeated Value money_values = 6;

  // defines max number of selections allowed for unique param
  int32 max_unique_param_selection_allowed = 7;

  // eg: Pick upto 3 bowlers
  string selection_limit_msg = 8;

  // For existing subscriptions for a rule
  // `existing_subscription_unique_param_to_sd_map` is the list of pair of unique param and associated sd
  // client can use this field to show mapping of subscription to SD on UI
  repeated UniqueParamToSDMap existing_subscription_unique_param_to_sd_map = 9;
}

enum IntroPageType {
  INTRO_PAGE_TYPE_UNSPECIFIED = 0;
  FIT_INTRO = 1;
  SPORTS_CHALLENGE_INTRO = 2;
}

message SubscriptionsSuccessCard {
  // rule name
  // eg: Super Sixer
  Text title = 1;
  // eg: When 3 batsmen hits a 6, put aside ₹6 into Rainy Day Fund
  Text desc = 2;
  CardDisplayInfo display_info = 3;
  // data to prepare share card
  ShareCardData share = 4;
  // list of subscription related data which will be used for analytics logging
  repeated Subscription subs = 5;
  string id = 6;
}

message MultipleSubscriptionsForRuleData {
  // name of unique param
  // eg: configuredCricketPlayer, configuredCricketTeam etc
  string unique_param_name = 1;
  // list of all the unique params (player/team) for the requested rule & match
  repeated Value selected_unique_values = 2;
  // name of deposit param
  // eg: depositAccountId
  string sd_param_name = 3;
  // only one deposit account can be selected for a rule
  SdParamValue selected_sd_value = 4;
  // name of money param
  // configuredDepositAmount, depositAmount etc
  string money_param_name = 5;
  // only one deposit amount can be chosen for a rule
  api.typesv2.Money selected_money_val = 6;
}

message RuleSubscriptionDataList {
  // multiple rule subscriptions at once
  repeated RuleSubscriptionData rule_subscriptions_list = 1;
}

message RuleSubscriptionData {
  // name of unique param
  // eg: configuredCricketPlayer, configuredCricketTeam etc
  string unique_param_name = 1;
  // unique param (player/team) for the requested rule & match
  Value selected_unique_value = 2;
  // name of deposit param
  // eg: depositAccountId
  string sd_param_name = 3;
  // deposit account selected for a rule
  SdParamValue selected_sd_value = 4;
  // name of money param
  // configuredDepositAmount, depositAmount etc
  string money_param_name = 5;
  // deposit amount chosen for a rule
  api.typesv2.Money selected_money_val = 6;
}

message SportsChallengeHomePageHeader {
  // list of cards for showing rewards or statistics related banners
  repeated BannerCard banners = 1;
  CardDisplayInfo display_info = 2;
}

message MatchCard {
  oneof card {
    // Match has not started and subscriptions are allowed and user has not made any subscription
    ZeroStateMatchCard zero_state_match = 1;
    // Match has been completed and stats are shown
    PostMatchCard post_match = 2;
    // Match has not started and subscriptions are allowed and user has subscribed to some rules
    SelectionMadeCard selection_made = 3;
    // Match has not started and subscriptions are locked
    LockedCard locked_card = 4;
  }
}

// specifies the state of CTA action
// eg: on subscriptions page, swipe to update/activate CTA
enum SubmitCTAState {
  SUBMIT_CTA_STATE_UNSPECIFIED = 0;
  // user should be allowed to perform the action associated
  SUBMIT_CTA_ENABLED = 1;
  // user should not be allowed to perform the action
  SUBMIT_CTA_DISABLED = 2;
}
