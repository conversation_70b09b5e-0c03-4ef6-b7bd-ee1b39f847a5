syntax = "proto3";

package frontend.fittt.rule;

import "api/typesv2/money.proto";

option go_package = "github.com/epifi/gamma/api/frontend/fittt/rule";
option java_package = "com.github.epifi.gamma.api.frontend.fittt.rule";

message RuleParamValues {
  // considering above example, {"confMerchantName":"Swiggy", "minAmount":500, "percentageVal":5, "sdName":"Test"} are the rule_param_values
  map<string, Value> rule_param_values = 1;
}

// Value for the user provided inputs on rule subscription
message Value {
  oneof value {
    SdParamValue sd_value = 4;
    api.typesv2.Money money_val = 5;
    // day of the week
    DayOfWeekVal day_of_week_val = 22;
    // date of a month
    // valid entries can be from 1 to 28
    DateOfMonthVal date_of_month_val = 23;

    // Value of stock for US stocks SIP specific rules and subscriptions
    USStockValue us_stock_value = 1;
  }
  // Id to identify value uniquely on client
  string id = 7;
  // required for multiple subscription support, applicable for unique params only
  // if the subscription exists with this param, is_selected flag is true
  bool is_selected = 17;
  // tells the behaviour of the param if its editable, not editable or not editable with no options
  RuleParamEditState edit_state = 24;
}

enum RuleParamEditState {
  RULE_PARAM_EDIT_STATE_UNSPECIFIED = 0;
  SELECT_FOR_EDIT = 1; // Param with this state should be allowed to edit first
  EDITABLE = 2; // can be edited
  NOT_EDITABLE = 3; // shouldn't allowed to edit. Ex., unique params are not allowed to edit till M40
  // the param will be merged with the rule description text and will not be clickable
  // example usage: "SD name would be not clickable in deposit inline auto-save flow"
  // e.g: "Vacay fund" in https://www.figma.com/file/tfzrfGYi5yJlJUUIhmptcn/%F0%9F%9A%80-Save-%E2%80%A2-FFF-%E2%80%A2-v1.1?node-id=10401-39305&t=Md6ebKFVVSqpHlY9-4
  NOT_EDITABLE_WITH_HIDDEN_OPTIONS = 4;
}

message SdParamValue {
  // name of the SD
  string name = 1;
  // account identifier
  string account_id = 2;
  // masked account number
  string masked_account_number = 3;
}

message DayOfWeekVal {
  // eg: Monday, Tuesday etc - parsed from google/type/dayofweek.proto and converted to Titlecase
  string weekday = 1;
  // eg: Mon, Tue, Thu etc
  string abbr_name = 2;
  // bool to check if the day is enabled to select by user
  bool enabled = 3;
}

message DateOfMonthVal {
  // eg: 1, 2, 3 etc
  int32 date = 1;
  // eg: 1st, 2nd, 3rd etc
  string display_string = 2;
  // bool to check if the date is enabled to select by user
  bool enabled = 3;
}

message USStockValue {
  // Identifier of a stock / ETF listed in US stock exchanges
  string stock_id = 1;
}
