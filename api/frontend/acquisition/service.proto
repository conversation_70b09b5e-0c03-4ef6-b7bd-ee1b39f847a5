syntax = "proto3";

package frontend.acquisition;

import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/frontend/acquisition";
option java_package = "com.github.epifi.gamma.api.frontend.acquisition";

service Acquisition {
  // IdentifyIntentAndSource tries best effort ways to figure out the AdIntent and Source of the user basis the attribution details.
  // AdIntent: Intent of the user coming on Fi app. This will depend upon the type of Ad shown to the user.
  // Source: Whether user came from an ad seen via Google Ads, Affiliates, FB, etc. The source could be Organic as well
  // in case user came on its own.
  // Since this RPC can get called right during the first app launch for a new user, auth and device integrity checks
  // are not required / can't be performed.
  rpc IdentifyIntentAndSource (IdentifyIntentAndSourceRequest) returns (IdentifyIntentAndSourceResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }
}

message IdentifyIntentAndSourceRequest {
  // fields from the onConversionDataSuccess callback of Appsflyer
  google.protobuf.Struct appsflyer_on_conversion_data_success_payload = 1;
  // fields from the install-referrer.
  // applicable only for Android.
  google.protobuf.Struct install_referrer_payload = 2;

  frontend.header.RequestHeader req = 15;
}

message IdentifyIntentAndSourceResponse {
  // AdIntent/Intent of the user coming to Fi.
  // string name of user.AcquisitionChannel
  // using string so that addition of new enum entries doesn't yield unknown fields at client's end.
  // If intent can't be identified, it shall be considered as BANKING.
  string intent = 1;
  // Acquisition source of the user coming to Fi.
  // string name of user.AcquisitionIntent
  // using string so that addition of new enum entries doesn't yield unknown fields at client's end.
  string source = 2;

  frontend.header.ResponseHeader resp_header = 15;
}


