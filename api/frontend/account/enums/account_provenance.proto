// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package frontend.account.enums;

option go_package = "github.com/epifi/gamma/api/frontend/account/enums";
option java_package = "com.github.epifi.gamma.api.frontend.account.enums";

// AccountProvenance specifies whether the account is a tpap,or internal or connected account
enum AccountProvenance {
  ACCOUNT_PROVENANCE_UNSPECIFIED = 0;
  ACCOUNT_PROVENANCE_INTERNAL = 1;
  ACCOUNT_PROVENANCE_CONNECTED_ACCOUNT = 2;
  ACCOUNT_PROVENANCE_TPAP = 3;
  ACCOUNT_PROVENANCE_DEPOSIT = 4;
  ACCOUNT_PROVENANCE_CREDIT_CARD_ACCOUNT = 5;
}
