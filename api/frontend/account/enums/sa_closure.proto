syntax = "proto3";

package frontend.account.enums;

option go_package = "github.com/epifi/gamma/api/frontend/account/enums";
option java_package = "com.github.epifi.gamma.api.frontend.account.enums";

enum SAClosureRequestEntryPoint {
  SA_CLOSURE_REQUEST_ENTRY_POINT_UNSPECIFIED = 0;
  // entry into the SA closure flow via chatbot
  SA_CLOSURE_REQUEST_ENTRY_POINT_CHATBOT = 1;
  // entry into the SA closure flow via FAQs
  SA_CLOSURE_REQUEST_ENTRY_POINT_FAQ = 2;
  // entry into the SA closure flow from profile section
  SA_CLOSURE_REQUEST_ENTRY_POINT_PROFILE = 3;
}

enum SaClosureCriteriaGroup {
  SA_CLOSURE_CRITERIA_GROUP_UNSPECIFIED = 0;
  SA_CLOSURE_CRITERIA_GROUP_INVESTMENTS = 1;
  SA_CLOSURE_CRITERIA_GROUP_LOANS = 2;
  SA_CLOSURE_CRITERIA_GROUP_CREDIT_CARDS = 3;
  SA_CLOSURE_CRITERIA_GROUP_AUTOMATED_PAYMENTS = 4;
  SA_CLOSURE_CRITERIA_GROUP_FI_COINS = 5;
  SA_CLOSURE_CRITERIA_GROUP_SA_BALANCE = 6;
  SA_CLOSURE_CRITERIA_GROUP_UPI_LITE = 7;
}

// TODO: Update gamma/frontend/account/saclosure/service.go : saClosureCriteriaItemToClosureReasonStringMap whenever adding new enum values
enum SaClosureCriteriaItem {
  SA_CLOSURE_CRITERIA_ITEM_UNSPECIFIED = 0;
  SA_CLOSURE_CRITERIA_ITEM_JUMP = 1;
  SA_CLOSURE_CRITERIA_ITEM_SMART_DEPOSIT = 2;
  SA_CLOSURE_CRITERIA_ITEM_FIXED_DEPOSIT = 3;
  SA_CLOSURE_CRITERIA_ITEM_MUTUAL_FUND = 4;
  SA_CLOSURE_CRITERIA_ITEM_US_STOCK = 5;
  SA_CLOSURE_CRITERIA_ITEM_INDIAN_STOCK = 6;

  SA_CLOSURE_CRITERIA_ITEM_VEHICLE_LOAN = 7;
  SA_CLOSURE_CRITERIA_ITEM_PERSONAL_LOAN = 8;
  SA_CLOSURE_CRITERIA_ITEM_EDUCATION_LOAN = 9;
  SA_CLOSURE_CRITERIA_ITEM_HOME_LOAN = 10;
  SA_CLOSURE_CRITERIA_ITEM_OTHER_LOAN = 11;

  SA_CLOSURE_CRITERIA_ITEM_AMPLIFI_CREDIT_CARD = 12;
  SA_CLOSURE_CRITERIA_ITEM_SIMPLIFI_CREDIT_CARD = 13;

  SA_CLOSURE_CRITERIA_ITEM_AUTO_PAY = 14;
  SA_CLOSURE_CRITERIA_ITEM_AUTO_PAY_SIP = 15;

  SA_CLOSURE_CRITERIA_ITEM_FI_COIN_BALANCE = 16;

  SA_CLOSURE_CRITERIA_ITEM_SAVINGS_ACCOUNT_BALANCE = 17;

  SA_CLOSURE_CRITERIA_ITEM_MAGNIFI_CREDIT_CARD = 18;
  SA_CLOSURE_CRITERIA_ITEM_AUTO_SAVE = 19;

  SA_CLOSURE_CRITERIA_ITEM_UPI_LITE = 20;
}
