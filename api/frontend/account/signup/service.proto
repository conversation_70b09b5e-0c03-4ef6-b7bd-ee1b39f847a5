// protolint:disable MAX_LINE_LENGTH

//    Proto & API definitions for User Signup front end service
//       * User Sign up(& Login) with phonenumber
//            * Generate OTP
//               * Resend OTP
//            * Verify OTP
//       * Link OAuth account
//       * Logout

syntax = "proto3";

package frontend.account.signup;

import "api/frontend/account/signup/enums.proto";
import "api/frontend/account/signup/oauth_provider.proto";
import "api/frontend/card/card.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/auth.proto";
import "api/frontend/header/metadata.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/date.proto";
import "api/typesv2/common/date.proto";
import "api/typesv2/form/consent.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/liveness.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/nulltypes.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/polling_request_info.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/user.proto";
import "api/typesv2/common/visual_element.proto";
import "google/protobuf/struct.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/postal_address.proto";
import "validate/validate.proto";
import "api/frontend/account/country.proto";
import "api/typesv2/otp.proto";


option go_package = "github.com/epifi/gamma/api/frontend/account/signup";
option java_package = "com.github.epifi.gamma.api.frontend.account.signup";


// User's Signup service to signup using a phone number and OAuth
// Consists of methods to help verify a phone number using OTP and add a OAuth account
// And helper methods to validate/invalidate user's login
service Signup {

  // Generates 6-digit OTP & delivers to phone number specified in the request via SMS
  // Resends the existing OTP if the request is made with same Token.
  //  - Generated OTP is to be valid for a short amount of time - Ex: 5 mins
  //  - If request is made prior to predefined time(Ex: 30 seconds), request the user/client to wait before second request.
  //  - To maintain exponential timer to prevent brute force attacks
  rpc GenerateOtp (GenerateOtpRequest) returns (GenerateOtpResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.add_device_verification_nonce) = true;
    option (rpc.skip_device_integrity_check) = true;
  };

  // Verify the OTP against a GenerateOTPRequest instance & login the user on a successful verification
  // On first successful verification, user's account is to be created and user is to be redirected to complete OAuth verification
  // On three invalid attempts to verify Otp, user has to be forced to generate a new Otp
  // VerifyOtp will return a Refresh token
  // Refresh token is to be used in AddOAuthAccount request to generate Access token
  rpc VerifyOtp (VerifyOtpRequest) returns (VerifyOtpResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.add_device_verification_nonce) = true;
    option (rpc.skip_device_integrity_check) = true;
  };


  // Method to add a Oauth Account to a user
  // On successful verification of the oauth token, user is linked to the oauth account
  // If the email account is already registered, token is to be updated
  // Access token is generated and returned as part of the Response.
  // Further requests between client and server has to make use of access token to authenticate themselves
  rpc AddOAuthAccount (AddOAuthRequest) returns (AddOAuthResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.device_integrity_check) = CONDITIONAL;
    option (rpc.skip_device_integrity_check) = true;
  };

  // LoginWithOAuth is called by client on email verification page and splash screen
  // This API verifies user credentials via their respective tokens and decides the flow for the user
  // Currently, it handles these flows:
  // 1. Blocks user if access is revoked
  // 2. Starts or resumes onboarding based on current state of user
  // 3. Does an auth factor update for the user
  // It also generates an access token based on the authorization level
  rpc LoginWithOAuth (LoginWithOAuthRequest) returns (LoginWithOAuthResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.device_integrity_check) = CONDITIONAL;
    option (rpc.skip_device_integrity_check) = true;
  };

  // FetchAccessToken RPC is called by client to get a new access token
  // Client calls this RPC in the below scenarios:
  // 1. When user is home reachable, client calls this RPC in async to refresh access token
  // 2. When user is not home reachable, client calls this RPC to get nextAction
  rpc FetchAccessToken (FetchAccessTokenRequest) returns (FetchAccessTokenResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.device_integrity_check) = NOT_REQUIRED;
    option (rpc.skip_device_integrity_check) = true;
  };

  // Method to get the next onboarding action of user
  // The method chooses among the consent action, kyc action, liveness action, based on what the
  // user has completed
  rpc GetNextOnboardingAction (GetNextOnboardingActionRequest) returns (GetNextOnboardingActionResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_afu) = true;
  };

  // Method to logout the user by invalidating a user's login
  rpc SignOut (SignOutRequest) returns (SignOutResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_afu) = true;
  }

  // TODO(aditya): Move to frontend/kyc service
  // Submit a cKYC request to the KYC service. Post successful registration, the caller
  // of this rpc should use the GetCKYCRecord(GetCKYCRecordRequest) method to retrieve the data.
  // Caller needs to handle any error status returned in the InitiateCKYCResponse.kyc_status
  rpc InitiateCKYC (InitiateCKYCRequest) returns (InitiateCKYCResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };


  // VerifyCKYCOTP can be used to verify otp or resend otp
  rpc VerifyCKYCOTP (VerifyCKYCOTPRequest) returns (VerifyCKYCOTPResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // Validates the provided PAN number using third party services.
  // There might be lot of cases for which validation can fail e.g.
  // PAN number is incorrect due to a typo, is marked as fake, marked as deactivated due to death, etc.
  // If the validation fails then the user will need to submit a correct PAN number to proceed
  // with KYC.
  rpc ValidatePAN (ValidatePANRequest) returns (ValidatePANResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Before starting EKYC process using SDK, client should call this method to
  // inform server. This method validates if EKYC should be done and
  // stores the EKYC state on backend. Validation is required because anyone can
  // land on EKYC page using a deeplink.
  // RPC Status OK in response represents a go ahead for EKYC.
  rpc InitiateEKYC (InitiateEKYCRequest) returns (InitiateEKYCResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_afu) = true;
  };


  // UploadEKYCRecord uploads user KYC data after success of EKYC. This is encrypted
  // and is expected to be the same payload received from vendor by the client.
  // Failure returned in case of failure to decrypt the request body or
  // the body doesn't match the expected schema. This data is stored in temporary storage
  // by KYC Service before being used in account creation.
  rpc UploadEKYCRecord (UploadEKYCRecordRequest) returns (UploadEKYCRecordResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_afu) = true;
  }

  // CheckLiveness sends video of the actor to do a liveness and/or face match for.
  // If the face match from KYC is enabled, then the service will wait for KYC service to transfer
  // the KYC ID image via another API call with the same actor to do the match.
  // If face match from old video is enabled, the face will be matched against old captured
  // video by Epifi.
  rpc CheckLiveness (stream CheckLivenessRequest) returns (CheckLivenessResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_afu) = true;
  }

  // ConfirmAuthFactorUpdate is called by the client when the user selects to update either of
  // the auth factors (i.e. Device, Email, Phone number). This is the final confirmation after
  // the user has passed all the checks. Once the users confirm to update the details,
  // they're logged out of any other existing devices and values updated.
  rpc ConfirmAuthFactorUpdate (ConfirmAuthFactorUpdateRequest) returns (ConfirmAuthFactorUpdateResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_afu) = true;
  };

  // RPC to get the content of the outgoing SMS that's used for device and phone number registration
  // with the partner bank.
  rpc GetDeviceRegistrationSms (GetDeviceRegistrationSmsRequest) returns (GetDeviceRegistrationSmsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_afu) = true;
  };

  // RPC to register the device with the partner bank after the outgoing SMS was sent. This method
  // as an optimization, might just fetch the latest status from our systems instead of polling
  // from the bank each time.
  rpc RegisterDevice (RegisterDeviceRequest) returns (RegisterDeviceResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_afu) = true;
  };

  // Method to fetch KYC Record of the customer. It's a single abstraction over
  // both CKYC & EKYC. Contains customer's personal data this is used for account creation.
  rpc GetKYCRecord (GetKYCRecordRequest) returns (GetKYCRecordResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Deprecated: No longer required. Client should stop calling it. No replacement API needed.
  rpc AccountSetup (AccountSetupRequest) returns (AccountSetupResponse) {
    option deprecated = true;
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // Check onboarding status fetches the progress percentage
  rpc CheckAccountSetupStatus (CheckAccountSetupStatusRequest) returns (CheckAccountSetupStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Re-OOBE / Profile update is an async process. This API checks the
  // current status of the update request. Client is expected to poll this
  // API based on the status in the response.
  rpc CheckAuthFactorUpdateStatus (CheckAuthFactorUpdateStatusRequest) returns (CheckAuthFactorUpdateStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.allow_afu) = true;
  }

  // rpc to skip a particular stage in onboarding
  // for eg. in certain cases user has the option to skip Add money during onboarding
  rpc SkipOnboardingStage (SkipOnboardingStageRequest) returns (SkipOnboardingStageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RecordIosDeviceAttestation is used to verify the attestation object
  // that was generated by Apple servers to attest the validity of the key.
  // It takes the attestation object to be verified, device info, key identifier
  // and nonce used to generate the attestation as input.
  // If the attestation passed as input is valid, then OK status is returned.
  // If the attestation is not valid, then INVALID_ATTESTATION status is returned.
  // INTERNAL status is returned if some unexpected error is encountered while
  // verifying the attestation like in case if DB is unavailable.
  rpc RecordIosDeviceAttestation (RecordIosDeviceAttestationRequest) returns (RecordIosDeviceAttestationResponse) {
    option (rpc.auth_required) = false;
    option (rpc.skip_device_integrity_check) = true;
  }

  // rpc to update user Name/DOB and validate it against Name/DOB found in kyc
  // This is called when a Name or DOB mismatch is found while uploading and validating EKYC record
  // Maximum number of attempts to retry entering the mismatched details is 3, after which user will be shown a permanent failure screen
  // UpdateUserDetailsRequest should contain either Name or DOB in the absence of which ErrInvalidArgument will be returned
  rpc UpdateUserDetails (UpdateUserDetailsRequest) returns (UpdateUserDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // Save PAN and DOB in user details for a customer attempting to onboard.
  // This is the first time we take these inputs from a user. Subsequently, users might see this screen again if
  // there's a name/dob mismatch.
  // The PAN and DOB details given by the user are validated before proceeding to next steps.
  // Users are allowed to proceed only if validation succeeds.
  rpc SavePanAndDob (SavePanAndDobRequest) returns (SavePanAndDobResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to send notification w.r.t vkyc
  // notification reason is expected to be sent for BE to understand the type of notification is to be taken
  // for example - for notification reason OUT_OF_BUSINESS_HOURS, notification will be send when vkyc is available inside business hours
  rpc SendVKYCNotification (SendVKYCNotificationRequest) returns (SendVKYCNotificationResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RPC to generate unique prospect ID. rpc will invoke vendor mapping to get a prospect ID such that there is no
  // duplicate prospect ID generation.
  rpc GenerateProspectId (GenerateProspectIdRequest) returns (GenerateProspectIdResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.add_device_verification_nonce) = true;
    option (rpc.skip_device_integrity_check) = true;
  }

  // CheckAccountClosureEligibility RPC checks if a user is eligible to close their Fi
  rpc CheckAccountClosureEligibility (CheckAccountClosureEligibilityRequest) returns (CheckAccountClosureEligibilityResponse) {
    option (rpc.auth_required) = true;
  }

  // ConfirmAccountClosure RPC handles account closure request from user.
  // revokes app access and tells user CX will reach out for further procedures
  rpc ConfirmAccountClosure (ConfirmAccountClosureRequest) returns (ConfirmAccountClosureResponse) {
    option (rpc.auth_required) = true;
  }

  // StoreDeviceSensorData RPC takes accelerometer, gyroscope and proximity data in the request
  // and persists it in the liveness_attempts table
  rpc StoreDeviceSensorData (StoreDeviceSensorDataRequest) returns (StoreDeviceSensorDataResponse) {
    option deprecated = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.auth_required) = true;
  }

  rpc SyncUserDeviceProperties (SyncUserDevicePropertiesRequest) returns (SyncUserDevicePropertiesResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // ResetOnboardingStage rpc handles resetting an onboarding stage for a user from the client and returns the next action to be taken by client
  // This only supports certain stages as listed in the implementation
  rpc ResetOnboardingStage (ResetOnboardingStageRequest) returns (ResetOnboardingStageResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // DeleteUser rpc is used to soft delete the user's current onboarding journey after storing the user's consent to do so
  // The user will then be able to restart their journey afresh
  rpc DeleteUser (DeleteUserRequest) returns (DeleteUserResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // PushEKYCEvent rpc is used to push EKYC events to BE, this will be used to monitor EKYC success/failures
  rpc PushEKYCEvent (PushEKYCEventRequest) returns (PushEKYCEventResponse) {
    option (rpc.auth_required) = false;
  };

  // UpdateFiLiteAccessibility rpc is used to update the status of Fi lite accessibility of the user
  // If this rpc is called then user will be redirected to home and will always land on home if user reopens the app
  rpc UpdateFiLiteAccessibility (UpdateFiLiteAccessibilityRequest) returns (UpdateFiLiteAccessibilityResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GenerateBKYCOtp rpc validates if user eligible for biometric kyc
  // if not eligible it will return detailed error view
  // if eligible will generate otp and return deeplink for polling screen
  rpc GenerateBKYCOtp (GenerateBKYCOtpRequest) returns (GenerateBKYCOtpResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  };

  // GetBKYCStatus checks if any of bkyc attempt is in review or success status
  // if present it returns next action expecting client to move ahead
  // else return polling status, expecting client to call again
  rpc GetBKYCStatus (GetBKYCStatusRequest) returns (GetBKYCStatusResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // ConfirmBKYCDetails records user consent stating kyc details are correct
  // Store consent given from user
  // mark bkyc attempt as success
  // If post onb upgrade user kyc level also
  // Client populate communication address also in case user want to update client address
  rpc ConfirmBKYCDetails (ConfirmBKYCDetailsRequest) returns (ConfirmBKYCDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // rpc to check the latest KYC status and return the relevant deeplink to client
  // This rpc will be used in the KYC status polling screen
  rpc GetKYCStatus (GetKYCStatusRequest) returns (GetKYCStatusResponse);

  // SetOnboardingIntent rpc will be used to set the onboarding journey for a user based
  // on the intent selected upon prompt, and corresponding next action is sent in the response
  rpc SetOnboardingIntent (SetOnboardingIntentRequest) returns (SetOnboardingIntentResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetOnboardingSoftIntentOptions rpc will be used to get the soft intent options for a user
  // Users can choose a variety of intents, then client will set choices using SetOnboardingSoftIntent
  rpc GetOnboardingSoftIntentOptions (GetOnboardingSoftIntentOptionsRequest) returns (GetOnboardingSoftIntentOptionsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // SetOnboardingSoftIntent rpc will be used to set the soft intents for a user
  // After storing the intent choices, corresponding next action is sent in the response
  rpc SetOnboardingSoftIntent (SetOnboardingSoftIntentRequest) returns (SetOnboardingSoftIntentResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetGenerateOtpInfo rpc will be used to get the country specific information required for signup.
  // Returns the list of countries where onb is enabled. For eg, UAE.
  // Info contains: isd code, flag, country name, min and max length of the phone number.
  rpc GetGenerateOtpInfo (GetGenerateOtpInfoRequest) returns (GetGenerateOtpInfoResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
    option (rpc.skip_device_integrity_check) = true;
    option (rpc.location_required) = LOCATION_CHECK_NOT_REQUIRED;
  }

  // Fetch profile from PAN number
  // Client can use this RPC to fetch the profile of a user and prefill the details
  rpc GetPanProfile (GetPanProfileRequest) returns (GetPanProfileResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message GetPanProfileRequest {
  frontend.header.RequestHeader req = 1;
  string pan = 2;
}

message GetPanProfileResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.common.Name pan_name = 2;
  api.typesv2.common.Date dob = 3;
}

message SyncUserDevicePropertiesRequest {
  frontend.header.RequestHeader req = 1;
  // list of device properties that need to be update
  repeated api.typesv2.DevicePropertyKeyValuePair pairs = 3;
}

message SyncUserDevicePropertiesResponse {
  frontend.header.ResponseHeader resp_header = 1;
}
// Represents the user's locale
enum Locale {
  // TODO(pruthvi): Migrate to ISO codes
  LOCALE_UNSPECIFIED = 0;
  ENGLISH = 1;
  HINDI = 2;
  TAMIL = 3;
}

message GenerateProspectIdRequest {
  // A set of authentication attributes
  frontend.header.RequestHeader req = 1;
}

message GenerateProspectIdResponse {
  // Status of the request
  frontend.header.ResponseHeader resp_header = 1;
  string prospect_id = 2;
}

// Represents user's basic profile
message UserProfile {
  // Unique identifier of user in Epifi system
  string actor_id = 1;
  // User's full name
  api.typesv2.common.Name name = 2;
  // User's gender
  api.typesv2.Gender gender = 3;
  // Age of the user
  uint32 age = 4;
  // Email id of the user
  string email = 5;
  // Language preference of the user
  Locale locale = 6;
}

// Represents a request to Generate OTP on a phone number
message GenerateOtpRequest {
  // Field Number 1 was being used by `device` field which is removed now. So, marking 1 as reserved.
  reserved 1;

  // A set of authentication attributes. Only device field is required to be present, auth token is not expected.
  frontend.header.AuthHeader auth = 4 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // Phone number that is to be verified
  api.typesv2.common.PhoneNumber phone_number = 2;

  // Unique identifier of GenerateOtp request
  // If token(+phone_number) is not sent, a new OTP is generated and sent to the user via SMS
  // If token(+phone_number) is sent, request is treated as ResendOtp and existing OTP is sent to the user phone via SMS
  string token = 3 [(validate.rules).string.pattern =
    "(^[a-fA-F0-9]{8}-[a-fA-F0-9]{4}-4[a-fA-F0-9]{3}-[8|9|aA|bB][a-fA-F0-9]{3}-[a-fA-F0-9]{12}$|^$)"];

  // AppsFlyer ID is generated by client is used by used propagate events to AppsFlyer.
  // TODO: (bug ID: 13179) This ID is temporary propagated through GenerateOtp RPC.
  // Eventually this ID will be propagated by a separate frontend RPC.
  string appsflyer_id = 5;

  // This is a Firebase specific property. It identifies a single installation of the app uniquely.
  // Note that `firebase_app_instance_id` (officially called `app_instance_id`) is different from `firebase_app_id`
  // Reference to the official documentation: https://developers.google.com/analytics/devguides/collection/protocol/ga4/reference?client_type=firebase#payload_post_body
  // TODO: (bug ID: 13179) This ID is temporary propagated through GenerateOtp RPC. TODO is for having a separate FE RPC for this.
  string firebase_app_instance_id = 6;
}

// Represents response of GenerateOtp method
message GenerateOtpResponse {
  // contains `device_integrity_nonce` required to generate safetynet attestaion.
  frontend.header.Metadata metadata = 4;

  // List of status codes returned
  enum Status {
    // Otp Sent
    OK = 0;
    // Expired token. Generate a new token
    OTP_TOKEN_EXPIRED = 100;
    // Input token is not active. Cannot reuse it
    OTP_INACTIVE = 101;
    // Resend failed; Reached the limit to resend OTP
    OTP_RESEND_LIMIT = 102;
    // Request too soon; Please retry after a while
    OTP_RESEND_INTERVAL = 103;
    // The user doesn't have access to the app. Possible due to user not in the waitlist or not given access by ranking.
    NO_APP_ACCESS = 106;
  }
  // Represents if the OTP has been generated and sent without any errors
  rpc.Status status = 1;

  // Unique identifier of GenerateOtp request
  string token = 2;

  // A timer(in seconds) for the client post which it should raise a new request for Otp
  // Any attempt prior to this timer will not be honored & will result in error
  uint32 retry_timer_seconds = 3;
  frontend.header.ResponseHeader resp_header = 15;
}


// Represents a request of VerifyOtp method
message VerifyOtpRequest {
  // Field Number 1 was being used by `device` field which is removed now. So, marking 1 as reserved.
  reserved 1;

  // A set of authentication attributes. Only device field is required to be present, auth token is not expected.
  frontend.header.AuthHeader auth = 5 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // Phone number that is being verified
  api.typesv2.common.PhoneNumber phone_number = 2;

  // Unique identifier of GenerateOtp request
  string token = 3 [(validate.rules).string.uuid = true];
  // 6-digit OTP that is shared to the phone number via SMS
  string otp = 4 [(validate.rules).string = {len: 6, pattern: "^[0-9]+$"}];
  // acquisition info for the user
  // this is used to decide the intent of the user
  // If client will not send this, BE will send default content (assuming saving account intent)
  AcquisitionInfo acquisition_info = 6;
}

// Represents a response of VerifyOtp method
message VerifyOtpResponse {
  // contains `device_integrity_nonce` required to generate safetynet attestaion.
  frontend.header.Metadata metadata = 3;
  // List of status codes returned
  enum Status {
    // OTP verified
    OK = 0;
    // Expired token. Generate a new token
    OTP_TOKEN_EXPIRED = 100;
    // Input token is not active. Cannot reuse it
    OTP_INACTIVE = 101;
    // Incorrect Otp
    OTP_INCORRECT = 102;
    // Incorrect Otp. Last attempt remaining
    // Account will be locked for 5 minutes after one more incorrect attempt
    OTP_INCORRECT_LAST_WARNING = 103;
    // Too many verification attempts on the token. Generate new token
    OTP_VERIFY_LIMIT = 104;
    // Account locked for 5 minutes
    ACCOUNT_LOCKED = 105;
  }
  // Status of the request
  rpc.Status status = 1;
  // Represents a User's refresh token.
  // This token will serve as authentication parameter in the
  // further requests until an access token is generated
  string refresh_token = 2;
  frontend.header.ResponseHeader resp_header = 15;
  // `is_existing_user` is true when there is actor present for at-least one of phone number and deviceId.
  // this flag will be false in case if its a new user or an existing user is trying to login with new phone number and device.
  bool is_existing_user = 4;
  // client will redirect to this next action after successful verification
  deeplink.Deeplink next_action = 5;
}

// Request data to invalidate a user's login - request of SignOut method
message SignOutRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

// Represents response of SignOut method
message SignOutResponse {
  enum Status {
    // Signout successful
    OK = 0;
  }
  // Returns if the SignOut request is processed without errors
  rpc.Status status = 1;
  frontend.header.ResponseHeader resp_header = 15;
}

message FetchAccessTokenRequest {
  frontend.header.RequestHeader req = 1;
  OAuthInfo oauth_info = 2;
  // device properties to be synced in our DB.
  LoginDeviceInfo login_device_info = 3;
  // source for which FetchAccessToken is triggered
  string source = 4;
}

message FetchAccessTokenResponse {
  // List of status codes returned
  enum Status {
    OK = 0;
    // Could not verify email; OAuth token is not valid
    INVALID_OAUTH_TOKEN = 100;

    // Client retries generating OIDC token with its respective provider
    // If provider requires user consent, user is navigated back to email selection screen
    // Any other unexpected errors in generating OIDC (oauth) token are handled by client
    EMAIL_SILENT_LOGIN = 101;

    // Client have to retry by prompting the user to grant phone permissions and
    // is_phone_permission_granted in the LoginWithOAuthRequest must be set to true
    PHONE_PERMISSION_REQUIRED = 109;
  }
  message UserInfo {
    string actor_id = 1;
    api.typesv2.common.BooleanEnum is_home_accessible = 2;
  }
  message DeviceIntegrityInfo {
    // device integrity nonce is returned when device integrity needs to be evaluated
    // It would be returned in following scenarios:
    // 1. device_integrity_token is absent in request
    // 2. device integrity status has expired or is invalid
    string device_integrity_nonce = 1;
  }
  frontend.header.ResponseHeader resp_header = 1;
  UserInfo user_info = 2;
  // Access token of the user that is initiating the request
  string access_token = 3;
  // Dynamic backend driven navigation from this screen.
  // Conveys next screen to go to.
  // When calling this RPC in async, next_action value can be a valid deeplink or null
  // If valid, respective screen must be shown. If null, client can use the existing stack or next action from the FE RPC it is calling
  deeplink.Deeplink next_action = 4;
  DeviceIntegrityInfo device_integrity_info = 5;
}

message LoginWithOAuthRequest {
  message UserInfo {
    // Name derived from user's gmail account
    api.typesv2.common.Name gmail_name = 1;
    // acquisition related info of the user, i.e. probable source of how user landed on the app
    AcquisitionInfo acquisition_info = 2;
  }
  message UserInput {
    // Pan number for acknowledgment and authentication in inactive account deletion flow
    string pan = 1;
    // User given name
    api.typesv2.common.Name given_name = 2;
    string finite_code = 3;
  }
  message ConsentInfo {
    // Used in opening new accounts for users whose savings account is closed. We ask for user's consent
    // before deleting old data
    api.typesv2.common.BooleanEnum delete_closed_account_consent = 1;
    // When there's a mismatch in phone while logging in, we ask the
    // user if she wants to update the phone. When the user confirms
    // update, the client makes the LoginWithOAuth call again with
    // this flag set to true.
    api.typesv2.common.BooleanEnum phone_update_consent = 2;
    // When there's a mismatch in email while logging in, we ask the
    // user if she wants to update the email. When the user confirms
    // update, the client makes the LoginWithOAuth call again with
    // this flag set to true.
    api.typesv2.common.BooleanEnum email_update_consent = 3;

    // List of consent ids along with its owner that user has given consent for
    // these consent will be store in consent DB post actor creation
    repeated api.typesv2.form.ConsentIdWithOwner consent_id_with_owners = 4;
  }
  frontend.header.RequestHeader req = 1;
  OAuthInfo oauth_info = 2;
  ConsentInfo consent_info = 3;
  // device properties to be synced in our DB.
  LoginDeviceInfo login_device_info = 4;
  UserInfo user_info = 5;
  UserInput user_input = 6;
  // source for which LoginWithOAuth is triggered
  string source = 7;
}

message LoginWithOAuthResponse {
  // List of status codes returned
  enum Status {
    OK = 0;
    // Could not verify email; OAuth token is not valid
    INVALID_OAUTH_TOKEN = 100;
    // Needs Auth factor update confirmation. User is
    // possibly trying to update the email or phone number.
    // This response code means that access token is not created,
    // user should confirm if they want to generate access token for AFU.
    AFU_FLAG_REQUIRED = 301;
    // The user doesn't have access to the app. Possible due to user not in the waitlist or not given access by ranking.
    NO_APP_ACCESS = 106;
    // user's phone number is not whitelisted to access app
    PHONE_NUMBER_NOT_WHITELISTED = 107;
    // Client retries generating OIDC token with its respective provider
    // If provider requires user consent, user is navigated back to email selection screen
    // Any other unexpected errors in generating OIDC (oauth) token are handled by client
    EMAIL_SILENT_LOGIN = 108;

    // Client have to retry by prompting the user to grant phone permissions and
    // is_phone_permission_granted in the LoginWithOAuthRequest must be set to true
    PHONE_PERMISSION_REQUIRED = 109;
  }
  message UserInfo {
    string actor_id = 1;
    api.typesv2.common.BooleanEnum is_home_accessible = 2;
  }
  message DeviceIntegrityInfo {
    // device integrity nonce is returned when device integrity needs to be evaluated
    // It would be returned in following scenarios:
    // 1. device_integrity_token is absent in request
    // 2. device integrity status has expired or is invalid
    string device_integrity_nonce = 1;
  }
  frontend.header.ResponseHeader resp_header = 1;
  UserInfo user_info = 2;
  // Access token of the user that is initiating the request
  string access_token = 3;
  // Dynamic backend driven navigation from this screen.
  // Conveys next screen to go to. If KYC not done, choose one
  // of the kyc methods, else go to account creation or home page.
  deeplink.Deeplink next_action = 4;
  DeviceIntegrityInfo device_integrity_info = 5;
}

message OAuthInfo {
  OAuthProvider oauth_provider = 1;
  // OAuth token generated by the OAuth provider. `id_token` in case of Google
  string oauth_token = 2;
}

// Request data to link OAuth Account to the user
message AddOAuthRequest {
  // A set of authentication attributes
  // Refresh token is to be used here
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // Oauth provider
  OAuthProvider oauth_provider = 3;
  // OAuth token generated by the OAuth provider. `id_token` in case of Google
  string oauth_token = 4;
  // Legal name submitted by the user
  api.typesv2.common.Name legal_name = 5 [deprecated = true];

  // Represents if user wants to update the phone number and the
  // source of phone number update flow.
  frontend.account.signup.PhoneNumberUpdateSource phone_number_update_source = 6;

  // When there's a mismatch in email while logging in, we ask the
  // user if she wants to update the email. When the user confirms
  // update, the client makes the AddOAuthAccount call again with
  // this flag set to true.
  bool is_email_update = 7;

  // unique identifier of the requesting app
  string app_id = 8;

  string finite_code = 9;

  // Name derived from user's gmail account
  api.typesv2.common.Name gmail_name = 10;

  // Deprecated in favour of DeviceMetadata -> default_device_language
  string default_device_language = 11 [deprecated = true];

  // device properties to be synced in our DB.
  DeviceMetadata device_metadata = 12;

  // attribution details of the user. This can include data from various methods/ways,
  // i.e. Appsflyer, Install Referrer (only Android).
  AcquisitionInfo acquisition_info = 13;
}

// Represents response data of a AddOAuth method
message AddOAuthResponse {
  // List of status codes returned
  enum Status {
    OK = 0;

    // Could not verify email; OAuth token is not valid
    INVALID_OAUTH_TOKEN = 100;

    // Needs Auth factor update confirmation. User is
    // possibly trying to update the email or phone number.
    // This response code means that access token is not created,
    // user should confirm if they want to generate access token for AFU.
    AFU_FLAG_REQUIRED = 301;
    // The user doesn't have access to the app. Possible due to user not in the waitlist or not given access by ranking.
    NO_APP_ACCESS = 106;
    // user's phone number is not whitelisted to access app
    PHONE_NUMBER_NOT_WHITELISTED = 107;
    // Client retries generating OIDC token with its respective provider
    // If provider requires user consent, user is navigated back to email selection screen
    // Any other unexpected errors in generating OIDC (oauth) token are handled by client
    EMAIL_SILENT_LOGIN = 108;
  }
  // Status of the request
  rpc.Status status = 1;
  // User's profile
  UserProfile user_profile = 2;
  // Access token of the user that is initiating the request
  string access_token = 4;
  // Dynamic backend driven navigation from this screen.
  // Conveys next screen to go to. If KYC not done, choose one
  // of the kyc methods, else go to account creation or home page.
  deeplink.Deeplink next_action = 5;
  // This flag denotes, if the client has completed all the authentication procedures
  // needed for the server to trust it and pass the a/c information of user. The
  // authentication procedures can include, Phone number verification, email verification,
  // device binding etc.
  // If true, client can make API calls that can have user's a/c related information
  // in the response. EG: [UserSession API]. If client makes such an API call, while
  // the user have not completed necessary authentication will lead failure with status
  // forbidden/permission denied. when the value is false, client is expected to handle the
  // [next_action] in the same response for refresh access token case.
  bool is_account_accessible = 6;

  // represents that the access token generated is for auth factor update / profile update / Re-OOBE flow.
  // It's needed by client for separate handling in the refresh call.
  bool is_profile_update = 7;

  // device integrity nonce is returned when device integrity needs to be evaluated
  // It would be returned in following scenarios:
  // 1. device_integrity_token is absent in request
  // 2. device integrity status has expired or is invalid
  string device_integrity_nonce = 8;

  frontend.header.ResponseHeader resp_header = 15;
}

// InitiateCKYCRequest is the request object for InitiateCKYC RPC
message InitiateCKYCRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // PAN number of the user
  string pan_number = 2 [deprecated = true];
  // DoB of the user
  google.type.Date dob = 3 [deprecated = true];
  // Phone number of user. This will be needed in case the user is a duplicate user in federal, but the phone number is mismatched
  api.typesv2.common.PhoneNumber phone_number = 4 [deprecated = true];
}

// InitiateCKYCResponse is the response object for InitiateCKYC RPC
message InitiateCKYCResponse {
  // Status of the request
  rpc.Status status = 1 [deprecated = true];

  // Dynamic backend driven navigation from this screen.
  // Conveys next screen to go to.
  deeplink.Deeplink next_action = 5;
  frontend.header.ResponseHeader resp_header = 15;
}

message VerifyCKYCOTPRequest {
  frontend.header.RequestHeader req = 1;
  api.typesv2.OTPRequestType otp_request_type = 2;
  // otp will be blank for resend otp
  string otp = 3;
}

message VerifyCKYCOTPResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}
// CKYCUserDetails is shown to the user to verify the details
message CKYCRecord {
  // CKYC Number is ID for the user's CKYC Record with CERSAI govt org.
  string ckyc_number = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Name fathers_name = 3;
  api.typesv2.PostalAddress permanent_address = 4;
}

// This is for pan validation from bank.
message ValidatePANRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // PAN number of the user
  string pan_number = 2 [(validate.rules).string = {len: 10, pattern: "^([A-Z]){3}P([A-Z])([0-9]){4}([A-Z])$"}];
}

message ValidatePANResponse {
  rpc.Status status = 1;
  bool is_valid = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

// InitiateEKYCRequest is the request object for InitiateEKYC RPC
message InitiateEKYCRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // denotes source which is trying to initiate ekyc
  // deprecated since added ekyc_source_str as its counterpart
  EkycSource ekyc_source = 2 [deprecated = true];
  // entry point indicates source for ekyc kyc/kyc/ekycSource
  string ekyc_source_str = 3;
}

enum EkycSource {
  EKYC_SOURCE_UNSPECIFIED = 0;
  EKYC_SOURCE_ONBOARDING = 1;
  EKYC_SOURCE_VKYC = 2;
}

// InitiateEKYCResponse is the response object for InitiateEKYC RPC
message InitiateEKYCResponse {
  // BackPressActionInfo controls the action on the back press in the EKYC SDK
  message BackPressActionInfo {
    // bottom sheet to confirm sdk exit from user.
    // this is being added to handle accidental back presses.
    bool show_bottom_sheet = 1;
    string title = 2;
    string subtitle = 3;
  }

  // Status of the request
  rpc.Status status = 1;

  // security token required in creating request
  // for EKYC from client to Vendor
  string security_token = 2;
  frontend.header.ResponseHeader resp_header = 15;
  // Empty string if ekyc is working, contains a message otherwise
  string downtime_message = 3;
  BackPressActionInfo back_press_action_info = 4;
  // consent for enter aadhaar screen in EKYC SDK
  string consent = 5;
  // subtitle for enter aadhaar screen in EKYC SDK
  string subtitle = 6;
}

// UploadEKYCRecordRequest is the request object for UploadEKYCRecord RPC
message UploadEKYCRecordRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // payload is the EKYC record received by the client from
  // vendor and forwarded as it is. contains personal user data.
  EKYCResponse payload = 2;

  // Error responses from federal for generate otp
  // deprecated in favour of generate_otp_responses
  repeated EKYCGenerateAadhaarOtpResponse error_generate_otp_error_responses = 3 [deprecated = true];

  // Error responses from federal for verify otp
  // deprecated in favour of verify_otp_responses
  repeated EKYCVerifyAadhaarOtpResponse error_verify_otp_error_responses = 4 [deprecated = true];

  // API responses from federal for generate otp request made from client
  repeated EKYCGenerateAadhaarOtpResponse generate_otp_responses = 5;

  // API responses from federal for verify otp request made from client
  repeated EKYCVerifyAadhaarOtpResponse verify_otp_responses = 6;

  // Last 4 digits of the mobile number on which the Aadhar OTP is sent.
  string masked_registered_phone_number = 7;

  // Source which is initiating EKYC. Client needs to populate this field using StartEKYCOptions.ekyc_source
  string ekyc_source = 8;
}

// UploadEKYCRecordResponse is the response object for UploadEKYCRecord RPC
message UploadEKYCRecordResponse {
  // status is success if decrypted payload matches the expected schema
  rpc.Status status = 1;
  // deprecated since added next action in StartEkycOption as its counter part
  deeplink.Deeplink next_action = 2 [deprecated = true];
  frontend.header.ResponseHeader resp_header = 15;
}

message EKYCRecord {
  // docs ref: https://uidai.gov.in/images/FrontPageUpdates/aadhaar_authentication_api_2_0.pdf

  // Postal Pincode
  string pincode = 1;

  // Post office name. Sometimes, addresses in India are identified
  // by the Post Office, especially in villages
  string postoffice = 2;

  // Gender of the customer
  string gender = 3;

  // Tehsil. Ref: https://dot.gov.in/sites/default/files/Re-verification%20instructions%2023.03.2017.pdf
  string locality = 4;

  // Village or Town or City name.
  string vtcname = 5;

  // Base64 encoded data
  string photo = 6;

  // Related person's name. e.g. value "S/O: Jolly Joseph"
  string careof = 7;

  // Registered mobile number of the customer
  string phone = 8;

  // Date of Birth in format "26-06-1989"
  string dob = 9;

  // Street Identifier
  string street = 10;

  // District / City
  string district = 11;

  // House identifer
  string houseno = 12;

  string state = 13;
  string landmark = 14;
  string email = 15;

  // Customer's Name
  string name = 17;
}

message EKYCResponse {
  // "Y" for success, "N" for failure
  string status = 1;
  string aua_specific_uid_token = 2;
  string auth_error_code = 3;
  string error_code = 4;

  // this is used as UID reference key by KYC
  string transaction_id = 5;
  EKYCRecord record = 6;
  frontend.header.ResponseHeader resp_header = 15;
}

message EKYCGenerateAadhaarOtpResponse {

  // Values are Y or N
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string status = 1 [json_name = "Status"];

  //Pass the UIDAI
  //transaction id got as the response of successful OTP generation.
  //All other case pass “NA”
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string uidai_txn = 2 [json_name = "UIDAITxn"];

  // Transaction id for this particular transaction in KUAserver
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string transaction_id = 3 [json_name = "TransactionId"];

  // details for response status
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string details = 4 [json_name = "Details"];

  // Information otp sent to user.
  string otp_info = 5 [json_name = "OtpInfo"];

  // General error code from UIDAI
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string error_code = 6 [json_name = "ErrorCode"];
}

message EKYCVerifyAadhaarOtpResponse {

  // Values are Y or N
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string status = 1 [json_name = "Status"];

  // 72 char unique token from UIDAI
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string aua_specific_uid_token = 2 [json_name = "auaSpecificUidT"];

  // Authentication Error Code from UIDAI
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string auth_error_code = 3 [json_name = "authErrorCode"];

  // General error code from UIDAI
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string error_code = 5 [json_name = "ErrorCode"];

  // Authentication Error Code from UIDAI
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string auth_e = 6 [json_name = "authE"];

  // Transaction id for this particular transaction in KUAserver
  // Ref: https://drive.google.com/drive/u/1/folders/1yRCPYwntlwSiqwOh6-1x3E7d2_FZ0EXF
  string transaction_id = 7 [json_name = "TransactionId"];
}

message LivenessOptions {
  // Attempt ID that can be used to get the status of this attempt of liveness. This is passed to the client
  // with the KYC next action call.
  string attempt_id = 3;
  FaceMatchSource face_match_source = 4;
  LivenessLevel liveness_level = 5;
  bool enable_face_match = 6;
  // sync=True implies the liveness check has to be completed before responding. In case of sync=False,
  // the status of KYC+Liveness can be gotten from KYC status check.
  bool sync = 7;
  api.typesv2.VPNInfo vpn_info = 8;
}

message CheckLivenessRequest {
  message Frames {
    repeated int64 frames = 1;
  }
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  // First chunk in the stream needs to be the options chunk and the later ones part of videos.
  // Expected format mp4
  oneof metadata_video_chunks {
    LivenessOptions options = 2;
    bytes video_chunk = 3;
    int64 image_frame = 4 [deprecated = true];
    // Frames to check passive liveness. Should be >=5 and <10.
    Frames passive_image_frames = 5 [deprecated = true];
  }
}

message CheckLivenessResponse {
  // Status of the request
  rpc.Status status = 1;
  // These fields are populated only in the case of sync requests. For async requests the
  // status should be retrieved by polling status of KYC.
  FaceMatchStatus face_matched_status = 2;
  LivenessStatus liveness_status = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

// NextOnboardingActionRequest request for GetNextOnboardingAction
message GetNextOnboardingActionRequest {
  // to get the actor_id
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // Feature for which the next action needs to be fetched
  // Feature enum in onboarding_details.proto is the value expected here
  string onboarding_feature = 3;

  api.typesv2.PollingRequestInfo polling_request_info = 4;

  string feature_onboarding_entry_point = 5;
}

// NextOnboardingActionResponse response for GetNextOnboardingAction
message GetNextOnboardingActionResponse {
  rpc.Status status = 1;
  // Next action of the user
  deeplink.Deeplink next_action = 2;
  api.typesv2.PollingResponseInfo polling_response_info = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

// Request message for ConfirmAuthFactorUpdate method.
message ConfirmAuthFactorUpdateRequest {
  // Common auth header
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // debit card pin cred block.
  string cred_block = 2;

  // request id or txn id used to create the cred block.
  string cred_block_request_id = 3;

  // card Id is needed to fetch vendorCardId which is to be sent to partner bank with device re-registration request
  string card_id = 4;
}

// Response message for ConfirmAuthFactorUpdate method.
message ConfirmAuthFactorUpdateResponse {
  // Status OK will denote ConfirmAuthFactorUpdate to be completed successfully
  rpc.Status status = 1;

  // Next action to take. If device or phone number is being updated,
  // then it would be REGISTER_DEVICE else HOME.
  deeplink.Deeplink next_action = 2;
  frontend.header.ResponseHeader resp_header = 15;
}

// Request message for fetching the outgoing SMS format for device binding
// on the bank side.
message GetDeviceRegistrationSmsRequest {
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

// Response message which contains the content for the out going SMS.
message GetDeviceRegistrationSmsResponse {
  enum Status {
    // Success
    OK = 0;
    // The user has exceeded the allowed quota for generating SMS payloads.
    SMS_PAYLOAD_QUOTA_EXCEEDED = 100;
  }

  rpc.Status status = 1;

  // Outgoing SMS content.
  // Has to be minimum 35 as per our partner bank requirement & max 160 as
  // per the char limit for a single SMS.
  // This is the encrypted payload to be verified by the bank.
  string sms_content = 2 [(validate.rules).string = {min_len: 35, max_len: 160}];

  // Receiver's phone number. This number is managed by the bank.
  api.typesv2.common.PhoneNumber recipient_phone_number = 3;

  // The retry intervals is list of int, which denotes the num of seconds to
  // wait between RegisterDevice API call. The length of the list tell, the
  // num retry we are allowed to do.
  // Eg: [5, 10, 10]
  // With the above response, client is now allowed to retry RegisterDevice API call
  // 3 times (length) with an interval of 5 sec between 1st & 2nd call. 10 seconds
  // interval between 2nd & 3rd call. 10 sec between 3rd & 4th call.
  repeated int32 register_device_api_retry_interval = 4;
  frontend.header.ResponseHeader resp_header = 15;
}

// Request message for RegisterDevice method.
message RegisterDeviceRequest {
  // Common auth header used for all frontend RPCs.
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;

  // The sms content, given by the server in GetDeviceRegistrationSms API
  // This is what will be matched by the bank
  string sms_content = 2;

  // debit card pin cred block. this is expected to be set in auth factor update flow.
  string cred_block = 3;

  // request id or txn id used to create the cred block.
  // this is expected to be set in auth factor update flow.
  string cred_block_request_id = 4;

  // recipient phone number - The phone number to which sms was sent
  api.typesv2.common.PhoneNumber recipient_phone_number = 5;

  // android_sim_sub_id is the sim id from which device registration sms was sent on android.
  // It's used to identify SIM change to trigger device registration flow as per NPCI guidelines.
  // In case an invalid sim subscription id -1 will be sent by the client.
  // https://developer.android.com/reference/android/telephony/SubscriptionManager#INVALID_SUBSCRIPTION_ID
  api.typesv2.NullInt32 android_sim_sub_id = 6;

  // source tracks the flow which triggered device registration. This refers to api.auth.DeviceRegistrationSource.
  // This will be populated from the deeplink screen options.
  string source = 7;

  // blob can be used to pass around serialized data.
  // This will be populated from the deeplink screen options.s
  bytes blob = 8;
}

// Response message for RegisterDevice method.
message RegisterDeviceResponse {
  reserved 3;

  // Status OK will denote RegisterDevice to be completed successfully
  rpc.Status status = 1;

  // Supported Status by this API
  enum Status {
    OK = 0;

    // RegisterDevice failed. Client should retry again.
    RetryableError = 101;

    // Happens when someone tries to send the SMS manually
    // from a different phone or when the SMS slot selected
    // on a dual sim phone is different than the number we
    // have verified.
    PhoneNumberMismatch = 102;

    // Payload sent received by vendor did not match the one in device registration
    PayloadMismatch = 103;

    // Payload sent on device registration has expired
    PayloadExpired = 104;

    // SMS send on device registration api was not received by vendor
    SMSNotReceived = 105;
  }

  deeplink.Deeplink next_action = 2;

  frontend.header.ResponseHeader resp_header = 15;
}

message GetKYCRecordRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
}

message GetKYCRecordResponse {
  message KYCRecord {
    api.typesv2.common.Name name = 1;
    api.typesv2.Date dob = 2;
    api.typesv2.PostalAddress permanent_address = 3;
    api.typesv2.common.Image photo = 4;
  }
  rpc.Status status = 1;
  KYCRecord record = 2;

  // Sub heading that's right below the title.
  string sub_heading = 3;
  frontend.header.ResponseHeader resp_header = 15;
}

message AccountSetupRequest {
  frontend.header.RequestHeader req = 1;
}

message AccountSetupResponse {
  frontend.header.ResponseHeader resp_header = 1;
  bytes meta_blob = 2;
}

message CheckAccountSetupStatusRequest {
  frontend.header.RequestHeader req = 1;

  // This is an opaque blob which gets passed back and forth from the client and
  // server to maintain state across multiple tries. The client is just expected
  // pass this in the CheckAccountSetupStatusRequest.
  bytes meta_blob = 2;
}

message CheckAccountSetupStatusResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // Detailed status related to various stages in account creation.
  message DetailedStatus {
    // Every stage of account creation will have 3 enums corresponding to it, viz.,
    // in-progress, complete, failed. In the detailed_status field, it's expected that
    // only one of the 3 is populated for each stage. The reason why this is modeled as
    // a single repeated field instead of individual statuses is to allow the client to
    // be compatible with changes on the backend where additional states might be incorporated
    // in the account creation process.
    enum Status {
      // Default value for the status enum.
      STATUS_UNSPECIFIED = 0;

      IN_PROGRESS = 1;
      SUCCESS = 2;
      FAILED = 3;
    }
    Status status = 1;

    // Detailed description of the status in a human understandable language. The
    // client may choose to display this string as-is to the user.
    string localized_description = 2;
  }

  // Details of the account set up process. Repeated because there are multiple
  // steps in each process and a status is returned for each one.
  repeated DetailedStatus detailed_status = 2;

  // Timestamp at which the client is supposed to poll next. If this is -1 or in the past,
  // then poll immediately.
  // to deal with issues like clock skew?
  google.protobuf.Timestamp next_poll_time = 3;

  // This is an opaque blob which gets passed back and forth from the client and
  // server to maintain state across multiple tries. The client is just expected
  // pass this in the CheckAccountSetupStatusRequest.
  bytes blob_meta = 4;

  // This value is between 0 to 100. It's used on the client side to display how much
  // the onboarding process is complete.
  int32 onboarding_progress_percent = 5;

  // Account related information for client
  AccountDetails account_details = 6;

  // Card related details for client
  Card card_details = 7;

  // Next action after onboarding
  frontend.deeplink.Deeplink next_action = 8;
}

// TODO (keerthana): Move this to frontend/savings
message AccountDetails {
  // account number belonging to the account,
  // is non empty in only if account is created
  string account_number = 1;

  // This is the account id of the newly created account.
  // This is different from account_number. This is what
  // is gonna be used, for frontend-backend communication
  // with reference to an account.
  // It will be non empty in only if account is created
  string account_id = 2;

  // vpa of user
  string vpa = 3;
}

// TODO (keerthana): Move this to frontend/card
message Card {
  // card identifier once the card is created successfully. This is unique across all
  // actors, card types, card forms
  //
  // A card can be uniquely identified using
  // 1. A UUID (lets say DB primary key)
  // 2. actor_id + card_type + card_form + vendor + network_type
  //
  // Can we always rely on 1. to refer to a card for a user?
  // TODO(anand): finalise the uniqueness criteria between 1 and 2.
  string card_id = 1;

  // Basic card data to be displayed on the card. As soon as a card is created, the
  // card number, expiry and the name on the card is displayed on the UI.
  // if these details can be saved/cached at UI.
  frontend.card.BasicCardInfo card_info = 2;

  // Card pin set otp token expire time. If expiry time is passed,
  // then pin set can be done via triggering OTP and passing OTP in cred block in pin set RPC.
  google.protobuf.Timestamp pin_set_token_expire_at = 3;
}

message CheckAuthFactorUpdateStatusRequest {
  // A set of authentication attributes
  frontend.header.AuthHeader auth = 1 [deprecated = true];
  frontend.header.RequestHeader req = 15;
  string afu_id = 2;
}

message CheckAuthFactorUpdateStatusResponse {
  enum Status {
    // AFU successful; client should follow next action
    OK = 0;

    // AFU in-progress; client should call the API again
    AFU_IN_PROGRESS = 101;

    // AFU has failed permanently; client should show error view
    AFU_FAILED = 102;
  }

  enum PollStatus {
    option deprecated = true;

    POLL_UNSPECIFIED = 0;

    // Client is expected to poll the API.
    IN_PROGRESS = 1;

    // No need to poll, perform next action.
    SUCCESS = 2;

    // some unexpected failure on backend.
    // client to do error handling.
    FAILURE = 3;
  }

  frontend.header.ResponseHeader resp_header = 15;

  PollStatus poll_status = 2 [deprecated = true];

  // Next action after onboarding
  // client should follow it if status is OK
  frontend.deeplink.Deeplink next_action = 3;
}

message SkipOnboardingStageRequest {
  // A set of authentication attributes
  frontend.header.RequestHeader req = 1;

  enum Stage {
    // unspecified
    STAGE_UNSPECIFIED = 0;
    // Add Money
    ADD_MONEY = 1;
    VKYC = 2;
    REFERRAL_FINITE_CODE = 3;
    WORK_EMAIL_VERIFICATION = 4;
    LINKEDIN_VERIFICATION = 5;
    OPTIONAL_VKYC = 6;
    GMAIL_VERIFICATION = 7;
    EMPLOYMENT_VERIFICATION = 9;
    EPFO_COMPANY_SEARCH = 10;
    CONNECTED_ACCOUNTS = 11;
    BKYC = 12;
    ITR_INTIMATION_VERIFICATION = 13;
    KYC_NAME_DOB_VALIDATION = 14;
    SA_DECLARATION = 15;
    ORDER_PHYSICAL_CARD = 16;
  }

  // stage to be skipped (preference will be given to stage_str)
  Stage stage = 2;

  // stage in string format map to OnboardingStage (preference will be given to stage_str)
  string stage_str = 3;
}

message SkipOnboardingStageResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // next action to be taken after skipping the specified stage
  frontend.deeplink.Deeplink next_action = 2;
}


message RecordIosDeviceAttestationRequest {
  // A set of authentication attributes
  frontend.header.RequestHeader req = 1;

  // Attestation generated by the iOS device
  string attestation = 2;

  // Base64 encoding of the identifier used to refer to the key whose validity is being attested.
  string key_identifier = 3;

  // One-time use token that client got from backend in order to generate
  // the attestation. This is used to avoid replay attacks.
  string nonce = 4;
}

message RecordIosDeviceAttestationResponse {
  enum Status {
    // Successfully validated attestation
    OK = 0;

    // Internal Server Error
    INTERNAL = 13;

    // If attestation sent in request is invalid
    INVALID_ATTESTATION = 100;
  }

  frontend.header.ResponseHeader resp_header = 1;
}

message UpdateUserDetailsRequest {
  // A set of authentication attributes
  frontend.header.RequestHeader req = 1;

  // Name of the user
  // optional
  api.typesv2.common.Name name = 2;

  // DOB of the user
  // optional
  api.typesv2.Date dob = 3;
}

message UpdateUserDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message SavePanAndDobRequest {
  frontend.header.RequestHeader req = 1;
  string pan_number = 2;
  google.type.Date dob = 3;
  string entry_point = 4;
  // contains next action to be sent, will be used in flows other than onboarding
  bytes blob = 5;
  // list of consents to be recorded by BE
  // client should use the list of consents sent in the screen options of REGISTER_CKYC screen to populate this field
  repeated string consents = 6;
  // The name as it appears on the PAN (Permanent Account Number) card.
  api.typesv2.common.Name name = 7;
}

message SavePanAndDobResponse {
  rpc.Status status = 1;
  frontend.header.ResponseHeader resp_header = 2;
  deeplink.Deeplink next_action = 3;

  // field_id <> error message mapping. The error message has to be shown inline
  map<string, api.typesv2.common.Text> field_inline_error = 4;
}

message SendVKYCNotificationRequest {
  frontend.header.RequestHeader req = 1;
  deeplink.VKYCNotificationScreenOptions.NotificationType notification_type = 2;
}

message SendVKYCNotificationResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message CheckAccountClosureEligibilityRequest {
  frontend.header.RequestHeader req = 1;
}

message CheckAccountClosureEligibilityResponse {
  frontend.header.ResponseHeader resp_header = 1;
  api.typesv2.common.BooleanEnum can_close_account = 2;
}

message ConfirmAccountClosureRequest {
  frontend.header.RequestHeader req = 1;
  // this is needed to fetch entry from oauth_signup_data table and revoke the refresh token (iOS) for the user
  string oauth_token = 2;

  // used to check if the user logged in through apple or google sign in
  OAuthProvider oauth_provider = 3;
}

message ConfirmAccountClosureResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message StoreDeviceSensorDataRequest {
  frontend.header.RequestHeader req = 1;
  string liveness_request_id = 2;
  repeated api.typesv2.ThreeDimensionalCoordinate accelerometer_data = 3;
  repeated api.typesv2.ThreeDimensionalCoordinate gyroscope_data = 4;
  repeated float proximity = 5;
}

message StoreDeviceSensorDataResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message ResetOnboardingStageRequest {
  frontend.header.RequestHeader req = 1;

  // The onboarding stage which needs to be reset sent as a string
  // This string maps to OnboardingStage enum in api/user/onboarding/internal/onboarding_details.proto
  string stage_identifier = 2;
}
message ResetOnboardingStageResponse {
  frontend.header.ResponseHeader resp_header = 1;

  frontend.deeplink.Deeplink next_action = 2;
}


message DeleteUserRequest {
  frontend.header.RequestHeader req = 1;
  string deletion_reason = 2;
}

message DeleteUserResponse {
  frontend.header.ResponseHeader resp_header = 1;
  deeplink.Deeplink next_action = 2;
}

message AcquisitionInfo {
  // params present in the attribution url used by the user to land on the app.
  // for e.g. { "af_channel": "value", "c": "campaign_name"}.
  // usage: for a new user only. It can be used to modify onboarding flows.
  map<string, string> attribution_link_url_params = 1 [deprecated = true];

  // fields from the onConversionDataSuccess callback of Appsflyer
  google.protobuf.Struct appsflyer_on_conversion_data_success_payload = 2;
  // fields from the install-referrer.
  // applicable only for Android.
  google.protobuf.Struct install_referrer_payload = 3;
}

// deprecated in favor of LoginDeviceInfo
message DeviceMetadata {
  // language set on device
  string default_device_language = 1;

  // Applicable only for Android. Device Identifier for Android.
  // The advertising ID is a unique, user-resettable ID for advertising, provided by Google Play services.
  string google_advertising_id = 2;

  // On Android 8.0 (API level 26) and higher versions of the platform, a 64-bit number (expressed as a hexadecimal
  // string), unique to each combination of app-signing key, user, and device. Values of ANDROID_ID are scoped by
  // signing key and user. The value may change if a factory reset is performed on the device or if an APK signing key changes
  string android_id = 3;

  // The Firebase installations service (FIS) provides a Firebase installation ID (FID) for each installed
  // instance of a Firebase app. The Firebase installation ID is used internally by other Firebase services.
  string firebase_installation_id = 4;

  // list of unallowed apps installed on the user's device. Applicable for Android.
  repeated string red_listed_apps = 5;

  // List of SIM subscription IDs on Android client. If a sim changes, these IDs change.
  // It's used to identify SIM change to trigger device registration flow as per NPCI guidelines.
  // In case an invalid sim subscription id -1 will be sent by the client.
  // https://developer.android.com/reference/android/telephony/SubscriptionManager#INVALID_SUBSCRIPTION_ID
  repeated int32 android_sim_sub_ids = 6;

  // It's an identifier for app's install event
  // For a device, this id remains unchanged, until user wipes app cache or re-installs app.
  string install_id = 7;

  // Total RAM of the device in MBs. (Not the available RAM).
  uint64 total_ram_in_mb = 8;
  // Total internal storage of the device in MBs. (Not the available storage).
  uint64 total_internal_storage_in_mb = 9;
  // flag used to indicate whether the Android client has obtained the necessary phone permissions.
  // phone permission is required for to fetch android_sim_sub_ids.
  bool is_phone_permission_granted = 10;
}

message LoginDeviceInfo {
  // language set on device
  string default_device_language = 1;

  // Applicable only for Android. Device Identifier for Android.
  // The advertising ID is a unique, user-resettable ID for advertising, provided by Google Play services.
  string google_advertising_id = 2;

  // On Android 8.0 (API level 26) and higher versions of the platform, a 64-bit number (expressed as a hexadecimal
  // string), unique to each combination of app-signing key, user, and device. Values of ANDROID_ID are scoped by
  // signing key and user. The value may change if a factory reset is performed on the device or if an APK signing key changes
  string android_id = 3;

  // The Firebase installations service (FIS) provides a Firebase installation ID (FID) for each installed
  // instance of a Firebase app. The Firebase installation ID is used internally by other Firebase services.
  string firebase_installation_id = 4;

  // list of unallowed apps installed on the user's device. Applicable for Android.
  repeated string red_listed_apps = 5;

  // List of SIM subscription IDs on Android client. If a sim changes, these IDs change.
  // It's used to identify SIM change to trigger device registration flow as per NPCI guidelines.
  // In case an invalid sim subscription id -1 will be sent by the client.
  // https://developer.android.com/reference/android/telephony/SubscriptionManager#INVALID_SUBSCRIPTION_ID
  repeated int32 android_sim_sub_ids = 6;

  // It's an identifier for app's install event
  // For a device, this id remains unchanged, until user wipes app cache or re-installs app.
  string install_id = 7;

  // unique identifier of the requesting app
  string app_id = 8;

  // Total RAM of the device in MBs. (Not the current RAM usage).
  uint64 total_ram_in_mb = 9;

  // Total internal storage of the device in MBs. (Not the current storage usage).
  uint64 total_internal_storage_in_mb = 10;

  // flag used to indicate whether the Android client has obtained the necessary phone permissions.
  // phone permission is required for to fetch android_sim_sub_ids.
  bool is_phone_permission_granted = 11;
}

message PushEKYCEventRequest {
  frontend.header.RequestHeader req = 1;
  // Status of the EKYC request: Y/N
  string ekyc_status = 2;
  // authErrorCode field of the response
  string error_code = 3;
  // error message received in the EKYC SDK
  string error_message = 4;
  // raw response received from the sdk
  string raw_response = 5;
  enum RequestType {
    REQUEST_TYPE_UNSPECIFIED = 0;
    REQUEST_TYPE_GENERATE_OTP = 1;
    REQUEST_TYPE_VERIFY_OTP = 2;
  }
  RequestType request_type = 6;
}

message PushEKYCEventResponse {
  frontend.header.ResponseHeader resp_header = 1;
}

message UpdateFiLiteAccessibilityRequest {
  frontend.header.RequestHeader req = 1;
  string source = 2;
}

message UpdateFiLiteAccessibilityResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GenerateBKYCOtpRequest {
  frontend.header.RequestHeader req = 1;
  bool is_consent_given = 2;
}

message GenerateBKYCOtpResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
  // otp for handshake between client and agent app
  string otp = 3;
  // qr code in base64 format for handshake between client and agent app
  string qr_code = 4;
  // failure reason will be populate if any of eligibility validation failed
  // at time of impl client is not consuming it we are using it for FeApiResponseEvent custom property
  string failure_reason = 5;
}

message GetBKYCStatusRequest {
  frontend.header.RequestHeader req = 1;
  // blob contains attempt count, helps backend to decide on next poll duration
  bytes blob = 2;
}

message GetBKYCStatusResponse {
  enum status {
    STATUS_OK = 0;
    // indicates client to poll again after next_poll_duration
    STATUS_POLL = 101;
  }
  frontend.header.ResponseHeader resp_header = 1;
  // client is expected to poll duration after following duration (in second)
  int32 next_poll_duration = 2;
  // in case of success status client is expected to consume next action
  frontend.deeplink.Deeplink next_action = 3;
  // blob contains attempt count, helps backend to decide on next poll duration
  bytes blob = 4;
}

message ConfirmBKYCDetailsRequest {
  frontend.header.RequestHeader req = 1;
  // user consent stating kyc details are correct
  bool is_consent_given = 2;
  message CommunicationAddressInfo {
    // true in case user want to update current address
    bool address_update_needed = 1;
    // new communication address that the user wants to update to
    google.type.PostalAddress communication_address = 2;
  }
  CommunicationAddressInfo comm_address_info = 3;
}

message ConfirmBKYCDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetKYCStatusRequest {
  frontend.header.RequestHeader req = 1;
  string client_req_id = 2;
}

message GetKYCStatusResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message SetOnboardingIntentRequest {
  frontend.header.RequestHeader req = 1;
  string intent_identifier = 2;
  // Entry point from where user entered the ONBOARDING_INTENT_SELECTION screen
  // The entry point from intent screen deeplink should be passed via client to the SetOnboardingIntent call
  // string version of user.onboarding.IntentSelectionEntryPoint
  string entry_point = 3;
}

message SetOnboardingIntentResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetOnboardingSoftIntentOptionsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetOnboardingSoftIntentOptionsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // https://www.figma.com/design/GNtKeutYAxPhAyBVvFxbMr/D2H-%E2%80%A2-Workfile?node-id=1778-22348&t=1LHThNz1BtxXaVKz-1
  api.typesv2.common.VisualElement logo = 2;
  api.typesv2.common.Text title = 3;

  // list of vertically stacked sections of soft intent choices
  // https://www.figma.com/design/GNtKeutYAxPhAyBVvFxbMr/D2H-%E2%80%A2-Workfile?node-id=1778-22348&t=FhwCbBKXyhIvIXYa-1
  repeated Section sections = 4;
  message Section {
    api.typesv2.common.Text title = 1;
    api.typesv2.common.VisualElement left_icon = 2;
    api.typesv2.common.ui.widget.BackgroundColour bg_colour = 3;

    // list of soft intent choices within a section
    // https://www.figma.com/design/GNtKeutYAxPhAyBVvFxbMr/D2H-%E2%80%A2-Workfile?node-id=1778-22348&t=FhwCbBKXyhIvIXYa-1
    repeated SoftIntentOption soft_intent_options = 4;
    message SoftIntentOption {
      // id will be used to record soft intent choices, eg - ONBOARDING_SOFT_INTENT_AUTO_PAY
      string id = 1;
      api.typesv2.ui.IconTextComponent unselected_chip = 2;
      api.typesv2.ui.IconTextComponent selected_chip = 3;
    }

    // if present, section will be collapsed by default and expand on click of this button
    api.typesv2.common.VisualElement right_collapse_icon = 6;
  }

  // maximum number of soft intent options that the user can choose
  // if user tries to select more than this limit, client shows a toast
  // if this field is zero, user can select as many soft intents as they want
  uint32 max_allowed_selections = 5;

  // toast message to be displayed when user exceeds max allowed selections
  api.typesv2.common.Text max_selections_exceeded_toast = 6;

  // this cta will only be enabled when user makes at least one selection
  // client will call SetOnboardingSoftIntentRequest on this cta
  api.typesv2.ui.IconTextComponent continue_cta = 7;

  // this cta will only be enabled if user doesn't make even one selection
  // client will call SetOnboardingSoftIntentRequest on this cta
  api.typesv2.ui.IconTextComponent later_cta = 8;

  // Subtitle for Soft Intent Screen
  // Example: Choose up to 3 options
  api.typesv2.common.Text subtitle = 9;
}

message SetOnboardingSoftIntentRequest {
  frontend.header.RequestHeader req = 1;
  repeated string soft_intent_identifiers = 2;
  // entry point from where user entered the ONBOARDING_SOFT_INTENT_SELECTION screen
  // this entry point should be passed from the deeplink screen options
  // string version of user.onboarding.SoftIntentSelectionEntryPoint
  string entry_point = 3;
}

message SetOnboardingSoftIntentResponse {
  frontend.header.ResponseHeader resp_header = 1;
  frontend.deeplink.Deeplink next_action = 2;
}

message GetGenerateOtpInfoRequest {
  frontend.header.RequestHeader req = 1;
  // acquisition info for the user
  // this is used to decide the intent of the user and based on that backend will send signup screen content
  // If client will not send this, BE will send default content (assuming saving account intent)
  AcquisitionInfo acquisition_info = 2;
}

message GetGenerateOtpInfoResponse {
  frontend.header.ResponseHeader resp_header = 1;
  repeated CountryInfo country_infos = 2;

  // https://www.figma.com/design/t7NXs6rvE0gQTKvA9dpIHM/Wealth-builder-2.0?node-id=2490-22414&t=iF0sPaACAxN6FR8v-4
  // https://www.figma.com/design/HDrcQuiYNLE7nTIN6ltAmd/%E2%9A%A1%EF%B8%8F-Onboarding-v1.2--%C2%A0FFF?node-id=44230-111&t=aZWpBSYekAhzUFyl-4

  message ConsentWithDescription {
    api.typesv2.form.Consent consent = 1;
    api.typesv2.common.Text description = 2;
    // Partner logos info include prefix text and logos
    PartnerLogosInfo partner_logos_info = 3;
  }

  // Partner logos information containing prefix text and logo images
  message PartnerLogosInfo {
    // Prefix text to be displayed before the partner logos
    api.typesv2.common.Text prefix_text = 1;
    // Partner logos to be displayed on the consent screen
    repeated api.typesv2.common.VisualElement partner_logos = 2;
  }

  message PhoneVerificationScreenData {
    // title for phone verification screen
    api.typesv2.common.Text title = 1;
    // background color for phone verification screen
    api.typesv2.common.ui.widget.BackgroundColour bg_colour = 2;
    ConsentWithDescription consents = 3;
  }
  PhoneVerificationScreenData phone_verification_screen_data = 3;
}

