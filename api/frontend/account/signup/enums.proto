syntax = "proto3";

package frontend.account.signup;

option go_package = "github.com/epifi/gamma/api/frontend/account/signup";
option java_package = "com.github.epifi.gamma.api.frontend.account.signup";

// FaceMatchSource indicates the source for the reference image to be used for comparing the sent
// image against.
enum FaceMatchSource {
  FACE_MATCH_SOURCE_UNSPECIFIED = 0;
  KYC_IMAGE = 1;
  OLD_VIDEO = 2;
}

// LivenessLevel specify the level of liveness to be used to treat as liveness as passed.
enum LivenessLevel {
  LIVENESS_LEVEL_UNSPECIFIED = 0;
  // LIVENESS_LENIENT represents a lenient check which passes even if reviewing is recommended by the vendor.
  LIVENESS_LENIENT = 1;
  // LIVENESS_HARD represents a hard check which passes only if reviewing isn't recommended by the vendor.
  LIVENESS_HARD = 2;
}

// FaceMatchStatus represents the current status of Face Match.
enum FaceMatchStatus {
  FACE_MATCH_STATUS_UNSPECIFIED = 0;
  // FACE_MATCH_PENDING means that the face match is in the process.
  FACE_MATCH_PENDING = 1;
  // FACE_MATCH_PASSED means that the face match has passed.
  FACE_MATCH_PASSED = 2;
  // FACE_MATCH_FAILED_RETRY means that the face match has failed with retryable error.
  FACE_MATCH_FAILED_RETRY = 3;
  // FACE_MATCH_FAILED means that the face match has failed.
  FACE_MATCH_FAILED = 4;
}

// FaceMatchStatus represents the current status of Liveness.
enum LivenessStatus {
  LIVENESS_STATUS_UNSPECIFIED = 0;
  // LIVENESS_STREAMING_STARTED means that the streaming from client has started
  LIVENESS_STREAMING_STARTED = 1;
  // LIVENESS_VIDEO_RECEIVED means that the streaming of video has completed.
  LIVENESS_VIDEO_RECEIVED = 2;
  // LIVENESS_PENDING means that the liveness is in the process.
  LIVENESS_PENDING = 3;
  // LIVENESS_PASSED means that the liveness has passed.
  LIVENESS_PASSED = 4;
  // LIVENESS_FAILED_RETRY means that the liveness has failed with retryable error.
  LIVENESS_FAILED_RETRY = 5;
  // LIVENESS_FAILED means that the liveness has failed.
  LIVENESS_FAILED = 6;
}


// PhoneNumberUpdateSource represents if the user wants to update the phone
// number during login and the source of the update. There can different business
// logic actions based on the source of the phone number update flow.
enum PhoneNumberUpdateSource {
  PHONE_NUM_SOURCE_UNSPECIFIED = 0;

  // User has NOT selected any update flow. Normal login/signup flow.
  NO_UPDATE = 1;

  // First source of phone number update flow. This is triggered when the app
  // detects change in SIM. App asks the user if they want to change the
  // phone number and triggers the update flow.
  SIM_CHANGE = 2;

  // Second source of phone number update flow. This is triggered when
  // the user clicks on "I want to update my phone number" CTA button.
  // It's present when user is on log-in screen, instead of sign up.
  LOGIN_UPDATE_CTA = 3;

  // Third source of phone number update flow. This is triggered when the
  // user clears the app data.
  CLEAR_APP_DATA = 4;
}

// Entrypoint for Screen options
// Same screen can be shown from different flows, we can use this to determine the flow
enum EntryPoint {
  ENTRY_POINT_UNSPECIFIED = 0;
  ENTRY_POINT_ONBOARDING = 1;
  ENTRY_POINT_CONNECTED_ACCOUNTS = 2;
  ENTRY_POINT_MUTUAL_FUNDS_ANALYSER = 3;
  ENTRY_POINT_CREDIT_SCORE_ANALYSER = 4;
  ENTRY_POINT_PL_ONBOARDING = 5;
}
