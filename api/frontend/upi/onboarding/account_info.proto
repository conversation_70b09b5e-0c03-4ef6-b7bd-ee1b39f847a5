// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package frontend.upi.onboarding;

import "api/accounts/account_type.proto";
import "api/frontend/upi/onboarding/bank_info.proto";
import "api/frontend/upi/onboarding/enums/upi_account_preference.proto";
import "api/typesv2/account/enums.proto";

option go_package = "github.com/epifi/gamma/api/frontend/upi/onboarding";
option java_package = "com.github.epifi.gamma.api.frontend.upi.onboarding";

// AccountInfo stores various info regarding the account such as id,masked account number etc
message AccountInfo {

  // id denotes the upi account id
  string id = 1;

  // masked_account_number denotes the masked account number of the account
  string masked_account_number = 2;

  // ifsc code for the account
  string ifsc_code = 3;

  // BankInfo stores various info regarding the bank like the name, ifsc, logo etc
  frontend.upi.onboarding.BankInfo bank_info = 4;

  // UpiAccountPreference specifies the preference of the account
  frontend.upi.onboarding.enums.UpiAccountPreference upi_account_preference = 5;

  // Type of the account
  accounts.Type account_type = 6;

  // derived_account_id stores base64 encoding for DerivedAccountId which contains different account ids
  // like tpap_account_id, connected_account id etc.
  string derived_account_id = 7;

  // Account Product Offering associated with the AccountType.
  // This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
  api.typesv2.account.AccountProductOffering apo = 8;
}
