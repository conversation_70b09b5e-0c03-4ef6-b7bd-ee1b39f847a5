syntax = "proto3";

package frontend.upi.onboarding;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/frontend/upi/onboarding/enums/upi_number_type.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/frontend/upi/onboarding/upi_mapper.proto";
import "api/frontend/pay/transaction/enums.proto";

option go_package = "github.com/epifi/gamma/api/frontend/upi/onboarding";
option java_package = "com.github.epifi.gamma.api.frontend.upi.onboarding";

message UpiAccountInfo {
  // Derived account id of the upi account
  string derived_account_id = 1;

  // Vpa of the upi account
  string vpa = 2;

  message BankInfo {
    api.typesv2.common.VisualElement bank_logo = 1;

    // Bank name and account number. E.g. HDFC Bank .. 1234
    api.typesv2.common.Text account_info_text = 2;

    // E.g. Primary • ssingh@fifederal
    api.typesv2.common.Text subtitle = 3;
  }

  // Bank account details of the upi account
  BankInfo bank_info = 3;

  // List of upi numbers linked to the account
  repeated UpiNumberCard upi_number_cards = 4;

  // Container properties for the upi account card, to set bg color, corner radius etc
  api.typesv2.ui.IconTextComponent.ContainerProperties container_properties = 5;

  // default expanded/collapsed state of the upi account card.
  // note: should never be UNSPECIFIED.
  frontend.pay.transaction.ExpandCollapseState expand_collapse_state = 6;

  // down arrow image to be shown when the account card is collapsed.
  // Note: client will rotate the image to show up arrow when card is expanded.
  api.typesv2.common.VisualElement down_arrow = 7;

  message FooterCtaComponent {
    // E.g. `Add custom UPI number`
    api.typesv2.ui.IconTextComponent left_text = 1;

    // E.g. `Create` cta with deeplink to create custom upi number screen
    api.typesv2.ui.IconTextComponent cta = 2;
  }

  // Cta shown at the bottom of the upi account card
  FooterCtaComponent footer_cta = 8;
}

// Upi number details and associated actions the user can perform on the upi number
message UpiNumberCard {
  // Upi number linked to the account
  api.typesv2.common.Text upi_number = 1;

  // Vpa to which the upi number is linked
  api.typesv2.common.Text vpa = 2;

  // Type of upi number. E.g. Phone Number, Numeric ID
  frontend.upi.onboarding.enums.UpiNumberType upi_number_type = 3;

  // To show copy icon for the upi number
  frontend.upi.onboarding.Copy copy_component = 4;

  // To show additional information about the upi number,
  // for eg, if the upi number is deactivated, shows `inactive` tag
  api.typesv2.ui.IconTextComponent tag = 5;

  // UI element to shown on the right side of the upi number card
  oneof RightDisplayElement {
    // Primary action associated with the UPI number
    // E.g. If mobile number is not linked as upi number yet, show `LINK` action.
    // If mobile number is disabled, show `ENABLE` action.
    frontend.upi.onboarding.UpiNumberAction quick_action = 6;

    // Three dot overflow menu to be shown on the upi number card
    UpiNumberOverflowMenu overflow_menu = 7;
  }

  // Prompts or promo text to be shown below a upi number
  // E.g. `Link your account to Fi to receive money from any app`
  api.typesv2.ui.IconTextComponent footer_info = 8;

  // Container properties for the upi number card, to set bg color, corner radius etc
  api.typesv2.ui.IconTextComponent.ContainerProperties container_properties = 9;
}

// Over flow menu to be shown on the upi number card
message UpiNumberOverflowMenu {
  // E.g. Three dots icon
  api.typesv2.common.VisualElement overflow_menu_icon = 1;

  // List of actions to be displayed in overflow menu
  // Show the display_text from UpiNumberAction as menu option.
  // Data required to perform the action will be present in UpiAccountInfo(derivedAccountId, vpa etc)
  // E.g. Disable, Enable, Delete, Reclaim etc
  repeated frontend.upi.onboarding.UpiNumberAction menu_options = 2;
}
