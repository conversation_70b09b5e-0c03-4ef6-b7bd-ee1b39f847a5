// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package frontend.upi.onboarding;

import "api/frontend/account/upi/cred_block.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/pay/transaction/service.proto";
import "api/frontend/upi/onboarding/account_info.proto";
import "api/frontend/upi/onboarding/bank_info.proto";
import "api/frontend/upi/onboarding/connected_account_info.proto";
import "api/frontend/upi/onboarding/enums/action.proto";
import "api/frontend/upi/onboarding/enums/international_upi_payments_enums.proto";
import "api/frontend/upi/onboarding/enums/ui_entry_point.proto";
import "api/frontend/upi/onboarding/enums/upi_lite_enums.proto";
import "api/frontend/upi/onboarding/enums/upi_number_linking_type.proto";
import "api/frontend/upi/onboarding/enums/upi_number_type.proto";
import "api/frontend/upi/onboarding/enums/upi_onboarding_status.proto";
import "api/frontend/upi/onboarding/enums/upi_pin_set_options_enums.proto";
import "api/frontend/upi/onboarding/single_click_tpap_enablement_payload.proto";
import "api/frontend/upi/onboarding/upi_lite.proto";
import "api/frontend/upi/onboarding/upi_mapper.proto";
import "api/frontend/upi/onboarding/upi_pin_set_intro_screen_info.proto";
import "api/frontend/upi/onboarding/upi_settings.proto";
import "api/frontend/vendor.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/money.proto";
import "api/typesv2/pay/pre_execution_message.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";
import "api/typesv2/common/ui/widget/common.proto";

option go_package = "github.com/epifi/gamma/api/frontend/upi/onboarding";
option java_package = "com.github.epifi.gamma.api.frontend.upi.onboarding";

// Defines the Frontend RPC service for UPI onboarding related actions like linking account, creating vpa
service Onboarding {
  // ListAccountProviders calls the backend to get the list of all account providers that supports upi tpap integration
  // This will be required to check for registered account providers before registering a user account
  rpc ListAccountProvider (ListAccountProvidersRequest) returns (ListAccountProviderResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // ListAccount returns list of accounts which are not linked through tpap for the user and the given bank ifsc
  rpc ListAccount (ListAccountRequest) returns (ListAccountResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // LinkUpiAccounts calls the backend RPC that triggers account linking flow
  // It takes map of upi account ids and respective client req id to be linked
  rpc LinkUpiAccounts (LinkUpiAccountsRequest) returns (LinkUpiAccountsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetUpiAccountsActionStatus calls the backend RPC that fetches the account linking/delinking status for given accounts
  // It takes list of client req id to be linked
  // It fetches status of triggered workflows using client request id
  // It doesn't returns error in case one/some of the accounts from given account list fails to be linked
  rpc GetUpiAccountsActionStatus (GetUpiAccountsActionStatusRequest) returns (GetUpiAccountsActionStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // InitiateDelinkUpiAccount calls the backend RPC that triggers the account delinking flow for given account id with all the vendors
  rpc InitiateDelinkUpiAccount (InitiateDelinkUpiAccountRequest) returns (InitiateDelinkUpiAccountResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // RecordVpaMigrationConsent calls the backend RPC that records consent for a user
  rpc RecordVpaMigrationConsent (RecordVpaMigrationConsentRequest) returns (RecordVpaMigrationConsentResponse) {}

  // UpdateAccountPreference updates the primary account for the actor
  rpc UpdateAccountPreference (UpdateAccountPreferenceRequest) returns (UpdateAccountPreferenceResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // ActivateUpiAccount calls the backend RPC that marks the account as active for the given accountId
  rpc ActivateUpiAccount (ActivateUpiAccountRequest) returns (ActivateUpiAccountResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // CheckIfVpaMigrationRequired :
  // 1. checks whether for the given user that vpa need to be migrated or not, if required it shows a nudge to user
  // 	  about vpa migration and asks for the consent to migrate
  // 2. if user's vpa is already migrated, we check if user should be shown pop up to link other bank accounts.
  rpc CheckIfVpaMigrationRequired (CheckIfVpaMigrationRequiredRequest) returns (CheckIfVpaMigrationRequiredResponse) {
    option (rpc.device_registration_required) = false;
  }

  // BalanceEnquiry calls the backend rpc to fetch the balance for the tpap account
  rpc BalanceEnquiry (BalanceEnquiryRequest) returns (BalanceEnquiryResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetNPCICLParameters gets the necessary NPCI CL Parameter for the upi flow type in the request
  // [Deprecated in favour of GetNPCICLParametersV1]
  rpc GetNPCICLParameters (GetNPCICLParametersRequest) returns (GetNPCICLParametersResponse) {
    option deprecated = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetAccountsForSelfTransfer fetches the accounts for the self transfer
  rpc GetAccountsForSelfTransfer (GetAccountsForSelfTransferRequest) returns (GetAccountsForSelfTransferResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetActionForEntryPoint returns the action to be taken by client based on entry point
  rpc GetActionForEntryPoint (GetActionForEntryPointRequest) returns (GetActionForEntryPointResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // InitiateLinkUpiNumber initiates workflow to link upi number to given vpa
  rpc InitiateLinkUpiNumber (InitiateLinkUpiNumberRequest) returns (InitiateLinkUpiNumberResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetUpiNumberActionStatus is used to check the status of workflow for a given req_id
  rpc GetUpiNumberActionStatus (GetUpiNumberActionStatusRequest) returns (GetUpiNumberActionStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetMapperInfo checks the availability for a upiNumber
  rpc GetMapperInfo (GetMapperInfoRequest) returns (GetMapperInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetUpiNumberDetails gets all the upi number details from the database for the given derived account id and vpa
  rpc GetUpiNumberDetails (GetUpiNumberDetailsRequest) returns (GetUpiNumberDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // InitiateDelinkUpiNumber initiates workflow to delink upi number to given vpa
  rpc InitiateDelinkUpiNumber (InitiateDelinkUpiNumberRequest) returns (InitiateDelinkUpiNumberResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // DisableOrEnableUpiNumber disables or enables the upi number based on the request type
  rpc DisableOrEnableUpiNumber (DisableOrEnableUpiNumberRequest) returns (DisableOrEnableUpiNumberResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // ValidateAadhaarNoForUpiPinSet validates last 4 digits of aadhaar number for the user in upi pin set flow
  //   - backend fetches the last 4 digits of aadhaar no from vendor using ListAccount rpc, and stores in redis for
  //     a specific amount of time (TTL)
  //   - If this validation request comes post that time, then aadhaar no validation will be considered failed and user will
  //     have to start upi pin set flow from the beginning
  rpc ValidateAadhaarNoForUpiPinSet (ValidateAadhaarNoForUpiPinSetRequest) returns (ValidateAadhaarNoForUpiPinSetResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetUpiPinSetIntroScreenInfo is used to get all the info on the upi pin set intro screen
  //  - If user's Bank Account is Aadhaar enabled and flow is Set / Reset
  //  - then debit card and Aadhaar otp flow should be available for the user to set upi pin
  //    else only debit card flow (In that case, we need not show options to user, as there is only
  //    one option to choose).
  rpc GetUpiPinSetIntroScreenInfo (GetUpiPinSetIntroScreenInfoRequest) returns (GetUpiPinSetIntroScreenInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // AuthoriseInternationalPaymentsAction - is used to authorise international payments action (activation/deactivation)
  // - After workflow initiation for activation / deactivation, user will be asked for auth
  // - then AuthoriseInternationalPaymentsAction is called with auth details and action
  //   to be initiated with vendor
  // - workflow initiated for the action waits for the signal,
  //   which is to be sent after initiating the given action
  //   with vendor
  rpc AuthoriseInternationalPaymentsAction (AuthoriseInternationalPaymentsActionRequest) returns (AuthoriseInternationalPaymentsActionResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // InitiateInternationalPaymentsActivation is used to initiate Activation of international payments with vendor for given account
  rpc InitiateInternationalPaymentsActivation (InitiateInternationalPaymentsActivationRequest) returns (InitiateInternationalPaymentsActivationResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // InitiateInternationalPaymentsDeactivation is used to initiate Deactivation of international payments with vendor for given account
  rpc InitiateInternationalPaymentsDeactivation (InitiateInternationalPaymentsDeactivationRequest) returns (InitiateInternationalPaymentsDeactivationResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetInternationalPaymentActionStatus is used to check the status of International payments action for a given req_id
  rpc GetInternationalPaymentActionStatus (GetInternationalPaymentActionStatusRequest) returns (GetInternationalPaymentActionStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // UpdateDefaultMerchantPaymentPreference updates the default merchant payment preference for a credit account
  rpc UpdateDefaultMerchantPaymentPreference (UpdateDefaultMerchantPaymentPreferenceRequest) returns (UpdateDefaultMerchantPaymentPreferenceResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetUpiLiteInfo - used to fetch the all the info regarding upi lite account
  // 1. If upi lite account is activated, then client should get the upi lite account info
  //    and the info required for top up payment for upi lite account
  // 2. If upi lite account is not activated, client should initiate call for
  //    upi lite activation and start polling to fetch latest status of workflow
  rpc GetUpiLiteInfo (GetUpiLiteInfoRequest) returns (GetUpiLiteInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }


  // InitiateUpiLiteActivation - initiates the upi lite activation for a given upi account
  // 1. client fetches creds using CL of type - 'DEVICE' and subtype = 'IDENTITY'
  // 2. initiates the activation of upi lite for an upi account.
  rpc InitiateUpiLiteActivation (InitiateUpiLiteActivationRequest) returns (InitiateUpiLiteActivationResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // InitiateUpiLiteDeactivation - initiates the upi lite deactivation for a given upi lite account
  rpc InitiateUpiLiteDeactivation (InitiateUpiLiteDeactivationRequest) returns (InitiateUpiLiteDeactivationResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // SyncUpiLiteInfo-
  // 1. Delete upi lite account which have 0 balance
  // 2. Fetch account status and detail for upi lite account
  rpc SyncUpiLiteInfo (SyncUpiLiteInfoRequest) returns (SyncUpiLiteInfoResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // CheckUpiLiteActionStatus -
  // 1. checks the status of upi lite action workflow.
  // 2. decides the next action to be performed by the client.
  //    Based on the workflow status, client would need to perform one of the following 3 actions:
  //    2.1 InitiatePayment - initiate upi lite top (or withdraw during deactivation) by calling InitiatePayment FE rpc
  //    2.2 SyncUpiLite - call SyncUpiLite Fe rpc to fetch the latest info regarding
  //    2.3 PollWorkflow - If upi lite action workflow is still in progress, then client will be given
  //                       a retry timer using which client shall make the next polling call (if required).
  // 3. generates the terminal_screen to be shown to the user. Client shall show the screen to the user if
  //    populated (irrespective of the next action, screen should be shown to user and next action can go on
  //    in background).
  rpc CheckUpiLiteActionStatus (CheckUpiLiteActionStatusRequest) returns (CheckUpiLiteActionStatusResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetAccountsForUpiLiteActivation - fetches the list of all the bank accounts that are eligible for upi lite activation
  // Eligibility of the bank account is determined by the response received from list account provider RPC
  rpc GetAccountsForUpiLiteActivation (GetAccountsForUpiLiteActivationRequest) returns (GetAccountsForUpiLiteActivationResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetConnectedAccountsForTpapLinking : fetches all the connected accounts for actor.
  // It will be used to prompt users to connect their connected accounts as TPAP accounts.
  rpc GetConnectedAccountsForTpapLinking (GetConnectedAccountsForTpapLinkingRequest) returns (GetConnectedAccountsForTpapLinkingResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // IsAccountActionAllowed: used to check if the given action is allowed for an account.
  // E.g. In case, user has an active Mandate present delink upi accounts and disable vpa actions
  // are not allowed.
  // Usage: If user tries to perform a given action, BE evaluates if it is allowed or not and gives
  // response for the further action. If user with an active Mandate tries to delink upi account
  // they will see a bottom sheet stating that delink upi account is not allowed due to an active
  // mandate.
  rpc IsAccountActionAllowed (IsAccountActionAllowedRequest) returns (IsAccountActionAllowedResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetUpiSettingsDetails is used to fetch the UPI settings details for the user
  // This currently includes the UPI number settings details i.e. managing mapper features
  rpc GetUpiSettingsDetails (GetUpiSettingsDetailsRequest) returns (GetUpiSettingsDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetMapperQuickLinkScreenDetails is used to fetch data required to show the quick link screen and eventually start linking
  rpc GetMapperQuickLinkScreenDetails (GetMapperQuickLinkScreenDetailsRequest) returns (GetMapperQuickLinkScreenDetailsResponse) {
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}
message ListAccountProvidersRequest {
  frontend.header.RequestHeader req = 15;
  // upi account type for which accounts needs to be listed
  frontend.upi.onboarding.enums.UpiAccountType upi_account_type = 1;
}

message ListAccountProviderResponse {

  // stores info regarding various banks like name,ifsc,logo etc
  repeated frontend.upi.onboarding.BankInfo bank_infos = 1;

  frontend.header.ResponseHeader resp_header = 15;

  // stores info regarding popular banks like name,ifsc,logo etc
  repeated frontend.upi.onboarding.BankInfo popular_bank_infos = 2;

  // device registration deeplink
  // Action for client to register device. The action 3 steps:
  // 1. Get Binding SMS
  // 2. Send SMS to recipient
  // 3. Make RegisterDevice() call to server
  // This is populated in case user wants to link a upi account but their device is not yet registered.
  // Expected to happen in case of Fi Lite only

  // Deprecated in favour of NextAction
  frontend.deeplink.Deeplink register_device_deeplink = 3 [deprecated = true];

  // sticky note to be used for NOTE/INFO purpose above the CTA
  // Note: Can be nil
  // for e.g. to inform regarding the txn amount limit within first 24hrs (of linking / of first txn)
  api.typesv2.common.Text sticky_note = 4;

  oneof NextAction {
    // device registration deeplink:
    // Action for client to register device. The action 3 steps:
    // 1. Get Binding SMS
    // 2. Send SMS to recipient
    // 3. Make RegisterDevice() call to server
    // This is populated in case user wants to link a upi account but their device is not yet registered.
    // Expected to happen in case of Fi Lite only
    frontend.deeplink.Deeplink device_registration_deeplink = 5;

    // This bottom sheet will be used to perform any next action for the user
    // E.g. If user's vpa is not migrated. We'll ask user to migrate their
    // vpa before allowing to link other bank accounts.
    frontend.pay.transaction.PaymentCtaOperationDetails.BottomSheet operation_bottom_sheet = 6;
  }

  // partner tag to be shown as footer for the ListAccountProviders page
  // Example: "Powered by Rupay Credit on UPI" in case of Rupay Credit
  api.typesv2.common.VisualElement partner_tag = 7;

}

message ListAccountRequest {
  frontend.header.RequestHeader req = 15;

  // ifsc code for a particular bank
  // Note: this is not ifsc code for a branch
  string bank_ifsc = 1;
  // name of the bank
  string bank_name = 2;
  // upi account type for which accounts needs to be listed
  frontend.upi.onboarding.enums.UpiAccountType upi_account_type = 3;

  // SingleClickTpapEnablementPayload : only to be used in single click
  // tpap enablement flow. Client is not expected to pass in normal
  // ListAccount flows.
  frontend.upi.onboarding.SingleClickTpapEnablementPayload single_click_tpap_enablement_payload = 4;
}

message ListAccountResponse {
  enum Status {
    OK = 0;
    // invalid argument passed by the client
    INVALID_ARGUMENT = 3;
    // upi number does not exist
    RECORD_NOT_FOUND = 5;
    // system faced internal errors while processing the request
    INTERNAL = 13;
    // all the accounts of users are already
    // linked for the given bank
    ALL_ACCOUNTS_ALREADY_LINKED_FOR_BANK = 101;
  }

  frontend.header.ResponseHeader resp_header = 15;

  // list of account info containing account details like account id, masked account number etc.
  repeated frontend.upi.onboarding.AccountInfo account_infos = 1;

  // Terms and conditions to be shown to user for taking consent
  api.typesv2.common.Text tnc_consent = 2;

  // This flag will be used by client to decide
  // if linking should be triggered automatically
  // for the accounts received in the list account
  // or should they wait for user's consent.
  // Context: If user has only one account, we
  // can directly trigger account linking, since
  // there is only one option to select.
  bool should_trigger_account_linking_automatically = 3;

  // next_action : is the deeplink for the next action
  // to be performed by client. E.g. In one-click tpap
  // enablement flow, if user has not done device
  // registration then client should get the deeplink
  // for the device-registration before proceeding for
  // account linking flows.
  frontend.deeplink.Deeplink next_action = 4;

  // partner tag to be shown as footer for the ListAccount page
  // Example: "Powered by Rupay Credit on UPI" in case of Rupay Credit
  api.typesv2.common.VisualElement partner_tag = 5;
}

message LinkUpiAccountsRequest {
  frontend.header.RequestHeader req = 15;
  // map of upi account ids and respective client req id to be linked
  map<string, string> account_id_client_req_id_map = 1;
  // vendor to be used as partner bank
  // [Optional] If vendor is not passed by the client, rpc will decide the vendor on run time
  frontend.Vendor vendor = 2;
}

message LinkUpiAccountsResponse {
  frontend.header.ResponseHeader resp_header = 15;
}


message GetUpiAccountsActionStatusRequest {
  frontend.header.RequestHeader req = 15;
  // list of client req id
  repeated string client_req_ids = 1;
}

message GetUpiAccountsActionStatusResponse {
  frontend.header.ResponseHeader resp_header = 15;
  // map that stores client request id and information regarding action taken on it
  map<string, UpiAccountActionInfo> client_req_id_action_info_map = 1;

  // deeplink for the next action to be performed by the client.
  deeplink.Deeplink next_action = 2;
}

message InitiateDelinkUpiAccountRequest {
  frontend.header.RequestHeader req = 15;

  // accountId to be delinked
  string derived_account_id = 1;

  // client req id
  string client_req_id = 2;
}

message InitiateDelinkUpiAccountResponse {
  frontend.header.ResponseHeader resp_header = 15;
}

message RecordVpaMigrationConsentRequest {
  frontend.header.RequestHeader req = 15;
  // client request id to be used for vpa migration
  string client_req_id = 1;
}

message RecordVpaMigrationConsentResponse {
  frontend.header.ResponseHeader resp_header = 15;
}

message UpdateAccountPreferenceRequest {
  frontend.header.RequestHeader req = 15;
  // account id for the actor
  string derived_account_id = 1;
}

message UpdateAccountPreferenceResponse {
  frontend.header.ResponseHeader resp_header = 15;
}

message ActivateUpiAccountRequest {
  frontend.header.RequestHeader req = 15;

  // accountId to be activated
  string derived_account_id = 1;
}

message ActivateUpiAccountResponse {
  frontend.header.ResponseHeader resp_header = 15;
}

message CheckIfVpaMigrationRequiredRequest {
  frontend.header.RequestHeader req = 15;
  // ui entry point from where rpc was called
  frontend.upi.onboarding.enums.UIEntryPoint ui_entry_point = 1;
}

message CheckIfVpaMigrationRequiredResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // deeplink for showing pop up option for recording user's consent for vpa migration
  // [deprecated in favour of next action deeplink
  frontend.deeplink.Deeplink vpa_migration_deeplink = 1 [deprecated = true];

  // User's next action depends on the current state of the user
  // 1. If user's vpa is not migrated, then we'll show vpa migration pop up
  // 2. If user's vpa is migrated then we'll show pop up to connect other bank accounts.
  frontend.deeplink.Deeplink next_action = 2;
}

message BalanceEnquiryRequest {
  frontend.header.RequestHeader req = 15;

  // derived_account_id stores base64 encoding for DerivedAccountId which contains different account ids
  // like tpap_account_id, connected_account id etc.
  string derived_account_id = 1;
  // unique id
  string txn_id = 2;
  // We will validate the cred block generated through NPCI common library.
  // Cred block is to be passed to NPCI in XML format
  // To prevent errors in data transformation and to keep business logic out of client,
  // frontend expects NPCI's cred block in a structured fashion
  frontend.account.upi.CredBlock npci_cred_block = 3;
}

message BalanceEnquiryResponse {
  frontend.header.ResponseHeader resp_header = 15;

  enum Status {
    OK = 0;
    // record not found if account not found
    RECORD_NOT_FOUND = 5;
    // System faced internal errors while processing the request
    INTERNAL = 13;
    // Incorrect UPI pin
    INVALID_SECURE_PIN = 100;
    // Secure pin validation retries exhausted
    PIN_RETRIES_EXCEEDED = 101;
    // business failures
    BUSINESS_FAILURE = 102;
  }

  // The balance available in the account for transacting
  // In a normal scenario, account balance and current balance are the same.
  //
  // But, sometimes banks tend to put certain chunk of money from an account
  // on hold and is not available for a normal transaction.
  // This can be a result of user initiated mandate or for any
  // upcoming standing instructions.
  // DEPRECATED - In favour of balance infos struct
  api.typesv2.Money available_balance = 1 [deprecated = true];

  // balance info to return, its repeated because we might need to show multiple balances to user
  repeated BalanceInfo balance_infos = 2;
}

message BalanceInfo {
  // title to be shown for balance e.g. available, used etc
  api.typesv2.common.Text title = 1;
  // amount to be shown
  api.typesv2.Money amount = 2;
}

message UpiAccountActionInfo {
  // upi account id
  string account_id = 1;
  // status of the action taken for upi account
  upi.onboarding.enums.UpiOnboardingStatus status = 2;
  // vpa created for upi account
  string vpa = 3;
}


message GetNPCICLParametersRequest {
  frontend.header.RequestHeader req = 15;
  // upi flow type specifies the type of flow for which we need the CL Parameters
  UPIFlowType upi_flow_type = 1;
}

enum UPIFlowType {
  UPI_FLOW_TYPE_UNSCPECIFIED = 0;
  UPI_FLOW_TYPE_BALANCE_ENQUIRY = 1;
}

message GetNPCICLParametersResponse {
  frontend.header.ResponseHeader resp_header = 15;
  // generated txn id
  string txn_id = 1;
}

message GetAccountsForSelfTransferRequest {
  frontend.header.RequestHeader req = 15;
}

message GetAccountsForSelfTransferResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // list of account info containing account details like account id, masked account number, bank details etc, from which actor can pay.
  repeated frontend.upi.onboarding.AccountInfo from_account_infos = 1;

  // list of account info containing account details like account id, masked account number, bank details etc, to which actor can pay.
  repeated frontend.upi.onboarding.AccountInfo to_account_infos = 2;

  // denotes the pi_ids corresponding to the TPAP account of the from account infos list.
  // the ordering of the returned PI IDs is the same as that of the from account infos list.
  repeated string from_tpap_account_pi_ids = 3;
}

message GetActionForEntryPointRequest {
  frontend.header.RequestHeader req = 15;
  // ui entry point from where rpc was called
  frontend.upi.onboarding.enums.UIEntryPoint ui_entry_point = 1;
}

message GetActionForEntryPointResponse {
  frontend.header.ResponseHeader resp_header = 15;
  // ui entry point from where rpc was called
  frontend.upi.onboarding.enums.Action action = 1;
}

message InitiateLinkUpiNumberRequest {
  frontend.header.RequestHeader req = 15;
  // vpa to which upi number to be linked
  string vpa = 1 [(validate.rules).string.min_len = 1];
  // upi number to link
  string upi_number = 2 [(validate.rules).string.min_len = 1];
  // upi number type e.g. phone number, numeric id
  frontend.upi.onboarding.enums.UpiNumberType upi_number_type = 3 [(validate.rules).enum = {not_in: [0]}];
  // upi number linking type e.g. new, port
  frontend.upi.onboarding.enums.UpiNumberLinkingType upi_number_linking_type = 4 [(validate.rules).enum = {not_in: [0]}];
  // client req id to poll of upi number linking status
  string client_req_id = 5 [(validate.rules).string.min_len = 1];
  // previous vpa from which upi number to be migrated
  // [OPTIONAL] - Only required in case of upi_number_linking_type PORT
  string pre_vpa = 8;
  // derived_account_id of the account
  string derived_account_id = 9 [(validate.rules).string.min_len = 1];
}

message InitiateLinkUpiNumberResponse {
  frontend.header.ResponseHeader resp_header = 15;
}

message GetUpiNumberActionStatusRequest {
  frontend.header.RequestHeader req = 15;

  // client req id to poll upi number action (link/delink) status
  string client_req_id = 1 [(validate.rules).string.min_len = 1];
}

message GetUpiNumberActionStatusResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // state of action taken for upi number
  upi.onboarding.enums.UpiOnboardingStatus upi_number_action_status = 2;

  // deeplink for the next action to be performed by the client.
  // E.g. For UPI number linking, this will be deep link to the success screen.
  frontend.deeplink.Deeplink next_action = 3;
}

message GetMapperInfoRequest {
  frontend.header.RequestHeader req = 15;

  // check the availability of the upiNumber
  string upiNumber = 1;

  // vpa corresponding to which the upiNumber availability request has been raised
  string vpa = 3;

  // type of upiNumber requested (NUMERIC_ID or PHONE_NUMBER)
  // In case of PHONE_NUMBER it should be the 10 digit phone number
  frontend.upi.onboarding.enums.UpiNumberType upi_number_type = 4;
}

message GetMapperInfoResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // type of info to be shown by the client based on the type of upiNumber (NUMERIC_ID or PHONE_NUMBER)
  oneof identifier {
    // When UpiNumberType is Numeric-ID OR upiNumberType is PhoneNumber && not linked to any other psp then
    // client will show UpiNumberAvailabilityInfo to the user
    frontend.upi.onboarding.UpiNumberAvailabilityInfo upi_number_availability_info = 1;

    // When UpiNumber is PhoneNumber and is linked to another psp then
    // client will show UpiNumberPortInfo (which asks user to port its UpiNumber) to the user
    frontend.upi.onboarding.UpiNumberPortInfo upi_number_port_info = 2;

    // When UpiNumber is PhoneNumber/numeric id and is linked to another VPA within same psp then
    // we will send UpiNumberSamePspPortInfo to the client
    // Client should call initiateLinkUpiNumber using prev_vpa and next_vpa from the UpiNumberSamePspPortInfo to port the number
    frontend.upi.onboarding.UpiNumberSamePspPortInfo upi_number_same_psp_port_info = 3;
  }

}

message GetUpiNumberDetailsRequest {
  frontend.header.RequestHeader req = 15;
  // account id for the actor whose upiNumbers need to be fetched
  string derived_account_id = 1 [(validate.rules).string.min_len = 1];
  // vpa to which upiNumbers are linked
  string vpa = 2 [(validate.rules).string.min_len = 1];
}

message GetUpiNumberDetailsResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // User can have at max 2 Numeric_ids
  // if the user already has set 2 numeric-ids, then this list should be nil and client should not show overflow options to the user
  repeated frontend.upi.onboarding.OverflowOption overflow_options = 1;

  // account details of the user to be shown on the manage-upi-numbers screen like vpa, bankName etc.
  frontend.upi.onboarding.AccountDetail account_detail = 2;

  // List of upiNumber along with different options for each of them
  repeated frontend.upi.onboarding.UpiNumberDetail upi_number_details = 3;

  // info to be shown at the end. E.g. (You can receive money from any UPI app through your UPI Number.)
  api.typesv2.ui.IconTextComponent faq_info = 4;
}

message InitiateDelinkUpiNumberRequest {
  frontend.header.RequestHeader req = 15;
  // vpa to which upi number to be delinked
  string vpa = 1 [(validate.rules).string.min_len = 1];
  // upi number to delink
  string upi_number = 2 [(validate.rules).string.min_len = 1];
  // client req id to poll of upi number delinking status
  string client_req_id = 3 [(validate.rules).string.min_len = 1];
  // derived_account_id of the account
  string derived_account_id = 5 [(validate.rules).string.min_len = 1];
  // upi number type e.g. phone number, numeric id
  frontend.upi.onboarding.enums.UpiNumberType upi_number_type = 6 [(validate.rules).enum = {not_in: [0]}];
}

message InitiateDelinkUpiNumberResponse {
  frontend.header.ResponseHeader resp_header = 15;
}

message DisableOrEnableUpiNumberRequest {
  frontend.header.RequestHeader req = 15;
  // upi number to be disabled/enabled
  string upi_number = 1;

  enum RequestType {
    REQUEST_TYPE_UNSPECIFIED = 0;
    // to enable the upi number
    ENABLE = 1;
    // to disable the upi number
    DISABLE = 2;
  }
  // toggle type denoting whether to disable or enable the VPA
  RequestType request_type = 2;
}

message DisableOrEnableUpiNumberResponse {
  enum Status {
    OK = 0;
    // invalid argument passed by the client
    INVALID_ARGUMENT = 3;
    // upi number does not exist
    RECORD_NOT_FOUND = 5;
    // user does not have access to the upi number
    PERMISSION_DENIED = 7;
    // system faced internal errors while processing the request
    INTERNAL = 13;
    // the upi number is already enabled/disabled
    ALREADY_PROCESSED = 50;
  }
  frontend.header.ResponseHeader resp_header = 15;
}

message ValidateAadhaarNoForUpiPinSetRequest {
  // derived_account_id stores base64 encoding for DerivedAccountId which contains different account ids
  // like tpap_account_id, connected_account id etc.
  string derived_account_id = 1 [(validate.rules).string.min_len = 1];

  // last 4 digits digits of Aadhaar number
  string truncated_aadhaar_no = 2 [(validate.rules).string.len = 4];

  frontend.header.RequestHeader req = 15;
}

message ValidateAadhaarNoForUpiPinSetResponse {
  enum Status {
    OK = 0;
    // invalid argument passed by the client
    INVALID_ARGUMENT = 3;
    // upi number does not exist
    RECORD_NOT_FOUND = 5;
    // system faced internal errors while processing the request
    INTERNAL = 13;
    // Aadhaar number validation failed
    AADHAAR_NUMBER_VALIDATION_FAILED = 16;
  }
  frontend.header.ResponseHeader resp_header = 15;
}

message GetUpiPinSetIntroScreenInfoRequest {
  frontend.header.RequestHeader req = 15;

  // derived_account_id stores base64 encoding for DerivedAccountId which contains different account ids
  // like tpap_account_id, connected_account id etc.
  string derived_account_id = 1 [(validate.rules).string.min_len = 1];

  // UpiPinSetFlowType - upi pin set flow is invoved in many cases (Set, Reset, Change Pin) .
  // - Intro screen will be decided based on the type of flow
  // - In favour of backend driven UI, backends needs to send info for intro screen, so backend needs this flow type from the client
  upi.onboarding.enums.UpiPinSetFlowType upi_pin_set_flow_type = 2;
}

message GetUpiPinSetIntroScreenInfoResponse {
  // Info to be shown on upi pin set intro screen
  upi.onboarding.UpiPinSetIntroScreenInfo upi_pin_set_intro_screen_info = 1;

  // Info to be shown after successful upi pin set
  upi.onboarding.UpiPinSetSuccessScreenInfo upi_pin_set_success_screen_info = 2;

  // default_option_type - client shall use if they don't get any options
  // E.g.
  // - In case when only debit card is the allowed option_type
  //   backend will not send any option. In that case client should
  //   proceed with the default option type flow.
  // - Client shall use this default option type for default selection
  frontend.upi.onboarding.enums.UpiPinSetOptionType default_option_type = 3;

  // partner_tag - partner tag to be shown as footer for the UPI pin set intro screen
  // Example: "Powered by Rupay Credit on UPI" in case of Rupay Credit Card
  // "UPI FEDERAL" in case of Savings Account/ Current Account etc.
  api.typesv2.common.VisualElement partner_tag = 4;

  frontend.header.ResponseHeader resp_header = 15;
}


message AuthoriseInternationalPaymentsActionRequest {
  // client req id for which the workflow was initiated
  // (for activation / deactivation of international payments)
  string client_req_id = 1 [(validate.rules).string.min_len = 1];

  // We will validate the cred block generated through NPCI common library.
  // Cred block is to be passed to NPCI in XML format
  // To prevent errors in data transformation and to keep business logic out of client,
  // frontend expects NPCI's cred block in a structured fashion
  repeated frontend.account.upi.CredBlock npci_cred_block = 2;

  // international payment action which needs to be authorised (activation / deactivation)
  frontend.upi.onboarding.enums.InternationalPaymentsAction international_payments_action = 3;

  // txn id to be used in vendor request for
  // activation / deactivation of international upi payments
  string transaction_id = 4 [(validate.rules).string.min_len = 1];

  frontend.header.RequestHeader req = 15;
}

message AuthoriseInternationalPaymentsActionResponse {
  frontend.header.ResponseHeader resp_header = 15;
}


message InitiateInternationalPaymentsActivationRequest {
  // client req id for which international payments activation should be initiated
  string client_req_id = 1;

  // derived account Id for which international payments needs to be activated
  string derived_account_id = 2;

  // start time of the international payments
  google.protobuf.Timestamp activationTime = 3;

  // expiry time of the international payments.
  // Time difference between start time and end time should be maximum 90 days.
  google.protobuf.Timestamp expiryTime = 4;

  frontend.header.RequestHeader req = 15;

}

message InitiateInternationalPaymentsActivationResponse {

  // Deeplink corresponding to the next action to be done post International payment action initiation
  frontend.deeplink.Deeplink next_action = 1;

  frontend.header.ResponseHeader resp_header = 15;
}
message InitiateInternationalPaymentsDeactivationRequest {
  // client req id for which workflow should be initiated
  string client_req_id = 1;

  // Account Id for which international payments needs to be activated
  string derived_account_id = 2;

  frontend.header.RequestHeader req = 15;

}

message InitiateInternationalPaymentsDeactivationResponse {
  frontend.header.ResponseHeader resp_header = 15;
}

message GetInternationalPaymentActionStatusRequest {
  frontend.header.RequestHeader req = 15;

  // client req id to poll international payment action (activate/deactivate) status
  string client_req_id = 1 [(validate.rules).string.min_len = 1];

  // international payment action which needs to be authorised (activation / deactivation)
  string derived_account_id = 2;
}

message GetInternationalPaymentActionStatusResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // state of action taken for International payments for upi account
  upi.onboarding.enums.UpiOnboardingStatus international_payment_action_status = 1;

  // screen to be shown based on the international payment request status response
  deeplink.Deeplink next_action = 2;
}

message UpdateDefaultMerchantPaymentPreferenceRequest {
  frontend.header.RequestHeader req = 15;
  // account id for the actor
  string derived_account_id = 1 [(validate.rules).string.min_len = 1];
  // action taken for account preference update
  frontend.upi.onboarding.enums.AccountPreferenceAction action = 2;
}

message UpdateDefaultMerchantPaymentPreferenceResponse {
  frontend.header.ResponseHeader resp_header = 15;
}

message GetUpiLiteInfoRequest {
  frontend.header.RequestHeader req = 15;

  // derived account id of upi account
  string derived_account_id = 1 [(validate.rules).string.min_len = 1];

  // upi lite account balance
  api.typesv2.Money upiLiteAccountBalance = 2;
}

message GetUpiLiteInfoResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // UpiLiteAddMoneyInfo - stores screen info required to activate
  // and add money to upi lite account
  frontend.upi.onboarding.UpiLiteAddMoneyInfo upi_lite_add_money_info = 1;

  // info required to call CreateFundTransferOrder in case of subsequent top up payment
  // requests (i.e. after first time top up)
  // NOTE - If it is first time top up, then this field will be nil.
  // client will get the required info for InitiatePayment from the workflow
  // polling rpc
  frontend.upi.onboarding.TopUpPaymentDetails top_up_payment_details = 2;

  // linking info for upi lite account
  // e.g. Money will be added from Fi Federal ****3747
  api.typesv2.ui.IconTextComponent footer_info = 3;

  // UpiLiteAccountStatus : status of upi lite account (Active / Inactive)
  frontend.upi.onboarding.enums.UpiLiteAccountStatus account_status = 4;

  // info regarding the balance (shows current upi
  // lite balance to the user)
  frontend.upi.onboarding.BalanceCardInfo balance_card_info = 5;

  // overflow options like Delete Upi Lite, Get Help etc.
  repeated frontend.upi.onboarding.OverflowOptionForUpiAction overflow_options = 6;

  // bg colour of the screen
  string screen_bg_colour = 7;

  // account ref number - required by CL for generating
  // creds for lrn and top up payments.
  string account_ref_number = 8;

  // lrn (Lite Reference Number) - If LRN / upi lite is active, then
  // lrn is required by client for any top up payment.
  string lrn = 9;
}


message InitiateUpiLiteActivationRequest {
  // client req id using which upi lite activation workflow should be triggered
  string client_req_id = 1 [(validate.rules).string.min_len = 1];

  // derived_account_id - account id for which upi lite needs to be activated
  string derived_account_id = 2 [(validate.rules).string.min_len = 1];

  // We will validate the cred block generated through NPCI common library.
  // Cred block is to be passed to NPCI in XML format
  // To prevent errors in data transformation and to keep business logic out of client,
  // frontend expects NPCI's cred block in a structured fashion
  repeated frontend.account.upi.CredBlock npci_cred_block = 3 [(validate.rules).repeated.min_items = 1];

  // initial_top_up_amount - amount that user wants to add as an initial top up
  api.typesv2.Money initial_top_up_amount = 4 [(validate.rules).message.required = true];

  frontend.header.RequestHeader req = 15;
}

message InitiateUpiLiteActivationResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // key_xml payload - client needs to share this xml payload
  // with CL, after successful activation
  // of LRN (Lite Reference Number)
  string key_xml_payload = 1;
}

message InitiateUpiLiteDeactivationRequest {
  // client req id using which upi lite deactivation workflow should be triggered
  string client_req_id = 1 [(validate.rules).string.min_len = 1];

  // derived_account_id - account id of the upi lite account which needs to be deactivated
  string derived_account_id = 2 [(validate.rules).string.min_len = 1];

  // balance of upi lite account
  api.typesv2.Money upi_lite_account_balance = 3 [(validate.rules).message.required = true];

  frontend.header.RequestHeader req = 15;
}

message InitiateUpiLiteDeactivationResponse {
  frontend.header.ResponseHeader resp_header = 15;
}

message CheckUpiLiteActionStatusRequest {
  frontend.header.RequestHeader req = 15;

  // client_req_id - fetches the status of the workflow
  // which was triggered using this client req id
  string client_req_id = 1 [(validate.rules).string.min_len = 1];
}

message CheckUpiLiteActionStatusResponse {
  frontend.header.ResponseHeader resp_header = 15;

  // based on the workflow status, client will need
  // to perform one of the following actions
  // (When workflow fails, nextAction will be nil,
  // client shall show the failure screen to the user)
  oneof NextAction {
    // client should call the InitiatePayment rpc in this case
    // to initiate topup (or withdrawal during deactivation) for the upi lite account
    frontend.upi.onboarding.InitiatePayment initiate_payment = 1;
    // client shall use SyncUpiLite rpc to get the latest status
    // of an LRN (Lite Reference Number) from vendor
    frontend.upi.onboarding.SyncUpiLite sync_upi_lite = 2;
    // client shall the decide the next timestamp to poll
    // workflow based on the specifications of PollWorkflow.
    frontend.upi.onboarding.PollWorkflow poll_workflow = 3;
  }

  // client shall show the terminal screen if it is not nil,
  // irrespective of anything and perform the nextAction in background
  frontend.deeplink.Deeplink upi_lite_action_terminal_screen = 4;
}


message SyncUpiLiteInfoRequest {
  frontend.header.RequestHeader req = 15;
  // derived account id for which lite data needs to be synced
  string derived_account_id = 1;
}

message SyncUpiLiteInfoResponse {
  frontend.header.ResponseHeader resp_header = 15;
  // final synced data
  string sync_data = 2;
}

message GetAccountsForUpiLiteActivationRequest {
  frontend.header.RequestHeader req = 15;
}

message GetAccountsForUpiLiteActivationResponse {
  frontend.header.ResponseHeader resp_header = 15;
  // list of account info containing account details like account id, masked account number etc.
  repeated frontend.upi.onboarding.AccountInfo account_infos = 1;
}

message GetConnectedAccountsForTpapLinkingRequest {
  frontend.header.RequestHeader req = 1;
}

message GetConnectedAccountsForTpapLinkingResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // contains all the accounts and design info for accounts
  // which can be potentially linked
  frontend.upi.onboarding.
    AccountsCard accounts_card = 2;
  api.typesv2.common.Text title = 3;
  string bg_color = 4;
  repeated api.typesv2.ui.IconTextComponent feature_info = 5;
  // flag for the client to decide whether user should be allowed
  // to select / deselect accounts.
  bool show_account_selection_option = 6;
  // info like : POWERED BY UPI BHIM
  api.typesv2.common.VisualElement partner_tag = 7;
  // Terms and conditions to be shown to user for taking consent
  api.typesv2.common.Text tnc_consent = 8;
}


message IsAccountActionAllowedRequest {
  frontend.header.RequestHeader req = 1;

  // derived account id for which the action needs to be validated.
  string derived_account_id = 2 [(validate.rules).string.min_len = 1];

  enum ActionType {
    ACTION_TYPE_UNSPECIFIED = 0;
    ACTION_TYPE_DELINK_UPI_ACCOUNT = 1;
    ACTION_TYPE_DISABLE_VPA = 2;
  }
  // action to be validated
  ActionType action_type = 3 [(validate.rules).enum = {not_in: [0]}];
}

message IsAccountActionAllowedResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // is requested action allowed or not
  bool is_allowed = 2;
  // action info required by the client depending on the action result (true / false)
  api.typesv2.pay.PreExecutionMessage action_info = 3;
}

message GetUpiSettingsDetailsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetUpiSettingsDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // E.g. UPI settings
  api.typesv2.common.Text title = 2;
  // screen bg color
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  // List of upi accounts
  repeated UpiAccountInfo upi_account_infos = 4;
  // Cta card occupying full width of the screen
  message CtaCard {
    // E.g. Connect more UPI accounts
    api.typesv2.ui.IconTextComponent cta_text = 1;
    // E.g. plus icon
    api.typesv2.common.VisualElement right_icon = 2;
    // E.g. deeplink to connect more UPI accounts
    frontend.deeplink.Deeplink deeplink = 3;
    //  Container properties for the CtaCard, to set bg color, corner radius etc.
    api.typesv2.ui.IconTextComponent.ContainerProperties container_properties = 4;
  }
  // Ctas to be shown after the list of upi accounts
  // E.g. `Connect more UPI accounts`
  repeated CtaCard cta_cards = 5;
  // E.g. `You can receive money from any UPI app through your UPI Number`
  // May contain deeplink to FAQ/Help etc.
  api.typesv2.ui.IconTextComponent footer_faq = 6;
  // info like: POWERED BY BHIM UPI
  api.typesv2.common.VisualElement partner_tag = 7;
}

message GetMapperQuickLinkScreenDetailsRequest {
  frontend.header.RequestHeader req = 1;
}

// https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=32620-6415&t=FH8m2hMMkWinJWip-1
message GetMapperQuickLinkScreenDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  // User specific mapper content to be shown here
  api.typesv2.ui.IconTextComponent content = 2;

  message LinkingMetadata {
    string upi_number = 1;
    string derived_account_id = 2;
    string vpa = 3;
    string previous_vpa = 4;
    frontend.upi.onboarding.enums.UpiNumberType upi_number_type = 5;
    frontend.upi.onboarding.enums.UpiNumberLinkingType upi_number_linking_type = 6;
    string client_request_id = 7;
  }

  // Details required to start linking number
  LinkingMetadata linking_metadata = 3;

  // Cta that will be shown as a backup in case we cannot show auto link
  frontend.deeplink.Cta default_cta = 4;

  message AutoLinkCta {
    // client should automatically start linking after receiving
    // the RPC response and waiting for auto_link_timer.
    // Additionally, the client should display an animation on the CTA to indicate that a timer is running before the automatic linking begins.
    int32 auto_link_timer = 1;
    // cta.text will contain $TIMER$, which the client should dynamically replace
    // with the remaining seconds from auto_link_timer as it counts down.
    // For example, "Linking in $TIMER$" should be updated to "Linking in 3s" and so on.
    frontend.deeplink.Cta cta = 2;
    // If true, tapping on CTA stops auto link and client falls back to default CTA
    bool should_stop_auto_link_on_cta_click = 3;
  }

  // Auto links the number after a certain time
  AutoLinkCta auto_link_cta = 5;

  // Background color for the screen
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 6;
  // Main image to be displayed on the screen
  api.typesv2.common.VisualElement image = 7;
  // e.g. Receive money instantly️ from any UPI app on Fi
  api.typesv2.common.Text title = 8;
  // Partner tag to be displayed(e.g. Powered by BHIM, NPCI etc.)
  api.typesv2.common.VisualElement partner_tag = 9;
  // checkbox to take consent from user for linking upi number. e.g. `I consent to linking my UPI number to Fi Money.`
  // If this field is present, `default_cta` should be enabled only if the checkbox is checked.
  api.typesv2.common.ui.widget.CheckboxItem consent_checkbox = 10;
}
