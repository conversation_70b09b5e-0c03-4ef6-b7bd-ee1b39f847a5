syntax = "proto3";

package frontend.upi.onboarding.enums;

option go_package = "github.com/epifi/gamma/api/frontend/upi/onboarding/enums";
option java_package = "com.github.epifi.gamma.api.frontend.upi.onboarding.enums";

// UpiAccountPreference specifies the preference of the account
enum UpiAccountPreference {
  ACCOUNT_PREFERENCE_UNSCPECIFIED = 0;
  // default upi account for paying/receiving in case of p2p payments
  ACCOUNT_PREFERENCE_PRIMARY = 1;
  // default upi account for paying to any merchant (only for credit accounts)
  ACCOUNT_PREFERENCE_DEFAULT_MERCHANT_PAYMENTS = 2;
}
