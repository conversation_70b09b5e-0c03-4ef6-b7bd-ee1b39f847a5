syntax = "proto3";

package frontend.upi.onboarding.enums;

option go_package = "github.com/epifi/gamma/api/frontend/upi/onboarding/enums";
option java_package = "com.github.epifi.gamma.api.frontend.upi.onboarding.enums";

// signifies the action to take for entrypoint
enum Action {
  // unspecified
  ACTION_UNSPECIFIED = 0;
  ACTION_TPAP = 1;
  ACTION_CONNECTED_ACCOUNTS = 2;
}

enum UpiAccountType {
  // unspecified
  UPI_ACCOUNT_TYPE_UNSPECIFIED = 0;
  // bank account e.g. savings, current etc
  UPI_ACCOUNT_TYPE_BANK_ACCOUNT = 1;
  // credit card
  UPI_ACCOUNT_TYPE_CREDIT_CARD = 2;
  // upi lite
  UPI_ACCOUNT_TYPE_UPI_LITE = 3;
}

enum AccountPreferenceAction {
  ACCOUNT_PREFERENCE_ACTION_UNSPECIFIED = 0;
  ACCOUNT_PREFERENCE_ACTION_ENABLE = 1;
  ACCOUNT_PREFERENCE_ACTION_DISABLE = 2;
}

// OverflowOptionActionType - all the overflow options for upi
// should be added in this enum
enum OverflowOptionActionType {
  OVERFLOW_OPTION_ACTION_TYPE_UNSPECIFIED = 0;
  OVERFLOW_OPTION_ACTION_TYPE_DELETE_UPI_LITE = 1;
}
