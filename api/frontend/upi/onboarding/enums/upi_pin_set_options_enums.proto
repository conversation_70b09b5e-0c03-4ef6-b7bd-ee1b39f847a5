syntax = "proto3";

package frontend.upi.onboarding.enums;

option go_package = "github.com/epifi/gamma/api/frontend/upi/onboarding/enums";
option java_package = "com.github.epifi.gamma.api.frontend.upi.onboarding.enums";

// UpiPinSetFlowType - upi pin set flow is invoved in many cases (Set, Reset, Change Pin) .
// - Intro screen will be decided based on the type of flow
// - In favour of backend driven UI, backend needs to send info for intro screen, so client needs to share the flow type while calling Fe
enum UpiPinSetFlowType {
  UPI_PIN_SET_FLOW_TYPE_UNSPECIFIED = 0;
  UPI_PIN_SET_FLOW_TYPE_SET_PIN = 1;
  UPI_PIN_SET_FLOW_TYPE_RESET_PIN = 2;
  UPI_PIN_SET_FLOW_TYPE_CHANGE_PIN = 3;
}


// UpiPinSetOptionType - represents the options using which user can set/reset pin
// required by the client to recognize the available options
enum UpiPinSetOptionType {
  UPI_PIN_SET_OPTION_TYPE_UNSPECIFIED = 0;
  UPI_PIN_SET_OPTION_TYPE_DEBIT_CARD = 1;
  UPI_PIN_SET_OPTION_TYPE_AADHAAR_NUMBER = 2;
  UPI_PIN_SET_OPTION_TYPE_RUPAY_CREDIT_CARD = 3;
}
