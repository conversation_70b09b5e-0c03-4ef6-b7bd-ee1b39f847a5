syntax = "proto3";

package frontend.smsscanner;

option go_package = "github.com/epifi/gamma/api/frontend/smsscanner";
option java_package = "com.github.epifi.gamma.api.frontend.smsscanner";

/*
  Used for defining characteristics for scanning user's sms for analytical and marketing campaign
  Refer: https://docs.google.com/document/d/1OT_Yp4bt5qyRXAcIVdgkNcmmgHDKeqAIBRrmLU_0Jys/

  Currently, we are sending this via 'FetchConfig' rpc if usecase evolves or we see any need,
  we can use this same proto in a new frontend service
 */
message SMSScannerConfig {
  // Map of merchant name to corresponding sender ids
  // Key should always correspond to a value present in 'SmsScannerMerchantName'
  // Clients should not rely on the enum and use the same as a string only
  map<string, MerchantSenderIdList> merchant_sender_id_map = 1;
  // Default threshold after which client should mark that event needs to be sent for the merchant
  uint32 default_merchant_threshold = 2;
  // Map of merchant name to threshold for marking it for sending event
  // This is essentially to override 'default_merchant_threshold' if needed for a particular merchant
  map<string, uint32> merchant_threshold_map = 3;
  // No of days for which client should scan the SMS history of the user in a single phase
  // We dont wish to scan the entire sms history, this would determine last x days to scan in a single phase
  uint32 days_to_scan_in_one_phase = 4;
  // No of total days of sms the client would scan
  // Client would use 'days_to_scan_in_one_phase' to scan upto 'total_days_to_scan'
  uint32 total_days_to_scan = 5;
  // Once scan is completed and events are sent, client can store the timestamp for the same
  // If the last scan happened more than 'refresh_interval_days' then client should rerun the scan
  // and send events again
  uint32 refresh_interval_days = 6;
  // Flag to determine if scanning should be done or not.
  bool is_scan_enabled = 7;
  // Flag to enable sms scan only when device is charging.
  bool is_scan_on_charging_enabled = 8;
  SmsTemplatesConfig sms_templates_config = 9;
}

// Configuration to enable scanning users' received SMSs and matching them against pre-defined
// templates to extract useful information.
// Doc: https://docs.google.com/document/d/1O3m9iajEmz_DZT8-fKQWdOkJrF8yIbHDplxVx1KIbnY/edit
message SmsTemplatesConfig {
  // Whether the template parsing flow should be enabled on clients
  bool enable_parser = 1 [json_name = "enable_parser"];
  // A list of sms template configurations, per sms handle/vendor identifier.
  repeated TemplateConfig template_configs = 2 [json_name = "template_configs"];
  // Defines how many previous days to scan from current time
  int32 days_to_scan = 3 [json_name = "days_to_scan"];
  // A set of configs for background workers which get invoked on client apps
  WorkerConfig worker_config = 4 [json_name = "worker_config"];
  // A config for a single SMS vendor/handle
  message TemplateConfig {
    // The string to identify the SMS handle/vendor (This is usually the SMS title/receiver name)
    string sms_handle = 1 [json_name = "sms_handle"];
    // A list of templates to match against the SMS body
    repeated Template templates = 2 [json_name = "templates"];
    // An identifier to identify the operator, e.g. Airtel, Jio
    string operator_id = 3 [json_name = "operator_id"];
    // A single regex template to match against the SMS body
    message Template {
      // An identifier to identify the template
      string id = 1 [json_name = "id"];
      // A regex pattern usd by clients to match against the SMS body
      string regex = 2 [json_name = "regex"];
    }
  }
  // Defines a set of configs for background workers which get invoked on client apps
  message WorkerConfig {
    // Defines that the SMS template scan worker on clients will run after every [worker_scan_interval_in_days]
    // days
    int32 worker_scan_interval_in_days = 1 [json_name = "worker_scan_interval_in_days"];
    // Defines whether the SMS template scan should only run when the device is connected to charging
    bool is_scan_on_charging_enabled = 2 [json_name = "is_scan_on_charging_enabled"];
  }
}

// List of sender ids which could be present in the sms
// Used for mapping a particular sms to a merchant
// Eg: 'GROWWO' sender id can be used to map sms to 'Groww' merchant
message MerchantSenderIdList {
  repeated string sender_ids = 1;
}

// Enum for possible merchants
// Client should not be using this enum unless some very specific handling is needed for a specific merchant
// Idea is client should be able to consume new 'merchants' with only backend changes
// Backend should always convert this to string and send to client for any usage
enum SmsScannerMerchantName {
  SMS_SCANNER_MERCHANT_NAME_UNSPECIFIED = 0;
  // SMSSMN is short for SMS_SCANNER_MERCHANT_NAME
  SMSSMN_INDMONEY = 1;
  SMSSMN_GROWW = 2;
  SMSSMN_VESTED = 3;
  SMSSMN_ET_MONEY = 4;
  SMSSMN_PAYTM = 5;
  SMSSMN_ZERODHA = 6;
  SMSSMN_ICICI_BANK = 7;
  SMSSMN_HDFC_BANK = 8;
  SMSSMN_AXIS_BANK = 9;
  SMSSMN_SBI_BANK = 10;
  SMSSMN_KOTAK_MAHINDRA_BANK = 11;
  SMSSMN_BOB_BANK = 12;
  SMSSMN_PNB_BANK = 13;
  SMSSMN_YES_BANK = 14;
  SMSSMN_BOI_BANK = 15;
  SMSSMN_INDUSIND_BANK = 16;
  SMSSMN_CANARA_BANK = 17;
  SMSSMN_INDIAN_BANK = 18;
  SMSSMN_CENTRAL_BANK = 19;
  SMSSMN_INDIAN_OVERSEAS_BANK = 20;
  SMSSMN_IDBI_BANK = 21;
  SMSSMN_UCO_BANK = 22;
  SMSSMN_MAHARASHTRA_BANK = 23;
  SMSSMN_NSE = 24;
  SMSSMN_BSE = 25;
  SMSSMN_CDSL = 26;
  SMSSMN_INFOSYS = 27;
  SMSSMN_SMALLCASE = 28;
  SMSSMN_MF_AMC_GROWW = 29;
  SMSSMN_MF_AMC_BARODA_BNP = 30;
  SMSSMN_MF_AMC_CANARA_ROBECO = 31;
  SMSSMN_MF_AMC_SBI = 32;
  SMSSMN_MF_AMC_UNION = 33;
  SMSSMN_MF_AMC_BANK_OF_INDIA = 34;
  SMSSMN_MF_AMC_LIC = 35;
  SMSSMN_MF_AMC_360_ONE = 36;
  SMSSMN_MF_AMC_BAJAJ_FINSERV = 37;
  SMSSMN_MF_AMC_BANDHAN = 38;
  SMSSMN_MF_AMC_UTI = 39;
  SMSSMN_MF_AMC_EDELWEISS = 40;
  SMSSMN_MF_AMC_ITI = 41;
  SMSSMN_MF_AMC_JM_FINANCIAL = 42;
  SMSSMN_MF_AMC_KOTAK = 43;
  SMSSMN_MF_PLATFORM_GROWW = 44;
  SMSSMN_MF_AMC_ADITYA_BIRLA = 45;
  SMSSMN_MF_AMC_AXIS = 46;
  SMSSMN_MF_AMC_DSP = 47;
  SMSSMN_MF_AMC_FRANKLIN = 48;
  SMSSMN_MF_AMC_HDFC = 49;
  SMSSMN_MF_AMC_HELIOS = 50;
  SMSSMN_MF_AMC_HSBC = 51;
  SMSSMN_MF_AMC_ICICI_PRU = 52;
  SMSSMN_MF_AMC_INVESCO = 53;
  SMSSMN_MF_AMC_MAHINDRA = 54;
  SMSSMN_MF_AMC_MIRAE = 55;
  SMSSMN_MF_AMC_MOTILAL = 56;
  SMSSMN_MF_AMC_NAVI = 57;
  SMSSMN_MF_AMC_NJ_ASSET = 58;
  SMSSMN_MF_AMC_OLD_BRIDGE = 59;
  SMSSMN_MF_AMC_PGIM = 60;
  SMSSMN_MF_AMC_PPFAS = 61;
  SMSSMN_MF_AMC_QUANT = 62;
  SMSSMN_MF_AMC_QUANTUM = 63;
  SMSSMN_MF_AMC_SAMCO = 64;
  SMSSMN_MF_AMC_SHRIRAM = 65;
  SMSSMN_MF_AMC_SUNDARAM = 66;
  SMSSMN_MF_AMC_TATA = 67;
  SMSSMN_MF_AMC_TAURUS = 68;
  SMSSMN_MF_AMC_TRUST = 69;
  SMSSMN_MF_AMC_WHITEOAK = 70;
  SMSSMN_MF_AMC_ZERODHA = 71;
  SMSSMN_MF_RTA_CAMS = 72;
  SMSSMN_MF_RTA_KFIN = 73;
  SMSSMN_MF_AMC_PLATFORM_PAYTM = 74;
  SMSSMN_MF_AMC_NIPPON = 75;
  SMSSMN_LOANS_BAJAJ_FINSERV = 76;
  SMSSMN_LOANS_FINNABLE = 77;
  SMSSMN_LOANS_HERO_FIRCORP = 78;
  SMSSMN_LOANS_INCRED = 79;
  SMSSMN_LOANS_INDUSLAND_BANK = 80;
  SMSSMN_LOANS_LOANTAP = 81;
  SMSSMN_LOANS_PAYSENSE = 82;
  SMSSMN_LOANS_POCKETLY = 83;
  SMSSMN_LOANS_PAYU = 84;
  SMSSMN_LOANS_SMARTCOIN = 85;
  SMSSMN_LOANS_TATA_CAPITAL = 86;
  SMSSMN_LOANS_NAVI = 87;
  SMSSMN_LOANS_ABLE_SOFTECH = 88;
  SMSSMN_LOANS_AK_4LOANS = 89;
  SMSSMN_LOANS_ALOANS = 90;
  SMSSMN_LOANS_ARMY_CENTRAL = 91;
  SMSSMN_LOANS_ARTHIMPACT_DIGITAL = 92;
  SMSSMN_LOANS_BLR_LOAN_BOSS = 93;
  SMSSMN_LOANS_BANKS_LOAN = 94;
  SMSSMN_LOANS_BFI_LOANS = 95;
  SMSSMN_LOANS_BIG_LOANS = 96;
  SMSSMN_LOANS_BUDGET_LOANS = 97;
  SMSSMN_LOANS_CAPITAL_INDIA_HOME = 98;
  SMSSMN_LOANS_CENTURY_VEHICLELOANS = 99;
  SMSSMN_LOANS_CLICK_4LOANS = 100;
  SMSSMN_LOANS_CRM_LOAN_MONEY = 101;
  SMSSMN_LOANS_DEV_JARILOAN = 102;
  SMSSMN_LOANS_DMT_LOANS = 103;
  SMSSMN_LOANS_DREAM_LOANS = 104;
  SMSSMN_LOANS_EASILOAN_TECHNO = 105;
  SMSSMN_LOANS_EASY_LIFE = 106;
  SMSSMN_LOANS_EASY_LOAN_MANTRA = 107;
  SMSSMN_LOANS_FACELESS_LOANS = 108;
  SMSSMN_LOANS_FAST_LOANS = 109;
  SMSSMN_LOANS_FASTWAYS_LOANS = 110;
  SMSSMN_LOANS_FIN_SHARE_LOANZ = 111;
  SMSSMN_LOANS_FINANZIO_LOANS = 112;
  SMSSMN_LOANS_FUTURE_LOANS = 113;
  SMSSMN_LOANS_GK_LOANS = 114;
  SMSSMN_LOANS_GENERAL_KURIES = 115;
  SMSSMN_LOANS_HAILP_LOAN_SERVICES = 116;
  SMSSMN_LOANS_HOME_LOAN_HUB = 117;
  SMSSMN_LOANS_INDIA_HOME_LOAN = 118;
  SMSSMN_LOANS_INDIA_LOANS_4U = 119;
  SMSSMN_LOANS_INSTANT_LOAN_SERVICES = 120;
  SMSSMN_LOANS_ITI_GOLD_LOAN_LIMITED = 121;
  SMSSMN_LOANS_JM_FINANCIAL_HOME_LOANS = 122;
  SMSSMN_LOANS_JMD_EDUCATIONAL_LOANS = 123;
  SMSSMN_LOANS_JOY_LOAN = 124;
  SMSSMN_LOANS_KBS_CREDITCARD = 125;
  SMSSMN_LOANS_KESHAV_ENTERPRISE = 126;
  SMSSMN_LOANS_KOONANS_GOLD_LOAN = 127;
  SMSSMN_LOANS_KPM_LOANS = 128;
  SMSSMN_LOANS_KWIK_LOANS = 129;
  SMSSMN_LOANS_LITTLE_FLOWER = 130;
  SMSSMN_LOANS_LM_LOANMANI = 131;
  SMSSMN_LOANS_LOAN_2_WEALTH_FINTECH = 132;
  SMSSMN_LOANS_LOAN_4_CARE = 133;
  SMSSMN_LOANS_LOAN_AND_CAR = 134;
  SMSSMN_LOANS_LOAN_AT_CLICK = 135;
  SMSSMN_LOANS_LOAN_BAZAAR = 136;
  SMSSMN_LOANS_LOAN_CHACHA = 137;
  SMSSMN_LOANS_LOAN_CLUB = 138;
  SMSSMN_LOANS_LOAN_DARBAR = 139;
  SMSSMN_LOANS_LOAN_DESK = 140;
  SMSSMN_LOANS_LOAN_GENIE = 141;
  SMSSMN_LOANS_LOAN_HUB = 142;
  SMSSMN_LOANS_LOAN_INTOUCH = 143;
  SMSSMN_LOANS_LOAN_JUNCTION = 144;
  SMSSMN_LOANS_LOAN_KART = 145;
  SMSSMN_LOANS_LOAN_MARKET = 146;
  SMSSMN_LOANS_LOAN_MYSTERY = 147;
  SMSSMN_LOANS_LOAN_NETWORK_TECH = 148;
  SMSSMN_LOANS_LOAN_POINT = 149;
  SMSSMN_LOANS_LOAN_SERVICE = 150;
  SMSSMN_LOANS_LOAN_STUDIO = 151;
  SMSSMN_LOANS_LOAN_VENTURES = 152;
  SMSSMN_LOANS_LOAN_WINDOW = 153;
  SMSSMN_LOANS_LOANAKA = 154;
  SMSSMN_LOANS_LOANBAZAAR_FIN_SERV = 155;
  SMSSMN_LOANS_LOANBRIX = 156;
  SMSSMN_LOANS_LOAN_CLOUD = 157;
  SMSSMN_LOANS_LOAN_GAADI = 158;
  SMSSMN_LOANS_LOANIY_FINTECH = 159;
  SMSSMN_LOANS_LOANKART = 160;
  SMSSMN_LOANS_LOANMANDI_SOLUTIONS = 161;
  SMSSMN_LOANS_LOAN_ADVISORY = 162;
  SMSSMN_LOANS_LOANME_NIDHI = 163;
  SMSSMN_LOANS_LOANNAKA = 164;
  SMSSMN_LOANS_LOANS_AND_INSURANCE_WALA = 165;
  SMSSMN_LOANS_LOANS_MART = 166;
  SMSSMN_LOANS_LOANSAATH_FINANCIAL = 167;
  SMSSMN_LOANS_LOAN_SANTHE = 168;
  SMSSMN_LOANS_LOANSCAPE = 169;
  SMSSMN_LOANS_LOANSEVAZONE_INDIA_NIDHI = 170;
  SMSSMN_LOANS_LOANSI_CONSULTANTS = 171;
  SMSSMN_LOANS_LOANS_LAN = 172;
  SMSSMN_LOANS_LOANS_MITRA = 173;
  SMSSMN_LOANS_LOANS_MUDRA = 174;
  SMSSMN_LOANS_LOANS_PRIDE_MULTICHANNEL = 175;
  SMSSMN_LOANS_LOANTAP_FINANCIAL = 176;
  SMSSMN_LOANS_LOANWIRED_FINTECH = 177;
  SMSSMN_LOANS_LOANWISE_FINANCIAL = 178;
  SMSSMN_LOANS_LOANZEN_FINANCE = 179;
  SMSSMN_LOANS_LORD_KRISHNA_FINANCIAL = 180;
  SMSSMN_LOANS_MS_JAIPUR_LOANMART = 181;
  SMSSMN_LOANS_MS_LOAN_BAZAR = 182;
  SMSSMN_LOANS_MS_LOAN_SUVIDHA = 183;
  SMSSMN_LOANS_M3_INSTANT_CASH_LOAN = 184;
  SMSSMN_LOANS_MALIEKAL_GOLD_LOAN = 185;
  SMSSMN_LOANS_MEDI_LOAN = 186;
  SMSSMN_LOANS_MEGHA_FINLOAN = 187;
  SMSSMN_LOANS_MENTOR_HOME_LOANS = 188;
  SMSSMN_LOANS_MODRA_LOAN = 189;
  SMSSMN_LOANS_MY_EASIEST_QUICK_LOAN = 190;
  SMSSMN_LOANS_MY_LOAN_MY_DEAL = 191;
  SMSSMN_LOANS_MY24_LOAN = 192;
  SMSSMN_LOANS_MYLOANBAZAR_DOT_COM = 193;
  SMSSMN_LOANS_MYLOANCARE_VENTURES = 194;
  SMSSMN_LOANS_NATIONAL_GOLD_LOAN_BANK = 195;
  SMSSMN_LOANS_NEED_4_LOAN = 196;
  SMSSMN_LOANS_NATIONAL_CHITS = 197;
  SMSSMN_LOANS_NOW_OF_LOAN = 198;
  SMSSMN_LOANS_OLICKAL_GOLD_LOAN = 199;
  SMSSMN_LOANS_ONLINE_PSB = 200;
  SMSSMN_LOANS_PARLOAN_MUTUAL_BENEFIT_NIDHI = 201;
  SMSSMN_LOANS_PHFL_HOME_LOANS = 202;
  SMSSMN_LOANS_PRO_LOANS = 203;
  SMSSMN_LOANS_PROLOANS_FINCORP = 204;
  SMSSMN_LOANS_PST_GOLD_LOAN_FINANCE = 205;
  SMSSMN_LOANS_QUICK_LOAN_FINANCE = 206;
  SMSSMN_LOANS_QUICKLY_GOLD_LOANS = 207;
  SMSSMN_LOANS_QUICK_WITHDRAW_LOANS = 208;
  SMSSMN_LOANS_REACH_4_LOANS = 209;
  SMSSMN_LOANS_REFER_LOAN_PRIVATE_LIMITED = 210;
  SMSSMN_LOANS_RIGHTWAY_LOANS = 211;
  SMSSMN_LOANS_RULOANS_DISTRIBUTION = 212;
  SMSSMN_LOANS_SAI_LOAN_ONLINE = 213;
  SMSSMN_LOANS_SARALOAN_TECHNOLOGIES = 214;
  SMSSMN_LOANS_SATHYADEEPAM_KURIES = 215;
  SMSSMN_LOANS_SATYA_LOANS = 216;
  SMSSMN_LOANS_SECONDARY_LOAN_MARKET = 217;
  SMSSMN_LOANS_SHARE_LOAN_SERVICES = 218;
  SMSSMN_LOANS_SPEED_HOMES = 219;
  SMSSMN_LOANS_SRI_MURUGAN_FINANCIAL = 220;
  SMSSMN_LOANS_SUPREME_ELOANS = 221;
  SMSSMN_LOANS_SWITCH_MY_LOAN_PRIVATE_LIMITED = 222;
  SMSSMN_LOANS_TAPLOAN = 223;
  SMSSMN_LOANS_TATKAL_LOAN = 224;
  SMSSMN_LOANS_THE_LOAN_SERVICES = 225;
  SMSSMN_LOANS_TRY_MY_LOAN = 226;
  SMSSMN_LOANS_VIDHYUT_PRASARAN = 227;
  SMSSMN_LOANS_VIRAL_LOAN = 228;
  SMSSMN_LOANS_WAY_2_LOANS = 229;
  SMSSMN_LOANS_WAY_2_BANK_LOAN = 230;
  SMSSMN_LOANS_YES_LOANS = 231;
  SMSSMN_LOANS_YOGAKSHEMAM_LOANS = 232;
  SMSSMN_LOANS_TRANSUNION_CIBIL = 233;
  SMSSMN_LOANS_EXPERIAN_CREDIT = 234;
  SMSSMN_LOANS_CRIF = 235;
  SMSSMN_REWARDS_GIFT_CARD_AMEX = 236;
  SMSSMN_REWARDS_GIFT_CARD_GYTRR = 237;
  SMSSMN_PF = 238;
}

