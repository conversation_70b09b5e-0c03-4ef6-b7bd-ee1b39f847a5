syntax = "proto3";

package employment;

import "api/frontend/account/screening/service.proto";
import "api/vendorgateway/employment/service.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/employment";
option java_package = "com.github.epifi.gamma.api.employment";


message EmploymentData {
  // unique identifier for an Employment data entry
  string id = 1;
  // actor_id of the user
  string actor_id = 2;
  // employment type of the user. eg. SALARIED, SELF_EMPLOYED....
  EmploymentType employment_type = 3;
  // this will contain the information user has provided about their employment.
  // for SALARIED users, this will be empty.
  EmploymentInfo employment_info = 4;
  // Outcome of the Employment Verification process
  EmploymentVerificationResult verification_result = 5;
  // Status of the Employment verification process
  EmploymentVerificationProcessStatus verification_process_status = 6;
  // reason for rejecting the user during verification process
  RejectionReason rejection_reason = 7;
  // reason for keeping the user in HOLD status
  HoldReason hold_reason = 8;
  // reason for keeping the user in ACCEPTED status
  AcceptedReason accepted_reason = 13;
  // employee name search response received from karza
  vendorgateway.employment.EmployeeNameSearchResponse org_pf_data = 12;
  // denotes the intent behind subjecting user to employment processing
  ProcessingIntent processing_intent = 14;
  // employer_id is the foreign key to employer table which contains details
  // for a particular employer
  string employer_id = 15;
  // updated_by_source is the source from where update employment call is made
  UpdateSource updated_by_source = 16;
  // updated_by_source_identifier gives finer details of the caller of employment update.
  // It is the sherlock agents email id in case the updated_by_source is UPDATE_SOURCE_SALARY_PROGRAM_DATA_OPS
  string updated_by_source_identifier = 17;
  // occupation_type is collected from the user at the time of onboarding.
  OccupationType occupation_type = 18;
  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp deleted_at = 11;
}

// possible employment types of a user
enum EmploymentType {
  EMPLOYMENT_TYPE_UNSPECIFIED = 0;
  SALARIED = 1;
  SELF_EMPLOYED = 2;
  RETIRED = 3;
  OTHERS = 4;
  BUSINESS_OWNER = 5;
  FREELANCER = 6;
  WORKING_PROFESSIONAL = 7;
  STUDENT = 8;
  HOMEMAKER = 9;
  SELF_EMPLOYED_PROFESSIONAL = 10;
  POLITICIAN_OR_STATESMAN = 11;
}

message EmploymentInfo {
  // company name selected/entered by salaried users
  string company_name = 1;
  // linkedin profile, personal website, company website etc.
  string personal_profile_info = 2;
  // if company is registered with EPFO
  bool is_epf_registered = 3;
  // was company name selected by the user from the suggested names, or was it manually entered by the user
  bool is_company_name_manual_input = 4;
  // vendor id for the company
  string vendor_id = 5;
  // text entered by the user before selecting a name from the suggestion dropdown
  string entered_text = 6;
  // gstin_no if provided by the user
  string gstin_no = 7;
  // enrollment no - For Working professionals Doctor/Lawyers etc.
  string enrollment_no = 8;
  // If they are a student, their graduation year is taken
  string student_graduation_year = 9;
  // If they are a student, their mail id is taken
  string student_mail_id = 10;
  // salary range struct contains annual absolute salary and salary range both
  frontend.account.screening.AnnualSalary annual_salary = 11;
  // risk ops action in case of income and occupation discrepancy
  IncOccDiscrepancyAction inc_occ_discrepancy_action = 12;
}

enum EmploymentVerificationResult {
  EMPLOYMENT_VERIFICATION_RESULT_UNSPECIFIED = 0;
  // User was accepted after checking their EPF data.
  EMPLOYMENT_VERIFICATION_RESULT_ACCEPTED = 2;
  // Employment verification wasn't done for these users. Users are assigned this state if they
  // choose profession type other than SALARIED or if their company is not registered with EPFO.
  EMPLOYMENT_VERIFICATION_RESULT_HOLD = 3;
  // User was rejected after checking their EPF data.
  EMPLOYMENT_VERIFICATION_RESULT_REJECTED = 4;
}

enum EmploymentVerificationProcessStatus {
  EMPLOYMENT_VERIFICATION_PROCESS_STATUS_UNSPECIFIED = 0;
  // Employment verification consumer has started processing the request
  EMPLOYMENT_VERIFICATION_PROCESS_STATUS_INITIATED = 1;
  // verification process is completed
  EMPLOYMENT_VERIFICATION_PROCESS_COMPLETED = 2;
  // exhausted all retried but were not able to successfully verify Employment Info of the user.
  EMPLOYMENT_VERIFICATION_PROCESS_FAILED = 3 [deprecated = true];
  // for cases where we don't have a way to verify employment info given by the user.
  EMPLOYMENT_VERIFICATION_PROCESS_SKIPPED = 4;
  // exhausted all retried but were not able to successfully verify Employment Info of the user.
  EMPLOYMENT_VERIFICATION_PROCESS_MANUAL_INTERVENTION = 5;
}

enum RejectionReason {
  REJECTION_REASON_UNSPECIFIED = 0;
  // karza id for company is not present in employment info.
  VENDOR_ID_NOT_PRESENT = 1;
  // we received invalid argument error from karza for company karza id and
  // employee name this might happen when we send invalid karza id in request.
  VENDOR_INVALID_ARGUMENT_ERROR = 2;
  // no EPF record was found for the requested karzaId and employee name
  // combination sent in request.
  VENDOR_NO_RECORD_FOUND = 3;
  // Name match not found for user in the epf data received from vendor
  EPF_DATA_NAME_MATCH_NOT_FOUND = 4;
  // no deposit from employer in user's PF account in last 3 months
  EPF_DATA_PF_NOT_RECEIVED = 5;
  // for cases when no handler is mapped to the check
  VERIFICATION_CHECK_HANDLER_NOT_FOUND = 6;
  // failed to verify user's employment status from epf records
  EPF_DATA_VERIFICATION_FAILED = 7;
  // send otp attempts exhausted
  SENT_OTP_ATTEMPTS_EXHAUSTED = 8;
  // verify otp attempts exhausted
  VERIFY_OTP_ATTEMPTS_EXHAUSTED = 9;
  // user email and company email domain does not match
  INVALID_EMAIL_DOMAIN = 10;
  // email not found due to CIN not found at vendor
  COMPANY_EMAIL_NOT_FOUND = 11;
  // denotes vendor api attempts exhaustion
  VENDOR_API_ATTEMPTS_EXHAUSTED = 12;
  // company not PF registered
  COMPANY_NOT_PF_REGISTERED = 13;
  // Domain name check disabled
  DOMAIN_NAME_CHECK_DISABLED = 14;
}

enum HoldReason {
  HOLD_REASON_UNSPECIFIED = 0;
  // This will be used for profession types that we can't verify ex. SELF_EMPLOYED, RETIRED, OTHERS
  PROFESSION_TYPE_UNVERIFIABLE = 1;
  // company name was manually added by user.
  COMPANY_NAME_MANUAL_INPUT = 2;
  // user's company is not epf registered
  COMPANY_NOT_EPFO_REGISTERED = 3;
}

enum EmploymentDataFieldMask {
  EMPLOYMENT_DATA_FIELD_MASK_UNSPECIFIED = 0;
  ID = 1;
  ACTOR_ID = 2;
  EMPLOYMENT_TYPE = 3;
  EMPLOYMENT_INFO = 4;
  VERIFICATION_RESULT = 5;
  VERIFICATION_PROCESS_STATUS = 6;
  REJECTION_REASON = 7;
  HOLD_REASON = 8;
  CREATED_AT = 9;
  UPDATED_AT = 10;
  DELETED_AT = 11;
  ORG_PF_DATA = 12;
  ACCEPTED_REASON = 13;
  PROCESSING_INTENT = 14;
  EMPLOYER_ID = 15;
  UPDATED_BY_SOURCE = 16;
  UPDATED_BY_SOURCE_IDENTIFIER = 17;
  EMPLOYMENT_DATA_FIELD_MASK_OCCUPATION_TYPE = 18;
}

enum AcceptedReason {
  ACCEPTED_REASON_UNSPECIFIED = 0;
  // we were able to successfully verify epf data
  ACCEPTED_REASON_EPF_DATA_VERIFIED = 1;
  // we were able to successfully verify domain name in personal profile link
  ACCEPTED_REASON_DOMAIN_NAME_VERIFIED = 2;
  // company selected by the user is present in MCA DB but is not registered with epfo
  ACCEPTED_REASON_PASS_NON_EPFO_REG_COMPANY = 3;
}


// denotes the intent behind subjecting user to employment processing
enum ProcessingIntent {
  UNSPECIFIED = 0;
  // case where user is subjected to employment declaration ONLY
  DECLARATION_ONLY = 1;
  // default case
  VERIFICATION = 2;
}

// UpdateSource is the source from where update employment call is made
enum UpdateSource {
  UPDATE_SOURCE_UNSPECIFIED = 0;
  // user's employer is updated during salary program onboarding flow
  UPDATE_SOURCE_SALARY_PROGRAM_INAPP = 1;
  // user's employer is updated by sherlock agent via salary program data ops
  UPDATE_SOURCE_SALARY_PROGRAM_DATA_OPS = 2;
  // user's employer is updated from user profile section in app
  UPDATE_SOURCE_USER_PROFILE = 3;
  // user's employer is updated during vkyc
  UPDATE_SOURCE_VKYC = 4;
  // user's employer is updated during onboarding
  UPDATE_SOURCE_ONBOARDING = 5;
  // user's occupation and salary via sherlock agent after document verification
  UPDATE_SOURCE_DEV_ACTION = 6;
  // user provides employer in employer selection screen in screener
  UPDATE_SOURCE_EPFO_SCREEN = 7;
  // user update income occupation from discrepancy prompt
  UPDATE_SOURCE_INCOME_OCCUPATION_DISCREPANCY_PROMPT = 8;
  // risk ops give verdict on income and occupation discrepancy
  UPDATE_SOURCE_RISKOPS_ACTION = 9;
  // We create employment data in B2B onboarding without occupation type, this field will be used to skip non-empty
  // occupation value check for B2B onboarding
  // UPDATE_SOURCE_ONBOARDING doesn't include B2B and it will check for missing occupation type
  UPDATE_SOURCE_B2B_ONBOARDING = 10;
  // user's occupation and salary updated through fi lite CC onboarding flow
  UPDATE_SOURCE_CREDIT_CARD_ONBOARDING = 11;
  // The behaviour for this source will be similar to UPDATE_SOURCE_SALARY_PROGRAM_INAPP
  // but the employer and salary registration employer confirmation stage of the user will be updated from salaryprogram_update_reg_employer_stage script.
  UPDATE_SOURCE_SALARY_REG_EMPLOYER_STAGE_SCRIPT = 12;
  // user update there income occupation from home top notice bar
  UPDATE_SOURCE_HOME_TOP_NOTICE_BAR = 13;
  // Salary onb stage update consumer automatically complete salary reg employer confirmation stage for b2b whitelisted users
  // The behaviour for this source will be similar to UPDATE_SOURCE_SALARY_PROGRAM_INAPP
  UPDATE_SOURCE_SALARY_ONB_STAGE_UPDATE_CONSUMER = 14;
  // user's employment data is updated during periodic kyc flow
  UPDATE_SOURCE_PERIODIC_KYC = 15;
  // employment data declared in acquire to lend flow
  UPDATE_SOURCE_ACQUIRE_TO_LEND = 16;
}

enum IncOccDiscrepancyAction {
  INC_OCC_DISCREPANCY_ACTION_UNSPECIFIED = 0;
  INC_OCC_DISCREPANCY_ACTION_VERIFIED = 1;
  INC_OCC_DISCREPANCY_ACTION_REJECTED = 3;
}

enum OccupationType {
  OCCUPATION_TYPE_UNSPECIFIED = 0;
  OCCUPATION_TYPE_LEGAL_AND_JUDICIARY = 1 [deprecated = true];
  OCCUPATION_TYPE_SOFTWARE_AND_IT = 2 [deprecated = true];
  OCCUPATION_TYPE_ENGINEERING = 3;
  OCCUPATION_TYPE_HEALTHCARE = 4;
  OCCUPATION_TYPE_ACADEMIA = 5;
  OCCUPATION_TYPE_BANKING = 6;
  OCCUPATION_TYPE_CHARTERED_ACCOUNTANT = 7;
  OCCUPATION_TYPE_PUBLIC_SERVICES = 8;
  OCCUPATION_TYPE_MERCHANT_AND_TRADE = 9;
  OCCUPATION_TYPE_NEWS_AND_MEDIA = 10;
  OCCUPATION_TYPE_BUSINESS = 11;
  OCCUPATION_TYPE_AVIATION = 12 [deprecated = true];
  OCCUPATION_TYPE_REAL_ESTATE_AND_INFRASTRUCTURE = 13;
  OCCUPATION_TYPE_DEFENCE_AND_LAW_ENFORCEMENT = 14;
  OCCUPATION_TYPE_MARKETING_AND_SALES = 15;
  OCCUPATION_TYPE_OTHERS = 16 [deprecated = true];
  OCCUPATION_TYPE_ENTERTAINMENT = 17;
  OCCUPATION_TYPE_CRYPTO_TRADING = 18;
  OCCUPATION_TYPE_LUXURY_CAR_DEALER = 19;
  OCCUPATION_TYPE_SCRAP_DEALER = 20;
  OCCUPATION_TYPE_STUDENT = 21;
  OCCUPATION_TYPE_HOMEMAKER = 22;
  OCCUPATION_TYPE_RETIRED = 23;
  OCCUPATION_TYPE_JUDGE = 24;
  OCCUPATION_TYPE_ADVOCATE = 25;
  OCCUPATION_TYPE_FUND_MANAGEMENT = 26;
  OCCUPATION_TYPE_SELF_EMPLOYED = 27;
  OCCUPATION_TYPE_DIPLOMAT = 28;
  OCCUPATION_TYPE_AGRICULTURE = 29;
  OCCUPATION_TYPE_VIRTUAL_CURRENCY_DEALER = 30;
  OCCUPATION_TYPE_ART_ANTIQUES_DEALER = 31;
  OCCUPATION_TYPE_ARM_ARMAMENTS_DEALER = 32;
  OCCUPATION_TYPE_GOLD_PRECIOUS_STONE_DEALER = 33;
  OCCUPATION_TYPE_PAWN_BROKER = 34;
  OCCUPATION_TYPE_UNEMPLOYED = 35;
  OCCUPATION_TYPE_SALARIED = 36;
  OCCUPATION_TYPE_SELF_EMPLOYED_PROFESSIONAL = 37;
  OCCUPATION_TYPE_POLITICIAN_OR_STATESMAN = 38;
  OCCUPATION_TYPE_BUREAUCRAT = 39;
  OCCUPATION_TYPE_FINANCIAL_SECTOR = 40;
  OCCUPATION_TYPE_GOVERNMENT = 41;
  OCCUPATION_TYPE_MEDIA = 42;
  OCCUPATION_TYPE_NGO = 43;
  OCCUPATION_TYPE_PRIVATE_SECTOR = 44;
  OCCUPATION_TYPE_PUBLIC_SECTOR = 45;
  OCCUPATION_TYPE_LLP = 46;
  OCCUPATION_TYPE_PARTNERSHIP = 47;
  OCCUPATION_TYPE_PROPRIETORSHIP = 48;
  OCCUPATION_TYPE_PRIVATE_LIMITED = 49;
  OCCUPATION_TYPE_PUBLIC_LIMITED = 50;
  OCCUPATION_TYPE_TRUST = 51;
  OCCUPATION_TYPE_SOCIETY = 52;
  OCCUPATION_TYPE_MULTINATIONAL = 53;
  OCCUPATION_TYPE_MANUFACTURING = 54;
  OCCUPATION_TYPE_PROFESSIONAL_INTERMEDIARIES = 55;
  OCCUPATION_TYPE_REAL_ESTATE_BUSINESS = 56;
  OCCUPATION_TYPE_STOCK_BROKER = 57;
  OCCUPATION_TYPE_MONEY_LENDER_OR_PRIVATE_FINANCIERS = 58;
  OCCUPATION_TYPE_SERVICE_PROVIDER = 59;
  OCCUPATION_TYPE_TRADER = 60;
  OCCUPATION_TYPE_ARCHITECT = 61;
  OCCUPATION_TYPE_CA_CS = 62;
  OCCUPATION_TYPE_DOCTOR = 63;
  OCCUPATION_TYPE_IT_CONSULTANT = 64;
  OCCUPATION_TYPE_LAWYER = 65;
  OCCUPATION_TYPE_FREELANCER = 66;
  OCCUPATION_TYPE_CAR_DEALER = 67;
}
