//go:generate gen_queue_pb
// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package employment.consumer;

import "api/employment/employment_verification_check.proto";
import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";
import "api/frontend/account/screening/service.proto";

option go_package = "github.com/epifi/gamma/api/employment/consumer";
option java_package = "com.github.epifi.gamma.api.employment/consumer";

// This GRPC service is registered with subscriber. RPC method will be invoked by the subscriber
// on receiving an event.
service EmploymentConsumer {
  // VerifyEmploymentDetails method verifies the employment details provided by the user.
  // This method is registered with a queue subscriber.
  // It currently only supports verification of salaried employees whose employer is registered with EPFO.
  // For all other cases it returns PERMANENT_FAILURE.
  rpc VerifyEmploymentDetails (VerifyEmploymentDetailsRequest) returns (VerifyEmploymentDetailsResponse) {};

  // VerifyUserLinkedinInformation method verifies the following
  // 1. linkedin name with the name registered with us
  // 2. employer name with the info given by the user
  rpc VerifyUserLinkedinInformation (VerifyUserLinkedinInformationRequest) returns (VerifyUserLinkedinInformationResponse) {};
}

message VerifyEmploymentDetailsRequest {
  // A set of all the common attributes to be contained in a consumer response
  queue.ConsumerRequestHeader request_header = 1;

  string actor_id = 2;
  // client_req_id is the unique identifier of a process entry for a client
  string client_req_id = 4;
  // time when employment verification started
  google.protobuf.Timestamp process_started_at = 3;
}

message VerifyEmploymentDetailsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message VerifyUserLinkedinInformationRequest {
  queue.ConsumerRequestHeader request_header = 1;
  string email_id = 2;
  employment.EmploymentVerificationCheck employmentVerificationCheckEntry = 3;
  string user_name = 4;
}

message VerifyUserLinkedinInformationResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message IncomeUpdateEvent {
  queue.ConsumerRequestHeader request_header = 1;
  string actor_id = 2;
  // timestamp at which the event happened.
  google.protobuf.Timestamp income_updated_at = 3;
  // salary range struct has both salary range and absolute salary
  frontend.account.screening.AnnualSalary annual_salary = 4;
}
