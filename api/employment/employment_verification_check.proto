syntax = "proto3";

package employment;

import "api/typesv2/common/boolean.proto";
import "api/vendorgateway/employment/service.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/employment";
option java_package = "com.github.epifi.gamma.api.employment";


message EmploymentVerificationCheck {
  // unique identifier for an Employment data entry
  string id = 1;
  // actor_id of the user
  string actor_id = 2;
  // unique identifier of the verification process that initiated the check
  string verification_process_id = 3;
  // name of the employment verification check
  CheckName name = 4;
  // `stage_details` stores status of the all stages that are part of the check
  CheckStageDetails stage_details = 5;
  // can be used to store metadata for different checks
  CheckMetadata metadata = 6;
  // outcome of the verification
  CheckResult result = 7;
  // reason for the result
  CheckResultReason reason = 8;
  // stores the verification data outcome.
  // for eg: vendor responses for the epfo karza call
  ResultMetadata result_metadata = 12;
  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp deleted_at = 11;
}

// CheckName contains name of the different verification checks that can performed on a user
enum CheckName {
  CHECK_NAME_UNSPECIFIED = 0;
  // check if there is a match for user's name and selected company by the user in EPFO data
  CHECK_NAME_EPFO_SELECTED_COMPANY_MATCH = 1;
  // check if there is a match for user's name and company names similar to the selected company by the user in EPFO data
  CHECK_NAME_EPFO_SIMILAR_COMPANY_NAME_MATCH = 2;
  // check if there is a match for user's name and entered company name prefix by the user in EPFO data
  CHECK_NAME_EPFO_ENTERED_COMPANY_NAME_MATCH = 3;
  // check if domain name of the personal profile link is whitelisted
  CHECK_NAME_DOMAIN_NAME_VERIFICATION = 4;
  // check if email domain of the user is same as company registered email domain
  CHECK_NAME_WORK_EMAIL_VERIFICATION = 5;
  // check if users name matches with linked details
  CHECK_NAME_LINKEDIN_VERIFICATION = 6;
  // check if user has gstin id present at vendor's end
  CHECK_NAME_GSTIN_PRESENCE_CHECK = 7;
  // check if TDS was deducted for the user
  CHECK_NAME_FORM16 = 8;
  // check if user has UAN present at vendor's end
  CHECK_NAME_UAN_PRESENCE_CHECK = 9;
}

// CheckStageDetails contains stage-wise status for all the checks.
// At a time it will store details for only one check type.
message CheckStageDetails {
  oneof stage_details {
    WorkEmailVerificationStageDetails work_email_verification_stage_details = 1;
    LinkedinVerificationCheckStageDetails linkedin_verification_stage = 2;
    GSTINPresenceCheckStageDetails gstin_presence_check_stage = 3;
    UANPresenceCheckStageDetails uan_presence_check_stage = 4;
  }
}

enum WorkEmailVerificationStage {
  WORK_EMAIL_VERIFICATION_STAGE_UNSPECIFIED = 0;
  WORK_EMAIL_VERIFICATION_STAGE_SEND_OTP = 1;
  WORK_EMAIL_VERIFICATION_STAGE_VERIFY_OTP = 2;
}

message WorkEmailVerificationStageDetails {
  // string here is expected to be string form of enum fields in WorkEmailVerificationStage
  map<string, CheckStageStatus> work_email_verification_stage_details_map = 1;
}

// status of different stages of verification checks
enum CheckStageStatus {
  CHECK_STAGE_STATUS_UNSPECIFIED = 0;

  CHECK_STAGE_STATUS_INITIATED = 1;

  CHECK_STAGE_STATUS_SUCCESS = 2;

  CHECK_STAGE_STATUS_FAILED = 3;
}

message WorkEmailVerificationCheckMetadata {
  int32 send_attempts = 1;
  int32 verify_attempts = 2;
  string token = 3;
  google.protobuf.Timestamp otp_expiry_at = 4;
  int32 generate_otp_response_code = 5;
  int32 verify_otp_response_code = 6;
  // to maintain attempts made to fetch company master data to get registered email domain
  int32 company_email_fetch_attempt = 7;
  // denotes overall state(used for transient check issue currently)
  WorkEmailVerificationFailureReason work_email_verification_failure_reason = 8;
  // email stores the email to which otp was sent to and verification was attempted.
  // this field was added later (the same field exists in employment_verification_processes) to support changing
  // emails in the same work email verification attempt.
  string email = 9;
}

// stores metadata for all the verification checks
message CheckMetadata {
  oneof check_metadata {
    WorkEmailVerificationCheckMetadata work_email_verification_check_metadata = 1;
  }
}

// result of the similar company name search
message SimilarCompanyNameSearchResult {
  string company_name = 1;
  string vendor_id = 2;
  // employee name search response received from vendor
  vendorgateway.employment.EmployeeNameSearchResponse vendor_response = 3;
  // verification was successful with this company
  api.typesv2.common.BooleanEnum match_found = 4;
}

message SimilarCompanyEmploymentCheckResult {
  // similar company name search results
  repeated SimilarCompanyNameSearchResult similar_company_name_search_results = 1;
}

message EnteredCompanyNameEmploymentCheckResult {
  // epfo data received from vendor on sending entered company name and employee name in request
  vendorgateway.employment.VerifyEmploymentResponse entered_company_name_search_result = 1;
}

enum WorkEmailVerificationFailureReason {
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_UNSPECIFIED = 0;
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_GENERATE_OTP_FAILURE = 1;
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_VENDOR_API_FAILURE = 2;
  WORK_EMAIL_VERIFICATION_FAILURE_REASON_GENERATE_OTP_ATTEMPTS_EXCEEDED = 3;
}

// stores the data used for verification
message ResultMetadata {
  oneof results {
    SimilarCompanyEmploymentCheckResult similar_company_employment_check = 1;
    EnteredCompanyNameEmploymentCheckResult entered_company_name_employment_check = 2;
    LinkedinVerificationCheckResult linkedin_verification_check_result_metadata = 3;
  }
}

// final outcome of the verification checks
enum CheckResult {
  CHECK_RESULT_UNSPECIFIED = 0;

  CHECK_RESULT_ACCEPTED = 1;

  CHECK_RESULT_REJECTED = 2;
}

enum CheckResultReason {
  CHECK_RESULT_REASON_UNSPECIFIED = 0;
  CHECK_RESULT_REASON_DOMAIN_NAME_WHITELISTED = 1;
  CHECK_RESULT_REASON_DOMAIN_NAME_NOT_WHITELISTED = 2;
  // vendor id for company is not present in employment info.
  CHECK_RESULT_REASON_VENDOR_ID_NOT_PRESENT = 3;
  // we received invalid argument error from vendor for company id and
  // employee name this might happen when we send invalid company id in request.
  CHECK_RESULT_REASON_VENDOR_INVALID_ARGUMENT_ERROR = 4;
  // no EPF record was found for the requested company id and employee name
  // combination sent in request.
  CHECK_RESULT_REASON_VENDOR_NO_RECORD_FOUND = 5;
  // Name match not found for user in the epf data received from vendor
  CHECK_RESULT_REASON_EPF_DATA_NAME_MATCH_NOT_FOUND = 6;
  // no deposit from employer in user's PF account in last 3 months
  CHECK_RESULT_REASON_EPF_DATA_PF_NOT_RECEIVED = 7;
  CHECK_RESULT_REASON_EPFO_DATA_SUCCESSFULLY_VERIFIED = 8;
  // no match found for user on searching in similar company names
  CHECK_RESULT_REASON_EPFO_NO_MATCH_FOUND_IN_SIMILAR_COMPANIES = 9;
  // api call to vendor timed out
  CHECK_RESULT_REASON_VENDOR_REQUEST_TIMEOUT = 10;
  // unknown response code received from vendor
  CHECK_RESULT_REASON_UNKNOWN_RESPONSE_CODE_FROM_VENDOR = 11;
  // send otp attempts exhausted
  CHECK_RESULT_REASON_SEND_OTP_ATTEMPTS_EXHAUSTED = 12;
  // verify otp attempts exhausted
  CHECK_RESULT_REASON_INVALID_EMAIL_DOMAIN = 13;
  // user email and company email domain does not match
  CHECK_RESULT_REASON_VERIFY_OTP_ATTEMPTS_EXHAUSTED = 14;
  // email not found due to CIN not found at vendor
  CHECK_RESULT_COMPANY_EMAIL_NOT_FOUND = 15;
  // denotes vendor api attempts exhaustion
  CHECK_RESULT_VENDOR_API_ATTEMPTS_EXHAUSTED = 16;
  // denotes domain not allowed through screener
  CHECK_RESULT_REASON_DOMAIN_NOT_VERIFIED = 17;
  // user not salaried
  CHECK_RESULT_REASON_NOT_SALARIED = 18;
  // denotes domain not found in our db or by vendor
  CHECK_RESULT_REASON_DOMAIN_NOT_FOUND = 19;
  // vendor response found, but user not employed
  CHECK_RESULT_REASON_EPFO_DATA_USER_NOT_EMPLOYED = 20;
}

enum EmploymentVerificationCheckFieldMask {
  EMPLOYMENT_VERIFICATION_CHECK_FIELD_MASK_UNSPECIFIED = 0;
  EMPLOYMENT_VERIFICATION_CHECK_ID = 1;
  EMPLOYMENT_VERIFICATION_CHECK_ACTOR_ID = 2;
  EMPLOYMENT_VERIFICATION_CHECK_VERIFICATION_PROCESS_ID = 3;
  EMPLOYMENT_VERIFICATION_CHECK_NAME = 4;
  EMPLOYMENT_VERIFICATION_CHECK_STAGE_DETAILS = 5;
  EMPLOYMENT_VERIFICATION_CHECK_METADATA = 6;
  EMPLOYMENT_VERIFICATION_CHECK_RESULT = 7;
  EMPLOYMENT_VERIFICATION_CHECK_REASON = 8;
  EMPLOYMENT_VERIFICATION_CHECK_CREATED_AT = 9;
  EMPLOYMENT_VERIFICATION_CHECK_UPDATED_AT = 10;
  EMPLOYMENT_VERIFICATION_CHECK_DELETED_AT = 11;
  EMPLOYMENT_VERIFICATION_CHECK_RESULT_METADATA = 12;
}

message LinkedinVerificationCheckStageDetails {
  map<string, CheckStageStatus> linkedin_verification_stage_map = 1;
}

enum LinkedinVerificationCheckStage {
  LINKEDIN_VERIFICATION_STAGE_UNSPECIFIED = 0;
  LINKEDIN_VERIFICATION_STAGE_GET_USER_DETAILS = 1;
}

enum GSTINPresenceCheckStage {
  GSTIN_PRESENCE_CHECK_UNSPECIFIED = 0;
  GSTIN_PRESENCE_CHECK_GET_USER_GSTIN = 1;
}

message LinkedinVerificationCheckResult {
  api.typesv2.common.BooleanEnum passed_user_name_check = 1;
  api.typesv2.common.BooleanEnum passed_company_name_check = 2;
}

message GSTINPresenceCheckStageDetails {
  map<string, CheckStageStatus> gstin_presence_check_stage_map = 1;
}

enum UANPresenceCheckStage {
  UAN_PRESENCE_CHECK_UNSPECIFIED = 0;
  UAN_PRESENCE_CHECK_GET_USER_UAN = 1;
}

message UANPresenceCheckStageDetails {
  map<string, CheckStageStatus> uan_presence_check_stage_map = 1;
}
