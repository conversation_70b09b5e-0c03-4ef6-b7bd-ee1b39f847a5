// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.fittt.action.aggregator.consumer;

import "api/fittt/action/action.proto";
import "api/fittt/action/aggregator/aggregation.proto";
import "api/fittt/action/aggregator/consumer/aggr_trigger_event.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/fittt/action/aggregator/consumer;consumerpb";
option java_package = "com.github.epifi.gamma.api.fittt.action.aggregator.consumer";

// Consumer service to process aggregation entity.
service AggregationEntityConsumer {
  // ProcessAggregationEntity processes the incoming entity and batches them to aggregate later.
  rpc ProcessAggregationEntity (ProcessAggregationEntityRequest) returns (ProcessAggregationEntityResponse) {}
}

message ProcessAggregationEntityRequest {
  // header for queue request
  queue.ConsumerRequestHeader request_header = 1;
  // aggregation entity object
  AggregationEntity entity = 2;
  // action_data is used in cases, there is need to aggregate actions and execute one single action for set of actions
  ActionData action_data = 3;
  // aggregation of actions can be done on subscriptionId
  string subscription_id = 4;
  // aggregation info
  AggregationInfo aggr_info = 5;
}

message ProcessAggregationEntityResponse {
  // header for queue response
  queue.ConsumerResponseHeader response_header = 1;
}


// Consumer service to process triggers and initiate execution of aggregated entities.
service AggregationTriggerConsumer {
  // PerformAggregation processes the message to finish aggregation for a specific aggregation key.
  // The consumer will then group AggregationEntity by aggregation key per actor_id and publish events for
  // each of them for AggregatedActionExecutionConsumer to consumer.
  rpc PerformAggregation (PerformAggregationRequest) returns (PerformAggregationResponse) {}
}

message PerformAggregationRequest {
  // header for queue request
  queue.ConsumerRequestHeader request_header = 1;
  AggregationType exec_type = 2;
  EventData event_data = 3;
  AggregationInfo aggr_info = 4;
  // clientRequestId will be used to identify and reject duplicate message at execution trigger consumer
  // clientRequestId should be unique for every trigger request. It can be a random generated uuid
  string client_request_id = 5;
}

message EventData {
  // event for which the executions has be aggregated
  // Aggregator will generate aggregation key based on the event and
  // group executions with the key for each each user and initiate execution
  oneof event {
    CricketAggregationEvent cricket_aggr_event = 3;
    FootballAggregationEvent football_aggr_event = 4;
    SMSAggregationEvent sms_aggr_event = 5;
    PurchaseMutualFundEvent purchase_mf_event = 6;
  }
}

message PurchaseMutualFundEvent {
  string subscription_id = 1;
  string mf_id = 2;
}

message PerformAggregationResponse {
  // header for queue response
  queue.ConsumerResponseHeader response_header = 1;
}

// Consumer service to process Execution of aggregated entities.
service AggregatedActionExecutionConsumer {
  // ProcessExecution processes the AggregatedExecution and performs final action.
  // AggregatedExecution can be sending a aggregated notification for multiple executions of a rules in a sports torunament
  // Or It can be single AddFunds deposit action for multiple executions of a Cricket rule for a match.
  // For these actions, all depsoit amount details will be retrieved, summed into single amount and deposited as single Txn order.
  rpc ProcessExecution (ProcessExecutionRequest) returns (ProcessExecutionResponse) {}
}

message ProcessExecutionRequest {
  // header for queue request
  queue.ConsumerRequestHeader request_header = 1;
  AggregatedExecution execution = 2;
}

message ProcessExecutionResponse {
  // header for queue response
  queue.ConsumerResponseHeader response_header = 1;
}
