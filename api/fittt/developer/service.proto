// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package api.fittt.developer;

option go_package = "github.com/epifi/gamma/api/fittt/developer";
option java_package = "com.github.epifi.gamma.api.fittt.developer";

import "api/cx/developer/db_state/db_state.proto";

service FITTTDbStates{
  // service to fetch list of entities for which savings can return the data
  rpc GetEntityList(cx.developer.db_state.GetEntityListRequest) returns (cx.developer.db_state.GetEntityListResponse) {}

  // For each entity as defined above, the parameter required to fetch that data will be different
  // This service will return appropriate params based on entity passed in request
  rpc GetParameterList(cx.developer.db_state.GetParameterListRequest) returns (cx.developer.db_state.GetParameterListResponse) {}

  // The actual get data API call where we will make a DB call to get the required data
  rpc GetData(cx.developer.db_state.GetDataRequest) returns (cx.developer.db_state.GetDataResponse) {}
}
