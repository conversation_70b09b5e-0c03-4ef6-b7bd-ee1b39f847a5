// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.fittt.devconsole;

import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "api/fittt/devconsole/sports.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/fittt/devconsole;devconsolepb";
option java_package = "com.github.epifi.gamma.api.fittt.devconsole";

// Service supports FIT Developer APIs to create and manage rules.
service DeveloperConsoleService {
  // RPC to get list of sports that are supported in FIT.
  rpc GetSportsList(GetSportsListRequest) returns(GetSportsListResponse){}

  // RPC to get list of ongoing and upcoming tournaments for the given sport.
  // Tournament message returned from this rpc will not have teams, players, rules populated.
  // Use GetTournament with tournament id to get all the details.
  rpc GetTournaments(GetTournamentsRequest) returns(GetTournamentsResponse){}

  // RPC returns tournament details with players, teams and rules.
  rpc GetTournament(GetTournamentRequest) returns(GetTournamentResponse){}

  // RPC to configure new tournament or update existing tournament with new configuration.
  // For existing tournament, RPC make changes to them based on the diff from new and existing configuration
  rpc ConfigureTournament(ConfigureTournamentRequest) returns(ConfigureTournamentResponse){}

  // RPC returns sports events that can be scheduled for a tournament.
  // TODO(sakthi) - Now, RPC doesn't validate existing schedules and returns all available schedules.
  // Change this contract and BE support if required in future.
  rpc GetSportsEventSchedules(GetSportsEventSchedulesRequest) returns(GetSportsEventSchedulesResponse){}

  // RPC to setup triggers to execute rules for sports events.
  rpc ScheduleSportsEventTriggers(ScheduleSportsEventTriggersRequest) returns(ScheduleSportsEventTriggersResponse){}

}


message GetSportsListRequest {}


message GetSportsListResponse {
  rpc.Status status = 1;
  repeated SportType sport_types = 2;
}

message GetTournamentsRequest {
  SportType sport_type = 1 [(validate.rules).enum = {not_in: [0]}];
}


message GetTournamentsResponse {
  rpc.Status status = 1;
  repeated Tournament tournaments = 2;
}

message GetTournamentRequest {
  SportType sport_type = 1 [(validate.rules).enum = {not_in: [0]}];
  string tournament_id = 2 [(validate.rules).string = {min_len: 1}];
}

message GetTournamentResponse {
  rpc.Status status = 1;
  Tournament tournament = 2;
}

message ConfigureTournamentRequest {
  Tournament tournament = 1;
}

message ConfigureTournamentResponse {
  rpc.Status status = 1;
}

message  GetSportsEventSchedulesRequest {
  SportType sport_type = 1 [(validate.rules).enum = {not_in: [0]}];
  string tournament_id = 2 [(validate.rules).string = {min_len: 1}];
}

message GetSportsEventSchedulesResponse {
  rpc.Status status = 1;
  repeated SportsEventSchedule schedules = 2;
}


message ScheduleSportsEventTriggersRequest {
  repeated SportsEventSchedule schedules = 1;
}

message ScheduleSportsEventTriggersResponse {
  rpc.Status status = 1;
}


message SportsEventSchedule {
  SportType sport_type = 1;
  string tournament_id = 2;
  string id = 3;
  // optional - will be empty if the start is not available
  google.protobuf.Timestamp expected_start = 4;
  // optional - will be empty if the end is not predictable or available
  google.protobuf.Timestamp expected_end = 5;
  // optional - will be empty if the expected_start is not available
  google.protobuf.Timestamp schedule_execution_at = 6;
}
