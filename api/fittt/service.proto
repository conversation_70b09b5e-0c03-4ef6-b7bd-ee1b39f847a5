// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.fittt;

import "api/fittt/action/action.proto";
import "api/fittt/action/action_execution.proto";
import "api/fittt/event/event.proto";
import "api/fittt/page.proto";
import "api/fittt/sports/sports.proto";
import "api/rms/manager/rule.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/money.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/fittt";
option java_package = "com.github.epifi.gamma.api.fittt";

// Service to expose rpcs related to user's rule execution information.
service Fittt {
  // Rpc provides subscription execution information
  rpc GetSubscriptionExecutionInfo (GetSubscriptionExecutionInfoRequest) returns (GetSubscriptionExecutionInfoResponse) {}
  // RPC provides aggregated information of a rule subscription
  rpc GetSubscriptionAggregationInfo (GetSubscriptionAggregationInfoRequest) returns (GetSubscriptionAggregationInfoResponse) {}
  // RPC to get fittt related details of order update event. RPC will be useful for rewards to evaluate reward points
  // of user based on order update event. Additional use case will be for ES to index based on rule id, subscription id.
  rpc GetFitttDetailsOfOrder (GetFitttDetailsOfOrderRequest) returns (GetFitttDetailsOfOrderResponse) {}
  // Once list of conditions to be evaluated at client is received by the client (by using rms.GetTrackMetricsAndPendingExecutionsForAppRules API)
  // and all the conditions are evaluated, client invokes HandleAppConditionEvaluationResult by providing result of condition evaluations to server for action execution based on result
  // It will be responsibility of HandleAppConditionEvaluationResult to resume the execution by bypassing Condition command processor and using the data in AppConditionResult
  // a new event will be queued to RMS for relaying the execution
  rpc HandleConditionEvaluationResult (HandleConditionEvaluationResultRequest) returns (HandleConditionEvaluationResultResponse) {}
  // Get the stats like active subscription count, total saved etc for app rating nudge.
  rpc GetStatsForAppRatingsNudge (GetStatsForAppRatingsNudgeRequest) returns (GetStatsForAppRatingsNudgeResponse) {}
  // RPC provides aggregated information of a sports rule subscription for a match
  rpc GetSportsMatchPoints (GetSportsMatchPointsRequest) returns (GetSportsMatchPointsResponse) {}
  // RPC to get match result event for a cricket match
  rpc GetCricketMatchResultEvents (GetCricketMatchResultEventsRequest) returns (GetCricketMatchResultEventsResponse) {}
  // RPC provides aggregated information of a sports rule subscription for a tournament within a period
  rpc GetSportsTournamentPoints (GetSportsTournamentPointsRequest) returns (GetSportsTournamentPointsResponse) {}
  // RPC provides count of successful executions for cricket rules for matches played in the given period.
  rpc GetCricketRuleExecutionCount (GetCricketRuleExecutionCountRequest) returns (GetCricketRuleExecutionCountResponse) {}
  // RPC provides leaderboard for a tournament. The leaderboard will include top N contestants who scored highest points in the
  // tournament and also includes rank of the requested user.
  rpc GetTournamentLeaderboard (GetTournamentLeaderboardRequest) returns (GetTournamentLeaderboardResponse) {}
  // RPC provides reward client ids for tournament matches played within a period
  rpc GetSportsTournamentRewardsClientIds (GetSportsTournamentRewardsClientIdsRequest) returns (GetSportsTournamentRewardsClientIdsResponse) {}
  // UpdateSportsChallengeStats performs stats calculation and updation of stats for all the users
  // for matches falling in the requested time range
  rpc UpdateSportsChallengeStats (UpdateSportsChallengeStatsRequest) returns (UpdateSportsChallengeStatsResponse) {}
  // publish sports challenge weekly reward events to reward service for requested tournamentId and weekNo
  rpc PublishSportsChallengeWeeklyRewards (PublishSportsChallengeWeeklyRewardsRequest) returns (PublishSportsChallengeWeeklyRewardsResponse) {}
  // RPC provides amount that has been collected of a total amount required for rule execution
  // when the collected amount exceeds the total amount required to be saved, the rule is executed
  rpc GetAggregatedAmountSummary (GetAggregatedAmountSummaryRequest) returns (GetAggregatedAmountSummaryResponse) {}
  // GetRuleDetails is a generic RPC to return rule related information with action entities
  rpc GetRuleDetails (GetRuleDetailsRequest) returns (GetRuleDetailsResponse) {}
  // Rpc provides bulk action execution information for a list of unique identifiers
  rpc GetActionExecutionInfos (GetActionExecutionInfosRequest) returns (GetActionExecutionInfosResponse) {}
  // HandleSharkTankEvent is entry point for executing shark tank rules
  // consumes shark tank event and publishes relevant rms events for rule execution
  rpc HandleSharkTankEvent (HandleSharkTankEventRequest) returns (HandleSharkTankEventResponse) {}
}

message HandleSharkTankEventRequest {
  // key: Judge name; value: number of companies judge has invested in
  map<string, int32> individual_judge_investments = 1;
  // no of companies all judges invested to
  int32 all_judge_investments = 2;
  // episode no of the event
  int32 episode_no = 3;
  // retry suffix for bypassing idempotency check for same event
  string retry_suffix = 4;
  // episode date
  google.protobuf.Timestamp episode_date = 5;
}

message HandleSharkTankEventResponse {
  rpc.Status status = 1;
}

message GetRuleDetailsRequest {
  // Identifier of action_executions entity
  string action_exec_id = 1;
}

message GetRuleDetailsResponse {
  rpc.Status status = 1;
  api.rms.manager.Rule rule = 2;
  api.rms.manager.RuleSubscription subscription = 3;
}

message GetAggregatedAmountSummaryRequest {
  string actor_id = 1;
  // subscription id for which aggregated amount is to be fetched
  string subscription_id = 2 [(validate.rules).string.min_len = 1];
}

message GetAggregatedAmountSummaryResponse {
  rpc.Status status = 1;
  // amount aggregated until now for the subscription
  api.typesv2.Money aggregated_amount = 2;
}

message PublishSportsChallengeWeeklyRewardsRequest {
  string tournament_id = 1;
  int32 week_no = 2;
  // max users that should be receive rewards
  // eg: rewards top 3, 5, 50 users
  int32 no_of_users_qualified_for_weekly_rewards = 3;
}

message PublishSportsChallengeWeeklyRewardsResponse {
  rpc.Status status = 1;
}

message UpdateSportsChallengeStatsRequest {
  string tournament_tag_id = 1;
  // start_time > `from`
  google.protobuf.Timestamp from = 2;
  // start_time < `to`
  google.protobuf.Timestamp to = 3;
  sports.SportsType sports_type = 4;
}

message UpdateSportsChallengeStatsResponse {
  rpc.Status status = 1;
}

message HandleConditionEvaluationResultRequest {
  // map of executionId and result of condition evaluation
  map<string, bool> condition_evaluation_result = 1 [deprecated = true];
  string actor_id = 2;
  map<string, ConditionEvalResult> cond_eval_result = 3;
}

message ConditionEvalResult {
  // returns true or false
  bool result = 1;
  // evaluation_metadata will be mainly used for debugging purpose
  // contains map of executionId to string, string can be plain text or json string
  // json may contain information about the expression used for evaluation
  // if the evaluation resulted to false, json may contain information about any permission issue, screen lock etc
  string metadata = 2;
}

message HandleConditionEvaluationResultResponse {
  rpc.Status status = 1;
}

message GetSubscriptionExecutionInfoRequest {
  string subscription_id = 1 [(validate.rules).string.min_len = 1];
  // page context for pagination
  fittt.PageContextRequest page_context = 2 [deprecated = true];
  // optional. If not specified, Execution of all status will be included.
  repeated action.ActionStatus status = 3;
  // page context for pagination
  rpc.PageContextRequest page_context_v2 = 4;
  // optional. If not specified, Execution of all types will be included.
  repeated action.ActionType action_types = 5;
  // field mask for event data to be fetched
  google.protobuf.FieldMask event_field_mask = 6;
  // actor_id is required in order to prevent IDOR requests
  // service ensures subscription belongs to the requested actor
  string actor_id = 7 [(validate.rules).string.min_len = 1];
}

message GetSubscriptionExecutionInfoResponse {
  // status
  rpc.Status status = 1;
  // page context response
  fittt.PageContextResponse page_context = 2 [deprecated = true];
  // Rule execution list
  repeated RuleExecution rule_execution = 3;
  // page context response
  rpc.PageContextResponse page_context_v2 = 4;
}

message RuleExecution {
  // Rule type - save, invest, pay, notify, etc
  HistoryType history_type = 1;
  // Amount collected, deposited or invested
  api.typesv2.Money amount = 2;
  // Timestamp of last execution
  google.protobuf.Timestamp timestamp = 3;
  // status of action execution at action processor
  action.ActionStatus status = 4;
  // action related details
  action.ActionData action_data = 5;
  // optional: event which triggered the rule execution
  // specific fields to be fetched can be specified in the field mask, else this will be empty
  event.Event event = 6;
  // order id to be sent for transaction recipient deeplink
  string order_id = 7;
}

// denotes the type of entry in the history for a subscription
enum HistoryType {
  HISTORY_ENTRY_TYPE_UNSPECIFIED = 0;
  // add funds to smart deposit
  ADD_FUND = 1;
  // an entry in history corresponding to pausing the subscription
  PAUSE = 2;
  // an entry in history corresponding to un-pausing(resuming) the subscription
  UNPAUSE = 3;
  // an entry in history corresponding to the create subscription event. This is always the
  // first entry in the history for every subscription
  SUBSCRIPTION_CREATED = 4;
  // an entry in history corresponding to the update/edit subscription event.
  SUBSCRIPTION_UPDATED = 5;
  // auto-pay payments to beneficiary
  PAY_TO_BENEFICIARY = 6;
  // remind
  REMINDER_SENT = 7;
  // purchase mutual funds
  PURCHASE_MUTUAL_FUND = 8;
  // aggregated purchase mutual fund
  AGGREGATE_PURCHASE_MUTUAL_FUND = 9;
  EXECUTE_US_STOCKS_SIP = 10;
}

// Adding duplicate Rule category to avoid cyclic dependency
// TODO (keerthana): Move this to common pkg
enum RuleCategory {
  RULE_CATEGORY_UNSPECIFIED = 0;
  AUTO_SAVE = 1;
  AUTO_PAY = 2;
  REMINDER = 3;
  AUTO_INVEST = 4;
  US_STOCKS_SIP = 5;
}

message GetSubscriptionAggregationInfoRequest {
  string subscription_id = 1;
  // Specifies the type of actions to aggregate. ex: DEPOSIT, PURCHASE_MUTUAL_FUND
  action.ActionType action_type = 2 [(validate.rules).enum = {in: [1, 7, 9, 11]}];
  repeated AggregationInfoFieldMask fieldMasks = 3 [(validate.rules).repeated.min_items = 1];
  // optional - Specifies the status of actions to aggregate. Empty list will include actions of all statuses
  repeated action.ActionStatus status = 4;
  // optional - finds aggregate for actions created >= from time
  google.protobuf.Timestamp from = 5;
  // optional - finds aggregate for actions created <= to time
  google.protobuf.Timestamp to = 6;
}

enum AggregationInfoFieldMask {
  AGGREGATION_INFO_FIELD_MASK_UNSPECIFIED = 0;
  AGGREGATION_INFO_FIELD_MASK_TOTAL_AMOUNT = 1;
  AGGREGATION_INFO_FIELD_MASK_COUNT_AND_LAST_EXECUTION_TIME = 2;
}

message GetSubscriptionAggregationInfoResponse {
  // status
  rpc.Status status = 1;
  // subscription aggregation value depends on the rule category.
  // Save, and pay rules have money aggregates. Reminder rules have count
  RuleCategory rule_category = 2 [deprecated = true]; // this is deprecated as the caller will/can already know the rule category with subscription detail
  oneof aggregatedValue {
    api.typesv2.Money amount = 3 [deprecated = true];
    uint32 count = 4 [deprecated = true];
  }
  api.typesv2.Money total_amount = 5;
  uint32 executions_count = 6;
  google.protobuf.Timestamp last_execution_time = 7;
}


message GetFitttDetailsOfOrderRequest {
  string client_request_id = 1;
}

message GetFitttDetailsOfOrderResponse {
  rpc.Status status = 1;
  // rule id associated with a given order
  string rule_id = 2;
  // rule category for which the order generated
  RuleCategory rule_category = 3;
  // timestamp when rule fittt rule evaluation evaluated to success and produced a fittt action
  google.protobuf.Timestamp execution_timestamp = 4;
  // some of the rules like IPL cricket rules can have one action associated with multiple rule evaluation counts.
  // For eg, if a user subscribes to Super Sixer for player Dhoni, and assume Dhoni hit 7 sixers, there is only one
  // action is produced at the end of the match. But the user's rule was successful 7 times. In this scenaio, rule_evaluation_count = 7
  int32 rule_evaluation_count = 5;
  // subscription id
  string subscription_id = 6;

  // tags associated with the execution leading to this order.
  repeated string execution_tags = 7;
}

message GetStatsForAppRatingsNudgeRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // if validate_zen_mode_or_football_subs is set to true, RPC validates whether the given user has any zen mode or
  // football rule subscriptions, then the response will have true value set 'has_zen_mode_or_football_subs'.
  // Other stats won't be computed. App Rating nudge will be skipped for such users until the production
  // issues for these rules are fixed. TO avoid extra computation for these users, this is handled as special case
  // in implementation.
  // OPTIONAL
  bool validate_zen_mode_or_football_subs = 2;
}

message GetStatsForAppRatingsNudgeResponse {
  rpc.Status status = 1;
  uint64 active_subscriptions_count = 2;
  api.typesv2.Money total_saved = 3;
  // OPTIONAL
  // has_zen_mode_or_football_subs will be set only if request has validate_zen_mode_or_football_subs param set to true
  bool has_zen_mode_or_football_subs = 4;
}



message GetSportsTournamentPointsRequest {
  string actor_id = 1;
  string tournament_tag = 2;
  // finds aggregate for events with event_start_time >= event_start_from time
  google.protobuf.Timestamp event_start_from = 3;
  // finds aggregate for events with event_start_time <= event_start_to time
  google.protobuf.Timestamp event_start_to = 4;
}

message GetSportsTournamentPointsResponse {
  // status
  rpc.Status status = 1;
  uint32 total_points = 2;
}

message GetSportsMatchPointsRequest {
  string actor_id = 1;
  string vendor_match_id = 2;
  sports.Vendor vendor = 3;
}

message GetSportsMatchPointsResponse {
  // status
  rpc.Status status = 1;
  uint32 total_points = 2;
  // map<subscriptionId, no_of_events[ex: 6s, wickets, 1]>
  map<string, uint32> event_frequency = 3;
  map<string, action.ActionStatus> execution_status = 4;
}

message GetCricketRuleExecutionCountRequest {
  string actor_id = 1;
  repeated string rule_ids = 2;
  // finds aggregate for events with event_start_time >= event_start_from time
  google.protobuf.Timestamp event_start_from = 3;
  // finds aggregate for events with event_start_time <= event_start_to time
  google.protobuf.Timestamp event_start_to = 4;
}

message GetCricketRuleExecutionCountResponse {
  // status
  rpc.Status status = 1;
  uint32 count = 2;
}

message GetCricketMatchResultEventsRequest {
  repeated string vendor_match_ids = 1;
  // finds event with event_start_time >= event_start_from time
  google.protobuf.Timestamp event_start_from = 3;
  // finds event with event_start_time <= event_start_to time
  google.protobuf.Timestamp event_start_to = 4;
}

message GetCricketMatchResultEventsResponse {
  rpc.Status status = 1;
  // map<vendor_match_id, event.Event>
  map<string, event.Event> events = 2;
}

message GetTournamentLeaderboardRequest {
  string actor_id = 1;
  string tournament_tag_id = 2;
  // for each tournament, fittt's sports service precomputes top N ranks for a pre configured interval.
  // RPC will limit the number of ranks returned if rank_limit is specified.
  uint32 rank_limit = 3 [(validate.rules).uint32 = {gte: 1}];
  uint32 week_no = 4;
}

enum LeaderboardType {
  LEADER_BOARD_TYPE_UNSPECIFIED = 0;
  LEADER_BOARD_TYPE_WEEKLY = 1;
}

message GetTournamentLeaderboardResponse {
  // status
  rpc.Status status = 1;
  // Stats for top ranked participants
  repeated ContestantStats contestants_by_rank = 2;
  // Stats for given user
  ContestantStats user_stats = 3;
}

message ContestantStats {
  string actor_id = 1;
  uint32 rank = 2;
  uint32 points = 3;
}

message GetSportsTournamentRewardsClientIdsRequest {
  string tournament_tag_id = 1;
  // finds reward client ids for matches with match_start_time > start_time
  google.protobuf.Timestamp start_time = 2;
  // finds reward client ids for matches with end_time > match_end_time
  google.protobuf.Timestamp end_time = 3;
  // if true fetches response fields in reverse chronological order
  // def:- chronological order: from start_time to end_time
  // reverse chronological order: from end_time to start_time
  // note: sorted as per weekno in response,
  // the reward client ids for a particular week may not be sorted
  bool in_reverse_chronological_order = 4;
}

message GetSportsTournamentRewardsClientIdsResponse {
  message MapEntry {
    uint32 week_no = 1;
    // client reward ids for that week
    repeated string reward_client_request_ids = 2;
  }
  // response status
  rpc.Status status = 1;
  // list of rewards client ids
  repeated string rewards_client_request_ids = 2 [deprecated = true];
  // weekNo - rewardsIds list map
  repeated MapEntry week_no_reward_client_ids_map = 3;
}

message GetActionExecutionInfosRequest {
  oneof id {
    action.Ids ids = 1;
    action.ActorIds actor_ids = 2;
    action.SubscriptionIds subscription_ids = 3;
    action.ExecutionIds execution_ids = 4;
  }
  // optional. If not specified along with ids, action execution of all status will be included.
  // in case when subscription ids or execution ids are passed, this variable wont have any effect
  repeated action.ActionStatus status = 5;
  // optional. If not specified with ids, action execution of all types will be included.
  // in case when subscription ids or execution ids are passed, this variable wont have any effect
  repeated action.ActionType action_types = 6;
  // page context for pagination
  rpc.PageContextRequest page_context = 7;
  // action execution fields to be fetched,
  // if passed null, then all fields will be fetched
  google.protobuf.FieldMask action_field_mask = 8;
}

message GetActionExecutionInfosResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // response status
  rpc.Status status = 1;
  // map of ids to their action executions
  map<string, action.ActionExecution> action_executions = 2;
  // page context response
  rpc.PageContextResponse page_context_resp = 3;
}
