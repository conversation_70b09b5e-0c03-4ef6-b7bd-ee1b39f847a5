// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package securities.developer;

import "api/cx/developer/db_state/db_state.proto";

option go_package = "github.com/epifi/gamma/api/securities/developer";
option java_package = "com.github.epifi.gamma.api.securities.developer";

// Developer service provides APIs to query and manage Securities entities.
// It allows clients to list available entities, fetch required parameters, and retrieve entity data.
service SecuritiesDev {
  // GetEntityList returns the list of available Securities entities that can be queried.
  // Used to populate entity options in the UI or client applications.
  rpc GetEntityList (cx.developer.db_state.GetEntityListRequest) returns (cx.developer.db_state.GetEntityListResponse) {}
  // GetParameterList returns the list of parameters required for a specific Securities entity.
  // Used to determine input fields needed to fetch data for the selected entity.
  rpc GetParameterList (cx.developer.db_state.GetParameterListRequest) returns (cx.developer.db_state.GetParameterListResponse) {}
  // GetData fetches data for a specific Securities entity using provided parameters.
  // Returns the entity data as a JSON string for debugging or analysis.
  rpc GetData (cx.developer.db_state.GetDataRequest) returns (cx.developer.db_state.GetDataResponse) {}
}
