syntax = "proto3";

package paymentinstrument;

import "api/payment_instruments/payment_instrument.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/paymentinstrument";
option java_package = "com.github.epifi.gamma.api.paymentinstrument";

// PaymentInstrumentStateLog captures the state log for a payment instrument
// captures data like the source which requested the state change, reason for state change ec.
message PaymentInstrumentStateLog {
  // deprecated id in favour to use id_v2
  int64 id = 1 [deprecated = true];
  // id corresponding to the pi
  string pi_id = 2;

  // source which requested the state change
  Source source = 3;

  // reason for the state change to the current state
  string reason = 4;

  // state of the pi when this entry was created
  paymentinstrument.PaymentInstrumentState state = 5;

  // Standard timestamp fields
  // timestamp corresponding to entry creation
  google.protobuf.Timestamp created_at = 6;

  // timestamp corresponding to last update of entry
  google.protobuf.Timestamp updated_at = 7;

  // timestamp corresponding to the deletion of entry (to be populated in case entry was soft deleted)
  google.protobuf.Timestamp deleted_at = 8;

  // id to uniquely identify an entry in the DB
  string id_v2 = 9;
}

enum Source {
  // unspecified
  SOURCE_UNSPECIFIED = 0;
  // epifi user
  EPIFI_USER = 1;
  // sherlock
  // sherlock tool will also have the access to disable pi
  SHERLOCK = 2;
  // system set the pi to the current state
  // eg. Created state
  SYSTEM = 3;
}
