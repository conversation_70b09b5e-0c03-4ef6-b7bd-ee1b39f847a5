syntax = "proto3";

package paymentinstrument;

import "api/accounts/account_type.proto";
import "api/payment_instruments/payment_instrument.proto";
import "api/payment_instruments/payment_instrument_state_log.proto";
import "api/rpc/status.proto";
import "api/typesv2/account/enums.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/paymentinstrument";
option java_package = "com.github.epifi.gamma.api.paymentinstrument";

// Pi service provides CRUD operations on PI
// A PI is unique across the service
service Pi {
  // CreatePi creates the PI.
  // If the PI already exists, it returns the existing PI.
  // If the PI is of type UPI, it is assumed to have already been verified
  rpc CreatePi (CreatePiRequest) returns (CreatePiResponse) {}

  // Get PI
  // 1. of type BANK_ACCOUNT by account number and IFSC code
  // 2. of type UPI by VPA
  // 3. of type CARD
  // TODO(kunal): TBD details for upi and card type
  rpc GetPi (GetPiRequest) returns (GetPiResponse) {}

  // Get PI by id
  rpc GetPiById (GetPiByIdRequest) returns (GetPiByIdResponse) {}

  // Get List of PIs by list of ids
  // If any of the PI Id is not found, remaining PIs are returned.
  // In this case it is upto the caller to decide if this is an error or not.
  rpc GetPIsByIds (GetPIsByIdsRequest) returns (GetPIsByIdsResponse) {}

  // UpdatePi updates the Pi fields present in the update fields mask
  // verifies if the field masks is eligible to be update for the pi type
  // for eg. ACCOUNT_REFERENCE_NUMBER_UPI can be only used with pi of type upi
  rpc UpdatePi (UpdatePiRequest) returns (UpdatePiResponse) {}
  // rpc to get the list of pis state log for a given pi id
  // returns status record not found if no entry is present for the pi id
  rpc GetPiStateLog (GetPiStateLogRequest) returns (GetPiStateLogResponse) {}

  // BatchHardDeletePi does hard delete of PI from the database and publishes deletion event to
  // intimate respective stack holders.
  // The RPC works on all or none principle for all eligible entries (eligible = ids for which entry is present in the DB).
  // i.e. PI deletion and deletion event publishing is done in an atomic block
  // **NOTE**
  // The method only supports deletion on epiFi wealth related PIs at the moment.
  rpc BatchHardDeletePi (BatchHardDeletePiRequest) returns (BatchHardDeletePiResponse);

  // GetPisByVpas returns a list of Pis for given list of vpas
  // will return the partial list if some of the vpas dosen't have a pi
  // will return StatusRecordNotFound if no pi is present
  rpc GetPisByVpas (GetPisByVpasRequest) returns (GetPisByVpasResponse);

  // CreatePiAndAccountPi will creates pi and account pi for the given request
  // NOTE : it is an idempotent rpc , and will return the pi if already exists
  rpc CreatePiAndAccountPi (CreatePiAndAccountPiRequest) returns (CreatePiAndAccountPiResponse);
}

// Request message for creating a PI
message CreatePiRequest {
  // type cannot be PAYMENT_INSTRUMENT_TYPE_UNSPECIFIED (0)
  PaymentInstrumentType type = 1 [(validate.rules).enum = {not_in: [0]}];

  oneof identifier {
    Account account = 2;
    Upi upi = 3;
    Card card = 4;
    CreditCard credit_card = 10;
    InternationalAccount international_account = 11;
    UpiLite upi_lite = 12;
  }

  // Mandatory for PI of type UPI and ignored for other PI types
  string verified_name = 5;

  // Signifies if a payment instrument is internal or external
  paymentinstrument.PaymentInstrumentIssuer issuer_classification = 6;

  // Represents a map of capability of PI
  // NOTE - missing capabilities will be marked as true
  map<string, bool> capabilities = 7;

  // ownership of pi. Values can be value of payemntinstrument.Ownership
  Ownership ownership = 8;

  // state of the payment instrument to be created
  // if nothing is passed, pi will be created in VERIFIED state if the verified name is present
  // and CREATED state if verified name is not present
  .paymentinstrument.PaymentInstrumentState state = 9;
}

// Response message for creating a PI
message CreatePiResponse {
  enum Status {
    OK = 0;

    // Wrong ifsc code entered for a known(internal) account in the request.
    // The status is only valid for PI type BANK_ACCOUNT
    // The payment instrument with corrected IFSC code is also
    // returned in the response.
    INCORRECT_IFSC = 100;
  }

  rpc.Status status = 1;
  PaymentInstrument paymentInstrument = 2;
}

// Request message to get a PI
message GetPiRequest {
  // type cannot be PAYMENT_INSTRUMENT_TYPE_UNSPECIFIED (0)
  PaymentInstrumentType type = 1 [(validate.rules).enum = {not_in: [0, 3]}];

  oneof identifier {
    AccountRequestParams accountRequestParams = 2;
    UpiRequestParams upiRequestParams = 3;
    DebitCardRequestParams debit_card_request_params = 4;
    PartialAccountRequestParams partial_account_request_params = 5;
    PartialUpiRequestParams partial_upi_request_params = 6;
    GenericAccountRequestParams generic_account_request_params = 7;
    UpiLiteRequestParams upi_lite_request_params = 8;
  }

  message AccountRequestParams {
    string actual_account_number = 1 [(validate.rules).string = {min_len: 8, max_len: 32}];
    string ifsc_code = 2 [(validate.rules).string = {min_len: 4, max_len: 32}];
  }

  message UpiRequestParams {
    string vpa = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  }

  message DebitCardRequestParams {
    // token issued from the ETS for the actual card number
    string tokenized_card_number = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
    // card expiry date of the format MMYY (eg : 0126)
    string expiry = 2 [(validate.rules).string.len = 4];
    // name printed on the card
    string name = 3 [(validate.rules).string = {min_len: 1, max_len: 100}];
  }

  message PartialAccountRequestParams {
    // contains the first n digits of the account number
    string partial_account_number = 1 [(validate.rules).string = {min_len: 1, max_len: 32}];
    // ifsc code for the partial account identifier
    string ifsc_code = 2 [(validate.rules).string = {len: 11}];
    // in case of partial pi we need the name to fetch the pi.
    // we fetch the pi with name_accountNumber_ifscCode
    string name = 3 [(validate.rules).string = {min_len: 1, max_len: 100}];
  }

  message PartialUpiRequestParams {
    // contains the first n digits of the vpa
    string partial_vpa = 1 [(validate.rules).string = {min_len: 1, max_len: 100}];
    // in case of partial pi we need the name to fetch the pi.
    // we fetch the pi with name_vpa
    string name = 2 [(validate.rules).string = {min_len: 1, max_len: 100}];
  }

  message GenericAccountRequestParams {
    // account number for the generic pi
    string account_number = 1 [(validate.rules).string = {min_len: 8, max_len: 32}];
    // ifsc code for the generic pi
    string ifsc_code = 2 [(validate.rules).string = {len: 11}];
    // name for the generic pi
    // will be used for searching on computed column -> name_accountNumber_ifsc
    string name = 3 [(validate.rules).string = {min_len: 1, max_len: 100}];

  }

  message UpiLiteRequestParams {
    string lrn = 1 [(validate.rules).string = {min_len: 1}];
  }
}

// Response message to get a PI
message GetPiResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  PaymentInstrument paymentInstrument = 2;
}

// Request message to get a PI
message GetPiByIdRequest {
  string id = 1 [(validate.rules).string = {min_len: 16, max_len: 96}];
}

// Response message to get a PI
message GetPiByIdResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  PaymentInstrument paymentInstrument = 2;
}

message GetPIsByIdsRequest {
  repeated string ids = 1 [(validate.rules).repeated.items.string = {min_len: 16, max_len: 96}];
}

message GetPIsByIdsResponse {
  enum Status {
    OK = 0;
    INTERNAL = 13;
  }

  rpc.Status status = 1;
  repeated PaymentInstrument paymentinstruments = 2;
}

message UpdatePiRequest {
  // update payment instrument
  PaymentInstrument payment_instrument = 1;

  // field mask to ensure only desired fields are updated
  repeated PaymentInstrumentFieldMask update_field_mask = 2;

  // source which triggered this update. eg. SHERLOCK, EPIFI
  // Note - In case of state change the source app will be stored in pi state log table
  Source source = 3;

  // reason for the update
  // Note - In case of state change the reason will be stored in pi state log table
  string update_reason = 4;
}

message UpdatePiResponse {
  enum Status {
    OK = 0;
    // invalid argument passed by the client.
    // for eg. mask ACCOUNT_REFERENCE_NUMBER_UPI passed for pi of type account
    INVALID_ARGUMENT = 3;
    // internal error while processing the update
    INTERNAL = 13;
  }
  // status of the request
  rpc.Status status = 1;
}

message GetPiStateLogRequest {
  // pi id for which we need the pi state log
  string pi_id = 1;
}

message GetPiStateLogResponse {
  enum Status {
    OK = 0;
    // record not found
    RECORD_NOT_FOUND = 5;
    // internal error
    INTERNAL = 13;
  }
  // status of the request
  rpc.Status status = 1;

  // list of pi state log for the given pi id
  repeated paymentinstrument.PaymentInstrumentStateLog pi_state_logs = 2;
}

message BatchHardDeletePiRequest {
  repeated string pi_ids = 1;
}

message BatchHardDeletePiResponse {
  enum Status {
    OK = 0;
    // internal error
    INTERNAL = 13;
    // if mentioned pi identifiers are already deleted/not present in the data base.
    ALREADY_PROCESSED = 50;
  }
  // status of the request
  rpc.Status status = 1;
}

message GetPisByVpasRequest {
  // vpas for which the pis needs to be fetched
  repeated string vpas = 1;
}

message GetPisByVpasResponse {
  enum Status {
    OK = 0;
    RECORD_NOT_FOUND = 5;
    INTERNAL = 13;
  }

  rpc.Status status = 1;
  // list of PIs for the given VPAs
  repeated PaymentInstrument paymentinstruments = 2;
}

message CreatePiAndAccountPiRequest {

  string actor_id = 1;

  // type cannot be PAYMENT_INSTRUMENT_TYPE_UNSPECIFIED (0)
  PaymentInstrumentType type = 2 [(validate.rules).enum = {not_in: [0]}];

  oneof identifier {
    Account account = 3;
    Upi upi = 4;
    Card card = 5;
    CreditCard credit_card = 6;
    UpiLite upi_lite = 14;
  }

  // Mandatory for PI of type UPI and ignored for other PI types
  string verified_name = 7;

  // Signifies if a payment instrument is internal or external
  paymentinstrument.PaymentInstrumentIssuer issuer_classification = 8;

  // Represents a map of capability of PI (inbound and outbound)
  // NOTE - missing capabilities will be marked as true
  map<string, bool> capabilities = 9;

  // ownership of pi (pass this taking entity segregation in mind)
  Ownership ownership = 10;

  // state of the payment instrument to be created
  // if nothing is passed, pi will be created in VERIFIED state if the verified name is present
  // and CREATED state if verified name is not present
  .paymentinstrument.PaymentInstrumentState state = 11;

  // account id to which the account<>pi resolution has to be created
  string account_id = 12;

  // type of account
  accounts.Type account_type = 13;

  // Account Product Offering associated with the AccountType.
  // If not passed, APO_REGULAR will be used as a default value.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
  api.typesv2.account.AccountProductOffering apo = 15;
}

message CreatePiAndAccountPiResponse {
  enum Status {
    // success response
    OK = 0;
    INVALID_ARGUMENT = 3;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // returns the created/already existed pi
  PaymentInstrument pi = 2;
}
