syntax = "proto3";

package paymentinstrument;

import "api/accounts/account_type.proto";
import "api/typesv2/account/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/paymentinstrument/account_pi";
option java_package = "com.github.epifi.gamma.api.paymentinstrument.account_pi";

// AccountPi is relationship table between account <> PI
message AccountPI {
  string id = 1;

  // Reference to actor id from actor service
  string actor_id = 2;

  // Account ID for one of the accounts service eg. savings
  string account_id = 3;

  // Type of the account eg. savings
  accounts.Type account_type = 4;

  // Reference to PI id from PI service
  string pi_id = 5;

  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;

  // Account Product Offering associated with the AccountType.
  // Retrieval: This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
  // Storage: If not specified, APO_REGULAR will be used as default.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
  api.typesv2.account.AccountProductOffering apo = 9;
}
