syntax = "proto3";

package paymentinstrument;

import "api/payment_instruments/account_pi/account_pi.proto";
import "api/payment_instruments/payment_instrument.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/paymentinstrument/account_pi";
option java_package = "com.github.epifi.gamma.api.paymentinstrument.account_pi";

enum PiEventType {
  UNSPECIFIED = 0;
  CREATE = 1;
  UPDATE = 2;
}

// `AccountPiCreateOrUpdateEvent` published via sns, when
// a new account-pi is created
message AccountPiCreateOrUpdateEvent {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  AccountPI account_pi = 2;

  PaymentInstrument pi = 3;

  PiEventType event_type = 4;
}
