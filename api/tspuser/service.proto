syntax = "proto3";

package tspuser;

import "api/rpc/status.proto";
import "api/tspuser/tsp_user.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/name.proto";

option go_package = "github.com/epifi/gamma/api/tspuser";
option java_package = "com.github.epifi.gamma.api.tspuser";

service TspUserService {
  // Registers a new external user and returns the user identifier
  rpc CreateTspUser(CreateTspUserRequest) returns (CreateTspUserResponse);

  // Get tsp user details
  rpc GetTspUser(GetTspUserRequest) returns (GetTspUserResponse);
}

message CreateTspUserRequest {
  string external_cust_ref_id = 1;
  api.typesv2.common.PhoneNumber phone_number = 2;
  string email = 3;
  api.typesv2.common.Owner owner = 4;
  // customer's name passed by the RE to TSP
  api.typesv2.common.Name name = 5;
}

message CreateTspUserResponse {
  rpc.Status status = 1;
  TspUser user = 2;
}

message GetTspUserRequest {
  api.typesv2.common.Owner owner = 1;
  oneof identifier {
    string tsp_user_id = 2;
    string external_cust_ref_id = 3;
  }
}

message GetTspUserResponse {
  rpc.Status status = 1;
  TspUser tsp_user = 2;
}
