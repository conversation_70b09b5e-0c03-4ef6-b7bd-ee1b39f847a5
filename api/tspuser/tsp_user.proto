//go:generate gen_sql -types=TspUser,Details,PersonalDetails
syntax = "proto3";

package tspuser;

import "google/protobuf/timestamp.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/name.proto";

option go_package = "github.com/epifi/gamma/api/tspuser";
option java_package = "com.github.epifi.gamma.api.tspuser";

message TspUser {
  string id = 1;
  string external_cust_ref_id = 2; // External customer reference id
  PersonalDetails personal_details = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  google.protobuf.Timestamp deleted_at = 6;
  api.typesv2.common.Owner owner = 7;
}

message PersonalDetails {
  api.typesv2.common.PhoneNumber phone_number = 1;
  string email = 2;
  // customer's name passed by the RE to TSP
  api.typesv2.common.Name name = 3;
}

