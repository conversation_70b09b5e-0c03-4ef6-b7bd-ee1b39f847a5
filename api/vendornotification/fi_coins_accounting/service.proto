syntax = "proto3";

package vendornotification.fi_coins_accounting;

import "api/vendors/fi_coins_accounting/fi_coins_accounting.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/fi_coins_accounting";
option java_package = "com.github.epifi.gamma.api.vendornotification.fi_coins_accounting";

// FiCoinsAccounting service to serve different APIs needed for interacting with internal fi-coins accounting system
service FiCoinsAccounting {
  // RPC to get the fi coins balance of the actor
  rpc GetUserFiCoinsBalance (vendors.fi_coins_accounting.GetUserFiCoinsBalanceRequest) returns (vendors.fi_coins_accounting.GetUserFiCoinsBalanceResponse) {
    option (google.api.http) = {
      get: "/fi-coins-accounting/balance"
    };
  }
  // RPC to make debit/credit txn for an external vendor redemption order using fi coins
  rpc TransactFiCoins (vendors.fi_coins_accounting.TransactFiCoinsRequest) returns (vendors.fi_coins_accounting.TransactFiCoinsResponse) {
    option (google.api.http) = {
      post: "/fi-coins-accounting/transact"
      body: "*"
    };
  }
  // RPC to get vendor user id from given phone number.
  rpc GetUserIdFromPhoneNumber (vendors.fi_coins_accounting.GetUserIdFromPhoneNumberRequest) returns (vendors.fi_coins_accounting.GetUserIdFromPhoneNumberResponse) {
    option (google.api.http) = {
      get: "/fi-coins-accounting/user-id"
    };
  };
}
