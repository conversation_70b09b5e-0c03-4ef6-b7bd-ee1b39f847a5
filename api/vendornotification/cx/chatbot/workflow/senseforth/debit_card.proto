syntax = "proto3";

package vendornotification.cx.chatbot.workflow.senseforth;

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot.workflow.senseforth";

// DebitCardTrackingData: data returned by debit card tracking entity
message DebitCardTrackingData {
  // card delivery state
  string card_tracking_delivery_state = 1 [json_name = "card_tracking_delivery_state"];
  // card activation state
  string card_activation_state = 2 [json_name = "card_activation_state"];
  // card creation timestamp
  string card_created_at = 3 [json_name = "card_created_at"];
  // awb number of the shipment
  string awb_number = 4 [json_name = "awb_number"];
  // courier partner of the shipment
  string courier_partner = 5 [json_name = "courier_partner"];
}

// DebitCardTrackingParameters is the input params required to fetch the debit card tracking data
message DebitCardTrackingParameters {}
