syntax = "proto3";

package vendornotification.cx.chatbot.workflow.senseforth;

import "google/api/annotations.proto";
import "api/vendornotification/cx/chatbot/workflow/senseforth/messages.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot.workflow.senseforth";

// service to receive requests from the senseforth chat bot for core workflows like data exchange and execute actions
service ChatBotWorkflow {
  // FetchData rpc to fetch data from backend db
  // request accepts mandatory short token and api key in http header
  // entity and fetch parameters in request body
  // response contains status and data objects which are fetched from backend
  // status - int [0 for OK, anything other than 0 is not OK]
  rpc FetchData (FetchDataRequest) returns (FetchDataResponse) {
    option (google.api.http) = {
      get: "/cx/chatbot/senseforth/fetch-data"
    };
  }

  // ExecuteAction rpc to execute action which are requested from chat-bot
  // Request accepts short_token, action type and action params
  // response contains rpc status and action response if any relevant for the given action
  // status - int [0 for OK, anything other than 0 is not OK]
  // Ideally we should be using POST body params for input, but Senseforth's a.ware platform is not supporting body params as of yet
  // Hence we have to pass even jsons through query params on a.ware platform
  // For details on why we chose to add additional_binding even though POST
  // supports query params, Refer to https://github.com/googleapis/googleapis/blob/b4bb0e2e2473016fedf9f8179db8cedad0b3ca5d/google/api/http.proto#L44
  rpc ExecuteAction (ExecuteActionRequest) returns (ExecuteActionResponse) {
    option (google.api.http) = {
      post: "/cx/chatbot/senseforth/execute-action"
      body: "*"
      // Reason for adding GET as additional binding: If we pass query params in POST,
      // senseforth a.ware platform is converting it into body that too json fileds are getting dropped.
      // So we can use GET with query params for now and once Senseforth fixes their issues we can use POST.
      // Hence choosing to have both GET and POST due to limitations of A.ware [to avoid dev rework if something changes on A.ware platform]
      additional_bindings: {
        get: "/cx/chatbot/senseforth/execute-action"
      }
    };
  }
}

message FetchDataRequest {
  // mandatory entity: faq, debit card, transactions, etc
  string entity = 1 [json_name = "entity"];
  // mandatory fetch_data_parameters: entity specific params
  FetchDataParameters fetch_data_parameters = 2 [json_name = "parameters"];
  // short token is used to identify the user
  string short_token = 3 [json_name = "short_token"];
}

message FetchDataResponse {
  // status - int [100 for OK, anything other than 100 is not OK]
  int64 status = 1 [json_name = "status"];
  // workflow_data: entity specific data
  WorkflowData workflow_data = 2 [json_name = "data"];
}

message ExecuteActionRequest {
  // mandatory action: create ticket, raise dispute, etc
  string action = 1 [json_name = "action"];
  // execute_action_parameters: action specific params
  ExecuteActionParams execute_action_params = 2 [json_name = "params"];
  // short token is used to identify the user
  string short_token = 3 [json_name = "short_token"];
}

message ExecuteActionResponse {
  // status - int [100 for OK, anything other than 100 is not OK]
  int64 status = 1 [json_name = "status"];
  // action_result: relevant for actions where some
  // data is expected once action execution is complete
  ActionResult action_result = 2 [json_name = "action_result"];
}
