syntax = "proto3";

package vendornotification.cx.chatbot.workflow.senseforth;

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot.workflow.senseforth";


// UserDetailsData: data returned for User details entity
message UserDetailsData {
  // KYC level of the user
  // deprecated, do not use. Use vkyc_status instead. [Can be removed once ops team moves away from using this]
  string kyc_level = 1 [json_name = "kyc_level", deprecated = true];
  // Video KYC status of the user
  string vkyc_status = 2 [json_name = "vkyc_status"];
  // number of days left for the user to finish vkyc
  int64 days_left_to_complete_vkyc = 3 [json_name = "days_left_to_complete_vkyc", deprecated = true];
  // date before which user is allowed to complete vkyc
  string vkyc_completion_deadline_date = 4 [json_name = "vkyc_completion_deadline_date"];
  // date at which user's account will be closed if vkyc is not completed
  string account_closure_date = 5 [json_name = "account_closure_date"];
  // the duration in days for which the vkyc record is in review
  int64 vkyc_review_duration_in_days = 6 [json_name = "vkyc_review_duration_in_days"];
}

// UserDetailsParameters is the input params required to fetch the required user details
message UserDetailsParameters {
}
