syntax = "proto3";

package vendornotification.cx.chatbot.workflow.senseforth;

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot.workflow.senseforth";

// BalanceRefreshParams contains parameters needed to be passed to chatbot workflow inorder to extract refreshed balance for the user.
message BalanceRefreshParams{}

// BalanceRefreshData: data returned for cx.chat.bot.workflow.WORKFLOW_ENTITY_REFRESH_BALANCE
message BalanceRefreshData{
  string refreshed_balance = 1 [json_name = "refreshed_balance"];
}
