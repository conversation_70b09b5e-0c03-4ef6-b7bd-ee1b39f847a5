syntax = "proto3";

package vendornotification.cx.chatbot.workflow.senseforth;

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot/workflow/senseforth";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot.workflow.senseforth";

// faq article object which has to be returned to calling client
message Article {
  // title of the category
  string title = 1 [json_name="title"];
  // description of the article
  string description = 2 [json_name="description"];
}

// data which has to be returned to client
message FaqData {
  // list of article objects
  repeated Article article_list = 1 [json_name="article_list"];
}

// FaqParameters specifies list of article ids which has to be accepted
message FaqParameters {
  repeated int64 article_id_list = 1 [json_name="article_id_list"];
}
