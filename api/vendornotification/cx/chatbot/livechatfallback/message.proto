syntax = "proto3";

package vendornotification.cx.chatbot.livechatfallback;

import "api/vendors/freshchat/conversation.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/cx/chatbot/livechatfallback";
option java_package = "com.github.epifi.gamma.api.vendornotification.cx.chatbot.livechatfallback";

// The event data specific to a event_type.
// Only one of the fields should be populated. Not using oneof to preserve json nesting
message EventData {
  // data for conversation_assignment event
  ConversationAssignmentEventData conversation_assignment = 1 [json_name = "conversation_assignment"];
  // data for agent_reply event
  AgentReplyEventData agent_reply = 2 [json_name = "agent_reply"];
  // data for conversation_resolution event
  ConversationResolutionEventData conversation_resolution = 3 [json_name = "conversation_resolution"];
}

message ConversationAssignmentEventData {
  // the conversation id received from Freshchat.
  // To be used by vendor to identify which conversation the agent has responded
  // The conversation_id value is returned to the vendor as part of the response to a successful create_conversation/send_message request.
  string conversation_id = 1 [json_name = "conversation_id"];
  // The agent's name to whom this conversation as assigned to (to be displayed in the chat)
  string agent_name = 2 [json_name = "agent_name"];
}

message AgentReplyEventData {
  // the conversation id received from Freshchat.
  // To be used by vendor to identify which conversation the agent has responded
  // The conversation_id value is returned to the vendor as part of the response to a successful create_conversation/send_message request.
  string conversation_id = 1 [json_name = "conversation_id"];
  // The details of the message sent by the agent
  Message message = 3 [json_name = "message"];
  // The agent's name to be displayed in the chat
  string agent_name = 4 [json_name = "agent_name"];
}

message ConversationResolutionEventData {
  // the conversation id received from Freshchat.
  // To be used by vendor to identify which conversation the agent has responded
  // The conversation_id value is returned to the vendor as part of the response to a successful create_conversation/send_message request.
  string conversation_id = 1 [json_name = "conversation_id"];
  // The agent's name to be displayed in the chat
  string agent_name = 2 [json_name = "agent_name"];
}

message Message {
  // Identifier of the message, auto-generated when a message is successfully posted to a conversation.
  // This should not be passed by Chat Bot in the request
  string message_id = 1 [json_name = "message_id"];
  // Different parts of a message posted to the conversation - plain text, images, or url buttons. The message can be a combination of these attributes.
  repeated vendors.freshchat.MessagePart message_parts = 2 [json_name = "message_parts"];
  // Specifies whether the message posted to the conversation is a private message.
  // Possible values (enum): normal, private
  string message_type = 3 [json_name = "message_type"];
  // Timestamp of when the message is posted, specified in the date-time format.
  // The timestamp is returned as part of the response conversation object, when a message is successfully posted.
  string created_time = 4 [json_name = "created_time"];
}
