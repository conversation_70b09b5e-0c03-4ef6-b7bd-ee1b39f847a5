// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.comms.unsubscribe;

import "google/protobuf/empty.proto";
import "api/vendors/unsubscribe/unsubscribe.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/comms/unsubscribe";
option java_package = "com.github.epifi.gamma.api.vendornotification.comms.unsubscribe";

// service to receive unsubscribe callbacks
service Unsubscribe {
  // fetches and returns list of supported areas with user subscription status.
  rpc GetCommsAreaSubscriptionStatusForUser(vendors.unsubscribe.GetCommsAreaSubscriptionStatusForUserRequest) returns (vendors.unsubscribe.GetCommsAreaSubscriptionStatusForUserResponse) {
    option (google.api.http) = {
      get: "/email/unsubscribe/areas"
    };
  }
  // unsubscribes emails from given areas.
  rpc EmailUnsubscribe(vendors.unsubscribe.EmailUnsubscribeRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/email/unsubscribe"
      body: "*"
    };
  }
}
