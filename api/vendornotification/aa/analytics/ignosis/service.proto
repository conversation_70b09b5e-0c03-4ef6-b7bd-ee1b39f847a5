syntax = "proto3";

package vendornotification.aa.analytics.ignosis;

import "api/vendors/aa/analytics/ignosis/pfm.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/aa/analytics/ignosis";
option java_package = "com.github.epifi.gamma.api.vendornotification.aa.analytics.ignosis";

service IgnosisAaAnalyticsNotification {
  // to receive the status callback for the detailed Analysis Response.
  rpc ProcessAnalysisStatusCallback (vendors.aa.analytics.ignosis.DetailedAnalysisStatusResponse) returns (vendors.aa.analytics.ignosis.CallbackResponse) {
    option (google.api.http) = {
      post: "/fi/aa/analytics/ignosis/detailedAnalysisStatus/notification"
      body: "*"
   };
  }
}

