syntax = "proto3";

package vendornotification.creditcard;

import "api/vendors/m2p/lending/credit_card.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/creditcard";
option java_package = "com.github.epifi.gamma.api.vendornotification.creditcard";

service CreditCard {
  rpc CardNotification (vendors.m2p.lending.CardNotificationRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/cc/m2p"
      body: "*"
    };
  }

  rpc AcsNotification (vendors.m2p.lending.AcsNotificationRequest) returns (vendors.m2p.lending.AcsNotificationResponse) {
    option (google.api.http) = {
      post: "/cc/m2p-acs"
      body: "*"
    };
  };
}
