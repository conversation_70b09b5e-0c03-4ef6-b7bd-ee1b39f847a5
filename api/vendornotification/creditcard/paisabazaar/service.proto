syntax = "proto3";

package vendornotification.creditcard.paisabazaar;

import "api/vendors/paisabazaar/onboarding_status.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/creditcard/paisabazaar";
option java_package = "com.github.epifi.gamma.api.vendornotification.creditcard.paisabazaar";

service PaisabazaarCallback {
  rpc GetCreditCardOnboardingStatus (vendors.paisabazaar.GetCreditCardOnboardingStatusRequest) returns (vendors.paisabazaar.GetCreditCardOnboardingStatusResponse){
    option (google.api.http) = {
      post: "/paisabazaar/onboarding"
      body: "*"
    };
  }
}
