// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.lending.bre.inhouse;

import "google/api/annotations.proto";
import "google/protobuf/struct.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/lending/bre/inhouse";
option java_package = "com.github.epifi.gamma.api.vendornotification.lending.bre.inhouse";

//  BRE service for providing rest-based interfaces for all inhouse bre related functionalities
service Bre {
  // GetFeaturesData rpc to fetch data related to the list of features sent in the request
  // Rpc uses feature store sdk to get the data
  // Deprecated : In favour of api.inhouse.GetFeaturesData
  rpc GetFeaturesData (GetFeaturesDataRequest) returns (GetFeaturesDataResponse) {
    option (google.api.http) = {
      post: "/lending/bre/inhouse/getFeaturesData"
      body: "*"
    };
  }
}

message GetFeaturesDataRequest {
  // features list for which data needs to be fetched from feature store
  // NOTE : feature store currently only supports fetching data for features which have same set of request identifiers
  // Example : Say a feature F1 has actorId and model name as request identifier, F2 has actorId as request identifier and F3 has actorId and
  // model name as request identifier, then in a single api call we can either support [F2] or [F1,F3].
  repeated string feature_name_list = 1 [json_name = "FeatureNameList"];
  // request identifiers containing list of Identifiers for each request
  // we will evaluate the above feature list for each request identifier
  // Request Identifiers should have same set of list of identifiers for each request as mentioned above
  repeated RequestIdentifiers request_identifiers_list = 2 [json_name = "RequestIdentifiersList"];
}

message RequestIdentifiers {
  // Identifiers for which data needs to be fetched
  // Currently the supported identifiers are actorId, accountId or Model Name and combination of the same
  Identifiers identifiers = 2 [json_name = "Identifiers"];
}

message GetFeaturesDataResponse {
  // list of features data for identifiers sent in the request
  repeated FeaturesResponseData features_response_data_list = 1 [json_name = "FeaturesResponseDataList"];
}

message FeaturesResponseData {
  // Identifiers sent in the request
  Identifiers identifiers = 1 [json_name = "Identifiers"];
  // List of features value for the identifier
  map<string, google.protobuf.Value> feature_value_map = 2 [json_name = "FeatureValueMap"];
}

// Identifiers for which data is fetched from feature store
// Based on the feature we can have any combination of these identifiers
message Identifiers {
    // actor Id
    string actor_id = 1 [json_name = "ActorId"];
    // account Id
    string account_id = 2 [json_name = "AccountId"];
    // Model name
    string model_name = 3 [json_name = "ModelName"];
}
