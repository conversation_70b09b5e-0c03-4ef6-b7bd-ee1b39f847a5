syntax = "proto3";

package vendornotification.lending.loans.moneyview;

import "google/protobuf/empty.proto";
import "api/vendors/moneyview/notification.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/lending/loans/moneyview";
option java_package = "com.github.epifi.gamma.api.vendornotification.lending.loans.moneyview";

// Moneyview callback service to process callbacks from moneyview vendor
service MoneyviewCallback {
  // This method will receive all the type of notifications from moneyview vendor like notifications for loan application rejection, loan disbursal, loan account closure etc.
  rpc ProcessLoanNotification(vendors.moneyview.RawVendorLoanNotificationPayload) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/lending/loans/moneyview/notification"
      body: "*"
    };
  }
}
