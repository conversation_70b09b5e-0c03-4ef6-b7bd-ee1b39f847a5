syntax = "proto3";

package vendornotification.whatsapp.acl;

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/whatsapp/acl";
option java_package = "com.github.epifi.gamma.api.vendornotification.whatsapp.acl";

// service to receive whatsapp callbacks from ACL
service WhatsappCallback {
  rpc AclWhatsappDLR (AclWhatsappDLRRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/whatsapp/callback/acl/UrlListner/requestListener"
      body: "*"
   };
  }
  rpc AclWhatsappReply (AclWhatsappReplyRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/whatsapp/reply/acl/UrlListner/requestListener"
      body: "*"
   };
  }
}

// All these values will be received as part of query parameters from ACL
message AclWhatsappDLRRequest {
  string rqst_ack_id = 1 [json_name = "rqst_ack_id"];
  string mobile_number = 2 [json_name = "phone_number"];
  string del_status = 3 [json_name = "del_status"];
  string del_time = 4 [json_name = "del_time"];
  string del_desc = 5 [json_name = "del_desc"];
}

message AclWhatsappReplyRequest {
  ReplyMessage message = 1[json_name="message"];
}

message ReplyMessage {
  // customer phone number who replied on whatsapp
  string from = 1[json_name="from"];

  // unique id for each reply
  string id = 2[json_name="id"];

  // type
  string type = 3[json_name="type"];

  // enterprise phone number
  string to = 4[json_name="to"];

  // timestamp at which message was sent
  int32 timestamp = 5[json_name="timestamp"];

  TextMessage text = 6[json_name="text"];
}

message TextMessage {
  string body = 1[json_name="body"];
}
