// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.kyc.axis;

import "api/vendors/axis/kyc.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/kyc/axis";
option java_package = "com.github.epifi.gamma.api.vendornotification.kyc.axis";

service Ekyc {
  rpc ProcessEkycCallBack (vendors.axis.EkycCallBackResponse) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/kyc/axis/response"
      body: "*"
    };
  }
}
