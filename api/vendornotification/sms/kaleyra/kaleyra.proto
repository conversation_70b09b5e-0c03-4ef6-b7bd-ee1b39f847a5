// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.sms.kaleyra;

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "api/vendors/kaleyra/kaleyra.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/sms/kaleyra";
option java_package = "com.github.epifi.gamma.api.vendornotification.sms.kaleyra";

// service to receive sms callbacks from ACL
service KaleyraCallback {
  rpc KaleyraSmsDLR (KaleyraSmsDLRRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/sms/callback/kaleyra/UrlListner/requestListener"
    };
  }

  rpc KaleyraIOSmsDLR (vendors.kaleyra.KaleyraIOSmsDLRRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/sms/callback/kaleyraIO/RealTimeDLR/requestListener"
      body: "*"
    };
  }
}

// These values will be received as part of query parameters from Kaleyra
// The following fields are available from the vendor. The fields must be passed in dlrurl to get the data.
// Ref: https://messaging.kaleyra.com/support/solutions/articles/3000091767-delivery-url-for-sms-api
// {status} SMS status code
// {sid} MessageID generated by us
// {custom} ReferenceID provided for the sms
// {custom1} Custom Flag 1 associated with the message
// {custom2}	Custom Flag 2 associated with the message
// {credits} Credits charged for the Sms
// {senttime} Sent to Operator time in YYYY-MM-DD HH:mm:SS format
// {submittime} Request receive time in YYYY-MM-DD HH:mm:SS format
// {delivered} Delivered time in YYYY-MM-DD HH:mm:SS format
// {sentat} Sent to Operator in unixtime format
// {delivat} Delivered time in unixtime format
// {submitat} Request Receive time in unixtime format
// {mobile} Mobile Number
// {units} No of Message Units
message KaleyraSmsDLRRequest {
  string reference_id = 1 [json_name = "reference_id"];
  string message_id = 2 [json_name = "message_id"];
  repeated string status = 3 [json_name = "status"];
  string submit_time = 4 [json_name = "submit_time"];
  string sent_time = 5 [json_name = "sent_time"];
  string delivered_time = 6 [json_name = "delivered_time"];
  string mobile_number = 7 [json_name = "mobile_number"];
  // Custom is not used now. May be used for comms message Id if required.
  string custom = 8 [json_name = "custom"];
  // number of message units charged for a single sms.
  // for sms if the count of characters is less than 160, then it is charged as single sms only
  // but if count is between 160 and 305 (2*153) characters then it is count as two sms,
  // similarly based on count of the characters increases, charges are also applied correspondingly.
  string sms_count = 9 [json_name = "sms_count"];
  repeated string status_trace = 10 [json_name = "status_trace"];
}
