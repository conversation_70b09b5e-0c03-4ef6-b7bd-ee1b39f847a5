syntax = "proto3";

package vendornotification.card.shipway;

import "api/vendors/shipway/shipway.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/card/shipway";
option java_package = "com.github.epifi.gamma.api.vendornotification.card.shipway";

service Shipway {
  // We receive callbacks for the tracking updates of each shipment registered at Shipway.
  rpc CardTrackingCallbacks (vendors.shipway.CardTrackingCallbacksRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/card/shipway"
      body: "*"
    };
  }
}
