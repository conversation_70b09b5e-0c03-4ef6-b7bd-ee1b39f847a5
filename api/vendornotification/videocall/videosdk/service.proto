syntax = "proto3";

package vendornotification.videocall.videosdk;

import "api/vendors/vkyccall/videosdk/videosdk.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/videocall/videosdk";
option java_package = "com.github.epifi.gamma.api.vendornotification.videocall.videosdk";


service VideoSdkCallback {
  rpc ParticipantLeftEvent (vendors.vkyccall.videosdk.PariticipantLeftEventRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/videocall/videosdk"
      body: "*"
    };
  }
}
