syntax = "proto3";

package vendornotification.rcs.gupshup;

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "api/vendors/gupshup/gupshup.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/rcs/gupshup";
option java_package = "com.github.epifi.gamma.api.vendornotification.rcs.gupshup";

// service to receive whatsapp callbacks from gupshup
service GupshupRcsCallback {
  rpc GupshupRcsDLR (vendors.gupshup.GupshupRcsDLRRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/rcs/callback/gupshup/RealTimeDLR/requestListener"
      body: "*"
    };
  }
}
