// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.openbanking.shipping_preference.federal;

import "google/protobuf/empty.proto";
import "google/api/annotations.proto";
import "api/vendors/federal/shipping_preference.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/openbanking/shipping_preference/federal";
option java_package = "com.github.epifi.gamma.api.vendornotification.openbanking.shipping_preference.federal";

service ShippingPreference {
  rpc ProcessUpdateShippingAddressCallBack(vendors.federal.UpdateShippingAddressCallBackRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/openbanking/shipping_preference/federal"
      body: "*"
    };
  }
}
