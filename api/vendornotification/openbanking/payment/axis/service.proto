// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.openbanking.payment.axis;

import "api/vendors/axis/payment.proto";
import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/openbanking/payment/axis";
option java_package = "com.github.epifi.gamma.api.vendornotification.openbanking.payment.axis";

service Payment {
  rpc ProcessPaymentCallBack (vendors.axis.PaymentCallBackResponse) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/openbanking/payment/axis"
      body: "*"
    };
  }
}
