syntax = "proto3";

option go_package = "github.com/epifi/gamma/api/vendornotification/openbanking/dispute";

message AttachCorrespondenceRequest {
  // dispute case number uniquely identifies a raised dispute in Federal's DMP system
  string dispute_case_number = 1 [json_name = "DisputeCaseNum<PERSON>"];
  // correspondence text
  string correspondence_text = 2 [json_name = "CorrespondenceText"];
  // agent email
  string agent_email = 3 [json_name = "AgentEmail"];
}

message AttachCorrespondenceResponse {
  string status_code = 1 [json_name = "StatusCode"];
}

// REFRENCE: https://docs.google.com/document/d/1x0qoKQo16u02-5hoZAXTeGfJMGT7eZw6hcHv6YVivKY
enum StatusCode {
  STATUS_CODE_UNSPECIFIED = 0;
  STATUS_CODE_INTERNAL_ERROR = 1;
  STATUS_CODE_SUCCESS = 2;
  STATUS_CODE_VALIDATION_FAIL = 3;
  STATUS_CODE_DISPUTE_CASE_NUMBER_NOT_FOUND = 4;
}
