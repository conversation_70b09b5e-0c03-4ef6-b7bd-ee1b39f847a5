// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package vendornotification.paymentgateway.razorpay;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/paymentgateway/razorpay";
option java_package = "com.github.epifi.gamma.api.vendornotification.paymentgateway.razorpay";

service RazorpayPaymentGateway {
  rpc ReceiveRazorpayEvent (ReceiveRazorpayEventRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/paymentgateway/razorpay/event"
      body: "*"
    };
  }
}

message ReceiveRazorpayEventRequest {
  string entity = 1 [json_name = "entity"];
  string account_id = 2 [json_name = "account_id"];
  string event = 3 [json_name = "event"];
  repeated string entities_contained = 4 [json_name = "contains"];
  Payload payload = 5 [json_name = "payload"];
  int64 created_at = 6 [json_name = "created_at"];
}

// Payload field contains the details of one or more entities received in the event callback.
// Please note that some of the fields may be empty/nil, based on the type of event we receive from razorpay
// and the entities_contained field stores the entities that are populated.
// Add more entities as required to determine the status of other events.
// Ref doc: https://razorpay.com/docs/api/payments/recurring-payments/webhooks
message Payload {
  Payment payment = 1 [json_name = "payment"];
  Order order = 2 [json_name = "order"];
  Token token = 3 [json_name = "token"];
}

message Payment {
  PaymentEntity payment_entity = 1 [json_name = "entity"];
}

message Order {
  OrderEntity order_entity = 1 [json_name = "entity"];
}

message Token {
  TokenEntity token_entity = 1 [json_name = "entity"];
}

// Also maintained at api/vendors/razorpay.Payment
// Maintaining separate messages for requests and webhooks even-though they have almost similar fields, so that
// they can be modified independent of each other, later on if there are any changes to the API contract.
message PaymentEntity {
  string id = 1 [json_name = "id"];
  string entity = 2 [json_name = "entity"];
  int64 amount = 3 [json_name = "amount"];
  string currency = 4 [json_name = "currency"];
  string status = 5 [json_name = "status"];
  string order_id = 6 [json_name = "order_id"];
  string invoice_id = 7 [json_name = "invoice_id"];
  bool international = 8 [json_name = "international"];
  string method = 9 [json_name = "method"];
  int64 amount_refunded = 10 [json_name = "amount_refunded"];
  string refund_status = 11 [json_name = "refund_status"];
  bool captured = 12 [json_name = "captured"];
  string description = 13 [json_name = "description"];
  string card_id = 14 [json_name = "card_id"];
  string bank = 15 [json_name = "bank"];
  string wallet = 16 [json_name = "wallet"];
  string vpa = 17 [json_name = "vpa"];
  string email = 18 [json_name = "email"];
  string contact = 19 [json_name = "contact"];
  string customer_id = 20 [json_name = "customer_id"];
  string token_id = 21 [json_name = "token_id"];
  int64 fee = 22 [json_name = "fee"];
  int64 tax = 23 [json_name = "tax"];
  string error_code = 24 [json_name = "error_code"];
  string error_description = 25 [json_name = "error_description"];
  string error_source = 26 [json_name = "error_source"];
  string error_step = 27 [json_name = "error_step"];
  string error_reason = 28 [json_name = "error_reason"];
  AcquirerData acquirer_data = 29 [json_name = "acquirer_data"];
  int64 created_at = 30 [json_name = "created_at"];
}

message AcquirerData {
  string rrn = 1 [json_name = "rrn"];
  string bank_transaction_id = 2 [json_name = "bank_transaction_id"];
  string auth_code = 3 [json_name = "auth_code"];
  string auth_ref_number = 4 [json_name = "authentication_reference_number"];
}

message OrderEntity {
  string id = 1 [json_name = "id"];
  string entity = 2 [json_name = "entity"];
  int64 amount = 3 [json_name = "amount"];
  int64 amount_paid = 4 [json_name = "amount_paid"];
  int64 amount_due = 5 [json_name = "amount_due"];
  string currency = 6 [json_name = "currency"];
  string receipt = 7 [json_name = "receipt"];
  string offer_id = 8 [json_name = "offer_id"];
  string status = 9 [json_name = "status"];
  int32 attempts = 10 [json_name = "attempts"];
  int64 created_at = 11 [json_name = "created_at"];
  OrderTokenDetails token = 12 [json_name = "token"];
}

// OrderTokenDetails represents a subset of the data of the token entity which is received with the order payload.
message OrderTokenDetails {
  string method = 1 [json_name = "method"];
  string recurring_status = 2 [json_name = "recurring_status"];
  string failure_reason = 3 [json_name = "failure_reason"];
  string currency = 4 [json_name = "currency"];
  int64 max_amount = 5 [json_name = "max_amount"];
  string auth_type = 6 [json_name = "auth_type"];
  int64 expire_at = 7 [json_name = "expire_at"];
  BankAccount bank_account = 8 [json_name = "bank_account"];
  int64 first_payment_amount = 9 [json_name = "first_payment_amount"];
}

message BankAccount {
  string ifsc = 1 [json_name = "ifsc"];
  string bank_name = 2 [json_name = "bank_name"];
  string name = 3 [json_name = "name"];
  string account_number = 4 [json_name = "account_number"];
  string account_type = 5 [json_name = "account_type"];
  string beneficiary_email = 6 [json_name = "beneficiary_email"];
  string beneficiary_mobile = 7 [json_name = "beneficiary_mobile"];
}

// Also maintained at api/vendors/razorpay.TokenDetails
// Maintaining separate messages for requests and webhooks even-though they have almost similar fields, so that
// they can be modified independent of each other, later on if there are any changes to the API contract.
message TokenEntity {
  string id = 1 [json_name = "id"];
  string entity = 2 [json_name = "entity"];
  string token = 3 [json_name = "token"];
  string bank = 4 [json_name = "bank"];
  string wallet = 5 [json_name = "wallet"];
  string method = 6 [json_name = "method"];
  bool recurring = 7 [json_name = "recurring"];
  RecurringDetails recurring_details = 8 [json_name = "recurring_details"];
  string auth_type = 9 [json_name = "auth_type"];
  string mrn = 10 [json_name = "mrn"];
  int64 used_at = 11 [json_name = "used_at"];
  int64 created_at = 12 [json_name = "created_at"];
  BankDetails bank_details = 13 [json_name = "bank_details"];
  int64 max_amount = 14 [json_name = "max_amount"];
  int64 expired_at = 15 [json_name = "expired_at"];
  bool dcc_enabled = 16 [json_name = "dcc_enabled"];
}

message RecurringDetails {
  string status = 1 [json_name = "status"];
  string failure_reason = 2 [json_name = "failure_reason"];
}

message BankDetails {
  string beneficiary_name = 1 [json_name = "beneficiary_name"];
  string account_number = 2 [json_name = "account_number"];
  string ifsc = 3 [json_name = "ifsc"];
  string account_type = 4 [json_name = "account_type"];
}

message ProcessAuthorisationPaymentCallbackRequest {
  // razorpay_order_id represents the mandate registration order created on Razorpay side
  string razorpay_order_id = 1;
  // razorpay_payment_id represents the payment id created on Razorpay side,
  // that is used to perform the authorization payment.
  string razorpay_payment_id = 2;
  string razorpay_signature = 3;
}
