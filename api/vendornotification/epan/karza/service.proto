syntax = "proto3";

package vendornotification.epan.karza;

import "api/vendors/karza/epan.proto";
import "google/protobuf/empty.proto";
import "google/api/annotations.proto";

option go_package = "github.com/epifi/gamma/api/vendornotification/epan/karza";
option java_package = "com.github.epifi.gamma.api.vendornotification.epan.karza";

// service to receive the data shared on a callback API provided by the client
service EPAN {
  // RPC to receive the data shared on a callback API provided by the client, Once the ePAN is downloaded,.
  rpc ProcessEPANEventCallback (vendors.karza.ProcessEPANEventCallbackResponse) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/epan/karza/response"
      body: "*"
    };
  }
}
