syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// MultiChoiceSelectType indicates the type of choice selection for a multiple choice question
// can be used in cases like - https://www.figma.com/file/QJ8rRcRoCTHPgf29W6oslE/%F0%9F%9A%80--Mutual-Funds-v1.1?node-id=14687-51349&t=1JDpI2k4VouE2qyL-0
enum MultiChoiceSelectType {
  MULTI_CHOICE_SELECT_TYPE_UNSPECIFIED = 0;
  // only single answer can be selected
  MULTI_CHOICE_SELECT_TYPE_SINGLE_SELECT = 1;
  // multiple answers can be selected
  MULTI_CHOICE_SELECT_TYPE_MULTI_SELECT = 2;
}
