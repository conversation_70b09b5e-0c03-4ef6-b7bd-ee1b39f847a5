// go:generate gen_sql -typesv2=SalaryRange
syntax = "proto3";

package api.typesv2;

import "api/typesv2/money.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

message SalaryRange {
  // MinValue is the lower limit of the salary range
  typesv2.Money min_value = 1;

  // MaxValue is the upper limit of the salary range
  typesv2.Money max_value = 2;
}
