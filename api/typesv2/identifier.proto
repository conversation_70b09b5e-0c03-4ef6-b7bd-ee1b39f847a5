syntax = "proto3";

package api.typesv2;

import "google/type/latlng.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

message Identifier {
  IdentifierType id_type = 1;
  IdentifierValue id_value = 2;
}

// IdentifierToken represents obfuscated value of an identifier
message IdentifierToken {
  string token = 1;
}

// Represents a user identifier type - such as IP_Address, Location etc.
enum IdentifierType {
  IDENTIFIER_TYPE_UNSPECIFIED = 0;
  IDENTIFIER_TYPE_IP_ADDRESS = 1;
  // Represents location token
  IDENTIFIER_TYPE_LOCATION = 2;
  // Represents pan number
  IDENTIFIER_TYPE_PAN = 3;
}

message IdentifierValue {
  oneof prop_value {
    // Value for IDENTIFIER_TYPE_LOCATION
    google.type.LatLng location = 1;
    // Value for IDENTIFIER_TYPE_IP_ADDRESS
    IpAddress ip_address = 2;
    // Value for IDENTIFIER_TYPE_PAN
    string pan = 3;
  }
}

message IpAddress {
  string ip_address = 1;
}
