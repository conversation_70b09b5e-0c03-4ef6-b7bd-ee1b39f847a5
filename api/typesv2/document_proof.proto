syntax = "proto3";

package api.typesv2;

import "api/typesv2/common/image.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// Type of Proof corresponding to a user
enum DocumentProofType {
  DOCUMENT_PROOF_TYPE_UNSPECIFIED = 0;
  DOCUMENT_PROOF_TYPE_PASSPORT = 1;
  DOCUMENT_PROOF_TYPE_VOTER_ID = 2;
  DOCUMENT_PROOF_TYPE_PAN = 3;
  DOCUMENT_PROOF_TYPE_DRIVING_LICENSE = 4;
  DOCUMENT_PROOF_TYPE_UID = 5;  // Aadhaar number
  DOCUMENT_PROOF_TYPE_NREGA_JOB_CARD = 6;
  DOCUMENT_PROOF_TYPE_NATIONAL_POPULATION_REGISTER_LETTER = 7;
  DOCUMENT_PROOF_TYPE_CKYC_RECORD = 8;
  DOCUMENT_PROOF_TYPE_PHOTOGRAPH = 9;
  DOCUMENT_PROOF_TYPE_PROOF_OF_POSSESSION_OF_AADHAAR = 10;
  DOCUMENT_PROOF_TYPE_SIGNATURE = 11;
  DOCUMENT_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT = 12;
  DOCUMENT_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED = 13;
  DOCUMENT_PROOF_TYPE_UTILITY_BILL = 14;
  DOCUMENT_PROOF_TYPE_PROPERTY_OR_MUNICIPAL_TAX_RECEIPT = 15;
  DOCUMENT_PROOF_TYPE_BANK_ACCOUNT_OR_POST_OFFICE_SAVINGS_BANK_ACCOUNT_STATEMENT = 16;
  DOCUMENT_PROOF_TYPE_PENSION = 17;
  DOCUMENT_PROOF_TYPE_LETTER_OF_ALLOTMENT_OF_ACCOMMODATION_FROM_EMPLOYER_ISSUED_BY_STATE_OR_CENTRAL_GOVERNMENT_DEPARTMENTS = 18;
  DOCUMENT_PROOF_TYPE_DOCUMENTS_ISSUED_BY_GOVERNMENT_DEPARTMENTS_OF_FOREIGN_JURISDICTIONS = 19;
  DOCUMENT_PROOF_TYPE_NATIONAL_POPULATION_REGISTRY_LETTER = 20;
  DOCUMENT_PROOF_TYPE_EKYC_AUTHENTICATION = 21;
  DOCUMENT_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION = 22;
  DOCUMENT_PROOF_TYPE_SELF_DECLARATION = 23;
  DOCUMENT_PROOF_TYPE_OTHERS = 24;
  DOCUMENT_PROOF_TYPE_LEGAL_ENTITY_DOCUMENT = 25;
  // sebi mandated agreement between investment advisor (epifi wealth) and user
  DOCUMENT_PROOF_TYPE_INVESTMENT_ADVISORY_AGREEMENT = 26;
}

// this will be used to refer any document proof provided by customer
message DocumentProof {
  DocumentProofType proof_type = 1;
  // identifier of the document
  // for some cases this id can be empty
  string id = 2;
  // images of the document
  repeated typesv2.common.Image photo = 3;
  // expiry of the proof
  google.type.Date expiry = 4;
  // stored s3 path of the document images
  repeated string s3_paths = 5;
  // dob on the proof
  google.type.Date dob = 6;
  // address string as present in the document
  // Note: used string data type for address because we are extracting address in plain string format.
  // The conversion of plain text address to google.postal_address needs to be done by the domain services, if required.
  string address = 7;
}
