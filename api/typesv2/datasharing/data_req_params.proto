syntax = "proto3";

package api.typesv2.datasharing;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/datasharing";
option java_package = "com.github.epifi.gamma.api.typesv2.datasharing";

message DataRequestParams {
  // Query params for each type of data
  oneof req_params {
    AaAccountsDataQueryParams aa_accounts_data_query_params = 1;
  }
}

message AaAccountsDataQueryParams {
  // Connected account IDs for which data is required
  repeated string account_ids = 1;

  // Timestamp of the earliest transaction required
  google.protobuf.Timestamp oldest_transaction_ts = 2;

  // Timestamp of the most recent transaction required
  google.protobuf.Timestamp latest_transaction_ts = 3;

  // ID of the consent against which data is being shared
  string consent_id = 4;
}
