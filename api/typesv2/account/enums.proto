//go:generate gen_sql -typesv2=AccountProductOffering
syntax = "proto3";

package api.typesv2.account;

option go_package = "github.com/epifi/gamma/api/typesv2/account";
option java_package = "com.github.epifi.gamma.api.typesv2.account";

// Enum AccountProductOffering represents different typesv2 of account product offerings.
// These can be used along with AccountType enum to represent granular bifurcation of the account typesv2.
// Refer AccountType: https://github.com/epiFi/protos/blob/e737f7903b3a95ff493273b750dc2e225c6fb031/api/accounts/account_type.proto#L11
//
// Rationale for creating a separate enum, AccountProductOffering, instead of extending AccountType:
// 1. The current values in AccountType cover a vast majority of the accounts in the market.
// 2. A 1-1 mapping with bank offerings might not scale well due to the variety and frequency of new offerings.
// 3. Using a new enum along with AccountType provides a complete picture while keeping the code neat.
//
// Example: Non-Resident (NR) use case for deposits
// 1. One approach could be to add AccountType_FIXED_DEPOSIT_NR, AccountType_RECURRING_DEPOSIT_NR, etc. along with existing FD and RD values.
//    However, this doesn't add much value as these are just new offerings for the same use-case.
// 2. A better approach is to keep AccountType as is and add NRE, NRO etc. to AccountProductOffering.
//    This maintains the overall functionality around FD, RD and only differentiates further when necessary.
//
// How can this be used/stored?
// - This can be stored as a separate attribute in primary entities such as deposit_accounts, savings_accounts etc. tables.
// - This can be stored along with AccountType enum in secondary entities relying on identification of account typesv2.
// - This can be passed on along with AccountType across the codebase to maintain the complete picture.
//
// Tech doc: https://docs.google.com/document/d/10BymFsKM-2nfRd_XTVsF0pTA_Lx5JcWRY2_5F7YrXY8/edit#bookmark=id.483jpsscje13
enum AccountProductOffering {
  // ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED represents an unspecified account product offering.
  ACCOUNT_PRODUCT_OFFERING_UNSPECIFIED = 0;

  // APO_REGULAR represents a regular account product offering.
  // This need not correspond to any banks' specific offerings and can be used as a default value for any AccountType.
  APO_REGULAR = 1;

  // APO_NRE represents a Non-Resident External (NRE) account product offering.
  // This can be used along with, lets say, AccountType_SAVINGS/DEPOSITS to represent NRE savings account, NRE FD etc.
  // If an AccountType does not distinguish between Non-Resident (NR) offerings, this value can serve as a default.
  APO_NRE = 2;

  // APO_NRO represents a Non-Resident Ordinary (NRO) account product offering.
  // This can be used along with, lets say, AccountType_SAVINGS/DEPOSITS to represent NRO savings account, NRO FD etc.
  APO_NRO = 3;

  // APO_FCNR represents a Foreign Currency Non-Resident (FCNR) account product offering.
  APO_FCNR = 4;

  // APO_RFC represents a Resident Foreign Currency (RFC) account product offering.
  APO_RFC = 5;
}
