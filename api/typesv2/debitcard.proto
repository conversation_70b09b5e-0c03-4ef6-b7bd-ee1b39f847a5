syntax = "proto3";

package api.typesv2;

import "google/type/latlng.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/boolean.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// CardHomeSectionType is enum to identify a component type of card home screen.
// Components are part of home screen layout for eg - international benefits section, physical card benefits section etc.
enum CardHomeSectionType {
  CARD_HOME_SECTION_TYPE_UNSPECIFIED = 0;
  CARD_HOME_SECTION_TYPE_TOP_SECTION = 1;
  CARD_HOME_SECTION_TYPE_REWARDS_AND_TXNS = 2;
  CARD_HOME_SECTION_TYPE_PHYSICAL_CARD_ORDER_WIDGETS = 3;
  CARD_HOME_SECTION_TYPE_INTERNATIONAL_WIDGETS = 4;
  CARD_HOME_SECTION_TYPE_BOTTOM_SECTION = 5;
  CARD_HOME_SECTION_TYPE_OFFERS_AND_PROMOTIONS = 6;
  CARD_HOME_SECTION_TYPE_PROMO_SECTION = 7;
  // NOTE: mandatory section with the highest priority to for every card home layout
  // this should be only visible in case digital card is not yet activated via user,
  // visibility should be handled by ui builders
  CARD_HOME_SECTION_TYPE_ACTIVATE_DIGITAL_CARD = 8;
  CARD_HOME_SECTION_TYPE_SHORTCUTS = 9;
  //  https://www.figma.com/design/YQrtZG4S5yQlf9omrvAcjD/Debit-Card-%E2%80%A2%C2%A0Workfile?node-id=8417-21156&t=qSiwqCFQmw5eH2r7-0
  CARD_HOME_SECTION_TYPE_PROMOTIONAL_BANNER_LARGE = 10;
}

enum ATMProperty {
  ATM_PROPERTY_UNSPECIFIED = 0;
  // no ATM access Fee
  ATM_PROPERTY_FREE_ATM = 1;
  // open hours
  ATM_PROPERTY_OPEN_24HRS = 2;
}


message ATM {
  // Address of the ATM location
  api.typesv2.PostalAddress address = 1;
  // Coordinates of the ATM location
  google.type.LatLng coordinates = 2;
  // Distance to the ATM location in km
  double distance_in_km = 3;
  // Whether the ATM location is mappable
  api.typesv2.common.BooleanEnum is_mappable = 4;
  // Business name of the ATM owner
  string owner_bussiness_name = 5;
  // Place name of the ATM location
  string place_name = 6;
  // Properties of the ATM
  repeated ATMProperty atm_properties = 7;
  // Score of the ATM location
  double score = 8;
  // Type name of the ATM location
  string type_name = 9;
}
