syntax = "proto3";

//  A form is used in the sense of web nomenclature. A form can have multiple fields of different typesv2 input typesv2
//  such as Text, Date etc.

package api.typesv2.form;

import "api/typesv2/common/visual_element.proto";
import "api/frontend/deeplink/deeplink.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/form";
option java_package = "com.github.epifi.gamma.api.typesv2.form";


message ActionableIcon {
  // Image/Lottie to be shown on page load
  typesv2.common.VisualElement visual_element = 1;
  enum ActionType {
    ACTION_TYPE_UNSPECIFIED = 0;
    // For action type deeplink, client will have to redirect the user to the given deeplink
    ACTION_TYPE_DEEPLINK = 1;
    // For action type toast, client will have to show toast with given message
    ACTION_TYPE_TOAST = 2;
  }
  oneof Action {
    // Action to redirect user to the deeplink
    // 1:1 mapping with ACTION_TYPE_DEEPLINK
    frontend.deeplink.Deeplink deeplink = 2;
    // Message to be shown as toast message
    // 1:1 mapping with ACTION_TYPE_TOAST
    string toast_message = 3;
  }
}
