syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

enum ResidentialStatus {
  RESIDENTIAL_STATUS_UNSPECIFIED = 0;
  RESIDENT_INDIVIDUAL = 1;
  NON_RESIDENT_INDIAN = 2;
  FOREIGN_NATIONAL = 3;
  PERSON_OF_INDIAN_ORIGIN = 4;
}

enum ResidentialStatusProof {
  RESIDENTIAL_STATUS_PROOF_UNSPECIFIED = 0;
  RESIDENTIAL_STATUS_PROOF_PASSPORT = 1;
  RESIDENTIAL_STATUS_PROOF_PIO_CARD = 2;
  RESIDENTIAL_STATUS_PROOF_OCI_CARD = 3;
}
