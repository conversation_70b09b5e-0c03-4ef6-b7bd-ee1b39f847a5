syntax = "proto3";

package api.typesv2;

import "google/type/latlng.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// LatLongWithPrecision can be used to take coordinates with a certain precision of the decimal point.
message LatLongWithPrecision {
  google.type.LatLng lat_long = 1;
  uint32 precision = 2;
}

// LatLongWithRadius can be used to specify an area with an accuracy radius around a coordinate
message LatLongWithRadius {
  google.type.LatLng lat_lng = 1;
  uint32 accuracy_radius = 2;
}
