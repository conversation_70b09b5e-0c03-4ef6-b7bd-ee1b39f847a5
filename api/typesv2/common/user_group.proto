syntax = "proto3";

package api.typesv2.common;

option go_package = "github.com/epifi/be-common/api/typesv2/common";
option java_package = "com.github.epifi.gamma.api.typesv2.common";

// user can belong to one or more group
// this will be used to divide users into different segments to control feature release
enum UserGroup {
  USER_GROUP_UNSPECIFIED = 0;

  // user belongs to epifi
  INTERNAL = 1;

  // user is part of fnf list
  FNF = 2;

  // user's card has been delivered and can proceed for physical card activation.
  CARD_QR_CODE = 3;

  // external users grp for which vkyc is enabled
  VKYC = 4;

  // FNF users for which new card has to be requested for physical card dispatch
  REQUEST_NEW_CARD_FNF = 5;

  // Users who have not requested for their physical card yet
  // and are present only with digital card.
  DIGITAL_CARD_ONLY = 6;

  // User is part of connected account group
  CONNECTED_ACCOUNT = 7;

  // this group will be used for phased roll out of wealth onboarding flows
  WEALTH_ONBOARDING_INTERNAL = 8;

  // Users for whom investment rules are accessible
  FIT_INVESTMENT = 9;

  // User group to disable card tracking and tracking related communication
  DISABLE_CARD_TRACKING = 10;

  // Users for whom any number of free card can be issued
  FREE_CARD_REPLACEMENT = 11;

  // this group will be used for phased roll out of P2P Investment
  P2P_INVESTMENT_INTERNAL = 12;

  // this group will be used to release salary program in a controlled way to specific users.
  SALARY_PROGRAM_WHITELIST = 13;

  // this group will be used for phased roll out of cx features like Chatbot.
  CX_INTERNAL = 14;

  // this group will be used for phased roll out of Investment Landing UI.
  INVEST_LANDING_INT = 15;

  // Internal group for Home V2 launch
  HOME_V2_INTERNAL = 16;

  // Internal group for credit card tab visibility
  CREDIT_CARD_INTERNAL = 17;

  // for controlled rollout of US stocks
  USSTOCKS_INTERNAL = 18;

  // Internal group for tiering
  TIERING = 19;

  // This group will be used to bypass cooldowns to enable automation testing in QA env
  BYPASS_COOLDOWN_QA = 20;

  // Internal group for tpap
  TPAP_INTERNAL = 21;

  // User group to represent BRAND STREET organisation. We have use cases where
  // we need to have special handling for brand street org employees like skipping
  // onboarding add funds
  BRAND_STREET_ORG = 22;

  // User group to represent AHMEDABAD ROADLINES organisation. We have use cases where
  // we need to have special handling for Ahmedabad org employees like skipping
  // onboarding add funds
  AHMEDABAD_ROADLINES_ORG = 23;

  // User group representing SAAR EDUCATION (I) Pvt. Ltd
  // This group requires skipping of add funds
  SAAR_EDUCATION_ORG = 24;

  // User group for controlled rollouts of us stocks to external users
  // user group is required as the rollout is not expected to be for random set of users in initial phases
  // and is expected to be based on LRS limit of the user in current and prev financial year
  USSTOCKS_EXTERNAL = 25;

  // User group representing Simplify Edtech
  // This group requires skipping of add funds
  SIMPLIFY_EDTECH_ORG = 26;

  // User group for salary program users via B2B flow
  B2B_SALARY_PROGRAM = 27;

  // NEW_VPA_HANDLE controls feature to assign new vpa handles to users i.e @fifederal
  NEW_VPA_HANDLE = 28;

  // User group representing 'Allround facilities' org
  // This group requires skipping of add funds
  ALLROUND_FACILITIES = 29;

  // BKYC is a user group to allow whitelisted users to start Biometric KYC process during onboarding.
  BKYC = 30;

  // TPAP_LIMITED_USERS is a user group that allows users to use TPAP feature.
  // (contains users from both inside and outside Fi org).
  TPAP_LIMITED_USERS = 31;

  // ENACH_ACTIVE_USERS is a user group that contains list of users which have active enach on their account.
  // currently its getting created via manual ops and it does not always guarantee the current updated list of active enach users.
  ENACH_ACTIVE_USERS = 32;

  // UPI_MANDATES_INTERNAL : Internal user group for Upi Mandates.
  UPI_MANDATES_INTERNAL = 33;

  // NO_GROUP is to be used when user doesn't belong to any group.
  // Helps in distinguish between UNSPECIFIED and user not belonging to any group
  NO_GROUP = 34;

  // FI_STORE_INTERNAL is an internal user group for fi store.
  FI_STORE_INTERNAL = 35;

  // POST_ONBOARDING_BKYC_INTERNAL will be used to test BKYC process in post onboarding flow without triggering vendor upgrade process.
  POST_ONBOARDING_BKYC_INTERNAL = 36;

  // PAY_EXPERIMENTAL : user group for any sorts of experimental features for Pay (i.e. potentially buggy).
  // Disclaimer: users to be added in this group with caution. Failures are expected.
  PAY_EXPERIMENTAL = 37;

  // user group for any sorts of experimental features for Home (i.e. potentially buggy).
  // Disclaimer: users to be added in this group with caution. Failures are expected.
  HOME_EXPERIMENTAL = 38;

  // user group for (potential) users acquired through partner channels. (e.g. Ixigo)
  DEBIT_CARD_PARTNER_ACQUISITION = 39;

  // user group for cug testing of sms parser solution
  SMS_PARSER_INTERNAL = 40;
  // user group for issuing free first physical debit card (not replacement) as a reward, ex- referral reward to referee
  FREE_PHYSICAL_DC_REWARD = 41;
  // experimental group for testing any comms/campaigns by the marketing/pmm team.
  CAMPAIGN_TEST = 42;
}

enum IdentifierType {
  IDENTIFIER_TYPE_UNSPECIFIED = 0;
  IDENTIFIER_TYPE_EMAIL = 1;
  IDENTIFIER_TYPE_PHONE_NUMBER = 2;
}
