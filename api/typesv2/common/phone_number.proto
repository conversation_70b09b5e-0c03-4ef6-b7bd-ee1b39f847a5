// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package api.typesv2.common;

import "validate/validate.proto";

option go_package = "github.com/epifi/be-common/api/typesv2/common";
option java_package = "com.github.epifi.gamma.api.typesv2.common";

// Definition of protocol buffer for representing international telephone numbers.
// Refer- https://github.com/google/libphonenumber/blob/master/resources/phonenumber.proto
// Note- Don't use the above referred directly as it's in proto2
// We can keep on adding new fields based on the requirement
// Note: Validations are added as per Indian numbers
// TODO(pruthvi): Change this to MobileNumber and add a generic phone number
//  with any of Mobile Number and LandLine
message PhoneNumber {
  // The country calling code for this number, as defined by the International
  // Telecommunication Union (ITU). For example, this would be 91 for India
  uint32 country_code = 1 [(validate.rules).uint32.gt = 0];

  // The National (significant) Number, as defined in International
  // Telecommunication Union (ITU), without any leading zero.
  // Do not use this field directly: if you want the national significant number,
  // call the getNationalSignificantNumber method of PhoneNumberUtil.
  //
  // For countries which have the concept of an "area code" or "national
  // destination code", this is included in the National (significant) Number.
  // Although the ITU says the maximum length should be 15, we have found longer
  // numbers in some countries e.g. Germany.
  // Note that the National (significant) Number does not contain the National
  // (trunk) prefix. Obviously, as a uint64, it will never contain any
  // formatting (hyphens, spaces, parentheses), nor any alphanumeric spellings.
  uint64 national_number = 2 [(validate.rules).uint64 = {gte: 1000000, lt: 10000000000}];
}

// explicitly made different from "PhoneNumber"
// PhoneNumber is being assumed to be mobile number
message Landline {
  string std_code = 1;
  string number = 2;
}
