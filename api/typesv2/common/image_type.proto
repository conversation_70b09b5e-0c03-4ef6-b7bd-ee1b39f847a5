syntax = "proto3";

package api.typesv2.common;

option go_package = "github.com/epifi/be-common/api/typesv2/common";
option java_package = "com.github.epifi.gamma.api.typesv2.common";

enum ImageType {
  IMAGE_TYPE_UNSPECIFIED = 0;
  JPEG = 1;
  PNG = 2;
  PDF = 3;
  TIFF = 4;
}

enum ImageContentType {
  IMAGE_CONTENT_TYPE_UNSPECIFIED = 0;
  IMAGE_CONTENT_TYPE_PRODUCT = 1;
  IMAGE_CONTENT_TYPE_LOGO = 2;
}
