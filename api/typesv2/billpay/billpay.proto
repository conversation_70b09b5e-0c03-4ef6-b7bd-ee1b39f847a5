//go:generate gen_sql -types=CustomerParamSpec,ParamDataType,BillerAdditionalInfo
syntax = "proto3";

package api.typesv2.billpay;

option go_package = "github.com/epifi/gamma/api/typesv2/billpay";
option java_package = "com.github.epifi.gamma.api.typesv2.billpay";

message CustomerParamSpec {
  // Available values for the parameter
  string values = 1;
  // Parameter visibility
  bool visibility = 2;
  // Data type of the parameter
  ParamDataType data_type = 3;
  // Maximum length
  int64 max_length = 4;
  // Minimum length
  int64 min_length = 5;
  // Whether parameter is optional
  bool optional = 6;
  // Parameter name
  string param_name = 7;
  // Regular expression for validation
  string regex = 8;
}

// Data type for biller parameters
enum ParamDataType {
  PARAM_DATA_TYPE_UNSPECIFIED = 0;
  PARAM_DATA_TYPE_NUMERIC = 2;
  PARAM_DATA_TYPE_ALPHANUMERIC = 3;
}

// Biller additional information
message BillerAdditionalInfo {
  // Data type of the parameter
  api.typesv2.billpay.ParamDataType data_type = 1;
  // Whether parameter is optional
  bool optional = 2;
  // Parameter name
  string param_name = 3;
}

// Details required to create a recharge order
message RechargeParams {
  // expected to be of RechargeAccountType enum
  string account_type = 1;
  // account identifier of corresponding account_type e.g. mobile number for MOBILE account
  string account_identifier = 2;
  // operator for the recharge e.g. Jio/Airtel/Vi for mobile recharges
  string operator_id = 3;
  // unique identifier for plans provided by an operator
  string plan_id = 4;
}
