syntax = "proto3";

package api.typesv2.billpay;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/billpay/common.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/money.proto";
import "api/frontend/deeplink/deeplink.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/billpay";
option java_package = "com.github.epifi.gamma.api.typesv2.billpay";
option java_multiple_files = true;

// Figma: https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-96670&t=Gtozqi2v1E48sSHc-4
message RechargeIntroScreen {
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
  TopNavBar top_nav_bar = 2;
  // Eg:- This may contain promo banner, section to list recent recharges etc
  api.typesv2.ui.sdui.sections.Section top_section = 3;

  // Represents the section containing searchbar and contacts from user's device.
  // since the contacts data is present only on client, UI for the list of contacts has to be driven by client.
  // https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-97244&t=Gtozqi2v1E48sSHc-4
  message ContactsSection {
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
    SearchBar search_bar = 2;
    // Nudge shown to prompt the user to grant contacts permission; display only if permission hasn’t been granted.
    api.typesv2.ui.sdui.sections.Section contacts_permission_nudge = 3;
  }
  ContactsSection contacts_section = 4;
  // e.g- bharat billpay logo
  api.typesv2.ui.sdui.sections.Section footer_Section = 5;
}

// Figma: https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-97597&t=Gtozqi2v1E48sSHc-4
message RechargePlansScreen {
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
  TopNavBar top_nav_bar = 2;
  api.typesv2.ui.sdui.sections.Section top_banner = 3;

  // The main content area for displaying recharge plans.
  message RechargePlansContent {
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
    // A card displaying information about the recharge. e.g- Operator, mobile number etc
    api.typesv2.ui.sdui.sections.Section recharge_info_card = 2;
    SearchBar search_bar = 3;
    // Recharge plan segments like "Popular", "Unlimited", etc.
    // Selecting a segment should update the UI to display plans under that category.
    repeated RechargePlanSegment plan_segments = 4;
    string default_selected_segment_id = 5;
    api.typesv2.common.ui.widget.BackgroundColour segments_underline_color = 6;
  }
  RechargePlansContent content = 4;
}

// Represents a segment of recharge plans, like "Popular", "Unlimited", etc.
message RechargePlanSegment {
  string id = 1;
  api.typesv2.common.Text default_display_text = 2;
  api.typesv2.common.Text selected_display_text = 3;
  api.typesv2.common.ui.widget.BackgroundColour selected_underline_color = 4;
  repeated RechargePlanCard plans = 5;
}

message RechargePlanCard {
  string plan_id = 1;
  api.typesv2.ui.sdui.sections.Section card_section = 2;
  // Keywords to be used by the client for client-side search- to filter plans based on the user's search query.
  repeated string search_keywords = 3;
}

// https://www.figma.com/design/G19L6pH6DBgfwsnL49oKXY/%F0%9F%9A%A7-BillPay-%E2%80%A2-New?node-id=1-99237&t=XhP8BlFamoV8q77J-4
// Screen displaying bill details, from which the user can initiate payment
message BillDetailsConfirmationScreen {
  api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
  TopNavBar top_nav_bar = 2;
  // The section containing the bill details. e.g- Service provider name, bill number, bill amount, due date, etc.
  api.typesv2.ui.sdui.sections.Section bill_details_section = 3;

  message AmountContainer {
    api.typesv2.common.ui.widget.BackgroundColour bg_color = 1;
    // e.g- PAY TOTAL
    api.typesv2.common.Text title = 2;
    api.typesv2.Money amount = 3;
  }
  AmountContainer amount_container = 4;
  // Tapping on this cta should initiate the payment flow.
  frontend.deeplink.Cta cta = 5;
}
