syntax = "proto3";

package api.typesv2.deeplink_screen_option.kyc;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/deeplink_screen_option/header.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/kyc";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.kyc";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Screen options for Screen - KYC_STATUS_POLLING_SCREEN
message KycStatusPollingScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // unique identifier to get kyc attempt
  string client_request_id = 2;
  // retry count will be passed to the client to specify the number of retries already done
  // client will increment this count and pass it in the status poll API so that backend
  // can control the next delay accordingly
  int32 retry_attempt_number = 3;
  int32 retry_delay = 4;
  frontend.deeplink.InfoItemV2 polling_text = 5;
}


