syntax = "proto3";


package api.typesv2.deeplink_screen_option.pay.penny_drop;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/deeplink_screen_option/pay/penny_drop/enums.proto";
import "api/typesv2/common/text.proto";


option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay/penny_drop";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.pay.penny_drop";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Screen for PENNY_DROP_ACCOUNT_DETAILS_SCREEN
// figma : https://www.figma.com/file/Z7XLSFvILKtvaCxhvEUfjW/Workfile-%E2%80%A2-Secured-CC?type=design&node-id=4799-51956&mode=design&t=Kjl16q813TlV7owJ-4
message PennyDropAccountDetailsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text title = 2;
  typesv2.common.Text desc = 3;

  repeated Field fields = 4;

  frontend.deeplink.Button confirm_details_button = 5;
  // enum to identify if we need to call a specific rpc or navigate to deeplink according to the value on the client side
  PennyDropEntryFlow entry_flow = 6;

  // client identification metadata is a bytes blob which
  // will be used for identifying the client request id and business use case for which the penny drop is to be initiated
  // this blob will be marshalled from /unmarshalled into PennyDropClientIdentificationMetadata message in /pay/service.proto.
  bytes client_identification_metadata = 7;

  message Field {
    InputType input_type = 1;
    typesv2.common.Text hint = 2;
    FieldType field_type = 3;

    enum FieldType {
      FIELD_TYPE_IFSC = 0;
      FIELD_TYPE_BANK_NAME = 1;
      FIELD_TYPE_ACCOUNT_NUMBER = 2;
      FIELD_TYPE_ACCOUNT_HOLDER_NAME = 3;
    }
    enum InputType {
      INPUT_TYPE_PLAIN_TEXT = 0;
      INPUT_TYPE_DROPDOWN = 1;
    }
  }
}
