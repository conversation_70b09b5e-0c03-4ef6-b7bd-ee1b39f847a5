syntax = "proto3";

package api.typesv2.deeplink_screen_option.pay;

import "api/frontend/deeplink/deeplink.proto";
import "api/frontend/deeplink/timeline/enter_amount_screen_options.proto";
import "api/frontend/pay/beneficiary_management.proto";
import "api/frontend/pay/order_event.proto";
import "api/frontend/pay/transaction/enums.proto";
import "api/frontend/timeline/timeline_action.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/date.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/money.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/swipe_button.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pay";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.pay";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// ref: https://www.figma.com/file/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?node-id=7516-16427&t=O9jpie6jkzMXPB8I-0
// figma for latest Post Payment Screen: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=29044-57148&t=7YdbqlUFvxLzUx2q-1
message PostPaymentScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 6;
  // encapsulates header component details
  message HeaderComponent {
    typesv2.common.Image icon = 1;
    typesv2.ui.IconTextComponent header_right_cta = 2;
  }
  // encapsulates payment status details
  message PaymentStatusDetails {
    // deprecated, use status_icon instead
    typesv2.common.Image icon = 1 [deprecated = true];
    typesv2.common.Text title = 2;
    typesv2.common.Text status_title = 3;
    // contains amount details, currency, units etc. repeated text is used to support different text styles for currency symbol, unit and decimal
    repeated typesv2.common.Text amount = 4;
    // contains conversion amount details, currency, units etc. repeated text is used to support different text styles for currency symbol, unit and decimal
    repeated typesv2.common.Text conversion_amount = 5;
    typesv2.common.ui.widget.BackgroundColour bg_color = 6;
    repeated typesv2.ui.IconTextComponent txn_category = 7;
    // status icon to be shown in the screen for e.g, green tick lottie for success, red cross for failure etc
    typesv2.common.VisualElement status_icon = 8;
  }
  // encapsulates txn details
  message TxnDetails {
    typesv2.common.Text payment_instrument = 1;
    typesv2.common.Text txn_id = 2;
    typesv2.common.ui.widget.BackgroundColour bg_color = 3;
  }
  message ErrorInfoItem {
    frontend.deeplink.InfoItemV2 info = 1;
    repeated frontend.timeline.TimelineAction actions = 2;
  }
  HeaderComponent header_component = 1;
  PaymentStatusDetails payment_status_details = 2;
  TxnDetails txn_details = 3;
  // displays error msg for pending/failed txns
  ErrorInfoItem error_info_item = 4;
  // list of ctas user can access post payment
  repeated typesv2.ui.IconTextComponent ctas = 5;
  // Map of all event properties and their values
  map<string, string> event_properties = 7;

  // banner control for post payment screen.
  enum Banner {
    // do not call for banner
    BANNER_UNSPECIFIED = 0;
    // show post payment banner
    BANNER_POST_PAYMENT = 1;
  }
  // appropriate banner rpc will be called based on this.
  Banner banner = 8;
  // order id for the payment
  string order_id = 9;

  // enum to specify post-payment redirection behavior
  enum RedirectionType {
    // do not redirect to any screen
    REDIRECTION_TYPE_UNSPECIFIED = 0;
    // redirect to an external application
    // e.g., if it's an intent type of order, we want user to go back to the app from where the intent was initiated after payment
    REDIRECTION_TYPE_EXTERNAL_APP = 1;
    // redirect to a different screen within the current app
    // e.g., if it's a mutual funds order, we want user to go to the mutual funds screen after payment
    // Note: This feature is yet to be implemented
    REDIRECTION_TYPE_INTERNAL_SCREEN = 2;
  }

  // field to specify the redirection behavior after payment
  RedirectionType redirection_type = 10;

  // order status for the payment
  frontend.pay.OrderStatus order_status = 11;

  // partner tag for the post payment screen.
  // Example: "Powered by Rupay Credit on UPI" in case of payments via Rupay Credit card.
  common.VisualElement partner_tag = 12;
}

message InitiateAuthForBeneficiaryActivationScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // actor who is initiating the auth flow
  string actor_from = 2;
  // pi id of the beneficiary
  string pi_to = 3;
  frontend.pay.BeneficiaryActivationAuthMode auth_mode = 4;
}

// Screen options for PAYMENT_OPTIONS_FULL_SCREEN_BOTTOM_SHEET
message PaymentOptionsScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // amount in consideration for the payment
  typesv2.Money amount = 2;
  // entry point for the payment-options.
  // basis this, the payment-options page will be built (by applying the rules)
  frontend.deeplink.timeline.TransactionUIEntryPoint transaction_ui_entry_point = 3;

  // Metadata is a serialised construct of fields that can evolve over a period of time
  // and is expected to help with any orchestration requirements
  // Field will get from Domain service (eg:CC),
  // this also helps to decide payment options available for users.
  // It also aids in identifying the vendor (e.g., payment gateway like CC) responsible for processing the payment.
  // we will fallback to transaction_ui_entry_point point to identifying the
  // vendor(e.g., payment gateway like CC) responsible for processing the payment
  // if value of this filed is not populated.
  // TODO(abhishekprakashfi) Encrypt this blob going forward to ensure data integrity.
  bytes orchestration_metadata = 4;

  // to indicate if client should allow user to dismiss the flow by clicking on the close button or hide/show the close button
  bool should_hide_close_icon = 5;

  // Refers to the list of payment options in the order in which the response should be displayed
  // If empty, the result will be sorted in a default order set at backend.
  repeated frontend.pay.transaction.PaymentOptionType ordered_payment_option_types = 6;

  // actor id of beneficiary
  string actor_to = 7 [(validate.rules).string.min_len = 1];

  // Optional: pi id of the beneficiary.
  // If not passed, it will be on best-effort basis to find the beneficiary PI. And if no link is found, payment can fail.
  // Thus, it's recommended to pass this if available.
  string pi_to = 8;

  // Optional: This deeplink is where user can be redirected to post payment.
  // Redirection depends upon client consuming this deeplink from the Order.
  // In Pay flows, this happens via `GetOrderStatus` RPC.
  frontend.deeplink.Deeplink post_payment_deeplink = 9;

  // [Optional] recurring payment id which will only be non empty for a
  // recurring payment i.e when is_recurring_payment = true
  string recurring_payment_id = 10;

}

// Screen options for RECURRING_PAYMENT_CANCELLATION_DISCLAIMER_SCREEN
message RecurringPaymentCancellationDisclaimerScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;

  // disclaimer image shown above the title
  typesv2.common.VisualElement top_image = 2;

  // icon to close the screen
  typesv2.common.VisualElement close_icon = 3;

  // for e.g, Before you continue cancellation of this eNACH
  typesv2.common.Text title = 4;

  // all the disclaimer key points for user to read before cancelling recurring payment
  repeated ui.IconTextComponent key_point = 5;

  // having more disclaimer information for cancelling recurring payment.
  // for e.g, The amount will be blocked in your account till shares are allotted. If only few shares are allocated, partial amount will be transferred and if none get allotted, the entire blocked amount will be released.
  typesv2.common.Text description = 6;

  // swipe button for cancelling recurring payment
  typesv2.ui.SwipeButton swipe_button = 7;

  // background colour
  string bg_colour = 8;
}

// Screen options for UPDATE_STANDING_INSTRUCTION_PIN_SCREEN
message UpdateStandingInstructionPinScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  string recurring_payment_id = 2;
  typesv2.Money updated_amount = 3;
  typesv2.Date end_date = 4;
  // Information related to the status of the update to be shown to the user
  message StatusDisplayInfo {
    // eg: Checkbox with tick for success
    typesv2.common.VisualElement status_icon = 1;
    typesv2.common.Text status_text = 2;
  }
  // map of 'frontend.recurringpayment.ActionState enum number' -> StatusDisplayInfo
  // using enum number instead of string due to auto gen mismatch in IOS for "IN_PROGRESS'
  map<int32, StatusDisplayInfo> status_display = 5;
  // time duration for which we should poll for terminal recurring status
  int32 overall_polling_duration_in_seconds = 6;
  // The status screen should be auto dismissed after showing it for below number of seconds
  int32 auto_dismiss_after_in_seconds = 7;
  // Client to use this for any other failures in Intermediate rpc calls
  StatusDisplayInfo error_display_info = 8;
}

// screen options for AUTO_PAY_FREQUENCY_SELECTION_BOTTOM_SHEET
// https://www.figma.com/design/xzcr5E8lsH1ihsh3RmqQTU/Pay-%E2%80%A2-FFF-%E2%80%A2--v1.3?node-id=14325-2303&t=bOa4Uhj5w3BFiEEM-1
// When setting up a UPI mandate by scanning a QR code, if the `oam` tag is present in the QR URL,
// the user should be given two options: Pay Once or AutoPay.
message AutoPayFrequencySelectionBottomSheetScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // title for the bottom sheet. eg: "Pick the frequency of your mandate"
  api.typesv2.common.Text title = 2;

  // Data to present the frequency selection UI
  message PaymentFrequencyOption {
    // eg: Text "One time only" with deeplink to payment screen, or
    // Text "Set up a mandate" with deeplink to autopay setup screen
    api.typesv2.ui.IconTextComponent title = 1;
    // eg: Chevron icon
    api.typesv2.common.VisualElement trailing_icon = 2;
  }
  // list of options with deeplinks that take the user to either set up AutoPay or make a one-time payment.
  repeated PaymentFrequencyOption options = 3;
}

// Screen options for SHARE_POST_PAYMENT_SCREEN. Use this to design the UI for sharing on the post-payment screen.
// figma: https://www.figma.com/design/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?node-id=29200-28293&node-type=section&t=PiwUJO972rd8COZj-0
message SharePostPaymentScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;

  // header icon for the screen. For eg: Fi icon
  typesv2.common.VisualElement header_icon = 2;

  // field containing all information related to payment status
  message PaymentStatusDetails {
    // status icon to be shown for the payment for e.g, green tick for success, red cross for failure etc
    typesv2.common.VisualElement status_icon = 1;

    // title for the payment status for e.g, "Successfully paid to" or "Payment failed to" etc
    typesv2.common.Text status_title = 2;

    // name of the payment recipient
    typesv2.common.Text payee_name = 3;

    // payment instrument with which the payment is made. For eg, upi id or bank account
    typesv2.common.Text payment_instrument = 4;

    // amount details, currency, units etc. icon text component is used to support different text styles for currency symbol, unit and decimal
    typesv2.ui.IconTextComponent amount = 5;

    // background colour for PaymentStatusDetails section
    string bg_colour = 6;

    // timestamp section for the payment
    message TimeStampSection {
      // human-readable time when the payment was made
      typesv2.common.Text execution_time = 1;

      // background colour for the timestamp section
      string bg_colour = 2;
    }

    TimeStampSection timestamp_section = 7;
  }

  PaymentStatusDetails payment_status_details = 3;

  // section having error info for pending/failed payments
  message ErrorInfoSection {
    // icon and title for the error info, for e.g., "Insufficient account balance UPI128"
    typesv2.ui.IconTextComponent title = 1;

    // description for the error info, for e.g., "You need more funds in your remitting account to complete this payment"
    typesv2.common.Text description = 2;
    typesv2.ui.IconTextComponent.ContainerProperties container_properties = 3;
  }

  ErrorInfoSection error_info_section = 4;

  // Additional payment metadata (e.g., debited from, transaction ID)
  message TransactionDetailsSection {
    message TransactionDetail {
      // title for the transaction detail, for e.g., "Debited from" , "Transaction ID" etc
      typesv2.common.Text title = 1;

      // value for the corresponding transaction detail, for e.g., Jolly Joseph, "**********" etc
      typesv2.common.Text value = 2;

      // corresponding icon text component for the transaction detail, for e.g., HDFC Savings A/c•• 4667 with icon
      typesv2.ui.IconTextComponent related_itc = 3;
    }
    repeated TransactionDetail transaction_details = 1;

    // background colour for the transaction details section
    string bg_colour = 2;

    typesv2.ui.IconTextComponent.ContainerProperties container_properties = 3;
  }

  TransactionDetailsSection transaction_details_section = 5;

  // footer for the screen, for eg: "Powered by upi, federal bank" icon
  typesv2.common.VisualElement footer_icon = 6;

  string screen_bg_colour = 7;
}
