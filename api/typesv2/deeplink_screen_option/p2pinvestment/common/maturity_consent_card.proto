syntax = "proto3";

package api.typesv2.deeplink_screen_option.p2pinvestment.common;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/p2pinvestment/common";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.p2pinvestment.common";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

enum MaturityConsentType {
  MATURITY_CONSENT_TYPE_UNSPECIFIED = 0;
  // renew the investment with the matured amount always
  MATURITY_CONSENT_TYPE_RENEW_ALWAYS = 1;
  // renew the investment with the matured amount once
  MATURITY_CONSENT_TYPE_RENEW_ONCE = 2;
  // payout the matured amount to user's bank account
  MATURITY_CONSENT_TYPE_PAYOUT = 3;
}

// Card for taking maturity consent
// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?type=design&node-id=15353-23852&t=QzvBLxX0MGk1vjTp-4
message MaturityConsentCard {
  // Eg: Auto-renew same plan on 23 Apr 2023
  typesv2.common.Text title = 1;
  // Eg: Can be updated after 23 May
  typesv2.common.Text sub_title = 2;
  // CTA to highlight/drive user to take specific action
  // Eg: You will be earning extra returns up to ₹210
  typesv2.ui.IconTextComponent cta = 3;
  // Type of consent selected currently
  MaturityConsentType consent_type = 4;
  bool edit_allowed = 5;
  // Icon for showing edit button
  typesv2.ui.IconTextComponent edit_icon = 6;
  // deeplink for redirection on the entire card
  // In almost all cases it would be the 'P2P_INVESTMENT_MATURITY_CONSENT_SCREEN'
  frontend.deeplink.Deeplink deeplink = 7;
  // background color of the card
  // black for invest screen and white for activity details
  string bg_color = 8;
  // border color of the card
  // black for invest screen and white for activity details
  string border_color = 9;
}
