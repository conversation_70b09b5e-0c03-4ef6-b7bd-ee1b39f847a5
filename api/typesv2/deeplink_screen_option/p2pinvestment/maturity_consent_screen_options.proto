syntax = "proto3";

package api.typesv2.deeplink_screen_option.p2pinvestment;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/deeplink_screen_option/p2pinvestment/common/maturity_consent_form.proto";


option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/p2pinvestment";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.p2pinvestment";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Used for 'P2P_INVESTMENT_MATURITY_CONSENT_SCREEN'
// https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?type=design&node-id=15353-23902&t=akSt8sz2mwKyN7c5-4
message MaturityConsentScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Form to take and record user's consent
  common.MaturityConsentForm consent_form = 2;
  // event properties that should be sent by the client along with P2PAutoRenewBottomSheetLoaded, P2PAutoRenewBottomSheetActioned event
  // this will contain prpoerties like activity_type, flow_name, selected_plan, reconsideration_period etc
  // ref - https://docs.google.com/spreadsheets/d/1UJWX6L5njKdzz-MvL6o18X0GoPDOSitBTAt7B53lNnI/edit#gid=1102015672
  map<string, string> event_properties = 3;
}
