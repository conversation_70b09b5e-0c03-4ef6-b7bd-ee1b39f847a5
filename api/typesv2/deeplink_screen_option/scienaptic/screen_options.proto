syntax = "proto3";

package api.typesv2.deeplink_screen_option.scienaptic;

import "api/typesv2/deeplink_screen_option/header.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/scienaptic";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.scienaptic";

// Helps in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// InvokeScienapticSmsSdkScreenOptions is used to enable or disable the Scienaptic Sms Sdk on client
// Deeplink : INVOKE_SCIENAPTIC_SMS_SDK
message InvokeScienapticSmsSdkScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  //  Flag to enable or disable the Sdk on client
  //  If this is true, client will register the sdk which will start scanning user's messages, even in background
  //  If this is false, client will de-register the sdk which will stop scanning user's messages, even in background
  //
  //  This flag will primarily be driven by sms consent from user
  //  We will never enable the sms scanner sdk without user's consent
  bool enable_sdk = 2;

  // Phone number to be used for registering the sdk
  // Eg - "919090909090" will be passed to the sdk for registration
  //
  // The same will be used to query the sms features
  string phone_number = 3;
}
