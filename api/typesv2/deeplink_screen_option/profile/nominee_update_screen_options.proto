syntax = "proto3";

package api.typesv2.deeplink_screen_option.profile;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/profile";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.profile";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Figma : https://www.figma.com/design/12Q1ALOaMPUw7FXLWC5NNC/Profile-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=14542-41244&t=oUlGBCkhBDKdS8mO-4
// Screen Options for SA_NOMINEE_DETAILS_UPDATE_SUCCESS_SCREEN deeplink
message NomineeDetailsUpdateSuccessScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // icon to be displayed on the top of the screen
  api.typesv2.common.VisualElement top_icon = 2;
  // title to be displayed on the screen
  api.typesv2.common.Text title = 3;
  // subtitle to be displayed on the screen
  api.typesv2.common.Text subtitle = 4;
  // cta to be displayed on the screen
  .frontend.deeplink.Cta bottom_cta = 5;
  // nominee initial to be displayed on the screen
  api.typesv2.common.Text nominee_initial = 6;
}
