syntax = "proto3";

package api.typesv2.deeplink_screen_option.upi;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/text.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.upi";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// ConnectBankAccountsIntroScreenOptions: screen options
// required to prompt user to connect a bank account
// E.g. For Fi Lite Pay, if no account is linked for the
// actor, then user would be prompted to connect a bank
// account
// figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=19320-18968&mode=design&t=Sval55XxIC9inTWk-0
message ConnectBankAccountsIntroScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.VisualElement icon = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text subtitle = 4;
  frontend.deeplink.Cta cta = 5;
  typesv2.common.Text terms_and_conditions = 6;
}
