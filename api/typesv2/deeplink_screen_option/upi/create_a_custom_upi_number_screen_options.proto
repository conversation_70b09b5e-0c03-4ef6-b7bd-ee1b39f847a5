syntax = "proto3";

package api.typesv2.deeplink_screen_option.upi;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.upi";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// figma: https://www.figma.com/file/OpXEKX1RZ6IsjvVloIjEv6/%F0%9F%9B%A0%EF%B8%8F-Pay-%2F-Workfile?type=design&node-id=9631-81501&mode=design&t=axsdjqXhz5qnqnJs-0
message CreateCustomUpiNumberScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.Text title = 2;
  AccountInfo account_info = 3;
  RulesSection rules_section = 4;
  // vpa for which upi number will be created
  string vpa = 5;
}

message AccountInfo {
  typesv2.common.VisualElement bank_icon = 1;
  // e.g. Fi Account ..4656
  typesv2.common.Text bank_account_info = 2;
}

// upi number validation rules
message RulesSection {
  typesv2.common.Text title = 1;
  repeated typesv2.common.Text rules = 2;
}
