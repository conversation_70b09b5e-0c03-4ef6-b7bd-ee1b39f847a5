syntax = "proto3";

package api.typesv2.deeplink_screen_option.upi;

import "api/typesv2/deeplink_screen_option/header.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/upi";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.upi";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// ActivateUpiAccountScreenOptions : used to trigger activation of an inactive upi account
message ActivateUpiAccountScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  string derived_account_id = 2;
}
