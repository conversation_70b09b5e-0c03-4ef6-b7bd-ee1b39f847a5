syntax = "proto3";

package api.typesv2.deeplink_screen_option.pkg;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.pkg";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;


// Screen options for 'SDUI_BOTTOM_SHEET'
message SduiBottomSheetOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;

  // SDUI Content
  typesv2.ui.sdui.sections.Section section = 2;

  // If true, the bottom sheet should be rendered as a full-screen modal.
  bool is_full_screen = 3;

  // Optional background visual element that spans the entire bottom sheet, ignoring internal padding.
  // Can be used to add background styling, gradients, images, or other visual treatments to the entire bottom sheet surface.
  typesv2.common.VisualElement background = 4;

  // Optional: When set to true, the user will be redirected back to the screen
  // that originally navigated to the current screen containing the deeplink
  // which opened this bottom sheet.
  //
  // Use this flag in cases where the bottom sheet is opened via a deeplink
  // from a specific screen, and the desired behavior upon closing is to
  // navigate back beyond the current screen to its parent/previous screen.
  //
  // Default behavior (false): The navigation stack remains unchanged when
  // the bottom sheet is dismissed.
  bool pop_parent_screen_on_close = 5;
}
