syntax = "proto3";

package api.typesv2.deeplink_screen_option.pkg;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/pkg";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.pkg";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Screen option to get INFO_ACKNOWLEDGEMENT_V2
message InfoAcknowledgementV2ScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  message Instruction {
    typesv2.common.Text header = 1;
    typesv2.common.Text message = 2;
  }
  typesv2.common.Text title = 2;
  typesv2.common.Text subtitle = 3;
  typesv2.common.VisualElement screen_image_url = 4;
  typesv2.common.VisualElement instructions_image_url = 5;
  repeated Instruction instructions = 6;
  repeated frontend.deeplink.Cta ctas = 7;
  typesv2.common.Text consent = 8;
  typesv2.common.ui.widget.BackgroundColour bg_colour = 20;
}
