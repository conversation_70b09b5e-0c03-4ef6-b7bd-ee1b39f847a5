syntax = "proto3";

package api.typesv2.deeplink_screen_option.re_kyc;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/re_kyc";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.re_kyc";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Screen options for Screen - RE-KYC
message ReKycSMSScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.VisualElement icon = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text subtitle = 4;
  message Tag {
    typesv2.common.Text text = 1;
    typesv2.common.ui.widget.BackgroundColour bg_color = 2;
  }
  // for now Client doesn't support multiple tags, client only shows the first tag from list
  repeated Tag tags = 5;
  repeated frontend.deeplink.Cta ctas = 6;

  SMSContent s_m_s_content = 7;

  // client will send this client_req_id in PeriodicKYCCallback rpc
  string client_req_id = 8;
}


// Screen options for Screen - RE-KYC
message ReKycScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.VisualElement icon = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text subtitle = 4;
  message Tag {
    typesv2.common.Text text = 1;
    typesv2.common.ui.widget.BackgroundColour bg_color = 2;
  }
  // for now Client doesn't support multiple tags, client only shows the first tag from list
  repeated Tag tags = 5;
  repeated frontend.deeplink.Cta ctas = 6;

  // client will send this client_req_id in PeriodicKYCCallback rpc
  string client_req_id = 7;
  frontend.deeplink.BackAction back_action = 8;
}

message SMSContent {
  // sim_id is unique index referring to a logical SIM slot.
  int32 sim_id = 1;
  // phone number that is used to open savings account
  typesv2.common.PhoneNumber phone_number = 2;
  // text that will send in sms for re-kyc
  string text = 3;
}

message PeriodicKYCCallbackApiScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // client will send this client_req_id in PeriodicKYCCallback rpc
  string client_req_id = 2;
  // client will sent the action like whether sms was sent on cta was clicked
  string action = 3;
}
