syntax = "proto3";

package api.typesv2.deeplink_screen_option.deposit;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/accounts/account_type.proto";
import "api/typesv2/money.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/deposit";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.deposit";
// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// screen options for DEPOSIT_CLOSE_ACCOUNT_V2 screen
message CloseAccountV2ScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;

  string deposit_account_id = 2;

  accounts.Type deposit_type = 3;

  string deposit_name = 4;
  // represent amount to be shown while closing of deposit
  typesv2.Money amount = 5;
}

