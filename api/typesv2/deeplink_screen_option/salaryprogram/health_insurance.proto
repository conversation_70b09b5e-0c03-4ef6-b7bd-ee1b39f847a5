syntax = "proto3";

package api.typesv2.deeplink_screen_option.salaryprogram;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/salaryprogram";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.salaryprogram";

// Helps in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message OnsurityPolicyPurchaseInfoInputScreenOptions {
  deeplink_screen_option.ScreenOptionHeader header = 1;
  typesv2.common.VisualElement input_screen_header = 2;
  typesv2.common.Text title = 3;
  typesv2.common.Text subtitle = 4;
  TextInputOption full_name_text_input_option = 5;
  TextInputOption dob_text_input_option = 6;
  TextInputOption ph_num_text_input_option = 7;
  DropdownSelectionInputOption gender_dropdown_selection_input_option = 8;
  frontend.deeplink.Cta submit_cta = 9;

  message TextInputOption {
    typesv2.common.Text placeholder = 1;
    string selected_value = 2;
  }

  message DropdownSelectionInputOption {
    typesv2.common.Text placeholder = 1;
    DropdownSelectionOptionComponent dropdown = 2;
    string selected_value = 3;

    message DropdownSelectionOptionComponent {
      typesv2.common.Text title = 1;
      repeated Option options = 2;
      message Option {
        typesv2.common.Text display_text = 1;
      }
    }
  }
}
