syntax = "proto3";

package api.typesv2.deeplink_screen_option.walkthrough;

import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/text.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/walkthrough";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.walkthrough";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

// Represents options for a walkthrough screen.
message WalkthroughScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  // Title of the walkthrough screen
  api.typesv2.common.Text title = 2;
  // Skip button for the walkthrough
  frontend.deeplink.Cta skip = 3;
  // Lottie to be shown
  typesv2.common.VisualElement walkthrough_lottie = 4;
  // List of bottom ctas
  repeated frontend.deeplink.Cta ctas = 5;
  // Loader information for the walkthrough
  WalkthroughLoader loader = 6;
  // To show skip button after certain duration
  int32 skip_cta_delay_duration = 7;
}

// Represents the loader component within a walkthrough.
message WalkthroughLoader {
  // Title displayed during the loading process.
  api.typesv2.common.Text loader_title = 1;
  // List of informational cards displayed at the bottom.
  repeated typesv2.ui.IconTextComponent bottom_info_cards = 2;
}
