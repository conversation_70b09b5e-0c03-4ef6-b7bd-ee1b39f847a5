syntax = "proto3";

package api.typesv2.deeplink_screen_option.tiering;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/tiering";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.tiering";

// Drop off option component for alternate ways to enter tiering flow
message DropOffOptionComponent {
  // Background color
  common.ui.widget.BackgroundColour background_colour = 1;
  // Icon
  common.VisualElement icon = 2;
  // Title of the component
  common.Text title = 3;
  // Subtitle of the component
  common.Text subtitle = 4;
  // Benefits or features of alternate way to enter tiering flow
  repeated ui.IconTextComponent benefits = 5;
  // Arrow
  common.VisualElement arrow_icon = 6;
  // Deeplink to navigate to alternate way to enter tiering flow
  frontend.deeplink.Deeplink deeplink = 7;
  // Metadata for identify the alternate way like us stocks or fd/sd which will be passed in events.
  string meta_data = 9;
}
