syntax = "proto3";

package api.typesv2.deeplink_screen_option.insights.sms_consent;

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/sms_consent";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.insights.sms_consent";

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/visual_element.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/ui/widget/common.proto";

option java_multiple_files = true;

//https://www.figma.com/design/IhigcxQ5Hfg1LgaD0zPCZ6/%F0%9F%9A%80-Net-Worth-V0-%E2%80%A2-FFF?node-id=10292-110396&t=wp3ISt7F66eiaob3-4
message SmsReaderConsentScreenOptions {
  // common header for all screen options
  deeplink_screen_option.ScreenOptionHeader header = 1;
  api.typesv2.common.VisualElement icon = 2;
  api.typesv2.common.Text title = 3;
  repeated api.typesv2.ui.IconTextComponent  benefits = 4;
  repeated api.typesv2.common.ui.widget.CheckboxItem consents = 5;
  frontend.deeplink.Button button = 6;
  common.ui.widget.BackgroundColour bg_color= 7;
  frontend.deeplink.HeaderBar header_bar = 8;
  api.typesv2.ui.IconTextComponent branding_info = 9;
  frontend.deeplink.Button tertiary_cta = 10;
}

