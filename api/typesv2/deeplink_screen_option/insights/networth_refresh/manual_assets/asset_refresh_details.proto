syntax = "proto3";

package api.typesv2.deeplink_screen_option.insights.networth_refresh.manual_assets;

import "api/frontend/insights/networth/manual_form.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/deeplink_screen_option/insights/networth_refresh/manual_assets";
option java_package = "com.github.epifi.gamma.api.typesv2.deeplink_screen_option.insights.networth_refresh.manual_assets";

option java_multiple_files = true;

message ManualAssetsCurrentValueRefreshDetails {
  // Uniquely identifies the asset line item
  // Can be a combination of ids + enums to identify which line item of an asset is being updated
  string asset_id = 1;
  typesv2.common.VisualElement icon = 2;
  // Name of the asset entry user provided
  // Eg: 'Farmland' for real estate asset
  typesv2.common.Text title = 3;
  // Real Estate • Updated 3 months ago
  typesv2.common.Text sub_title = 4;
  // CURRENT VALUE (₹)
  typesv2.common.Text value_header = 5;
  // Current value of the Asset with validations to be performed
  frontend.insights.networth.InputOptionValue current_option_value = 6;
}
