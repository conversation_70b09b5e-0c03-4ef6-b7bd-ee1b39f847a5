syntax = "proto3";

package api.typesv2;

import "api/typesv2/common/boolean.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";


// InAppChatViewType identifies different typesv2 of chat views available on the client
enum InAppChatViewType {
  IN_APP_CHAT_VIEW_TYPE_UNSPECIFIED = 0;
  // Freshchat's chat sdk
  IN_APP_CHAT_VIEW_TYPE_FRESHCHAT_SDK = 1;
  // Senseforth's chatbot webview
  IN_APP_CHAT_VIEW_TYPE_SENSEFORTH_WEBVIEW = 2;
  // Nugget chatbot's sdk
  IN_APP_CHAT_VIEW_TYPE_NUGGET_CHATBOT_SDK = 3;
}

// Data needed to initialize the chatbot SDK should be defined here
message ChatbotInitInformation {
  oneof ChatbotSpecificInitInformation {
    NuggetChatbotInitInformation nugget_chatbot_init_information = 2;
    FreschatChatbotInitInformation freshchat_chatbot_init_information = 3;
    SenseforthChatbotInitInformation senseforth_chatbot_init_information = 4;
  }
}

// SenseforthChatbotInitInformation provides information
// to initialize senseforth webview on client side
message SenseforthChatbotInitInformation {
  // url which client has to load for chatbot webview
  string web_view_url = 1;
  // short token which client need to pass to senseforth
  string short_token = 2;
  // If this flag is set to TRUE , the client should reuse the short token & url stored in the cache
  api.typesv2.common.BooleanEnum reuse_cache_data = 3;
  // The context to invoke the bot. Ref: https://docs.google.com/document/d/15A9XIZRPD0omXeBTIm36WXU3GuQU46zt32zagqB2wrs/edit
  // This context will be used by client only if the deeplink(which triggered this RPC) doesn't contain context_code
  string bot_context_code = 4;
}

message FreschatChatbotInitInformation {
  // mapped freshdesk_id received from vendor-mapping service for current actor
  string reference_id = 1;

  // App id of the freshchat mobile sdk
  string app_id = 2;

  // App key of the freshchat mobile sdk
  string app_key = 3;

  // Domain name of freshchat mobile sdk
  string domain = 4;

  // Custom user properties to be passed to freschat sdk via android/ios client
  map<string, string> custom_user_properties = 5;

  // backend generated email to be passed by mobile client to Freshchat sdk
  // it is not part of custom property because client sdk explicitly expects
  // email to passed separately as an identifier
  string email = 6;

  // To filter and display only Topics tagged with a specific term, use the filterByTags API in ConversationOptions instance passed to showConversations() API as below.
  // Eg: To link and display only specific Topics from say orders page in your app, those specific Topics can be tagged with the term "order_queries".
  repeated string topic_tags = 7;
}

message NuggetChatbotInitInformation {
  // access_token to initialize the SDK
  string access_token = 1;
  // [Deprecated] This will not be used in favor of [BusinessContext] field
  map<string, string> custom_user_properties = 2 [deprecated = true];
  // Namespace to be used for the chatbot
  string namespace = 3;
  // Deeplink uri to be used for the chatbot. Note: This is not to be confused with frontend.Deeplink.
  // This deeplink is a Nugget specific deeplink used within the SDK
  string deeplink_uri = 4;
  // HTTP code received from Nugget's backend
  int32 http_code = 5;
  // Business Context object which Nugget SDK can use for various
  // optional configurations. Some values like auth token for Server to Server call from Nugget
  // are passed here
  BusinessContext business_context = 6;
  // Object representing the Business Context object which Nugget SDK can use
  // for various optional configurations
  message BusinessContext {
    message StringList {
      repeated string values = 1;
    }
    string channel_handle = 1;
    string ticket_grouping_id = 2;
    map<string, StringList> ticket_properties = 3;
    map<string, StringList> bot_properties = 4;
  }
}

// Represents the set of data fields that a chatbot can request from the backend.
enum ChatbotRequestedDataField {
  CHATBOT_USER_DATA_FIELD_UNSPECIFIED = 0;
  CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_REASON = 1;
  CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_STATUS = 2;
  CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_TYPE = 3;
  CHATBOT_USER_DATA_FIELD_RISK_FORM_ID = 4;
  CHATBOT_USER_DATA_FIELD_RISK_FORM_STATUS = 5;
  CHATBOT_USER_DATA_FIELD_RISK_FORM_EXPIRY_DATE = 6;
  CHATBOT_USER_DATA_FIELD_RISK_LEA_REPORTED = 7;
  CHATBOT_USER_DATA_FIELD_RISK_COMPLAINT_DETAILS = 8;
  CHATBOT_USER_DATA_FIELD_TRANSACTION_DETAILS = 9;
  CHATBOT_USER_DATA_FIELD_SAVINGS_ACCOUNT_FREEZE_DETAILS = 10;
  CHATBOT_USER_DATA_FIELD_AFU_UPDATE_ELIGIBILITY = 11;
  CHATBOT_USER_DATA_FIELD_LAST_CHEQUEBOOK_REQUEST_DETAILS = 12;
  CHATBOT_USER_DATA_FIELD_CHEQUEBOOK_ELIGIBILITY = 13;
  CHATBOT_USER_DATA_FIELD_ACCOUNT_CLOSURE = 14;
  CHATBOT_USER_DATA_FIELD_B2B_SALARY_PROGRAM = 15;
  CHATBOT_USER_DATA_FIELD_KYC_COMPLAINT_STATUS = 16;
  CHATBOT_USER_DATA_FIELD_CURRENT_TIER_DETAILS = 17;
  CHATBOT_USER_DATA_FIELD_IS_ELIGIBLE_FOR_TIER = 18;
  CHATBOT_USER_DATA_FIELD_LAST_DC_DELIVERY_DETAILS = 19;
  CHATBOT_USER_DATA_FIELD_POWER_UP_REDEMPTIONS = 20;
  CHATBOT_USER_DATA_FIELD_DEPOSIT_DETAILS = 21;
  CHATBOT_USER_DATA_FIELD_FI_STORE_REDEMPTIONS = 22;
  CHATBOT_USER_DATA_FIELD_TIERING_REWARD_ELIGIBILITY = 23;
  CHATBOT_USER_DATA_FIELD_USSTOCKS_ONBOARDING_STATUS = 24;
  CHATBOT_USER_DATA_FIELD_USSTOCKS_CURRENT_AMOUNT = 25;
  CHATBOT_USER_DATA_FIELD_USSTOCKS_WALLET_TXN = 26;
  CHATBOT_USER_DATA_FIELD_USSTOCKS_LAST_FIVE_TRADES = 27;
  CHATBOT_USER_DATA_FIELD_USSTOCKS_PORTFOLIO = 28;
  CHATBOT_USER_DATA_FIELD_USSTOCKS_POSITION_HISTORY = 29;
}
