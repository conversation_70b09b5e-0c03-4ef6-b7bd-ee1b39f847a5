syntax = "proto3";

package api.typesv2;

import "api/typesv2/address.proto";
import "api/typesv2/common/address.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/date.proto";
import "api/typesv2/document_type.proto";
import "api/typesv2/file.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/marital_status.proto";
import "api/typesv2/nationality.proto";
import "api/typesv2/nominee.proto";
import "api/typesv2/passport_type.proto";
import "google/type/date.proto";
import "google/type/postal_address.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// DocumentDetails can be used to capture any customer document.
// Currently, supports:
// 1. PAN
message DocumentDetails {
  typesv2.DocumentType document_type = 1;
  oneof DocumentTypeDetails {
    PanDocumentDetails pan_details = 2;
    PassportData passport_details = 3;
    CountryIdDetails country_id_details = 4;
    // Each country has its own unique ID, so we have specifically included the Emirates ID to account for this.
    EmiratesIdDetails emirates_id_details = 5;
    EPANDetails epan_details = 6;
    CKYCDocumentDetails ckyc_data = 7;
    QatarIdDetails qatar_id_details = 8;
    AadhaarDigilockerData aadhaar_digilocker_data = 9;
  }
}

message PanDocumentDetails {
  string id = 1;
  typesv2.common.Name name = 2;
  message GuardianInformation {
    typesv2.common.Name guardian_name = 1;
    typesv2.RelationType relation_type = 2;
  }
  GuardianInformation guardian_info = 3;
  typesv2.common.Image photo = 4;
  typesv2.common.Image signature = 5;
  google.type.Date date_of_birth = 6;
  google.type.Date date_of_issue = 7;
}

message PassportData {
  typesv2.PassportType passport_type = 1;
  string country_code = 2;
  typesv2.Nationality nationality = 3;
  string passport_number = 4;
  typesv2.common.Name name = 5;
  typesv2.Date date_of_birth = 6;
  typesv2.Gender gender = 7;
  string place_of_birth = 8;
  string place_of_issue = 9;
  typesv2.Date date_of_issue = 10;
  typesv2.Date date_of_expiry = 11;
  // machine-readable zone line 1, useful for computers to read data easily from the document.
  string mrz_line1 = 12;
  // machine-readable zone line 2, useful for computers to read data easily from the document.
  string mrz_line2 = 13;
  typesv2.common.Name fathers_name = 14;
  typesv2.common.Name mothers_name = 15;
  typesv2.common.Name spouses_name = 16;
  typesv2.PostalAddress address = 17;
  string old_passport_number = 18;
  typesv2.Date old_date_of_issue = 19;
  string old_place_of_issue = 20;
  // file number is basically an appication tracking number
  string file_number = 21;
}

message CountryIdDetails {
  string id = 1;
  typesv2.Date issue_date = 2;
  typesv2.Date date_of_expiry = 3;
  typesv2.common.Name name = 4;
  typesv2.common.Name father_name = 5;
  typesv2.common.Name mother_name = 6;
  typesv2.Date date_of_birth = 7;
  typesv2.Gender gender = 8;
  typesv2.MaritalStatus marital_status = 9;
  google.type.PostalAddress communication_address = 10;
  typesv2.Nationality nationality = 11;
  typesv2.common.Image user_photo = 12;
  typesv2.common.Image front_photo = 13;
  typesv2.common.Image back_photo = 14;
}

message EmiratesIdDetails {
  string identity_number = 1;
  typesv2.Date issue_date = 2;
  typesv2.Date date_of_expiry = 3;
  typesv2.common.Name name = 4;
  typesv2.common.Name mother_name = 5;
  typesv2.Date dob = 6;
  typesv2.Gender gender = 7;
  typesv2.MaritalStatus marital_status = 8;
  google.type.PostalAddress home_address = 9;
  // holder signature image in base64
  string holder_signature_image = 10;
  typesv2.Nationality nationality = 11;
  typesv2.common.Image user_photo = 12;
  string document_number = 13;
  string serial_number = 14;
  string issuer = 15;
  string document_code = 16;
}

message QatarIdDetails {
  string identity_number = 1;
  typesv2.Date date_of_expiry = 2;
  typesv2.common.Name name = 3;
  typesv2.Date date_of_birth = 4;
  typesv2.Nationality nationality = 5;
  typesv2.common.Image user_photo = 6;
  string document_number = 7;
  string serial_number = 8;
  string passport_number = 9;
  typesv2.Date passport_expiry = 10;
  typesv2.common.Image front_photo = 11;
  typesv2.common.Image back_photo = 12;
}

message EPANDetails {
  string pan_number = 1;
  typesv2.common.Name name = 2;
  message GuardianInformation {
    typesv2.common.Name guardian_name = 1;
    typesv2.RelationType relation_type = 2;
  }
  GuardianInformation guardian_info = 3;
  google.type.Date dob = 4;
  typesv2.common.PhoneNumber phone_number = 5;
  typesv2.Gender gender = 7;
  // photo for user, url content will be stored in s3
  typesv2.common.Image face_photo = 8;
  // epan pdf content, url content will be stored in s3 stored in s3
  typesv2.File epan_pdf = 9;
}

message CKYCDocumentDetails {
  CKYCDownloadPayload payload = 1;
}

// CKYCDownloadPayload contains data fetched from CKYC Download API.
message CKYCDownloadPayload {
  PersonalDetails personal_data = 1;
  repeated Identity identity_data = 2;
  api.typesv2.common.Image user_image = 3;
}

message PersonalDetails {
  string ckyc_no = 1;
  string acc_type = 2;
  api.typesv2.common.Name name = 3;
  api.typesv2.common.Gender gender = 4;
  api.typesv2.common.Name maiden_name = 5;
  // Father's or Spouse's name based on father_spouse_flag value
  api.typesv2.common.Name father_spouse_name = 6;
  api.typesv2.common.Name mother_name = 7;
  string nationality = 8;  // ISO 3166
  google.type.Date birth_date = 9;
  // Permanent Address
  enum AddressType {
    ADDRESS_TYPE_UNSPECIFIED = 0;
    ADDRESS_TYPE_RESIDENTIAL_OR_BUSINESS = 1;
    ADDRESS_TYPE_RESIDENTIAL = 2;
    ADDRESS_TYPE_BUSINESS = 3;
    ADDRESS_TYPE_REGI_OFFICE = 4;
  }
  AddressType permanent_address_type = 10;
  api.typesv2.common.PostalAddress permanent_address = 11;
  // Correspondence Address
  bool is_perm_corres_address_same = 12;
  api.typesv2.common.PostalAddress corres_address = 13;
  api.typesv2.common.PhoneNumber mobile = 14;
  string email = 15;
  KYCVerifier kyc_verifier = 16;
}

message Identity {
  IdProof id_proof = 1;
  bool is_id_proof_submitted = 2;
  bool is_id_verified = 3;
}

message IdProof {
  enum IdProofType {
    ID_PROOF_TYPE_UNSPECIFIED = 0;
    ID_PROOF_TYPE_PASSPORT = 1;
    ID_PROOF_TYPE_VOTER_ID = 2;
    ID_PROOF_TYPE_PAN = 3;
    ID_PROOF_TYPE_DRIVING_LICENSE = 4;
    ID_PROOF_TYPE_UID = 5;  // Aadhaar number
    ID_PROOF_TYPE_NREGA_JOB_CARD = 6;
    ID_PROOF_TYPE_NATIONAL_POPULATION_REGISTER_LETTER = 7;
    ID_PROOF_TYPE_CKYC_RECORD = 8;
    ID_PROOF_TYPE_EKYC_AUTHENTICATION = 9;
    ID_PROOF_TYPE_OFFLINE_AADHAAR_VERIFICATION = 10;
    ID_PROOF_TYPE_OFFLINE_OTHERS = 11;
    ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GOVERNMENT = 12;
    ID_PROOF_TYPE_SIMPLIFIED_MEASURES_ACC_ISSUED_GAZETTED = 13;
  }
  IdProofType type = 1;
  // The identifier based on the type of the id. e.g PAN Number / Aadhar Number
  string id_value = 2;
  api.typesv2.common.Image document_image = 3;
  google.type.Date expiry = 4;
}

message KYCVerifier {
  string declaration_date = 1;
  string declaration_place = 2;
  string kyc_verification_date = 3;
  string kyc_verifier_name = 4;
  string kyc_verifier_employee_code = 5;
  string kyc_verifier_designation = 6;
  string kyc_verification_branch = 7;
  enum SubmittedDocType {
    SUBMITTED_DOC_TYPE_UNSPECIFIED = 0;
    SUBMITTED_DOC_TYPE_CERTIFIED_COPIES = 1;
  }
  SubmittedDocType submitted_docs_type = 8;
  string org_name = 9;
  string org_code = 10;
}

message AadhaarDigilockerData {
  string masked_aadhaar_number = 1;
  api.typesv2.common.Name name = 2;
  api.typesv2.common.Gender gender = 3;
  google.type.Date date_of_birth = 4;
  api.typesv2.common.PostalAddress permanent_address = 5;
  api.typesv2.common.Image user_image = 6;
}
