syntax = "proto3";

package api.typesv2;

import "api/typesv2/common/ownership.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

// An actor is an abstract entity which serves as an interface for
// epifi user, external user, epifi merchant and external merchant.
message Actor {
  string id = 1;
  Type type = 2 [deprecated = true];

  // User ID or Merchant ID based on the type of actor
  // In case of external actors, this will be empty
  string entity_id = 3;

  // Name to be present if type is ex-user or ex-merchant
  // since we're not creating external-user and external-merchant entity
  string name = 4;

  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp deleted_at = 7;

  // ownership of actor. If actor is created via app ownership is `EPIFI_TECH`
  // if actor is created as part of aa service, ownership is `EPIFI_WEALTH`
  typesv2.common.Ownership ownership = 8;

  // Actor Type
  enum Type {
    option deprecated = true;

    TYPE_UNSPECIFIED = 0;
    USER = 1;
    MERCHANT = 2;
    EXTERNAL_USER = 3;
    EXTERNAL_MERCHANT = 4;
    WAITLISTED_USER = 5;
    KYC_AGENT = 6;
    TSP_USER = 7; // adding this here for now as still not completely moved implementations to v2. Once moved, no need to add here
  }
}

enum ActorType {
  TYPE_UNSPECIFIED = 0;
  USER = 1;
  MERCHANT = 2;
  EXTERNAL_USER = 3;
  EXTERNAL_MERCHANT = 4;
  WAITLISTED_USER = 5;
  // kyc agent actors are the ones who carry out kyc related activities like biometric kyc for fi users.
  KYC_AGENT = 6;
  // tsp user type actors are the ones who are registered at tsp
  TSP_USER = 7;
}
