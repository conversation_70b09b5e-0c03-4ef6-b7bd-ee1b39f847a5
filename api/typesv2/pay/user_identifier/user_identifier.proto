syntax = "proto3";

package api.typesv2.pay.user_identifier;

import "api/accounts/account_type.proto";
import "api/typesv2/account/enums.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/pay/user_identifier";
option java_package = "com.github.epifi.gamma.api.typesv2.pay.user_identifier";

// Information related to user like actorId, derivedAccountID, accountType, piId.
// It is not mandatory to have all the field.
message UserIdentifier {
  // actor id for the user
  string actor_id = 1;
  // derived_account_id - account id derived from combination of :
  // internal account id, tpap account id, connected account id, deposit account id
  string derived_account_id = 2;
  // Account type like Savings, Current
  accounts.Type account_type = 3;
  // payment instrument id for the user
  string pi_id = 4;
  // 1. upi number corresponding to a vpa / PI.
  // 2. a user can receive money on a UPI number.
  // Note - If client wants to pay to a upi number, they
  // should send existing identifiers (actor-id, pi-id,
  // or account id) along with it. Decision Engine doesn't
  // have the capability to decide PI for payments soley
  // on the basis of upi number for now. But it could be
  // added eventually.
  string upi_number = 5;
  // Account Product Offering associated with the AccountType.
  // 1. This can be UNSPECIFIED if it's an older account which did not have a product offering explicitly associated with it.
  //    We can default to APO_REGULAR in such cases.
  //
  // For e.g., AccountType: SAVINGS, AccountProductOffering: NRE
  typesv2.account.AccountProductOffering account_product_offering = 6;
}
