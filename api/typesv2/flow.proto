syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";


// Denotes various flows in the app including custom flows that can be created by users
// A flow is set of actions performed on the app to achieve a goal.
// Eg. Onboarding, Doing a transaction, buying a stock, making bill payment, etc.
enum Flow {
  FLOW_UNSPECIFIED = 0;

  ONBOARDING = 1;
  CRITICAL_MARKETING = 2; // events which are critical for marketing have been covered here
}

