syntax = "proto3";

package api.typesv2.connected_account;

import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/connected_account";
option java_package = "com.github.epifi.gamma.api.typesv2.connected_account";

// Figma: https://www.figma.com/design/c1efQtlpXMlZh0OovkvSld/Salary-Rewards-%E2%80%A2-Workfile?node-id=22394-30453&t=3Ipf2AmvgSrNChos-0
message ComingSoonComponent {
  typesv2.common.ui.widget.BackgroundColour background_colour = 1;
  typesv2.common.Text label = 2;
  typesv2.ui.IconTextComponent coming_soon_label = 3;
}
