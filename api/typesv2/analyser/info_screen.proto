syntax = "proto3";

package api.typesv2.analyser;

import "api/typesv2/analyser/consent_screen.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/analyser";
option java_package = "com.github.epifi.gamma.api.typesv2.analyser";

message InfoScreen {
  string background_color = 1;
  Message message = 2;
  Footer footer = 3;

  message Message {
    typesv2.common.VisualElement image = 1;
    typesv2.ui.IconTextComponent title = 2;
    typesv2.ui.IconTextComponent description = 3;
  }

  // Footer with tapable components
  message Footer {
    // Primary button prompting an action
    CTA primary_action = 1;
    // Secondary action with either just text or a tapable deeplink
    typesv2.ui.IconTextComponent secondary_action = 2;
  }
}



