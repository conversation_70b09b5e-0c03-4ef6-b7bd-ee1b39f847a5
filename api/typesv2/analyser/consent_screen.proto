syntax = "proto3";

package api.typesv2.analyser;

import "api/typesv2/common/text.proto";
import "api/typesv2/common/visual_element.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/ui/widget/common.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/analyser";
option java_package = "com.github.epifi.gamma.api.typesv2.analyser";

// Generic consent screen for a consent originating from analyser flows
// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13544-11114&mode=design&t=EJpDU7R6VwOeAW6t-4
message ConsentScreen {
  typesv2.ui.IconTextComponent title = 1;
  // primary image https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13544-11136&mode=design&t=EJpDU7R6VwOeAW6t-4
  typesv2.common.VisualElement primary_image = 2;
  typesv2.ui.IconTextComponent description = 3;
  // benefits section illustrating the features available
  BenefitsSection benefits_section = 4;
  TnC tnc_content = 7;
  string background_color = 8;
  CTA cta = 9;
}


// BenefitsSection contains list of benefits/features available to user after providing consent and importing their data
// https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13544-11141&mode=design&t=EJpDU7R6VwOeAW6t-4
message BenefitsSection {
  string background_color = 1;
  repeated LineItem benefits = 2;
  // footer below the benefits list https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13470-10260&mode=design&t=EJpDU7R6VwOeAW6t-4
  typesv2.ui.IconTextComponent footer = 3;
  message LineItem {
    // icon in the benefits line item https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13628-14086&mode=design&t=EJpDU7R6VwOeAW6t-4
    Icon icon = 1;
    typesv2.common.Text text = 2;
    message Icon {
      string background_color = 1;
      typesv2.common.VisualElement image = 2;
    }
  }
}

enum TnCType {
  TNC_TYPE_UNSPECIFIED = 0;
  TNC_TYPE_PARAGRAPH = 1;
  TNC_TYPE_CHECKBOX_LIST = 2;
}

message TnC {
  TnCType tnc_type = 1;
  oneof content {
    ConsentParagraph paragraph = 2;
    CheckBoxList check_box_list = 3;
  }
}

// ConsentParagraph contains consent text with corresponding consent id
message ConsentParagraph {
  string consent_id = 1;
  typesv2.common.Text text = 2;
}

// List of check boxes with tnc https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13470-10288&mode=design&t=EJpDU7R6VwOeAW6t-4
message CheckBoxList {
  repeated typesv2.common.ui.widget.CheckboxItem check_boxes = 1;
}

enum CTAType {
  CTA_TYPE_UNSPECIFIED = 0;
  // Tappable button https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13628-14100&mode=design&t=EJpDU7R6VwOeAW6t-4
  CTA_TYPE_BUTTON = 1;
  // Slider https://www.figma.com/file/cIdrsnWPL88OqVVL2XZhHU/%F0%9F%93%8A-All-Analysers-%E2%80%A2-FFF?type=design&node-id=13628-14042&mode=design&t=EJpDU7R6VwOeAW6t-4
  CTA_TYPE_SLIDER = 2;
}

// CTA with optional deeplink to another screen
// In absence of deeplink client specific logic should determined the next screen
// In case of checkbox tnc CTA should only be enabled if all mandatory boxes are checked
message CTA {
  CTAType cta_type = 1;
  typesv2.ui.IconTextComponent content = 2;
  // optional shadow at tbe bottom of cta
  string bottom_shadow_color = 3;
}
