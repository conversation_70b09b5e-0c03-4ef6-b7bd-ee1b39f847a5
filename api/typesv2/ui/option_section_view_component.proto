syntax = "proto3";

package api.typesv2.ui;

import "api/typesv2/common/text.proto";
import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";

message OptionSelectionView {
  OptionSelectionHeader header = 1;
  repeated OptionSelectionItem items = 2;
}

message OptionSelectionItem {
  int64 id = 1;
  api.typesv2.ui.IconTextComponent option_value = 2;
  // Used to show the badge above the option value.
  api.typesv2.ui.IconTextComponent option_badge = 3;
  string identifier = 4;
}

message OptionSelectionHeader {
  api.typesv2.common.Text label = 1;
  api.typesv2.common.Text value = 2;
}
