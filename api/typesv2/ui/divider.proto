syntax = "proto3";

package api.typesv2.ui;

import "api/typesv2/ui/icon_text_component.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";

// Figma: https://www.figma.com/design/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=34550-21502&t=4yCNisx08mfG5ToE-1
message Divider {
  // Border needs to be shown for the divider
  string color = 1;
  // this will be the height/width depending on the orientation of the divider
  int32 dimension = 2;
  // this will be the text shown over the divider (in figma it is STOCK)
  // kept it icon text component as it is a good candidate to add some icons or anything with text
  // this is optional if you need this to be shown else it will be simple divider
  api.typesv2.ui.IconTextComponent text = 3;
}
