syntax = "proto3";

package api.typesv2.ui;

import "api/typesv2/common/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";

message Line {
  // Color of the line
  typesv2.common.ui.widget.BackgroundColour color = 1;
  // Thickness of the line can be float i.e 1.5, 2.5 etc
  float thickness = 2;
  // Type of the line (solid or dashed)
  LineType type = 4;
}

enum LineType {
  LINE_TYPE_UNSPECIFIED = 0;
  LINE_TYPE_SOLID = 1;
  LINE_TYPE_DASHED = 2;
}
