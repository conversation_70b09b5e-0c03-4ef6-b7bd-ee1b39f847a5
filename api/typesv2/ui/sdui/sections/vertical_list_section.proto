syntax = "proto3";

package api.typesv2.ui.sdui.sections;

import "api/typesv2/ui/sdui/behaviors/lifecycle_behaviors.proto";
import "api/typesv2/ui/sdui/components/component.proto";
import "api/typesv2/ui/sdui/properties/visual_properties.proto";
import "api/typesv2/ui/sdui/behaviors/interaction_behaviors.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/sdui/sections";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.sdui.sections";

// Figma screen link: https://www.figma.com/file/GGiPlpxpvYa2eHdg68m0pg/Help-%E2%80%A2-Workfile?type=design&node-id=6499-49605&mode=design&t=p2Wx6HkA94C0gIbg-4
// Drive link to section: https://drive.google.com/file/d/1zYYyylin8lWz_Jh6j5aI1nuD-T9PiX-l/view?usp=share_link
// A vertical list section is a section that renders a list of components
message VerticalListSection {
  // Whether the section is scrollable or not.
  bool is_scrollable = 1;
  // The components to be rendered in the section.
  repeated components.Component components = 2;
  // Visual properties for the full width vertical list section.
  repeated properties.VisualProperty visual_properties = 3;
  // Horizontal alignment of this list section.
  HorizontalAlignment horizontal_alignment = 4;
  // Click behavior of this list section.
  repeated behaviors.InteractionBehavior interaction_behaviors = 5;
  // Specifies how to arrange the contents in the vertical axis
  VerticalArrangement vertical_arrangement = 6;
  // (Optional) properties to instruct overlap of elements
  properties.ListElementOverlapProps list_element_overlap_props = 7;
  // (Optional) Behaviour (and optional Analytics metadata) to be used by clients when this section loads on the screen
  // (may or may not be visible)
  behaviors.LifecycleBehavior load_behavior = 8;
  // (Optional) Behaviours (and optional Analytics metadata) to be used by clients when this section is visible on the
  // client device's screen
  behaviors.LifecycleBehavior visible_behavior = 9;
  // This will be used to align the content horizontally in the Vertical list section
  enum HorizontalAlignment {
    HORIZONTAL_ALIGNMENT_UNSPECIFIED = 0;
    HORIZONTAL_ALIGNMENT_LEFT = 1;
    HORIZONTAL_ALIGNMENT_CENTER_HORIZONTALLY = 2;
    HORIZONTAL_ALIGNMENT_RIGHT = 3;
  }
  // Enum to specify how to arrange and place the contents in this vertical
  // list.
  enum VerticalArrangement {
    // No spacing specified. the client will place the elements as per client defaults
    // (Usually, this means from the top in vertical lists)
    VERTICAL_ARRANGEMENT_UNSPECIFIED = 0;
    // Place the contents such that all the remaining space in the vertical list is spread
    // between the elements. E.g. 1##2##3 (where # is example of space)
    VERTICAL_ARRANGEMENT_SPACE_BETWEEN = 1;
    // Place the contents at the center of the vertical space. E.g. ##123##
    VERTICAL_ARRANGEMENT_CENTER = 2;
    // Place the contents at the start of the list. E.g. 123####
    VERTICAL_ARRANGEMENT_TOP = 3;
    // Place the contents at the end of the list. E.g. ####123
    VERTICAL_ARRANGEMENT_BOTTOM = 4;
  }
}
