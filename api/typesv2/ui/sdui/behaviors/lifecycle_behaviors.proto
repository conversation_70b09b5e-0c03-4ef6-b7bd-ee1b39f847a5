syntax = "proto3";

package api.typesv2.ui.sdui.behaviors;

import "api/typesv2/ui/sdui/analytics/analytics_event.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/sdui/behaviors";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.sdui.behaviors";

/**
Message defining the common lifecycle related behaviors that may be associated with a Screen, Section or component
Lifecycle: load, hide, view
 */
message LifecycleBehavior {
  oneof behavior {
    OnLoadBehavior load_behavior = 1;
    OnViewedBehavior viewed_behavior = 2;
    OnHiddenBehavior hidden_behavior = 3;
  }
  // (Optional) Analytics event to be associated with this interaction. This is logged by clients whenever client apps
  // execute this interaction
  analytics.AnalyticsEvent analytics_event = 4;
}

message OnLoadBehavior {
  // TODO:
}

message OnViewedBehavior {
  // TODO:
}

message OnHiddenBehavior {
  // TODO:
}
