syntax = "proto3";

package api.typesv2.ui.sdui.components;

import "api/typesv2/common/ui/widget/widget_themes.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui/sdui/components";
option java_package = "com.github.epifi.gamma.api.typesv2.ui.sdui.components";

message Divider {
  int32 space = 1;
  typesv2.common.ui.widget.BackgroundColour bg_colour = 2;
  int32 line_size = 3;
  Orientation orientation = 4;

  enum Orientation {
    ORIENTATION_UNSPECIFIED = 0;
    HORIZONTAL = 1;
    VERTICAL = 2;
  }
}
