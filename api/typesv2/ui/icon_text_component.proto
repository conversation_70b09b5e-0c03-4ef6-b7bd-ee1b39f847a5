syntax = "proto3";

package api.typesv2.ui;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/image.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/common/ui/widget/widget_themes.proto";
import "api/typesv2/common/visual_element.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";

// Component for showing icon+text component on the screen, explicitly for horizontal components
message IconTextComponent {
  // Icon to be shown to the left of the text
  // Deprecated in favour of left_visual_element
  typesv2.common.Image left_icon = 1 [deprecated = true];
  /*
  Text to be shown
  Impl. Caveats
  - Client to not put any spacing when rendering this list of texts. Its BE's onus to provide space/separator as part of the text itself etc.
  - If using HTML, Pass only one element in the array. Array is only meant for simple multi-formatted text.
    Client would take this array to create a single label/span with texts having multiple formats.
    Combination of texts which cannot confine to this would break.
   */
  repeated typesv2.common.Text texts = 2;
  // Icon to be shown to the right of the text
  // Deprecated in favour of right_visual_element
  typesv2.common.Image right_icon = 3 [deprecated = true];
  // Padding for the gap between the left image and text
  // Can be picked directly from the figma
  // Eg: https://drive.google.com/file/d/1g7vptUsEXrLM8ES4tnWAkBSGDV40IICI/view?usp=sharing
  int32 left_img_txt_padding = 4;
  // Padding for the gap between the right image and text
  // Can be picked directly from the figma
  // Eg: https://drive.google.com/file/d/1WaGANP9bFxGMysmwSjGRQe8e9opxXM-I/view?usp=sharing
  int32 right_img_txt_padding = 5;
  // Deeplink for redirection if the component is clicked
  frontend.deeplink.Deeplink deeplink = 6;

  // Needed if the IconTextComponent is within a 'container'
  // If not specified, the icon text would be rendered with no container.
  // Thus, it would be part of the component this is part of
  message ContainerProperties {
    // If unspecified, default handling would be to use the same background color as the place
    // where the content is being rendered
    // Deprecated in favour of background_colour to add extended support for different types of background colours
    string bg_color = 1 [deprecated = true];
    // corner radius for the container
    int32 corner_radius = 2;
    // Optional: Height of the container
    // If not specified, container height would be resized according to the content
    int32 height = 3;
    // Optional: Width of the container
    // If not specified, container width would be resized according to the content
    int32 width = 4;
    /*
      Padding values between the 'text+icon' component and container its in
      This would determine how the 'text+icon' is aligned wrt to the container its in
      Eg: Figma SS: https://drive.google.com/file/d/1NI_UeP2w-Om9cL-l4gOPEitBq3aiKvPo/view?usp=sharing
    */
    // Eg: Figma SS: https://drive.google.com/file/d/1xVYsSMWqQiypYcymcLk0TxJbkl7wYR4I/view?usp=sharing
    int32 left_padding = 5;
    // Eg: Figma SS: https://drive.google.com/file/d/1-PlWgcwRSwqCCPDgYkp1J4qKDxgF738A/view?usp=sharing
    int32 right_padding = 6;
    // Eg: Figma SS: https://drive.google.com/file/d/1N850dgvI74kDYiJXdf4MPa33KeLcJ_G3/view?usp=sharing
    int32 top_padding = 7;
    // Eg: Figma SS: https://drive.google.com/file/d/1N850dgvI74kDYiJXdf4MPa33KeLcJ_G3/view?usp=sharing
    int32 bottom_padding = 8;
    // border color of the container: Eg Figma SS: https://drive.google.com/file/d/1eO9KqLzdlg6bXvgIinyBFC8KOH9uo-3M/view?usp=share_link
    // Deprecated in favour of bg_border_colour to add extended support for different types of borders colours like gradients
    string border_color = 9 [deprecated = true];
    // border width of the container: Eg Figma SS: https://drive.google.com/file/d/1eO9KqLzdlg6bXvgIinyBFC8KOH9uo-3M/view?usp=share_link
    int32 border_width = 10;
    // Optional: shadow height of the container
    string shadow_height = 11;
    // Optional: shadow color of the container
    string shadow_color = 12;
    // Optional : Background color for the itc widget
    typesv2.common.ui.widget.BackgroundColour background_colour = 13;
    // Optional : Border color for the itc widget
    typesv2.common.ui.widget.BackgroundColour bg_border_colour = 14;
  }
  ContainerProperties container_properties = 7;
  // visual element to be rendered to the left side of component
  typesv2.common.VisualElement left_visual_element = 8;
  // visual element to be rendered to the right side of component
  typesv2.common.VisualElement right_visual_element = 9;
}

// Component for showing icon+text component on the screen, explicitly for vertical components
message VerticalIconTextComponent {
  // Icon to be shown to the top of the text
  // Figma SS: https://drive.google.com/file/d/14NNB0U5YJE2dWSIwXHdmIrpUdFcWi1zy/view?usp=sharing
  // Deprecated in favour of top_visual_element
  typesv2.common.Image top_icon = 1 [deprecated = true];
  // Padding for the gap between the image and text at the bottom of it
  // Can be picked directly from the figma
  int32 top_img_txt_padding = 2;

  // Icon to be shown to the bottom of the text
  // Deprecated in favour of bottom_visual_element
  typesv2.common.Image bottom_icon = 3 [deprecated = true];
  // Padding for the gap between the image and text on top of it
  // Can be picked directly from the figma
  int32 bottom_img_txt_padding = 4;

  /*
  Text to be shown
  Impl. Caveats
  - Client to not put any spacing when rendering this list of texts. Its BE's onus to provide space/separator as part of the text itself etc.
    /n needs to be used in case the text needs to be broken down into multiple lines
  - If using HTML, Pass only one element in the array. Array is only meant for simple multi-formatted text.
    Client would take this array to create a single label/span with texts having multiple formats.
    Combination of texts which cannot confine to this would break.
    Stack would be vertical
   */
  repeated typesv2.common.Text texts = 5;

  // Deeplink for redirection if the component is clicked
  frontend.deeplink.Deeplink deeplink = 6;

  // Needed if the IconTextComponent is within a 'container'
  // If not specified, the icon text would be rendered with no container.
  // Thus, it would be part of the component this is part of
  message ContainerProperties {
    // If unspecified, default handling would be to use the same background color as the place
    // where the content is being rendered
    string bg_color = 1 [deprecated = true];
    // corner radius for the container
    int32 corner_radius = 2;
    // Optional: Height of the container
    // If not specified, container height would be resized according to the content
    int32 height = 3;
    // Optional: Width of the container
    // If not specified, container width would be resized according to the content
    int32 width = 4;
    /*
      Padding values between the 'text+icon' component and container its in
      This would determine how the 'text+icon' is aligned wrt to the container its in
      Eg: Figma SS: https://drive.google.com/file/d/1NI_UeP2w-Om9cL-l4gOPEitBq3aiKvPo/view?usp=sharing
    */
    // Eg: Figma SS: https://drive.google.com/file/d/1xVYsSMWqQiypYcymcLk0TxJbkl7wYR4I/view?usp=sharing
    int32 left_padding = 5;
    // Eg: Figma SS: https://drive.google.com/file/d/1-PlWgcwRSwqCCPDgYkp1J4qKDxgF738A/view?usp=sharing
    int32 right_padding = 6;
    // Eg: Figma SS: https://drive.google.com/file/d/1N850dgvI74kDYiJXdf4MPa33KeLcJ_G3/view?usp=sharing
    int32 top_padding = 7;
    // Eg: Figma SS: https://drive.google.com/file/d/1N850dgvI74kDYiJXdf4MPa33KeLcJ_G3/view?usp=sharing
    int32 bottom_padding = 8;
    // border color of the container: Eg Figma SS: https://drive.google.com/file/d/1eO9KqLzdlg6bXvgIinyBFC8KOH9uo-3M/view?usp=share_link
    string border_color = 9 [deprecated = true];
    // border width of the container: Eg Figma SS: https://drive.google.com/file/d/1eO9KqLzdlg6bXvgIinyBFC8KOH9uo-3M/view?usp=share_link
    int32 border_width = 10;
    // Background color for the itc widget
    typesv2.common.ui.widget.BackgroundColour background_colour = 11;
    // Border color for the itc widget
    typesv2.common.ui.widget.BackgroundColour border_colour = 12;
  }
  ContainerProperties container_properties = 7;
  // visual element to be rendered to the top side of component
  typesv2.common.VisualElement top_visual_element = 8;
  // visual element to be rendered to the bottom side of component
  typesv2.common.VisualElement bottom_visual_element = 9;
}
