syntax = "proto3";

package api.typesv2.ui;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/text.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/ui";
option java_package = "com.github.epifi.gamma.api.typesv2.ui";

// TextWithHyperlinks provide the sub text that need to be treated as hyper link
// Where the hyperlink_map key act as sub text from main text that require click behaviour
// and hyperlink_map value provide the info such deeplink for click
// eg: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=9675%3A28742&t=OUwONLcRXzjWSJ4w-4
// in above figma link terms and remittance are the sub text that require custom hyper links behaviour
message TextWithHyperlinks {
  typesv2.common.Text text = 1;

  // Key will represent the sub text that will be treated as underlined hyper link.
  // Value will represent hyper link proto, providing the url extra data.
  map<string, HyperLink> hyperlink_map = 9;
}

// HyperLink provide the info about the hyper link such as deeplink
message HyperLink {
  frontend.deeplink.Deeplink next_action = 1 [deprecated = true];
  oneof Link {
    // client will redirect to the corresponding screen based on the next action here
    frontend.deeplink.Deeplink next_action_link = 2;
    // client will open this url
    string url = 3;
  }
  // [Optional] Parameter for Analytics attributes, e,g. event names or some other id, associated with this Hyper-link, if any
  string event_parameter = 4;
}
