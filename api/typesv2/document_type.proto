//go:generate gen_sql -typesv2=DocumentType
syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

enum DocumentType {
  option allow_alias = true;
  DOCUMENT_TYPE_UNSPECIFIED = 0;
  PAN = 1;
  AADHAR = 2;
  VOTER_ID = 3;
  PASSPORT = 4;
  DRIVING_LICENCE = 5;
  DOCUMENT_TYPE_AADHAAR = 2;
  DOCUMENT_TYPE_PAN = 1;
  DOCUMENT_TYPE_PASSPORT = 4;
  DOCUMENT_TYPE_COUNTRY_ID = 7;
  DOCUMENT_TYPE_EMIRATES_ID = 6;
  DOCUMENT_TYPE_PASSPORT_FRONT = 8; // TODO (Rishu): deprecate this enum after altering document side logic in omegle
  DOCUMENT_TYPE_PASSPORT_BACK = 9; // TODO (Rishu): deprecate this enum after altering document side logic in omegle
  DOCUMENT_TYPE_EPAN = 10;
  DOCUMENT_TYPE_CKYC_RECORD = 11;
  DOCUMENT_TYPE_QATAR_ID = 12;
}
