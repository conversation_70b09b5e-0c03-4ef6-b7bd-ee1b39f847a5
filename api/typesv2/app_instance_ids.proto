syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";

message AppInstanceId {
  // name of the ID, eg. Firebase app_instance_id, AppsFlyerId, Prospect ID, etc.
  AppInstanceIdName name = 1;
  // value of the ID
  string value = 2;
}

enum AppInstanceIdName {
  ID_NAME_UNSPECIFIED = 0;
  // unique identifier created internally by us on every app installation
  PROSPECT_ID = 1;
  // A<PERSON>F<PERSON>er's 'appsflyer_id'
  CLIENT_APPSFLYER_ID = 2;
  // Google Firebase's 'app_instance_id'
  FIREBASE_APP_INSTANCE_ID = 3;
}
