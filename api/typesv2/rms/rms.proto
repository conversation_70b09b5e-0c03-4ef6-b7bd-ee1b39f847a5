syntax = "proto3";

package api.typesv2.rms;

import "google/type/money.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/rms";
option java_package = "com.github.epifi.gamma.api.typesv2.rms";

// This payload represents the possible actions supported during the RMS workflow.
// Note: It is a generic payload similar to the action payload in FIT, but formatted
// in typesv2 to ensure compatibility and reuse across different services.
message RmsActionInfo{
  oneof data{
    BatchedUsstocksSIPActionInfo batched_uss_sip_action_info = 1;
  }
  string actor_id = 2;
  string rule_id = 3;
  string dedupe_id = 4;
}

message BatchedUsstocksSIPActionInfo{
  repeated UssSIPActionInfo uss_sip_action_info = 1;
}

message UssSIPActionInfo{
  string subscription_id = 1;
  google.type.Money amount = 2;
  oneof id{
    string usstocks_id = 3;
  }
  string execution_id = 4;
  google.type.Date sip_execution_date = 5;
}
