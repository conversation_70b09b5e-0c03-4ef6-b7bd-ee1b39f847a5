syntax = "proto3";

package api.typesv2;

option go_package = "github.com/epifi/gamma/api/typesv2";
option java_package = "com.github.epifi.gamma.api.typesv2";


// Transactions of high risk e.g., high valued transaction or new install
// may mandate additional level of authentication for security reasons.
//
// `cred_required` denotes if additional authentication is required from the user
//
enum CredRequiredType {
  CRED_REQUIRED_TYPE_UNSPECIFIED = 0;
  // For UPI payments client needs to generate cred block via NPCI CL.
  CRED_REQUIRED_TYPE_NPCI_PIN = 1;
  // For payments those are carried through NEFT, IMPS, etc.
  // client need to generate cred block via partner bank's CL.
  CRED_REQUIRED_TYPE_FEDERAL_SECURE_PIN = 2;
  // No pin required to execute the transaction. The client can hit initiate transaction
  // without any cred block.
  CRED_REQUIRED_TYPE_NONE = 3;
}
