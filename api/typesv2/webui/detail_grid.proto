syntax = "proto3";

package api.typesv2.webui;

import "api/typesv2/webui/table.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/webui";
option java_package = "com.github.epifi.gamma.api.typesv2.webui";

enum ValueType {
  VALUE_TYPE_UNSPECIFIED = 0;
  VALUE_TYPE_TEXT = 1;
  VALUE_TYPE_KEY_VALUE = 2;
}

message KeyValueList {
  message KeyValue {
    string key = 1;
    string value = 2;
    // key value pair style takes priority over the cell style
    // if the style is not set, the cell style will be used
    Style style = 3;
  }
  repeated KeyValue key_value_list = 1;
}



message Cell {
  ValueType type = 1;
  // cell style is applied to higher div
  Style style = 2;
  oneof value {
    string text_value = 3;
    KeyValueList key_value_pairs = 4;
  }
}

message GridRow {
  string label = 1;
  Cell detail_value = 2;
}

message DetailView {
  string title = 1;
  repeated GridRow rows = 2;
}
