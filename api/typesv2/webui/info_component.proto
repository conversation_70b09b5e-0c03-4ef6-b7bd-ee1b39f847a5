syntax = "proto3";

package api.typesv2.webui;

import "api/typesv2/webui/cta.proto";

option go_package = "github.com/epifi/gamma/api/typesv2/webui";
option java_package = "com.github.epifi.gamma.api.typesv2.webui";

message InfoComponentViewRow {
  string title = 1;
  string description = 2;
  typesv2.webui.CTA cta = 3;
}

message InfoComponentView {
  string title = 1;
  repeated InfoComponentViewRow rows = 2;
}
