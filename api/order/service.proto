// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order;

import "api/accounts/account_type.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/order/actor_add_fund_options_map.proto";
import "api/order/order.proto";
import "api/order/order_metadata.proto";
import "api/order/payment/notification/parsed_txn_particulars.proto";
import "api/order/payment/notification/txn_details.proto";
import "api/order/payment/payment_protocol.proto";
import "api/order/payment/transaction.proto";
import "api/order/txn_category.proto";
import "api/order/workflow.proto";
import "api/rpc/status.proto";
import "api/typesv2/actor.proto";
import "api/typesv2/common/device.proto";
import "api/typesv2/common/ownership.proto";
import "api/typesv2/common/text.proto";
import "api/typesv2/inapphelp_media_uicontext.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/ui/sdui/sections/section.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/order";
option java_package = "com.github.epifi.gamma.api.order";

// Defines the GRPC service to perform various operations to process a order.
service OrderService {
  // We define an order as a workflow for exchange of goods and services between two actors in our system with minimum
  // one transaction involved.

  // CreateOrder creates order in the system.
  // It also creates transaction entry through payment and returns the generated transaction_req_id.
  // This transaction_req_id is used at client end to generate a cred block for the transaction.
  // Post success of the rpc an order is expected to be in CREATED state.
  //
  // Now, to initiate a transaction post pin entry and cred block generation a separate InitiateTransaction call needs to
  // be initiated by client. Any failure to do so leads to an `abandoned order` in the system.
  // Thus, an order is defined as an abandoned order of it's transaction is not initiated beyond a particular threshold.
  rpc CreateOrder (CreateOrderRequest) returns (CreateOrderResponse) {}

  // GetOrder fetches the order details for a particular order id or ref id.
  rpc GetOrder (GetOrderRequest) returns (GetOrderResponse) {}

  // GetOrders processes a batch of order requests and fetches a batch of orders accordingly.
  // To be used only by domain services because there is no authorisation
  rpc GetOrders (GetOrdersRequest) returns (GetOrdersResponse) {}

  // GetOrderBatchWithTransaction processes a batch of order requests and fetches a batch of orders along with
  // transactions for the orders which have entries in the DB and ignore the requests for which orders doesn't exist.
  // It will return record not found if there is no entry for any of the batch request.
  rpc GetOrdersWithTransactions (GetOrdersWithTransactionsRequest) returns (GetOrdersWithTransactionsResponse) {}

  // GetTimelineOrdersWithTransactions fetches a list of orders along with the first transaction between two actors.
  // as displayed in the timeline.
  //
  // The order list can grow very big with time.
  // Thus, rpc method returns the list in pages. It returns all the order from specified start_timestamp.
  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  // The max number of order returned is bounded by the page_size specified in the request, while offset helps in
  // eliminating initial entries which are already returned in the last call.

  // Sorting logic - The orders are sorted based on transaction initiation time. Hence, results are sorted based on
  // transaction's create_at timestamp.
  // GetTimelineOrdersWithTransactionsRequest is used to fetch orders with transactions to be displayed as timelieve events on the timeline
  // NOTE:
  // by default, all the outbound orders are shown, to hide an order for given workflow and status it has to be passed explicitly in the request
  // by default, only paid and fulfilled inbound orders are shown, to show an order for given workflow and status it has to be passed explicitly in the request
  rpc GetTimelineOrdersWithTransactions (GetTimelineOrdersWithTransactionsRequest) returns (GetTimelineOrdersWithTransactionsResponse) {}

  // GetUpdatedTimelineOrdersWithTransactions fetches a list of updated orders along with the first transaction between two actors.
  // as displayed in the timeline.
  // The API is meant to be used in conjunction with `GetTimelineOrdersWithTransactions`. To get updates on timeline events
  // pre fetched by the client.
  //
  // The order list can grow very big depending on the start time being passed.
  // Hence, the number of result is bounded by the limit provided in the request.
  // The results are sorted in ASCENDING manner from the given start timestamp (INCLUSIVE).
  //
  // Sorting logic - The orders are sorted based on order last updated timestamp in ASCENDING manner.
  // NOTE: paginating over updated time can lead to blind spots. Hence, this API shouldn't be used
  // as paginated API.
  rpc GetUpdatesOfTimelineOrdersWithTransactions (GetUpdatesOfTimelineOrdersWithTransactionsRequest) returns (GetUpdatesOfTimelineOrdersWithTransactionsResponse) {}

  // ValidateAndGetOrder helps with validations on the order before the payment initialisation.
  // A payment orchestrator can make use of this RPC before initialising a payment.
  // Validations can include, but not limited to:
  //  1. Time duration between order creation and payment initialisation.
  //    E.g., There can be time bound orders where price of an item say gold fluctuates and is locked for the next 5 minutes.
  //    There is no point in initialising a payment post this duration as this would cause great overhead
  //    w.r.t reconciliation and refunds.
  //  2. checks if order is in CREATED state.
  //  3. Checks if order belongs to the current actor.
  // The RPC returns respective status code in case of validation checks fail
  // Order is only returned if validations pass.
  rpc ValidateAndGetOrder (ValidateAndGetOrderRequest) returns (ValidateAndGetOrderResponse) {}

  // RPC method to update an order as per the field mask provided in the request
  // Only fields specified in field masks are updated.
  //
  // Important Note: the call succeeds even if there is no record in DB for the given order id
  // To begin with update of status and order payload is only allowed more fields will be allowed when use case arises
  rpc UpdateOrder (UpdateOrderRequest) returns (UpdateOrderResponse) {}

  // RPC to dismiss a collect request
  // A client can use this when a user wants to dismiss a collect request. A user can dismiss a collect request in the following scenarios-
  //  1) On receiving a collect request from another actor be it internal or external.
  //  2) When a user sends a collect request to an internal epiFi user.
  //
  //  Conditions for Dismiss:
  //   1) Collect order should be in CREATED/COLLECT_REGISTERED state
  //      and
  //   2) Transaction (if present) should be in CREATED state
  rpc DismissCollect (DismissCollectRequest) returns (DismissCollectResponse);

  // RPC method to create an order and transaction.
  // It makes sures that order and transaction entry are created in a single DB transactional block.
  // The expected outcome is a single all-or-nothing. If a DB transaction succeeds, all mutations are applied
  // together with virtual simultaneity. If any part of a DB transaction fails, the entire transaction is aborted,
  // and the database is left unchanged.
  //
  // The method also ensures that order update events are published in case order is created with one of
  // order publishable state
  rpc CreateOrderWithTransaction (CreateOrderWithTransactionRequest) returns (CreateOrderWithTransactionResponse) {}

  // RPC method to get an order receipt
  // An order receipt contains the details of order stage, that can be used to display
  // the journey of order to the user.
  // It also returns the order and associated transactions details. Client may use this info to
  // populate unique identifiers for payment.
  rpc GetReceiptDetails (GetReceiptDetailsRequest) returns (GetReceiptDetailsResponse);

  // GetSuccessOrdersWithTransactionsForActor fetches a list of orders along with the first transactions belonging to a actor.
  // The order list can grow very big with time.
  // Thus, rpc method returns the list in pages. It returns all the order from specified start_timestamp.
  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  // The max number of order returned is bounded by the page_size specified in the request, while offset helps in
  // eliminating initial entries which are already returned in the last call.
  //
  // Sorting logic - The orders are sorted based on last updated timestamp of the order.
  // Deprecated: This rpc was written to serve the all transactions page
  // This is not recommended to be used by the internal services since it fetches all the transactions for an actor in pagination manner
  // Heavy query is being triggered in this rpc which can easily bring CRDB down
  // If any requirement is there regarding orders and transactions for an actor, please consult with Pay team
  rpc GetSuccessOrdersWithTransactionsForActor (GetSuccessOrdersWithTransactionsForActorRequest) returns (GetSuccessOrdersWithTransactionsForActorResponse) {
    option deprecated = true;
  };

  // GetSelectedOrdersWithTransactions fetches a list of orders along with the first transactions belonging to an actor within start and end timestamps
  //  all the later pagination calls will not include any recent order changes post time_travel_query_timestamp since it uses time travel query internally, this is required due to the dynamic nature of the order workflow which have multiple status based upon different legs an order can have and this can break the pagination logic
  // The order list can grow very big with time.
  // Thus, rpc method returns the list in pages. It returns all the order from specified start_timestamp.
  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  // The max number of order returned is bounded by the page_size specified in the request, while offset helps in
  // eliminating initial entries which are already returned in the last call.
  // Right now we only support following order status [IN_PAYMENT, FAILED, SUCCESSFUL, REVERSED]
  // Sorting logic - The orders are sorted based on last updated timestamp of the order.
  rpc GetSelectedOrdersWithTransactions (GetSelectedOrdersWithTransactionsRequest) returns (GetSelectedOrdersWithTransactionsResponse);


  // GetOrderDetails fetches the order related to an order id. It also fetches the transactions associated
  // with an order. The request message contains an order id which is an unique id for each order.
  // The service returns details corresponding to the first transaction belonging to the order.
  rpc GetOrderWithTransactions (GetOrderWithTransactionsRequest) returns (GetOrderWithTransactionsResponse);

  // RaiseDisputeForTransaction is used for raising dispute for a transaction. Dispute can be raised only in the following cases :
  // 1. For UPI transaction dispute can be raised only after 24 hours of the transaction and for NEFT/IMPS/RTGS/IntraBank
  //    dispute can we raised after 3 hours of transaction
  // 2. Dispute cannot be raised for a transaction whose Status is expired or cancelled
  // 3. Dispute can be raised only once for a transaction
  rpc RaiseDisputeForTransaction (RaiseDisputeForTransactionRequest) returns (RaiseDisputeForTransactionResponse);

  // GetOrdersForActor fetches list of orders belonging to an actor (where actor is either from or to).
  // List can be filtered with:
  // 1. Type of transaction
  // 2. Status of order
  // 3. `from_time` and `to_time` compared with order updated time
  //   a. If both are specified, list between time range would be returned in ascending order
  //   b. If only `from_time` is specified, orders will be returned from given time in ascending oder
  //   c. If only `to_time` is specified, orders will be returned until the given time
  // The order list can grow very big with time.
  // Thus, rpc method returns the list in pages.
  // The max number of order returned is bounded by the page_size specified in the request, while offset helps in
  // eliminating initial entries which are already returned in the last call.
  // Sorting logic - The orders can be sorted on the basis of the sort_by column passed in the request
  rpc GetOrdersForActor (GetOrdersForActorRequest) returns (GetOrdersForActorResponse);

  // GetActorsFromReqId fetches the actors (both from and to) which are involved in a transaction.
  // The request message contains an request id which is a unique id for each transaction.
  rpc GetActorsFromReqId (GetActorsFromReqIdRequest) returns (GetActorsFromReqIdResponse);

  // ForceProcessOrder is a sync RPC to be used to force order order processing at any given
  // point of time.
  //
  // Generally order processing happens abides by the retry strategies defined in the workflow config.
  // However, in certain scenarios we might want to force order processing due to business requirement.
  // This can be due various reasons but not limited to -
  // 1) Enquire and move order to terminal state as next attempt is scheduled after X hrs.
  // 2) Enquire and update order in MANUAL_INTERVENTION post it has been processed by domain service.
  // 3) Enquire and update order in terminal state. In case there is a discrepancy between order state
  //    and domain state
  // NOTE: the RPC is meant to be triggered manually either using some script or from sherlock
  rpc ForceProcessOrder (ForceProcessOrderRequest) returns (ForceProcessOrderResponse);

  // GetTotalAmount returns the aggregate amount of all the orders within set of workflow, payment protocol and status filters
  // fromActorId and toActorId are optional, but one of fromActor or toActorId must be present
  //
  // The method function as follows-
  // If `FromTime` and `ToTime` both are present then resultant orders are bounded by these timestamp and sorted in ASCENDING manner
  // If only `FromTime` is present then resultant orders are bounded with a lower bound defined by fromTime
  // If only `ToTime` is present then resultant orders are bounded with an upper bound defined by toTime
  // If both FromTime and ToTime are empty then `InvalidArgument` error is returned
  //
  // NOTE - the amount returned is in paisa. Only orders which have transactions are considered
  rpc GetTotalAmount (GetTotalAmountRequest) returns (GetTotalAmountResponse);

  // GetTotalTransactionCount returns the number of transactions within set of workflow, payment protocol and status filters
  // fromActorId and toActorId are optional, but one of fromActor or toActorId must be present
  //
  // The method function as follows-
  // If `FromTime` and `ToTime` both are present then resultant orders are bounded by these timestamp and sorted in ASCENDING manner
  // If only `FromTime` is present then resultant orders are bounded with a lower bound defined by fromTime
  // If only `ToTime` is present then resultant orders are bounded with an upper bound defined by toTime
  // If both FromTime and ToTime are empty then `InvalidArgument` error is returned
  //
  // Only orders which have transactions are considered
  // Deprecated: In favour of pay.GetTransactionAggregates: Query for this RPC will impact CRDB cluster.
  rpc GetTotalTransactionCount (GetTotalTransactionCountRequest) returns (GetTotalTransactionCountResponse);

  // rpc to persist add fund option for an actor
  //
  // The method function as follows-
  // Fetches the latest option for the actor
  // If the latest option is same as the option passed in rpc returns
  // else created a new entry with the new option passed in rpc
  rpc PersistAddFundOption (PersistAddFundOptionRequest) returns (PersistAddFundOptionResponse);

  // rpc to initiate order processing for order workflows where auto trigger is off.
  // To maintain idempotency we will check if the order is currently in the first stage of the workflow
  rpc InitiateOrderProcessing (InitiateOrderProcessingRequest) returns (InitiateOrderProcessingResponse);

  // RPC to update existing orders with new to_actor_ids.
  // It will replace all the old to_actor_ids pass in the request with the new to_actor_id passed in the request
  // It will be publish this update event to sns topic for downstream services to consume
  rpc UpdateOrdersWithNewToActorIds (UpdateOrdersWithNewToActorIdsRequest) returns (UpdateOrdersWithNewToActorIdsResponse);

  // RPC to update existing orders with new from_actor_ids.
  // It will replace all the old from_actor_ids pass in the request with the new from_actor_id passed in the request
  // It will be publish this update event to sns topic for downstream services to consume
  rpc UpdateOrdersWithNewFromActorIds (UpdateOrdersWithNewFromActorIdsRequest) returns (UpdateOrdersWithNewFromActorIdsResponse);

  // An order is placed between two actors ( from_actor_id -> to_actor_id)
  // Rpc to get all unique from_actor_ids for the to_actor_ids passed in the request.
  // It provides all the actor who has placed any order to actors passed in to_actor field of request.
  rpc GetDistinctFromActorIdsByToActorIds (GetDistinctFromActorIdsByToActorIdsRequest) returns (GetDistinctFromActorIdsByToActorIdsResponse);

  // An order is placed between two actors ( from_actor_id -> to_actor_id)
  // Rpc to get all unique to_actor_ids for the from_actor_ids passed in the request.
  // It provides all the actor who has placed any order to actors passed in from_actor field of request.
  rpc GetDistinctToActorIdsByFromActorIds (GetDistinctToActorIdsByFromActorIdsRequest) returns (GetDistinctToActorIdsByFromActorIdsResponse);

  // GetOrdersForActorMultiplex fetches list of orders belonging to an actor (where actor is either from or to).
  // List can be filtered with:
  // 1. Type of transaction
  // 2. Status of order
  // 3. `from_time` and `to_time` compared with order updated time
  //   a. If both are specified, list between time range would be returned in ascending order
  //   b. If only `from_time` is specified, orders will be returned from given time in ascending oder
  //   c. If only `to_time` is specified, orders will be returned until the given time
  // The order list can grow very big with time.
  // Thus, rpc method returns the list in pages.
  // The max number of order returned is bounded by the page_size specified in the request.
  //
  // This RPC is wrapper over GetOrdersForActor. For transaction type both, it will call GetOrdersForActor two time one for
  // credit and one for debit.
  rpc GetOrdersForActorWithNoOffset (GetOrdersForActorWithNoOffsetRequest) returns (GetOrdersForActorWithNoOffsetResponse);

  // CreateOrderMetadata RPC is used to create an orderMetadata entry in the database.
  rpc CreateOrderMetadata (CreateOrderMetadataRequest) returns (CreateOrderMetadataResponse);

  // GetByOrderId RPC is used to fetch all the orderMetadata with the given OrderId and Metadataapi.typesv2.
  rpc GetByOrderId (GetByOrderIdRequest) returns (GetByOrderIdResponse);

  // RecordOffAppPayment rpc for recording off app payment request, it takes raw and parsed transaction details as the
  // input and creates order and transaction for the given raw details.
  //
  // It also supports OrderWorkflow under which the Order has to be created. If not passed, NO_OP should be expected by-default.
  rpc RecordOffAppPayment (RecordOffAppPaymentRequest) returns (RecordOffAppPaymentResponse);

  // DedupeTransaction performs the process of deduping by first matching the passed details for an existing transaction.
  // It then proceeds to perform a series of checks to decide whether a new order can be created with the details or not.
  // Note: Internally it will also end up enriching the existing transaction if found.
  rpc DedupeTransaction (DedupeTransactionRequest) returns (DedupeTransactionResponse);
}

message DedupeTransactionRequest {
  // Vendor bank where transaction occurred
  vendorgateway.Vendor partner_bank = 1;
  // raw transaction details
  order.payment.notification.TransactionDetails txn_details = 2;
  // parsed transaction details
  order.payment.notification.ParsedTxnParticulars parsed_txn_particulars = 3;
  // source of the txn details.
  // expected values: NOTIFICATION_CALLBACK, STATEMENT
  order.payment.TransactionDetailedStatus.DetailedStatus.API source = 4;
  // identifier to the user whom the txn-details belong to.
  // single identifier usage is enforced to avoid possibility of incorrect combination of identifiers passed.
  oneof user_identifier {
    // (internal) savings account id for which these txn details were received
    string savings_account_id = 9 [(validate.rules).string.min_len = 1];
  }
}

message DedupeTransactionResponse {
  enum Status {
    // OK to be returned when we find an existing transaction and its corresponding
    // order in the system.
    // Refer boolean field CreateNewOrder if a new order can be created or not.
    OK = 0;

    // INVALID_ARGUMENT to be returned in case the request received doesn't contain all the
    // required fields
    INVALID_ARGUMENT = 3;

    // NOT_FOUND to be returned when no transaction is found in the system using the passed details.
    // Refer boolean field CreateNewOrder if a new order can be created or not.
    NOT_FOUND = 5;

    // PERMANENT_ERROR to be returned when dedupe process would fail for sure upon retrying.
    // For e.g., in case of inconsistent data / missing partial entities.
    PERMANENT_ERROR = 101;

    // TRANSIENT_ERROR to be returned when dedupe process fails temporarily and can yield some result
    // upon retrying.
    TRANSIENT_ERROR = 102;
  }

  rpc.Status status = 1;
  // transaction which was found using the details passed for dedupe process
  order.payment.Transaction transaction = 2;
  // order of the corresponding txn which was found upon dedupe
  order.Order order = 3;
  // during the dedupe process, parsed-txn-particulars can get enriched with more/updated details in the
  // scenario where a new order has to be created.
  // If not enriched, it will be same as the one received in the request.
  // Thus, if `CreateNewOrder` says true, caller can use the updated particulars to create the new order & txn.
  order.payment.notification.ParsedTxnParticulars enriched_parsed_txn_particulars = 4;
  // flag to tell whether new order should be created with the details received from vendor
  bool create_new_order = 5;
  // Flag which tells whether the txn details received indicate a reversal or not.
  // This can be used along with CreateNewOrder to determine the course of action for the txn details received.
  // For e.g., CreateNewOrder: true, IsReversal: true -> Could mean that a new order has to be created for the reversal txn as the original is already in success state.
  bool is_reversal = 6;
}

message RecordOffAppPaymentRequest {
  // savings account id for the user
  string savings_account_id = 1;

  // actor id
  string internal_actor_id = 2;

  accounts.Type account_type = 3;

  // raw transaction details
  order.payment.notification.TransactionDetails txn_details = 4;

  // parsed transaction details
  order.payment.notification.ParsedTxnParticulars parsed_txn_particulars = 5;

  // partner bank
  vendorgateway.Vendor partner_bank = 6;

  // transaction status
  order.payment.TransactionStatus transaction_status = 7;

  // transaction detailed status contains details of the transaction
  order.payment.TransactionDetailedStatus transaction_detailed_status = 8;

  // boolean to determine if this the last attempt for an off app payment processing
  bool is_last_attempt = 9;

  // Order Workflow associated with the off-app payment
  // Note: not all values are permitted here.
  // todo(rohan): update the permitted values once logic is implemented
  order.OrderWorkflow order_workflow = 10;

  // timestamp when the txn details were received from the vendor
  google.protobuf.Timestamp received_from_vendor_at = 11;

  // boolean to indicate that the flow can attempt remitter-info-backfill workflow invocation if applicable
  bool attempt_remitter_info_backfill = 12;
}

message RecordOffAppPaymentResponse {
  enum Status {
    OK = 0;

    // INTERNAL to be returned for Unknown and Transient errors and there is a chance
    // that it will work upon retrying.
    INTERNAL = 13;

    // PERMANENT_FAILURE to be returned when we are sure that retrying won't help.
    PERMANENT_FAILURE = 100;
  }

  rpc.Status status = 1;

  // order and its respective transactions which were recorded in the system
  order.OrderWithTransactions order_with_transactions = 2;
}

// CreateOrder request message
message CreateOrderRequest {
  // actor who initiated the order
  string actor_from = 1;

  // receiving entity of the order
  string actor_to = 2 [(validate.rules).string.min_len = 1];

  // workflow that the order needs to follow.
  order.OrderWorkflow workflow = 5 [(validate.rules).enum = {not_in: [0]}];

  // provenance of the order
  order.OrderProvenance provenance = 6 [(validate.rules).enum = {not_in: [0]}];

  // An opaque blob containing the data needed for fulfillment of an order.
  // This might vary based on the use case. The data inside the blob will
  // depend on underlying domain service.
  // Note: below payload is to be UTF-8 encoded & stored in the database as JSONB.
  // Thus, use "encoding/json" while marshaling
  bytes order_payload = 7;

  // order amount
  google.type.Money amount = 8 [(validate.rules).message.required = true];

  // status of order with which order needs to be created.
  // in certain scenarios like incoming payments we need to create order with IN_PAYMENT, SUCCESS, or FAILURE state
  // as well.
  order.OrderStatus status = 9 [(validate.rules).enum = {not_in: [0]}];

  // tags associated with the order
  repeated OrderTag tags = 10;

  // device of the current user
  api.typesv2.common.Device device = 11;

  // Optional: expiry timestamp of the order
  // if left empty expiry duration is set based on the workflow
  // Some of the use case include -
  // creating order for an incoming collect
  google.protobuf.Timestamp expire_at = 12;

  // signifies the entry point on the client for order creation eg. QR, intent
  order.UIEntryPoint ui_entry_point = 13;

  // is_dynamic_qr_initialised denotes if the order creation is initialised via dynamic qr scan
  bool is_dynamic_qr_initialised = 14;

  // Optional: request id to be sent by client in case idempotency around
  // order creation is important for the caller use case. This can be typically
  // used for automated workflows like B2C_FUND_TRANSFER where single order per request
  // is important.
  // client_req_id must be a valid UUID (via RFC 4122)
  string client_req_id = 15;

  // Obfuscated GPS coordinates location identifier for the payer.
  // Since, location identifier is a sensitive user information, it's not
  // recommended to store this data directly in the order domain object.
  // A location token is a place holder for the exact user co-ordinates.
  // Co-ordinates for the specified token can be fetched using location service's
  // GetCoordinates RPC
  // Note - Optional field, will be populated only if the payer's location is known
  string from_actor_location_token = 16;

  // Obfuscated GPS coordinates location identifier for the payee.
  // Since, location identifier is a sensitive user information, it's not
  // recommended to store this data directly in the order domain object.
  // A location token is a place holder for the exact user co-ordinates.
  // Co-ordinates for the specified token can be fetched using location service's
  // GetCoordinates RPC
  // Note - Optional field, will be populated only if the payee's location is known
  string to_actor_location_token = 17;

  // external is internal id equivalent for an order,
  // that can be shared with actor or any other external system inorder to identify an order uniquely in the system.
  // It can be typically used in places where for
  // security reasons we don't want to expose internal id to the outside world
  // [optional]: If not passed by the client, some default logic is already in place
  // to generate the external_id.
  string external_id = 18;

  // This deeplink is where user is redirected post successful payment. Irrespective of it's state
  // i.e. success or failure.
  // If not passed by default the client is routed to pay landing screen.
  // We are not directly using deeplink here to avoid frontend dependency in the root domain proto
  frontend.deeplink.Deeplink post_payment_deeplink = 19;
}

// CreateOrder response message
message CreateOrderResponse {
  enum Status {
    OK = 0;
    // for a collect request(non short circuit) there is a max limit of 5,0000 per transaction
    AMOUNT_LIMIT_EXCEEDED = 100;

    // a user has raise more collect request than provided quota
    // for protection against fraud cases NPCI has enforced a limit
    // of 5 collect request in 24hrs.
    COLLECT_VELOCITY_LIMIT_EXCEEDED = 101;
    // cool off validation has failed
    // for a newly registered user/ or a user who has changed the device,
    // there is cool off period for 24 hrs.
    // in the cool off period the user have certain restrictions on the
    // transactions like the total amount should be less then 5k
    COOL_OFF_VALIDATION_FAILED = 102;
    // amount limit for the urn payment exceeded
    // for QR/intent based payments there is a limit on max value per txn
    // based on if the qr/intent is signed or not
    URN_AMOUNT_LIMIT_EXCEEDED = 103;
    // order creation failure as bank/vendor is in
    // CSIS downtime and transaction to/from min-kyc user will fail
    CSIS_DOWNTIME = 104;
    // amount addition not allowed for the user, since name match check has failed
    ADD_FUNDS_NAME_MATCH_FAILED = 110;
    // add funds failure due to account duration check failed for a min kyc kyc user
    ADD_FUNDS_MIN_KYC_ACCOUNT_DURATION_CHECK_FAILED = 111;
    // add funds failure due to maximum credit limit check failed for a min kyc kyc user
    ADD_FUNDS_MIN_KYC_MAX_CREDIT_LIMIT_CHECK_FAILED = 112;
    // add funds failure due to maximum balance check failed for a min kyc kyc user
    ADD_FUNDS_MIN_KYC_MAX_BALANCE_CHECK_FAILED = 113;
    // for certain mccs only intent is allowed as entry point
    // this status will be returned if the payment is initiated using an invalid entry point
    INVALID_ENTRY_POINT = 114;
  }

  rpc.Status status = 1;

  // unique identifier to the order created
  order.Order order = 2;

  // signifies if PIN is required for the transaction initiation.
  // The boolean flag is calculated in the system based on various risk parameters like order workflow, payment protocol, amount, etc.
  bool is_pin_required = 3;
}

// GetOrder request message
message GetOrderRequest {
  oneof identifier {
    string order_id = 1;
    string external_id = 2;
    string client_req_id = 3;
  }
  api.typesv2.common.Ownership entity_ownership = 4;
}

// GetOrder response message
message GetOrderResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  order.Order order = 2;
}

// GetTimelineOrdersWithTransactions request message
message GetTimelineOrdersWithTransactionsRequest {
  // unique ids of actors for whom orders and transactions records needs to be fetched.
  // It is important for the server to know the POV as the results vary accordingly.
  // e.g. failed orders are only shown to the user who initialized the order

  // actor to whom the current session belongs to.
  // TODO(nitesh): check if this can be fetched from context
  string current_actor_id = 1 [(validate.rules).string.min_len = 1];

  // actor against which orders needs to be fetched
  string second_actor_id = 2 [(validate.rules).string.min_len = 1];

  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp start_timestamp = 3 [(validate.rules).timestamp.required = true];

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // Page size must be in the range [10, 40]
  // minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
  int32 page_size = 4 [(validate.rules).int32 = {gte: 10, lte: 40}];

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 offset = 5 [(validate.rules).int32.gte = 0];

  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  bool is_descending = 6;

  // txn field masks to ensure only desired fields are returned from transactions table.
  // this helps in optimization fo data returned and make the query more efficient.
  // caller can choose to get all field of transaction by specifying ALL in field mask
  // Only first transaction corresponding to an order is returned.
  // NOTE- ALL supported is not implemented yet. TODO(nitesh): Add support for querying by ALL field.
  repeated order.payment.TransactionFieldMask txn_field_masks = 7;
  // hide_outbound_status_and_workflow_filter is useful to hide the outbound orders for given workflow and filter
  repeated order.OrderStatusAndWorkflowTypeFilter hide_outbound_status_and_workflow_filter = 8;
  // show_inbound_status_and_workflow_filter is useful to show the inbound orders for given workflow and filter
  repeated order.OrderStatusAndWorkflowTypeFilter show_inbound_status_and_workflow_filter = 9;
}

// GetTimelineOrdersWithTransactions response message
message GetTimelineOrdersWithTransactionsResponse {
  enum Status {
    OK = 0;

    // in case orders are not found for a given set of request params.
    RECORD_NOT_FOUND = 5;
  }

  rpc.Status status = 1;

  // ordered list of orders and transactions
  // Note - As of now the RPC returns details corresponding to only the first transaction
  // belonging to the order.
  repeated order.OrderWithTransactions orders_with_txns = 2;
}

// GetUpdatesOfTimelineOrdersWithTransactions request message
message GetUpdatesOfTimelineOrdersWithTransactionsRequest {
  // unique ids of actors for whom orders and transactions records needs to be fetched.
  // It is important for the server to know the POV as the results vary accordingly.
  // e.g. failed orders are only shown to the user who initialized the order

  // actor to whom the current session belongs to.
  // TODO(nitesh): check if this can be fetched from context
  string current_actor_id = 1 [(validate.rules).string.min_len = 1];

  // actor against which orders needs to be fetched
  string second_actor_id = 2 [(validate.rules).string.min_len = 1];

  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp start_timestamp = 3 [(validate.rules).timestamp.required = true];

  // limit determines the upper bound on the number of records
  // returned in a particular response.
  // limit must be in the range [1, 30]
  int32 limit = 4 [(validate.rules).int32 = {gte: 1 lte: 30}];

  // txn field masks to ensure only desired fields are returned from transactions table.
  // this helps in optimization fo data returned and make the query more efficient.
  // caller can choose to get all field of transaction by specifying ALL in field mask
  // Only first transaction corresponding to an order is returned.
  // NOTE- ALL supported is not implemented yet. TODO(nitesh): Add support for querying by ALL field.
  repeated order.payment.TransactionFieldMask txn_field_masks = 5;
}

// GetUpdatesOfTimelineOrdersWithTransactions response message
message GetUpdatesOfTimelineOrdersWithTransactionsResponse {
  enum Status {
    OK = 0;

    // in case orders are not found for a given set of request params.
    RECORD_NOT_FOUND = 5;
  }

  rpc.Status status = 1;

  // ordered list of orders and transactions.
  // The result is sorted in ASCENDING manner based on order
  // last updated timestamp.
  // Note - As of now the RPC returns details corresponding to only the first transaction
  // belonging to the order.
  repeated order.OrderWithTransactions orders_with_txns = 2;
}

message ValidateAndGetOrderRequest {
  // actor id belonging to the user who is initiating the payment
  string actor_id = 1;

  // unique id of the order against which payment is being initialised
  string order_id = 2;

  // device of the current order
  api.typesv2.common.Device device = 3;
}

message ValidateAndGetOrderResponse {
  enum Status {
    // order is valid
    OK = 0;
    // order record not found
    RECORD_NOT_FOUND = 5;
    // the caller user doesn't have the permission to access the order as the order doesn't belong to him/her
    PERMISSION_DENIED = 7;
    // order has expired, payment shouldn'be initiated against this order
    ORDER_EXPIRED = 100;
    // order status is in valid. Typically returned when order is not in `CREATED` state.
    ORDER_STATUS_INVALID = 101;
    // cool off validation has failed
    // for a newly registered user/ or a user who has changed the device,
    // there is cool off period for 24 hrs.
    // in the cool off period the user have certain restrictions on the
    // transactions like the total amount should be less then 5k
    COOL_OFF_VALIDATION_FAILED = 102;
  }

  rpc.Status status = 1;

  // details of the order are returns in case the order is valid
  order.Order order = 3;

  // signifies if PIN is required for the transaction initiation.
  // The boolean flag is calculated in the system based on various risk parameters like order workflow, payment protocol, amount, etc.
  bool is_pin_required = 4;
}

message UpdateOrderRequest {
  // NOTE: order id must be present to update order
  order.Order order = 1 [(validate.rules).message.required = true];

  // order field masks to ensure only desired fields are updated in transaction.
  repeated order.OrderFieldMask field_masks = 2 [(validate.rules).repeated.min_items = 1];
}

message UpdateOrderResponse {
  enum Status {
    OK = 0;

    // internal server error. Can be due to various reason e.g. DB unavailable
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}

message DismissCollectRequest {
  // actor id of the user initiating DismissCollectRequest
  string current_actor_id = 1;
  // A unique identifier to represent ta collect request
  // Order ID is a concept between client and Epifi and is abstracted
  // from the partner banks.
  string order_id = 2;
}

message DismissCollectResponse {
  enum Status {
    OK = 0;
    // request parameters invalid. It can be due to a bunch of factors like order id invalid etc.
    INVALID_ARGUMENT = 3;
    // order does not exist
    RECORD_NOT_FOUND = 5;
    // user doesn't have access to perform operation on the given order id
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
    // order status is in valid. Returned when the order is not in CREATED and not in COLLECT_REGISTERED state
    ORDER_STATUS_INVALID = 100;
    // order workflow invalid. Returned for a non collect order workflow
    ORDER_WORKFLOW_INVALID = 101;
    // transaction status invalid. Returned for a transaction not in CREATED state
    TRANSACTION_STATUS_INVALID = 102;
  }
  // denotes the status of the DismissCollect Request
  rpc.Status status = 1;

  // updated order after dismissing the collect
  order.Order order = 2;
}

message CreateOrderWithTransactionRequest {
  // consolidated set of parameters required to create an order
  message OrderCreationParams {
    // actor who initiated the order
    string actor_from = 1 [(validate.rules).string.min_len = 1];

    // receiving entity of the order
    string actor_to = 2 [(validate.rules).string.min_len = 1];

    // workflow that the order needs to follow.
    order.OrderWorkflow workflow = 5 [(validate.rules).enum = {not_in: [0]}];

    // provenance of the order
    order.OrderProvenance provenance = 6 [(validate.rules).enum = {not_in: [0]}];

    // An opaque blob containing the data needed for fulfillment of an order.
    // This might vary based on the use case. The data inside the blob will
    // depend on underlying domain service.
    // Note: below payload is to be UTF-8 encoded & stored in the database as JSONB.
    // Thus, use "encoding/json" while marshaling
    bytes order_payload = 7;

    // status of order with which order needs to be created.
    // in certain scenarios like incoming payments we need to create order with IN_PAYMENT, SUCCESS, or FAILURE state
    // as well.
    order.OrderStatus status = 9 [(validate.rules).enum = {not_in: [0]}];

    // tags associated with the order
    repeated OrderTag tags = 10;

    // device of the current user
    api.typesv2.common.Device device = 11;

    // Optional: expiry timestamp of the order
    // if left empty expiry duration is set based on the workflow
    // Some of the use case include -
    // creating order for an incoming collect
    google.protobuf.Timestamp expire_at = 12;

    // Optional: request id to be sent by client in case idempotency around
    // order creation is important for the caller use case. This can be typically
    // used for automated workflows like B2C_FUND_TRANSFER where single order per request
    // is important.
    // client_req_id must be a valid UUID (via RFC 4122)
    string client_req_id = 13;
  }

  // consolidated set of parameters required to create a transaction
  message TransactionCreationParams {
    // payment instrument from which asset need to be debited
    string pi_from = 1 [(validate.rules).string.min_len = 1];

    // payment instrument to which asset needs to be credited
    string pi_to = 2 [(validate.rules).string.min_len = 1];

    // user remarks / auto generated remarks
    string remarks = 4;

    // payment protocol chosen by decision engine for a particular transaction.
    order.payment.PaymentProtocol payment_protocol = 5 [(validate.rules).enum = {not_in: [0]}];

    // status of transaction to be created.
    // in certain scenarios like incoming payment, a transaction can be created with
    // intermittent state IN_PROGRESS or terminal states
    order.payment.TransactionStatus status = 6 [(validate.rules).enum = {not_in: [0]}];

    // optional: protocol status to be specified while creating a transaction
    // in certain scenarios like incoming payment, a transaction can be created with
    // intermittent state or terminal states
    order.payment.TransactionProtocolStatus protocol_status = 7;

    // contains necessary information related to bank payment request,
    // that can be inferred for sending requests and response while processing a transaction
    order.payment.PaymentRequestInformation req_info = 8;

    // OPTIONAL: transaction execution timestamp
    // To be used by client in case transaction entry is created post
    // payment is in terminal state
    google.protobuf.Timestamp executed_at = 9 [deprecated = true];

    // Transaction detailed status contains raw feedback we get from banks.
    // it's important to store this detailed data, as it will help us to understand and debug the reason of failure of a transaction.
    // e.g. A transaction can fail due to multiple reasons, broadly we can categorize them as transient and permanent failure -
    // 1. Permanent failure - Insufficient balance, Debit Frozen, Account Frozen, Transfer limit exceeded, etc.
    // 2. Transient failure - CBS connection failure, CBS response timeout, Server is down please try after sometime.
    // all of these detailed status are stores in below message.
    //
    // for certain use cases like upi transactions, we need to create a transaction when we get a reqTxnConfirmation
    // hence we also need to add detailed status while creating the transaction
    order.payment.TransactionDetailedStatus detailed_status = 10;

    // Unique Transaction Reference for Online Txns like NEFT/IMPS/UPI
    // Can be RRN in case of card payments
    string utr = 11;

    // external payment Ref referring to the partner bank’s transaction
    string partner_ref_id = 12;

    // timestamp at which  the amount is debited
    google.protobuf.Timestamp debited_at = 13;

    // timestamp at which  the amount is credited
    google.protobuf.Timestamp credited_at = 14;

    // batch serial-id for transaction. It is received for off app transaction ( in notification / recon via statement)
    // required and mandatory for all off-app transaction
    string batch_serial_id = 15;

    // cbs-id received from vendor for off-app transaction (CBS -> core banking service)
    // It is required & mandatory for all off-app transaction.
    string cbs_id = 16;

    // ownership under which transaction data is persisted
    api.typesv2.common.Ownership ownership = 17;

    // raw_notification_details is a map where key could be debit and credit and the values include particular,
    // additional_details, batch serial id, instrument details, value date
    map<string, order.payment.NotificationDetails> raw_notification_details = 18;

    message DedupeId {
      // We selectively expose only the transaction timestamp to be overridden since other fields are determined from
      // the transaction details and we don't see any need for overriding them.
      google.protobuf.Timestamp override_dedupe_time = 1;
    }

    // dedupe_id is used to override any parameter in the dedupe id field of the transaction.
    // This field should be used by the clients only when all the necessary details for creating the dedupe ID
    // cannot be passed in the transaction (for ex, we can't store the execution attempt timestamp for a transaction,
    // where money movement hasn't happened in debited_at/credited_at, but we need the timestamp in dedupe ID, in
    // such cases, the override_dedupe_id field can be used).
    DedupeId override_dedupe_id = 19;
  }

  OrderCreationParams order_param = 1;

  TransactionCreationParams transaction_param = 2;

  // amount of money required
  google.type.Money amount = 3 [(validate.rules).message.required = true];

  // flag to disable workflow processing through OMS
  bool disable_workflow_processing = 4;
}

message CreateOrderWithTransactionResponse {
  enum Status {
    OK = 0;
    // request parameters invalid. It can be due to a bunch of factors
    // for instance if payer and payee pi being same in transaction creation params
    INVALID_ARGUMENT = 3;
    // internal server error. Can be due to various reason e.g. DB unavailable
    // or DB transaction failure, etc.
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // order corresponding to the entry created
  order.Order order = 2;

  // transaction corresponding to the entry created
  order.payment.Transaction transaction = 3;
}

message GetReceiptDetailsRequest {
  // actor to whom the current session belongs to.
  // TODO(nitesh): check if this can be fetched from context
  string current_actor_id = 1;

  // unique order identifier
  string order_id = 2 [deprecated = true];

  oneof identifier {
    string orders_id = 3;
    string aa_txn_id = 4;
  }
}

message GetReceiptDetailsResponse {
  reserved 7, 11;

  enum Status {
    OK = 0;
    // request parameters invalid. It can be due to a bunch of factors like order id invalid etc.
    INVALID_ARGUMENT = 3;
    // order does not exist
    RECORD_NOT_FOUND = 5;
    // user doesn't have access to perform operation on the given order id
    // one of the reason could be logged in user is not involved in the order
    PERMISSION_DENIED = 7;
    // internal server error
    INTERNAL = 13;
  }
  // denotes the status of the Request
  rpc.Status status = 1;

  // Defines the order stage
  // A stage is nothing but description of an order step of the order execution
  // along with the status
  message OrderStage {
    enum Status {
      // Default value for the status enum.
      STATUS_UNSPECIFIED = 0;
      // Order stage is yet to be started
      PENDING = 1;
      // Order stage has started bu yet to be finished
      IN_PROGRESS = 2;
      // Order stage has finished successfully
      SUCCESS = 3;
      // Order stage execution has failed
      FAILED = 4;
    }
    Status status = 1;

    // Detailed description of the status in a human understandable language. The
    // client may choose to display this string as-is to the user.
    string localized_description = 2;

    // timestamp belonging to the order stage execution
    google.protobuf.Timestamp execution_timestamp = 3;

    // human understandable version of execution timestamp
    string execution_time = 4;

    // epiFi error code corresponding to the order stage
    // this is only populated for failure scenarios
    string fi_err_code = 5;

    // Text giving more info about a particular stage.
    // for eg. in case of reversal will contain time stamp of
    // when the amount is credited back to the account
    string detailed_description = 6;
  }

  OrderStage overall_stage = 4;
  // the order narration to be displayed to the user
  // It gives details of all the possible steps involved in order execution
  // along with their status
  repeated OrderStage order_stages = 5;

  // bank account details from/to which txn has happened
  message BankAccountInfo {
    string bank_name = 1;
    string ifsc = 2;
    string display_info = 3;
  }

  message PaymentDetail {
    // user interpretable description of the payment detail
    string description = 1;

    // actual value of the payment details
    // It can contains a wide variety depending on payment detail
    // e.g. in case of payer/payee details the value will be the payment instrument used to make the payment
    // in case of external transaction id, it can be utr or rrn number to be referred by the customer
    string value = 2;

    // OPTIONAL - only sent in case of UPI transactions
    BankAccountInfo bank_account_info = 3;
  }

  // list of payment details returned with the receipt.
  // A user can use this information to share the transaction confirmation
  // with the payee or to raise a request against the payment.
  //
  // repeated list because the payment details to be displayed in the receipt
  // can vary from on case by case basis.
  // e.g. a bill payment order can have additional details like recharge fulfillment
  // id, along with the usual payment details.
  // or an order with refund or reversal can have reversal transaction id.
  //
  // Also, repeated list allows send back the response in the specified ordered
  // sequence to be displayed
  // while using a plan map would not honour the ordering here.
  repeated PaymentDetail details = 6;

  // amount of monies refunded back to the payer.
  // This field is only applicable to the orders involving
  // refund. The refunds can be partial or full depending on
  // case by case basis but not greater than the original amount.
  google.type.Money refund_amount = 10;

  message DisputeDetails {
    // timestamp of the moment dispute was raised
    google.protobuf.Timestamp disputed_at = 1;

    // dispute description to be displayed on the UI
    string description = 2;
  }

  // details of the dispute raised by the actor
  // it will be populated in case actor has already raised dispute
  DisputeDetails dispute_details = 12;

  message ReceiptHead {
    // Receipt heading
    // contains the heading of the receipts.
    // e.g. Received From in case of credit
    // and Paid To in case of debit
    string title = 1;

    // Subtitle of the receipt
    // contains details from/to where money movement happened
    // contains payer/payee actor information in case of payment
    // contains deposit information in case of deposit money movement
    string subtitle = 2;

    // Icon image url, to give user easy understanding on what the transaction
    // is about.
    string icon_url = 3;

    // Color code of the icon back ground. In the absence of icon url client can
    // generate an icon using the initials from subtitle
    string icon_colour_code = 4;

    // colour code for receipt background
    string bg_colour_code = 5;

    // amount of monies involved in the payment
    google.type.Money amount = 6;

    // a.k.a transaction notes
    // it can be entered by user as well system generated remarks
    string remarks = 7;

    // boolean to denote if the transaction is reversed.
    bool is_reversal = 8;

    // Bank Icon image url to denote the Bank's logo which carried the transaction
    string bank_icon_url = 9;

    // timeline_id for the timeline to which the order belongs to.
    // For certain orders timeline might not exist in such cases timeline id will be empty

    string timeline_Id = 10;

    // Icon image url to show the status of the order
    string status_icon = 11;

    // banner to show in case of pending payment
    // Deprecated, please use link_banner for similar use cases
    api.typesv2.common.Text banner = 12 [deprecated = true];

    // amount of money in foreign currency involved in payment in case of international payment
    google.type.Money BaseAmountQuoteCurrency = 13;

    // banner with deeplink based upon different scenerios (but not limited to)
    // ecs/enach charges
    // deemed transactions
    api.typesv2.ui.IconTextComponent deeplink_banner = 14;

    // categories data to show in receipt head
    order.TxnCategoriesData categories_data = 26;

    // Order receipt banner to be shown on the top of the receipt.
    // This can be used for various purposes such as:
    // 1. for taking the user to a story based on a combination of multiple attributes associated with the txn, such as status, payer-payee status-code etc.
    // 2. for redirecting the user to another related order-receipt.
    // 3. for show details regarding a dispute ticket.
    // Note: If this field is present, prioritise it over inapphelp_media_params. Else, fallback to the earlier logic.
    api.typesv2.ui.IconTextComponent order_receipt_banner = 28;
  }

  ReceiptHead receipt_head = 13;

  // internal transaction id against which
  // dispute can be raised by the user
  string transaction_id = 14;

  // external order id is human readable shorter analogous to the order id
  // to be used to display to the end user
  string order_external_id = 15;

  // payment protocol used for a particular transaction.
  order.payment.PaymentProtocol payment_protocol = 16;

  // list of components to be shown for receipt
  // eg- should dispute raise option be enabled, show get help section etc
  repeated order.ReceiptComponent receipt_components = 20;

  // list of ids for all the attached transactions for a given order
  // NOTE- for some others, we may not have txns. In such cases list might be empty
  repeated string txn_ids = 21;

  // Parameters to pass for showing story (or other inapphelp media) on the receipt screen
  // eg cases- UPI deemed success, UPI txn failure P2P, P2M etc
  // Deprecated: prioritise `OrderReceiptBanner` (within `ReceiptHead`) over this field.
  api.typesv2.InapphelpMediaUIContextMeta.TxnReceiptMeta inapphelp_media_params = 22 [deprecated = true];

  // banner which will be shown on the order receipt based on payment protocol
  string payment_info_banner = 23;

  // Can Contain mupltiple ctas from list of UPI help, Get Help and FAQs.
  repeated api.typesv2.ui.IconTextComponent footer_ctas = 24;

  // flag to enable or disable user caution
  api.typesv2.ui.IconTextComponent user_caution = 25;

  // Defines the tag associated with the order, e.g. Deposit, FD, etc.
  // An order can be associated with multiple tags.
  repeated order.OrderTag order_tags = 26;

  // when this flag is true we allow the user to report it for a fraud complaint
  // otherwise we don't allow the user to report it
  // The reporting timeframe will be defined by the protocol, allowing users to report any fraudulent transactions within the specified duration.
  bool is_report_fraud_available_for_order = 27;

  // boolean to denote if the transaction is with trusted merchant
  bool is_trusted_merchant_txn = 28;


  // component to be used in OrderStage for redirection to another screen
  // Currently used to display an associated transaction, e.x. forex fees & corresponding original transaction
  // Figma: https://www.figma.com/design/eTeJfUmG9pjHJcAKzJ25k9/Debit-Card-%E2%80%A2-FFF-%E2%80%A2-v1.2?node-id=22750-40316&t=XtpnXnspz5kOLvl3-1
  api.typesv2.ui.IconTextComponent associated_transaction = 29;

  // Stores the transaction for which this payment was made.
  payment.Transaction primary_transaction = 30;

  // SDUI components provided by domain services
  // These are pre-built SDUI components ready for frontend consumption
  repeated EnhancementTile enhancement_tiles = 31;
}

// EnhancementTile is the order-owned tile definition to avoid frontend coupling
message EnhancementTile {
  oneof display_tile {
    // Generic SDUI section to render
    api.typesv2.ui.sdui.sections.Section sdui_section = 1;
  }
}


message GetSuccessOrdersWithTransactionsForActorRequest {
  // actor id for which the orders and transactions needs to be fetched
  string actor_id = 1;

  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp start_timestamp = 3 [(validate.rules).timestamp.required = true];

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // Page size must be in the range [10, 40]
  // minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
  int32 page_size = 4 [(validate.rules).int32 = {gte: 10, lte: 40}];

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 offset = 5 [(validate.rules).int32.gte = 0];

  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  bool is_descending = 6;

  // txn field masks to ensure only desired fields are returned from transactions table.
  // this helps in optimization fo data returned and make the query more efficient.
  // caller can choose to get all field of transaction by specifying ALL in field mask
  // Only first transaction corresponding to an order is returned.
  // NOTE- ALL supported is not implemented yet. TODO(raunak): Add support for querying by ALL field.
  repeated order.payment.TransactionFieldMask txn_field_masks = 7;

  // optional: list of payment instrument ids TO or FROM which payments was made.
  // The caller can use this to filter out the results corresponding to a particular PI.
  // If kept empty, by default all the orders related to the actor are returned
  repeated string pi_filter = 8;

  // optional: timestamp till ending for which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  // If kept empty, by default all the orders related to the actor are returned.
  google.protobuf.Timestamp end_time = 9;

  // optional: list of order tags for which payments was made.
  // The caller can use this to filter out the results corresponding to particular ordertags.
  // If kept empty, by default all the orders related to the actor are returned
  repeated OrderTag order_tags = 10;

  // optional: transaction type wrt the given actor.
  // The caller can use this to filter out the results corresponding to a particular tranactionType.
  // If kept empty, by default all the orders (credit or debit) related to the actor are returned
  // If TransactionType is TRANSACTION_TYPE_UNSPECIFIED, all the orders related to the actor are returned.
  enum TransactionType {
    // If both debit and credit transactions needs to be fetched for the given actor
    TRANSACTION_TYPE_UNSPECIFIED = 0;
    // If debit transaction is to be fetched with respect to the given actor
    DEBIT = 1;
    // If credit transaction is to be fetched with respect to the given actor
    CREDIT = 2;
  }
  TransactionType transaction_type = 11 [(validate.rules).enum.defined_only = true];

  // optional: order payment_protocol filter. orders with payment_protocol in this list are considered.
  // If kept empty, by default all the orders related to the actor are returned
  repeated order.payment.PaymentProtocol payment_protocol = 12;

  // optional: orders with order Provenance in this list are considered.
  // If kept empty, by default all the orders related to the actor are returned
  repeated order.OrderProvenance order_provenance = 13;

  // optional: actor on the receiving end of the order.
  // If kept empty, by default all the orders related to the first actor id are returned
  string other_actor_id = 14;

  // Optional: The minimum amount or the lower limit of the amount to be searched on.
  google.type.Money from_amount = 15;

  // Optional: The maximum amount or the upper limit of the amount to be searched on.
  google.type.Money to_amount = 16;

  // optional: list of other payment instrument ids TO or FROM which payments was made.
  // It is used to fetch ordersWithTxns between a list of from and to pi_ids
  // In case of debit transaction, it corresponds to the list of to_pi_ids
  // In case of credit transaction it corresponds to the list of from_pi_ids
  repeated string other_pi_filter = 17;
}

message GetSelectedOrdersWithTransactionsRequest {
  // actor id for which the orders and transactions needs to be fetched
  string actor_id = 1;

  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp start_timestamp = 2 [(validate.rules).timestamp.required = true];

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // Page size must be in the range [10, 40]
  // minimum page size is kept to 10 to avoid infinite loops (collisions from prev page) when fetching orders.
  int32 page_size = 3 [(validate.rules).int32 = {gte: 10, lte: 200}];

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 offset = 4 [(validate.rules).int32.gte = 0];

  // The sequence of the result returned be based on the boolean flag descending.
  // i.e. If marked true DESCENDING ordered results are returned from the given start timestamp.
  // If marked false ASCENDING ordered results are returned from the given start timestamp.
  bool is_descending = 5;

  // txn field masks to ensure only desired fields are returned from transactions table.
  // this helps in optimization fo data returned and make the query more efficient.
  // caller can choose to get all field of transaction by specifying ALL in field mask
  // Only first transaction corresponding to an order is returned.
  // NOTE- ALL supported is not implemented yet. TODO(raunak): Add support for querying by ALL field.
  repeated order.payment.TransactionFieldMask txn_field_masks = 6;


  // the timestamp till which the order and transactions changes should be considered
  // as we know while the user might use the pagination to keep seeing all order and transactions
  // in the meanwhile his order and transactions status might change we are ignoring to show
  // all the changes post this given timestamp, for new update user needs to call the RPC again
  // user need to set the use_time_travel_query flag to make the following timestamp effective
  google.protobuf.Timestamp time_travel_query_timestamp = 7;

  // optional: list of payment instrument ids TO or FROM which payments was made.
  // The caller can use this to filter out the results corresponding to a particular PI.
  // If kept empty, by default all the orders related to the actor are returned
  repeated string pi_filter = 8;

  // optional: timestamp till ending for which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  // If kept empty, by default all the orders related to the actor are returned.
  google.protobuf.Timestamp end_time = 9;

  // optional: list of order tags for which payments was made.
  // The caller can use this to filter out the results corresponding to particular ordertags.
  // If kept empty, by default all the orders related to the actor are returned
  repeated OrderTag order_tags = 10;

  // optional: transaction type wrt the given actor.
  // The caller can use this to filter out the results corresponding to a particular tranactionType.
  // If kept empty, by default all the orders (credit or debit) related to the actor are returned
  // If TransactionType is TRANSACTION_TYPE_UNSPECIFIED, all the orders related to the actor are returned.
  enum TransactionType {
    // If both debit and credit transactions needs to be fetched for the given actor
    TRANSACTION_TYPE_UNSPECIFIED = 0;
    // If debit transaction is to be fetched with respect to the given actor
    DEBIT = 1;
    // If credit transaction is to be fetched with respect to the given actor
    CREDIT = 2;
  }
  TransactionType transaction_type = 11 [(validate.rules).enum.defined_only = true];


  // optional: order payment_protocol filter. orders with payment_protocol in this list are considered.
  // If kept empty, by default all the orders related to the actor are returned
  repeated order.payment.PaymentProtocol payment_protocol = 12;

  // optional: orders with order Provenance in this list are considered.
  // If kept empty, by default all the orders related to the actor are returned
  repeated order.OrderProvenance order_provenance = 13;

  // optional: actor on the receiving end of the order.
  // If kept empty, by default all the orders related to the first actor id are returned
  string other_actor_id = 14;

  // Optional: The minimum amount or the lower limit of the amount to be searched on.
  google.type.Money from_amount = 15;

  // Optional: The maximum amount or the upper limit of the amount to be searched on.
  google.type.Money to_amount = 16;

  // optional: list of other payment instrument ids TO or FROM which payments was made.
  // It is used to fetch ordersWithTxns between a list of from and to pi_ids
  // In case of debit transaction, it corresponds to the list of to_pi_ids
  // In case of credit transaction it corresponds to the list of from_pi_ids
  repeated string other_pi_filter = 17;

  // list of order status and workflow types for filteration
  repeated OrderStatusAndWorkflowTypeFilter order_status_workflow_type_filters = 18;
}

message GetSuccessOrdersWithTransactionsForActorResponse {
  enum Status {
    OK = 0;

    // in case orders are not found for a given set of request params.
    RECORD_NOT_FOUND = 5;

    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // ordered list of orders and transactions
  // Note - As of now the RPC returns details corresponding to only the first transaction
  // belonging to the order.
  repeated order.OrderWithTransactions orders_with_txns = 2;
}

message GetSelectedOrdersWithTransactionsResponse {
  enum Status {
    OK = 0;

    // in case orders are not found for a given set of request params.
    RECORD_NOT_FOUND = 5;

    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // ordered list of orders and transactions
  // Note - As of now the RPC returns details corresponding to only the first transaction
  // belonging to the order.
  repeated order.OrderWithTransactions orders_with_txns = 2;
}

message GetOrderWithTransactionsRequest {
  // This will be used for identifying any order.
  string order_id = 1 [(validate.rules).string.min_len = 1];
}

message GetOrderWithTransactionsResponse {
  enum Status {
    OK = 0;
    // order does not exist
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
  }
  // denotes the status of the GetOrderWithTransactions Request
  rpc.Status status = 1;
  // OrderWithTransactions is a wrapper message containing order along with a list of transactions associated with it.
  OrderWithTransactions order_with_transactions = 2;
}

message RaiseDisputeForTransactionRequest {
  // This will be used for identifying any order.
  string order_id = 1 [(validate.rules).string.min_len = 1];
}

message RaiseDisputeForTransactionResponse {
  enum Status {
    OK = 0;
    // order does not exist
    NOT_FOUND = 5;
    // internal server error
    INTERNAL = 13;
    // dispute is already raised for the transaction
    ALREADY_PROCESSED = 50;
    // Order id does not belong to the actor logged in
    PERMISSION_DENIED = 7;
    // if order is not in right state
    FAILED_PRECONDITION = 9;
  }
  // denotes the status of the RaiseDisputeForTransaction Request
  rpc.Status status = 1;
}

message GetOrdersForActorRequest {
  // actor id for which the orders and transactions needs to be fetched
  string actor_id = 1 [(validate.rules).string.min_len = 4];

  // field mask to ensure only desired fields are returned from order schema.
  // this helps in optimization fo data returned and make the query more efficient.
  // caller can choose to get all field of order by specifying ALL in field mask
  repeated order.OrderFieldMask field_mask = 2 [(validate.rules).repeated.min_items = 1];

  enum TransactionType {
    TRANSACTION_TYPE_UNSPECIFIED = 0;
    // If debit transaction is to be fetched with respect to the given actor
    DEBIT = 1;
    // If credit transaction is to be fetched with respect to the given actor
    CREDIT = 2;
    // If both debit and credit transactions needs to be fetched for the given actor
    BOTH = 3;
  }
  TransactionType transaction_type = 3 [(validate.rules).enum.defined_only = true];

  enum OrderStatusFilter {
    ORDER_STATUS_FILTER_UNSPECIFIED = 0;
    // To fetch success orders
    SUCCESS = 1;
    // To fetch orders in progress
    IN_PROGRESS = 2;
    // To fetch failed orders
    FAILED = 3;
    // To fetch orders in settlement state
    IN_SETTLEMENT = 4;
    // To fetch order in manual intervention
    MANUAL_INTERVENTION = 5;
  }
  // List of order status filters for orders to be fetched.
  repeated OrderStatusFilter status_filters = 4 [(validate.rules).repeated.min_items = 1];

  // Column by which the orders are sorted
  order.OrderFieldMask sort_by = 5;

  // from time if specified all orders will be returned from that given time
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp from_time = 6;

  // to time if specified all orders will be returned till given time.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp to_time = 7;

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // since, the number of orders can be huge for an actor,
  // page size helps in keeping the payload small
  // Page size must be in the range [1, 30]
  int32 page_size = 8 [(validate.rules).int32 = {gte: 1, lte: 30}];

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 orders starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 offset = 9 [(validate.rules).int32.gte = 0];

  // workflow if specified all orders will be returned from that given workflow
  // deprecated use workflows
  order.OrderWorkflow workflow = 10 [deprecated = true];

  // list of workflows for which the orders should be returned
  // if the list is empty all the workflows will be returned
  repeated order.OrderWorkflow workflows = 11;

  // based on entry point we decide which dao method to be called, due to evolving different needs of internal teams and UI
  // we have decided to keep different implementations one for internal services and one for UI / app
  // we can expect different data points in response corresponding to different entry points.
  // Eg. in case of UI we fetch orders based on OrderStatusAndWorkflowTypeFilter (i.e. a combination of workflow and status filter).
  // This is needed to support different types of txns i.e. pending, reversed, failed only for certain specific workflows
  // For internal services we fetch by independently taking order workflows and status as input.
  EntryPointType entry_point_type = 12;

  // list of order status and workflow types for filtration
  // deprecated in the favour of order_status_workflow_type_filters_for_debit_transactions and order_status_workflow_type_filters_for_credit_transactions
  repeated OrderStatusAndWorkflowTypeFilter order_status_workflow_type_filters = 13 [deprecated = true];
  // NOTE: all the orders are fetched for the given actor and then they are filtered based upon the use case
  // for serving the UI i.e. recent user activities, we have to show or hide orders based upon the product requirements, so these filters are helpful there
  repeated OrderStatusAndWorkflowTypeFilter order_status_workflow_type_filters_for_debit_transactions = 14;
  repeated OrderStatusAndWorkflowTypeFilter order_status_workflow_type_filters_for_credit_transactions = 15;
}

// EntryPointType - enum to help pass the calling entry point and hence make the internal logic implementation accordingly
// due to evolving different needs of internal teams and UI
enum EntryPointType {
  ENTRY_POINT_TYPE_UNSPECIFIED = 0;

  ENTRY_POINT_TYPE_UI = 1;

  ENTRY_POINT_TYPE_INTERNAL_SERVICE = 2;
}

message GetOrdersForActorResponse {
  enum Status {
    OK = 0;
    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // list of orders involving the specified actor
  repeated order.Order orders = 2;
}

message GetActorsFromReqIdRequest {
  // server side generated unique request id per transaction (Surrogate key).
  // This req id is passed to all the external systems to uniquely identify an system transaction.
  string req_id = 1;
}

message GetActorsFromReqIdResponse {
  enum Status {
    OK = 0;
    // order or transaction doesn't exist
    NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }

  // actor who initiated the order
  api.typesv2.Actor from_actor = 1;

  // actor on the receiving end of the order
  api.typesv2.Actor to_actor = 2;

  rpc.Status status = 3;
}

message ForceProcessOrderRequest {
  // order for which processing attempt has to be made
  string order_id = 1 [(validate.rules).string.min_len = 1];

  // Optional: flag to force attempt the order processing even after order termination.
  //
  // Caller can use this method to bypass the state terminal check and call the domain
  // service to process the order and finally update the order accordingly.
  // Typically to be used in scenarios where order is say marked as FAILED at our end
  // but the processing successful at the domain or vendor end.
  bool enable_processing_after_termination = 2;
}

message ForceProcessOrderResponse {
  enum Status {
    OK = 0;

    // invalid request arguments
    INVALID_ARGUMENT = 3;

    // order doesn't exists for the passed id
    RECORD_NOT_FOUND = 5;

    // order is already in a terminal state and further processing can't be done.
    // usually returned if enable_processing_after_termination is false and order is in terminal state
    FAILED_PRECONDITION = 9;

    // internal server error
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // order status after processing
  order.OrderStatus order_status = 2;
}

message GetTotalAmountRequest {
  // actor who initiated the order
  string from_actor_id = 1;
  // actor on the receiving end of the order
  string to_actor_id = 2;
  // starting time from which we want the transactions
  google.protobuf.Timestamp from_time = 3;
  // ending time until which we want the transactions
  google.protobuf.Timestamp to_time = 4;
  // order status filter. orders with STATUS in this list are considered
  repeated order.OrderStatus status = 5 [(validate.rules).repeated.min_items = 1];
  // order payment_protocol filter. orders with payment_protocol in this list are considered
  repeated order.payment.PaymentProtocol payment_protocols = 6 [(validate.rules).repeated.min_items = 1];
}

message GetTotalAmountResponse {
  enum Status {
    OK = 0;
    // request parameters invalid.
    INVALID_ARGUMENT = 3;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // total sum of all transactions found according to filters in request
  google.type.Money total_amount = 2;
}

message GetTotalTransactionCountRequest {
  // actor who initiated the order
  string from_actor_id = 1;
  // actor on the receiving end of the order
  string to_actor_id = 2;
  // starting time from which we want the transactions
  google.protobuf.Timestamp from_time = 3;
  // ending time until which we want the transactions
  google.protobuf.Timestamp to_time = 4;
  // order status filter. orders with STATUS in this list are considered
  repeated order.OrderStatus status = 5 [(validate.rules).repeated.min_items = 1];
  // order payment_protocol filter. orders with payment_protocol in this list are considered
  repeated order.payment.PaymentProtocol payment_protocols = 6 [(validate.rules).repeated.min_items = 1];
}

message GetTotalTransactionCountResponse {
  enum Status {
    OK = 0;
    // request parameters invalid.
    INVALID_ARGUMENT = 3;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // total number of transactions found according to filters in request
  int64 total_transaction_count = 2;
}

message PersistAddFundOptionRequest {
  // actor id to which the option belong
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // add fund option to be inserted for the actor
  order.AddFundOption add_fund_option = 2;
}

message PersistAddFundOptionResponse {
  enum Status {
    OK = 0;
    // request parameters invalid.
    INVALID_ARGUMENT = 3;
    // internal error while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message OrderIdentifier {
  oneof identifier {
    string order_id = 1;
    string client_req_id = 2;
    string external_id = 3;
  }
}

message GetOrdersRequest {
  repeated OrderIdentifier get_order_by = 1;
}

message GetOrdersResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;
  repeated Order orders = 2;
}

message GetOrdersWithTransactionsRequest {
  repeated OrderIdentifier order_identifiers = 1;
}

message GetOrdersWithTransactionsResponse {
  enum Status {
    OK = 0;
    // order or transaction doesn't exist
    NOT_FOUND = 5;
    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;
  repeated OrderWithTransactions order_with_transactions = 2;
}

message InitiateOrderProcessingRequest {
  // unique identifier for an order
  oneof identifier {
    string order_id = 1;
    string client_req_id = 2;
  }
}

message InitiateOrderProcessingResponse {
  enum Status {
    OK = 0;
    // order not found
    NOT_FOUND = 5;
    INTERNAL = 13;
    // Order is already in processing or processed state
    ALREADY_PROCESSED = 50;
  }
  rpc.Status status = 1;
}

message UpdateOrdersWithNewToActorIdsRequest {

  // to_actor_id that we want to update the existing to_actor_ids with
  string new_to_actor_id = 1;

  // list of to_actor_ids that we want to update with the new to_actor_id
  repeated string old_to_actor_ids = 2;

  // limit determines the upper bound on the number of records
  // returned in a particular response.
  int32 limit = 3 [(validate.rules).int32 = {gte: 0, lte: 10000}];

  // An offset lets the caller control the number of records that needs to be skipped
  int32 offset = 4 [(validate.rules).int32.gte = 0];
}

message UpdateOrdersWithNewToActorIdsResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message UpdateOrdersWithNewFromActorIdsRequest {

  // from_actor_id that we want to update the existing from_actor_ids with
  string new_from_actor_id = 1;

  // list of from_actor_ids that we want to update with the new from_actor_id
  repeated string old_from_actor_ids = 2;

  // limit determines the upper bound on the number of records
  // returned in a particular response.
  int32 limit = 3 [(validate.rules).int32 = {gte: 0, lte: 10000}];

  // An offset lets the caller control the number of records that needs to be skipped
  int32 offset = 4 [(validate.rules).int32.gte = 0];
}

message UpdateOrdersWithNewFromActorIdsResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;
}

message GetDistinctFromActorIdsByToActorIdsRequest {

  // list of to_actor_ids
  repeated string to_actor_ids = 1;

  // limit determines the upper bound on the number of records
  // returned in a particular response.
  int32 limit = 2 [(validate.rules).int32 = {gte: 0, lte: 10000}];

  // An offset lets the caller control the number of records that needs to be skipped
  int32 offset = 3 [(validate.rules).int32.gte = 0];
}

message GetDistinctFromActorIdsByToActorIdsResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // list of unique from_actor_ids for the to_actor_ids passed in the request
  repeated string from_actor_ids = 2;
}

message GetDistinctToActorIdsByFromActorIdsRequest {
  // list of from_actor_ids
  repeated string from_actor_ids = 1;

  // limit determines the upper bound on the number of records
  // returned in a particular response.
  int32 limit = 2 [(validate.rules).int32 = {gte: 0, lte: 10000}];

  // An offset lets the caller control the number of records that needs to be skipped
  int32 offset = 3 [(validate.rules).int32.gte = 0];
}

message GetDistinctToActorIdsByFromActorIdsResponse {
  enum Status {
    OK = 0;

    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // list of unique to_actor_ids for the from_actor_ids passed in the request
  repeated string to_actor_ids = 2;
}

enum ReceiptComponent {
  RECEIPT_COMPONENT_UNSPECIFIED = 0;
  USER_CAUTION = 1;
  SHOW_HELP = 2;
  RAISE_DISPUTE = 3;
  // when this component is present, frontend need to show dispute option in disabled state for user
  // e.g., If user has already raised a dispute, user doesn't need an option raise dispute again
  DISABLE_DISPUTE = 4;
}

message GetOrdersForActorWithNoOffsetRequest {
  // actor id for which the orders and transactions needs to be fetched
  string actor_id = 1 [(validate.rules).string.min_len = 4];

  // field mask to ensure only desired fields are returned from order schema.
  // this helps in optimization fo data returned and make the query more efficient.
  // caller can choose to get all field of order by specifying ALL in field mask
  repeated order.OrderFieldMask field_mask = 2 [(validate.rules).repeated.min_items = 1];

  enum TransactionType {
    TRANSACTION_TYPE_UNSPECIFIED = 0;
    // If debit transaction is to be fetched with respect to the given actor
    DEBIT = 1;
    // If credit transaction is to be fetched with respect to the given actor
    CREDIT = 2;
    // If both debit and credit transactions needs to be fetched for the given actor
    BOTH = 3;
  }
  TransactionType transaction_type = 3 [(validate.rules).enum.defined_only = true];

  enum OrderStatusFilter {
    ORDER_STATUS_FILTER_UNSPECIFIED = 0;
    // To fetch success orders
    SUCCESS = 1;
    // To fetch orders in progress
    IN_PROGRESS = 2;
    // To fetch failed orders
    FAILED = 3;
    // To fetch orders in settlement state
    IN_SETTLEMENT = 4;
    // To fetch order in manual intervention
    MANUAL_INTERVENTION = 5;
  }
  // List of order status filters for orders to be fetched.
  repeated OrderStatusFilter status_filters = 4 [(validate.rules).repeated.min_items = 1];

  // Column by which the orders are sorted
  order.OrderFieldMask sort_by = 5;

  // from time if specified all orders will be returned from that given time
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp from_time = 6;

  // to time if specified all orders will be returned till given time.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp to_time = 7;

  // Page size determines the upper bound on the number of records
  // returned in a particular response.
  // since, the number of orders can be huge for an actor,
  // page size helps in keeping the payload small
  // Page size must be in the range [1, 30]
  int32 page_size = 8 [(validate.rules).int32 = {gte: 1, lte: 30}];

  // list of workflows for which the orders should be returned
  // if the list is empty all the workflows will be returned
  repeated order.OrderWorkflow workflows = 9;

  // based on entry point we decide which dao method to be called, due to evolving different needs of internal teams and UI
  // we have decided to keep different implementations one for internal services and one for UI / app
  // we can expect different data points in response corresponding to different entry points.
  // Eg. in case of UI we fetch orders based on OrderStatusAndWorkflowTypeFilter (i.e. a combination of workflow and status filter).
  // This is needed to support different types of txns i.e. pending, reversed, failed only for certain specific workflows
  // For internal services we fetch by independently taking order workflows and status as input.
  EntryPointType entry_point_type = 10;

  // list of order status and workflow types for filtration
  // deprecated in the favour of order_status_workflow_type_filters_for_debit_transactions and order_status_workflow_type_filters_for_credit_transactions
  repeated OrderStatusAndWorkflowTypeFilter order_status_workflow_type_filters = 11 [deprecated = true];
  // NOTE: all the orders are fetched for the given actor and then they are filtered based upon the use case
  // for serving the UI i.e. recent user activities, we have to show or hide orders based upon the product requirements, so these filters are helpful there
  repeated OrderStatusAndWorkflowTypeFilter order_status_workflow_type_filters_for_debit_transactions = 12;
  repeated OrderStatusAndWorkflowTypeFilter order_status_workflow_type_filters_for_credit_transactions = 13;
}

message GetOrdersForActorWithNoOffsetResponse {
  enum Status {
    OK = 0;
    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;

  // list of orders involving the specified actor
  repeated order.Order orders = 2;
}

// Request object for the CreateOrderMetadata rpc
message CreateOrderMetadataRequest {
  // order id from orders table (foreign key)
  string order_id = 1;
  // type of orderMetadata stored (e.g. qr)
  order.MetadataType metadata_type = 2;
  // stored metadata (e.g. qrData)
  order.Metadata metadata = 3;
}

// Response object for the CreateOrderMetadata rpc
message CreateOrderMetadataResponse {
  enum Status {
    // Returned an success
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 1;
    // Invalid Argument Passed
    INVALID_ARGUMENT = 3;
  }
  // newly created orderMetadata in the database
  order.OrderMetadata order_metadata = 1;
  rpc.Status status = 2;
}

// Request object for the GetByOrderId rpc
message GetByOrderIdRequest {
  // order id from orders table (foreign key)
  string order_id = 1;
  // Optional: list of metadata_type
  // if empty list is passed, all the orderMetadata associated with order_id will be returned
  repeated order.MetadataType metadata_types = 2;
}

// Response object for the GetByOrderId rpc
message GetByOrderIdResponse {
  enum Status {
    // Returned an success
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 1;
    // no orderMetadata found
    RECORD_NOT_FOUND = 2;
    // Invalid Argument Passed
    INVALID_ARGUMENT = 3;
  }

  // represents all the OrderMetadata obtained from the database for the given OrderId and MetadataTypes
  repeated order.OrderMetadata order_metadata = 1;
  rpc.Status status = 2;
}
