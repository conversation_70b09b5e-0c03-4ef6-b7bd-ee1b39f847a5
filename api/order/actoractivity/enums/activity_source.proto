// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package order.actoractivity.enums;

option go_package = "github.com/epifi/gamma/api/order/actoractivity/enums";
option java_package = "com.github.epifi.gamma.api.order.actoractivity.enums";

// activity source specifies the source of financial activities that we want
// we can specify whether we want fi transactions activities or connected account transactions activities
enum ActivitySource {
  ACTIVITY_SOURCE_UNSPECIFIED =0;
  CONNECTED_ACCOUNT_TRANSACTION = 1;
  FI_TRANSACTION = 2;
  ACTIVITY_SOURCE_CREDIT_CARD = 3;
}
