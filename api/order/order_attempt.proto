// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package order;

import "api/order/order.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/order";
option java_package = "com.github.epifi.gamma.api.order";


// Order attempt defines the number of attempt an order has gone through inorder to process a particular order stage.
// It also stores the necessary params required to perform different retry strategies.
message OrderAttempt {
  // a unique identifier for an order stage completion attempt
  int64 id = 1;

  // order id corresponding to which processing is being done
  string order_id = 2;

  // the order stage
  order.OrderStage stage = 3;

  // Number of attempts that has been made to complete a particular order stage
  int32 num_attempts = 4;

  // request identifier to identifies a unique request made by order service to the domain service.
  string req_id = 5;

  // timestamp at which first order stage completion attempt was initiated
  google.protobuf.Timestamp created_at = 6;

  // timestamp at which last order stage completion attempt was made
  google.protobuf.Timestamp updated_at = 7;
}
