// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order.payment;

import "api/order/domain/request.proto";

option go_package = "github.com/epifi/gamma/api/order/payment";
option java_package = "com.github.epifi.gamma.api.order.payment";

// gRPC service to facilitate payments using epiFi's business account.
// It contains RPC to make payments for both B2C and B2B use cases.
service Business {
  // MakeB2CPayment contains the business logic to make a payment from epiFi's business account to
  // an end customer's savings account.
  //
  // The RPC is called by central order retry orchestrator, for processing order payment having B2C_FUND_TRANSFER workflow.
  // The order service takes care of the retrying logic here while Business service acts a domain service.
  //
  // The API is made idempotent, using the clientRequestId passed by order.
  // The RPC does the following-
  // 1. Creates an entry in the transaction schema if not already exists and initiates payment with the partner bank.
  // 2. In case entry already exists then, transaction status is checked and action is taken accordingly.
  //    a. If the transaction is in a terminal state, then the corresponding order domain status is returned. i.e.
  //       if txn was successful then DomainProcessingStatus SUCCESS is returned and if txn failed then DomainProcessingStatus
  //       PERMANENT_FAILURE is returned.
  //    b. If the transaction is in suspected state, then status for the request is checked with the vendor and after
  //       updating the state machine of the transaction one of DomainProcessingStatus is returned in the response.
  rpc MakeB2CPayment(order.domain.ProcessPaymentRequest) returns (order.domain.ProcessPaymentResponse) {}

  // RewardsCreateSDMakeB2CPaymentWrapper RPC is a wrapper over the order.payments' MakeB2CPayment RPC
  // It is used as part of the first leg of REWARDS_CREATE_SD workflow where funds need to be transferred from
  // the business account to customer's savings account post which Smart Deposit creation is initiated.
  //
  // The RPC will call the MakeB2CPayment RPC with the appropriate payload and return the status returned by MakeB2CPayment
  // as it is to the order orchestrator.
  rpc RewardsCreateSDMakeB2CPaymentWrapper(order.domain.ProcessPaymentRequest) returns (order.domain.ProcessPaymentResponse) {}

  // RewardsAddFundsSDMakeB2CPaymentWrapper RPC is a wrapper over the order.payments' MakeB2CPayment RPC
  // It is used as part of the first leg of REWARDS_ADD_FUNDS_SD workflow where funds need to be transferred from
  // the business account to customer's savings account post which addition of funds to SD is initiated.
  //
  // The RPC will call the MakeB2CPayment RPC with the appropriate payload and return the status returned by MakeB2CPayment
  // as it is to the order orchestrator.
  rpc RewardsAddFundsSDMakeB2CPaymentWrapper(order.domain.ProcessPaymentRequest) returns (order.domain.ProcessPaymentResponse) {}
}

