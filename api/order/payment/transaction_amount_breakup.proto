syntax = "proto3";

package order.payment;

import "google/protobuf/timestamp.proto";
import "api/typesv2/money.proto";

option go_package = "github.com/epifi/gamma/api/order/payment";
option java_package = "com.github.epifi.gamma.api.order.payment";

message TransactionAmountBreakup {
  // Unique identifier for each row
  string id = 1;

  // transaction id corresponding to the transaction to which the amount breakup belongs
  string transaction_id = 2;

  // amount corresponds to the total amount of particular breakup type
  api.typesv2.Money amount = 3;

  // breakup_type corresponds to the type of breakup the data belongs
  payment.AmountBreakupType breakup_type = 4;

  // conversion_rate corresponds to the amount of INR for 1 unit of quote_currency
  float conversion_rate = 5;

  // conversion_rate_source corresponds to the source from where conversion_rate is extracted
  payment.ConversionRateSource conversion_rate_source = 6;

  // overhead_charge_percentage overhead charges like GST or Markup actual value in percentage
  float overhead_charge_percentage = 7;

  // time of creation
  google.protobuf.Timestamp created_at = 8;

  // last updated time
  google.protobuf.Timestamp updated_at = 9;

  // time of deletion
  google.protobuf.Timestamp deleted_at = 10;

}

// Types of breakup
enum AmountBreakupType {
  AMOUNT_BREAKUP_TYPE_UNSPECIFIED = 0;
  // it denotes transaction breakup is of markup
  AMOUNT_BREAKUP_TYPE_MARKUP = 1;
  // it denotes transaction breakup is a GST
  AMOUNT_BREAKUP_TYPE_GST = 2;
  // It denotes the base amount without any additional fees/charges
  AMOUNT_BREAKUP_TYPE_BASE_AMOUNT = 3;
}

// Source from where the conversion rate is being fetched
enum ConversionRateSource {
  Conversion_Rate_Source_Unspecified = 0;
  // Global QRs scanned by app containing forex details
  Conversion_Rate_Source_NPCI_QR = 1;
}
