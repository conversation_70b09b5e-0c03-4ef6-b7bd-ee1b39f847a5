syntax = "proto3";

package order.payment;

option go_package = "github.com/epifi/gamma/api/order/payment";
option java_package = "com.github.epifi.gamma.api.order.payment";

// AccountingEntryType represents the type of transaction that was requested/completed.
// It can be of type Debit or Credit.
enum AccountingEntryType {
  ACCOUNTING_ENTRY_TYPE_UNSPECIFIED = 0;
  // DEBIT implies that given amount was deducted from the account in the transaction.
  DEBIT = 1;
  // CREDIT denotes given amount was added to the account in the transaction.
  CREDIT = 2;
}
