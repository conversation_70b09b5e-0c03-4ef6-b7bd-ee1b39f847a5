// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order.payment;

import "api/order/payment/payment_protocol.proto";
import "api/order/payment/notification/enums/enums.proto";
import "api/typesv2/common/ownership.proto";
import "api/upi/customer.proto";
import "api/upi/merchant.proto";
import "api/upi/upi.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/order/payment";
option java_package = "com.github.epifi.gamma.api.order.payment";

// Different status through which a transaction state machine transitions
// throughout a transaction lifetime.
// The state machine of transactions will/can vary with payment mode.
enum TransactionStatus {
  TRANSACTION_STATUS_UNSPECIFIED = 0;

  // Transaction entry created in the database.
  // A transaction is created in a system side by side to an order.
  // A transaction in CREATED state can be distinguished as abandoned after a certain threshold,
  // signifying no action was taken post order creation.
  CREATED = 1;

  // Transaction initiation request registered in the system.
  // Initiated state signifies at least one call has been made to initiate transaction from the client(APP).
  INITIATED = 2;

  // Transaction request has been registered successfully at the bank end. The status needs to be
  // polled. Requests can be in this state after successful acknowledgement of receiving the request by a vendor
  // in case the APIs are async.
  // The transaction is expected to move to a terminal state from here. It is done by following 2 ways -
  // 1. Vendor sends us callbacks and based on the response action transaction is moved to a terminal state.
  // 2. Vendor doesn't send us callbacks and after a certain threshold. We start polling vendor servers
  // till we get a terminal status back.
  IN_PROGRESS = 3;

  // Transaction has been carried out successfully.
  // This represents both credit to payee and debit from payer
  SUCCESS = 4;

  // Transaction has failed.
  // Failure of a transaction can be due to various reasons.
  // One of the possible reasons can be insufficient balance.
  // in case money has been deducted from payer account then a refund transaction to be initiated.
  FAILED = 5;

  // System has exhausted all the retries post transient errors so this needs attention from a human.
  MANUAL_INTERVENTION = 6;

  // Transaction has been dismissed by the user
  // For an incoming collect request from external actor, payer can dismiss the transaction
  DISMISSED = 7;

  // In scenarios where the outcome of a payment request cannot be determined
  // E.g., Payment Request RPC method was fired which resulted in a timeout
  // In such cases, transaction status will marked as `UNKNOWN` until the exact status is determined
  UNKNOWN = 8;

  // Transaction has been dismissed by the Payer
  DISMISSED_BY_PAYER = 9;

  // Transaction has been dismissed by the Payee
  DISMISSED_BY_PAYEE = 10;

  // Transaction has expired
  EXPIRED = 11;

  // Transaction has been reversed.
  // In such cases the amount is first debited and
  // then credited back to the same account
  REVERSED = 12;

  // Transaction has been rejected, caller should take care of logic for rejected txns.
  REJECTED = 13;
}

// Defines Protocol specific granular status.
// These status map to more generic statuses represented by `TransactionStatus`
// The mapping between `TransactionStatus` and `TransactionProtocolStatus` is one-to-many.
//
// Below enums from 1-11 are specific UPI protocol. Keeping forward compatibility in mind reserving 1-20 for UPI.
enum TransactionProtocolStatus {
  PROTOCOL_STATUS_UNSPECIFIED = 0;

  // Pay request has been registered in the system.
  // Its an intermittent state, signifies at least one call has been made to initiate pay transaction from the client(APP).
  PAY_INITIATED = 1;

  // Collect request has been registered in the system.
  // Its an intermittent state, signifies at least one call has been made to initiate collect transaction from the client(APP).
  COLLECT_INITIATED = 2;

  // A special case when payer and payee both belong to epiFi, collect requests are short-circuited.
  // To the user it seems to be a collect request but actually when payer authenticates a transaction
  // it is treated similar to a normal pay transaction in backend.
  COLLECT_SHORT_CIRCUITED = 3;

  // ReqPay request has been successfully sent to NPCI via partner bank.
  // In case of payment initialised from epiFi app, epiFi initiates payment request by calling `ReqPay`
  // to NPCI via partner bank with type `Pay`
  PAY_REQ_PAY_SENT = 4;

  // We get `ReqAuth` call from NPCI in the following scenarios-
  // 1) when an external user creates a COLLECT request against an epiFi user (payer= epiFi user). In this case,
  //    we are supposed to send collect notification to the user and based on the user action (payment or declined)
  //    we sent the `RespAuth` back to NPCI.
  // 2) when for a UPI `PAY` request payee also belongs to epiFi. In this case we simply resolve the account details
  //    for the payee and return the details in `RespAuth`
  REQ_AUTH_RECEIVED = 5;

  // RespAuth call has been initiated.
  // Its an intermittent state, signifies at least once attempt has been made to send `RespAuth`.
  // For collect request received from an external payee PSP. It means at least one call has been made to initiate transaction
  // has been made from the client app.
  RESP_AUTH_INITIATED = 6;

  // RespAuth is successfully sent back to NPCI via partner bank.
  RESP_AUTH_SENT = 7;

  // RespPay has been received from NPCI.
  // NPCI sends back txn updated in the form of callback `RespPay` to the PSP who initiated payment using `ReqPay`.
  // In all other cases we are notified using `ReqTxnConfirmation`
  RESP_PAY_RECEIVED = 8;

  // ReqTxnConfirmation received from NPCI via partner bank.
  // ReqTxnConfirmation is sent to payer PSP in case of collect request
  // and to payee PSP in case of Pay Request.
  REQ_TXN_CONFIRMATION_RECEIVED = 9;

  // RespTxnConfirmation sent back to NPCI via partner bank
  RESP_TXN_CONFIRMATION_SENT = 10;

  // Collect request has been declined by the payer
  COLLECT_DISMISSED_BY_PAYER = 11;

  // Collect request has been cancelled by the payee or the initiator
  COLLECT_DISMISSED_BY_PAYEE = 12;

  // `ReqPay` of type collect has been successfully sent to NPCI via partner bank.
  // In case of collect request initialised from epiFi app, epiFi initiates payment request by calling `ReqPay`
  // to NPCI via partner bank with type `Collect`.
  COLLECT_REQ_PAY_SENT = 13;
}

// Transaction detailed status contains raw feedback we get from every status check call from banks.
// it's important to store this detailed data, as it will help us to understand and debug the reason of failure of a transaction.
// e.g. A transaction can fail due to multiple reasons, broadly we can categorize them as transient and permanent failure -
// 1. Permanent failure - Insufficient balance, Debit Frozen, Account Frozen, Transfer limit exceeded, etc.
// 2. Transient failure - CBS connection failure, CBS response timeout, Server is down please try after sometime.
// all of these detailed status are stores in below message.
//
// NOTE- We need to show different status description to payer/payee, hence we have
// two different status descriptions one for payee and one for payee
message TransactionDetailedStatus {
  repeated string details = 1;

  message DetailedStatus {
    // epifi status code of the detailed status for payer
    string status_code_payer = 1;

    // description of the status code for payer
    string status_description_payer = 2;

    // epifi status code of the detailed status for payee
    string status_code_payee = 3;

    // description of status code for payee
    string status_description_payee = 4;

    // category of the error
    // will be unspecified in case of success status code
    enum ErrorCategory {
      // category unspecified
      ERROR_CATEGORY_UNSPECIFIED = 0;
      // error that happens due to server related issues while processing the payment
      // e.g. invalid txn id passed to vendor
      // will be used for errors returned by the vendor
      SERVER = 1;
      // error that happens as a direct consequence of user's actions.
      // e.g. user providing invalid details of the payee
      // for eg. invalid VPA for upi payments
      USER = 2;
      // epifi system errors out while processing the transaction.
      // eg. rpc failures
      SYSTEM_ERROR = 3;
    }

    // Category of the error. eg. Server, user
    ErrorCategory error_category = 5;

    // timestamp when this entry in detailed status is created.
    google.protobuf.Timestamp created_at = 6;

    enum State {
      // unspecified
      STATE_UNSPECIFIED = 0;
      // transaction successful
      SUCCESS = 1;
      // transaction failed
      FAILURE = 2;
      // transaction has completed partially and is still in progress
      IN_PROGRESS = 3;
      // denotes that transaction is deemed
      // Deemed transaction is a use case only for UPI and IMPS transactions
      // NPCI sends the status as DEEMED in certain cases like - the remitter bank request times out with NPCI
      // for deemed transaction, generally the transaction goes to success, but for rare cases the transaction might also fail
      // epifi will treat Deemed transaction as success on the client side, on backend  will check the status
      // in intervals with a retry logic
      DEEMED = 4;
      // denotes the status is unknown
      UNKNOWN = 5;
      // denotes that the transaction was reversed
      REVERSED = 6;
    }

    //  signifies the state in which transaction is when the corresponding detailed status entry is created.
    State state = 7;

    message CustomerLevelStatus {
      // status code
      // for certain transaction protocols like UPI, status code is equal to the
      // raw status code (i.e as sent by the vendor)
      string raw_status_code = 1;

      // description of the status code
      // for certain transaction protocols like UPI, status description is equal to the
      // raw status description (i.e as sent by the vendor)
      string raw_status_description = 2;

      enum CustomerType {
        // unspecified
        CUSTOMER_TYPE_UNSPECIFIED = 0;
        // status corresponds to payer
        PAYER = 1;
        // status corresponds to payee
        PAYEE = 2;
      }

      // for certain transaction protocols like UPI, we have response codes corresponding to both payers and payee
      // customer_type will denote the customer(payer/payee) to which the status corresponds to
      CustomerType customer_type = 3;

      // raw reversal code as received front he vendor
      // will be present in case the transaction is reversed
      string raw_reversal_status_code = 4;
    }

    // for certain transaction protocols like UPI along with the overall status we also receive customer(payer/payee) level status codes
    // customer_level_status_list will contain the customer level status codes (payer and payee level status codes)
    repeated CustomerLevelStatus customer_level_status_list = 8;

    // status code as sent by the partner bank
    string raw_status_code = 9;

    // description of the status code as sent by the vendor bank
    string raw_status_description = 10;

    // in case of system errors contains the error description
    string system_error_description = 11;

    // contains the api in which we received this status
    // will contain upi/fund transfer pay and status enquiry apis
    enum API {
      // unspecified
      API_UNSPECIFIED = 0;
      // req txn callback confirmation for upi txns
      REQ_TXN_CONFIRMATION = 1;
      // req pay for upi txns
      REQ_PAY = 2;
      // payment enquiry for upi txns
      UPI_PAYMENT_ENQUIRY = 3;
      // req auth callback for upi txns
      REQ_AUTH = 4;
      // resp auth for upi txns
      RESP_AUTH = 5;
      // resp pay callback for upi txns
      RESP_PAY = 6;
      // fund transfer pay api
      FUND_TRANSFER_PAY = 7;
      // call back for fund transfer
      FUND_TRANSFER_CALLBACK = 8;
      // payment enquiry for fund transfer
      FUND_TRANSFER_PAYMENT_ENQUIRY = 9;
      // enquiry for add funds transfer
      ADD_FUNDS_ENQUIRY = 10;
      // transaction was updated based on the statement response
      STATEMENT = 11;
      // transaction was updated based on the payment push notification from the partner
      NOTIFICATION_CALLBACK = 12;
      // req txn callback confirmation for auto update
      REQ_TXN_CONFIRMATION_AUTO_UPDATE = 13;
      // Transaction added manually for failed card transaction via data dump from s3
      CARD_DECLINE_DATA_DUMP = 14;
      // transaction added via card switch notifications
      CARD_SWITCH_NOTIFICATION = 15;
      // transaction added via payment gateway execution.
      // For e.g., In case of Razorpay, this involves pulling Payments on an Order and creating transactions in our system via it.
      PAYMENT_GATEWAY_PAYMENTS = 16;
      // indicates that the transaction status was updated manually
      // (For ex. through jenkins job https://jenkins-prod.pointz.in/job/Scripts/job/Backend/job/Restricted/job/Pay/job/update-and-publish-order-txn-status/, fixtures, or dev-actions, etc)
      MANUAL_UPDATE_VIA_OPS = 17;
      // indicates that the transaction was created for a PG payment via the reconciliation process.
      PG_RECON = 18;
      // indicates that the transaction status was updated via the MakeB2CPayment flow's fund transfer status enquiry.
      B2C_PAYMENT_ENQUIRY = 19;
      // indicates that the transaction status was updated via the failed ENACH transactions sync flow.
      ENACH_FAILED_TXN_SYNC = 20;
    }
    // api in which we received this status
    API api = 12;

    // timestamp when this entry in detailed status is updated.
    // if we receive the same status codes and description
    // instead of creating a new entry we will change the updated time
    // of the existing entry
    google.protobuf.Timestamp updated_at = 13;

    // we get this field in auto updates for a transaction. It contains granular level details about the transactions status
    string note = 14;

    // reversal_transaction_id: unique identifier for the refund txn. E.g. Reversal of first
    // leg due to 2nd leg failure
    // it may be empty and non empty depending on the status of transaction and api response
    string reversal_transaction_id = 15;

    // Customer reference ID of the reversal transaction i.e. RRN.
    // e.g. Reversal of first leg due to 2nd leg failure
    string reversal_cust_ref_id = 16;
  }

  // list of status for the transactions.
  repeated DetailedStatus detailed_status_list = 2;
}

// A transaction is defined as flow of assets from one payment instrument to another.
message Transaction {
  reserved 12, 29;

  string id = 1;

  // payment instrument from which transaction is initiated
  string pi_from = 2;

  // payment instrument to which transaction is made
  string pi_to = 3;

  // external payment Ref referring to the partner bank’s transaction
  string partner_ref_id = 4 [deprecated = true];

  // Unique Transaction Reference for Online Txns like NEFT/IMPS/UPI.
  // A customer gets this id on initiating a transaction and can refer to the same
  // in case of any query regarding his/her transaction.
  // A utr is same across all the parties of a transaction.
  //
  // For NEFT/RTGS/INTRA fund transfer protocols this refers to UTR
  // For IMPS/UPI/CARD fund transfer protocol this refers to RRN
  string utr = 5;

  // vendor bank through which transaction is done
  vendorgateway.Vendor partner_bank = 6;

  // transactions can have child transactions.
  // e.g. a refund txn is a child to the original txn against which refund is being done.
  string parent_transaction_id = 7;

  // amount involved in the transaction
  google.type.Money amount = 8;

  // the state of a transaction
  // transaction state machine varies with payment mode.
  TransactionStatus status = 9;

  TransactionDetailedStatus detailed_status = 10;

  order.payment.PaymentProtocol payment_protocol = 11;

  // comments/remarks added by an user during transaction.
  // this can be auto-populated by partner banks in some case.
  string remarks = 13;

  // timestamp referring to moment when entry was created in the DB.
  google.protobuf.Timestamp created_at = 14;

  // timestamp referring to moment when entry was last updated in the DB.
  google.protobuf.Timestamp updated_at = 15;

  // timestamp referring to moment when transaction was executed at bank's end.
  // this will act as a source of truth for ordering transactions on epifi.
  google.protobuf.Timestamp partner_executed_at = 16 [deprecated = true];

  google.protobuf.Timestamp deleted_at = 17;

  // server side generated unique request id per transaction (Surrogate key).
  // This req id is passed to all the external systems to uniquely identify an system transaction.
  //
  // This is unique ID generated for all on-app transaction and shared with vendor only. Any other external entity
  // might not aware of the value of this field. For example: For on-app bank account transfer, this value generated
  // by us and send to FEDERAL for transaction initiation. Federal do not share this id with any other entity involve in payment.
  string req_id = 18;

  // protocol specific granular status code, that is needed for processing a txn.
  TransactionProtocolStatus protocol_status = 19;

  // timestamp referring to the moment when a user raised a dispute against a transaction
  // nil/empty timestamp denotes the transaction is not disputed
  google.protobuf.Timestamp disputed_at = 20;

  // Obfuscated GPS coordinates location identifier.
  // Since, location identifier is a sensitive user information, it's not
  // recommended to store this data directly in the transaction domain object.
  // A location token is a place holder for the exact user co-ordinates.
  // Co-ordinates for the specified token can be fetched using location service's
  // GetCoordinates RPC
  // This had been deprecated as we have moved on to storing location tokens in the order entity.
  // This decision was based on the fact that the order represents an intent of payment which is a
  // better place to record location. There can be few cases where intent is recorded in our
  // system but corresponding payment can be initiated outside the app
  string location_token = 21 [deprecated = true];

  // particulars received in inbound notifications
  // optional filed wil be populated only if notification has been received for the txn
  string particulars = 22 [deprecated = true];

  // timestamp referring to moment when amount was debited at bank's end.
  google.protobuf.Timestamp debited_at = 23;

  // timestamp referring to moment when amount was credited at bank's end.
  google.protobuf.Timestamp credited_at = 24;

  // external payment Ref referring to the partner bank’s debit transaction
  // represents the ref Id for transaction corresponding to a deduction
  // of amount from the account at partner bank
  string partner_ref_id_debit = 25;

  // external payment Ref referring to the partner bank’s credit transaction
  // represents the ref Id for transaction corresponding to a addition
  // of amount to the account at partner bank
  string partner_ref_id_credit = 26;

  // particulars received in a debit notification
  // optional filed wil be populated only if notification has been received for the txn
  string particulars_debit = 27 [deprecated = true];

  // particulars received in a credit notification
  // optional filed wil be populated only if notification has been received for the txn
  string particulars_credit = 28 [deprecated = true];

  // raw_notification_details is a map where key could be debit and credit and the values include particular,
  // additional_details, batch serial id, instrument details, value date
  map<string, NotificationDetails> raw_notification_details = 30;

  // unique order identifier to which the transaction is linked to
  // there is a 1:N mapping between order<>transaction and transaction
  // can't exist without order. The other-way round might be possible
  string order_id = 31;

  // unique transaction reference to uniquely identify a transaction. It can be a combination of multiple parameters
  // received from a transaction.
  //
  // For all in-app transaction, dedupe_id will be same as request_id generated for initiating transaction
  // For off-app transaction, it will be combination of multiple params which can be used to dedupe two different transaction.
  order.payment.DedupeId dedupe_id = 32;

  // ownership under which transaction data is persisted
  api.typesv2.common.Ownership ownership = 33;

  // computed_executed_at is the COALESCE of (debited_at,credited_at,updated_at)
  google.protobuf.Timestamp computed_executed_at = 35;

  // Additional Data related to transaction
  // Currently it contains information about parser rule which was used to parse raw notification details.
  Metadata metadata = 36;
}

message NotificationDetails {

  // particulars received in a debit notification
  // optional filed wil be populated only if notification has been received for the txn
  string particulars_debit = 1 [deprecated = true];

  // particulars received in a credit notification
  // optional filed wil be populated only if notification has been received for the txn
  string particulars_credit = 2 [deprecated = true];

  // additional transaction related information received from notification from the partner banks
  // in case of federal below holds true:
  // UPI - TransactionId
  // Inward NEFT/RTGS - Sender Account Number and IFSC
  // Outward NEFT/RTGS - Beneficiary Account Number and IFSC
  // Card Transactions - Merchant Details(MCC, City, CountryCode)
  string additional_details = 3;

  // to identify a transaction uniquely (Combination of Tran_Id, Tran_date and batch_serial_id will be unique)
  string batch_serial_id = 4;

  // Instrument(like DD/Cheque) Details
  InstrumentDetails instrument_details = 5;
  // Value date of transaction
  google.protobuf.Timestamp value_date = 6;
  // particulars received in a notification
  // optional field will be populated only if notification has been received for the txn
  string particulars = 7;

  // country code for the country in which transaction was made
  // optional field will be populated only if notification has been received for the txn which is POS, ATM Withdrawal or E-Comm.
  string country_code = 8;

  // cbs_id of the transaction is stored here as soon as the transaction was successful,
  // this is needed for cases where we need a unique identifier immediately to be shared with a 3rd party vendor, for example US stocks use case where we would like
  // share the cbs_id of transaction back to vendor
  string cbs_id = 9;
}

message InstrumentDetails {
  // Business date of transaction
  google.protobuf.Timestamp instrument_date = 1;
  // Type of Instrument on which transaction is made – Cheque/ DD
  string instrument_type = 2;
  // Number to uniquely Identify the Instrument - Cheque/ DD
  string instrument_number = 3;
}

// PaymentRequestInformation contains necessary information related to bank payment request.
// can be used to fetch status of a given transaction or to send back response to vendors
// e.g. in case of UPI we need to send back RespAuth corresponding to ReqAuth with exact information that came in
// the request
message PaymentRequestInformation {

  // id to identify the device from which payment was initiated
  string device_id = 1;

  // Unique Id generated per transaction. It is shared across all the external vendor systems to identify a transaction
  // uniquely.
  // For UPI transactions it is referred to as `TxnId` in the requests.
  // For Non-UPI transactions (via IMPS, NEFT, etc.) it is referred to as `TxnReqId` in the request.
  // req id can be both epiFi generated ID as well as id generated by external system depending on the case
  // e.g. in dynamic QR based or intent based UPI payments req_id can be generated by external system
  string req_id = 2;

  // contains fields specific to UPI based transactions
  message UPI {
    // reference id corresponds to order-id in case of merchant payment. It has no significance in case of P2P
    // e.g. a merchant shows a dynamic QR and wants a reference id in the payment that ties it to their internal order system.
    string merchant_ref_id = 1;

    // msg id is used to map request with its corresponding response or callback.
    string msg_id = 2;

    // transaction reference url should be a URL when clicked provides customer with further transaction details
    // like complete bill details, bill copy, order copy, ticket details, etc.
    //
    // for dynamic QR and intent based payments.. merchant system can send this information
    // in other cases we will be using a default string which may redirect to epifi
    string ref_url = 3;

    // Transaction origination timestamp represents the time when transaction was originated
    // the timestamp can belong to both internal system as well as external system
    // based on the case e.g.
    // In case of pay or collect request initiated by epifi the timestamp will be generated by epifi
    // In case of incoming transaction from external systems the timestamp sent in request is stored as it it.
    // Typically used while sending `RespAuth` for a transaction
    google.protobuf.Timestamp txn_origin_timestamp = 4;

    // initiation mode of the transaction
    string initiation_mode = 5;

    // purpose of the transaction
    string purpose = 6;

    // merchant code
    // for QR and intent based payments.. merchant system can send this information
    // in other cases we will be using the default code -  "0000"
    string mcc = 7;

    // merchant id
    // for QR and intent based payments.. merchant system can send this information
    // for all other cases this field will be empty
    string merchant_id = 8 [deprecated = true];

    // store id of the merchant
    // for QR and intent based payments.. merchant system can send this information
    // for all other cases this field will be empty
    string merchant_store_id = 9 [deprecated = true];

    // terminal id of the merchant
    // for QR and intent based payments.. merchant system can send this information
    // for all other cases this field will be empty
    string merchant_terminal_id = 10 [deprecated = true];

    // If the transaction is initiated by any PSP app then the respective orgID needs to be passed.
    // for merchant initiated intent ‘000000’ will be used.
    // for non QR and non intent based payments epifi org id will be used
    string org_id = 11;

    // Risk Score related to the transaction and the entities involved
    message RiskScore {
      string provider = 1;

      string type = 2;

      string value = 3;
    }

    RiskScore risk_scores = 12 [deprecated = true];

    upi.Rules rules = 13;

    upi.CustomerInformation customer_info = 14;

    // customer account details as received in the
    upi.CustomerAccountDetails customer_account_info = 15;

    // merchant details to be populated for P2M payments via UPI
    upi.MerchantDetails merchant_details = 16;

    upi.TransactionType transaction_type = 17;

    // list of risk scores
    repeated RiskScore risk_score_list = 18;

    // vpa of the payer involved in the transaction
    // we are storing this as even though the vpa is case insensitive, we need to pass the exact
    // vpa which we received, for salt generation and while sending a call back to NPCI
    string payer_vpa = 19;

    // vpa of the payee involved in the transaction
    // we are storing this as even though the vpa is case insensitive, we need to pass the exact
    // vpa which we received, for salt generation and while sending a call back to NPCI
    string payee_vpa = 20;

    // upi number of the payer if the transaction is done from a upi number
    string payer_upi_number = 21;

    // upi number of the payee if the transaction is done to a upi number
    string payee_upi_number = 22;
    // sequence number of the transaction
    string seq_num = 23;
    // ref category of the transaction
    string ref_category = 24;
  }

  UPI upi_info = 3;
}

// unique transaction reference to uniquely identify a transaction. It can be a combination of multiple parameters
// received from a transaction. Id will be base64 encoded proto.Marshal of this message.
//
// For all in-app transaction, dedupe_id will be same as request_id generated for initiating transaction
// For off-app transaction, it will be combination of multiple params which can be used to dedupe two different transaction.
//
// We will transform this message to base64 after doing proto.Marshal and keep this into our db. It will be considered as
// unique identifier for de-duping transactions.
message DedupeId {
  // unique reference generated from fi while initiating a on app transaction
  string request_id = 1;
  // unique transaction reference generated from federal. This is supposed to be unique but
  // in some cases it is not resulting conflicting utr.
  string utr = 2;
  // to identify a transaction uniquely (Combination of Tran_Id, Tran_date and batch_serial_id will be unique)
  string batch_serial_id = 3;
  // CBS generated code for each transaction (Field TranId from notification).
  // It is generally populated in partner_ref_id credit/debit field in transaction proto.
  string cbs_id = 4;
  // transaction date time
  google.protobuf.Timestamp txn_time = 5;
}

// TransactionFieldMask is the enum representation of all the transaction fields.
// Meant to be used as field mask to help with database updates
enum TransactionFieldMask {
  TRANSACTION_FIELD_MASK_UNSPECIFIED = 0;
  ID = 1;
  PI_FROM = 2;
  PI_TO = 3;
  PARTNER_REF_ID = 4;
  UTR = 5;
  PARTNER_BANK = 6;
  PARENT_TRANSACTION_ID = 7;
  AMOUNT = 8;
  STATUS = 9;
  DETAILED_STATUS = 10;
  PAYMENT_PROTOCOL = 11;
  REMARKS = 13;
  PARTNER_EXECUTED_AT = 14;
  PAYMENT_REQ_INFO = 15;
  CREATED_AT = 16;
  UPDATED_AT = 17;
  ALL = 18;
  PROTOCOL_STATUS = 19;
  DISPUTED_AT = 20;
  LOCATION_TOKEN = 21;
  PARTICULARS = 22;
  DEBITED_AT = 23;
  CREDITED_AT = 24;
  PARTNER_REF_ID_DEBIT = 25;
  PARTNER_REF_ID_CREDIT = 26;
  PARTICULARS_DEBIT = 27;
  PARTICULARS_CREDIT = 28;
  NOTIFICATION_DETAILS = 29;
  RAW_NOTIFICATION_DETAILS = 30;
  DEDUPE_ID = 31;
  ORDER_REF_ID = 32;
  COMPUTED_EXECUTED_AT = 33;
  METADATA = 34;
}

// Metadata contains additional information for each transaction. It will not contain any queryable data. It will be
// used for analytics or for displaying UI elements.
message Metadata {
  ParserTemplate parser_template = 1;
  EnrichedPiDetailsOfTransactingActors fetched_remitter_details = 2;
  // stores the VPA of the payer and payee involved in the transaction for UPI transactions
  VpaDetails vpa_details = 3;
}

// Raw Transaction notification/ statement data gets parsed using parser ( regex or via parser service) service containing multiple parser rules
// Parser Template is unique identifier for the rules which will be used to uniquely identify each rule.
message ParserTemplate {
  // Unique identifier for the parser rule used to execute/parse the raw notifications.
  // This id is unique with each regex and with any update it will not change.
  // id is constant for each parser rule and doesnot change for a rule.
  order.payment.notification.enums.ParserTemplateId id = 1;

  // Version for a particular rule signifies changes in rule and is upgraded with each change in the rule.
  // Its maintained so that old parsed transactions with older version of same parser rule can be backfilled again with updated version of parser.
  string version = 2;
}

// EnrichedPiDetailsOfTransactingActors data for transaction fetched from enriched process. Process could be one of below:
// 1. RemitterFetch API from vendor.
message EnrichedPiDetailsOfTransactingActors {
  string payer_pi_id = 1;
  string payee_pi_id = 2;
  string payer_name = 3;
  string payee_name = 4;
}

// VpaDetails can be used to store metadata corresponding to the payer & payee VPAs in the transaction metadata column.
message VpaDetails {
  reserved 1, 2;
  // PI ID the payment_instrument corresponding to the payer VPA
  string payer_vpa_pi_id = 3;
  // PI ID the payment_instrument corresponding to the payee VPA
  string payee_vpa_pi_id = 4;
}
