// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order.payment;

import "api/queue/consumer_headers.proto";
import "api/vendorgateway/vendor.proto";
import "api/vendorgateway/decline_type.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/order/payment";
option java_package = "com.github.epifi.gamma.api.order.payment";

message ProcessCallBackRequest {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;

  // Vendor bank at which transaction is initiated
  vendorgateway.Vendor partner_bank = 2 [(validate.rules).enum = {not_in: [0]}];

  // Request ID of the transaction for which the call back is initiated
  string request_id = 3 [(validate.rules).string.min_len = 1];

  // UTR number stands for Unique Transaction Reference number
  // UTR No (Unique transaction reference number) is generally used for
  // reference of a particular NEFT /RTGS transaction.
  //
  // IMPS reference number in case of IMPS
  // Bank reference number in case of Intra fund transfer
  string utr = 4;

  // Account number of the Remitter
  string remitter_account_number = 5;

  // Account number of the Beneficiary
  string beneficiary_account_number = 6;

  // Registered name of Account holder with the beneficiary bank
  // Populated only in the case of IMPS transaction
  string credited_account_name = 7;

  // Transaction amount
  google.type.Money amount = 8;

  // Timestamp at which the transaction state is updated
  google.protobuf.Timestamp trans_timestamp = 9 [(validate.rules).timestamp.required = true];


  // High level response codes depicting the stage of the transaction
  // PROCESSED, SUCCESS, FAILURE, UNKNOWN(SUSPECT)
  enum ResponseAction {
    RESPONSE_ACTION_UNSPECIFIED = 0;
    // Initiated at partner bank's end
    PROCESSED = 1;
    // Partner bank is unaware of the transaction status and hence marked as a SUSPECT transaction.
    // Partner bank will send update once it is aware of the state
    // Epifi can also enquire the transaction status to resolve the ambiguity
    UNKNOWN = 2;
    // Transaction has been processed and money is credited to the beneficiary account
    SUCCESS = 3;
    // Transaction has been processed unsuccessfully. Money is not credited to the beneficiary account
    FAILURE = 4;
    // Transaction is in DEEMED APPROVED state, i.e. the money has been debited from the Remitter account
    // but the credit to beneficiary is pending. If the transaction is settled in 3-5 days, then the
    // money is transferred otherwise it is refunded.
    DEEMED_APPROVED = 5;
  }
  ResponseAction response_action = 10 [(validate.rules).enum = {not_in: [0]}];

  // response code as sent by the vendor bank
  string raw_response_code = 11;

  // description for the response code as sent by the vendor bank
  string raw_response_description = 12;

  // status Code for the Transaction corresponding to the ras response code
  string status_code = 13;

  // description of the status code for payer
  string status_description_payer = 14;

  // description of the status code for the payee
  string status_description_payee = 15;

  // failure decline type
  vendorgateway.DeclineType decline_type = 16;
}

message ProcessCallBackResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}

// Defines the GRPC service to process the payment callback from the vendor
// This GRPC service is registered with queue subscriber and RPC method will be invoked by the consumer
// on receiving an event
service CallBackConsumer {
  // ProcessCallBack updates the state of a transaction
  // This method is invoked by queue subscriber to consume call back notifications from vendor's payment service.
  rpc ProcessCallBack (ProcessCallBackRequest) returns (ProcessCallBackResponse) {}
}
