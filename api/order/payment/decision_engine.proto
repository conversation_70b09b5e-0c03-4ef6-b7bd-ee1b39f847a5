// protolint:disable MAX_LINE_LENGTH
// Defines the payment decision engine grpc service
syntax = "proto3";

package order.payment;

import "api/accounts/account_type.proto";
import "api/order/payment/payment_protocol.proto";
import "api/payment_instruments/payment_instrument.proto";
import "api/rpc/status.proto";
import "api/typesv2/account/enums.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/order/payment";
option java_package = "com.github.epifi.gamma.api.order.payment";

message GetTransferDecisionRequest {
  // This is now deprecated. Use current_user_identifier instead.
  // actor id of the current logged in actor
  string current_actor_id = 1 [deprecated = true];

  // This is now deprecated. Use second_user_identifier instead.
  // actor id of the second actor, with whom the current actor is doing a transaction with
  string second_actor_id = 2 [deprecated = true];

  // Amount of monies involved in the transaction
  google.type.Money amount = 3;

  // differentiate between a pay and collect request
  bool is_collect_request = 4;

  // payment protocol preferred for the transaction
  order.payment.PaymentProtocol preferred_payment_protocol = 5 [deprecated = true];

  oneof payment_protocol {
    // soft preferred payment protocol
    // this payment protocol is given the first priority, if available this will be used
    // if not available will check for other protocols
    order.payment.PaymentProtocol soft_preferred_payment_protocol = 6;

    // payment protocol preferred for the transaction
    // this payment protocol will be used if available
    // availability of other protocols will not be checked
    order.payment.PaymentProtocol hard_preferred_payment_protocol = 7;
  }

  // initiation mode in case of URN payment
  string initiation_mode = 8;

  // purpose code in case of URN payment
  string purpose_code = 9;

  // This is now deprecated. Use second_user_identifier instead.
  // preferred second actor pi id
  string second_actor_hard_preferred_pi_id = 10 [deprecated = true];

  // This is now deprecated. Use current_user_identifier instead.
  // optional: accountId for the account through which we want to make payment
  // If this field is not empty, we will use this to fetch pis for the account.
  string account_id = 11 [deprecated = true];

  // information related to current user (like actorId, accountId, account type, piId).
  UserIdentifier current_user_identifier = 12;

  // information related to second user (like actorId, accountId, account type, piId) with whom the current actor is doing a transaction with
  UserIdentifier second_user_identifier = 13;
}

message GetTransferDecisionResponse {

  enum Status {
    OK = 0;
    // request parameters invalid
    INVALID_ARGUMENT = 3;
    // internal server error
    INTERNAL = 13;
    // current actor vpa is disabled.
    // returned when no payment option is available,
    // but upi could have been available if the VPA was enabled
    VPA_DISABLED = 100;
    // returned when user exhausts daily txn amount limit for upi
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED = 101;
    // returned when user exhausts daily txn count limit for upi
    UPI_TOTAL_TXN_COUNT_EXCEEDED = 102;
    // when user update their device, user account enters a cooldown phase
    // during this period, user has a total txn amount restriction for upi transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_DEVICE_COOLDOWN_PHASE = 103;
    // txn amount not in range permitted for upi protocol
    UPI_TXN_AMOUNT_EXCEEDED = 104;
    // after the user resets their upi pin for a period of 12 hrs
    // during this period, user has a total txn amount restriction for upi transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_AFTER_PIN_RESET = 105;
    // during Cooldown period, user has a total txn amount restriction for IMPS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    IMPS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 106;
    // for new user, user account is in cooldown phase for cooldown period.
    // during Cooldown period, user has a total txn amount restriction for IMPS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    IMPS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_NEW_USER_COOLDOWN_PHASE = 107;
    // when user update their device, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for IMPS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    IMPS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_DEVICE_COOLDOWN_PHASE = 108;
    // when user update their mobile, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for IMPS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    IMPS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_MOBILE_COOLDOWN_PHASE = 109;
    // when user update their email, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for IMPS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    IMPS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_EMAIL_COOLDOWN_PHASE = 110;
    // during Cooldown period, user has a total txn amount restriction for INTRA transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    INTRA_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 111;
    // for new user, user account is in cooldown phase for cooldown period.
    // during Cooldown period, user has a total txn amount restriction for INTRA transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    INTRA_TOTAL_TXN_AMOUNT_EXCEEDED_IN_NEW_USER_COOLDOWN_PHASE = 112;
    // when user update their device, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for INTRA transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    INTRA_TOTAL_TXN_AMOUNT_EXCEEDED_IN_DEVICE_COOLDOWN_PHASE = 113;
    // when user update their mobile, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for INTRA transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    INTRA_TOTAL_TXN_AMOUNT_EXCEEDED_IN_MOBILE_COOLDOWN_PHASE = 114;
    // when user update their email, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for INTRA transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    INTRA_TOTAL_TXN_AMOUNT_EXCEEDED_IN_EMAIL_COOLDOWN_PHASE = 115;
    // during Cooldown period, user has a total txn amount restriction for NEFT transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    NEFT_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 116;
    // for new user, user account is in cooldown phase for cooldown period.
    // during Cooldown period, user has a total txn amount restriction for NEFT transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    NEFT_TOTAL_TXN_AMOUNT_EXCEEDED_IN_NEW_USER_COOLDOWN_PHASE = 117;
    // when user update their device, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for NEFT transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    NEFT_TOTAL_TXN_AMOUNT_EXCEEDED_IN_DEVICE_COOLDOWN_PHASE = 118;
    // when user update their mobile, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for NEFT transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    NEFT_TOTAL_TXN_AMOUNT_EXCEEDED_IN_MOBILE_COOLDOWN_PHASE = 119;
    // when user update their email, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for NEFT transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    NEFT_TOTAL_TXN_AMOUNT_EXCEEDED_IN_EMAIL_COOLDOWN_PHASE = 120;
    // during Cooldown period, user has a total txn amount restriction for RTGS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    RTGS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 121;
    // for new user, user account is in cooldown phase for cooldown period.
    // during Cooldown period, user has a total txn amount restriction for RTGS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    RTGS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_NEW_USER_COOLDOWN_PHASE = 122;
    // when user update their device, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for RTGS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    RTGS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_DEVICE_COOLDOWN_PHASE = 123;
    // when user update their Mobile, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for RTGS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    RTGS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_MOBILE_COOLDOWN_PHASE = 124;
    // when user update their email, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for RTGS transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    RTGS_TOTAL_TXN_AMOUNT_EXCEEDED_IN_EMAIL_COOLDOWN_PHASE = 125;
    // during Cooldown period, user has a total txn amount restriction for UPI transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 126;
    // for new user, user account is in cooldown phase for cooldown period.
    // during Cooldown period, user has a total txn amount restriction for UPI transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_NEW_USER_COOLDOWN_PHASE = 127;
    // when user update their mobile, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for UPI transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_MOBILE_COOLDOWN_PHASE = 128;
    // when user update their email, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for UPI transactions
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    UPI_TOTAL_TXN_AMOUNT_EXCEEDED_IN_EMAIL_COOLDOWN_PHASE = 129;
    // during Cooldown period, user has a total txn amount restriction for all transactions like rtgs, upi, neft, imps, intra.
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    TOTAL_TXN_AMOUNT_EXCEEDED_IN_NEW_USER_COOLDOWN_PHASE = 130;
    // for new user, user account is in cooldown phase for cooldown period.
    // during Cooldown period, user has a total txn amount restriction for all transactions like rtgs, upi, neft, imps, intra.
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    TOTAL_TXN_AMOUNT_EXCEEDED_IN_COOLDOWN_PHASE = 131;
    // when user update their device, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for all transactions like rtgs, upi, neft, imps, intra.
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    TOTAL_TXN_AMOUNT_EXCEEDED_IN_DEVICE_COOLDOWN_PHASE = 132;
    // when user update their mobile, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for all transactions like rtgs, upi, neft, imps, intra.
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    TOTAL_TXN_AMOUNT_EXCEEDED_IN_MOBILE_COOLDOWN_PHASE = 133;
    // when user update their email, user account enters a cooldown phase
    // during Cooldown period, user has a total txn amount restriction for all transactions like rtgs, upi, neft, imps, intra.
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    TOTAL_TXN_AMOUNT_EXCEEDED_IN_EMAIL_COOLDOWN_PHASE = 134;
    // user has limit on the total transaction amount in the given time duration
    // returned when user has breached the total transaction amount in the given time duration
    TOTAL_TXN_AMOUNT_EXCEEDED_FOR_USER_IN_CONFIGURED_TIME = 135;
    // payer vpa is disabled.
    PAYER_VPA_DISABLED = 136;
    // upi payments for remitter account is unhealthy
    UPI_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 137;
    // IMPS payments for remitter account is unhealthy
    IMPS_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 138;
    // RTGS payments for remitter account is unhealthy
    RTGS_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 139;
    // NEFT payments for remitter account is unhealthy
    NEFT_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 140;
    // INTRA payments for remitter account is unhealthy
    INTRA_PAYMENTS_IN_UNHEALTHY_STATE_FOR_REMITTER = 141;
    // when user add any new beneficiary, beneficiary  enters a cooldown phase for that user who has added that beneficiary
    // during Cooldown period, user has a total txn amount restriction for all transactions like rtgs, neft, imps, intra.
    // returned when no payment option is available and current transaction amount crosses this restriction limit
    BENEFICIARY_IS_IN_COOLDOWN_PHASE = 142;
    // - Indicates accounts that cannot perform transactions due to their status (Closed, Dormant, Inactive).
    // - Not Applies on Debit Freeze & Total Freeze, since we will be covering those in ACCOUNT_FROZEN
    // For Example these cases:
    // Account Closed (Freeze Status is either Credit Freeze or No Freeze)
    // Account Dormant (Freeze Status is either Credit Freeze or No Freeze)
    // Account Inactive (Freeze Status is either Credit Freeze or No Freeze)
    ACCOUNT_NOT_ACTIVE = 143;
    // - Specifically targets the freeze status of an account (Total Freeze, Debit Freeze, Credit Freeze) which restricts to perform transactions.
    // - Credit Freeze is considered only if the account is active, as it restricts incoming transactions but allows outgoing ones.
    // For Example these cases:
    // Total Freeze
    // Debit Freeze
    // Credit Freeze (only if Account is Active)
    ACCOUNT_FROZEN = 144;
    // payments are not supported for the given IFSC
    IFSC_NOT_SUPPORTED = 145;
    // RuPay credit card is not accepted by the merchant for the transaction
    RUPAY_CREDIT_CARD_NOT_ACCEPTED = 146;
    // Initiation mode is not allowed for the merchants with this MCC
    MCC_TO_INITIATION_MODE_NOT_ALLOWED = 147;
  }

  rpc.Status status = 1;
  // payment instrument to which the payment should be done
  paymentinstrument.PaymentInstrument pi_to = 2;
  // payment instrument from which the payment should be done
  paymentinstrument.PaymentInstrument pi_from = 3;
  // payment protocol used for transfer as decided by the decision engine
  order.payment.PaymentProtocol payment_protocol = 4;
  // Pay error code is an internal translation of various vendor and pay failure codes
  // The client may refer to this code to get more detailed insights regarding the
  // payment failure.
  // Some possible use-cases can be, but not limited to:
  // 1) Showing detailed error view to the user on the app
  // 2) Handling payment retries in backend for non-retryable failures like insufficient balance or
  //    account de-activation
  string pay_error_code = 5;
}

// Defines the GRPC service to decide the best payment method for a particular transaction
service DecisionEngine {
  // RPC method to get the best payment protocol and Pis (PiFrom and PiTo) to use for a transaction

  // This method takes in the current actor, second actor, preferred payment protocol and transaction amount
  // Fetches Pis corresponding to both payer and payee actors.

  // If preferred payment protocol is passed, only feasibility of that payment protocol will be checked, and pis
  // corresponding to the protocol will be returned

  // If payment protocol is not present, payment protocol is decided based on various factors like availability of payment instruments,
  // transaction amount, time window when transaction is being done, etc.
  rpc GetTransferDecision (GetTransferDecisionRequest) returns (GetTransferDecisionResponse) {}
}

message CoolDownDetails {
  enum ProfileUpdateType {
    COOLDOWN_DETAILS_TYPE_UNSPECIFIED = 0;
    // when user onboarded first time
    NEW_USER_COOLDOWN = 1;
    // when user update their device
    DEVICE_COOLDOWN = 2;
    // when user update their email id
    EMAIL_COOLDOWN = 3;
    // when user update their mobile number
    MOBILE_COOLDOWN = 4;
  }
}

// Information related to user like actorId, accountId, accountType, piId.
// It is not mandatory to have all the field.
message UserIdentifier {
  string actor_id = 1;
  string account_id = 2;
  accounts.Type account_type = 3;
  string pi_id = 4;
  // AccountProductOffering associated with the AccountType.
  // Note: It can be UNSPECIFIED in case it's an older account.
  api.typesv2.account.AccountProductOffering apo = 5;
}
