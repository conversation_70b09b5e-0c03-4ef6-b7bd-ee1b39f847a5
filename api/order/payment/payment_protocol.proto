// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package order.payment;


option go_package = "github.com/epifi/gamma/api/order/payment";
option java_package = "com.github.epifi.gamma.api.order.payment";

// PaymentProtocol defines list of protocols at a bank through which a transaction can be sent
enum PaymentProtocol {
  PAYMENT_PROTOCOL_UNSPECIFIED = 0;

  // Intra bank Fund Transfer denotes a transfer to a beneficiary having account in the same bank
  // This protocol provides a real time fund transfer solution to transfer funds between two accounts maintained with the
  // same partner bank
  //
  // Restrictions like minimum and maximum amount can be applicable and are applicable by the partner bank on a client
  INTRA_BANK = 1;

  // National Electronic Funds Transfer (NEFT) is an electronic funds transfer system maintained by the RBI.
  // It enables Bank customers to transfer funds from any bank branch to any individual having an account with another
  // participating bank branch.
  //
  // The fund transfer is not real time but takes place in batches which are settled on half-hourly time slots.
  // This means that if the transaction is done after the settlement, it is required to wait until the next settlement time.
  // There will be 48 half-hourly batches every day. The settlement of first batch will commence after 00:30 hours and the
  // last batch will end at 00:00 hours.
  // The system will be available on all days of the year, including holidays.
  //
  // NEFT does not impose any minimum or maximum limits on the amount to be transferred although partner bank can.
  NEFT = 2;

  // The Immediate Payment Service (IMPS) provides a real time fund transfer solution,
  // offering an instantaneous, 24x7, interbank electronic fund transfer service that is provided by the NPCI.
  //
  // In order to use IMPS for fund transfer, both Remitter as well as Beneficiary need to register their mobile number with the
  // respective Bank account and get MMID (Mobile Money Identifier). In order to cater to a scenario where the Beneficiary’s
  // mobile number is not registered with any Bank Account, IMPS funds transfer has been made possible using Beneficiary account
  // number and IFSC code.
  //
  // Maximum limit that can be transferred is INR 2 lakhs, although this can vary from Bank to Bank.
  IMPS = 3;

  // Real Time Gross Settlement (RTGS) is a fund transfer system maintained by the RBI, which enables fund transfer
  // from one Bank to another, in real-time and gross basis. Real time settlement means transactions are settled as soon as
  // they are processed. The transactions are settled on a one-to-one basis, which means the system will not club or bundle
  // a transaction with another. This is one of the fastest possible ways to transfer money, between Banks in India.
  // RTGS is a better option for high value transactions, that need immediate clearing.
  //
  // The RTGS service window for customer's transactions is available to banks from 9.00 hours to 16.30 hours on week days and
  // from 9.00 hours to 14:00 hours on Saturdays for settlement at the RBI end. However, the timings that the banks follow may
  // vary depending on the customer timings of the bank branches.
  //
  // If the RTGS request is received after the cut-off time, system will try the NEFT protocol.
  // Note that the request cannot be cancelled.
  //
  // Minimum amount to be remitted is INR 2 lakhs and maximum limit varies from Bank to Bank.
  RTGS = 4;

  // Unified Payments Interface (UPI) is an instant real-time 24x7 payment system developed by National Payments Corporation of India
  // facilitating inter-bank transactions. The interface is regulated by the Reserve Bank of India and works by instantly
  // transferring funds between two bank accounts on a mobile platform.
  //
  // Money can be sent or requested with the following methods:
  //
  // - Virtual Payment Address (VPA) or UPI ID: Send or request money from/to bank account mapped using VPA.
  // - Mobile number: Send or request money from/to the bank account mapped using mobile number.
  // - Account number & IFSC: Send money to the bank account.
  // - Aadhaar: Send money to the bank account mapped using Aadhaar number.
  // - QR code: Send money by QR code which has enclosed VPA, Account number and IFSC or Mobile number.
  //
  // The transaction limit per UPI transaction and overall transaction limit per day is INR 1 lakh
  // UPI transaction frequency limit has been set to 10 per day for Peer to Peer transfers (P2P) and no limit for P2M
  // These limits vary from Bank to Bank.
  UPI = 5;

  // all the card related transactions - POS/ATM/ E-Comm are denoted by cards
  // card can be of any type - debit/credit
  CARD = 6;

  // Aadhaar Enabled Payment System (AePS) is a bank led model which allows online interoperable financial inclusion transaction at PoS (MicroATM)
  // through the Business correspondent of any bank using the Aadhaar authentication
  AEPS = 7;

  // Society for Worldwide Interbank Financial Telecommunications (SWIFT)
  // SWIFT payments are payment transactions using the SWIFT international payment network. This network is used to send or receive international electronic payments,
  // which is why SWIFT payments are sometimes referred to as international wire payments. The SWIFT network doesn’t actually transfer funds but instead,
  // sends payment orders between banks using SWIFT codes. It’s a means to transfer money overseas quickly, accurately, and securely.
  //
  // each bank in the SWIFT network will have SWIFT code which identifies the bank individually
  // if a bank in SWIFT network don't know the bank to which it is sending money it needs to route the money to an intimidatory bank
  // which knows the payee bank
  //
  // for more details please ref: https://tipalti.com/swift-payments/
  SWIFT = 8;

  // e-NACH stands for Electronic National Automated Clearing House. It is a centralized electronic payment system introduced by the National Payments Corporation of India (NPCI). e-NACH facilitates automated, paperless, and electronic clearing of payments, allowing individuals and businesses to authorize their banks to debit their accounts for recurring payments.
  // e-NACH enables various types of transactions, including utility bill payments, loan repayments, insurance premiums, investments, and more. It eliminates the need for physical mandates and paper-based processes by providing an electronic platform for initiating, processing, and settling payments.
  ENACH = 9;

  // RBI is considering replacement of the existing system of settlement of payment on the basis of physical cheques by a new procedure called “ Cheque Truncation System” (CTS).
  // It is an online image-based cheque clearing system where cheque images and Magnetic Ink Character Recognition (MICR) data are captured at the collecting  bank branch and transmitted electronically eliminating the actual cheque movement.
  CTS = 10;
}
