syntax = "proto3";

package order.campaign;

import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";


option go_package = "github.com/epifi/gamma/api/order/campaign";
option java_package = "com.github.epifi.gamma.api.order.campaign";

// Defines grpc service to run the payment related campaigns
service Campaign {
  // RunPayCampaigns executes operations necessary for a campaign.
  // The business logic for running a campaign depends on the campaign type
  rpc RunPayCampaigns(RunPayCampaignsRequest) returns (RunPayCampaignsResponse);
}


message RunPayCampaignsRequest {

  message AddFundsCampaign {
    // timestamp from which the users should be considered for running the campaigns
    google.protobuf.Timestamp from_time = 1;

    // timestamp till which the users should be considered for running the campaigns
    google.protobuf.Timestamp to_time = 2;
    // size of the batch to send campaigns
    int32 batch_size = 3;
  }

  // campaign to run
  oneof campaign{
    AddFundsCampaign add_funds_campaign = 1;
  }

}

message RunPayCampaignsResponse {
  enum Status {
    OK = 0;
    // invalid argument passed in the request
    INVALID_ARGUMENT = 3;
    // internal error while processing the request
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}
