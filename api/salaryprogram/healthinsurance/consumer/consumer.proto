//go:generate gen_queue_pb
syntax = "proto3";

package salaryprogram.healthinsurance.consumer;

import "api/queue/consumer_headers.proto";
import "api/salaryprogram/healthinsurance/policy_details.proto";
import "api/vendorgateway/vendor.proto";


option go_package = "github.com/epifi/gamma/api/salaryprogram/healthinsurance/consumer";
option java_package = "com.github.epifi.gamma.api.salaryprogram.healthinsurance.consumer";

service Consumer {
  // method to fetch the policy purchase status (if it was not in terminal state before) from the vendor and update the policy status and details in the db.
  rpc ProcessPollPolicyPurchaseStatusEvent (PollPolicyPurchaseStatusRequest) returns (ConsumerResponse);
  // method to process policy issuance completion event, this event is received from the vendor once a policy issuance is successful.
  rpc ProcessPolicyIssuanceCompletedEvent (PolicyIssuanceCompletedEventRequest) returns (ConsumerResponse);
}

message PollPolicyPurchaseStatusRequest {
  vendorgateway.Vendor policy_vendor = 1;
  // vendor request id using which the policy purchase was initiated.
  string policy_issuance_vendor_req_id = 2;

  queue.ConsumerRequestHeader request_header = 15;
}

message PolicyIssuanceCompletedEventRequest {
  // policy vendor who issued the policy.
  vendorgateway.Vendor policy_vendor = 1;
  // vendor request id using which the policy purchase was initiated.
  // can be empty if policy was issued due to auto-renew case.
  string policy_issuance_vendor_req_id = 2;
  // additional details of the issued policy.
  PolicyMetadata policy_metadata = 3;

  queue.ConsumerRequestHeader request_header = 15;
}

message ConsumerResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
