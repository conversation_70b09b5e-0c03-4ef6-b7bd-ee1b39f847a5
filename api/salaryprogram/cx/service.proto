syntax = "proto3";

package salaryprogram.cx;

import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/salaryprogram/cx/enums.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/salaryprogram/cx";
option java_package = "com.github.epifi.gamma.api.salaryprogram.cx";

service Cx {
  // GetPossibleActorsForManualSalaryVerification returns the list of possible actors for whom salary verification request can be raised.
  // The eligibility criteria is subject to change but depends on the following:
  // 1. Days since last-verification-txn-timestamp
  // 2. Current activation status
  // 3. Their last approval was via Ops
  rpc GetPossibleActorsForManualSalaryVerification (GetPossibleActorsForManualSalaryVerificationRequest) returns (GetPossibleActorsForManualSalaryVerificationResponse);

  // UpdateUserSalaryVerificationEligibilityStatus updates the status of the user who is into consideration for raising salary verification request.
  // salary txn verification request is raised on updating the status to COMPLETE.
  rpc UpdateUserSalaryVerificationEligibilityStatus (UpdateUserSalaryVerificationEligibilityStatusRequest) returns (UpdateUserSalaryVerificationEligibilityStatusResponse);
}

message GetPossibleActorsForManualSalaryVerificationRequest {
  // filters for the actors
  Filters filters = 1;
  // sort order for sorting by last verified salary txn date.
  SortOrder sort_order = 2;
  // page context to help server fetch the page
  rpc.PageContextRequest page_context_request = 3;

  message Filters {
    // user salary verification eligibility status is status given to actor based on eligibility for raising manual salary verification request
    // If this param is not passed then actors with NEW status will be returned.
    UserSalaryVerificationEligibilityStatus user_salary_verification_eligibility_status = 1;
    // actor id of possible actor for manual salary verification.
    string actor_id = 2;
    // salary program stage denotes the stage at which user is in salary program flow
    // eg. REGISTERED_AND_ACTIVATION_PENDING, SALARY_ACTIVE
    SalaryProgramStage salary_program_stage = 3;
  }
}

message GetPossibleActorsForManualSalaryVerificationResponse {
  // grpc response status
  rpc.Status status = 1;
  // list of possible actors info
  repeated ActorInfo actors_info = 2;
  // page context to help client fetch the next page
  rpc.PageContextResponse page_context_response = 3;

  message ActorInfo {
    string actor_id = 1;
    // last verified salary txn timestamp
    google.protobuf.Timestamp last_verified_salary_txn_timestamp = 4;
    // actor's employer name.
    string employer_name = 5;
    // status given to actor based on eligibility for raising manual salary verification request
    UserSalaryVerificationEligibilityStatus user_salary_verification_eligibility_status = 6;
    // denotes the time at which user completed salary program registration
    google.protobuf.Timestamp registration_completion_time = 7;
  }
}

message UpdateUserSalaryVerificationEligibilityStatusRequest {
  // actor id of actor whose status to be updated
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // txn-id of the txn for which salary verification request is to be raised, the above actor should be the beneficiary of the given order.
  // Required if updating the status to COMPLETE.
  string txn_id = 2;
  // updated user salary verification eligibility status
  UserSalaryVerificationEligibilityStatus updated_user_salary_verification_eligibility_status = 3 [(validate.rules).enum = {not_in: [0]}];
}

message UpdateUserSalaryVerificationEligibilityStatusResponse {
  // grpc response status
  rpc.Status status = 1;
}
