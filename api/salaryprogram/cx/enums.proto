//go:generate gen_sql -types=UserSalaryVerificationEligibilityStatus
syntax = "proto3";

package salaryprogram.cx;

option go_package = "github.com/epifi/gamma/api/salaryprogram/cx";
option java_package = "com.github.epifi.gamma.api.salaryprogram.cx";

// UserSalaryVerificationEligibilityStatus: status given to users based on the eligibility for raising manual salary verification.
enum UserSalaryVerificationEligibilityStatus {
  ELIGIBILITY_STATUS_UNSPECIFIED = 0;
  // status for users whose eligibility for manual salary verification is to be reviewed.
  NEW = 1;
  // status for users whose eligibility for manual salary verification was reviewed and salary verification request was not raised.
  PENDING = 2;
  // status for users for whom the salary verification request is raised.
  COMPLETE = 3;
}

enum SortOrder {
  DESC = 0;
  ASC = 1;
}

enum RaiseSalaryVerByOpsEligibilityInfoFieldMask {
  FIELD_MASK_UNSPECIFIED = 0;
  LAST_ACTIVATION_FROM_TIME = 1;
  LAST_ACTIVATION_TILL_TIME = 2;
  LAST_VERIFIED_SALARY_TXN_TIMESTAMP = 3;
  LAST_VERIFIED_SALARY_TXN_VERIFIED_BY = 4;
  ELIGIBILITY_STATUS = 5;
}

// salary program stage denotes the stage at which user is in salary program flow REGISTERED_AND_ACTIVATION_PENDING, SALARY_ACTIVE
enum SalaryProgramStage {
  SALARY_PROGRAM_STAGE_UNSPECIFIED = 0;
  // user has completed registration but has not been salary program active even once
  REGISTERED_AND_ACTIVATION_PENDING = 1;
  // user is salary program active
  SALARY_ACTIVE = 2;
}
