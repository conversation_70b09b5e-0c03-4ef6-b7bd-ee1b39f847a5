//go:generate gen_sql -types=SalaryLiteMandateExecutionRequestStatus,SalaryLiteMandateExecutionProvenance
syntax = "proto3";

package salaryprogram;

import "google/protobuf/timestamp.proto";


option go_package = "github.com/epifi/gamma/api/salaryprogram";
option java_package = "com.github.epifi.gamma.api.salaryprogram";

message SalaryLiteMandateExecutionRequest {
  string id = 1;
  // recurring payment id for the mandate
  string recurring_payment_id = 2;
  // status of the mandate execution
  SalaryLiteMandateExecutionRequestStatus request_status = 3;
  // Provenance: the beginning of something's existence; something's origin.
  // This enum represents different entry provenance of request in the system
  SalaryLiteMandateExecutionProvenance provenance = 7;
  // denotes the actor id of the user for which the mandate execution request has been made
  string actor_id = 8;
  // denotes the id of salary_lite_mandate_request for which the mandate execution request has been made
  string mandate_request_id = 9;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  google.protobuf.Timestamp deleted_at = 6;
}

enum SalaryLiteMandateExecutionRequestStatus {
  EXECUTION_REQUEST_STATUS_UNSPECIFIED = 0;
  // mandate execution success
  EXECUTION_SUCCESS = 1;
  // mandate execution is in progress
  EXECUTION_IN_PROGRESS = 2;
  // mandate execution failed
  EXECUTION_FAILED = 3;
}

// enum fields corresponding to columns allowed to update in SalaryLiteMandateExecutionRequest model
enum SalaryLiteMandateExecutionRequestFieldMask {
  FIELD_MASK_UNSPECIFIED = 0;
  // field mask corresponding to request_status column
  // prefixed with EXECUTION to avoid namespace conflict with SalaryLiteMandateRequestFieldMasks
  EXECUTION_REQUEST_STATUS = 1;
}

// Provenance: the beginning of something's existence; something's origin.
// This enum represents different entry provenance of request in the system
enum SalaryLiteMandateExecutionProvenance {
  SALARY_LITE_MANDATE_EXECUTION_PROVENANCE_UNSPECIFIED = 0;
  // salary lite mandate execution initiated by a scheduled job/script
  PROVENANCE_SCHEDULED_JOB = 1;
  // salary lite mandate execution initiated from retry execution flow on Fi app
  PROVENANCE_RETRY_EXECUTION_FROM_APP = 2;
}
