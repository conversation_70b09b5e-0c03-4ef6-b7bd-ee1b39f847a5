//go:generate gen_sql -types=SalaryBand
syntax = "proto3";

package salaryprogram.enums;

option go_package = "github.com/epifi/gamma/api/salaryprogram/enums";
option java_package = "com.github.epifi.gamma.api.salaryprogram.enums";

enum SalaryBand {
  SALARY_BAND_UNSPECIFIED = 0;
  SALARY_BAND_1 = 10;
  SALARY_BAND_2 = 20;
  SALARY_BAND_3 = 30;
}

enum B2BSalaryBand {
  B2B_SALARY_BAND_UNSPECIFIED = 0;
  B2B_SALARY_BAND_1 = 10;
  B2B_SALARY_BAND_2 = 20;
}

enum AaSalaryStage {
  AA_SALARY_STAGE_UNSPECIFIED = 0;
  AA_SALARY_STAGE_INITIATED = 5;
  AA_SALARY_STAGE_ACCOUNT_CONNECTED = 10;
  AA_SALARY_STAGE_INCOME_ESTIMATED = 20;
  AA_SALARY_STAGE_SALARY_COMMITTED = 30;
  AA_SALARY_STAGE_AMOUNT_TRANSFERRED = 40;
  AA_SALARY_STAGE_ACTIVATED = 50;
  AA_SALARY_STAGE_ACTIVATED_IN_GRACE = 60;
  AA_SALARY_STAGE_DEACTIVATED = 70;
}
