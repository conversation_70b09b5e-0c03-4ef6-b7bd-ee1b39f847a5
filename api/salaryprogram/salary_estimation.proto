//go:generate gen_sql -types=SalaryEstimatedBy,SalaryEstimationProvenance,SalaryAccountSourceType
syntax = "proto3";

package salaryprogram;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/salaryprogram";
option java_package = "com.github.epifi.gamma.api.salaryprogram";

// SalaryEstimatedBy indicates the entity that verified the salary detection
enum SalaryEstimatedBy {
    SALARY_ESTIMATED_BY_UNSPECIFIED = 0;
    SALARY_ESTIMATED_BY_AA_SALARY_ESTIMATOR_V1 = 1;
    SALARY_ESTIMATED_BY_SMS_PARSER = 2;
}

// SalaryEstimationProvenance indicates the source or provenance of the salary detection
enum SalaryEstimationProvenance {
    SALARY_ESTIMATION_PROVENANCE_UNSPECIFIED = 0;
    SALARY_ESTIMATION_PROVENANCE_AUTO_UPGRADES = 1;
    SALARY_ESTIMATION_PROVENANCE_IN_APP_USER_REQUEST = 2;
}

// SalaryAccountSourceType signifies the source type of entity which is used for salary detection
enum SalaryAccountSourceType {
    SALARY_ACCOUNT_SOURCE_TYPE_UNSPECIFIED = 0;
    SALARY_ACCOUNT_SOURCE_TYPE_AA_ACCOUNT = 1;
    SALARY_ACCOUNT_SOURCE_TYPE_SMS_PARSER = 2;
}

// SalaryEstimationStalenessStatusType signifies the status of the staleness i.e. LATEST/STALE
enum SalaryEstimationStalenessStatusType {
    SALARY_ESTIMATION_STALENESS_STATUS_TYPE_UNSPECIFIED = 0;
    SALARY_ESTIMATION_STALENESS_STATUS_TYPE_STALE = 1;
    SALARY_ESTIMATION_STALENESS_STATUS_TYPE_LATEST = 2;
}

message SalaryEstimation {
    // Unique identifier for each record
    string id = 1;
    // Identifier for the actor associated with the salary estimation
    string actor_id = 2;
    // Identifier of the account considered for salary estimation, e,g, AA Account,
    string salary_account_source_ref_id = 3;
    // Source type of entity which is used for salary estimation
    SalaryAccountSourceType salary_account_source_type = 4;
    // Last estimated amount of the salary estimation
    google.type.Money last_estimated_amount = 5;
    // Timestamp indicating when the last verification was performed
    google.protobuf.Timestamp last_estimated_at = 6;
    // Timestamp indicating the last verified transaction time
    google.protobuf.Timestamp last_estimated_txn_timestamp = 7;
    // Source or provenance of the salary estimation
    SalaryEstimationProvenance provenance = 8;
    // Identifier of the entity that estimated the salary estimation
    SalaryEstimatedBy estimated_by = 9;
    // signifies the status of the staleness
    SalaryEstimationStalenessStatusType status = 10;
    google.protobuf.Timestamp created_at = 11;
    google.protobuf.Timestamp updated_at = 12;
    google.protobuf.Timestamp deleted_at = 13;
}
