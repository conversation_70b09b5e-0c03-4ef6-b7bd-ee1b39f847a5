//go:generate gen_sql -types=CriteriaName,CriteriaStatus,Criterion
syntax = "proto3";

package salaryprogram.aa;

import "api/salaryprogram/enums/salary_band.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/salaryprogram/aa";
option java_package = "com.github.epifi.gamma.api.salaryprogram.aa";

message AaSalaryCriteria {
  // Auto generated uuid
  string id = 1;
  // Enum for criteria name
  CriteriaName criteria_name = 2;
  Criterion details = 3;
  // Enum to represent current status of a tier criteria
  CriteriaStatus status = 4;
  // To represent timestamp of first time execution of a criteria
  google.protobuf.Timestamp executed_at = 5;
  // Timestamp at which criteria created
  google.protobuf.Timestamp created_at = 6;
  // Timestamp at which criteria updated
  google.protobuf.Timestamp updated_at = 7;
}

message Criterion {
  repeated CriteriaDetail criteria_detail_list = 1;
}

message CriteriaDetail {
  // Set of actions - on meeting all of them, criteria will be satisfied
  repeated Action actions = 1;
  // Salary Band
  enums.SalaryBand salary_band = 2;
}

message Action {
  // Action details related to a particular action
  // Can be one of Salary, balance etc
  QualifyingCriteria action_details = 1;
}

// qualifying criteria for a tier to satisfy
message QualifyingCriteria {
  oneof criteria {
    Salary salary = 1;
  }
}

// Salary Qualifying Criterion
message Salary {
  // minimum amount of salary (inclusive) needed to qualify
  google.type.Money min_salary = 1;
  // maximum amount of salary (exclusive) needed to qualify
  google.type.Money max_salary = 2;
  // min ratio (inclusive) of salary transfer needed to qualify
  double min_ratio = 3;
  // max ratio (exclusive) of salary transfer needed to qualify
  double max_ratio = 4;
}

enum CriteriaName {
  CRITERIA_NAME_UNSPECIFIED = 0;
  CRITERIA_NAME_C_ONE = 1;
  CRITERIA_NAME_C_TWO = 2;
  CRITERIA_NAME_C_THREE = 3;
  CRITERIA_NAME_C_FOUR = 4;
  CRITERIA_NAME_C_FIVE = 5;
  CRITERIA_NAME_C_SIX = 6;
  CRITERIA_NAME_C_SEVEN = 7;
  CRITERIA_NAME_C_EIGHT = 8;
  CRITERIA_NAME_C_NINE = 9;
  CRITERIA_NAME_C_TEN = 10;
}

// Enum to represent current status of a criteria
enum CriteriaStatus {
  CRITERIA_STATUS_UNSPECIFIED = 0;
  CRITERIA_STATUS_ACTIVE = 1;
  CRITERIA_STATUS_DEPRECATED = 2;
}

enum AaSalaryCriteriaFieldMask {
  AA_SALARY_CRITERIA_FIELD_MASK_UNSPECIFIED = 0;
  AA_SALARY_CRITERIA_FIELD_MASK_ID = 1;
  AA_SALARY_CRITERIA_FIELD_MASK_CRITERIA_NAME = 2;
  AA_SALARY_CRITERIA_FIELD_MASK_DETAILS = 3;
  AA_SALARY_CRITERIA_FIELD_MASK_STATUS = 4;
  AA_SALARY_CRITERIA_FIELD_MASK_EXECUTED_AT = 5;
  AA_SALARY_CRITERIA_FIELD_MASK_CREATED_AT = 6;
  AA_SALARY_CRITERIA_FIELD_MASK_UPDATED_AT = 7;
}
