syntax = "proto3";

package bigdata.categorizer;

import "api/accounts/account_type.proto";
import "api/order/order.proto";
import "api/order/payment/accounting_entry_type.proto";
import "api/order/payment/payment_protocol.proto";
import "api/order/workflow.proto";
import "api/payment_instruments/payment_instrument.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/actor.proto";
import "api/typesv2/bank.proto";
import "api/typesv2/common/name.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/bigdata/categorizer";
option java_package = "com.github.epifi.gamma.api.bigdata.categorizer";

// Since this event is published for a Spark job, we have defined the event in the bigdata package.
// We will be moving the event definition to the consumer_service.proto to separate it from the other events.
message BatchOrderDetailsEvent {
  queue.ConsumerRequestHeader request_header = 1;
  // List of orders to categorize
  repeated OrderDetail order_details = 2 [(validate.rules).repeated.max_items = 1000];
}

message OrderDetail {
  string order_id = 1;
  // from actor id for the given order
  ActorDetail from_actor = 2;
  // to_actor_id for the given order
  ActorDetail to_actor = 3;
  // provenance is the source of order.
  order.OrderProvenance provenance = 4;
  // workflow tells how the order has to be treated.
  order.OrderWorkflow workflow = 5;
  // source for transaction id is orders_transactions_map table.
  repeated TransactionDetail transaction_details = 6;
  // order tags gives some information on the type of transaction.
  // A sample value is: "{\"Tags\": [\"INTEREST\"]}"
  // We are passing the tags column from data lake as it is, instead of parsing there. It is easier to unmarshal it in the consumer than in spark.
  string tags = 7;
}

message ActorDetail {
  reserved 4;
  // actor id
  string id = 1;
  // actor type such as USER, EXTERNAL_MERCHANT etc.
  api.typesv2.ActorType type = 2;
  // Actor name. For merchant the name is fetched from merchants table, for user it is fetched from users table
  string name = 3;
  // kycName will be populated for fi users.
  api.typesv2.common.Name kyc_name = 5;
}

message TransactionDetail {
  reserved 11;
  string transaction_id = 1;
  // amount transferred in the transaction
  float computed_amount = 2;
  // The source through which transaction is done
  vendorgateway.Vendor bank_name = 3;
  // source is trans_remarks field from transactions table in epifi DB.
  string transaction_remarks = 4;
  // Pi details of the receiver
  PIDetails to_pi = 5;
  // PI details of the sender
  PIDetails from_pi = 6;
  // Location token is used to get the location where the transaction was made.
  string location_token = 7;
  // The time at which the transaction was made
  google.protobuf.Timestamp created_at = 8;
  // we will fetch gplace categories only when enable_gplace flag if set to true
  bool enable_gplace = 9;
  // payment protocol refers to how the transaction was processed eg. UPI, NEFT etc.
  order.payment.PaymentProtocol payment_protocol = 10;
  // notifications received for a txn from debit perspective
  string particulars_debit = 12;
  // notifications received for a txn from credit perspective
  string particulars_credit = 13;
}

message PIDetails {
  reserved 8;
  string pi_id = 1;
  // Name of whom the pi belongs to.
  string pi_name = 2;
  // The type of pi i.e UPI,
  paymentinstrument.PaymentInstrumentType pi_type = 3;
  // source is computed_unique_vpa field from payment_instruments table in epifi DB.
  string vpa = 4;
  // source is mcc field in vpa_merchant_infos table
  string vpa_mcc = 5;
  // source is sub_code field in vpa_merchant_infos table
  string vpa_subcode = 6;
  // source is mcc field in card_merchant_infos table
  string card_mcc = 7;
  // Type of the deposit account. Can be FD, RD, or SD.
  accounts.Type account_type = 9;
}

message BatchAATransactionDetailsEvent {
  queue.ConsumerRequestHeader request_header = 1;
  repeated AATransactionDetails aa_txn_details = 2;
}

message AATransactionDetails {
  // unique identifier of the transaction
  string id = 1;
  // This is the connected account bank name. The transaction belong to this bank.
  api.typesv2.Bank bank = 2;
  // Transaction remarks
  string remarks = 3;
  // It tells if a txn was credit type of debit type type for the user whose connected the account
  order.payment.AccountingEntryType accounting_entry = 4;
  // Details of sender actor
  ActorDetail from_actor = 5;
  // Details of receiver actor
  ActorDetail to_actor = 6;
  // PI details of the sender
  PIDetails from_pi = 7;
  // Pi details of the receiver.
  PIDetails to_pi = 8;
  // payment protocol refers to how the transaction was processed eg. UPI, NEFT etc.
  order.payment.PaymentProtocol payment_protocol = 9;
  // keywords fetched by smart parser from the remarks of the transaction
  string keyword = 10;
  // particulars received in a notification
  string particulars = 11;
}
