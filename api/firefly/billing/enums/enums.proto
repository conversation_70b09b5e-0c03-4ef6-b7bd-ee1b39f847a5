syntax = "proto3";

package firefly.billing.enums;

option go_package = "github.com/epifi/gamma/api/firefly/billing/enums";
option java_package = "com.github.epifi.gamma.api.firefly.billing.enums";

enum CreditCardBillFieldMask {
  CREDIT_CARD_BILL_FIELD_MASK_UNSPECIFIED = 0;
  CREDIT_CARD_BILL_FIELD_MASK_ID = 1;
  CREDIT_CARD_BILL_FIELD_MASK_ACTOR_ID = 2;
  CREDIT_CARD_BILL_FIELD_MASK_ACCOUNT_ID = 3;
  CREDIT_CARD_BILL_FIELD_MASK_LAST_STATEMENT_BALANCE = 4;
  CREDIT_CARD_BILL_FIELD_MASK_CURRENT_STATEMENT_AMOUNT = 5;
  CREDIT_CARD_BILL_FIELD_MASK_TOTAL_CREDIT = 6;
  CREDIT_CARD_BILL_FIELD_MASK_TOTAL_DEBIT = 7;
  CREDIT_CARD_BILL_FIELD_MASK_CASH = 8;
  CREDIT_CARD_BILL_FIELD_MASK_PURCHASE = 9;
  CREDIT_CARD_BILL_FIELD_MASK_MIN_DUE = 10;
  CREDIT_CARD_BILL_FIELD_MASK_TOTAL_DUE = 11;
  CREDIT_CARD_BILL_FIELD_MASK_STATEMENT_DATE = 12;
  CREDIT_CARD_BILL_FIELD_MASK_SOFT_DUE_DATE = 13;
  CREDIT_CARD_BILL_FIELD_MASK_HARD_DUE_DATE = 14;
  CREDIT_CARD_BILL_FIELD_MASK_REWARDS_INFO = 15;
  CREDIT_CARD_BILL_FIELD_MASK_ANALYTICS_INFO = 16;
  CREDIT_CARD_BILL_FIELD_MASK_CREATED_AT = 17;
  CREDIT_CARD_BILL_FIELD_MASK_UPDATED_AT = 18;
  CREDIT_CARD_BILL_FIELD_MASK_DELETED_AT = 19;
  CREDIT_CARD_BILL_FIELD_MASK_STATEMENT_SUMMARY = 20;
  CREDIT_CARD_BILL_FIELD_MASK_AVAILABLE_LIMIT = 21;
  CREDIT_CARD_BILL_FIELD_MASK_S3_PATH = 22;
  CREDIT_CARD_BILL_FIELD_MASK_REWARD_ID = 23;
}

enum CreditCardPaymentInfoFieldMask {
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_UNSPECIFIED = 0;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_ID = 1;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_BILL_INFO_ID = 2;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_EXTERNAL_TXN_ID = 3;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_ORDER_ID = 4;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_PAYMENT_DATE = 5;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_AMOUNT = 6;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_STATUS = 7;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_SUB_STATUS = 8;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_CREATED_AT = 9;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_UPDATED_AT = 10;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_DELETED_AT = 11;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_VENDOR_TXN_REF_NO = 12;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_ACCOUNT_ID = 13;
  CREDIT_CARD_PAYMENT_INFO_FIELD_MASK_REPAYMENT_TYPE = 14;
}


enum CreditCardStatementNotificationType {
  CREDIT_CARD_STATEMENT_NOTIFICATION_TYPE_UNSPECIFIED = 0;
  CREDIT_CARD_STATEMENT_NOTIFICATION_TYPE_STATEMENT_GENERATED = 1;
}

enum MerchantRewardType {
  MERCHANT_REWARD_TYPE_UNSPECIFIED = 0;
  MERCHANT_REWARD_TYPE_TWO_X = 1;
  MERCHANT_REWARD_TYPE_FIVE_X = 2;
}


// Enum to determine the type of credit card repayment.
// This will help identify the type of repayment that was done
enum PaymentProvenance {
  PAYMENT_PROVENANCE_UNSPECIFIED = 0;
  // this type of repayment is initiated by the user from the fi-coin catalogue
  PAYMENT_PROVENANCE_FI_COIN_CATALOGUE = 1;
  // this type of payment will be done from the normal payment flow as a discount.
  PAYMENT_PROVENANCE_USER_INITIATED = 2;
}

enum RepaymentType {
  REPAYMENT_TYPE_UNSPECIFIED = 0;
  // type of repayment in which the amount has been transferred from the user's savings account
  REPAYMENT_TYPE_REGULAR = 1;
  // type of repayment that has been done via a redemption or reward
  REPAYMENT_TYPE_REDEMPTION = 2;
}
