//go:generate gen_sql -types=StatementSummary
syntax = "proto3";

package firefly.billing;

import "api/firefly/enums/enums.proto";
import "api/typesv2/transaction_transfer_type.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/firefly/billing";
option java_package = "com.github.epifi.gamma.api.firefly.billing";

message Statement {
  StatementUserDetail user_detail = 1;

  // date from which the statement has been fetched
  google.type.Date from_date = 2;

  // date upto which the statement has been fetched
  google.type.Date to_date = 3;

  StatementSummary summary = 4;

  repeated StatementTransaction transactions = 5;
}

message StatementSummary {
  // date on which the statement has been created
  google.type.Date statement_date = 1;
  // date on which the payment is due
  google.type.Date payment_due_date = 2;
  // amount of money available
  google.type.Money available_limit = 3;
  // total amount due
  google.type.Money total_amount_due = 4;
  // minimum amount due
  google.type.Money min_amount_due = 5;
  // Opening balance on the date from which statement begins
  google.type.Money opening_balance = 6;
  // summation of all expense transactions
  google.type.Money spends = 7;
  // interest accrued
  google.type.Money interest_charges = 8;
  // total fees
  google.type.Money fees = 9;
  // repayments and returns for the statement period
  google.type.Money repayment_and_refunds = 10;
  // spent amount converted to emi
  google.type.Money spend_converted_to_emi = 11;
}

message StatementUserDetail {
  // name of the user
  string name = 1;
  // contact number of the user
  uint64 phone_number = 2;
  // email id of the user
  string email = 3;
  // permanent address of the user
  string address = 4;
  // masked credit card number of the user
  string masked_credit_card_number = 5;
}

message StatementTransaction {
  // amount of money transacted (in INR)
  google.type.Money amount = 1;
  // date of transaction
  google.protobuf.Timestamp transaction_timestamp = 2;

  string merchant_name = 3;
  // location where the transaction happened
  string location = 4;

  firefly.enums.TransactionOrigin transaction_origin = 5;

  string payment_method = 6;

  string category = 7;

  // Type of transaction - debit or credit
  api.typesv2.TransactionTransferType transaction_transfer_type = 8;

  int32 reward_coins = 9;

  int32 reward_points = 10;
}

message FeeBreakDown {
  string title = 1;
  repeated FeeBreakDownComponents fee_break_down_components = 2;
}

message FeeBreakDownComponents {
  string fee_type = 1;
  google.type.Money amount = 2;
  // PLUS OR MINUS
  string fee_amount_type = 3;
}

message ExtraRewardsInfo {
  string text = 1;
  int32 reward_coins_earned = 2;
  string reward_type_logo = 3;
}

message EmiSummary {
  int64 number_of_active_emi = 1;
  google.type.Money due_amount = 2;
  repeated EmiDetail emi_details = 3;
}

message EmiDetail {
  string merchant_name = 1;
  int64 installment_number = 2;
  int64 total_installments = 3;
  google.type.Money amount = 4;
}
