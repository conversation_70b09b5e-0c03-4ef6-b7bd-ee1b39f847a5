syntax = "proto3";

package firefly.cx;

import "api/firefly/accounting/internal/card_transaction.proto";
import "api/firefly/cx/enum/enums.proto";
import "api/firefly/enums/enums.proto";
import "api/firefly/service.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/firefly/cx";
option java_package = "com.github.epifi.gamma.api.firefly.cx";

service Cx {
  // RPC to update card request status
  rpc UpdateCardRequestStatus (UpdateCardRequestStatusRequest) returns (UpdateCardRequestStatusResponse);
  // RPC to fetch credit limit for an actor
  rpc GetCreditLimit (GetCreditLimitRequest) returns (GetCreditLimitResponse);
  // RPC to fetch outstanding dues for an actor
  rpc GetOutstandingDues (GetOutstandingDuesRequest) returns (GetOutstandingDuesResponse);
  // A generic rpc, will be used for freeze/unfreeze card, reissue card
  rpc InitiateCardAction (InitiateCardActionRequest) returns (InitiateCardActionResponse);
  // RPC to fetch transaction details for an actor
  rpc GetTransactionDetails (GetTransactionDetailsRequest) returns (GetTransactionDetailsResponse);
  // RPC to fetch card action status
  rpc GetCardActionStatus (GetCardActionStatusRequest) returns (GetCardActionStatusResponse);

  // RPC to toggle card control details. Only restrictive/disablement actions are allowed via this RPC
  rpc ChangeCardControlDetails (ChangeCardControlDetailsRequest) returns (ChangeCardControlDetailsResponse);

  // RPC to permanently block the card of a user
  rpc BlockCard (BlockCardRequest) returns (BlockCardResponse);

  // RPC to update free card replacement counter for a user
  rpc UpdateFreeCardReplacement (UpdateFreeCardReplacementRequest) returns (UpdateFreeCardReplacementResponse);

  // RPC to update card request stage status.
  rpc UpdateCardRequestStageStatus (UpdateCardRequestStageStatusRequest) returns (UpdateCardRequestStageStatusResponse);
}

message UpdateFreeCardReplacementRequest {
  string actor_id = 1;
  firefly.enums.CardSKUType card_sku_type = 2;
}

message UpdateFreeCardReplacementResponse {
  rpc.Status status = 1;
}

message ChangeCardControlDetailsRequest {
  string actor_id = 1;
  ControlsChangeData change_data = 2;
}

message ChangeCardControlDetailsResponse {
  rpc.Status status = 1;
}

message BlockCardRequest {
  string actor_id = 1;
  string reason = 2;
}

message BlockCardResponse {
  rpc.Status status = 1;
}

message UpdateCardRequestStatusRequest {
  string actor_id = 1;
  string credit_card_id = 2;
  firefly.enums.CardRequestWorkFlow card_request_work_flow = 3;
  firefly.enums.CardRequestStatus updated_card_request_status = 4;
  string card_request_id = 5;
}

message UpdateCardRequestStatusResponse {
  rpc.Status status = 1;
}

message GetCreditLimitRequest {
  string actor_id = 1;
}

message GetCreditLimitResponse {
  rpc.Status status = 1;
  // current available credit limit on the card
  float available_limit = 2;
  // limit utilized by the cardholder
  float limit_utilized = 3;
}

message GetOutstandingDuesRequest {
  string actor_id = 1;
}

message GetOutstandingDuesResponse {
  rpc.Status status = 1;
  // current total outstanding dues of the card
  double total_outstanding_dues = 2;
  google.type.Date due_date = 3;
}

message GetTransactionDetailsRequest {
  string actor_id = 1;
  google.protobuf.Timestamp from_time = 2;
  google.protobuf.Timestamp to_time = 3;
}

message GetTransactionDetailsResponse {
  rpc.Status status = 1;
  repeated accounting.CardTransaction transactions = 2;
}

message GetCardActionStatusRequest {
  string card_request_id = 1;
}

message GetCardActionStatusResponse {
  rpc.Status status = 1;
  firefly.enums.CardRequestStatus card_request_status = 2;
}

message InitiateCardActionRequest {
  string actor_id = 1;
  firefly.cx.enum.CardActionType card_action_type = 2;

  oneof RequestPayload {
    FreezeUnfreezeCardActionPayload freeze_unfreeze_card_action_payload = 3;
    ReissueCardActionPayload reissue_card_action_payload = 4;
    DisputeCardActionPayload dispute_card_action_payload = 5;
  }
}

message InitiateCardActionResponse {
  rpc.Status status = 1;
  string card_request_id = 2;
}

message FreezeUnfreezeCardActionPayload {
  // freeze/unfreeze request
  firefly.enums.CardRequestType request_type = 1;
  // reason for freeze/unfreeze card
  string reason = 2;
}

message ReissueCardActionPayload {
  // reason for reissue
  string reason = 1;
}

message DisputeCardActionPayload {
  string txn_id = 1;
  google.type.Money amount = 2;
  string reason = 3;
  string description = 4;
  firefly.enums.DisputeType dispute_type = 5;
  string url = 6;
}

message UpdateCardRequestStageStatusRequest {
  string card_request_stage_id = 1;
  firefly.enums.CardRequestStageStatus updated_card_request_stage_status = 2;
}

message UpdateCardRequestStageStatusResponse {
  rpc.Status status = 1;
}
