syntax = "proto3";

package firefly.pinot;

import "api/categorizer/enums.proto";
import "api/firefly/accounting/enums/enums.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/firefly/pinot";
option java_package = "com.github.epifi.gamma.api.firefly.pinot";

service TxnAggregates {
  // GetTransactionAggregates returns transaction aggregates: aggregates amount and aggregates count
  // mandatory fields: actorId and time range: executed_at (from and to).
  rpc GetTransactionAggregates (GetTransactionAggregatesRequest) returns (GetTransactionAggregatesResponse);

  // GetRefundTransactionAggregates returns transaction aggregates of refund/reversal transactions: aggregates amount and aggregates count
  // mandatory fields: actorId and time range: parent_executed_at (from and to).
  rpc GetRefundTransactionAggregates (GetRefundTransactionAggregatesRequest) returns (GetRefundTransactionAggregatesResponse);

  // GetTopCategories generates aggregates grouped by transaction category.
  // Client needs to pass the time range and user's credit card ids for which we need to calculate the aggregates.
  // Client also needs to pass the list of aggregation metric required in the request.
  rpc GetTopCategories (GetTopCategoriesRequest) returns (GetTopCategoriesResponse) {}
}

message GetTransactionAggregatesRequest {
  // mandatory: actorId of the user
  string actor_id = 1;
  // optional: internal credit card id
  string credit_card_id = 2;
  // mandatory: start time of the time range
  google.protobuf.Timestamp from_executed_time = 3;
  // mandatory: end time of the time range
  google.protobuf.Timestamp to_executed_time = 4;
  // optional: status of the transaction
  repeated accounting.enums.TransactionStatus transaction_status = 5;
  // optional: type of the transaction (credit/debit)
  accounting.enums.TransactionType transaction_type = 6;
  // optional: internal credit account id
  string credit_account_id = 7;
  // optional: ds ontology ids to be excluded from the aggregate
  // if a ds_ontology_id is present in both exclude and include filter,
  // exclude filter will take priority
  repeated string exclude_ds_ontology_ids = 8;
  // optional: ds ontology ids to be included for the aggregate
  // if a ds_ontology_id is present in both exclude and include filter,
  // exclude filter will take priority
  repeated string include_ds_ontology_ids = 9;
}

message GetTransactionAggregatesResponse {
  rpc.Status status = 1;
  // aggregates for transaction
  TransactionAggregates transaction_aggregates = 2;
}

message GetRefundTransactionAggregatesRequest {
  // actorId of the user
  string actor_id = 1;
  // internal credit card id
  string credit_card_id = 2;
  // start time of the time range
  google.protobuf.Timestamp from_executed_time = 3;
  // end time of the time range
  google.protobuf.Timestamp to_executed_time = 4;
  // start time of parent transaction
  google.protobuf.Timestamp from_parent_executed_time = 5;
  // end time of parent transaction
  google.protobuf.Timestamp to_parent_executed_time = 6;
  // internal credit account id
  string credit_account_id = 7;
  // optional: parent ds ontology ids to be excluded from the aggregate
  repeated string exclude_parent_ds_ontology_ids = 8;
  // optional: status of the transaction
  repeated accounting.enums.TransactionStatus transaction_status = 9;
}

message GetRefundTransactionAggregatesResponse {
  rpc.Status status = 1;
  // aggregates for transaction
  TransactionAggregates transaction_aggregates = 2;
}

// TransactionAggregates contains aggregates like amount and count
message TransactionAggregates {
  // aggregates amount based on filters
  google.type.Money amount = 1;
  // Count of records under filters
  int64 count = 2;
}

message GetTopCategoriesRequest {
  // actorId of the user
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // start time of the time range
  google.protobuf.Timestamp from_time = 2;
  // end time of the time range
  google.protobuf.Timestamp to_time = 3;
  // credit cards of the user which are to be included in aggregate calculation.
  repeated string credit_card_ids = 4;
  // L0 of categories (optional)
  // if not specified, no l0 restriction is applied on category aggs
  repeated categorizer.L0 category_l0s = 6;
  // type of the transaction (credit/debit)
  accounting.enums.TransactionType transaction_type = 7;
}

message GetTopCategoriesResponse {
  rpc.Status status = 1;
  // aggregates for categories
  repeated CategoryAggregate category_aggregates = 2;
}

message CategoryAggregate {
  // display category enum as string - category dimension
  string category = 1;
  // L0 of category
  categorizer.L0 category_l0 = 2;
  // type of the transaction (credit/debit)
  accounting.enums.TransactionType transaction_type = 3;
  // total amount spent on category i.e., sum(amount)
  google.type.Money sum_amount = 4;
  // total number of orders
  int64 count = 5;
}
