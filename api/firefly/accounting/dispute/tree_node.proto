syntax = "proto3";

package firefly.accounting.dispute;


option go_package = "github.com/epifi/gamma/api/firefly/accounting/dispute";
option java_package = "com.github.epifi.gamma.api.firefly.accounting.dispute";


message TreeNode {
  QuestionMeta question_meta = 1;
  repeated TreeNode child_nodes = 2;
}

message QuestionMeta {
  string question_code = 1;
  string actual_question = 2;
  repeated string answer_options = 3;
  AnswerDataType answer_data_type = 4;
  // indicates if the user can skip the question
  bool is_optional = 5;
  string placeholder_text = 6;
}

enum AnswerDataType {
  ANSWER_DATA_TYPE_UNSPECIFIED = 0;
  ANSWER_DATA_TYPE_DROPDOWN = 1;
  ANSWER_DATA_TYPE_TEXT = 2;
  ANSWER_DATA_TYPE_AMOUNT = 3;
  ANSWER_DATA_TYPE_DATE = 4;
  ANSWER_DATA_TYPE_NUMBER = 5;
  ANSWER_DATA_TYPE_NO_INPUT = 6;
}
