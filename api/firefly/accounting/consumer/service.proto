//go:generate gen_queue_pb
syntax = "proto3";

package firefly.accounting.consumer;

import "api/firefly/accounting/enums/enums.proto";
import "api/firefly/accounting/internal/card_transaction.proto";
import "api/firefly/accounting/internal/transaction_additional_info.proto";
import "api/firefly/accounting/notification/payload.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/firefly.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/firefly/accounting/consumer";
option java_package = "com.github.epifi.gamma.api.firefly.accounting.consumer";

service Consumer {
  // ProcessCardTransactionNotification for processing card transaction notifications
  // we will get both successful and failure transaction notifications here, we will parse these notification and
  // store the txns in database.
  rpc ProcessCardTransactionNotification (ProcessCardTransactionNotificationRequest) returns (ProcessCardTransactionNotificationResponse) {}

  // ProcessAcsNotification for processing credit card acs notifications
  // we will get both successful and failure acs notifications here, we will parse these notification and
  // store in s3 in group of daily notifications csv file.
  rpc ProcessAcsNotification (ProcessAcsNotificationRequest) returns (ProcessAcsNotificationResponse);
}

message ProcessCardTransactionNotificationRequest {
  // A set of all the common attributes to be contained in a consumer response
  queue.ConsumerRequestHeader request_header = 1;

  // unique account identifier for a credit account
  string entity_id = 2;

  // unique transaction identifier
  // we will use it for deduping the card transaction in case of duplicate notifications
  string transaction_reference_number = 3;

  // merchant information
  firefly.accounting.notification.MerchantDetails merchant_details = 4;

  // transaction timestamp
  google.protobuf.Timestamp transaction_timestamp = 5;

  // transaction status
  firefly.accounting.enums.TransactionStatus txn_status = 6;

  // transaction amount
  google.type.Money txn_amount = 7;

  // origin of the txn
  firefly.accounting.enums.TransactionOrigin txn_origin = 8;

  string acquirer_id = 9;

  // transaction description if available or transaction failure reason in case of failure
  string transaction_description = 10;

  // vendor card identifier
  string vendor_card_identifier = 11;

  firefly.accounting.enums.TransactionCategory transaction_category = 12;

  firefly.accounting.enums.TransactionFailureType failure_type = 13;

  firefly.accounting.enums.TransactionType transaction_type = 14;

  string external_txn_id = 15;

  string auth_code = 16;

  string retrieval_reference_no = 17;

  // original txn currency in which txn was done
  string original_txn_currency = 18;
}



message ProcessCardTransactionNotificationResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

// Credit card transaction event which will be published when we receive any txn event from vendor
// This can be consumed by the downstream services which needs to perform any action on CC txn.
// Example : Insights, rewards etc
message CreditCardTransactionEvent {
  // A set of all the common attributes to be contained in a consumer response
  queue.ConsumerRequestHeader request_header = 1;
  // raw cc txn
  firefly.accounting.CardTransaction card_transaction = 2 [deprecated = true];
  // parsed additional info for a transaction
  firefly.accounting.TransactionAdditionalInfo transaction_additional_info = 3 [deprecated = true];
  // event payload for a transaction
  TransactionEventPayload transaction_event_payload = 4;
}

message TransactionEventPayload {
  // internal txn id
  string id = 1;

  // internal account id
  string account_id = 2;

  // internal card id
  string card_id = 3;

  // transaction amount
  google.type.Money amount = 4;

  // transaction time
  google.protobuf.Timestamp txn_time = 5;

  // transaction status
  accounting.enums.TransactionStatus txn_status = 6;

  // transaction category
  accounting.enums.TransactionCategory txn_category = 7;

  // origin of the txn
  // it can be either atm machine, pos, mobile
  accounting.enums.TransactionOrigin txn_origin = 8;

  // credit/debit
  accounting.enums.TransactionType txn_type = 9;

  // details of the beneficiary
  accounting.BeneficiaryInfo beneficiary_info = 10;

  // description of the transaction
  string description = 11;

  // external transaction identifier
  string external_txn_id = 12;

  string retrieval_reference_no = 13;

  // parent transaction id will be populated in case of a reversal, refund or a fees/tax transaction for an existing transaction
  // for which refund/reversal has been done or on which fees has been applied
  string parent_transaction_id = 14;

  // child transaction id's will be populated in case of refund, reversal or fees for the original transaction
  // This will contain transaction id's of all the refunds/reversal/fees child txns assuming there can be more than
  // 1 refund/reversal for a txn
  // Downstream services can get more details for each of these child transactions id like their categories by using
  // the GetTransaction rpc
  repeated string child_transaction_ids = 15;

  // payment instrument id to which payment is made to
  string pi_to = 16;

  // payment instrument id to which payment is made from
  string pi_from = 17;

  // actor from which payment was made
  string actor_from = 18;

  // actor to which payment was made
  string actor_to = 19;

  // enriched beneficiary info containing resolved beneficiary name
  firefly.accounting.EnrichedBeneficiaryInfo enriched_beneficiary_info = 21;

  // original currency code for the transaction
  string original_currency_code = 22;

  // card program associated with the account for which the transaction event is being published
  api.typesv2.CardProgram card_program = 23;
}

message ProcessAcsNotificationRequest {
  // A set of all the common attributes to be contained in a consumer response
  queue.ConsumerRequestHeader request_header = 1;

  // acs notification payload
  firefly.accounting.notification.AcsNotificationPayload acs_notification_payload = 2;
}

message ProcessAcsNotificationResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

