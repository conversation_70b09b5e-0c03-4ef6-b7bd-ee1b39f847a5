// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=BeneficiaryInfo,DisputeInfo,ConversionInfo,FailureInfo
syntax = "proto3";

package firefly.accounting;

import "api/firefly/accounting/enums/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/firefly/accounting";
option java_package = "com.github.epifi.gamma.api.firefly.accounting";

// CardTransaction proto for storing details for a credit/debit card transaction
message CardTransaction {
  string id = 1;

  // internal account id
  string account_id = 2;

  // internal card id
  string card_id = 3;

  // transaction amount
  google.type.Money amount = 4;

  // remaining balance
  google.type.Money balance = 5;

  // transaction time
  google.protobuf.Timestamp txn_time = 6;

  // transaction status
  accounting.enums.TransactionStatus txn_status = 7;

  // transaction category
  accounting.enums.TransactionCategory txn_category = 8;

  // origin of the txn
  // it can be either atm machine, pos, mobile
  accounting.enums.TransactionOrigin txn_origin = 9;

  // credit/debit
  accounting.enums.TransactionType txn_type = 10;

  // details of the beneficiary
  accounting.BeneficiaryInfo beneficiary_info = 11;

  // details for conversion info
  accounting.ConversionInfo conversion_info = 12;

  // description of the transaction
  string description = 13;

  // details in case of dispute txn
  accounting.DisputeInfo dispute_info = 14;

  // external transaction identifier
  // TODO(priyansh) : Add more details here
  string external_txn_id = 15;

  // bill reference number
  // TODO(priyansh) : Add more details here
  string bill_ref_no = 16;

  // bank transaction id
  string bank_txn_id = 17;

  // authorisation code
  // TODO(priyansh) : Add more details here
  string auth_code = 18;

  // TODO(priyansh) : Add more details here
  string acquirer_id = 19;

  string retrieval_reference_no = 20;

  // sor transaction id
  // TODO(priyansh) : Add more details here
  string sor_txn_id = 21;

  // vendor's txn reference id
  string txn_reference_no = 22;

  // txn id for vendor's side
  string vendor_ext_txn_id = 23;

  google.protobuf.Timestamp created_at = 24;

  google.protobuf.Timestamp updated_at = 25;

  google.protobuf.Timestamp deleted_at = 26;

  // jsonb containing failure info for a txn which includes failure type for now
  firefly.accounting.FailureInfo failure_info = 27;

  // parent transaction id will be populated in case of a reversal, refund or a fees/tax transaction for an existing transaction
  // for which refund/reversal has been done or on which fees has been applied
  string parent_transaction_id = 28;

  // child transaction id's will be populated in case of refund, reversal or fees for the original transaction
  // This will contain transaction id's of all the refunds/reversal/fees child txns assuming there can be more than
  // 1 refund/reversal for a txn
  repeated string child_transaction_ids = 29;

  // unique transaction reference to uniquely identify a transaction. It can be a combination of multiple parameters
  // received from a transaction.
  // We will transform the DedupeId message to sha 256 hash after doing proto.Marshal and keep this into our db. It will
  // be considered as unique identifier for de-duping transactions.
  string dedupe_id = 30;

  // transaction authorization status for a transaction
  // Transaction initially remains in Authorized state and move to settled/unsettled/reversed based on VISA's or NFS
  // settlement process.
  // Note : Only settled txns are considered for ledger management at M2P's end
  firefly.accounting.enums.TransactionAuthorizationStatus transaction_authorization_status = 31;
}

message FailureInfo {
  firefly.accounting.enums.TransactionFailureType failure_type = 1;
}

message BeneficiaryInfo {
  string beneficiary_name = 1;

  string beneficiary_type = 2;

  string beneficiary_id = 3;

  // this will be populated in case of merchant transaction
  string mcc_code = 4;
}

message ConversionInfo {
  // original txn currency in which txn was done
  string original_txn_currency = 1;
}

message DisputeInfo {
  string disputed_dto = 1;

  string dispute_ref_no = 2;
}

// We will transform this message to sha256 hash after doing proto.Marshal and keep this into our db. It will be considered as
// unique identifier for de-duping transactions.
// For a given transaction received from vendor we will do the following :
// 1. We will check if rrn exist for the txn and use RrnDedupeIdentifier if it exists
// 2. If rrn does not exist we will check for external txn id and use ExternalTxnIdDedupeIdentifier
// 3. If rrn or external txn id does not exist we will go ahead with entity id and amount as unique identifier
// Deprecated
message DedupeId {
  oneof Identifier {
    RrnDedupeIdentifier rrn_dedupe_identifier = 1;
    ExternalTxnIdDedupeIdentifier external_txn_id_dedupe_identifier = 2;
    EntityIdAndAmountIdentifier entity_id_and_amount_identifier = 3;
  }
  google.protobuf.Timestamp transaction_time = 15;
}

message RrnDedupeIdentifier {
  // retrieval reference number
  string rrn = 1;
}

message ExternalTxnIdDedupeIdentifier {
  // external txn id
  string external_txn_id = 1;
}

message EntityIdAndAmountIdentifier {
  string entity_id = 1;

  google.type.Money amount = 2;
}

// We will transform this message to sha256 hash after doing proto.Marshal and keep this into our db. It will be considered as
// unique identifier for de-duping transactions.
// For a given transaction received from vendor we will do the following :
// 1. We will check if external txn id exist for the txn and use it as the identifier
// 2. If external txn id does not exist we will check for rrn and use it as an identifier
message DedupeIdV2 {
  oneof Identifier {
    // vendor external txn id
    // external txn id will be present for all successful txns
    // NOTE : We will give priority to external txn id if present otherwise we will fallback to rrn
    string external_txn_id = 1;
    // retrieval reference number
    // rrn will be present for all failure txn
    string rrn = 2;
  }
  // entity id
  string entity_id = 3;
}
