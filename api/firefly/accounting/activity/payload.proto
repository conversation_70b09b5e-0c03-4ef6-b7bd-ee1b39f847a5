syntax = "proto3";

package firefly.accounting.activity;

import "api/celestial/activity/header.proto";
import "api/firefly/accounting/internal/card_transaction.proto";
import "api/firefly/accounting/internal/credit_account.proto";
import "api/firefly/accounting/internal/transaction_additional_info.proto";
import "api/firefly/accounting/notification/payload.proto";
import "api/firefly/internal/credit_card.proto";
import "api/typesv2/firefly.proto";
import "api/vendorgateway/lending/creditcard/service.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/firefly/accounting/activity";
option java_package = "com.github.epifi.gamma.api.firefly.accounting.activity";

// activity request for creating transaction
message CreateTransactionActivityRequest {
  firefly.accounting.notification.CardTransactionNotificationPayload card_transaction_notification_payload = 1;
}

message CreateTransactionActivityResponse {
  firefly.CreditCard card = 1;
  firefly.accounting.CardTransaction transaction = 2;
  firefly.accounting.CreditAccount account = 3;
}

// activity for resolving merchant name
message ResolveMerchantNameActivityRequest {
  firefly.accounting.CardTransaction transaction = 1;
}

message ResolveMerchantNameActivityResponse {
  string resolved_merchant_name = 1;
}

message CreateTxnAdditionalInfoActivityRequest {
  firefly.CreditCard card = 1;
  firefly.accounting.CardTransaction transaction = 2;
  string resolved_merchant_name = 3;
  CreditAccount credit_account = 4;
}

message CreateTxnAdditionalInfoActivityResponse {
  firefly.accounting.TransactionAdditionalInfo transaction_additional_info = 1;
}

message UpdateAccountLimitFromVendorActivityRequest {
  firefly.accounting.CreditAccount account = 1;
}

message UpdateAccountLimitFromVendorActivityResponse {}

message PopulateParentAndChildTxnIdActivityRequest {
  firefly.accounting.CardTransaction transaction = 1;
}

message PopulateParentAndChildTxnIdActivityResponse {
  // updated txn
  firefly.accounting.CardTransaction updated_transaction = 1;
}

message TransactionEventPublishActivityRequest {
  firefly.accounting.CardTransaction transaction = 1;
  firefly.accounting.TransactionAdditionalInfo transaction_additional_info = 2;
  api.typesv2.CardProgram card_program = 3;
}

message TransactionEventPublishActivityResponse {}

message ProcessRepaymentPaymentInfoActivityRequest {
  firefly.accounting.CardTransaction transaction = 1;
}

message ProcessRepaymentPaymentInfoActivityResponse {}


message FetchTxnFromVendorActivityRequest {
  string credit_account_id = 1;
}

message FetchTxnFromVendorActivityResponse {
  repeated vendorgateway.lending.creditcard.Transaction vendor_txns_list = 1;

  string entity_id = 2;
}

message ReconcileAndUpdateTxnStatusActivityRequest {
  repeated vendorgateway.lending.creditcard.Transaction vendor_txns_list = 1;

  string entity_id = 2;
}

message ReconcileAndUpdateTxnStatusActivityResponse {}

message FetchAndUpdateDuesActivityRequest {
  string entity_id = 1;

  string credit_account_id = 2;
}

message FetchAndUpdateDuesActivityResponse {}

// activity request for fetching accounts for which reconciliation needs to be triggered
message FetchReconcileAccountsActivityRequest {
  // number of accounts which needs to be fetched
  int64 limit = 1;
  oneof Identifier {
    // boolean to determine if latest accounts with the given limit needs to be fetched
    bool latest = 2;
    // time post which accounts need to be fetched
    google.protobuf.Timestamp start_time = 3;
  }
}

message FetchReconcileAccountsActivityResponse {
  // list of credit account id's
  repeated string credit_account_ids = 1;

  // time post which next accounts need to be fetched
  // if empty then no more accounts have to be fetched
  google.protobuf.Timestamp start_time = 2;
}

message CheckWelcomeOfferEligibilityAndClaimRequest {
  string actor_id = 1;
  accounting.CardTransaction transaction = 2;
  celestial.activity.RequestHeader request_header = 3;
}

message CheckWelcomeOfferEligibilityAndClaimResponse {
  celestial.activity.ResponseHeader response_header = 1;
}

// Request message for validating and triggering a fee waiver request for a card transaction.
message ValidateAndTriggerFeeWaiverRequest {
  // Details of the credit card associated with the transaction.
  firefly.CreditCard card = 1;

  // Details of the card transaction for which fee waiver is requested.
  firefly.accounting.CardTransaction transaction = 2;

  // Details of the credit account associated with the transaction.
  firefly.accounting.CreditAccount account = 3;
}

// Response message for ValidateAndTriggerFeeWaiverRequest.
message ValidateAndTriggerFeeWaiverResponse {
  // Add any response details here if needed.
}

// Validates and registers a fee waiver fee reversal transaction request.
message RecordFeeReversalRequest {
  // Identifier of the actor initiating the fee waiver reversal transaction.
  string actor_id = 1;

  // Details of the original fee transaction to be reversed.
  accounting.CardTransaction transaction = 2;

  // Details of the credit account associated with the transaction.
  firefly.accounting.CreditAccount account = 3;
}

// Response message for RecordFeeReversalRequest.
message RecordFeeReversalResponse {
}

// Checks eligibility for fee waiver offer and claims it if eligible for a card transaction.
message CheckAndClaimFeeWaiverOfferRequest {
  // Identifier of the actor initiating the fee waiver offer check and claim.
  string actor_id = 1;

  // Details of the card transaction for which fee waiver offer eligibility is checked.
  accounting.CardTransaction transaction = 2;

  // Details of the credit account associated with the transaction.
  firefly.accounting.CreditAccount account = 3;
}

// Response message for CheckAndClaimFeeWaiverOfferRequest.
message CheckAndClaimFeeWaiverOfferResponse {
}

message FetchAndUpdateCardStatusActivityRequest {
  string entity_id = 1;
  string credit_account_id = 2;
}
message FetchAndUpdateCardStatusActivityResponse {
  repeated firefly.CreditCard credit_cards = 1;
}
