syntax = "proto3";

package firefly;

import "api/firefly/billing/enums/enums.proto";
import "api/firefly/enums/enums.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/address.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/deposit.proto";
import "api/typesv2/device_unlock.proto";
import "api/typesv2/firefly.proto";
import "api/typesv2/common/name.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "google/type/postal_address.proto";

option go_package = "github.com/epifi/gamma/api/firefly";
option java_package = "com.github.epifi.gamma.api.firefly";

//go:generate gen_sql -types=CardRequestDetails
// Message to keep track of the card requests
message CardRequest {
  // primary key to identify a card request
  string id = 1;
  // id for the card for which request is getting formed
  string card_id = 2;
  // primary identifier to the actor table, card is being provisioned for this actor
  string actor_id = 3;
  // Orchestration identifier which has started this execution
  string orchestration_id = 4;
  // vendor handling the request
  vendorgateway.Vendor vendor = 5;
  // Metadata for a given request. This might contain the error reason and codes received from the vendor for a request.
  firefly.CardRequestDetails request_details = 6;
  // Enum denoting the type of request
  enums.CardRequestWorkFlow workflow = 7;
  // Status of the request
  enums.CardRequestStatus status = 8;

  // Deeplink to redirect to the next screen
  frontend.deeplink.Deeplink next_action = 9;

  // enum denoting entry point for the request, APP/SHERLOCK etc
  enums.Provenance provenance = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  google.protobuf.Timestamp deleted_at = 13;

  // identifier used by external vendor for a given onboarding request
  // ex: paisabaazar uses this to get the onboarding status of the user they are trying to onboard via DSA.
  string external_vendor_id = 14;
}

message CardRequestDetails {
  // user's address type where card needs to be delivered
  api.typesv2.AddressType address_type = 1;
  // card request type to identify the type of request for a given workflow, as a single workflow can be shared by
  // multiple requests.
  firefly.enums.CardRequestType card_request_type = 2;

  // reason for the request from user, we will collect this reason from user
  string reason = 3;

  // date on which the monthly bill is generated for the card
  int64 bill_gen_date = 4;

  // bill payment due date
  int64 payment_due_date = 5;

  // one of containing request details based on the workflow type
  oneof Data {
    firefly.ControlChangeRequestDetails control_change_request_details = 6;

    firefly.LimitChangeRequestDetails limit_change_request_details = 7;

    firefly.ReissueRequestDetails reissue_request_details = 8;

    firefly.DisputeRequestDetails dispute_request_details = 9;

    firefly.PaymentRequestDetails payment_request_details = 13;

    firefly.CardTrackingRequestDetails card_tracking_request_details = 14;

    firefly.FreezeUnfreezeDetails freeze_unfreeze_details = 15;

    firefly.CardOnboardingDetails card_onboarding_details = 16;

    firefly.ExportStatementDetails export_statement_details = 17;

    firefly.CardActivationDetails card_activation_details = 22;

    firefly.RealTimeProfileValidationCheckDetails real_time_profile_validation_check_details = 23;

    firefly.AutoRepaymentRequestDetails auto_payment_request_details = 24;

    firefly.RealtimeEligibilityDetails realtime_eligibility_details = 26;
    // details to be populated in card request details in case of user communication workflow
    firefly.UserCommunicationDetails user_communication_details = 27;
    // Field to store fee waiver request details.
    firefly.RenewalFeeWaiverRequestDetails renewal_fee_waiver_request_details = 28;
  }
  // auth required for a workflow request
  firefly.enums.CardAuthFlow card_auth_flow = 10;
  api.typesv2.DeviceUnlockMechanism device_unlock_mechanism = 11;
  api.typesv2.DeviceUnlockMechanismStrength device_unlock_mechanism_strength = 12;
  enums.CardMaterial card_material = 18;
  string selected_reward_id = 19;
  string selected_reward_option_id = 20;
  enums.CCRewardType selected_reward_type = 21;
  api.typesv2.CardProgram card_program = 25;
}

// Message to encapsulate details of a fee waiver request.
message RenewalFeeWaiverRequestDetails {
  // Type of fee waiver being requested.
  enums.FeeWaiverType renewal_fee_waiver_type = 1;

  // Indicates whether the fee waiver is triggered or not.
  bool is_fee_waiver_triggered = 2;
  // Indicates renewal fee reversal triggered
  bool is_fee_reversal_triggered = 3;
  // Indicates renewal service tax reversal triggered
  bool is_fee_service_tax_reversal_triggered = 4;
  // Indicates renewal fee reversal received
  bool is_fee_reversal_received = 5;
  // Indicates renewal service tax reversal received
  bool is_fee_service_tax_reversal_received = 6;
}



message UserCommunicationDetails {
  // it represents the type of communication for which card request has been created
  firefly.enums.CommunicationType communication_type = 1;
  // it is the time from which the comms are configured to be triggered after some delay, this will not be used in case of stage drop off comms as there can be separate reference time for each stages
  google.protobuf.Timestamp reference_time = 2;
  // this is required when we need to send workflow stage drop off comms as we need this to fetch latest card request and then current stage
  string initiator_wf_client_req_id = 3;
  // Flag used for enabling and disabling certain duration-based comms delivery,
  // such as between 10 am to 10 pm. Time range will be controlled from config.
  bool skip_trigger_time_window_validation = 4;
}


message RealTimeProfileValidationCheckDetails {
  // email id of the user for whom the validation is running .
  // storing here since the email is unverified and hence cannot be stored in the
  // user entity
  string email_id = 1;
  // credit limit that has been estimated based on the credit reports of the user
  google.type.Money estimated_credit_limit = 2;
  // client req id with which the auth has been completed. If non-empty, we can assume that
  // an auth has already been done
  string auth_client_req_id = 3;
  // name of the user. This will be used in sending comms
  api.typesv2.common.Name name = 4;
  // Type of card program for which eligibility is being checked.
  api.typesv2.CardProgramType card_program_type = 5;
  // Vendor providing the card program.
  api.typesv2.CardProgramVendor card_program_vendor = 6;
}

// contains user input and other internally fetched details for control change request
message ControlChangeRequestDetails {
  // Enable or disable International
  api.typesv2.common.BooleanEnum international = 1;
  // Enable or disable contactless
  api.typesv2.common.BooleanEnum contactless = 2;
  // Enable or disable ATM transactions
  api.typesv2.common.BooleanEnum atm = 3;
  // Enable or disable POS transactions
  api.typesv2.common.BooleanEnum pos = 4;
  // Enable or disable ECOM transactions
  api.typesv2.common.BooleanEnum ecom = 5;
  // Stores the settings that were changed. This is used for cx comms and record of what was changed
  repeated ChangedSettingsDetail changed_settings_details = 6;
  message ChangedSettingsDetail {
    firefly.enums.CardControlType card_control_type = 1;
    // New value set for the card control type
    bool new_control_value = 2;
  }
}

// contains user input and other internally fetched details for limit change request
message LimitChangeRequestDetails {
  // This is used to convey if the updated daily value is increased from previous value or not
  // If true, then auth will be done; otherwise not
  bool is_value_increase = 1;

  // control type (ATM, POS, ECOM, Contactless) for which new value is being set
  firefly.enums.CardControlType control_type = 2;

  google.type.Money updated_limit_value = 3;
  // location type for which the limit change is taking place
  firefly.enums.CardUsageLocationType card_usage_location_type = 4;
}

// contains user input and other internally fetched details for reissue card request
message ReissueRequestDetails {
  // reason for blocking the previous card
  string block_card_reason = 1;
  // address type where card needs to be delivered
  api.typesv2.AddressType address_type = 2;
  // address where card is to be delivered(populated when shipping address is chosen)
  google.type.PostalAddress shipping_address = 3;
}

message DisputeRequestDetails {
  // internal txn id
  string txn_id = 1;
  google.type.Money amount = 2;
  string reason = 3;
  string description = 4;
  firefly.enums.DisputeType dispute_type = 5;
  string url = 6;
}

message PaymentRequestDetails {
  string actor_id = 3;
  google.type.Money amount = 4;
  string card_id = 5;
  string payment_client_req_id = 6;
  // amount of money  that has to be paid without user intervention.
  // this can come as a redemption of fi-coins, etc. Something like a
  // discount
  google.type.Money discount_amount = 7;
  // account id from which the payment has to be done
  string account_id = 8;
  // type of account from which the payment has to be done
  // It can be an internal (fi savings) account or an external
  // account via UPI
  firefly.enums.PaymentAccountType payment_account_type = 9;
  // order id for the order that was created as a part of the fund transfer.
  // will be populated once the order initialisation has been successfully done
  string order_id = 10;

  // base64 encoded payer account id
  string derived_account_id = 11;
}

message AutoRepaymentRequestDetails {
  google.type.Money amount = 1;
  // client req id to poll the payment status of the fund transfer
  string payment_client_req_id = 2;
  // entry point from where the bill payment is being done .
  // It can be user initiated or automatic
  billing.enums.PaymentProvenance payment_provenance = 3;
  // order id of the order which was created for the fund transfer
  string order_id = 4;
}

message CardTrackingRequestDetails {
  string awb = 1;
  google.protobuf.Timestamp card_creation_date = 2;
  vendorgateway.Vendor courier_partner = 3;
  bool card_activated = 4;
  bool card_dispatched = 5;
  string courier_partner_name = 6;
  // Expected date of delivery
  google.protobuf.Timestamp expected_delivery_date = 7;
  // Reference id of courier partner
  string reference = 8;
  // Vendor status code of delivery
  string current_status_code = 9;
  // Date of pickup
  google.protobuf.Timestamp pickup_date = 10;
}

message FreezeUnfreezeDetails {
  enums.CardState current_card_state = 1;
  enums.CardState state_to_update = 2;
}

message CardOnboardingDetails {
  // user's address type where card needs to be delivered
  api.typesv2.AddressType address_type = 1;

  // date on which the monthly bill is generated for the card
  int64 bill_gen_date = 2;

  // bill payment due date
  int64 payment_due_date = 3;

  // Limit available for card
  google.type.Money card_limit = 4;

  string offer_id = 5;

  // Populated when user selects shipping_address for delivery
  google.type.PostalAddress shipping_address = 6;

  // contains details associated with deposit in case the onboarding is of type secured onboarding
  SecuredCardOnboardingDetails secured_card_onboarding_details = 7;

  // contains the card program used for onboarding
  api.typesv2.CardProgram card_program = 8;

  // flag contains whether to capture bill gen date on bill gen date capture stage or not
  bool skip_bill_gen_date_capture_stage = 9;
  // flag contains whether letting the user select bill gen date on consent screen/ activation screen
  bool skip_bill_gen_date_capture_on_activation_screen = 10;

  // deprecated in favour of using enable_mass_unsecured_onboarding_v2
  bool enable_magnifi_onboarding_v2 = 11 [deprecated = true];

  // Enable Magnifi v2 onboarding (for consent flow)
  bool enable_mass_unsecured_onboarding_v2 = 12;

  // Enable Unsecured v2 onboarding (for enabling onboarding through templated workflow)
  bool enable_unsecured_onboarding_v2 = 13;

  // flag contains whether amplifi consent flow onboarding enabled or not within templated workflow
  bool enable_unsecured_consent_flow_onboarding = 14;
}

message RealtimeEligibilityDetails {
  bool is_fi_savings_account_holder = 1;
  api.typesv2.CardProgram card_program = 2;
}

message CardActivationDetails {
  bool isQrCodeVerified = 1;
}

message ExportStatementDetails {
  string bill_id = 1;
}

message SecuredCardOnboardingDetails {
  google.type.Money deposit_amount = 1;
  api.typesv2.DepositNomineeDetails nominee_details = 2;
  string deposit_client_req_id = 3;
  string deposit_account_id = 4;
  api.typesv2.DepositTerm deposit_term = 5;
  // PI id of the account from which the payment is to be taken
  // place. This will be verified before any payment is done
  string pi_id = 6;
}
