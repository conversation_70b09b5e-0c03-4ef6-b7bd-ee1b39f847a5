//go:generate gen_sql -types=CreditCardOfferEligibilityCriteria,VendorResponse
syntax = "proto3";

package firefly;

import "api/firefly/enums/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/firefly";
option java_package = "com.github.epifi.gamma.api.firefly";

message CreditCardOfferEligibilityCriteria {
  string id = 1;
  string actor_id = 2;
  enums.Vendor vendor = 3;
  enums.CreditCardOfferEligibilityCriteriaStatus status = 4;
  VendorResponse vendor_response = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  google.protobuf.Timestamp deleted_at = 8;
  enums.CreditCardOfferEligibilityProvenance provenance = 9;
}

message VendorResponse {
  google.protobuf.Timestamp vendor_response_timestamp = 1;
}
