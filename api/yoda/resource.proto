//go:generate gen_sql -types=Metadata,ResourceType
syntax = "proto3";

package api.yoda;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/yoda;yodapb";
option java_package = "com.github.epifi.gamma.api.yoda";

message Resource {
  string id = 1;
  ResourceType resource_type = 2;
  // The ID of the resource in the source system ex: Google Docs ID, slack thread id
  string origin_resource_id = 3;
  google.protobuf.Timestamp last_updated_at_src = 4;
  google.protobuf.Timestamp  created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp deleted_at = 7;
  Metadata metadata = 8;
  string collection_id = 9;
}

enum ResourceType {
  RESOURCE_TYPE_UNSPECIFIED = 0;
  GOOGLE_DOC = 1;
  GITHUB_PULL_REQUEST = 2;
  SLACK_THREAD = 3;
  GO_CODE_DOC = 4;
}


message Metadata {
  oneof data {
    GoogleDoc google_doc = 1;
    GithubPullRequest github_pr = 2;
    SlackThread slack_thread = 3;
    GoCodeDoc go_code_docs = 4;
  }
}

message GoogleDoc {
  string id = 1;
  string title = 2;
  string url = 3;
  string description = 4;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

message GithubPullRequest {
  int64 number = 1;
  string title = 2;
  string url = 3;
  string author = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message SlackThread {
  string origin_id = 1;
  string sender_id = 2;
  string url = 3;
  string sender_username = 5;
  google.protobuf.Timestamp message_sent_time = 6;
  string channel_name = 7;
  string channel_id = 8;
}

message GoCodeDoc {
  string pkg_path = 1;
  string repo_name = 2;
  string package_url = 3;
}
