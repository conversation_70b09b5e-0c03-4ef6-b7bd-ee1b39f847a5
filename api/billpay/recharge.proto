//go:generate gen_sql -types=RechargeOrder,MobileRechargePlan,RechargeAccountDetails,OperatorDetails,PlanDetails,RechargeOrderStage,RechargeStageData,RechargePoolAccountPaymentStageData,RechargeFulfillmentStageData,RechargeRefundStageData,RechargeReconStageDetails,RechargeDetails
syntax = "proto3";

package api.billpay;

import "api/billpay/enums/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "api/typesv2/common/phone_number.proto";

option go_package = "github.com/epifi/gamma/api/billpay;billpaypb";
option java_package = "com.github.epifi.gamma.api.billpay";

// RechargeOrder entity
message RechargeOrder {
  string id = 1;
  string client_request_id = 2;
  string actor_id = 3;
  api.billpay.enums.RechargeAccountType account_type = 4;
  // raw phone number will be the account identifier for Mobile Recharge account type
  string account_identifier = 5;
  api.billpay.enums.Operator account_operator = 6;
  RechargeAccountDetails account_details = 8;
  PlanDetails plan_details = 7;
  api.billpay.enums.RechargeOrderStatus status = 9;
  api.billpay.enums.RechargeOrderSubStatus sub_status = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  google.protobuf.Timestamp completed_at = 13;
}

message RechargeAccountDetails {
  OperatorDetails operator_details = 1;
}

message OperatorDetails {
  // Mobile number for which operator details are fetched.
  api.typesv2.common.PhoneNumber mobile_number = 1;

  // Current operator of the mobile number.
  api.billpay.enums.Operator current_operator = 2;

  // Current location of the mobile number.
  string current_location = 3;

  // Previous operator of the mobile number.
  api.billpay.enums.Operator previous_operator = 4;

  // Previous location of the mobile number.
  string previous_location = 5;

  // Indicates if the number is ported.
  bool ported = 6;
}


// Billpay-specific plan details to avoid tight coupling with vendorgateway
message PlanDetails {
  MobileRechargePlan mobile_recharge_plan_details = 1; // for Mobile Recharge
}

// Billpay-specific mobile recharge plan (decoupled from vendorgateway.recharge.Plan)
message MobileRechargePlan {
  // Talktime available with the plan
  string talktime = 1;
  // Name of the prepaid plan
  string plan_name = 2;
  // Price of the plan
  google.type.Money amount = 3;
  // Validity period of the plan
  string validity = 4;
  // Details of the plan benefits
  string plan_description = 5;
  // Service Provider
  string service_provider = 6;
  // Operator for which this plan is applicable
  api.billpay.enums.Operator operator = 7;
}

// RechargeOrderStage entity
message RechargeOrderStage {
  string id = 1;
  string recharge_order_id = 2;
  string client_request_id = 3;
  api.billpay.enums.RechargeStage stage = 4;
  api.billpay.enums.RechargeStageStatus status = 5;
  google.protobuf.Timestamp expires_at = 6;
  RechargeStageData data = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp completed_at = 10;
}

// RechargeStageData message (to be extended with oneof fields later)
message RechargeStageData {
  oneof data {
    RechargePoolAccountPaymentStageData pool_account_payment_details = 1;
    RechargeFulfillmentStageData fulfillment_details = 2;
    RechargeRefundStageData refund_details = 3;
    RechargeReconStageDetails recon_details = 4;
  }
}

message RechargePoolAccountPaymentStageData {
  string order_id = 1;
  enums.PaymentMode payment_mode = 2;
  google.protobuf.Timestamp payment_timestamp = 3;
}

// Billpay-specific recharge details (decoupled from vendorgateway.recharge.RechargeDetails)
message RechargeDetails {
  // Mobile number associated with the transaction
  api.typesv2.common.PhoneNumber mobile_number = 1;
  // Provider for the recharged number
  api.billpay.enums.Operator provider = 2;
  // Indicates if the mobile number is postpaid
  bool is_postpaid = 3;
  // Indicates if this is a special service
  bool is_special = 4;
  // Recharge amount
  google.type.Money amount = 5;
  // Unique reference ID for the transaction from the vendor
  string transaction_ref_id = 6;
  // Status of the recharge transaction
  api.billpay.enums.RechargeOrderStatus status = 7;
  // Reference ID provided by the operator
  string operator_ref_id = 8;
}

message RechargeFulfillmentStageData {
  // recharge detail on successful fulfillment
  RechargeDetails recharge_details = 1;
  // vendor responses for the recharge process with vendor
  // ref id generated by us for initiating recharge with vendor
  string initiate_recharge_ref_id = 2;
  string initiate_recharge_trace_id = 3;
  string enquire_recharge_status_trace_id = 4;
}

message RechargeRefundStageData {
  string order_id = 1;
}

message RechargeReconStageDetails {

}

message RechargePlansData {
  repeated MobileRechargePlan plans = 1;
  RechargeAccountDetails account_details = 2;
  enums.Operator operator = 3;
}
