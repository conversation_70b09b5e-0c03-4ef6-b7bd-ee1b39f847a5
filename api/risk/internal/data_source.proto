// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package risk;

import "api/employment/employment_data.proto";
import "api/risk/enums/enums.proto";
import "api/risk/internal/redlist.proto";
import "api/risk/screener/enums.proto";
import "api/typesv2/common/boolean.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk";
option java_package = "com.github.epifi.gamma.api.risk";

message RiskData {
  string id = 1;
  string actor_id = 2;
  RiskParam risk_param = 3;
  Payload payload = 4;
  Result result = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  int64 deleted_at_unix = 8;
  float score = 9 [(validate.rules).float = {gte: 0, lte: 1}];
  // PotentialRiskFlags indicates plausible risk flags observed during the screener check
  repeated screener.PotentialRiskFlag potential_risk_flags = 10;
}

// RiskParam indicates parameter for risk calculation
enum RiskParam {
  RISK_PARAM_UNSPECIFIED = 0;
  // kyc address pin code checks if user kyc address's pincode is in block list
  RISK_PARAM_KYC_ADDRESS_PIN_CODE = 1;
  // geo location pin code checks if pincode derive from user location is in block list
  RISK_PARAM_GEO_LOCATION_PIN_CODE = 2;
  // onboarding risk detection model check if onboarding risk detection model prediction for user
  RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL = 3;
  RISK_PARAM_GEO_LOCATION_LAT_LONG = 4;
  RISK_PARAM_FINITE_CODE = 5;
  RISK_PARAM_RISKY_DEVICE = 6;
  RISK_PARAM_EKYC_ONBOARDING_NUMBER_MISMATCH = 7;
  RISK_PARAM_EMPTY_GEO_LOCATION_PIN_CODE = 8;
  RISK_PARAM_ONBOARDING_VELOCITY = 9;
  RISK_PARAM_EMAIL_ID = 10;
  RISK_PARAM_PHONE_NUMBER = 11;
  RISK_PARAM_EPFO_SCREENER = 12;
  RISK_PARAM_HUNTER = 13;
  // checks for unusual employment type and income combinations.
  // this rule was provided by the federal bank
  RISK_PARAM_INCOME_EMPLOYMENT_DISCREPANCY = 14;
  RISK_PARAM_AFU_RISK_DETECTION_MODEL = 15;
  // For users with low affluence class,
  // it will check few other user parameters ex: onboarding risk score, employment status etc and return the final verdict and score accordingly
  RISK_PARAM_AFFLUENCE_CLASS = 16;
  // Fetches already registered VPA's against a a user, checks for name mismatch across and generates score accordingly
  RISK_PARAM_UPI_VPA_NAME_MATCH = 17;
  // Risk check to evaluate if user's email is not some junk or randomly generated email
  // A lot of the risky users have random email which doesn't contain any name or vowels etc in the email us
  // will be performing few checks to figure out probability of an email being a junk email and user can be sent for manual review if confidence is high
  RISK_PARAM_JUNK_EMAIL = 18;
  RISK_PARAM_LOCATION_RISK_MODEL = 19;
  // Risk checks over installed apps in actors phone
  RISK_PARAM_INSTALLED_APPS = 20;
  // Risk checks over contact associations with fraudulent/LEA actors
  RISK_PARAM_CONTACT_ASSOCIATION = 21;
  // Check to evaluate onboarding model score of user onboarding for personal loans in fi lite flow.
  RISK_PARAM_PL_FI_LITE_ONBOARDING_RISK_DETECTION_MODEL = 22;
  // Checks based on credit score and affluence class combination
  RISK_PARAM_CREDIT_SCORE_AFFLUENCE_CLASS = 23;
  // Checks based on cross video appearance liveness & facematch checks
  RISK_PARAM_CROSS_VIDEO_LIVENESS_FACEMATCH_CHECK = 24;
  // Checks if savings account status is affected by risk related actions
  RISK_PARAM_VALIDATE_SAVINGS_ACCOUNT_STATUS = 25;
  // Checks unreviewed alerts from last 60 days against existing savings account of an actor
  RISK_PARAM_SA_UNREVIEWED_ALERTS = 26;
  // Flags the user if count of contacts is lower than a min threshold
  RISK_PARAM_LOW_CONTACT_COUNT = 27;
  // Flags the user if count of installed apps is lower than a min threshold
  RISK_PARAM_LOW_INSTALLED_APP_COUNT = 28;
  // Checks whether vpn was used while onboarding
  RISK_PARAM_VPN_PRESENCE = 29;
  // this checks if the IP address of the user if risky
  // this currently uses the IP address from red-list as the source of risk IP addresses
  RISK_PARAM_IP_ADDRESS = 30;
  // onboarding risk detection model check if onboarding risk detection model prediction for user
  // this is a retrained version of the old RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL
  RISK_PARAM_ONBOARDING_RISK_DETECTION_MODEL_V1 = 31;
  // check for contact association with LEA and if the profile of the actor is risky
  RISK_PARAM_CONTACT_ASSOCIATION_AND_RISKY_PROFILE = 32;
  // complex rule to check if the user is risky based on the combination of multiple rules
  // can be used as a dynamic controller for hard block vs manual review
  // *special screener* runs after normal run is finished
  RISK_PARAM_COMPLEX_RULE_TIP_TO_AUTO_BLOCK = 33;
}

enum Result {
  RESULT_UNSPECIFIED = 0;
  RESULT_PASS = 1;
  RESULT_FAIL = 2;
}

message Payload {
  oneof option {
    KycAddressPinCode kyc_address_pin_code = 1;
    GeoLocationPinCode geo_location_pin_code = 2;
    OnboardingRiskDetectionModel onboarding_risk_detection_model = 3;
    PhoneNumberRiskInfo phone_number_risk_info = 4;
    HunterInfo hunter_info = 5;
    IncomeEmploymentDiscrepancyInfo income_employment_discrepancy_info = 6;
    AFURiskDetectionModel afu_risk_detection_model = 7;
    AffluenceClassRiskInfo affluence_class_risk_info = 8;
    JunkEmailRiskInfo junk_email_risk_info = 9;
    LocationRiskModel location_risk_model = 10;
    UpiVpaNameMatchInfo upi_vpa_name_match_info = 11;
    InstalledAppsInfo installed_apps_info = 12;
    CreditScoreAffluenceClassInfo credit_score_affluence_class_info = 13;
    CrossVideoLivenessFacematchInfo cross_video_liveness_facematch_info = 14;
    RiskyDeviceInfo risky_device_info = 15;
    UnreviewedAlertsCheckInfo unreviewed_alerts_check_info = 16;
    ContactAssociationInfo contact_association_info = 17;
    ComplexRuleTipToAutoBlockInfo complex_rule_tip_to_auto_block_info = 18;
  }
}

message IncomeEmploymentDiscrepancyInfo {
  employment.EmploymentType employment_type = 1;
  int32 salary = 2;
}

message HunterInfo {
  // foreign key to entry in user intel table based on which decision was taken
  string user_intel_id = 1;
}

message KycAddressPinCode {
  string kyc_pincode = 1;
}

message GeoLocationPinCode {
  string geo_location_pincode = 1;
}

message PhoneNumberRiskInfo {
  RedListCategory category = 1;
}

message OnboardingRiskDetectionModel {
  float score = 1;
  float threshold = 2;
  // bool flag denotes if user is risky
  api.typesv2.common.BooleanEnum riskyUser = 3;
  // response time of the api in second
  google.protobuf.Duration time = 4;
  // list of error returned by the api
  repeated string error = 5;
  string RawVendorResponse = 6;
  string request_id = 7;
}

message AFURiskDetectionModel {
  float score = 1;
  float threshold = 2;
  // bool flag denotes if user is risky
  api.typesv2.common.BooleanEnum risky_user = 3;
  // response time of the api in second
  google.protobuf.Duration time = 4;
  // list of error returned by the api
  repeated string error = 5;
  string raw_vendor_response = 6;
  string request_id = 7;
}

message AffluenceClassRiskInfo {
  enum AffluenceCheck {
    AFFLUENCE_CHECK_UNSPECIFIED = 0;
    // will check if user is in low affluence class and also has high onboarding risk score
    AFFLUENCE_CHECK_LOW_CLASS_AND_HIGH_RISK_SCORE = 1;

    // will check if user is in low affluence class and self employed + also has high onboarding risk score bucket
    AFFLUENCE_CHECK_LOW_CLASS_AND_SELF_EMPLOYED_AND_HIGH_RISK_SCORE = 2;
  }

  repeated AffluenceCheck failed_checks = 1;

  float onboarding_risk_score = 2;
}

// Object to store addtional details for junk email check
// can store details for all the failed checks and thresolds used etc.
message JunkEmailRiskInfo {
  enum JunkEmailCheck {
    JUNK_EMAIL_CHECK_UNSPECIFIED = 0;
    // this check will fail if there are no vowels in the email username
    JUNK_EMAIL_CHECK_NO_VOWELS = 1;
  }

  repeated JunkEmailCheck failed_checks = 1;
  // ration of vowel in the email username
  // will be in the range 0 to 1
  float vowel_ratio = 2;
}

message LocationRiskModel {
  // location risk score will be in range [0, 1]
  float score = 1;

  enums.RiskSeverity risk_severity = 2;
  // Underlying risk evaluator model version
  string model_version = 3;

  string raw_vendor_response = 4;

  string request_id = 5;
}

message UpiVpaNameMatchInfo {
  enum NameMatchDecision {
    NAME_MATCH_UNSPECIFIED = 0;
    // name match resulted in failure scenario
    NAME_MATCH_FAIL = 1;
    // name match resulted in success scenario
    NAME_MATCH_PASS = 2;
  }
  message MatchDetails {
    NameMatchDecision name_match_decision = 1;
    // name match score
    float name_match_score = 2;
  }
  repeated MatchDetails match_details = 1;
}

// Object to store addtional details for installed apps check.
// Contains list of all failed sub checks.
message InstalledAppsInfo {
  enum InstalledAppsCheck {
    INSTALLED_APPS_CHECK_UNSPECIFIED = 0;
    // Whether non system apps installed by the user is less than the set threshold.
    INSTALLED_APPS_CHECK_NON_SYSTEM_APPS = 1;
    // Whether social apps installed by the user is less than the set threshold.
    INSTALLED_APPS_CHECK_SOCIAL_APPS = 2;
    // Evaluates if user has apps from list of malicious apps and doesn't have apps from good apps list
    INSTALLED_APPS_CHECK_MALICIOUS_APPS_ONLY = 3;
    // lift here is: ratio of %LEA actors sufficing a condition to %all onboarded actors sufficing the condition
    // If any one of the bad apps with high lift and none of the good apps with low LEA rate are present
    // Deprecated in favour of INSTALLED_APPS_CHECK_HIGH_LIFT_BAD_APPS_WITHOUT_LOW_LEA_GOOD_APPS
    HIGH_LIFT_BAD_APPS_LOW_LEA_GOOD_APPS = 4 [deprecated = true];
    // If any one of the bad apps with low lift and none of the good apps with high LEA rate are present
    // Deprecated in favour of INSTALLED_APPS_CHECK_LOW_LIFT_BAD_APPS_WITHOUT_HIGH_LEA_GOOD_APPS
    LOW_LIFT_BAD_APPS_HIGH_LEA_GOOD_APPS = 5 [deprecated = true];
    // The check failed in previous runs
    INSTALLED_APPS_CHECK_PREVIOUS_FAILURES = 6;
    // Evaluates if user has apps from list of malicious apps and also has apps from good apps list.
    INSTALLED_APPS_CHECK_MALICIOUS_APPS_WITH_GOOD_APPS = 7;
    // If any one of the bad apps with high lift and at least one of the good apps with low LEA rate are present
    INSTALLED_APPS_CHECK_HIGH_LIFT_BAD_APPS_WITH_LOW_LEA_GOOD_APPS = 8;
    // If any one of the bad apps with low lift and at least one of the good apps with high LEA rate are present
    INSTALLED_APPS_CHECK_LOW_LIFT_BAD_APPS_WITH_HIGH_LEA_GOOD_APPS = 9;
    // If any one of the bad apps with high lift and none of the good apps with low LEA rate are present
    INSTALLED_APPS_CHECK_HIGH_LIFT_BAD_APPS_WITHOUT_LOW_LEA_GOOD_APPS = 10;
    // If any one of the bad apps with low lift and none of the good apps with high LEA rate are present
    INSTALLED_APPS_CHECK_LOW_LIFT_BAD_APPS_WITHOUT_HIGH_LEA_GOOD_APPS = 11;
  }

  repeated InstalledAppsCheck failed_checks = 1;
}

message CreditScoreAffluenceClassInfo {
  enum DecisionDetail {
    DECISION_DETAIL_UNSPECIFIED = 0;
    // no credit report fetch consent available for user
    DECISION_DETAIL_NO_CONSENT_GIVEN = 1;
    // failed to fetch credit report, unspecified verification status
    DECISION_DETAIL_FAILED_TO_FETCH_CREDIT_REPORT = 2;
    // failed to fetch affluence income estimate
    DECISION_DETAIL_FAILED_TO_FETCH_AFFLUENCE_INCOME_ESTIMATE = 3;
    // consent available but no credit report available from bureau
    DECISION_DETAIL_NO_CREDIT_REPORT_AVAILABLE = 4;
    // failed with credit report score check only
    DECISION_DETAIL_FAILED_ONLY_CREDIT_SCORE_CHECKS = 5;
    // failed with affluence and credit report checks
    DECISION_DETAIL_FAILED_CREDIT_AND_AFFLUENCE_CHECKS = 6;
    // failed as borrower has intentionally chosen not to repay the loan
    DECISION_DETAIL_SUIT_FILED_WILFUL_DEFAULT = 7;
    // failed as debt is written off, means that the lender has given up on trying
    // to collect the debt and has written it off as a loss
    DECISION_DETAIL_SUIT_FILED_WILFUL_DEFAULT_WRITTEN_OFF = 8;
  }

  DecisionDetail decision_detail = 1;
}

message CrossVideoLivenessFacematchInfo {
  // https://monorail.pointz.in/p/fi-app/issues/detail?id=75339
  enum CrossVideoCheck {
    CROSS_VIDEO_CHECK_UNSPECIFIED = 0;
    // Failed due to high liveness and low facematch score
    // as different people attempting for liveness across videos
    CROSS_VIDEO_CHECK_FAILED_HIGH_LIVENESS_LOW_FACEMATCH = 1;
  }
  CrossVideoCheck cross_video_check_detail = 1;
  // optional parameter, will be populated for failed cases for tracking
  // tracking only fm req id as liveness can be backtracked
  string failed_fm_req_id = 2;
}

message RiskyDeviceInfo {
  enum Check {
    CHECK_UNSPECIFIED = 0;
    CHECK_DEVICE_MODEL = 1;
    CHECK_DEVICE_ID = 2;
  }
  repeated Check failed_checks = 1;
}

message UnreviewedAlertsCheckInfo {
  enum FailureReason {
    FAILURE_REASON_UNSPECIFIED = 0;
    // if precision score below threshold, for the rules hit in past 30 days
    FAILURE_REASON_LOW_PRECISION_ALERTS = 1;
    // if precision score exceeds threshold, for the rules hit in past 60 days
    FAILURE_REASON_HIGH_PRECISION_ALERTS = 2;
    // if rule belongs to income discrepancy group
    FAILURE_REASON_INCOME_DISCREPANCY_ALERTS = 3;
  }
  repeated FailureReason failure_reasons = 1;
  repeated string failed_rule_ids = 2;
}

message ContactAssociationInfo {
  enum ContactAssociationCheck {
    CONTACT_ASSOCIATION_CHECK_UNSPECIFIED = 0;
    // this refers to the ingress contact association with LEA
    // i.e this actor's phone number was found in LEA's contact list
    CONTACT_ASSOCIATION_CHECK_FOUND_IN_LEAS_CONTACT_LIST = 1;
    // this refers to the ingress contact association with blocked
    // i.e this actor's phone number was found in blocked actor's contact list
    CONTACT_ASSOCIATION_CHECK_FOUND_IN_BLOCKED_ACTORS_CONTACT_LIST = 2;
    // this refers to the egress contact association with LEA
    // i.e LEA's contact was found in actor's contact list
    CONTACT_ASSOCIATION_CHECK_LEA_FOUND_IN_ACTORS_CONTACT_LIST = 3;
  }

  repeated ContactAssociationCheck failure_reasons = 1;
}

message ComplexRuleTipToAutoBlockInfo {
  // failed checks array
  repeated RiskParam failed_checks = 1;
}
