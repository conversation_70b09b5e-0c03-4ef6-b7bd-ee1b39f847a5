syntax = "proto3";

package risk;

import "api/risk/enums/enums.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk";
option java_package = "com.github.epifi.gamma.api.risk";

// Risk Evaluator entity contains the information for the entities synced with the Risk Evaluator
message RiskEvaluatorEntity {
  // Unique Id
  string id = 1;

  // This is the entity pair of entity_type and entity_id sent to the a Risk Evaluator
  RiskEntity entity = 2;

  // Risk Evaluator vendor for which we have sent the entity to, values would be Dronapay, PhonePe etc
  vendorgateway.Vendor vendor = 3;

  // Created AT
  google.protobuf.Timestamp created_at = 4;

  // Updated AT
  google.protobuf.Timestamp updated_at = 5;

  // Deleted AT
  google.protobuf.Timestamp deleted_at = 6;
}

// RiskEntity contains the object for which we would have to sync the data to Risk Evaluator
message RiskEntity {
  // This is the entity type sent to Risk Evaluator, values could be Customer, Account, PA,  etc.
  enums.EntityType type = 1;

  // entity id, corresponding values of transaction_id, actor_id etc.
  string id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

// RiskEvaluatorEntityFieldMask is the enum representation of all the RiskEvaluatorEntity fields.
// Meant to be used as field mask to help with database updates
enum RiskEvaluatorEntityFieldMask {
  RISK_EVALUATOR_ENTITY_FIELD_MASK_UNSPECIFIED = 0;
  RISK_EVALUATOR_ENTITY_FIELD_MASK_ID = 1;
  RISK_EVALUATOR_ENTITY_FIELD_MASK_ENTITY_TYPE = 2;
  RISK_EVALUATOR_ENTITY_FIELD_MASK_ENTITY_ID = 3;
  RISK_EVALUATOR_ENTITY_FIELD_MASK_VENDOR = 4;
  RISK_EVALUATOR_ENTITY_FIELD_MASK_CREATED_AT = 5;
  RISK_EVALUATOR_ENTITY_FIELD_MASK_UPDATED_AT = 6;
  RISK_EVALUATOR_ENTITY_FIELD_MASK_DELETED_AT = 7;
  RISK_EVALUATOR_ENTITY_FIELD_MASK_ALL = 8;
}

