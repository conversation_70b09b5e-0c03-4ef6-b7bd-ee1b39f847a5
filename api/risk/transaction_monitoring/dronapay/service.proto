syntax = "proto3";

package risk.transaction_monitoring.dronapay;

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "api/vendors/dronapay/rule_hit_callback.proto";

option go_package = "github.com/epifi/gamma/api/risk/transaction_monitoring/dronapay";
option java_package = "com.github.epifi.gamma.api.risk.transaction_monitoring.dronapay";

service DronapayCallback {
  rpc IngestRuleHitCallback (vendors.dronapay.RuleHitCallback) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/risk/dronapay/rule-hit/callback"
      body: "*"
    };
  }
}
