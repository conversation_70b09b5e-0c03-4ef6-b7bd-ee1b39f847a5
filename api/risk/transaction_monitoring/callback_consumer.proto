//go:generate gen_queue_pb
syntax = "proto3";

package risk.transaction_monitoring;

import "api/risk/transaction_monitoring/rule_hit_callback.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/risk/transaction_monitoring";
option java_package = "com.github.epifi.gamma.api.risk.transaction_monitoring";

service CallbackConsumer {
  // ProcessRuleHitCallBack receives callback event for a rule trigger
  // This rpc is invoked by queue subscriber to consume callbacks queue packet.
  rpc ProcessRuleHitCallBack (transaction_monitoring.RuleHitCallbackEvent)
    returns (ProcessRuleHitCallBackResponse) {};
}

message ProcessRuleHitCallBackResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
