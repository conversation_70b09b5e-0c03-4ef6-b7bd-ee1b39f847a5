//go:generate gen_sql -types=Category,IdentifierType,MemberItem,DeletionMetadata

syntax = "proto3";

package risk.whitelist;

import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/whitelist";
option java_package = "com.github.epifi.gamma.api.risk.whitelist";

// Category used to specify the type of members that we store in whitelist.
enum Category {
    CATEGORY_UNSPECIFIED = 0;
    // Current Fi Employees
    CATEGORY_FI_EMPLOYEES = 1;
    // EX Fi Employees (who have left now)
    CATEGORY_EX_FI_EMPLOYEES = 2;
    // Investors in Fi
    CATEGORY_FI_INVESTOR = 3;
    // Federal Bank employees
    CATEGORY_FEDERAL_BANK_EMPLOYEES = 4;
    // Friends and Family of current Fi employees
    CATEGORY_FI_EMPLOYEES_FNF = 5;
    // Regulator / Actor / Actress / Senior Executive of a company
    CATEGORY_FI_VIP = 6;
}

// whitelist field mask contains an entry for each whitelist column
enum WhiteListFieldMask {
    WHITELIST_FIELD_MASK_UNSPECIFIED = 0;
    WHITELIST_FIELD_MASK_ALL = 1;
    WHITELIST_FIELD_MASK_ID = 2;
    WHITELIST_FIELD_MASK_IDENTIFIER_TYPE = 3;
    WHITELIST_FIELD_MASK_IDENTIFIER_VALUE = 4;
    WHITELIST_FIELD_MASK_ADDED_BY = 5;
    WHITELIST_FIELD_MASK_DELETION_METADATA = 6;
    WHITELIST_FIELD_MASK_CREATED_AT = 7;
    WHITELIST_FIELD_MASK_UPDATED_AT = 8;
    WHITELIST_FIELD_MASK_DELETED_AT_UNIX = 9;
}

// type of identifier associated with each entry
enum IdentifierType {
    IDENTIFIER_TYPE_UNSPECIFIED = 0;
    // identifier type is actor
    IDENTIFIER_TYPE_ACTOR_ID = 1;
}

message Member {
    // primary key
    string id = 1;
    MemberItem member_item = 2;
    // indicates who added this entry, should be email id of person
    string added_by = 3 [(validate.rules).string.min_len = 1];
    // will be present only for deleted records
    DeletionMetadata deletion_metadata = 4;

    google.protobuf.Timestamp created_at = 5;
    google.protobuf.Timestamp updated_at = 6;
    int64 deleted_at_unix = 7;
}

message MemberItem {
    Category category = 1 [(validate.rules).enum = {not_in: [0]}];
    IdentifierType identifier_type = 2 [(validate.rules).enum = {not_in: [0]}];
    // identifier value associated with each identifier type
    oneof identifier_value {
        string actor_id = 3;
    }
}

message DeletionMetadata {
    // should be email id of person who wants to delete whitelist members
    string deleted_by = 1 [(validate.rules).string.min_len = 1];
    // reason for deletion of whitelist member
    string reason = 2 [(validate.rules).string.min_len = 1];
}
