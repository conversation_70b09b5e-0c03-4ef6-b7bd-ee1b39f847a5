//go:generate gen_sql -types=TransactionTag
syntax = "proto3";

package risk.tagging;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/risk/tagging";
option java_package = "com.github.epifi.gamma.api.risk.tagging";

enum TransactionTag {
  TRANSACTION_TAG_UNSPECIFIED = 0;

  TRANSACTION_TAG_FAMILY_TRANSFER = 1;
}

message TransactionTagMapping {
  // Identifier for the mapping row
  string id = 1;
  // internal fi txn id
  string txn_id = 2;

  TransactionTag tag = 3;

  google.protobuf.Timestamp created_at = 4;
  
  google.protobuf.Timestamp updated_at = 5;
}
