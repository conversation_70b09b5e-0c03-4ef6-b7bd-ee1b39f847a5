//go:generate gen_sql -types=Lien,DisputedTransactions,ReporterContactDetails,AdditionalDetails,Dates,ActionType,ProcessingStatus
syntax = "proto3";

package risk;

import "api/accounts/account_type.proto";
import "api/risk/enums/enums.proto";
import "api/typesv2/nulltypes.proto";
import "google/protobuf/timestamp.proto";


option go_package = "github.com/epifi/gamma/api/risk/lea";
option java_package = "com.github.epifi.gamma.api.risk.lea";

// contains details of LEA complaint we received from bank
message UnifiedLeaComplaint {
  string id = 1;

  // Police/NCCRP complaint number for identifying this particular complaint
  // Can be null in cases where we don't get this identifier, and can be backfilled
  string complaint_id = 2;

  // Identifier for the actor associated with the complaint
  string actor_id = 3;

  // Identifier for the account related to the complaint
  string account_number = 4;

  // Account type for which there was a complaint
  accounts.Type account_type = 5;

  // Dates related to the complaint like AuditDate, ComplaintDate
  Dates dates = 6;

  // Type of action taken regarding the complaint
  ActionType action_type = 7;

  // Status of the account we got in the LEA complaint
  enums.AccountFreezeStatus operational_status = 8;

  // Details of any lien placed on the account
  Lien lien = 9;

  // Reasons provided for the complaint
  string reason = 10;

  // Additional remarks about the complaint, can contain transaction ref, date etc
  string remark = 11;

  // Code indicating the type of freeze applied to account
  string freeze_code = 12;

  // Layer number represents an account's position in chain of fund transfers
  api.typesv2.NullInt32 layer_number = 13;

  // Details of any disputed transactions
  DisputedTransactions disputed_transactions = 14;

  // Current status of the complaint, eg - only raw detials are present for the complaint
  // or the final action has been taken by the bank
  ProcessingStatus processing_status = 15;

  // Origin of the complaint
  enums.LEAReportOrigin lea_origin = 16;

  // Contact details of the institution where the complaint was reported
  ReporterContactDetails reporter_contact_details = 17;

  // Geographical state where the complaint reported
  string origin_geographical_state = 18;

  // Any additional details regarding the complaint
  AdditionalDetails additional_details = 19;

  google.protobuf.Timestamp created_at = 20;

  google.protobuf.Timestamp updated_at = 21;

  google.protobuf.Timestamp deleted_at_unix = 22;
}


message Lien {
  string txn_ref = 1;
  double amount = 2;
  google.protobuf.Timestamp timestamp = 3;
  string raw_data = 4;
}

message DisputedTransactions {

  message Transaction {
    string txn_ref = 1;
    google.protobuf.Timestamp timestamp = 2;
    double amount = 3;
    double disputed_amount = 4;
  }

  repeated Transaction transactions = 1;
}

message ReporterContactDetails {
  message Details {
    string name = 1;
    string phone_number = 2;
    string email = 3;
    string raw_data = 4;
  }

  Details nodal_cyber_cell_officer_details = 1;
  Details grievance_officer_details = 2;
}

message AdditionalDetails {
}

enum UnifiedLeaComplaintFieldMask {
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_UNSPECIFIED = 0;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_COMPLAINT_ID = 1;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACCOUNT_TYPE = 2;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACTION_TYPE = 3;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_ACCOUNT_OPERATIONAL_STATUS = 4;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_LIEN = 5;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_REASON = 6;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_REMARK = 7;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_FREEZE_CODE = 8;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_LAYER_NUMBER = 9;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_DISPUTED_TRANSACTIONS = 10;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_PROCESSING_STATUS = 11;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_LEA_ORIGIN = 12;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_REPORTER_CONTACT_DETAILS = 13;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_ORIGIN_GEOGRAPHICAL_STATE = 14;
  UNIFIED_LEA_COMPLAINT_FIELD_MASK_DATES = 15;
}


enum ActionType {
  ACTION_TYPE_UNSPECIFIED = 0;
  // this denotes that the operational status of the account has been changed
  ACTION_TYPE_UPDATE_OPER_STATUS = 1;
  // this denotes that a lien has been applied on the account
  // i.e an amount has been blocked in the user's account
  ACTION_TYPE_LIEN = 2;
  // this denotes that we do not expect any action to be taken
  ACTION_TYPE_NO_ACTION = 3;
}

enum ProcessingStatus {
  PROCESSING_STATUS_UNSPECIFIED = 0;
  PROCESSING_STATUS_RAW_DETAILS_PRESENT = 1;
  PROCESSING_STATUS_FINAL_ACTION_DETAILS_PRESENT = 2;
}

message Dates {
  // Date when we got the file from the bank
  google.protobuf.Timestamp audit_date = 1;
  // Date when the account was closed
  google.protobuf.Timestamp account_closure_date = 2;
  // Date when the complaint was filed
  google.protobuf.Timestamp complaint_date = 3;
  // Date when we received file from bank
  google.protobuf.Timestamp details_received_date = 4;
}
