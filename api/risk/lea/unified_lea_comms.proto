syntax = "proto3";

package risk;

import "api/risk/case_management/rule.proto";
import "api/risk/enums/enums.proto";
import "api/risk/lea/unified_lea_complaint.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/nulltypes.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/lea";
option java_package = "com.github.epifi.gamma.api.risk/lea";

// Intended handling for an LEA complaint
// Can be used in multiple events including sending comms
message HandlingParams {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  string account_id = 2 [(validate.rules).string.min_len = 1];
  // Status of the account we got in the LEA complaint
  enums.AccountFreezeStatus operational_status = 3;
  // actual lien amount applied on the account
  google.type.Money lien_amount = 4;
  // whether to remove app access or not
  bool remove_app_access = 5;
  // simplified complaint relevant to end user
  message complaint {
    string complaint_id = 1;
    // date of complaint relevant to end user
    google.protobuf.Timestamp date = 2;
    ReporterContactDetails contact_details = 3;
    string complaint_origin_state = 4;
    DisputedTransactions disputed_transactions = 5;
  }

  repeated complaint complaints = 6;

  // if this is true we should send comms
  bool should_send_comms = 7;

  // defines if the action has been taken by bank
  // BOOLEAN_ENUM_UNSPECIFIED -> bank api is failing with permanent failure, so we cannot determine the state
  api.typesv2.common.BooleanEnum action_taken_by_bank = 8;

  // entity id of the actor in LEA
  string entity_id = 9 [(validate.rules).string.min_len = 1];

  // list of rules against which we will create alerts for this LEA
  repeated case_management.RuleIdentifier rule_identifiers = 10;

  // layer number of the complaint
  api.typesv2.NullInt32 layer_number = 11;
}
