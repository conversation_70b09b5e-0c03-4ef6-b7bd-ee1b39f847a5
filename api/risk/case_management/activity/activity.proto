syntax = "proto3";

package risk.case_management.activity;

import "api/celestial/activity/header.proto";
import "api/celestial/activity/notification/notification.proto";
import "api/cx/ticket/ticket.proto";
import "api/risk/case_management/alert.proto";
import "api/risk/case_management/auto_action/auto_action.proto";
import "api/risk/case_management/enums/enums.proto";
import "api/risk/case_management/escalation/escalation.proto";
import "api/risk/case_management/essential/cx_ticket.proto";
import "api/risk/case_management/form/enums.proto";
import "api/risk/case_management/form/form.proto";
import "api/risk/case_management/review/action.proto";
import "api/risk/case_management/review/case.proto";
import "api/risk/case_management/review/comment.proto";
import "api/risk/case_management/review/enums.proto";
import "api/risk/case_management/rule.proto";
import "api/risk/screener/screener.proto";
import "api/typesv2/common/boolean.proto";
import "api/vendorgateway/crm/risk/enums.proto";
import "api/vendorgateway/crm/risk/ticket.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management/activity";
option java_package = "com.github.epifi.gamma.api.risk.case_management.activity";

message LogActionInDBRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // case for which the action was taken
  string case_id = 2;
  // type of review for which action was taken
  // storing this explicitly since review type can change during the lifecycle of a case
  risk.case_management.review.ReviewType review_type = 3;
  // different actions can be performed against a case like pass_account, freeze_account, request_more_info etc
  // this field represents the type of action that was taken on the case
  risk.case_management.review.ActionType action_type = 4;
  // for each action type we will have some action specific parameters
  risk.case_management.review.ActionParameters parameters = 5;
  // this is to identify from where the action was taken,
  risk.case_management.review.ActionSource source = 6;
  // email of analyst taking the action
  string analyst_email = 7;
  // time at which action was initiated from the source system
  google.protobuf.Timestamp initiated_at = 8;

  string actor_id = 9;
}

message LogActionInDBResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  risk.case_management.review.Action action = 2;
}

/*
ValidateAlert will perform basic validation on alert fields as well as Rule which generated the alert
This activity will return non-retryable error if alert validation fails
*/
message ValidateAlertRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // Raw alert received in workflow request
  risk.case_management.RawAlert raw_alert = 2;
}

message ValidateAlertResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // Alert and correspoding rule details post validation
  // DB table specific details such as alert id are expected to be nil
  risk.case_management.AlertWithRuleDetails alert_with_rule = 2;
}


// CreateAlertInDB will return non-retryable error if any schema level validation fails
message CreateAlertInDBRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.Alert alert = 2;
}

message CreateAlertInDBResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // Alert object with id and other details populated post creation
  risk.case_management.Alert alert = 2;
}

/*
CheckCaseCreationEligibility will consider different properties like rule state, alert score, user risk score etc and
perform config based checks to decide if a case needs to be created for a given alert.
It will return AlertActionLevel based on the severity of alert derived above.
*/
message CheckCaseCreationEligibilityRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.AlertWithRuleDetails alert_with_rule = 2;
}

message CheckCaseCreationEligibilityResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // Level of action is needed against the case
  risk.case_management.enums.AlertActionLevel action_level = 2 [deprecated = true];

  // Handling type and reason for input alert.
  risk.case_management.AlertHandlingParams alert_handling_params = 3;
}

/*
AcquireDistributedLockForAlertRequest will Acquire a lock on given set of dedupe fields and locking duration.
If lock is Acquired successfully, it will return the lock expiry time.
*/
message AcquireDistributedLockForAlertRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.review.DedupeParams dedupe_params = 2;
  // max lock duration supported is 10 mins
  // if duration is not passed default lock duration will be 5mins
  google.protobuf.Duration lock_duration = 3 [(validate.rules).duration.lt.seconds = 600];
}

message AcquireDistributedLockForAlertResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  google.protobuf.Timestamp lock_expiry_time = 2;
}

/*
CheckForExistingCaseRequest will check if a case already exists for given set of dedupe fields.
Dedupe will happen at alert level since we don't store sensitive identifiers with case.
Activity will return non-retryable error if given dedupe params are invalid.
If a case is found it will return case_id in response along with boolean flag.
*/
message CheckExistingCaseRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.review.DedupeParams dedupe_params = 2;
}

message CheckExistingCaseResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // field to indicate whether a existing case was found
  api.typesv2.common.BooleanEnum is_found = 2;
  // case id will only be present if is_found field is set to true
  string case_id = 3;
}

/*
Even if a past case is found with a given set of dedupe fields,
case can be in a state where it is already being review or actioned or closed.
For these scenario a new alert can't be appended to the same case and a new case will need to be created.
CheckCaseEligibilityForAppendRequest will perform certain checks on the existing case to check if alert can be appended to the same case and
return the case details as well if given case is eligible for appending.
Ex: If a case is already been reviewed or currently being reviewed we will not append more alerts or other details to the same case
*/
message CheckCaseAppendEligibilityRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  string case_id = 2;

  // Review type of the alert. Alert won't be appended to past case if alert and past case review types are not same.
  review.ReviewType alert_review_type = 3;
}

message CheckCaseAppendEligibilityResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // field to indicate whether a case is eligible for appending new alerts
  api.typesv2.common.BooleanEnum is_eligible = 2;

  // details of the case corresponding to case_id passed in request
  risk.case_management.review.Case case = 3;
}

message CreateCaseForAlertRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.AlertWithRuleDetails alert_with_rule = 2;

  // field indicating whether auto action will be done for the given case
  // use CaseBuilderOptions
  api.typesv2.common.BooleanEnum is_auto_action_required = 3 [deprecated = true];
  // case creation options, contains fields helping
  // decide different behaviours, like is auto action enabled
  review.CaseBuilderOptions case_builder_options = 4;
}

message CreateCaseForAlertResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  string case_id = 2 [deprecated = true];

  review.Case case = 3;
}

message UpdateAlertRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.Alert alert = 2;

  repeated risk.case_management.AlertFieldMask field_mask = 3;
}

message UpdateAlertResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message UpdateCaseForAlertRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // current case details
  // case id is mandatory for update
  risk.case_management.review.Case case = 2;
  // alerts details for which case needs be updated
  risk.case_management.AlertWithRuleDetails alert_with_rule = 3;
  // field indicating whether auto action will be done for the given case
  // use CaseBuilderOptions
  api.typesv2.common.BooleanEnum is_auto_action_required = 4 [deprecated = true];
  // case creation options, contains fields helping
  // decide different behaviours, like is auto action enabled
  review.CaseBuilderOptions case_builder_options = 5;
}

message UpdateCaseForAlertResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

message ReleaseDistributedLockForAlertRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.review.DedupeParams dedupe_params = 2;
}

message ReleaseDistributedLockForAlertResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// ValidateBankAction performs pre validations on concerned actor
// and returns account id as response OR error in-case of failures
// Activity will return non-retryable errors if requested user state
// is same as current user state or an active workflow exists for actor
message ValidateBankActionRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // case for which the action was taken
  string case_id = 2;
  // this field represents the type of action that was taken on the case
  // only accepts validation request for freeze, unfreeze flows
  risk.case_management.review.ActionType action_type = 3 [(validate.rules).enum = {in: [1, 3, 14]}];
  // additional parameters for action request
  risk.case_management.review.ActionParameters parameters = 4;
  string actor_id = 5 [(validate.rules).string.min_len = 1];
}

message ValidateBankActionResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // account id under consideration, required by further steps
  string account_id = 2;
}

// UpdateCaseRequest accepts update field mask
// with case object, returns error if case id is nil
message UpdateCaseRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // list of fields to be updated
  repeated review.CaseFieldMask field_masks = 2;
  // case object to be updated
  review.Case case = 3;
}

message UpdateCaseResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// Activity makes call to onboarding service to apply final verdict
// for user onboarding
message ApplyOnboardingReviewVerdictRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  string case_id = 3 [(validate.rules).string.min_len = 1];
  // final verdict for action ie. pass or fail user onboarding
  enums.Verdict verdict = 4 [(validate.rules).enum = {not_in: [0]}];
}

message ApplyOnboardingReviewVerdictResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// Activity calls onboarding service to add liveness attempts
message AddLivenessRetriesRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

message AddLivenessRetriesResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// UnsnoozeCaseRequest fetches latest case details and removes snooze on case if
// another snooze request has not extended snooze timer
// i.e. unsnooze is against a snooze request only
message UnsnoozeCaseRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // case id against which case needs to be fetched
  string case_id = 2 [(validate.rules).string.min_len = 1];
}

message UnsnoozeCaseResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// GetAutoActionsRequest activity takes the case id and returns the actions needed to be performed against the case
message GetAutoActionsRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string case_id = 2 [(validate.rules).string.min_len = 1];
}

// GetAutoActionsResponse gives the auto action against the case.
// It also gives the commentary about the dedupe alerts and picked actions among them.
message GetAutoActionsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  repeated auto_action.DeduplicatedReviewAction deduplicated_review_actions = 2;
}

// AddCommentRequest creates the comment in the case management
message AddCommentRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // comment to be created
  review.Comment comment = 2 [(validate.rules).message.required = true];
}

// AddCommentResponse
message AddCommentResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// GetExtendedRuleRequest fetches additional details for a rule
// such as review types and info associated with it, tags etc.
// For empty field masks, only rule object will be populated
message GetExtendedRuleRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // Rule identifier against which extended rule object needs to be fetched
  RuleIdentifier rule_identifier = 2;
  // For empty field masks, only rule object will be populated
  repeated case_management.ExtendedRuleFieldMask extended_rule_field_masks = 4;
}

message GetExtendedRuleResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // Additional details associated to a rule
  case_management.ExtendedRule extended_rule = 2;
}

// ApplyAFUReviewVerdictRequest Calls Auth service to apply verdict on an afu attempt.
// If fail, afu attempt will be marked as fail. If Pass, afu is passed.
// Does nothing if input afu id is not latest afu attempt of the user.
message ApplyAFUReviewVerdictRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // actor whose afu attempt is being reviewed
  string actor_id = 2 [(validate.rules).string.min_len = 1];
  // case linked to afu attempt
  string case_id = 3 [(validate.rules).string.min_len = 1];
  // afu attempt against which verdict will be applied
  string afu_id = 4 [(validate.rules).string.min_len = 1];
  // verdict on an the attempt. Pass or fail the afu attempt.
  enums.Verdict verdict = 5 [(validate.rules).enum = {not_in: [0]}];
}

message ApplyAFUReviewVerdictResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// Retrieves alerts against the identifier
message GetAlertsRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // Identifier to fetch alerts
  oneof identifier {
    string case_id = 2;
  }
}

message GetAlertsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  // alerts against the identifier
  repeated case_management.Alert alerts = 2;
}

message GetCaseRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // case id to fetch
  string case_id = 2;
}

message GetCaseResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // response case object
  review.Case case = 2;
}

message ShouldExcludeReviewRequest {
  // Common response header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // alerts details for which case review could be redundant
  risk.case_management.AlertWithRuleDetails alert_with_rule = 2;
}

message ShouldExcludeReviewResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  enums.CaseReviewLevel case_review_level = 2;
  // general remarks validating the above decision
  string remarks = 3;
}

message MoveToReviewRequest {
  // Common response header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  string case_id = 2;
}

message MoveToReviewResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // case response object
  review.Case case = 2;
}

message ApplyScreenerVerdictRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  // ScreenerVerdictRequest contains request entities
  // required to mark verdict on a screener attempt
  screener.ScreenerVerdictRequest verdict_request = 2 [(validate.rules).message.required = true];
}

message ApplyScreenerVerdictResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// If outcall agent failed to reach out to user,
// a communication can be triggered reminding them of current and further outcall attempts.
// Preconditions for sending communication, communication medium and content will be decided by underlying implementation.
// A comment against a case will be added after successful communication.
message SendOutcallReminderCommsRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // case on which outcall attempt was made.
  string case_id = 2 [(validate.rules).string.min_len = 1];
}

message SendOutcallReminderCommsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// UpdateActionRequest updates action columns specified by field masks.
message UpdateActionRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.review.Action action = 2 [(validate.rules).message.required = true];

  repeated risk.case_management.review.ActionFieldMask action_field_masks = 3 [(validate.rules).repeated.min_items = 1];
}

message UpdateActionResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // Updated action
  risk.case_management.review.Action action = 2;
}

// DetermineActionProcessingRequest determines processing required for given action.
message DetermineActionProcessingRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.review.Action action = 2 [(validate.rules).message.required = true];
}

message DetermineActionProcessingResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  risk.case_management.review.ActionProcessingParams processing_params = 2;
}

// SendForSecondaryReviewRequest sends the case for secondary review.
message SendForSecondaryReviewRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.review.ActionProcessingParams processing_params = 2 [(validate.rules).message.required = true];
}

message SendForSecondaryReviewResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// Sends notifications to user with attached form.
// Form specific details such as identifier and expiry will be populated in messages.
message SendFormRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  form.FormOrchestrationParams params = 2;
}

message SendFormResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// Updates form object in db for given field masks.
message UpdateFormRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.form.Form form = 2;

  repeated risk.case_management.form.FormFieldMask form_field_masks = 3;
}

message UpdateFormResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// Creates form object in db.
message CreateFormRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  form.Form form = 2;
}

message CreateFormResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  risk.case_management.form.Form form = 2;
}

// Generates questionnaire with given question identifiers and links to the form.
message GenerateQuestionnaireRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  form.FormOrchestrationParams params = 2;
}

message GenerateQuestionnaireResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// Return form object for given form id.
message GetFormRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  string form_id = 2 [deprecated = true];
  oneof identifier {
    string identifier_actor_id = 3;
    string identifier_case_id = 4;
    string identifier_form_id = 5;
  }
}

message GetFormResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  risk.case_management.form.Form form = 2;
}

// Return notifications to be sent for outcall.
message GetOutcallNotificationsRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  string actor_id = 2;
}

message GetOutcallNotificationsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  celestial.activity.notification.Notification notification = 2;

  celestial.activity.notification.Notification reminder_notification = 3;
}

// DetermineEscalationEventHandlingRequest determines handling required for given escalation event.
message DetermineEscalationEventHandlingRequest {
  // Common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  risk.case_management.escalation.EscalationEvent event = 2 [(validate.rules).message.required = true];
}

message DetermineEscalationEventHandlingResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  risk.case_management.escalation.EventWithHandlingParams handling_params = 2 [(validate.rules).message.required = true];
}

// UpdateCXTicketsRequest updates input cx tickets.
message UpdateCXTicketsRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  repeated risk.case_management.essential.UpdateCXTicketParams params_list = 2 [(validate.rules).repeated.min_items = 1];
}

message UpdateCXTicketsResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
}

// GetCXTicketsRequest returns cx ticket for input ticket id.
message GetCXTicketRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string ticket_id = 2 [(validate.rules).string.min_len = 1];
}

message GetCXTicketResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // Requested ticket
  cx.ticket.Ticket ticket = 2;
}

/*
CreateEscalationRawAlertObject returns with a raw alert object for escalations given actor and entity id
*/
message CreateEscalationRawAlertObjectRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;
  string actor_id = 2;
  // primary ticket id which is entity id for alert
  string primary_ticket_id = 3;
  // initiated at for escalation event from source
  google.protobuf.Timestamp initiated_at = 4;
}

message CreateEscalationRawAlertObjectResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;
  risk.case_management.RawAlert raw_alert = 2;
}

// AppendDetailsToCXTicketRequest activity appends all updates automatically to cx ticket and returns cx ticket with updated details.
message AppendDetailsToCXTicketRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  string actor_id = 2;
  cx.ticket.Ticket ticket = 3;
}

message AppendDetailsToCXTicketResponse {
  celestial.activity.ResponseHeader response_header = 1;

  // cx ticket with case details
  cx.ticket.Ticket ticket = 2;
}

// Case Reprioritization Activity Messages

message GetCasesFromFreshdeskRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // filters for fetching specific cases
  CaseFetchFilters filters = 2;

  // sorting options
  SortInfo sort_by = 3;
}

message GetCasesFromFreshdeskResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // S3 path where enriched case data is stored
  string s3_path = 5;
}

enum Status {
  STATUS_UNSPECIFIED = 0;

  STATUS_CREATED = 1;

  STATUS_ASSIGNED = 2;

  STATUS_IN_REVIEW = 3;

  STATUS_IN_QA_REVIEW = 4;

  // Case review is completed and a verdict is assigned to it
  STATUS_DONE = 5;

  // Case can be marked as invalid at times without any verdict or before review starts
  // due to various reasons e.g., re-prioritisation, wrong upload, bugs in the code
  STATUS_WONT_REVIEW = 6;

  // review action in process for case
  STATUS_REVIEW_ACTION_IN_PROGRESS = 7;
  // case needs human intervention, can be due to retry exhausted, bank rejection's etc.
  STATUS_MANUAL_INTERVENTION = 8;
  // more information is required from user on the case and it is currently pending on agent to collect additional user information.
   // Agent intervention is required and case will be assigned to an agent.
  STATUS_PENDING_USER_INFO = 9;
  // case is marked for auto action and will consolidate all the suggested actions for x period of time
  STATUS_MARKED_FOR_AUTO_ACTION = 10;
  // Ticket is pending on user to share information regarding their account and no agent intervention is required.
  // e.g., It can be used if outcall form is sent to the user and awaiting user response.
  STATUS_PENDING_ON_USER = 11;
}

message CaseFetchFilters {
  // Freshdesk ticket status filter (open, pending, etc.)
  Status status = 1;

  // Whether to include only unassigned tickets
  bool unassigned_only = 2;

  // Date range filters
  google.protobuf.Timestamp created_after = 4;  // ISO format
  google.protobuf.Timestamp updated_after = 5;  // ISO format

  // Review type
  repeated vendorgateway.crm.risk.ReviewType review_types = 6;
}

message SortInfo {
  SortByColumn column = 1;
  bool is_asc = 2;
}

enum SortByColumn {
  SORT_BY_COLUMN_UNSPECIFIED = 0;
  SORT_BY_COLUMN_CREATED_AT = 1;
  SORT_BY_COLUMN_UPDATED_AT = 2;
  SORT_BY_COLUMN_PRIORITY = 3;
  SORT_BY_COLUMN_CONFIDENCE_SCORE = 4;
}

message GetAlertsBatchRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // List of actor IDs to get alerts for
  repeated string actor_ids = 2;

  // Maximum number of alerts per actor
  int32 max_alerts_per_actor = 3;
}

message GetAlertsBatchResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // Alerts grouped by actor ID
  repeated ActorAlerts actor_alerts = 2;
}

message ActorAlerts {
  string actor_id = 1;
  repeated AlertWithRuleDetails alerts = 2;
}

message GetConfidenceScoresBatchRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // List of scoring requests
  repeated ConfidenceScoreRequest score_requests = 2;
}

message GetConfidenceScoresBatchResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // Results of confidence scoring
  repeated ConfidenceScoreResult score_results = 2;
}

message ConfidenceScoreRequest {
  string actor_id = 1;
  repeated AlertWithRuleDetails alerts = 2;
}

message ConfidenceScoreResult {
  string actor_id = 1;
  float confidence_score = 2;
  bool success = 3;
  string error_message = 4;
}

message BatchUpdateFreshdeskScoresRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // Batches of score updates grouped by score value for efficiency
  repeated ScoreBatch score_batches = 2;

  // Whether this is a dry run (no actual updates)
  bool dry_run = 3;
}

message BatchUpdateFreshdeskScoresResponse {
  // Common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // Summary of update operations
  FreshdeskUpdateSummary update_summary = 2;
}

message ScoreBatch {
  // The confidence score value for this batch
  float score_value = 1;

  // List of ticket IDs to update with this score
  repeated int64 ticket_ids = 2;
}

message FreshdeskUpdateSummary {
  // Total number of tickets processed
  int32 total_tickets = 1;

  // Number of tickets successfully updated
  int32 successful_updates = 2;

  // Number of tickets that failed to update
  int32 failed_updates = 3;

  // List of update errors
  repeated TicketUpdateError errors = 4;

  // Processing time in milliseconds
  int64 processing_time_ms = 5;
}

message TicketUpdateError {
  int64 ticket_id = 1;
  string error_message = 2;
  int32 http_status_code = 3;
}

// S3 Storage Messages for Case Reprioritization

// EnrichedCaseData represents case data enriched with alerts
message EnrichedCaseData {
  // The case information
  review.Case case = 1;

  // Associated alerts for this case
  repeated AlertWithRuleDetails alerts = 2;
}

// Basic ticket information structure for case reprioritization
message TicketInfo {
  // Ticket ID (Freshdesk ticket ID)
  string id = 1;

  // Only taking ticket ids for now because it is only needed to calculate the scores again.
  // Add the fields here for updating getting the existing ticket fields.
}

// Single confidence score request for DS model
message GetConfidenceScoreRequest {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  string caseId = 2;
}

// Single confidence score response from DS model
message GetConfidenceScoreResponse {
  // common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // Model 1 name
  string model1_name = 2;

  // Model 2 Name
  string model2_name = 3;

  // Model 1 score
  float model1_score = 4;

  // Model 2 score
  float model2_score = 5;

  // Whether scoring was successful
  bool success = 6;

  // Error message if scoring failed
  string error_message = 7;
}

// Store tickets to S3 request
message StoreTicketsToS3Request {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // List of tickets to store in S3
  repeated vendorgateway.crm.risk.RiskTicket tickets = 2;

  // Batch ID for tracking
  string batch_id = 3;

  // Workflow run ID for unique identification
  string workflow_run_id = 4;
}

// Store tickets to S3 response
message StoreTicketsToS3Response {
  // common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // S3 path where tickets are stored
  string s3_path = 2;

  // Size of data stored in bytes
  int64 data_size = 3;

  // Batch ID that was processed
  string batch_id = 4;
}

// Read case data from S3 request
message ReadCaseDataFromS3Request {
  // common request header across all the celestial activities.
  celestial.activity.RequestHeader request_header = 1;

  // S3 path to read case data from
  string s3_path = 2;
}

// Read case data from S3 response
message ReadCaseDataFromS3Response {
  // common response header across all the celestial activities.
  celestial.activity.ResponseHeader response_header = 1;

  // List of tickets read from S3
  repeated vendorgateway.crm.risk.RiskTicket tickets = 2;

  // Metadata about the case data
  CaseDataMetadata metadata = 3;
}

// List of case IDs for grouping
message CaseIdList {
  repeated string case_ids = 1;
}

// Case reprioritization data structure for S3 storage
message CaseReprioritizationData {
  // Metadata about the stored case data
  CaseDataMetadata metadata = 1;

  // List of tickets
  repeated TicketInfo tickets = 2;
}

// Metadata for case data stored in S3
message CaseDataMetadata {
  // Batch ID for tracking
  string batch_id = 1;

  // Total number of tickets in this batch
  int32 total_tickets = 2;

  // Timestamp when data was created
  string created_at = 3;

  // Workflow run ID for unique identification
  string workflow_run_id = 4;
}

// Ticket information with calculated confidence score
message TicketWithScore {
  // Ticket ID (Freshdesk ticket ID)
  string ticket_id = 1;

  // Calculated score 1 from DS model (0-100 integer)
  float model1_score = 2;

  // Name 1 of the DS model used for scoring
  string model1_name = 3;

  // Calculated score 2 from DS model (0-100 integer)
  float model2_score = 4;

  // Name 2 of the DS model used for scoring
  string model2_name = 5;
}
