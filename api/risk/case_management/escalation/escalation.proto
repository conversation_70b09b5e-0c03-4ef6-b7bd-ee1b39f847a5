syntax = "proto3";

package risk.case_management.escalation;

import "api/cx/ticket/ticket.proto";
import "api/risk/case_management/escalation/enums.proto";
import "api/risk/case_management/review/enums.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management/escalation";
option java_package = "com.github.epifi.gamma.api.risk.case_management.escalation";

// Object to be used in event ingestion flow, containing the raw details for escalation event
// After receiving this escalation event we can normalize the details, add handling etc. which
// would result in EventWithHandlingParams object below
message EscalationEvent {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // entity for we need escalation handling
  // Is a one of condition with entities like CX ticket to create a risk escalation case
  oneof request_entity {
    CXTicketEvent cx_ticket_event = 2;
  }
}

// CX ticket event object
message CXTicketEvent {
  cx.ticket.Ticket cx_ticket = 1;
}

message HandlingParams {
  // Intended handling for escalation event
  // Every handling will have respective options with more granular details
  HandlingType handling_type = 1;

  // Mandatory options for above event handling
  oneof options {
    CreateCaseOptions create_case = 2;
    UpdateCaseOptions update_case = 3;
    DropEscalationOptions drop_escalation = 4;
  }
}

// Wraps original escalation event along with handling params
// This specifies handling type with respective options
message EventWithHandlingParams {
  EscalationEvent escalation_event = 1;
  HandlingParams handling_params = 2;
}

message CreateCaseOptions {
  // Case creation status, only specific values are accepted
  // status CREATED: will create in a case with CREATED status
  // status PENDING_ON_USER: this could be an edge case where create ticket update was missed, and we get update with this status
  risk.case_management.review.Status status = 1 [(validate.rules).enum = {in: [1, 11]}];
}

message UpdateCaseOptions {
  // If a case is existent
  // Specific values are accepted here,
  // status CREATED: we got reply from user, we might want to move risk case to created again
  // status PENDING_ON_USER: incase action like outcall or an email reply is awaited
  // status CLOSED: will update case status to CLOSED
  risk.case_management.review.Status status = 1 [(validate.rules).enum = {in: [1, 6, 11]}];
  // associated case id that needs to be updated
  string case_id = 2 [(validate.rules).string.min_len = 1];
}

message DropEscalationOptions {
  enum Reason {
    REASON_UNSPECIFIED = 0;
    // A previous rejected escalation action is present, hence dropping current escalation
    REASON_PREVIOUS_ESCALATION_REJECTED = 1;
    // Previous unfreeze action present and is in progress
    REASON_UNFREEZE_ACTION_IN_PROGRESS = 2;
    // Account is already unfrozen from bank and our side and hence no risk action is required
    REASON_ACCOUNT_UNFROZEN = 3;
    // Account is closed
    REASON_ACCOUNT_CLOSED = 4;
    // Current event updates the secondary tickets
    // not the primary ticket by which case creation was done
    // As per design we are only tracking the primary CX tickets
    REASON_UPDATES_SECONDARY_TICKET = 5;
    // Duplicate update received for the same case
    // Eg. for open risk case, if we get an update for STATUS_ESCALATED_TO_L2
    // from CX, it maps to same OPEN state at our side, we can drop this update
    REASON_DUPLICATE_UPDATE = 6;
    // Form is sent to the user but not submitted
    REASON_FORM_ALREADY_SENT = 7;
  }
  Reason reason = 1 [(validate.rules).enum = {not_in: [0]}];
}


