syntax = "proto3";

package risk.case_management.review;

import "api/risk/case_management/review/enums.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management/review";
option java_package = "com.github.epifi.gamma.api.risk.case_management.review";

// Annotation is the construct to capture the structured response from the analyst. This will derive its values from
// the annotation library and will get added against the entity
message Annotation {

  // primary key for the annotation
  string id = 1;

  // against which entity the input should get created
  ReviewEntityType entity_type = 2 [(validate.rules).enum = {not_in: [0]}];

  // entity id against which the annotation is created
  string entity_id = 3 [(validate.rules).string.min_len = 1];

  // allowed annotation id for the value and type
  string allowed_annotation_id = 4 [(validate.rules).string.min_len = 1];

  // added_by_email for the audit purpose
  string added_by_email = 5 [(validate.rules).string.min_len = 1];

  // create at
  google.protobuf.Timestamp created_at = 6;

  // updated at
  google.protobuf.Timestamp updated_at = 7;

  // deleted at
  google.protobuf.Timestamp deleted_at = 8;

  // case id against which the annotation has been added
  string case_id = 9;
}
