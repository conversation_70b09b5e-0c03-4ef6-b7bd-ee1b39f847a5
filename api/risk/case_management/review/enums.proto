//go:generate gen_sql -types=ActionStatus,ActionProcessingType
syntax = "proto3";

package risk.case_management.review;

option go_package = "github.com/epifi/gamma/api/risk/case_management/review";
option java_package = "com.github.epifi.gamma.api.risk.case_management.review";

// enum to define types of actions available against a case
enum ActionType {
  ACTION_TYPE_UNSPECIFIED = 0;
  // Current user account is deemed fraud and needs to be frozen
  ACTION_TYPE_FREEZE_ACCOUNT = 1;
  // Current user account is passed and no freeze action is being taken
  ACTION_TYPE_PASS_ACCOUNT = 2;
  // Unfreeze an already frozen account
  ACTION_TYPE_UNFREEZE_ACCOUNT = 3;
  // More information is required from user to make decision for the case
  // Case will be moved to the user outcall queue post this action
  ACTION_TYPE_REQUEST_USER_INFO = 4;
  // Case will be moved back to review queue
  // EX: This action can be used in cases where case is moved to different queue like pending user info queue
  // and case needs to move back to review post user info is gathered and response is added in the investigation notes
  ACTION_TYPE_MOVE_TO_REVIEW = 5;
  // Case verdict is passed and user can continue onboarding
  // applicable for pre-onboarding user review
  ACTION_TYPE_PASS_USER_ONBOARDING = 6;
  // Case verdict is failed and user onboarding is denied
  // applicable for pre-onboarding user review
  ACTION_TYPE_FAIL_USER_ONBOARDING = 7;
  // Non terminal action, user will be asked to retry liveness
  // applicable for pre-onboarding user review
  ACTION_TYPE_ADD_LIVENESS_RETRIES = 8;
  // Non terminal, non blocking action. Case will be snoozed till input time and unsnoozed after expiry.
  ACTION_TYPE_SNOOZE = 9;
  // Case verdict and Afu attempt are marked as fail.
  // Fail verdict is applied against latest afu attempt attached to a case only if it is also user's latest attempt
  ACTION_TYPE_FAIL_AFU = 10;
  // Case verdict and Afu attempt are marked as pass.
  // Pass verdict is applied against latest afu attempt attached to a case only if it is also user's latest attempt
  ACTION_TYPE_PASS_AFU = 11;
  // To review lea reported user's financial behaviour and add patters as annotations and comments.
  // It is a Terminal action and applicable for lea complaint review type.
  ACTION_TYPE_INVESTIGATE_LEA_ACTOR = 12;
  // Terminal action to not entertain current escalation by user. Resolve escalation cases with both cx and risk.
  // All future escalation cases by user will be diverted to a separate bucket and escalations might be rejected by agent or system.
  ACTION_TYPE_REJECT_ESCALATION = 13;
  ACTION_TYPE_ADD_LIEN = 14;
}

// enum to define different sources from which an action can be taken against a case
enum ActionSource {
  ACTION_SOURCE_UNSPECIFIED = 0;
  // to be used for action taken by analysts during case review flow
  ACTION_SOURCE_REVIEW_FLOW = 1;
  // to be used for action taken by auto-FREEZE flow
  ACTION_SOURCE_AUTO_FREEZE_FLOW = 2;
  // to be used for action taken by system such as auto action flow, default action post outcall etc.
  ACTION_SOURCE_AUTO_ACTION = 3;
}

// enum to define freeze level for ACTION_TYPE_FREEZE_ACCOUNT
enum FreezeLevel {
  FREEZE_LEVEL_UNSPECIFIED = 0;
  // perform a credit freeze for user account
  FREEZE_LEVEL_CREDIT = 1;
  // perform debit freeze for user account
  FREEZE_LEVEL_DEBIT = 2;
  // perform both debit and credit freeze for user account
  FREEZE_LEVEL_TOTAL = 3;
}

// enum to be used while fetching review details to indicate what all fields are needed in response
enum ReviewDetailsFieldMask {
  REVIEW_DETAILS_FIELD_MASK_UNSPECIFIED = 0;
  // to be used if case details are required in response
  REVIEW_DETAILS_FIELD_MASK_CASE = 1;
  // to be used if actions are required in response
  REVIEW_DETAILS_FIELD_MASK_ACTIONS = 2;
  // to be used if related cases are required in response
  REVIEW_DETAILS_FIELD_MASK_RELATED_CASES = 3;
  // to be used if alerts are required in response
  REVIEW_DETAILS_FIELD_MASK_ALERTS = 4;
  // to be used if all the fields are required in response
  REVIEW_DETAILS_FIELD_MASK_ALL = 5;
  // to be used if firehose id is required in response
  REVIEW_DETAILS_FIELD_MASK_FIREHOSE_ID = 6;
  // to be used if day wise aggregates of alerts for the actor is required.
  REVIEW_DETAILS_FIELD_MASK_ALERT_AGGREGATES = 7;
}

// ActionFieldMask is the enum representation of all the Actions fields.
enum ActionFieldMask {
  ACTION_FIELD_MASK_UNSPECIFIED = 0;
  // field to signify ALL columns for action table
  ACTION_FIELD_MASK_ALL = 1;

  ACTION_FIELD_MASK_ID = 2;
  ACTION_FIELD_MASK_CASE_ID = 3;
  ACTION_FIELD_MASK_REVIEW_TYPE = 4;
  ACTION_FIELD_MASK_ACTION_TYPE = 5;
  ACTION_FIELD_MASK_PARAMETERS = 6;
  ACTION_FIELD_MASK_SOURCE = 7;
  ACTION_FIELD_MASK_ANALYST_EMAIL = 8;
  ACTION_FIELD_MASK_INITIATED_AT = 9;
  ACTION_FIELD_MASK_CREATED_AT = 10;
  ACTION_FIELD_MASK_UPDATED_AT = 11;
  ACTION_FIELD_MASK_ACTOR_ID = 12;
  ACTION_FIELD_MASK_STATUS = 13;
  ACTION_FIELD_MASK_PROCESSING_TYPE = 14;
}

// We can assign priority for a ticket based on different factors like source, rule, score etc of an alert
// based on priority of a ticket we can assign them to different groups or prioritise the review within the same group
enum Priority {
  PRIORITY_UNSPECIFIED = 0;

  PRIORITY_CRITICAL = 1;

  PRIORITY_HIGH = 2;

  PRIORITY_MEDIUM = 3;

  PRIORITY_LOW = 4;
}

// A ticket undergoes multiple states based on actions taken by multiple stakeholders
// `Status` enum will contain list of all possible such states
// The enum may be used as a filter to limit the type of tickets that appears in a queue
enum Status {
  STATUS_UNSPECIFIED = 0;

  STATUS_CREATED = 1;

  STATUS_ASSIGNED = 2;

  STATUS_IN_REVIEW = 3;

  STATUS_IN_QA_REVIEW = 4;
  // case was reviewed and resolved with one of the verdict
  STATUS_RESOLVED = 5;
  // case was closed without and review/verdict, will be used if we are truncating some exisiting cases due to various reasons
  STATUS_CLOSED = 6;
  // review action in process for case
  STATUS_REVIEW_ACTION_IN_PROGRESS = 7;
  // case needs human intervention, can be due to retry exhausted, bank rejection's etc.
  STATUS_MANUAL_INTERVENTION = 8;
  // more information is required from user on the case and it is currently pending on agent to collect additional user information.
  // Agent intervention is required and case will be assigned to an agent.
  STATUS_PENDING_USER_INFO = 9;
  // case is marked for auto action and will consolidate all the suggested actions for x period of time
  STATUS_MARKED_FOR_AUTO_ACTION = 10;
  // Case is pending on user to share information regarding their account and no agent intervention is required.
  // e.g., It can be used if outcall form is sent to the user and awaiting user response.
  STATUS_PENDING_ON_USER = 11;
}

// Based on review type we might assign a case to different group
// Also review type will decide what information will be available to analysts during review process
enum ReviewType {
  REVIEW_TYPE_UNSPECIFIED = 0;

  REVIEW_TYPE_USER_REVIEW = 1;

  REVIEW_TYPE_TRANSACTION_REVIEW = 2;

  REVIEW_TYPE_AFU_REVIEW = 3;

  REVIEW_TYPE_LEA_COMPLAINT_REVIEW = 4;

  REVIEW_TYPE_ESCALATION_REVIEW = 5;

  REVIEW_TYPE_MULTI_REVIEW = 6;
}

// Order status filters available for GetTransactionDetailsForReview rpc
enum TransactionReviewOrderStatusFilter {
  TRANSACTION_REVIEW_ORDER_STATUS_FILTER_UNSPECIFIED = 0;
  TRANSACTION_REVIEW_ORDER_STATUS_FILTER_SUCCESS = 1;
  TRANSACTION_REVIEW_ORDER_STATUS_FILTER_IN_PROGRESS = 2;
  TRANSACTION_REVIEW_ORDER_STATUS_FILTER_FAILED = 3;
  TRANSACTION_REVIEW_ORDER_STATUS_FILTER_IN_SETTELEMENT = 4;
  TRANSACTION_REVIEW_ORDER_STATUS_FILTER_MANUAL_INTERVENTION = 5;
}

// enum to be used while creating the review inputs i.e. Annotations/Comments
// it denotes against which the review input should be created
enum ReviewEntityType {
  REVIEW_ENTITY_TYPE_UNSPECIFIED = 0;
  // review input is against user entity
  REVIEW_ENTITY_TYPE_USER = 1;
  // review input is against the liveness entity
  REVIEW_ENTITY_TYPE_LIVENESS = 2;
  // review input is against the transaction entity
  REVIEW_ENTITY_TYPE_TRANSACTION = 3;
  // review input is against the rule entity
  REVIEW_ENTITY_TYPE_RULE = 4;
  // review input is against the case entity
  REVIEW_ENTITY_TYPE_CASE = 5;
  // review input is against the facematch entity
  REVIEW_ENTITY_TYPE_FACEMATCH = 6;
  // review input is against the afu( Auth Factor Update) entity
  REVIEW_ENTITY_TYPE_AFU = 7;
  // review input is against lea complaint entity
  REVIEW_ENTITY_TYPE_LEA_COMPLAINT = 8;
  // review input is against alert entity.
  REVIEW_ENTITY_TYPE_ALERT = 9;
}

// CaseFieldMask is the enum representation of all the Case fields
enum CaseFieldMask {
  CASE_FIELD_MASK_UNSPECIFIED = 0;
  CASE_FIELD_MASK_ID = 1;
  CASE_FIELD_MASK_PRIORITY = 2;
  CASE_FIELD_STATUS = 3;
  CASE_FIELD_ASSIGNED_TO = 4;
  CASE_FIELD_IS_SAMPLE = 5;
  CASE_FIELD_ACTOR_ID = 6;
  CASE_FIELD_MASK_REVIEW_TYPE = 7;
  CASE_FIELD_MASK_VERDICT = 8;
  CASE_FIELD_MASK_TAGS = 9;
  CASE_FIELD_MASK_CREATED_AT = 10;
  CASE_FIELD_MASK_UPDATED_AT = 11;
  CASE_FIELD_MASK_SNOOZED_TILL = 12;
  CASE_FIELD_MASK_CONFIDENCE_SCORE = 13;
  CASE_FIELD_MASK_LAST_ASSIGNED_ANALYST_EMAIL = 14;
  CASE_FIELD_MASK_ANALYST_GROUP = 15;
  CASE_FIELD_MASK_MODEL1_SCORE = 16;
  CASE_FIELD_MASK_MODEL2_SCORE = 17;
  CASE_FIELD_MASK_MODEL_SELECTED = 18;
}

// Groups for analysts, these groups can be created based on one or combination of more that one of these factors
// Analyst levels like L1, L2 etc
// Analyst expertise like user review, txn review Or outcall
// based on case priority i.e we might have different queues based on priority and different group might look at different queues
enum AnalystGroup {
  ANALYST_GROUP_UNSPECIFIED = 0;
  // group primarily working on transaction review cases
  ANALYST_GROUP_TRANSACTION_REVIEW = 1;
  // group working on user outcall for getting more information
  ANALYST_GROUP_USER_OUTBOUND_CALL = 2;
  // group primarily working on user review cases
  ANALYST_GROUP_USER_REVIEW = 3;
  // Group for entry level analysts.
  ANALYST_GROUP_L1 = 4;
  // Group for team lead/experienced analysts.
  ANALYST_GROUP_L2 = 5;
  // Group for QA review analysts
  ANALYST_GROUP_QA_REVIEW = 6;
  // Group for escalation ops
  ANALYST_GROUP_ESCALATION = 7;
  // Group for agents working on multiple type of case review
  ANALYST_GROUP_MULTI_REVIEW = 8;
}

enum SortOrder {
  SORT_ORDER_UNSPECIFIED = 0;

  SORT_ORDER_DESC = 1;

  SORT_ORDER_ASC = 2;
}

enum ActionStatus {
  ACTION_STATUS_UNSPECIFIED = 0;

  // Action is successfully executed.
  ACTION_STATUS_SUCCESS = 1;

  // Failed to execute action.
  ACTION_STATUS_FAILED = 2;

  // Action wasn't executed and skipped.
  ACTION_STATUS_SKIPPED = 3;
}

// ActionProcessingType indicates how an action on case should be processed.
// e.g. For a junior analyst, action might be skipped and case sent for secondary review or
// for a random sampled case may flow to QA review
enum ActionProcessingType {
  ACTION_PROCESSING_TYPE_UNSPECIFIED = 0;
  ACTION_PROCESSING_TYPE_EXECUTE = 1;
  ACTION_PROCESSING_TYPE_SEND_FOR_SECONDARY_REVIEW = 2;
  ACTION_PROCESSING_TYPE_SEND_FOR_QA_REVIEW = 3;
}

// OutcallMode indicates the different communication channels used for outcall to users.
enum OutcallMode {
  OUTCALL_MODE_UNSPECIFIED = 0;
  // Outcall will be made by agent to user.
  OUTCALL_MODE_MANUAL_CALL = 1;
  // Form will be sent to the user with questionnaire.
  OUTCALL_MODE_FORM = 2;
}

// UIElementType defines ui element types present in a case review flow.
// Can be used to map required annotation or comments against an ui element.
enum UIElementType {
  UI_ELEMENT_TYPE_UNSPECIFIED = 0;
  // UI Pages in case review flow.
  UI_ELEMENT_TYPE_PAGE = 1;
  // Review actions
  UI_ELEMENT_TYPE_ACTION = 2;
  // A section of a page
  UI_ELEMENT_TYPE_SECTION = 3;
}

