syntax = "proto3";

package risk.case_management.review;

import "api/risk/case_management/review/enums.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management/review";
option java_package = "com.github.epifi.gamma.api.risk.case_management.review";

// comment is the unstructured response comes from the analyst against the entity
// this will be against the entity and comment type
message Comment {

  // primary key for the comment
  string id = 1;

  // against which entity the input should get created
  ReviewEntityType entity_type = 2 [(validate.rules).enum = {not_in: [0]}];

  // entity id against which the comment is created
  string entity_id = 3 [(validate.rules).string.min_len = 1];

  // comment type defined for the library, it can be field, all or some specific type like assignment tags etc.
  // This is tightly coupled with the entity type and need to add into the oneof with the addition of new entity type
  CommentType comment_type = 4;

  // comment input by analyst
  string comment = 7 [(validate.rules).string.min_len = 4];
  // comment added by for audit
  string added_by_email = 8 [(validate.rules).string.min_len = 1];
  // created at
  google.protobuf.Timestamp created_at = 9;
  // updated at
  google.protobuf.Timestamp updated_at = 10;
  // deleted at
  google.protobuf.Timestamp deleted_at = 11;

  // case id against which the comment has been added
  string case_id = 12;
}

// UserCommentType defines the allowed user comment type
enum UserCommentType {

  USER_COMMENT_TYPE_UNSPECIFIED = 0;

  USER_COMMENT_TYPE_ALL = 1;
}

// TxnCommentType defines the allowed transaction comment type
enum TxnCommentType {
  TXN_COMMENT_TYPE_UNSPECIFIED = 0;

  TXN_COMMENT_TYPE_ALL = 1;
}

// LivenessCommentType defines the allowed Liveness comment type
enum LivenessCommentType {

  LIVENESS_COMMENT_TYPE_UNSPECIFIED = 0;

  LIVENESS_COMMENT_TYPE_ALL = 1;
}

// CaseCommentType defines the allowed Liveness comment type
enum CaseCommentType {

  CASE_COMMENT_TYPE_UNSPECIFIED = 0;

  CASE_COMMENT_TYPE_ALL = 1 [deprecated = true];
  // Comment type to be used by default for any adhoc comments on the case
  // can be used in cases where comment is not related to a particular use case
  CASE_COMMENT_TYPE_REMARKS = 2;
  // Investigation notes to be saved against the case to back up the verdict given OR
  // to sharing context for further reviews
  CASE_COMMENT_TYPE_INVESTIGATION_NOTES = 3;
  // If more information is required from the user and case needs to be moved to outbound call flow,
  // questions to be asked can be added with this comment type
  CASE_COMMENT_TYPE_USER_INFO_QUESTIONS = 4;
  // Comment type to be used for capturing user response for the questions in the outbound call flow
  CASE_COMMENT_TYPE_USER_RESPONSE = 5;
  // Comment type to be used for snooze case comments by analysts
  CASE_COMMENT_TYPE_SNOOZE = 6;
  // comment type to be used when there are multiple actions against the case are possible
  // and out of which system picks the best actions to be picked.
  // Not keeping the system in the name as it can be manually triggered as well.
  CASE_COMMENT_TYPE_ACTION_DEDUPE = 7;
  // comment type to be used when system has decided to exclude the actor from certain auto action
  CASE_COMMENT_TYPE_EXCLUSION = 8;
  // comment type to be used when any communication is sent to user in context of a case.
  CASE_COMMENT_TYPE_COMMS = 9;
}

enum FacematchCommentType {
  FACEMATCH_COMMENT_TYPE_UNSPECIFIED = 0;

  FACEMATCH_COMMENT_TYPE_REMARKS = 1;
}

// AfuCommentType defines comment type against afu entity
enum AfuCommentType {
  AFU_COMMENT_TYPE_UNSPECIFIED = 0;
}

// LEACommentType defines comment type against lea complaint entity
enum LEAComplaintCommentType {
  LEA_COMPLAINT_COMMENT_TYPE_UNSPECIFIED = 0;
}

// AlertCommentType defines comment type against alert entity
enum AlertCommentType {
  ALERT_COMMENT_TYPE_UNSPECIFIED = 0;

  ALERT_COMMENT_TYPE_ALERT_HANDLING_REMARKS = 1;
}

// comment query is the abstraction over the possible queries we would want to fetch the comments from system
message CommentQuery {

  review.ReviewEntityType entity_type = 1 [(validate.rules).enum = {not_in: [0]}];

  string entity_id = 2 [(validate.rules).string.min_len = 1];

  CommentType comment_type = 3;
}

// All non-empty filters will be applied with AND condition.
// At least one filter should be present.
message CommentFilters {
  // CAUTION: only a single query is supported.
  repeated review.CommentQuery queries = 1;

  // It Can be used to fetch all comments against review entities linked to the actor and comment added in context to a case.
  // e.g., liveness comments on actor's liveness video, case comments, actor's afu attempt comments or
  // transaction comments.
  string actor_id = 2;
}

// possible comment type for the comments
message CommentType {
  oneof comment_type {
    UserCommentType user_comment_type = 3;

    TxnCommentType txn_comment_type = 4;

    LivenessCommentType liveness_comment_type = 5;

    CaseCommentType case_comment_type = 6;

    FacematchCommentType facematch_comment_type = 7;

    AfuCommentType afu_comment_type = 8;

    LEAComplaintCommentType lea_complaint_comment_type = 9;
  }
}
