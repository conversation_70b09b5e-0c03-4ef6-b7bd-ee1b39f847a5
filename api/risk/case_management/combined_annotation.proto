syntax = "proto3";

package risk.case_management;

import "api/risk/case_management/review/allowed_annotation.proto";
import "api/risk/case_management/review/annotation.proto";
import "api/risk/case_management/review/enums.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/risk/case_management";
option java_package = "com.github.epifi.gamma.api.risk.case_management";

message CombinedAnnotation {

  risk.case_management.review.Annotation annotation = 1;

  risk.case_management.review.AllowedAnnotation allowed_annotation = 2;
}

// AnnotationQuery object can be used to query the annotations from the db.
// It can be queried against the entity_id.
// optionally it can be queried against the allowed_annotation_id to get finer control.
message AnnotationQuery {

  // against which entity the annotations gets queried
  risk.case_management.review.ReviewEntityType entity_type = 1 [(validate.rules).enum = {not_in: [0]}];

  // entity id against which the annotation is queried
  string entity_id = 2 [(validate.rules).string.min_len = 1];

  // allowed annotation id to get the results
  string allowed_annotation_id = 3;
}

// All non-empty filters will be applied with AND condition.
// At least one filter should be present.
message AnnotationFilters {
  // CAUTION: only a single query is supported.
  repeated AnnotationQuery queries = 1;

  // It Can be used to fetch all annotations against review entities linked to the actor and annotation added in context to a case.
  // e.g., liveness annotations on actor's liveness video, case annotations, actor's afu attempt annotations or
  // transaction annotations.
  string actor_id = 2;
}
