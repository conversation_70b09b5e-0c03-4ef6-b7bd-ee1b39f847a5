syntax = "proto3";

package api.rms.manager;

import "api/rms/manager/rule.proto";
import "api/rms/orchestrator/event/event.proto";
import "api/rms/ui/client.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/device.proto";
import "google/protobuf/field_mask.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/rms/manager";
option java_package = "com.github.epifi.gamma.api.rms.manager";

/*
 Rule Manager is the store for RMS, all the defined rules and related db entities is managed by RuleManager
 Rule Manager also manages all the rules subscribed by users
 APIs for accessing these data is exposed by RuleManager service
*/
service RuleManager {
  // creates a new rule in the system.
  // Every rule should contain a set of actions to be triggered on successful evaluation of the condition
  rpc CreateNewRule (CreateNewRuleRequest) returns (CreateNewRuleResponse) {}

  // Returns existing rule by ruleId
  rpc GetRuleById (GetRuleByIdRequest) returns (GetRuleByIdResponse) {}

  // Returns map of ruleId and rule for given ruleIds
  rpc GetRulesByIds (GetRulesByIdsRequest) returns (GetRulesByIdsResponse) {}

  // updates existing rule, returns the updated instance of the rule
  rpc UpdateRule (UpdateRuleRequest) returns (UpdateRuleResponse) {}

  // delete existing rule based on ruleId
  rpc DeleteRule (DeleteRuleRequest) returns (DeleteRuleResponse) {}

  // returns list of all the rules defined at RMS for requested client
  rpc GetRulesForClient (GetRulesForClientRequest) returns (GetRulesForClientResponse) {}

  // subscribes rule for a user
  // while subscribing to a rule, user should provide input to all the user defined parameters for the rule
  // once subscription, whenever RMS receives a relevant event it triggers the rule to be executed
  // "client_request_id" field is optional and to be sent if we want idempotency for the request when we are calling this RPC from workflows/orchestrator
  // if not sent, then the request will not be idempotent.
  rpc SubscribeRule (SubscribeRuleRequest) returns (SubscribeRuleResponse) {}

  // updates user defined values for already subscribed rules
  // updates valid_till date for a subscription. i.e may extend or shorten the subscription expiration
  rpc UpdateRuleSubscription (UpdateRuleSubscriptionRequest) returns (UpdateRuleSubscriptionResponse) {}

  // updates state of a subscription
  // setting a state as Inactive for a subscription will stop trigger execution of rule
  // only Active rules are eligible to be executed
  // [DEPRECATED]: USE UpdateSubscriptionState which handles all the subscription states
  rpc PauseResumeSubscription (PauseResumeSubscriptionRequest) returns (PauseResumeSubscriptionResponse) {
    option deprecated = true;
  }


  // updates state of a subscription
  // setting a state as Inactive or Closed for a subscription will stop trigger execution of rule
  // Closed is a terminal state, changing from closed to Active or Inactive is not permitted
  // only Active rules are eligible to be executed
  rpc UpdateSubscriptionState (UpdateSubscriptionStateRequest) returns (UpdateSubscriptionStateResponse) {}

  // unsubscribes rule for a user
  rpc UnsubscribeRule (UnsubscribeRuleRequest) returns (UnsubscribeRuleResponse) {}

  // get rule subscribed by user by subscription id
  rpc GetSubscriptionById (GetSubscriptionByIdRequest) returns (GetSubscriptionByIdResponse) {}

  // get rules subscribed by user by subscription ids
  rpc GetRulesForSubscriptions (GetRulesForSubscriptionsRequest) returns (GetRulesForSubscriptionsResponse) {}

  // UI specific APIs
  // returns all the rules subscribed by user for specific client
  rpc GetSubscriptionsByActorId (GetSubscriptionsByActorIdRequest) returns (GetSubscriptionsByActorIdResponse) {}

  // get just active subscriptions for an Actor
  rpc GetActiveSubscriptionsByActorId (GetActiveSubscriptionsByActorIdRequest) returns (GetActiveSubscriptionsByActorIdResponse) {}

  // get count of active subscriptions for an Actor
  rpc GetActiveSubsCount (GetActiveSubsCountRequest) returns (GetActiveSubsCountResponse) {}

  // get map of state and count of subscriptions in that state for an Actor
  rpc GetSubsCount (GetSubsCountRequest) returns (GetSubsCountResponse) {}

  // get subscriptions by actor for list of rules
  rpc GetSubscriptionsByActorForRules (GetSubscriptionsByActorForRulesRequest) returns (GetSubscriptionsByActorForRulesResponse) {}

  // Returns count of user subscriptions for a particular rule
  rpc GetSubscribersCountForRule (GetSubscribersCountForRuleRequest) returns (GetSubscribersCountForRuleResponse) {}

  // returns:
  // 1. total number of NOT CLOSED subscriptions by a given actor for this rule
  // 2. total number of NOT CLOSED subscriptions across all actors. Multiple subscriptions by the same actor are also counted.
  rpc GetSubscriptionCountForRule (GetSubscriptionCountForRuleRequest) returns (GetSubscriptionCountForRuleResponse) {}

  // returns:
  // 1. total number of ACTIVE subscriptions by a given actor for this rule
  // 2. total number of ACTIVE subscriptions across all actors. Multiple subscriptions by the same actor are also counted.
  rpc GetActiveSubscriptionCountForRule (GetActiveSubscriptionCountForRuleRequest) returns (GetActiveSubscriptionCountForRuleResponse) {}

  // returns map of rule and its subscription count:
  // Key of the map will be rule_id
  // Value will contain
  // 1. total number of ACTIVE subscriptions by a given actor for a rule
  // 2. total number of ACTIVE subscriptions across all actors. Multiple subscriptions by the same actor are also counted.
  rpc GetActiveSubscriptionCountForRules (GetActiveSubscriptionCountForRulesRequest) returns (GetActiveSubscriptionCountForRulesResponse) {}

  // returns:
  // Number of times a rule has been executed across actors
  rpc GetExecutionsCountForRules (GetExecutionsCountForRulesRequest) returns (GetExecutionsCountForRulesResponse) {}

  // returns all the subscription versions for requested subscriptions, and requested time range
  // if startTime is nil, returns all versions till endTime
  // if endTime is nil, returns all versions from startTime till present
  // if startTime and endTime both are nil, returns all the versions
  rpc GetAllSubscriptionVersions (GetAllSubscriptionVersionsRequest) returns (GetAllSubscriptionVersionsResponse) {}

  // Client rule specific APIs
  // specific handling for client rules needs to be done as condition evaluation is happening at client
  // reason to have condition processing at client is because it is not as per our policies to bring client's information at server
  // returns 1) List of apps to be tracked 2) conditions to be evaluated
  rpc GetClientMetricsAndPendingExecutions (GetClientMetricsAndPendingExecutionsRequest) returns (GetClientMetricsAndPendingExecutionsResponse) {}

  // GetNewRulesInfoForUser returns information for specific new rules for a user
  // a rule is considered new for a user if it is not older than 7 days and user has not yet subscribed to it
  rpc GetNewRulesInfoForUser (GetNewRulesInfoForUserRequest) returns (GetNewRulesInfoForUserResponse) {}

  // returns list of home cards, based on filter states provided
  rpc GetHomeCards (GetHomeCardsRequest) returns (GetHomeCardsResponse) {}

  // creates a new home card for request
  rpc CreateHomeCard (CreateHomeCardRequest) returns (CreateHomeCardResponse) {}

  // update a home card's weight, state
  // api to be used by CX service only, no actor interaction required
  rpc UpdateHomeCard (UpdateHomeCardRequest) returns (UpdateHomeCardResponse) {}

  // creates a new collection for requested object
  rpc CreateCollection (CreateCollectionRequest) returns (CreateCollectionResponse) {}

  // returns list of collections based on requested filters
  rpc GetCollections (GetCollectionsRequest) returns (GetCollectionsResponse) {}

  // returns collection information
  // and list of tags if
  rpc GetCollectionInfo (GetCollectionInfoRequest) returns (GetCollectionInfoResponse) {}

  // updates existing collection, returns the updated instance of the collection
  rpc UpdateCollection (UpdateCollectionRequest) returns (UpdateCollectionResponse) {}

  // returns list of rules for tagId
  rpc GetRules (GetRulesRequest) returns (GetRulesResponse) {}

  rpc UpdateRulesWeightage (UpdateRulesWeightageRequest) returns (UpdateRulesWeightageResponse) {}

  // my rules page lists overview of all the subscriptions for user
  // subscriptions are aggregated based on either tag or rule
  // GetMyRulesPageInfo returns list of cards showing aggregated summary of subscriptions
  // if subscriptions are aggregated based on tags, client should use GetRecentSubscriptionsForTag for next page
  // and for aggregation based on rules, client should use GetAllSubscriptionsForRule for next page
  rpc GetMyRulesPageData (GetMyRulesPageDataRequest) returns (GetMyRulesPageDataResponse) {}

  // GetSubscriptionSummariesPageData returns static data, for the subscription summary page
  rpc GetSubscriptionSummariesPageData (GetSubscriptionSummariesPageDataRequest) returns (GetSubscriptionSummariesPageDataResponse) {}

  // GetSubscriptionSummariesForTag returns recent 3/x subscriptions for a rule and it lists all the rules for the requested tag
  rpc GetSubscriptionSummariesForTag (GetSubscriptionSummariesForTagRequest) returns (GetSubscriptionSummariesForTagResponse) {}

  // GetAllSubscriptionsPageData returns static data on all subscriptions page
  rpc GetAllSubscriptionsPageData (GetAllSubscriptionsPageDataRequest) returns (GetAllSubscriptionsPageDataResponse) {}

  // user can decide to list subscriptions from my rules page
  // since my rules page provides overview, in aggregated form
  // GetAllSubscriptionsForRule returns overview of specific subscriptions for requested rule
  rpc GetAllSubscriptionsForRule (GetAllSubscriptionsForRuleRequest) returns (GetAllSubscriptionsForRuleResponse) {}

  // returns count of active rules
  rpc GetActiveRulesCount (GetActiveRulesCountRequest) returns (GetActiveRulesCountResponse) {}

  // if the actor is accessing his profile for the first time, it will be created
  // else returns rms profile of an existing actor, internal server error if actor not present
  // Profile of an actor can be any specific data to a particular actor, which is required to be maintained
  // in order to provide a customised experience
  // eg: Switch cards content (this can be statistical data, suggestions for new rules from users pref) based on page visits.
  rpc GetProfile (GetProfileRequest) returns (GetProfileResponse) {}

  // any updates to the actor's profile need to be carried out using this api
  // e.g: update the last accessed timestamp pf the actor using this api
  rpc UpdateProfile (UpdateProfileRequest) returns (UpdateProfileResponse) {}

  // archived subscriptions for a tag should be listed on a single page
  // GetAllSubscriptionsForTag returns list of subscriptions
  rpc GetAllSubscriptionsForTag (GetAllSubscriptionsForTagRequest) returns (GetAllSubscriptionsForTagResponse) {}

  // Bulk update API
  // updates state for all subscriptions for requested ruleId
  rpc UpdateSubscriptionsStateForRule (UpdateSubscriptionsStateForRuleRequest) returns (UpdateSubscriptionsStateForRuleResponse) {}

  // Provides summary of subscription history
  // returns
  // 1. Subscribed rule description
  // 2. Statistics related to rule executions
  // 4. Actions for corresponding subscription
  // 3. recent rule actions
  rpc GetSubscriptionInfo (GetSubscriptionInfoRequest) returns (GetSubscriptionInfoResponse) {}

  // ArchiveRuleAndSubscriptions will update rule state to INACTIVE and underlying subscriptions state to CLOSED
  // this API will be exposed from sherlock, to archive all rules and subscriptions for a particular tournament when it ends
  rpc ArchiveRuleAndSubscriptions (ArchiveRuleAndSubscriptionsRequest) returns (ArchiveRuleAndSubscriptionsResponse) {}

  // GetUserStats will include user stats related to rules, subscription, tags, collection or anythig  related to RMS.
  // Stats will be computed based on the field masks set in the request. Any future requirements can be extended in the same
  // RPC with new field masks
  rpc GetUserStats (GetUserStatsRequest) returns (GetUserStatsResponse) {}

  // GetActiveSubscriptionsForTimeInterval returns versions of subscriptions active throughout the mentioned duration (start_time, end_time)
  // this API also supports filtering on param values
  // NOTE: param value filters can only be used along with actorId
  rpc GetActiveSubscriptionsForTimeInterval (GetActiveSubscriptionsForTimeIntervalRequest) returns (GetActiveSubscriptionsForTimeIntervalResponse) {}

  // returns data required to pass to rewards service for configuring different rewards
  // based on rule category, collection, tag etc
  // response is returned based on field_mask requested
  rpc GetRewardsRelatedData (GetRewardsRelatedDataRequest) returns (GetRewardsRelatedDataResponse) {}

  // updates execution state of a subscription
  // setting a state other than SUBSCRIPTION_EXECUTION_ALLOWED for a subscription will stop trigger execution of rule from BE
  // Only rules which have subscription state ACTIVE and Execution state as SUBSCRIPTION_EXECUTION_ALLOWED are eligible for execution
  rpc UpdateSubscriptionExecutionState (UpdateSubscriptionExecutionStateRequest) returns (UpdateSubscriptionExecutionStateResponse) {}

  // fetches all the subscriptions which has a particular execution state
  // subscriptions can be fetched on per actor basis as well which have the mentioned state
  rpc GetSubscriptionsByExecutionState (GetSubscriptionsByExecutionStateRequest) returns (GetSubscriptionsByExecutionStateResponse) {}

  // GetSubscriptionsForUpcomingExecutions returns the upcoming subscriptions which will be next executed for a particular actor
  // if you pass a particular rule category, it will return upcoming subscriptions for that rule category only
  // if field mask is nil, then complete RuleSubscription instances will be returned
  rpc GetSubscriptionsForUpcomingExecutions (GetSubscriptionsForUpcomingExecutionsRequest) returns (GetSubscriptionsForUpcomingExecutionsResponse) {}

  // GetActorsWithSubscriptions returns all the unique actors with the given RuleSubscriptionState
  // NOTE: this RPC returns list all eligible actors for the provided filters, so the response can grow huge with time
  rpc GetActorsWithSubscriptions (GetActorsWithSubscriptionsRequest) returns (GetActorsWithSubscriptionsResponse) {}

  // GetSubscriptionAmountInDateRange computes the amount of the recurring subscriptions (like invest the change) within the given date range.
  // for eg. If for date range (1/5/23 to 15/5/23), it consists of 15 daily rules 2 weekly and 1 monthly rule, the rpc computes the upcoming amounts
  // and returns the upcoming amount.
  // executions for shopping rules can not be predicted.
  rpc CalculateSubscriptionAmountInDateRange (CalculateSubscriptionAmountInDateRangeRequest) returns (CalculateSubscriptionAmountInDateRangeResponse);



  // CheckSubscriptionExists returns true if there is an existing subscription for requested filters
  // RPC is useful in checking if subscription exists for an actor for specified event type
  // if subscription does not exists, received packet can be dropped by fit system
  rpc CheckSubscriptionExists (CheckSubscriptionExistsRequest) returns (CheckSubscriptionExistsResponse) {}

  // SendProactiveFundAdditionNotification sends the notification to an actor with upcoming SIP amount < current balance
  // currently the logic is configured for the 1st and 15th of every month
  // 1. upcoming SIPs from 1st-15th
  // 2. upcoming SIPs from 16th to the end of the month
  rpc SendProactiveFundAdditionNotification (SendProactiveFundAdditionNotificationRequest) returns (SendProactiveFundAdditionNotificationResponse);

  // GetUpcomingExecutions returns list of expected executions for requested set of rules
  // all recurring rules (auto save, auto pay, auto invest) will be considered for execution, if no filter is passed in request
  // active recurring subscriptions are considered for computing upcoming executions
  rpc GetUpcomingExecutions (GetUpcomingExecutionsRequest) returns (GetUpcomingExecutionsResponse) {}
}

message GetUpcomingExecutionsRequest {
  // actorId for which event is received
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // [MANDATORY] start_date is the date from which the executions are required by caller
  google.type.Date start_date = 2 [(validate.rules).message.required = true];
  // [MANDATORY] end_date is the date till which the executions are required by caller
  google.type.Date end_date = 3 [(validate.rules).message.required = true];
  // [OPTIONAL] list of rule types for which executions is required by caller
  // for cases where all upcoming executions for a duration needs to be presented, rule_types can be nil
  // rule_types should be populated for specifying instrument, use case for this could be displaying upcoming activities on instrument specific pages
  // eg: AutoPay screen only requires auto pay activities to be visible to user
  repeated api.rms.manager.RuleTypeForSpecialHandling rule_types = 4;
}

message GetUpcomingExecutionsResponse {
  // rpc status
  rpc.Status status = 1;
  // list of upcoming executions
  // there can be multiple executions for one subscription
  // eg: If (start_time, end_time) range is 15 days, so there will be 15 entries for daily rules,
  repeated api.rms.manager.UpcomingExecution upcoming_executions = 2;
}

message CheckSubscriptionExistsRequest {
  // actorId for which event is received
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  // type of event for which rules should be considered
  api.rms.orchestrator.event.EventType event_type = 2;
  // time at which event occurred
  // if event_time is nil, runtime state of subscription is considered
  // [OPTIONAL]
  google.protobuf.Timestamp event_time = 3;
  // execution_state of the subscription
  api.rms.manager.SubscriptionExecutionState execution_state = 4;
  // filter for subscription state
  api.rms.manager.RuleSubscriptionState state = 5;
}

message CheckSubscriptionExistsResponse {
  // rpc status
  rpc.Status status = 1;
  // true if subscription exists for provided filters
  bool subscription_exists = 2;
}

message GetRewardsRelatedDataRequest {
  string subscription_id = 1;
  string actor_id = 2;
  google.protobuf.FieldMask field_mask = 3;
}

message GetRewardsRelatedDataResponse {
  rpc.Status status = 1;
  repeated Collection collections = 2;
  Profile profile = 3;
  Rule rule = 4;
}

message GetActiveSubscriptionsForTimeIntervalRequest {
  repeated string rule_ids = 1;
  string actor_id = 2;
  google.protobuf.Timestamp start_time = 3;
  google.protobuf.Timestamp end_time = 4;
  // OPTIONAL: filter on param values
  // Note: current implementation provides support for CRICKET_PLAYER & CRICKET_TEAM
  repeated ParamFilter param_filters = 5 [(validate.rules).repeated = {min_items: 1}];
}

message GetActiveSubscriptionsForTimeIntervalResponse {
  rpc.Status status = 1;
  // key of the map is ruleId and value is list of RuleSubscription objects
  map<string, Subscriptions> subscriptions_map = 2;

}

message GetSubscriptionInfoRequest {
  // subscription Id
  string sub_id = 1;
  // actor_id is required in order to prevent IDOR requests
  // service ensures subscription belongs to the requested actor
  string actor_id = 2 [(validate.rules).string.min_len = 1];
}

message GetSubscriptionInfoResponse {
  rpc.Status status = 1;
  Rule rule = 2;
  RuleSubscription subscription = 6;
  int64 total_executions = 7;
  google.protobuf.Timestamp last_executed_at = 8;
}

message ArchiveRuleAndSubscriptionsRequest {
  string rule_id = 1;
  // all associated rules for the tag will be updated with state INACTIVE
  // `tag_id` has higher preference than rule_id
  // i.e if tag_id is non-empty then rule_id field will be ignored
  string tag_id = 2;
  // all the subscriptions for tag_id or rule_id will be updated with state CLOSED
  bool archive_all_subs = 3;
  SubscriptionStateChangeReason reason = 4;
  SubscriptionStateChangeProvenance provenance = 5;
}

message ArchiveRuleAndSubscriptionsResponse {
  rpc.Status status = 1;
}

message UpdateSubscriptionsStateForRuleRequest {
  string rule_id = 1;
  RuleSubscriptionState target_state = 2;
  SubscriptionStateChangeReason reason = 3;
  SubscriptionStateChangeProvenance provenance = 4;
  // if state_filter is not nil, updates subscriptions with states present in filter_states
  repeated RuleSubscriptionState state_filter = 5;
  // if state_change_reason_filter is not nil, updates subscriptions with state_change_reason present in state_change_reason_filter
  repeated SubscriptionStateChangeReason state_change_reason_filter = 6;
}

message UpdateSubscriptionsStateForRuleResponse {
  rpc.Status status = 1;
}

message GetAllSubscriptionsForTagRequest {
  rpc.PageContextRequest page_context = 1;
  string tag_id = 2;
  string actor_id = 3;
  repeated RuleSubscriptionState sub_states = 4;
}

message GetAllSubscriptionsForTagResponse {
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  repeated Subscription subscriptions = 3;
}

message GetActiveRulesCountRequest {
  api.rms.orchestrator.event.RMSClient client = 1;
  string actor_id = 2;
  api.typesv2.common.Platform platform = 3;
  uint32 app_version = 4;
}

message GetActiveRulesCountResponse {
  rpc.Status status = 1;
  int32 count = 2;
}

message GetAllSubscriptionsPageDataRequest {
  string rule_id = 1;
  string actor_id = 2;
  api.rms.orchestrator.event.RMSClient client = 3;
  repeated RuleSubscriptionState sub_states = 4;
}

message GetAllSubscriptionsPageDataResponse {
  rpc.Status status = 1;
  string page_title = 2;
  string page_sub_title = 3;
  string rule_title = 4;
  string rule_sub_title = 5;
  string amount_saved_str = 6;
  // user is allowed to add new subscription for rule from all subscriptions page
  CTA new_sub_cta = 7;
  CardDisplayInfo display_info = 8;
}

message GetSubscriptionSummariesPageDataRequest {
  string tag_id = 1;
  // my rules page provides options to filter rules on rule type (Auto Save, Auto Pay etc)
  // rule_type will be used to filter rules based on requested type
  RuleCategory rule_type = 2;
  string actor_id = 3;
  repeated RuleSubscriptionState sub_states = 4;
}

message GetSubscriptionSummariesPageDataResponse {
  rpc.Status status = 1;
  string page_title = 2;
  string page_sub_title = 3;
  // user is allowed to add new subscription from summary page
  CTA new_sub_cta = 4;
  // total amount saved or invested or paid
  google.type.Money total_amount = 5;
  // no of subscriptions for the requested filter
  int32 subs_count = 6;
}

message GetAllSubscriptionsForRuleRequest {
  rpc.PageContextRequest page_context = 1;
  string actor_id = 2;
  string rule_id = 3;
  repeated RuleSubscriptionState sub_states = 4;
}

message GetAllSubscriptionsForRuleResponse {
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  repeated Subscription subscriptions = 3;
  Rule rule = 4;
}

message GetSubscriptionSummariesForTagRequest {
  rpc.PageContextRequest page_context = 1;
  string actor_id = 2;
  string tag_id = 3;
  // my rules page provides options to filter rules on rule type (Auto Save, Auto Pay etc)
  // rule_type will be used to filter rules based on requested type
  RuleCategory rule_category = 4;
  repeated RuleSubscriptionState sub_states = 5;
}

message GetSubscriptionSummariesForTagResponse {
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  // MySubscriptionsForRule contains list of subscription per rule
  // list of MySubscriptionsForRule means that response will contain subscriptions for multiple rules
  repeated SubscriptionsForRule recent_subs = 3;
  // map of all rules corresponding to tag
  map<string, Rule> rules_map = 4;
}

message GetMyRulesPageDataRequest {
  rpc.PageContextRequest page_context = 1;
  string actor_id = 2;
  // my rules page provides options to filter rules on rule type (Auto Save, Auto Pay etc)
  // rule_type will be used to filter rules based on requested type
  RuleCategory rule_category = 3;
  repeated RuleSubscriptionState sub_states = 4;
}

message GetMyRulesPageDataResponse {
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  // aggregated summary of subscribed rules based, aggregation can be based on rule or tag
  repeated AggregatedSubscriptionsSummary agg_sub_summary = 3;
  int32 archived_subs_count = 4;
}

message UpdateRulesWeightageRequest {
  map<string, int32> rule_wise_weight = 1;
}

message UpdateRulesWeightageResponse {
  rpc.Status status = 1;
}

message GetRulesRequest {
  // tagId is having higher preference here,
  // if tag_id is not empty, rules returned will be for the tagId
  string tag_id = 1;
  // if tag_id is empty, it will be assumed that the collection has rules as their children
  string collection_id = 2;
  rpc.PageContextRequest page_context = 3;
  // if empty, do not populate display related fields
  repeated DisplayDataFieldMask field_masks = 4;
  // required to find out user groups and fetch rules accordingly
  string actor_id = 5;
  // optional: if empty, this rpc shows all rules
  RuleCategory rule_category = 6;
  // platform checks to return only specific rules for the platform
  api.typesv2.common.Platform platform = 14;
  // app version checks to return only specific rule valid for version
  uint32 app_version = 15;
}

message GetRulesResponse {
  rpc.Status status = 1;
  repeated Rule rules = 2;
  rpc.PageContextResponse page_context = 3;
}

message CreateCollectionRequest {
  Collection collection = 1;
}

message CreateCollectionResponse {
  rpc.Status status = 1;
  Collection collection = 2;
}

message GetCollectionInfoRequest {
  string collection_id = 1;
  string actor_id = 2;
  // platform checks to return only specific rules for collections
  api.typesv2.common.Platform platform = 14;
  // app version checks to return only specific details of supported collections
  uint32 app_version = 15;

  // optional:
  RuleCategory rule_category = 3;
}

message GetCollectionInfoResponse {
  rpc.Status status = 1;
  // collection related information. i.e. description, tag, background color & img etc
  Collection collection = 2;
  // if the collection contains tags as children
  // if children are rules directly, this list will be empty
  repeated RuleTag tags = 3;
}

message CollectionChildren {
  oneof children {
    // if tag_wise_rules is not nil
    // tags & respective rules should be listed on UI
    Tags tags = 2;
    // in case tag_wise_rules is nil
    // all the rules inside rules_list should be listed without any tag on collections page
    Rules rules = 3;
  }
}

message Tags {
  // map of tagId and Tag display info
  map<string, RuleTag> tags_map = 1;
  // default selected tag
  string selected_tag = 2;
  // list of rule tags
  repeated RuleTag tags_list = 3;
}

message Rules {
  // list of rules
  repeated Rule rules = 1;
}

message GetCollectionsRequest {
  google.protobuf.Timestamp range_start_time = 1;
  google.protobuf.Timestamp range_end_time = 2;
  repeated CollectionState state_filter = 3;
  rpc.PageContextRequest page_context = 4;
  // fetches collection with featured flag true
  // do not use pagination, as feature collections are expected to be few in numbers
  bool featured_collections_only = 5;
  // fetched collections will be of type mentioned in `type` field, unless UNSPECIFIED
  CollectionType type = 6;
  string actor_id = 7;
  // platform checks to return only specific collections
  api.typesv2.common.Platform platform = 14;
  // app version checks to return only specific collections
  // and rule related info for that collection
  uint32 app_version = 15;
}

message GetCollectionsResponse {
  rpc.Status status = 1;
  repeated Collection collections = 2;
  rpc.PageContextResponse page_context = 3;
}

message CreateHomeCardRequest {
  HomeCard card = 1;
}

message CreateHomeCardResponse {
  rpc.Status status = 1;
  HomeCard card = 2;
}

message GetHomeCardsRequest {
  repeated HomeCardState filter_states = 1;
  // fetching homecards for a particular actor
  string actor_id = 2;
  // platform checks to return only specific homecards for the platform
  api.typesv2.common.Platform platform = 14;
  // app version checks to return only specific homecards valid for version
  uint32 app_version = 15;
}

message GetHomeCardsResponse {
  rpc.Status status = 1;
  repeated HomeCard cards = 2;
}

message UpdateHomeCardRequest {
  HomeCard card = 1;
}

message UpdateHomeCardResponse {
  rpc.Status status = 1;
}

message GetNewRulesInfoForUserRequest {
  string actor_id = 1;
}

message GetNewRulesInfoForUserResponse {
  rpc.Status status = 1;
  // map of id to new rules for actor
  map<string, Rule> new_rules = 2 [deprecated = true];
  // list of new rules for actor
  repeated Rule rules = 3;
}

message GetClientMetricsAndPendingExecutionsRequest {
  string actor_id = 1;
}

message GetClientMetricsAndPendingExecutionsResponse {
  rpc.Status status = 1;
  // apps_to_be_tracked will be determined by the subscriptions of the particular user
  repeated MetricType apps_to_be_tracked = 2;
  // map of executionId and ConditionData
  map<string, ConditionData> conditions_to_be_evaluated = 3;
}

message CreateNewRuleRequest {
  Rule rule = 1;
}

message CreateNewRuleResponse {
  rpc.Status status = 1;
  Rule rule = 2;
}

message GetRuleByIdRequest {
  string rule_id = 1;
  // if empty, do not populate display related fields
  repeated DisplayDataFieldMask field_masks = 2;
}

enum DisplayDataFieldMask {
  REQUIRED_DATA_FIELD_MASK_UNSPECIFIED = 0;
  RULE_DISPLAY_INFO = 1;
  TAG_DISPLAY_INFO = 2;
  POSSIBLE_PARAM_VALUES = 3;
  POSSIBLE_PARAM_VALUES_AND_SELECTOR_CTA = 4;
}

message GetRuleByIdResponse {
  rpc.Status status = 1;
  Rule rule = 2;
  ui.RuleDisplayInfo rule_display_info = 3 [deprecated = true];
  repeated ui.TagsDisplayInfo tag_display_info = 4 [deprecated = true];
  ui.ParamValues param_values = 5 [deprecated = true];
}

message GetRulesByIdsRequest {
  repeated string rule_ids = 1;
  // if empty, do not populate display related fields
  repeated DisplayDataFieldMask field_masks = 2;
}

message GetRulesByIdsResponse {
  rpc.Status status = 1;
  map<string, Rule> rules = 2;
  map<string, ui.RuleDisplayInfo> rule_display_map = 3 [deprecated = true];
  map<string, ui.TagDisplayInfos> tag_display_map = 4 [deprecated = true];
  map<string, ui.ParamValues> params_display_map = 5 [deprecated = true];
}

message UpdateRuleRequest {
  string rule_id = 1;
  Rule rule = 2;
  // The fields that need to be updated. This is repeated because a single update
  // call can update multiple fields. The update request ignores any fields that aren't specified in the field mask,
  // leaving them with their current values.
  repeated RuleFieldMask update_fields = 3;
  // DISCLAIMER: using this flag might trigger bulk update
  // Closes all subscriptions for ruleId
  bool close_all_subscriptions = 14;
}

message UpdateRuleResponse {
  rpc.Status status = 1;
  Rule rule = 2;
}

message DeleteRuleRequest {
  string rule_id = 1;
}

message DeleteRuleResponse {
  rpc.Status status = 1;
}

message GetRulesForClientRequest {
  api.rms.orchestrator.event.RMSClient client = 1;
  // page context for pagination
  rpc.PageContextRequest page_context = 2;
  // empty states list will indicate api to consider rules of all states.
  repeated RuleState states = 3;
  // if empty, do not populate display related fields
  repeated DisplayDataFieldMask field_masks = 4;
  // rules with specified categories will be returned
  // if array is empty or nil, no filter on category is applied
  repeated RuleCategory categories = 5;
  string actor_id = 6;
  api.typesv2.common.Platform platform = 7;
  uint32 app_version = 8;
  repeated RuleTypeForSpecialHandling rule_types = 9;
}

message GetRulesForClientResponse {
  rpc.Status status = 1;
  repeated Rule rules = 2;
  // page context response
  rpc.PageContextResponse page_context = 3;
  map<string, ui.RuleDisplayInfo> rule_display_map = 4 [deprecated = true];
  map<string, ui.TagDisplayInfos> tag_display_map = 5 [deprecated = true];
  map<string, ui.ParamValues> params_display_map = 6 [deprecated = true];
}

message SubscribeRuleRequest {
  RuleSubscription subscription_data = 1;
}

message SubscribeRuleResponse {
  rpc.Status status = 1;
  RuleSubscription subscription_data = 2;
}

message UpdateRuleSubscriptionRequest {
  string rule_subscription_id = 1;
  // partial update for user defined rules is not allowed
  // for eg: {"amount":1000, "Merchant":"Swiggy"} is already configured
  // Invalid request -> {"Merchant":"Zomato"} or {"amount":500}
  // Valid request -> {"amount":500, "Merchant":"Zomato"}
  RuleParamValues user_defined_values = 2;
  google.protobuf.Timestamp valid_till = 3;
  // list of the fields which will be updated by the request
  // for eg: if i just want to update userDefinedValues then, ["userDefinedValues"].
  // in case i want both the values to be updated then, ["userDefinedValues", "validTill"]
  repeated RuleSubscriptionFieldMask updated_fields = 4;
  SubscriptionExpiryData subscription_expiry_data = 5;
  // requested state for subscription
  RuleSubscriptionState state = 6;
  // this will be considered for update only if state is being changed
  SubscriptionStateChangeReason state_change_reason = 7;
  // source of request (App, Internal, Sherlock)
  SubscriptionStateChangeProvenance state_change_provenance = 8;
}

message UpdateRuleSubscriptionResponse {
  rpc.Status status = 1;
  RuleSubscription subscription_data = 2;
}

message PauseResumeSubscriptionRequest {
  string subscription_id = 1;
  RuleSubscriptionState state = 2;
}

message PauseResumeSubscriptionResponse {
  rpc.Status status = 1;
  string subscription_id = 2;
  RuleSubscriptionState state = 3;
}

message UpdateSubscriptionStateRequest {
  string subscription_id = 1;
  RuleSubscriptionState state = 2;
  SubscriptionStateChangeReason state_change_reason = 3;
  // source of request (App, Internal, Sherlock)
  SubscriptionStateChangeProvenance state_change_provenance = 4;
  // list of Ids will be having higher priority
  // NOTE: Response will not contain list of subscriptions if bulk state update is requested,
  //  it is recommended to use subscription_id field if object is required
  repeated string sub_ids = 5;
  // actor_id is required in order to prevent IDOR requests
  // service ensures subscription belongs to the requested actor
  string actor_id = 6 [(validate.rules).string.min_len = 1];
}

message UpdateSubscriptionStateResponse {
  rpc.Status status = 1;
  RuleSubscription subscription = 2;
}

message UnsubscribeRuleRequest {
  string subscription_id = 1;
}

message UnsubscribeRuleResponse {
  rpc.Status status = 1;
}

message GetSubscriptionByIdRequest {
  string subscription_id = 1;
  // if empty, do not populate display related fields
  repeated DisplayDataFieldMask field_masks = 2;
}

message GetSubscriptionByIdResponse {
  rpc.Status status = 1;
  RuleSubscription subscription_data = 2;
  Rule rule = 3;
  ui.RuleDisplayInfo rule_display_info = 4 [deprecated = true];
  repeated ui.TagsDisplayInfo tag_display_info = 5 [deprecated = true];
  ui.ParamValues param_values = 6 [deprecated = true];
}


message GetRulesForSubscriptionsRequest {
  repeated string subscription_ids = 1;
  // Empty field mask will populate all fields in response.
  // If "subs_to_rules_map" is present, subs_to_rules_map will be populated
  // If "rules_map" is present then rules_map will be populated
  google.protobuf.FieldMask field_mask = 2;
}

message GetRulesForSubscriptionsResponse {
  rpc.Status status = 1;
  // map <subscriptionId, ruleId>
  map<string, string> subs_to_rules_map = 2;
  // map <ruleId, Rule>
  map<string, Rule> rules_map = 3;
}

message SubscriptionDetails {
  RuleSubscription subscription_data = 1;
  Rule rule = 2;
}

message GetSubscriptionsByActorIdRequest {
  string actor_id = 1;
  api.rms.orchestrator.event.RMSClient client = 2;
  // page context for pagination
  // By default Subscriptions are ordered in most recently created first.
  // But by setting update_after/update_before filter, subscriptions will be in most recently updated first order.
  // optional for now. Will be made as required after
  rpc.PageContextRequest page_context = 3;

  // Filters:
  // All filters are combined as AND conditions
  // -> Time Range
  //      created_after/created_before takes precedence over updated_after/updated_before
  //      If created_after/created_before is specified, updated_after/updated_before will not
  //      not be considered for filtering even if set and ordering will be based on created time of subscription.
  // -> Subscription state
  //      if not specified, Subscriptions of all states will be included in response.

  // optional - Filter param
  repeated RuleSubscriptionState states = 4;

  // optional - Filter param
  google.protobuf.Timestamp created_after = 5;
  // optional - Filter param
  google.protobuf.Timestamp created_before = 6;

  // optional - Filter param
  google.protobuf.Timestamp updated_after = 7;
  // optional - Filter param
  google.protobuf.Timestamp updated_before = 8;
}

message GetSubscriptionsByActorIdResponse {
  rpc.Status status = 1;
  //TODO(sakthi) Do we need actor_id and client in response
  string actor_id = 2;
  api.rms.orchestrator.event.RMSClient client = 3;
  repeated RuleSubscription subscription_data = 4;
  // page context response
  rpc.PageContextResponse page_context = 5;
}

message GetActiveSubscriptionsByActorIdRequest {
  string actor_id = 1;
  api.rms.orchestrator.event.RMSClient client = 2;
}

message GetActiveSubscriptionsByActorIdResponse {
  rpc.Status status = 1;
  // params received in request are not required to be passed back
  string actor_id = 2 [deprecated = true];
  api.rms.orchestrator.event.RMSClient client = 3 [deprecated = true];
  repeated RuleSubscription subscription_data = 4;
}

message GetActiveSubsCountRequest {
  string actor_id = 1;
  api.rms.orchestrator.event.RMSClient client = 2;
}

message GetActiveSubsCountResponse {
  rpc.Status status = 1;
  // the number of active subscriptions of an actor
  int64 subscription_count = 4;
}

message GetSubsCountRequest {
  string actor_id = 1;
  api.rms.orchestrator.event.RMSClient client = 2;
  // OPTIONAL: if user want to get count of subscriptions for specific rules
  repeated string rule_ids = 3;
}

message GetSubsCountResponse {
  rpc.Status status = 1;
  // map of subscription state to the total number of subscriptions for corresponding state for requested actor
  // if rule_ids is populated then, value will contain count of subscriptions across requested ruleIds else across all rules in the system
  map<string, int64> subscription_count = 2;
  // key is the ruleId
  // map<rule_id, map<sub_state, count>>
  map<string, StateWiseSubsCount> subs_count_per_rule = 3;
  // map<category, map<sub_state, count>>
  map<string, StringToInt64Map> subs_count_per_category_and_state = 4;
}
message StateWiseSubsCount {
  // key is subscription state
  map<string, int64> subs_count_per_state = 1;
}

// since proto does not allow defining map<string, map<string, int64>>
// StringToInt64Map is a generic map which can be used as in nested map
message StringToInt64Map {
  map<string, int64> map = 1;
}

message GetSubscribersCountForRuleRequest {
  string ruleId = 1 [deprecated = true];
  repeated string rule_ids = 2;
}

message GetSubscribersCountForRuleResponse {
  rpc.Status status = 1;
  string rule_id = 2 [deprecated = true];
  // the number of unique actors that have subscribed to the rules
  int64 user_count = 3;
}

message GetSubscriptionCountForRuleRequest {
  string rule_id = 1;
  string actor_id = 2;
}

message GetSubscriptionCountForRuleResponse {
  rpc.Status status = 1;
  string rule_id = 2;
  string actor_id = 3;
  uint32 num_subscriptions_across_actor = 4;
  uint32 num_subscriptions_for_actor = 5;
}


message GetActiveSubscriptionCountForRuleRequest {
  string rule_id = 1;
  string actor_id = 2;
  // field_mask for getting required fields in response only
  repeated SubscriptionCountFieldMask field_masks = 3;
}

message GetActiveSubscriptionCountForRuleResponse {
  rpc.Status status = 1;
  uint32 num_subscriptions_across_actor = 4;
  uint32 num_subscriptions_for_actor = 5;
}

enum SubscriptionCountFieldMask {
  SUBSCRIPTION_COUNT_FIELD_MASK_UNSPECIFIED = 0;
  SUBSCRIPTION_COUNT_FIELD_MASK_PER_ACTOR = 1;
  SUBSCRIPTION_COUNT_FIELD_MASK_ACROSS_ACTORS = 2;
  SUBSCRIPTION_COUNT_FIELD_MASK_UNIQUE_SUBSCRIBERS = 3;
}

message GetActiveSubscriptionCountForRulesRequest {
  string actor_id = 1;
  repeated string rule_ids = 2;
}

message GetActiveSubscriptionCountForRulesResponse {
  rpc.Status status = 1;
  map<string, SubscriptionCount> count = 2;
}

message SubscriptionCount {
  uint32 count_across_actors = 1;
  uint32 count_per_actor = 2;
}

message GetSubscriptionsByActorForRulesRequest {
  string actor_id = 1;
  repeated string rule_ids = 2;
  // optional - If states are passed, only subscriptions with given states will be returned.
  repeated RuleSubscriptionState states = 3;
  // Optional filter - action entity can be one of Mutual Fund or Smart deposit based on the category and type of rules.
  // This can be used by Deposit and Investment service to filter recurring rule subscriptions
  // for a smart deposit or mutual fund.
  oneof action_entity_id {
    string smart_deposit_acc_id = 4;
    string mutual_fund_id = 5;
    string us_stock_id = 9;
  }
  // page context request
  rpc.PageContextRequest page_context = 6;
  // if true, pagination would not be used for fetched records
  bool should_not_use_pagination = 7;

  repeated FieldMask field_mask = 8;

  // field mask for optional fields
  enum FieldMask {
    FIELD_MASK_UNSPECIFIED = 0;
    // if set, rule_subscription_additional_details will be populated
    FIELD_MASK_ADDITIONAL_DETAILS = 1;
  }
}

message GetSubscriptionsByActorForRulesResponse {
  rpc.Status status = 1;
  string actorId = 2;
  // map of ruleId vs list of subscriptions
  map<string, Subscriptions> rule_subscriptions = 3;
  // page context response
  rpc.PageContextResponse page_context = 5;
  // map of ruleId vs list of SubscriptionsAdditionalDetails
  map<string, SubscriptionsAdditionalDetails> rule_subscription_additional_details = 6;
}

message SubscriptionsAdditionalDetails {
  repeated RuleSubscriptionAdditionalDetails rule_subscriptions = 1;
}

// RuleSubscriptionDetails have all the details of a rule subscription
message RuleSubscriptionAdditionalDetails {
  // e.g: When Chennai Super Kings win a match, save ₹175 into Rainy Day Fund
  string rule_description = 1;
  // e.g: When Chennai Super Kings win a match, save ₹175
  string short_rule_description = 2;
  string rule_icon_url = 3;
  string rule_id = 4;
  // e.g.: One Team One Dream
  string rule_name = 5;
  // amount saved via this fit rule till now
  google.type.Money amount_saved = 6;
}

message Subscriptions {
  repeated RuleSubscription rule_subscriptions = 1;
}

message GetExecutionsCountForRulesRequest {
  // Either one of rule_ids or rule_names has to be used.
  repeated string rule_ids = 1;
  repeated string rule_names = 2;
}

message GetExecutionsCountForRulesResponse {
  rpc.Status status = 1;
  // map of ruleId/ruleName and count of executions across actors
  // key will be ruleId if request has ruleIds populated.
  // else key will be ruleName if request has ruleNames populated.
  map<string, int64> executions_count_map = 2;
}

message GetAllSubscriptionVersionsRequest {
  // list of subscriptions
  repeated string subscription_ids = 1;
  // start time for versions
  // if this is nil, does not considers any constraint on start_time
  google.protobuf.Timestamp start_time = 2;
  // if this is nil, does not considers any constraint on end_time
  google.protobuf.Timestamp end_time = 3;
  // if start_time and end_time both are nil, does not considers any constraint on start and end time
}

message GetAllSubscriptionVersionsResponse {
  rpc.Status status = 1;
  // key is subscription Id, value is list of all the versions
  map<string, AllSubscriptionVersions> subscriptionToVersionsMap = 2;
}

message AllSubscriptionVersions {
  repeated RuleSubscription versions = 1;
}

message GetProfileRequest {
  // actor Id of the user
  string actor_id = 1;
  // which client has sent the request: fittt/rewards
  api.rms.orchestrator.event.RMSClient client = 2;
}

message GetProfileResponse {
  // rpc status
  rpc.Status status = 1;
  // actor's rms profile
  Profile profile = 2;
}

message UpdateProfileRequest {
  // actor Id of the user
  string actor_id = 1;
  // which client has sent the request: fittt/rewards
  api.rms.orchestrator.event.RMSClient client = 2;
  // actor's rms profile
  Profile profile = 3;
}

message UpdateProfileResponse {
  // rpc status
  rpc.Status status = 1;
}

message UpdateCollectionRequest {
  // id of the collection to be updated
  string collecton_id = 1;
  // the collection instance with new data
  Collection collection = 2;
  // The fields that need to be updated. This is repeated because a single update
  // call can update multiple fields. The update request ignores any fields that aren't specified in the field mask,
  // leaving them with their current values.
  repeated CollectionFieldMask field_masks = 3;
}

message UpdateCollectionResponse {
  // rpc status
  rpc.Status status = 1;
  // returning the updated collection
  Collection collection = 2;
}

message GetUserStatsRequest {
  string actor_id = 1 [(validate.rules).string.min_len = 1];
  repeated UserStatsFieldMask field_masks = 2 [(validate.rules).repeated.min_items = 1];
  SubscriptionCountStatsRequest subs_count_req = 3;
  TotalSubscriptionCountStatsRequest total_subs_count_req = 4;
}

message GetUserStatsResponse {
  rpc.Status status = 1;
  SubscriptionCountStats subs_count_stats = 2;
  int64 total_subs_count = 3;
}

enum UserStatsFieldMask {
  USER_STATS_FIELD_MASK_UNSPECIFIED = 0;
  USER_STATS_SUBSCRIPTION_COUNT_PER_RULE = 1;
  USER_STATS_TOTAL_SUBSCRIPTIONS_COUNT = 2;
}

message SubscriptionCountStatsRequest {
  // optional - If rule_ids are passed, only count for the given rules are computed.
  // If empty, subscription count for all rules will be computed
  repeated string rule_ids = 1;
  // optional - If states are passed, only subscriptions with given states are considered to compute count.
  // If empty, count is computed without considering the subscription state.
  repeated RuleSubscriptionState states = 2;
}

message SubscriptionCountStats {
  // map<rule_id, count>
  map<string, int64> count_map = 1;
}

message TotalSubscriptionCountStatsRequest {
  // optional - If rule_ids are passed, only count for the given rules are computed.
  // If empty, subscription count for all rules will be computed
  repeated string rule_ids = 1;
  // optional - If states are passed, only subscriptions with given states are considered to compute count.
  // If empty, count is computed without considering the subscription state.
  repeated RuleSubscriptionState states = 2;
}

message UpdateSubscriptionExecutionStateRequest {
  repeated string subscription_ids = 1;
  // target execution state for the arg subscription ids
  SubscriptionExecutionState execution_state = 2;
  // reason fo which execution state was changed
  SubscriptionStateChangeReason state_change_reason = 3;
  // source of request (Internal, Sherlock)
  SubscriptionStateChangeProvenance state_change_provenance = 4;
}

message UpdateSubscriptionExecutionStateResponse {
  // rpc status to be returned
  rpc.Status status = 1;
}

message GetSubscriptionsByExecutionStateRequest {
  // optional: actor_id for which subs with given execution states are to be fetched
  string actor_id = 1;
  // list of subscription execution states, cannot be nil
  repeated SubscriptionExecutionState execution_states = 2;
  // field mask, if any, to fetch specific fields for subscriptions
  // if nil, subscription with all fields will be fetched
  google.protobuf.FieldMask field_mask = 3;
}

message GetSubscriptionsByExecutionStateResponse {
  // rpc status to be returned
  rpc.Status status = 1;
  // list of subscriptions to be returned
  Subscriptions subscriptions = 2;
}

message GetSubscriptionsForUpcomingExecutionsRequest {
  rpc.PageContextRequest page_context = 1;
  // actor id for whom the upcoming subscriptions are to be fetched
  string actor_id = 2;
  // optional: the number of subscriptions to be fetched
  int32 limit = 3;
  // optional: rule category of subscription to be fetched,
  // if passed, subscriptions belonging to only that rule category will be fetched
  RuleCategory rule_category = 4;
  // field mask, if any, to fetch specific fields for subscriptions
  // if nil, subscription with all fields will be fetched
  google.protobuf.FieldMask field_mask = 5;
}

message GetSubscriptionsForUpcomingExecutionsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  rpc.PageContextResponse page_context = 2;
  // list of subscriptions to be returned
  Subscriptions subscriptions = 3;
}

message GetActorsWithSubscriptionsRequest {
  // [required]
  repeated RuleSubscriptionState rule_subscription_state = 1;
  // [required] if rule_ids not provided
  repeated RuleTypeForSpecialHandling rule_type_for_special_handling = 2;
  // [required] if rule_type_for_special_handling is not provided
  // list of rule_ids for identifying rules
  repeated string rule_ids = 3;
}
message GetActorsWithSubscriptionsResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated string actor_ids = 2;

}

message CalculateSubscriptionAmountInDateRangeRequest {
  string actor_id = 1;
  repeated RuleCategory rule_categories = 2;
  repeated RuleSubscriptionState rule_subscription_states = 3;
  google.type.Date from_date = 4;
  google.type.Date to_date = 5;
}

message CalculateSubscriptionAmountInDateRangeResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  // this is the amount which is aggregated for all the subscriptions within given date range
  google.type.Money computed_amount = 2;
}

message SendProactiveFundAdditionNotificationRequest {
  google.type.Date from_date = 1;
  google.type.Date to_date = 2;
}

message SendProactiveFundAdditionNotificationResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
}
