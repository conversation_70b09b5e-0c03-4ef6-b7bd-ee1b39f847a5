// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package api.rms.developer;

option go_package = "github.com/epifi/gamma/api/rms/developer";
option java_package = "com.github.epifi.gamma.api.rms.developer";

enum RMSEntity{
  RMS_ENTITY_UNSPECIFIED = 0;
  // for checking the defined rules
  RULES = 1;
  // for checking subscription params configurations
  RULE_SUBSCRIPTIONS = 2;
  // for checking rule execution related details
  RULE_EXECUTIONS = 3;
  // For fetching home card data
  HOME_CARDS = 4;
  // For fetching Rule Tags
  RULE_TAGS = 5;
  // For Fetching Collections
  COLLECTIONS = 6;
}
