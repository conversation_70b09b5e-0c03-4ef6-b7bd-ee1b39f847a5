// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package api.rms.orchestrator.event.scope;

option go_package = "github.com/epifi/gamma/api/rms/orchestrator/event/scope;scopepb";
option java_package = "com.github.epifi.gamma.api.rms.orchestrator.event.scope";

// scope to restrict rule execution to specific set of subscriptions.
message RuleExecutionScope {
  oneof scope {
    // only subscriptions related to these rules will be evaluated
    RuleIdsList rules = 1;
    // only subscriptions related to these rule tags will be evaluated
    SubscriptionIdsList subscriptions = 2;
    // only subscriptions for rules with anyone of the given tags will be evaluated
    TagIdsList tags = 3;
  }
}

message RuleIdsList {
  repeated string rule_ids = 1;
}

message SubscriptionIdsList {
  repeated string subscription_ids = 1;
}

message TagIdsList {
  repeated string tag_ids = 1;
}
