syntax = "proto3";

package rewards.notification;

import "api/comms/email_template.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/rewards/reward.proto";

option go_package = "github.com/epifi/gamma/api/rewards/notification";
option java_package = "com.github.epifi.gamma.api.rewards.notification";

enum NotificationMedium {
  NOTIFICATION_MEDIUM_UNSPECIFIED = 0;
  MEDIUM_NOTIFICATION = 1;
  MEDIUM_EMAIL = 2;
}

enum NotificationTrigger {
  NOTIFICATION_TRIGGER_UNSPECIFIED = 0;
  // reward unlocked for the user
  REWARD_UNLOCKED_TRIGGER = 1;
  // when reward is earned by the user
  REWARD_EARNED_TRIGGER = 2;
  // when reward processing status is updated
  REWARD_PROCESSING_STATUS_UPDATE_TRIGGER = 3;
}

// enum useful for differentiating notifications originating from the same trigger point, with same metadata,
// but different behaviour/config
enum NotificationVersion {
  // default version of the notification
  VERSION_V0 = 0;
}

message TriggerMetadata {
  oneof metadata {
    // data required for validating and sending notification when a reward is unlocked
    RewardUnlockedTriggerMetadata reward_unlocked_trigger_metadata = 1;
    // data required for validating and sending notification when a reward is earned by the user
    RewardEarnedTriggerMetadata reward_earned_trigger_metadata = 2;
    // data required for validating and sending notification when a reward processing status is updated
    RewardProcessingStatusUpdateTriggerMetadata reward_processing_status_update_trigger_metadata = 3;
  }
  // version of the notification
  NotificationVersion notification_version = 15;
}

message RewardUnlockedTriggerMetadata {
  string reward_id = 1;
}

message RewardEarnedTriggerMetadata {
  string reward_id = 1;
  // optional field to send id of the nudge to be triggered
  string nudge_id = 2;
  // sends the notification irrespective of the reward status i.e. created here
  bool should_skip_status_validation = 3;
}

message RewardProcessingStatusUpdateTriggerMetadata {
  // denotes the reward for which notification is to be triggered
  string reward_id = 1;
  // denotes the status of reward at the time of notification trigger
  rewards.RewardStatus reward_status = 2;
}

message Content {
  string title = 1;
  string body = 2;
  string image_url = 3;
  frontend.deeplink.Deeplink deeplink = 4;
}

// NotificationTypeMetadata contains notification type specific metadata
// Ex : nudge id for nudge notification, email template for email notification etc
message NotificationTypeMetadata {
  oneof metadata {
     NudgeNotificationTypeMetadata nudge = 1;
     EmailNotificationTypeMetadata email = 2;
  }
}

// NudgeNotificationTypeMetadata contains nudge specific metadata
// Ex : nudge id
message NudgeNotificationTypeMetadata {
  string nudge_id = 1;
}

// EmailNotificationTypeMetadata contains email specific metadata
// Ex : email template
message EmailNotificationTypeMetadata {
  // email template to be used for sending the notification via email
  comms.EmailType email_template = 1;
}
