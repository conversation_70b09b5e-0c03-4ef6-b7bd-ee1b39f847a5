// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package luckydraw;

import "api/rewards/luckydraw/reward.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/rewards/luckydraw";
option java_package = "com.github.epifi.gamma.api.rewards.luckydraw";


// LuckyDraw contains all the information related to lucky draw.
// Mapping between LuckyDrawCampaign and LuckyDraw is 1 to many.
message LuckyDraw {
  // unique identifier of a lucky draw.
  string id = 1;

  // id of campaign of which is lucky draw is a part of.
  // mapping between LuckyDrawCampaign and LuckyDraw is 1 to many.
  string campaign_id = 2;

  // orchestrates all the config related to display, rewarding strategy (including rewards) etc.
  LuckyDrawConfig lucky_draw_config = 3;

  // registration_from denotes the time from which lucky draw registration is active
  google.protobuf.Timestamp registration_from = 5;

  // registration_till denotes the time upto which lucky draw registration is active
  google.protobuf.Timestamp registration_till = 6;

  // lucky_draw_reveal_time denotes the time at which lucky draw results would be declared
  google.protobuf.Timestamp lucky_draw_reveal_time = 7;

  // status of lucky draw, namely CREATED or COMPLETED
  LuckyDrawStatus lucky_draw_status = 8;
}

// LuckyDrawStatus denotes status of lucky draw
enum LuckyDrawStatus {
  LUCKY_DRAW_STATUS_UNSPECIFIED = 0;
  // status of lucky draw remains CREATED upto
  // the time when the lucky draw gets revealed.
  LUCKY_DRAW_STATUS_CREATED = 5;
  // COMPLETED status denotes the lucky draw
  // has been revealed and winnings have been generated.
  LUCKY_DRAW_STATUS_COMPLETED = 20;
}

message LuckyDrawConfig {
  oneof reward_distribution_config {
    TieredRewardDistributionConfig tiered_reward_distribution_config = 1;
  }
}

message TieredRewardDistributionConfig {
  repeated RewardDistributionUnit reward_distribution_units = 1;
}

message RewardDistributionUnit {
  // distribution_order denotes order of distribution of reward i.e rewards
  // would be distributed in ascending order to distribution order.
  // Ordering ensures that if participating users are less than
  // than cumulative number of users who need to be rewarded
  // then the rewards whose distribution order is less are given first.
  int32 distribution_order = 1;

  // the reward to be distributed,
  luckydraw.Reward reward = 2;

  // distribution_quantity denotes the amount of rewards
  // to be distributed of above reward type as a part of lucky draw.
  int32 distribution_quantity = 10;
}

