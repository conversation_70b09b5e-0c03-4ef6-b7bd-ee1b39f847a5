// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package luckydraw;

option go_package = "github.com/epifi/gamma/api/rewards/luckydraw";
option java_package = "com.github.epifi.gamma.api.rewards.luckydraw";

// LuckyDrawCampaign is used to group multiple lucky draws under a single campaign.
// Mapping between LuckyDrawCampaign and LuckyDraw is 1 to many.
message LuckyDrawCampaign {
  // unique identifier of a campaign
  string id = 1;

  // description of campaign
  string description = 2;
}
