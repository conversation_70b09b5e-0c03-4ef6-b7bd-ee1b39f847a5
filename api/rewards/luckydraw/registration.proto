// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package luckydraw;

option go_package = "github.com/epifi/gamma/api/rewards/luckydraw";
option java_package = "com.github.epifi.gamma.api.rewards.luckydraw";

// LuckyDrawRegistration contains information related to a user registration in a lucky draw.
// If a user registered in n different lucky draws then n LuckyDrawRegistration entries would be present.
// Mapping between LuckyDraw and LuckyDrawRegistration is 1 to many.
message LuckyDrawRegistration {
  // unique identifier of a lucky draw registration.
  string id = 1;

  // Identifier of the lucky draw for which the actor has registered.
  string lucky_draw_id = 2;

  // id of actor who registered for the lucky draw.
  string actor_id = 3;
}

enum RegistrationStatus {
  REGISTRATION_STATUS_UNSPECIFIED = 0;
  // NOT_REGISTERED status to indicate that user is not registered for the lucky draw.
  NOT_REGISTERED = 1;
  // NOT_REGISTERED status to indicate that user is registered for the lucky draw
  REGISTERED = 5;
}
