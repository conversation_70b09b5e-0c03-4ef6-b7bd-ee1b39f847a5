syntax = "proto3";

package rewards.campaigncomm;

option go_package = "github.com/epifi/gamma/api/rewards/campaigncomm";
option java_package = "com.github.epifi.gamma.api.rewards.campaigncomm";

enum CommType {
  COMMS_TYPE_UNSPECIFIED = 0;
  // Send notification to the users (who never added funds even once) to perform fund addition to earn a reward.
  REWARD_ON_FIRST_FUND_ADDITION = 1;
  // Send notification to the users to deposit their salary in Fi and earn a reward.
  REWARD_ON_SALARY_DEPOSIT_V1 = 2;
}
