syntax = "proto3";

package rewards.campaigncomm;

import "api/rewards/campaigncomm/comm_type.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/rewards/campaigncomm";
option java_package = "com.github.epifi.gamma.api.rewards.campaigncomm";

// service to send reward campaign related comm to the users.
service RewardsCampaignComm {
  rpc SendComm (SendCommRequest) returns (SendCommResponse);
}

message SendCommRequest {
  // type of communication
  CommType comm_type = 1;
}

message SendCommResponse {
  // rpc status
  rpc.Status status = 1;
}
