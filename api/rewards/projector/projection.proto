//go:generate gen_sql -types=RewardOption,OptionsInfo
syntax = "proto3";

package projector;

import "api/rewards/collected_data_type.proto";
import "api/rewards/reward.proto";
import "api/rewards/reward_offer_type.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/rewards/projector";
option java_package = "com.github.epifi.gamma.api.rewards.projector";

// Projection of a reward that could be given to a user for an event
message Projection {
  // unique id of the projection
  string id = 1;
  // actor ID
  string actor_id = 2;
  // further level of differentiating between different accounts of same actor (can be empty)
  string account_id = 3;
  // reward type of projection
  rewards.RewardType reward_type = 4;
  // Projections of options that are generated for the offer.
  OptionsInfo projected_options = 5;
  // Actual contribution of the projection to generated reward
  OptionsInfo reward_contributions = 6;
  // ID of the reward that's generated for this projection
  string reward_id = 7;
  // action type that generated the projection
  rewards.CollectedDataType action_type = 8;
  // time at which action was performed
  google.protobuf.Timestamp action_time = 9;
  // reference ID of the action
  string ref_id = 10;
  // type of offer that generated the projection
  rewards.RewardOfferType offer_type = 11;
  // timestamps
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp deleted_at = 14;
  string offer_id = 15;
}

// will generate a reward option for every configured option
message OptionsInfo {
  repeated RewardOption reward_units_with_types = 1;
}

message RewardOption {
  rewards.RewardType reward_type = 1;
  float reward_units = 2;

  oneof RewardTypeSpecificMetadata {
    FiCoinsMetadata fi_coins_metadata = 3;
  }

  message FiCoinsMetadata {
    // this flag will be true when the projection is given for reward type FI_COINS but given as FI_POINTS
    bool is_fi_points = 1;
  }
}

enum ProjectionFieldMask {
  PROJECTION_FIELD_MASK_UNSPECIFIED = 0;
  PROJECTION_FIELD_MASK_ID = 1;
  PROJECTION_FIELD_MASK_ACTOR_ID = 2;
  PROJECTION_FIELD_MASK_ACCOUNT_ID = 3;
  PROJECTION_FIELD_MASK_REWARD_TYPE = 4;
  PROJECTION_FIELD_MASK_PROJECTED_OPTIONS = 5;
  PROJECTION_FIELD_MASK_REWARD_CONTRIBUTIONS = 6;
  PROJECTION_FIELD_MASK_REWARD_ID = 7;
  PROJECTION_FIELD_MASK_ACTION_TYPE = 8;
  PROJECTION_FIELD_MASK_ACTION_TIME = 9;
  PROJECTION_FIELD_MASK_REF_ID = 10;
  PROJECTION_FIELD_MASK_OFFER_TYPE = 11;
  PROJECTION_FIELD_MASK_CREATED_AT = 12;
  PROJECTION_FIELD_MASK_UPDATED_AT = 13;
  PROJECTION_FIELD_MASK_DELETED_AT = 14;
  PROJECTION_FIELD_MASK_OFFER_ID = 15;
}
