syntax = "proto3";

package projector;

import "api/rewards/collected_data_type.proto";
import "api/rewards/projector/projection.proto";
import "api/rewards/reward.proto";
import "api/rewards/reward_offer_type.proto";
import "api/rewards/service.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/rewards/projector";
option java_package = "com.github.epifi.gamma.api.rewards.projector";

service ProjectorService {
  // GetRewardProjection RPC is used to fetch projection of the reward that will be generated that could be generated for a given offer type
  rpc GetRewardsProjections (GetRewardsProjectionsRequest) returns (GetRewardsProjectionsResponse);
  // GetProjectionAggregates returns the aggregates of projection filtered based on given values
  rpc GetProjectionAggregates (GetProjectionAggregatesRequest) returns (GetProjectionAggregatesResponse);
}


message GetRewardsProjectionsRequest {
  // actor for which we want to fetch projections
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // filters for fetching projections
  Filters filters = 2;
  // flag to decide if we want to fetch aggregate (sum) of all the projections obtained after applying filters
  // or individual projection values
  // note: time_window OR list of ref_ids filter is mandatory when this flag is set.
  // deprecated: please use GetProjectionAggregates for fetching aggregates
  bool fetch_aggregates = 3 [deprecated = true];
  rpc.PageContextRequest page_ctx_request = 4;

  message Filters {
    // account identifier in case multiple accounts are possible for an actor (for example, in CC)
    string account_id = 1;
    // action types for which projections are to be fetched
    repeated rewards.CollectedDataType action_type = 2;
    // offer types for which projections are to be fetched
    repeated rewards.RewardOfferType offer_type = 3;
    // reference IDs of the event that we want to fetch the projection for
    repeated string ref_ids = 4;
    // time window in which we want to fetch projections
    rewards.TimeWindow time_window = 5;
    // offer IDs for which we want to fetch the projections
    repeated string offer_ids = 6;
    // reward IDs for which we want to fetch the projections
    repeated string reward_ids = 7;
    // If true, includes deleted projections in the response.
    // By default, deleted projections are excluded from query results.
    bool fetch_deleted_projections = 8;
  }
}

message GetRewardsProjectionsResponse {
  rpc.Status status = 1;
  // projection of reward
  oneof Projections {
    IndividualProjections individual_projections = 2;
    AggregateProjections aggregate_projections = 3;
  }

  rpc.PageContextResponse page_ctx_response = 4;

  // includes a list of projections fetched for the applied filters
  message IndividualProjections {
    repeated Projection projections = 1;
  }

  // includes aggregates of reward projections
  message AggregateProjections {
    repeated RewardUnitsDetails reward_units_details = 1;
    // details of projected and actual reward units aggregated on reward type
    message RewardUnitsDetails {
      rewards.RewardType reward_type = 1;
      float projected_reward_units = 2;
      float actual_reward_units = 3;
    }
  }
}

message GetProjectionAggregatesRequest {
  // actor for which we want to fetch projections
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
  // filters for fetching projections
  Filters filters = 2;

  message Filters {
    // account identifier in case multiple accounts are possible for an actor (for example, in CC)
    string account_id = 1;
    // action types for which projections are to be fetched
    repeated rewards.CollectedDataType action_types = 2;
    // offer types for which projections are to be fetched
    repeated rewards.RewardOfferType offer_types = 3;
    // offer IDs for which we want to fetch the projections
    repeated string offer_ids = 4;
    // list of time ranges in which we want to fetch the projections/aggregations
    repeated rewards.TimeWindow time_windows = 5;
  }
}

message GetProjectionAggregatesResponse {
  rpc.Status status = 1;
  // includes aggregates of reward projections
  repeated RewardProjectionAggregate aggregates = 2;
  // details of projected and actual reward units aggregated on reward type
  message RewardProjectionAggregate {
    rewards.RewardType reward_type = 1;
    float projected_reward_units = 2;
    float actual_reward_units = 3;
  }
}
