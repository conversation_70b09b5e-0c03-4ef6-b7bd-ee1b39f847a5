syntax = "proto3";

package rewards;

import "api/rewards/reward.proto";
import "api/rewards/reward_offer_type.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/rewards";
option java_package = "com.github.epifi.gamma.api.rewards";

message CreditCardRewardInfo {
  string id = 1;

  // ID of reward for which the additional information is present
  string reward_id = 2;

  RewardType reward_type = 3;

  // reward units given out initially (before any clawbacks happened)
  uint32 initial_reward_units = 4;

  // offer type that led to reward creation (for differentiating between 1x, 5x rewards)
  RewardOfferType reward_offer_type = 5;

  // merchant ID of the merchant to which the transaction was made (if the transaction was made to a merchant)
  // would be present only for txn level credit card reward constructs like 1x rewards which are given on a txn level.
  string txn_merchant_id = 6;

  // account ID of the credit card
  string credit_card_account_id = 7;

  // credit card ID
  string credit_card_id = 8;

  // credit card scheme id
  // todo (divyadeep/utkarsh) - confirm the exact name of this field from CC team and update the same here
  string credit_card_scheme_code = 9;

  // timestamp of action that lead to reward
  google.protobuf.Timestamp action_timestamp = 10;

  // standard timestamps
  google.protobuf.Timestamp created_at = 11;

  google.protobuf.Timestamp updated_at = 12;
}

