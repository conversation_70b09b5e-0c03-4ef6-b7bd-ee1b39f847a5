//go:generate gen_sql -types=ClawbackEventType
syntax = "proto3";

package rewards;

option go_package = "github.com/epifi/gamma/api/rewards";
option java_package = "com.github.epifi.gamma.api.rewards";

// Types of Collected Data
enum ClawbackEventType {
  // todo(divyadeep): gracefully migrate UNSPECIFIED_EVENT_TYPE and CREDIT_CARD_TXN_REVERSAL to new naming methodology
  UNSPECIFIED_EVENT_TYPE = 0;
  CREDIT_CARD_TXN_REVERSAL = 1;
  // some cc related rewards need to be clawed back at the time of billing
  CLAWBACK_EVENT_TYPE_CREDIT_CARD_BILLING = 2;
}
