//go:generate gen_sql -types=LeadState
syntax = "proto3";

package api.collection;

import "google/protobuf/timestamp.proto";

import "api/vendorgateway/vendor.proto";

option go_package = "github.com/epifi/gamma/api/collection";
option java_package = "com.github.epifi.gamma.api.collection";

message Lead {
  // unique identifier of the lead in our system
  string id = 1;
  // actor_id of the user whom the loan belongs to
  string actor_id = 2;
  // unique identifier of the credit product that the lead is for
  // e.g: for a personal loan product, this would be the id of the loan_accounts table
  string account_id = 3;
  // currently only Credgenics (56) is supported
  vendorgateway.Vendor vendor = 4;
  // a unique identifier that we generate for each lead and use it at the vendor's end (e.g. Credgenics)
  // this would avoid leaking the primary identifier of our entities to external companies.
  string vendor_id = 5;
  // tells whether the lead is active or not
  LeadState state = 6;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;
}

enum LeadState {
  LEAD_STATE_UNSPECIFIED = 0;
  LEAD_STATE_ACTIVE = 1;
  // lead is archived at the vendor
  LEAD_STATE_INACTIVE = 2;
}

// LeadFieldMask is an enum that represents the fields of the Lead entity
// An example use-case is using this enum as a update/select field mask for update RPCs or DAO methods
enum LeadFieldMask {
  LEAD_FIELD_MASK_UNSPECIFIED = 0;
  LEAD_FIELD_MASK_ID = 1;
  LEAD_FIELD_MASK_ACTOR_ID = 2;
  LEAD_FIELD_MASK_ACCOUNT_ID = 3;
  LEAD_FIELD_MASK_VENDOR = 4;
  LEAD_FIELD_MASK_VENDOR_ID = 5;
  LEAD_FIELD_MASK_STATE = 6;
  LEAD_FIELD_MASK_CREATED_AT = 8;
  LEAD_FIELD_MASK_UPDATED_AT = 9;
  LEAD_FIELD_MASK_DELETED_AT = 10;
}
