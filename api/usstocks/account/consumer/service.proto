//go:generate gen_queue_pb
syntax = "proto3";

package usstocks.account.consumer;

import "api/aml/aml_case_decision_event.proto";
import "api/queue/consumer_headers.proto";
import "api/vendorgateway/stocks/enums.proto";
import "google/type/date.proto";


option go_package = "github.com/epifi/gamma/api/usstocks/account/consumer";
option java_package = "com.github.epifi.gamma.api.usstocks.account.consumer";

// Consumer service is used to process incoming events
service Consumer {
  // ProcessAMLActionEvent consumes the incoming AML action event and updates the database with received data
  // Eg: When ops agent approves or rejects an AML match with remarks and additional data, this method updates
  // the same in db and workflow polls for the update in database
  rpc ProcessAMLActionEvent (aml.AmlCaseDecisionEvent) returns (ProcessAMLActionEventResponse) {}

  // ProcessSendMailToUsersEvent used to send trade confirmation or monthly statement mail,
  // SendMailForAllUser request type used to send mail to all users who invested in UsStocks on given date
  // SendMailForOneUser request type used to send mail to single actor who invested in UsStocks on given date
  rpc ProcessSendMailToUsersEvent (ProcessSendMailToUsersEventRequest) returns (ProcessSendMailToUsersEventResponse) {};
}

message ProcessAMLActionEventResponse {
  // header for queue response
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessSendMailToUsersEventRequest {
  // standard header to be added to all requests
  queue.ConsumerRequestHeader consumer_request_header = 1;
  oneof option {
    SendMailForAllUser send_mail_for_all_user = 2;
    SendMailForOneUser send_mail_for_one_user = 3;
  }
}

message SendMailForAllUser {
  google.type.Date start_date = 1;
  google.type.Date end_date = 2;
  vendorgateway.stocks.DocumentStatementType document_statement_type = 3;
}

message SendMailForOneUser {
  google.type.Date start_date = 1;
  google.type.Date end_date = 2;
  string actor_id = 3;
  string vendor_account_id = 4;
  vendorgateway.stocks.DocumentStatementType document_statement_type = 5;
}

message ProcessSendMailToUsersEventResponse {
  // header for queue response
  queue.ConsumerResponseHeader response_header = 1;
}
