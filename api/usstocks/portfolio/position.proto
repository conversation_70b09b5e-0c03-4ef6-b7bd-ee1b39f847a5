syntax = "proto3";

package usstocks.portfolio;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/portfolio";
option java_package = "com.github.epifi.gamma.api.usstocks.portfolio";

// A position is the expression of a market commitment, or exposure, held by a trader.
// It is the financial term for a trade that is either currently able to incur a profit or a loss – known as an open position
message Position {
  // represent symbol of holding
  string symbol = 1;

  // amount invested in symbol
  google.type.Money invested_amount = 2;

  // amount profit/loss in investment
  // Note pl denotes profit/loss
  google.type.Money pl_amount = 3;

  // percentage of profit/ loss in investment
  double pl_percentage = 4;

  // total qty the user is holding
  double total_qty = 5;

  // Avg price of share
  // Eg:  if user buy 1 stock at 30$
  // and user buy again 1 stock at 50$
  // Then avg price per share is 40$
  google.type.Money avg_price = 6;

  // investment market value
  google.type.Money market_value = 7;

  // Represent logo to display
  string logo_url = 8;

  // Represent display name
  string display_name = 9;

  // Represent catalog Id
  string stock_id = 10;

  // Represent price of symbol while getting position
  google.type.Money current_price = 11;

  // Represent sell Lock unit
  double sell_lock_unit = 12;

  // Time at which the stored position was last refreshed using broker's portfolio API
  google.protobuf.Timestamp last_refreshed_at = 13;

  // Profit/loss percent change from last day price
  double pl_percentage_change_today = 14;
}

// this information is used for display in Investment Summary
message InvestmentSummaryInfo {
  // invested_amount returns total current investment amount of the user
  // it does not include amount present in wallet
  google.type.Money invested_amount = 1;
  // current projected value of all stocks in non-closed states.
  // represent current value of asset according to market
  google.type.Money current_amount = 2;
  // current growth of all stocks in non-closed states.
  // growth = current_amount - total_invested
  google.type.Money growth_amount = 3;
  // current growth in % of all stocks in non-closed states.
  // growth_percent = current_growth / total_invested * 100
  double growth_percent = 4;
  // processing_amount is the total amount of money that is currently being processed for stocks
  google.type.Money processing_amount = 5;
  // has_invested is true if the user has active investments in any stocks, has_invested will be true only if there are open positions
  bool has_invested = 6;
  // percentage growth during current day
  double one_day_growth = 7;

  // represent the forex rate that is applicable
  // this help in conversion of usd to inr
  // note: this field will be populated only if has_invested is true
  google.type.Money forex_rate_in_inr = 8;

  // count of orders that are in pending state.
  // all the orders not in terminal state (success/failure) are considered pending
  int64 pending_orders_count = 9;

  // Time at which all stored positions in portfolio were last refreshed using broker's portfolio API
  google.protobuf.Timestamp positions_last_refreshed_at = 10;

  // has_ever_invested is true if the user has ever invested in us stocks, is_invested will be true even if there are closed positions for user
  // only considers trade related orders
  bool has_ever_invested = 11;
}
