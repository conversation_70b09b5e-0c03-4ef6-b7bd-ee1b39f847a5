//go:generate gen_sql -types=AccountActivitySyncType,PageTokenInfo
syntax = "proto3";

package usstocks.order;

import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/order";
option java_package = "com.github.epifi.gamma.api.usstocks.order";

message AccountActivitySyncCursor {
  // A unique identifier of a cursor that moves through pages of account activities
  string id = 1;

  AccountActivitySyncType type = 2;

  // Token of the last account activity page that was synced using the cursor,
  // usually, the stockbroker's ID of the last synced account activity
  string page_token = 3;

  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  google.protobuf.Timestamp deleted_at = 6;

  // represent vendor for given activity sync
  vendorgateway.Vendor vendor = 7;

  // Information related to the last account activity synced
  PageTokenInfo page_token_info = 8;
}

enum AccountActivitySyncFieldMask {
  ACCOUNT_ACTIVITY_SYNC_FIELD_MASK_UNSPECIFIED = 0;
  ACCOUNT_ACTIVITY_SYNC_FIELD_MASK_PAGE_TOKEN = 1;
  ACCOUNT_ACTIVITY_SYNC_FIELD_MASK_PAGE_TOKEN_INFO = 2;
}

enum AccountActivitySyncType {
  ACCOUNT_ACTIVITY_SYNC_TYPE_UNSPECIFIED = 0;
  ACCOUNT_ACTIVITY_SYNC_TYPE_BATCH_UPDATE = 1;
}

message PageTokenInfo {
  // The date on which the activity occurred or on which the transaction associated with the activity settled.
  google.type.Date date = 1;
}
