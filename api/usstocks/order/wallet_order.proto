//go:generate gen_sql -types=WalletOrderInvoiceDetails,WalletOrderFailureReason
syntax = "proto3";

package usstocks.order;

import "api/celestial/workflow/stage/status.proto";
import "api/usstocks/enums.proto";
import "api/usstocks/order/order.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/order";
option java_package = "com.github.epifi.gamma.api.usstocks.order";

// WalletOrder denotes all the details that are associated with an add/withdraw funds in wallet transactions
message WalletOrder {
  // unique identifier for order
  string id = 1;
  // OrderId of corresponding order at vendor
  string vendor_order_id = 2;
  // transaction Id of the pool transfer for corresponding to add funds order
  string pool_txn_id = 3;
  // transaction Id of the swift transfer for corresponding to add funds order
  string swift_txn_id = 4;
  // corresponding workflow request Id for the order
  string wf_req_id = 5;
  // actor_id of the user
  string actor_id = 6;
  // remittance req id is identifier for remittance_process responsible for moving money to user's account
  string remittance_req_id = 7;
  // defines type of order placed by user
  WalletOrderType order_type = 8;
  // further classifies the order
  // e.g. for order type ADD_FUNDS
  // order can have sub_type of INSTANT_WALLET_FUNDING or NON_INSTANT_WALLET_FUNDING
  WalletOrderSubType order_sub_type = 9;
  // current state of the order
  WalletOrderStatus status = 10;
  // amount value that user has requested to add/withdraw from wallet in rs
  // eg: 1000 Rs
  google.type.Money amount_requested = 11;
  // InvoiceDetails holds the component wise amount breakdown
  // eg: GST, TCS, Forex rate etc
  WalletOrderInvoiceDetails invoice_details = 12;
  //  External order Id is an alternative which can be shared with external users.
  string external_order_id = 13;
  // order Id generated by client eg: FIT
  string client_order_id = 14;
  // reference identifier to map an order to accounts table
  string vendor_account_id = 15;
  // represent payment info for order
  // eg: it contains utr number for successful pool transaction
  PaymentInfo payment_info = 16;
  // reason for a order that fails,shouldn't be populated for successful or orders that are still being processed
  WalletOrderFailureReason failure_reason = 17;

  google.protobuf.Timestamp created_at = 18;
  google.protobuf.Timestamp updated_at = 19;
  google.protobuf.Timestamp deleted_at = 20;
  // timestamp at which the order was created at vendor
  google.protobuf.Timestamp order_creation_timestamp_at_vendor = 21;
  // unique gst invoice number for reporting GST amount to federal
  string gst_invoice_number = 22;
}

message WalletOrderInvoiceDetails {
  // exchange rate decided for the transaction by partner bank
  google.type.Money partner_exchange_rate = 1;
  // amount in INR to be added/withdrawn from wallet
  google.type.Money amount_in_inr = 2;
  // amount in INR to be added/withdrawn from wallet
  google.type.Money amount_in_USD = 3;
  // GST(tax) for the foreign fund transfer
  google.type.Money GST = 4;
  // TCS(tax) for the foreign fund transfer, this is the amount charged by govt
  google.type.Money TCS = 5;
  // total amount is taxes plus amount to be transferred amount for add funds
  google.type.Money total_debit_amount = 6;
  // fee that is charged for creating order
  google.type.Money fee = 7;
  // if forex rate is from DB, then it'll be populated
  string forex_rate_id = 8;
  // source from where forex rate is fetched
  ForexRateProvenance forex_rate_provenance = 9;
  // represent total credit amount show to user in case of withdraw funds
  // total_credit_amount = withdraw_amount - GST
  google.type.Money total_credit_amount = 10;
}

enum WalletOrderFieldMask {
  WALLET_ORDER_FIELD_MASK_UNSPECIFIED = 0;
  WALLET_ORDER_FIELD_MASK_VENDOR_ORDER_ID = 1;
  WALLET_ORDER_FIELD_MASK_POOL_TXN_ID = 2;
  WALLET_ORDER_FIELD_MASK_SWIFT_TXN_ID = 3;
  WALLET_ORDER_FIELD_MASK_INVOICE_DETAILS = 4;
  WALLET_ORDER_FIELD_MASK_AMOUNT_REQUESTED = 5;
  WALLET_ORDER_FIELD_MASK_ORDER_STATUS = 6;
  WALLET_ORDER_FIELD_MASK_ORDER_SUB_TYPE = 7;
  WALLET_ORDER_FIELD_MASK_WF_REQ_ID = 8;
  WALLET_ORDER_FIELD_MASK_PAYMENT_INFO = 9;
  WALLET_ORDER_FIELD_MASK_FAILURE_REASON = 10;
  // Invoice details is a JSON column, below enums corresponds to fields in the JSON
  // update query is responsible for updating/inserting the mentioned field in JSON. i.e. other fields in json is not affected
  WALLET_ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE = 11;
  WALLET_ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE_ID = 12;
  WALLET_ORDER_FIELD_MASK_INVOICE_DETAILS_FOREX_RATE_PROVENANCE = 13;
  WALLET_ORDER_FIELD_MASK_INVOICE_DETAILS_GST = 14;
  WALLET_ORDER_FIELD_MASK_REMITTANCE_REQ_ID = 15;
  WALLET_ORDER_FIELD_MASK_INVOICE_DETAILS_TOTAL_CREDIT_AMOUNT = 16;
  WALLET_ORDER_FIELD_MASK_INVOICE_DETAILS_AMOUNT_IN_INR = 17;
  WALLET_ORDER_FIELD_MASK_ORDER_CREATION_TIMESTAMP_AT_VENDOR = 18;
  WALLET_ORDER_FIELD_MASK_GST_INVOICE_NUMBER = 19;
}

enum WalletOrderFailureReason {
  WALLET_ORDER_FAILURE_REASON_UNSPECIFIED = 0;
  // An add-funds-order was not fulfilled due to amount not being transferred to Fi Federal pool account
  // This could be for several reasons like user not entering UPI PIN, wrong PIN, etc.
  WALLET_ORDER_FAILURE_REASON_ERROR_TRANSFERRING_AMOUNT_TO_POOL_ACCOUNT = 1;

  // International fund transfer of add-funds-order is not allowed as the order amount  will breach the
  // maximum amount allowed by RBI for international remittance for a financial year for the user
  WALLET_ORDER_FAILURE_REASON_LRS_LIMIT_BREACHED = 2;

  // Source of funds that are being transferred could not be determined
  // This could be because user has no bank account connected with Fi that is at least 6 months old, etc.
  WALLET_ORDER_FAILURE_REASON_SOF_DETERMINATION_FAILED = 3;

  // Generic error when placing order with broker
  WALLET_ORDER_FAILURE_REASON_ERROR_PLACING_ORDER_WITH_VENDOR = 4;

  // Generic error due to unexpected IFT status
  WALLET_ORDER_FAILURE_REASON_UNEXPECTED_IFT_STATUS = 6;

  // Generic error while sending sell order to broker
  WALLET_ORDER_FAILURE_REASON_ERROR_SENDING_WITHDRAW_FUNDS_ORDER_TO_VENDOR = 8;

  // Generic error while tracking order status with broker
  WALLET_ORDER_FAILURE_REASON_ERROR_TRACKING_ORDER_STATUS_WITH_VENDOR = 9;

  // Signifies that signal is not received during defined wait time, possible reason could be payment not credited to user or
  // bug in signalling flow
  WALLET_ORDER_FAILURE_REASON_TIMED_OUT_WAITING_FOR_PAYMENT_RECEIVED_SIGNAL_FOR_WITHDRAW_FUNDS_ORDER = 10;

  // generic error encountered while waiting for payment received signal
  WALLET_ORDER_FAILURE_REASON_ERROR_WAITING_FOR_PAYMENT_RECEIVED_SIGNAL_FOR_WITHDRAW_FUNDS_ORDER = 11;

  // generic error encountered while waiting for payment initiated signal
  WALLET_ORDER_FAILURE_REASON_ERROR_WAITING_FOR_PAYMENT_INITIATED_SIGNAL_FOR_WITHDRAW_FUNDS_ORDER = 12;

  // Signifies that payment initiated signal is not received during defined wait time
  WALLET_ORDER_FAILURE_REASON_TIMED_OUT_WAITING_FOR_PAYMENT_INITIATED_SIGNAL_FOR_WITHDRAW_FUNDS_ORDER = 13;

  // order creation failed due to max allowed limit to add funds for the day already consumed
  WALLET_ORDER_FAILURE_REASON_BREACHED_MAX_ALLOWED_LIMIT_TO_ADDED_FUNDS_FOR_DAY = 14;

  // order creation failed due to max allowed to add funds limit for the financial year already consumed
  WALLET_ORDER_FAILURE_REASON_BREACHED_MAX_ALLOWED_LIMIT_TO_ADDED_FUNDS_IN_FINANCIAL_YEAR = 15;

  // order creation failed as user is not a vintage Fi user
  // vintage is config driven. eg: 6 Months
  WALLET_ORDER_FAILURE_REASON_INSUFFICIENT_SAVINGS_ACCOUNT_VINTAGE = 16;

  // order creation failed as user have not performed min number of required transactions through Fi
  WALLET_ORDER_FAILURE_REASON_INSUFFICIENT_NO_OF_TRANSACTIONS = 17;

  // order creation failed as partner bank does not allow foreign remittance for the actor
  // reason could be - not a full KYC user, NRI user etc
  WALLET_ORDER_FAILURE_REASON_FOREIGN_REMITTANCE_NOT_ALLOWED = 18;

  // user blacklisted for foreign fund transfer with Fi
  WALLET_ORDER_FAILURE_REASON_USER_BLACKLISTED = 19;
  // order amount is suspected only if all the following conditions are met:
  // 1. International Transaction amount is >2 L
  // 2. International Transaction amount/ Account Balance > 80%
  // 3. International Transaction Amount/ Max(Last 5 Credit transaction Amount) > 90%
  // https://docs.google.com/document/d/1OYrGhaNFnJDY8CcDq6_XgtmRAXFoLeOBzGW22PQ4nWM/edit#bookmark=id.kqm7896u5dv
  WALLET_ORDER_FAILURE_REASON_ORDER_AMOUNT_SUSPECTED = 20;

  // Can be because of partner bank rejecting a user's order
  WALLET_ORDER_FAILURE_REASON_OUTWARD_SWIFT_TRANSFER_FAILED = 21;

  // Forex deal used to place order has expired before SWIFT transfer was completed
  WALLET_ORDER_FAILURE_REASON_FX_DEAL_EXPIRED = 22;

  // order creation failed due to max allowed sof based add funds limit  already consumed for the financial year
  WALLET_ORDER_FAILURE_REASON_SOF_REMITTANCE_LIMIT_BREACHED = 23;

  // KYC status is not ok, according to the partner remitter bank
  WALLET_ORDER_FAILURE_REASON_KYC_CHECK_FAILED_WITH_BANKING_PARTNER = 24;

  // PAN status is not ok, according to the partner remitter bank
  WALLET_ORDER_FAILURE_REASON_PAN_CHECK_FAILED_WITH_BANKING_PARTNER = 25;

  // Wallet Order will be failed if order retried on the wrong day
  WALLET_ORDER_FAILURE_REASON_ORDER_RETRIED_ON_WRONG_DAY = 26;

  // Wallet Order will be failed if user has insufficient balance in their savings account
  WALLET_ORDER_FAILURE_REASON_ORDER_FAILED_DUE_TO_INSUFFICIENT_BALANCE = 27;
}

message WalletOrderProcessingStageDetails {
  string stage = 1;
  celestial.workflow.stage.Status status = 2;
  google.protobuf.Timestamp last_updated_timestamp = 3;
  // eta for stage completion
  // nil if stage has already transitioned to terminal state
  // Note: not all stages require ETAs, only stages which are expected to take longer duration to complete are expected to show ETA
  // eta will be only populated for few eligible stages
  google.protobuf.Timestamp eta = 4;
}
