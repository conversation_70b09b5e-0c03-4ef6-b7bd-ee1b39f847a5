syntax = "proto3";

package usstocks.order;

import "api/usstocks/enums.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/order";
option java_package = "com.github.epifi.gamma.api.usstocks.order";

//go:generate gen_sql -types=AggregatedRemittanceTransaction,AggregatedRemittanceTransactionType,AggregatedRemittanceTransactionState,AggregatedGstReportingState
message AggregatedRemittanceTransaction {
  string id = 1;
  // represent type of aggregated remittance transaction
  RemittanceType remittance_type = 2;
  // represent actor_id for user
  string actor_id = 3;
  vendorgateway.Vendor vendor = 4;
  // represents the aggregated amount that the user needs to be paid
  google.type.Money total_amount = 5;
  // Represent gst charged for aggregated transaction on given forex rate
  google.type.Money gst_charged = 6;
  // Represent state of aggregated transaction and behaviour of txn
  AggregatedRemittanceTransactionState transaction_state = 7;
  // represent internal forex rate table identifier
  // it represent the forex rate in which transaction was initiated
  string forex_rate_id = 8;
  // external id which help in sharing with external vendor
  // eg: we can use this field while forming remark and consume during credit notification
  string external_id = 9;
  // remittance batch_id. same as the batch_id added in aggregated txn model.
  // eg: file gen attempt client req id = batch id in case of direct bank account transfers
  string batch_id = 10;
  // a business-logic-based ID to uniquely identify transactions
  // this is used to not create duplicate transactions when business logic is re-executed for a batch
  string dedupe_id = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp deleted_at = 14;
  AggregatedGstReportingState gst_reporting_state = 15;
  // If GST is reported via API, this represents the Vendor GST Request ID.
  // This ID allows us to track and understand the status with the vendor.
  string gst_reporting_vendor_request_id = 16;
  // unique gst invoice number for reporting GST amount to federal
  string gst_invoice_number = 17;
}

// GST Reporting State Orchestration

// Initial State: INITIATED
// - The GST reporting process starts in this state.
// - currently it is populating of gst reporting vendor request id

// Reporting State: REPORTED
// - After initiating the process, the GST is reported.
// - If reporting is successful, transition to VERIFIED.
// - If reporting fails, transition to FAILED.
enum AggregatedGstReportingState {
  AGGREGATED_GST_REPORTING_STATE_UNSPECIFIED = 0;
  AGGREGATED_GST_REPORTING_STATE_INITIATED = 1;
  AGGREGATED_GST_REPORTING_STATE_REPORTED = 2;
  AGGREGATED_GST_REPORTING_STATE_VERIFIED = 3;
  AGGREGATED_GST_REPORTING_STATE_FAILED = 4;
}

enum AggregatedRemittanceTransactionFieldMask {
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_UNSPECIFIED = 0;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_ID = 1;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_REMITTANCE_TYPE = 2;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_ACTOR_ID = 3;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_VENDOR = 4;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_AMOUNT = 5;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_GST_CHARGED = 6;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_STATE = 7;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_FOREX_RATE = 8;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_EXTERNAL_ID = 9;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_BATCH_ID = 10;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_DEDUPE_ID = 11;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_CREATED_AT = 12;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_UPDATED_AT = 13;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_DELETED_AT = 14;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_GST_REPORTING_STATE = 15;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_GST_REPORTING_VENDOR_REQUEST_ID = 16;
  AGGREGATED_REMITTANCE_TRANSACTION_FIELD_MASK_GST_INVOICE_NUMBER = 17;
}
