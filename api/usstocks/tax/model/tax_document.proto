//go:generate gen_sql -types=TimeRangeType,UsStockDocumentType,DocumentSource
syntax = "proto3";

package api.usstocks.tax.model;

import "api/usstocks/tax/enums.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/tax/model";
option java_package = "com.github.epifi.gamma.api.usstocks.tax.model";

message TaxDocument {
  string id = 1;
  // in case the document is system generated, this field contains the foreign key reference of the params used to generated it
  string tax_document_params_id = 2;
  string account_id = 3 [(validate.rules).string.min_len = 1];
  // a identifier used in naming the document given to the user
  // this can be helpful in debugging user reported documents
  string external_id = 4 [(validate.rules).string.min_len = 1];
  // TimeRangeType determines the type of time range e.g. financial year
  TimeRangeType time_range_type = 5 [(validate.rules).enum = {not_in: [0]}];
  // computed till gives the end timestamp of the time range e.g. Mar 31 2024, 23:59:59 for Financial year
  // Note: computed_from can be derived using time_range_type and computed_till
  google.protobuf.Timestamp computed_till = 6 [(validate.rules).timestamp.required = true];
  // determines the type of document e.g. capital gain
  UsStockDocumentType document_type= 7 [(validate.rules).enum = {not_in: [0]}];
  // path where the document is stored
  string path_to_document = 8 [(validate.rules).string.min_len = 1];
  // source of document e.g. manual upload, system generated, etc
  DocumentSource source = 9 [(validate.rules).enum = {not_in: [0]}];
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  google.protobuf.Timestamp deleted_at = 12;
}

enum TaxDocumentFieldMask {
  TAX_DOCUMENT_FIELD_MASK_UNSPECIFIED = 0;
  TAX_DOCUMENT_FIELD_MASK_ID = 1;
  TAX_DOCUMENT_FIELD_MASK_TAX_DOCUMENT_PARAMS_ID = 2;
  TAX_DOCUMENT_FIELD_MASK_ACCOUNT_ID = 3;
  TAX_DOCUMENT_FIELD_MASK_EXTERNAL_ID = 4;
  TAX_DOCUMENT_FIELD_MASK_TIME_RANGE_TYPE = 5;
  TAX_DOCUMENT_FIELD_MASK_COMPUTED_TILL = 6;
  TAX_DOCUMENT_FIELD_MASK_DOCUMENT_TYPE = 7;
  TAX_DOCUMENT_FIELD_MASK_PATH_TO_DOCUMENT = 8;
  TAX_DOCUMENT_FIELD_MASK_SOURCE = 9;
  TAX_DOCUMENT_FIELD_MASK_CREATED_AT = 10;
  TAX_DOCUMENT_FIELD_MASK_UPDATED_AT = 11;
  TAX_DOCUMENT_FIELD_MASK_DELETED_AT = 12;
}
