//go:generate gen_sql -types=DocumentParams
syntax = "proto3";

package api.usstocks.tax.model;

import "api/usstocks/tax/enums.proto";
import "api/usstocks/tax/documentparams/params.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/tax/model";
option java_package = "com.github.epifi.gamma.api.usstocks.tax.model";


message TaxDocumentParams {
  string id = 1;
  // type of document e.g. capital gain, schedule fa, etc
  UsStockDocumentType document_type = 2 [(validate.rules).enum = {not_in: [0]}];
  // internal us stocks account id
  string account_id = 3 [(validate.rules).string.min_len = 1];
  // params data to be used to generate the documents
  documentparams.DocumentParams params = 4 [(validate.rules).message.required = true];
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp deleted_at = 7;
}


enum TaxDocumentParamsFieldMask {
  TAX_DOCUMENT_PARAMS_FIELD_MASK_UNSPECIFIED = 0;
  TAX_DOCUMENT_PARAMS_FIELD_MASK_ID = 1;
  TAX_DOCUMENT_PARAMS_FIELD_MASK_DOCUMENT_TYPE = 2;
  TAX_DOCUMENT_PARAMS_FIELD_MASK_ACCOUNT_ID = 3;
  TAX_DOCUMENT_PARAMS_FIELD_MASK_PARAMS = 4;
  TAX_DOCUMENT_PARAMS_FIELD_MASK_WORKFLOW_ID = 5;
  TAX_DOCUMENT_PARAMS_FIELD_MASK_CREATED_AT = 6;
  TAX_DOCUMENT_PARAMS_FIELD_MASK_UPDATED_AT = 7;
  TAX_DOCUMENT_PARAMS_FIELD_MASK_DELETED_AT = 8;
}


