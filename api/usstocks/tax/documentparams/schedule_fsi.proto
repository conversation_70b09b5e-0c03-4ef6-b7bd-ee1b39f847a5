syntax = "proto3";

package api.usstocks.tax.documentparams;

import "api/usstocks/tax/wrapper/moneywrapper.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/tax/documentparams";
option java_package = "com.github.epifi.gamma.api.usstocks.tax.documentparams";

// refer PRD for more details: https://docs.google.com/document/d/1dj4Nk82RAnwQn-Ukrq6MnWf_Qnt0GBWzo2EIf0SsldE/edit#heading=h.rnbwm3sex584
message ScheduleFSIParams {
  repeated IncomeFromForeignSourcesDetails incomes_from_outside_sources_details = 1;
}

message IncomeFromForeignSourcesDetails {
  string country_name = 1;
  usstocks.tax.wrapper.UsdInrWrapper stcg_amount = 2;
  usstocks.tax.wrapper.UsdInrWrapper ltcg_amount = 3;
  usstocks.tax.wrapper.UsdInrWrapper gain_from_dividends = 4;
  usstocks.tax.wrapper.UsdInrWrapper gain_from_interests = 5;
  usstocks.tax.wrapper.UsdInrWrapper tax_paid_on_dividends = 6;
  usstocks.tax.wrapper.UsdInrWrapper tax_paid_on_interests = 7;
}
