syntax = "proto3";

package usstocks.payload;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/device.proto";
import "api/usstocks/account/account.proto";
import "api/usstocks/account/investor.proto";
import "api/usstocks/enums.proto";
import "api/usstocks/order/order.proto";
import "api/vendorgateway/stocks/service.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/payload";
option java_package = "com.github.epifi.gamma.api.usstocks.payload";

// signal payload for CollectPoaAndPoiSignalPayload
message CollectPoaAndPoiSignalPayload {}

// signal payload for CollectDisclosuresSignal
message CollectDisclosuresSignalPayload {}

// signal payload for CollectEmploymentDetailsSignal
message CollectEmploymentDetailsSignalPayload {}

// signal payload for CollectInvestmentInterestSignal
message CollectInvestmentInterestSignalPayload {}

// signal payload for CollectAgreementsSignal
message CollectAgreementsSignalPayload {}

// signal payload for CollectSourceOfFundsSignal
message CollectSourceOfFundsSignalPayload {}

// payload to be sent as payment status signal for Sell Order in USStocks
message SellPaymentStatusSignal {
  SellPaymentStatusType status = 1;
  // GST deduction for the sell order
  google.type.Money gst_amount = 2;
  // forex rate that was applicable for the corresponding inward swift transfer
  google.type.Money forex_rate = 3;
  // source from where forex rate is fetched
  usstocks.order.ForexRateProvenance fore_rate_provenance = 4;
  // Id of the applicable forex rate
  string forex_rate_id = 5;

  // amount (in INR) remitted from pool account of Fi to user's account
  google.type.Money remittance_amount_inr = 6;
}

// payload to be sent as order status signal for USStocks
message VendorOrderUpdateSignalPayload {
  // updated order from vendor
  vendorgateway.stocks.Order order = 1;
}

message VendorAccountUpdateSignalPayLoad {
  // Account Update status
  account.AccountStatus account_status = 1;
}

message WalletFundTransferStatusUpdateSignalPayload {
  // latest wallet transfer status
  usstocks.WalletTransferStatus transfer_status = 1;
  // journal order created at vendor as part of wallet fund transfer
  // Deprecated - order creation timestamp should be part of order creation request and not of order status update
  google.protobuf.Timestamp order_creation_timestamp_at_vendor = 3 [deprecated = true];
}

enum SellPaymentStatusType {
  SELL_PAYMENT_STATUS_TYPE_UNSPECIFIED = 0;
  // This signal is initiated when partner bank acknowledges the given inward fund transfer
  SELL_PAYMENT_STATUS_TYPE_ACKNOWLEDGE = 1;
  // This signal is initiated when funds are received at the user account
  SELL_PAYMENT_STATUS_TYPE_RECEIVED = 2;
  // This signal is initiated when we share inward remittance details is shared with partner bank
  SELL_PAYMENT_STATUS_TYPE_INITITATED = 3;
}

message ProxySignalRequest {
  enum RequestType {
    REQUEST_TYPE_UNSPECIFIED = 0;
    REQUEST_TYPE_NEXT_ACTION = 1;
    REQUEST_TYPE_COLLECT_DATA = 2;
  }
  // workflow id of the sync-proxy workflow
  string calling_workflow_id = 1;
  api.typesv2.common.Platform platform = 2;
  uint32 app_version = 3;
  RequestType request_type = 4;
  // if request type is collect data then request will contain collected data
  account.Disclosures disclosures = 5;
  account.Agreements agreements = 6;
  account.UserInvestmentInterest investment_interest = 7;
  account.OnboardingStep onboarding_step = 8;
}

message ProxySignalResponse {
  frontend.deeplink.Deeplink deeplink = 1;
  string error = 2;
}

// signal payload for PanManualReviewActionSignal
message PanManualReviewActionSignalPayload {}

// signal for notifying us stocks workflow of order initiation failure
// eg: failure in starting corresponding IFT workflow, RPC failure etc
// since there are multiple steps involved while initiating an order, If there is failure after workflow is initiated
// corresponding workflow will be notified. Workflow can decide to mark the order failed gracefully
message OrderInitiationFailedSignal {}

// UsStocksInwardRemittanceAckSignal is used for notify inward remittance workflow that
// ops agent has acknowledged that the partner bank has received the inward remittance amount.
message InwardRemittanceAckSignal {
  // forex rate id being used for USD to INR conversion.
  string forex_rate_id = 1;
}

// UsStocksInwardRemittanceCompletionSignal is used for notify inward remittance workflow that
// remittance process is completed from our side. Currently we're considering creation of TTUM file
// as the completion step.
message InwardRemittanceCompletionSignal {
  // transaction id of swift
  string swift_transaction_id = 1;
  // forex rate used to convert USD to INR
  google.type.Money forex_rate = 2;
  // amount being transferred to user's accounts.
  google.type.Money amount_transferred = 3;
}
