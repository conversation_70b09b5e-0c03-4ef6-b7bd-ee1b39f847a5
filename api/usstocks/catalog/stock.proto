//go:generate gen_sql -types=CompanyInfo,CompanyName,CompanyDescription,CompanyAddress,CompanyType,CompanyEmployeeInfo,MarketCap,FinancialInfo,IncomeStatement,BalanceSheet,CashFlowStatement,ProfitabilityRatio,EfficiencyRatio,FinancialHealthRatio,GrowthRatio,ValuationRatio,EstimatesInfo,AnalystRecommendations,AnalystEstimates,TargetPriceEstimates,PeriodicTargetPriceEstimates
// This file contains proto specific to stock
syntax = "proto3";

package api.usstocks.catalog;

import "google/type/money.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/catalog";
option java_package = "com.github.epifi.gamma.api.usstocks.catalog";

// CompanyInfo contains details like name, description, address, etc. of the company
message CompanyInfo {
  // names of the company
  CompanyName company_name = 1;

  // logo of the company
  string logo_url = 2;

  // URL pointing to company website if any, e.g. https://www.apple.com
  string website_url = 3;

  // a complete and often lengthy business description of the company; often a paragraph or more. (up to 800 characters)
  CompanyDescription company_description = 4;

  // address of the company incl. city, state, country
  CompanyAddress company_address = 5;

  // the categories the company falls into, e.g REIT, S&P 500, etc.
  CompanyType company_type = 6;

  // employee counts
  CompanyEmployeeInfo company_employee_info = 7;

  // current market cap of the company, ref: https://www.investopedia.com/terms/m/marketcapitalization.asp
  MarketCap market_cap = 8;

  // contact details of company
  CompanyContactInfo company_contact_info = 10;

  // year the company was established, e.g. 2004
  int32 year_of_establishment = 9;
}

message CompanyName {
  // e.g. Apple
  string short_name = 1;

  // e.g. Apple Inc
  string standard_name = 2;
}

message CompanyDescription {
  string long_description = 1;
  string medium_description = 2;
  string short_description = 3;
}

message CompanyAddress {
  // e.g. One Apple Park Way
  repeated string address_lines = 1;

  // e.g. Cupertino
  string city = 2;

  // e.g. CA
  string state = 3;

  // e.g. USA
  string country = 4;
}

message CompanyType {
  // S&P indicator e.g. S&P500
  string snp_indicator = 1;

  // Dow Jones indicator, e.g. DJ Industrials
  string dji_indicator = 2;

  // if the company is a Special Purpose Acquisition Company, ref: https://www.investopedia.com/terms/s/spac.asp
  bool is_spac = 3;

  // if the company is a Master Limited Partnership, ref: https://www.investopedia.com/terms/m/mlp.asp
  bool is_mlp = 4;

  // if the company is a Business Development Company, ref: https://www.investopedia.com/terms/b/bdc.asp
  bool is_bdc = 5;

  // if the company is a Real Estate Investment Trust, ref: https://www.investopedia.com/terms/r/reit.asp
  bool is_reit = 6;
}

message CompanyEmployeeInfo {
  // total full-time employees in company
  int32 full_time_employee_count = 1;

  // total part-time employees in company
  int32 part_time_employee_count = 2;

  // total employees in company, ideally should be full time + part time
  int32 total_employee_count = 3;
}

// MarketCap refers to the total value of all a company's shares of a symbol.
// market-cap changes daily and need to be updated every day
message MarketCap {
  // value of market cap in currency specified
  // Price * Total SharesOutstanding.
  // The most current market cap for example, would be the most recent closing price x the most recent reported shares outstanding.
  google.type.Money market_cap_value = 1;

  // value of enterprise market cap in currency specified
  // This number tells you what cash return you would get if you bought the entire company, including its debt.
  // Enterprise Value = Market Cap + Preferred stock + Long-Term Debt And Capital Lease + Short Term Debt And Capital Lease
  // + Securities Sold But Not Yet Repurchased - Cash, Cash Equivalent And Market Securities - Securities Purchased with Agreement to Resell - Securities Borrowed.
  google.type.Money enterprise_market_cap_value = 2;

  // date on which this market cap was reported
  google.type.Date reported_date = 3;
}

message CompanyContactInfo {
  // e.g. Investor Relations, CEO, etc.
  string professional_title = 1;

  // e.g. Tim Cook, etc.
  string person_name = 2;
}

// https://www.investopedia.com/ask/answers/031815/how-are-three-major-financial-statements-related-each-other.asp
// quarterly and yearly financial data for previous periods for the given stock symbol
// expected to be present for recent quarters and recent years only
message FinancialInfo {
  repeated BalanceSheet quarterly_balance_sheets = 1;

  repeated BalanceSheet yearly_balance_sheets = 2;

  repeated CashFlowStatement quarterly_cash_flow_statements = 3;

  repeated CashFlowStatement yearly_cash_flow_statements = 4;

  repeated IncomeStatement quarterly_income_statements = 5;

  repeated IncomeStatement yearly_income_statements = 6;

  repeated ProfitabilityRatio quarterly_profitability_ratios = 7;

  repeated ProfitabilityRatio yearly_profitability_ratios = 8;

  repeated EfficiencyRatio quarterly_efficiency_ratios = 9;

  repeated EfficiencyRatio yearly_efficiency_ratios = 10;

  repeated FinancialHealthRatio quarterly_financial_health_ratios = 11;

  repeated FinancialHealthRatio yearly_financial_health_ratios = 12;

  repeated GrowthRatio quarterly_growth_ratios = 13;

  repeated GrowthRatio yearly_growth_ratios = 14;

  repeated ValuationRatio quarterly_valuation_ratios = 15;

  repeated ValuationRatio yearly_valuation_ratios = 16;

  ValuationRatio latest_valuation_ratio = 17;
}

// IncomeStatement reports a company’s financial performance over a specific time period
// ref: https://www.investopedia.com/terms/i/incomestatement.asp
message IncomeStatement {
  // total revenue, ref: https://www.investopedia.com/terms/r/revenue.asp
  // diff b/w revenue and income: https://www.investopedia.com/ask/answers/122214/what-difference-between-revenue-and-income.asp
  google.type.Money total_revenue = 1;

  // expenses incurred by company
  google.type.Money total_expenses = 2;

  // profit a company makes after deducting the costs associated with making and selling its products, or the costs associated with providing its services
  // ref: https://www.investopedia.com/terms/g/grossprofit.asp
  google.type.Money gross_profit = 3;

  // net income after taxes, ref: https://www.investopedia.com/terms/n/net-income-after-taxes-niat.asp
  // diff b/w revenue and income: https://www.investopedia.com/ask/answers/122214/what-difference-between-revenue-and-income.asp
  google.type.Money net_income = 4;

  // earnings before interest, taxes, depreciation, and amortization, ref: https://www.investopedia.com/terms/e/ebitda.asp
  google.type.Money ebitda = 5;

  google.type.Date period_ending_date = 6;

  google.type.Date report_date = 7;
}

// BalanceSheet reports a company's assets, liabilities, and shareholder equity at a specific point in time
// ref: https://www.investopedia.com/terms/b/balancesheet.asp
message BalanceSheet {
  // cash, accounts receivable/customers’ unpaid bills, and inventories of raw materials and finished goods
  google.type.Money total_assets = 1;

  // accounts payable and debts
  google.type.Money total_liabilities = 2;

  // difference between assets and liabilities
  google.type.Money working_capital = 3;

  google.type.Date period_ending_date = 4;

  google.type.Date report_date = 5;
}

// CashFlowStatement summarizes the movement of cash and cash equivalents in and out of a company
// ref: https://www.investopedia.com/investing/what-is-a-cash-flow-statement/
message CashFlowStatement {
  // cash generated from business operations before interest payments and after subtracting capital expenditures
  google.type.Money free_cash_flow = 1;

  // cash generated from business operations or activities
  google.type.Money operating_cash_flow = 2;

  // cash generated or spent from various investment-related activities
  google.type.Money investing_cash_flow = 3;

  // net flows of cash that are used to fund the company and its capital
  google.type.Money financing_cash_flow = 4;

  google.type.Date period_ending_date = 5;

  google.type.Date report_date = 6;
}

// ProfitabilityRatio indicates profit margins of the company
message ProfitabilityRatio {
  // Gross Margin: refers to the ratio of gross profit to revenue.
  double gross_margin = 1;

  // EBITDA Margin: refers to the ratio of earnings before interest, taxes and depreciation and amortization to revenue
  double ebitda_margin = 2;

  // Net Margin: refers to the ratio of net income to revenue
  double net_margin = 3;

  google.type.Date period_ending_date = 4;

  google.type.Date report_date = 5;
}

// EfficiencyRatio indicates efficiency of the company stock in market
message EfficiencyRatio {
  // Return on Equity, ref: https://www.investopedia.com/terms/r/returnonequity.asp
  double roe = 1;

  // Return on Invested Capital, ref: https://www.investopedia.com/terms/r/returnoninvestmentcapital.asp
  double roic = 2;

  google.type.Date period_ending_date = 3;

  google.type.Date report_date = 4;
}

// FinancialHealthRatio indicates financial health of the company
message FinancialHealthRatio {
  // debt to equity ratio of company, ref: https://www.investopedia.com/terms/d/debtequityratio.asp
  double total_debt_to_equity = 1;

  google.type.Date period_ending_date = 2;

  google.type.Date report_date = 3;
}

// GrowthRatio indicates historical growth of the company, including growth in revenue, earnings per share, etc. of the company
// For quarterly reporting, last 3 months growth ratios are considered
// For annual reporting, last 1 year growth ratios are considered
message GrowthRatio {
  // For quarterly reporting, this indicates 3-month book value per share growth year over year
  // For annual reporting, this indicates annual growth rate of book value per share
  double diluted_eps_growth = 1;

  // For quarterly reporting, this indicates 3-month total revenue growth year over year
  // For annual reporting, this indicates annual growth rate of total revenue over 1 year
  double revenue_growth = 2;

  google.type.Date period_ending_date = 3;

  google.type.Date report_date = 4;
}

// ref: https://www.fidelity.com/learning-center/trading-investing/fundamental-analysis/company-valuation-ratios
message ValuationRatio {
  // price to earnings per share ratio, ref: https://www.investopedia.com/terms/p/price-earningsratio.asp
  double price_to_eps = 1;

  // ref: https://www.investopedia.com/terms/p/price-to-bookratio.asp
  double price_to_book = 2;

  // dividend paid by company each year relative to its stock price, ref: https://www.investopedia.com/terms/d/dividendyield.asp
  double dividend_yield = 3;

  // Enterprise multiple, ref: https://www.investopedia.com/terms/e/ev-ebitda.asp
  double ev_to_ebitda = 4;

  google.type.Date as_of_date = 5;
}

// EstimatesInfo are details related to estimates made on future of a company / stock by market analysts
message EstimatesInfo {
  AnalystRecommendations analyst_recommendations = 1;

  AnalystEstimates analyst_estimates = 2;
}

message AnalystRecommendations {
  // number of analysts recommending buying stock
  int32 buy = 1;

  // number of analysts recommending stock will outperform
  int32 outperform = 2;

  // number of analysts recommending holding on to stock
  int32 hold = 3;

  // number of analysts recommending stock will underperform
  int32 underperform = 4;

  // number of analysts recommending selling stock
  int32 sell = 5;

  // number of analysts with no opinion
  int32 no_opinion = 6;

  google.type.Date as_of_date = 7;
}

message AnalystEstimates {
  TargetPriceEstimates target_price_estimates = 1;
}

message TargetPriceEstimates {
  // estimated target price made by analysts in diff periods, e.g. current, 7 days ago, 30 days ago, etc.
  repeated PeriodicTargetPriceEstimates periodic_target_price_estimates = 1;
}

message PeriodicTargetPriceEstimates {
  // estimated highest price of stock
  double high = 3;

  // estimated lowest price of stock
  double low = 4;

  // estimated mean price of stock
  double mean = 5;

  // estimated median price of stock
  double median = 6;

  // TODO(Brijesh): Confirm meaning of value
  int32 num_of_estimates = 8;
}
