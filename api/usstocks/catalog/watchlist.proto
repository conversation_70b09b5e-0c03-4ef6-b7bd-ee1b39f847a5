syntax = "proto3";

package api.usstocks.catalog;

import "google/protobuf/timestamp.proto";
import "api/usstocks/catalog/usstock.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/catalog";
option java_package = "com.github.epifi.gamma.api.usstocks.catalog";

// Watchlist table contains all the user's watchlist.
message Watchlist {
  string id = 1;
  string actor_id = 2;
  string name = 3;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp deleted_at = 7;
}

// WatchlistStockMapping table have a mapping between a watchlist and a stock
message WatchlistStockMapping {
  string watchlist_id = 1;
  string stock_id = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  // deleted_at will be representing the deleted_at_unix in model struct/DB
  google.protobuf.Timestamp deleted_at = 5;
}

// WatchlistWithStocks contains the watchlist details along with the stocks in the watchlist
message WatchlistWithStocks {
  Watchlist watchlist = 1;
  repeated WatchlistedStock watchlisted_stocks = 2;
  // total number of stocks in the watchlist irrespective of pagination
  uint32 total_number_of_stocks = 3;
}

// WatchlistedStock is a wrapper over Stock to add additional fields specific to watchlist
message WatchlistedStock {
  Stock stock = 1;
  // TODO(mounish): add additional information like price increased after adding to watchlist, etc.
}

enum WatchlistAction {
  WATCHLIST_ACTION_UNSPECIFIED = 0;
  // add stock to user's watchlist
  WATCHLIST_ACTION_ADD = 1;
  // remove stock from user's watchlist
  WATCHLIST_ACTION_REMOVE = 2;
}

enum WatchlistFieldMask {
  WATCHLIST_FIELD_MASK_UNSPECIFIED = 0;
  WATCHLIST_FIELD_MASK_ID = 1;
  WATCHLIST_FIELD_MASK_ACTOR_ID = 2;
  WATCHLIST_FIELD_MASK_NAME = 3;
  WATCHLIST_FIELD_MASK_CREATED_AT = 4;
  WATCHLIST_FIELD_MASK_UPDATED_AT = 5;
  WATCHLIST_FIELD_MASK_DELETED_AT = 6;
}

enum WatchlistStockMappingFieldMask {
  WATCHLIST_STOCK_MAPPING_FIELD_MASK_UNSPECIFIED = 0;
  WATCHLIST_STOCK_MAPPING_FIELD_MASK_WATCHLIST_ID = 1;
  WATCHLIST_STOCK_MAPPING_FIELD_MASK_STOCK_ID = 2;
  WATCHLIST_STOCK_MAPPING_FIELD_MASK_CREATED_AT = 3;
  WATCHLIST_STOCK_MAPPING_FIELD_MASK_UPDATED_AT = 4;
  WATCHLIST_STOCK_MAPPING_FIELD_MASK_DELETED_AT = 5;
}
