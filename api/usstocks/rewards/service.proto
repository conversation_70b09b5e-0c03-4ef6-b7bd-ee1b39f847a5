syntax = "proto3";

package usstocks.rewards;

import "api/rpc/status.proto";
import "api/usstocks/rewards/reward_request.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/rewards";
option java_package = "com.github.epifi.gamma.api.usstocks.rewards";

/*
  Uss Reward Manager service is used for managing reward related orchestration, storage and other crud operations
  It also provides an abstraction over the 'Rewards' service for other Uss services and workers
 */
service UssRewardManager {
  // RPC used for checking the user reward status
  // This would call Rewards service to fetch rewards related data and provide relevant reward offer related info
  rpc GetRewardDetails (GetRewardDetailsRequest) returns (GetRewardDetailsResponse) {};
  // RPC to initiate reward processing if not triggered and fetch status for initiated reward process
  rpc ProcessReward (ProcessRewardRequest) returns (ProcessRewardResponse) {}
}

message GetRewardDetailsRequest {
  string actor_id = 1;
}

// TODO: Add relevant fields when more clarit
message GetRewardDetailsResponse {
  // rpc status
  rpc.Status status = 1;
  RewardDetails reward_details = 2;
  StockRewardDisplayDetails display_details = 3;
}

message StockRewardDisplayDetails {
  // list of options that would be or was presented to user.
  // User is expected to choose one of the multiple options for reward
  repeated StockRewardOptionDetails options = 1;
  // amount value of stock that will be offered as reward to user
  google.type.Money amount = 2;
  // background lottie url
  string bg_lottie_url = 3;
}

message StockRewardOptionDetails {
  string stock_name = 1;
}


message ProcessRewardRequest {
  string actor_id = 1;
  // reward identifier for which this request is being placed
  // used for deduping requests as well to prevent duplicate fulfillment requests
  string reward_id = 2;
  // stock identifier for the stock which is rewarded to the user
  string stock_id = 3;
  // amount of stock in inr which is rewarded to the user
  google.type.Money reward_amount_inr = 4;
}

message ProcessRewardResponse {
  enum Status {
    OK = 0;
    // if usstocks reward is unable to fulfill requirement due to expected reason
    REWARD_PROCESSING_FAILED = 101;
    // if usstocks reward is unable to fulfill requirement due to unexpected reason
    REWARD_PROCESSING_REQUIRE_MANUAL_INTERVENTION = 102;
    // reward allocation process is initiated and being processed at usstocks system
    REWARD_PROCESSING_IN_PROGRESS = 103;
  }
  // rpc status
  rpc.Status status = 1;
}


message RewardDetails {
  // reward_id represent user is eligible reward in rewards system
  string reward_id = 1;
  RewardStatus status = 2;
  // reward request entity at us stocks db
  RewardRequest reward_request = 3;
}

enum RewardStatus {
  REWARD_STATUS_UNSPECIFIED = 0;
  // user is not eligible for any uss reward
  REWARD_STATUS_INELIGIBLE_FOR_REWARD = 1;
  // user is eligible for reward but it is in lock state
  REWARD_STATUS_LOCK = 2;
  // user is eligible for reward and user need to claim it
  REWARD_STATUS_TO_BE_CLAIM = 3;
  // user is eligible for reward and allocation is in process
  REWARD_STATUS_PROCESSING = 4;
  // user got the reward
  REWARD_STATUS_SUCCESS = 5;
  // user got reward claim was failed
  REWARD_STATUS_FAILED = 6;
}
