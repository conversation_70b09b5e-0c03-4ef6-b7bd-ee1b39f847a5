syntax = "proto3";

package usstocks.rewards;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/usstocks/rewards";
option java_package = "com.github.epifi.gamma.api.usstocks.rewards";


//go:generate gen_sql -types=RewardRequestType,RewardRequestProcessingStatus,RewardFulfillmentDetails
// Reward request is used for orchestrating fulfillment of a reward given to the user
message RewardRequest {
  // unique identifier
  string id = 1;
  // actor id for which the reward offer is applicable
  string actor_id = 2;
  // request type
  RewardRequestType reward_request_type = 3;
  // reward request processing status
  RewardRequestProcessingStatus status = 4;
  // workflow request id for processing the fulfillment of the reward
  string workflow_req_id = 5;
  // Details for fulfilling the reward for the user
  RewardFulfillmentDetails reward_fulfillment_details = 6;
  // reward id passed when rewards raises the fulfillment request
  // used for de-duping requests
  string reward_id = 7;
  // Only applicable for 'FAILED' or 'MANUAL_INTERVENTION'
  // max length '100' characters
  string failure_debug_reason = 8;

  google.protobuf.Timestamp created_at = 9;
  google.protobuf.Timestamp updated_at = 10;
  google.protobuf.Timestamp deleted_at = 11;
}


enum RewardRequestType {
  REWARD_REQUEST_TYPE_UNSPECIFIED = 0;
  // Rewards for users to nudge them to complete onboarding and buy stocks
  // Ref: TBD
  REWARD_REQUEST_TYPE_BUY_STOCKS_ONBOARDING = 1;
}

enum RewardRequestProcessingStatus {
  REWARD_REQUEST_PROCESSING_STATUS_UNSPECIFIED = 0;
  // Reward has been claimed/auto-claimed by the user and received for fulfillment
  RRPS_CREATED = 1;
  // Reward fulfillment processing has been initiated for the same
  RRPS_INITIATED = 2;
  // Reward has been successfully given to the user
  RRPS_SUCCESS = 3;
  // Reward processing is stuck due to unexpected error
  // Check failure_debug_reason to debug
  RRPS_MANUAL_INTERVENTION = 4;
  // Reward processing is marked failed
  // Check failure_debug_reason to know exact reason
  RRPS_FAILED = 5;
  // The corresponding wallet & stock orders have been created in uss systems as part of fulfillment
  RRPS_ORDERS_CREATED = 6;
}

message RewardFulfillmentDetails {
  // stock id selected/claimed for fulfillment
  string stock_id = 1;
  // notional value for which the order would be placed
  google.type.Money notional_amount = 2;
  // wallet add funds order id
  // id for the order responsible for journaling the reward amount into user's wallet
  string wallet_order_id = 3;
  // stock buy order id
  // id for the order responsible for buying relevant stock from the vendor using the rewarded amount
  string stock_buy_order_id = 4;
}

enum RewardRequestFieldMask {
  REWARD_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  REWARD_REQUEST_FIELD_MASK_PROCESSING_STATUS = 1;
  REWARD_REQUEST_FIELD_MASK_WORKFLOW_REQUEST_ID = 2;
  REWARD_REQUEST_FIELD_MASK_FAILURE_DEBUG_REASON = 3;
  REWARD_REQUEST_FIELD_MASK_FULFILLMENT_DETAILS_WALLET_ORDER_ID = 4;
  REWARD_REQUEST_FIELD_MASK_FULFILLMENT_DETAILS_STOCK_ORDER_ID = 5;
}
