//go:generate gen_queue_pb
syntax = "proto3";

package creditreportv2.derivedattributes.consumer;

import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/creditreportv2/derivedattributes/consumer";
option java_package = "com.github.epifi.gamma.api.creditreportv2.derivedattributes.consumer";

service Consumer {
  // ProcessStatementGeneratedNotification for processing monthly bill generated notifications
  rpc ProcessGenerateDerivedAttributesMessage (ProcessGenerateDerivedAttributesMessageRequest) returns (ProcessGenerateDerivedAttributesMessageResponse) {}
}

message ProcessGenerateDerivedAttributesMessageRequest {
  // A set of all the common attributes to be contained in a consumer response
  queue.ConsumerRequestHeader request_header = 1;

  // actor id of the report for which derived attributes have to be generated
  string actor_id = 2;
  // credit report id for which derived attributes have to be generated
  string credit_report_id = 3;
}

message ProcessGenerateDerivedAttributesMessageResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
