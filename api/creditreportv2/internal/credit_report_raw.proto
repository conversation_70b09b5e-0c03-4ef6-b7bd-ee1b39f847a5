syntax = "proto3";

package creditreportv2;

import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/creditreportv2";
option java_package = "com.github.epifi.gamma.api.creditreportv2";

// CreditReportRaw object is used to store / manage raw credit report as sent by vendor.
message CreditReportRaw {
  string id = 1;
  string actor_id = 2;
  vendorgateway.Vendor vendor = 3;
  // byte array of report as sent by vendor
  bytes raw_report = 4;
  google.protobuf.Timestamp consent_valid_till = 5;
  google.protobuf.Timestamp created_at = 6;
}
