// protolint:disable MAX_LINE_LENGTH
//go:generate gen_sql -types=OtpInfo,ConsentInfo,CreditReportFetchType,CreditReportDownloadStatus,CreditReportDownloadSubStatus,CreditReportDownloadFieldMask,Provenance,Details
syntax = "proto3";

package creditreportv2;

import "api/frontend/deeplink/deeplink.proto";
import "api/vendorgateway/credit_report/cibil.proto";
import "api/vendorgateway/credit_report/enums.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/creditreportv2";
option java_package = "com.github.epifi.gamma.api.creditreportv2";

message CreditReportDownload {
  string id = 1;
  string request_id = 2;
  string actor_id = 3;
  // vendor from whom credit report download was attempted
  vendorgateway.Vendor vendor = 4;
  CreditReportFetchType fetch_type = 5;
  // info related to user's phone number otp verification
  OtpInfo otp_info = 6;
  // info related to download consent given by user
  ConsentInfo consent_info = 7;
  CreditReportDownloadStatus process_status = 8;
  CreditReportDownloadSubStatus process_sub_status = 9;
  // deeplink where user will be redirected to once the process is completed.
  frontend.deeplink.Deeplink redirect_deeplink = 10;
  // report download timestamp
  google.protobuf.Timestamp downloaded_at = 11;
  google.protobuf.Timestamp completed_at = 12;
  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  google.protobuf.Timestamp deleted_at = 15;
  Provenance provenance = 16;
  string credit_report_id = 17;
  string orch_id = 18;
  frontend.deeplink.Deeplink next_action = 19;
  string vendor_error = 20;
  // details stores additional data necessary to complete the donwload process and related metadata
  Details details = 21;
}

message OtpInfo {
  string request_id = 1;
  // boolean to determine if otp is already verified for the given request id
  // we will skip verification in case of otp already verified≤
  bool otp_already_verified = 2;
}

message ConsentInfo {
  ConsentStatus consent_status = 1;
  // deprecated in favor of consent_ids
  string consent_id = 2 [deprecated = true];
  // Consent request id is used for creating a unique consent in consent table for a given flow.
  string consent_req_id = 3;
  repeated string consent_ids = 4;
}

message Details {
  // download_details stores data needed to complete the download process
  DownloadDetails download_details = 1;
}

message DownloadDetails {
  oneof VendorDownloadDetails {
    CibilDownloadDetails cibil_download_details = 1;
    ExperianDownloadDetails experian_download_details = 2;
  }
}

message ExperianDownloadDetails {
  bool allow_report_fetch_without_pan = 1;
}

message CibilDownloadDetails {
  // ClientKey / PartnerCustomerId sent in requests for the user
  string customer_id = 1;
  // skip_auth is true if fulfill offer sends back success status and authentication can be skipped
  bool skip_auth = 2;
  // authentiation question(s) sent by vendor
  repeated vendorgateway.credit_report.AtlasGetAuthQuestionsResponse questions_response = 3;
  // authentication answers of various authenticatino attempts
  repeated vendorgateway.credit_report.AtlasVerifyAuthAnswersResponse answers_responses = 4;
  // verify_auth_status determines the state of customer authentication.
  // UNSPECIFIED -> answer submission not tried
  // PENDING / IN_PROGRESS -> fetch more questions
  // SUCCESS / FAILURE -> as name suggests
  vendorgateway.credit_report.AtlasResponseStatus auth_status = 5;
  // indicates if sms has been sent to user with vendor's product url
  bool product_sms_sent = 6;
  // terminal_error_reason stores error if sent back by cibil in case of a terminal / non-transient failure.
  // otherwise stores unhandled errors / scenarios
  CibilTerminalError terminal_error_reason = 7;
}

// CibilTerminalError message contains atlas error response and/or custom message that stopped the cibil download process
message CibilTerminalError {
  vendorgateway.credit_report.AtlasErrorResponse atlas_error_response = 1;
  string message = 2;
}

enum ConsentStatus {
  CONSENT_STATUS_UNSPECIFIED = 0;
  // user rejected consent
  CONSENT_STATUS_REJECTED = 1;
  // user accepted consent
  CONSENT_STATUS_GIVEN = 2;
}

enum CreditReportFetchType {
  CREDIT_REPORT_FETCH_TYPE_UNSPECIFIED = 0;
  // use previously given consent to fetch the report.
  CREDIT_REPORT_FETCH_TYPE_EXISTING = 1;
  // take a fresh consent from the user before fetching the report.
  CREDIT_REPORT_FETCH_TYPE_NEW_CONSENT = 2;
  // fetch report using existing consent and take user's consent to extend the existing consent
  CREDIT_REPORT_FETCH_TYPE_CONSENT_EXTENSION = 3;
}

enum CreditReportDownloadStatus {
  CREDIT_REPORT_DOWNLOAD_STATUS_UNSPECIFIED = 0;
  CREDIT_REPORT_DOWNLOAD_STATUS_OTP_VERIFICATION_INITIATED = 1;
  CREDIT_REPORT_DOWNLOAD_STATUS_OTP_VERIFIED = 2;
  CREDIT_REPORT_DOWNLOAD_STATUS_RECORD_CONSENT_INITIATED = 3;
  CREDIT_REPORT_DOWNLOAD_STATUS_CONSENT_RECORDED = 4;
  CREDIT_REPORT_DOWNLOAD_STATUS_REPORT_DOWNLOADED = 5;
  CREDIT_REPORT_DOWNLOAD_STATUS_SUBSCRIPTION_EXTENDED = 6;
  CREDIT_REPORT_DOWNLOAD_STATUS_FAILED = 7;
  CREDIT_REPORT_DOWNLOAD_STATUS_COMPLETED = 8;
  CREDIT_REPORT_DOWNLOAD_STATUS_RECORD_PAN_DOB_INITIATED = 9;
  CREDIT_REPORT_DOWNLOAD_STATUS_RECORD_PAN_DOB_COMPLETED = 10;
  CREDIT_REPORT_DOWNLOAD_STATUS_FULFILL_OFFER_INITIATED = 11;
  CREDIT_REPORT_DOWNLOAD_STATUS_FULFILL_OFFER_COMPLETED = 12;
  CREDIT_REPORT_DOWNLOAD_STATUS_CUSTOMER_AUTHENTICATION_INITIATED = 13;
  CREDIT_REPORT_DOWNLOAD_STATUS_CUSTOMER_AUTHENTICATION_COMPLETED = 14;
  CREDIT_REPORT_DOWNLOAD_STATUS_SEND_PRODUCT_URL_INITIATED = 15;
  CREDIT_REPORT_DOWNLOAD_STATUS_SEND_PRODUCT_URL_COMPLETED = 16;
}

enum CreditReportDownloadSubStatus {
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_UNSPECIFIED = 0;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_OTP_VERIFICATION_INIT_FAILED = 1;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_OTP_VERIFICATION_FAILED = 2;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_INIT_FAILED = 3;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_FAILED = 4;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CONSENT_NOT_GIVEN = 5;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_DOWNLOAD_FAILED = 6;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_NOT_FOUND = 7;
  // denotes credit report could not be exported by vendor. The reasons could be vendor specific
  // and typically non retryable. For example SYS100009 (too many accounts) error in Experian
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_REPORT_CANNOT_BE_EXPORTED_BY_VENDOR = 8;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_PAN_DOB_CHECK_FAILED = 9;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_FULFILL_OFFER_FAILED = 10;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_FULFILL_OFFER_SKIP_AUTH = 11;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CUSTOMER_AUTHENTICATION_FAILED = 12;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_SEND_PRODUCT_URL_FAILED = 13;
  // denotes credit history not sent back by bureau
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_NO_HISTORY = 14;
  // denotes the case where name of the user was incomplete, i.e. either first name or last name is missing
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_INCOMPLETE_USER_NAME = 15;
  // denotes difference in PII from cibil system -> permanent failure
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_SSN_MISMATCH = 16;
  // invalid name, for example, string with numbers etc.
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_USER_NAME_INCORRECT_FORMAT = 17;
  // already existing customer, can't update identifier
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_ALREADY_ENROLLED_CUSTOMER = 18;
  // invalid request enquiry data
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_INVALID_ENQUIRY_DATA = 19;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_RECORD_CONSENT_EXPIRED = 20;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_PAN_DOB_CHECK_EXPIRED = 21;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_CUSTOMER_AUTHENTICATION_EXPIRED = 22;
  CREDIT_REPORT_DOWNLOAD_SUB_STATUS_OTP_VERIFICATION_EXPIRED = 23;
}

enum Provenance {
  PROVENANCE_UNSPECIFIED = 0;
  PROVENANCE_ANALYSER = 1;
  PROVENANCE_ANALYSER_AUTO_REFRESH = 2;
  PROVENANCE_PERSONAL_LOAN = 3;
  PROVENANCE_CREDIT_CARD = 4;
  PROVENANCE_PERSONAL_LOAN_ATL = 5;
  PROVENANCE_MONEY_SECRETS = 6;
  PROVENANCE_D2H_ONBOARDING = 7;
  PROVENANCE_SA_ONBOARDING = 8;
  // Provenance for loan pre-eligibility flow
  // where user has already provided consent for fetching credit report during login.
  PROVENANCE_LOAN_PRE_ELIGIBILITY = 9;
  // Provenance for credit report analyser flow
  // where user has already provided consent for fetching credit report during login.
  PROVENANCE_WEB_CREDIT_REPORT_ANALYSER = 10;
}

enum CreditReportDownloadFieldMask {
  CREDIT_REPORT_DOWNLOAD_FIELD_UNSPECIFIED = 0;
  CREDIT_REPORT_DOWNLOAD_FIELD_ID = 1;
  CREDIT_REPORT_DOWNLOAD_FIELD_REQUEST_ID = 2;
  CREDIT_REPORT_DOWNLOAD_FIELD_ACTOR_ID = 3;
  CREDIT_REPORT_DOWNLOAD_FIELD_VENDOR = 4;
  CREDIT_REPORT_DOWNLOAD_FIELD_FETCH_TYPE = 5;
  CREDIT_REPORT_DOWNLOAD_FIELD_OTP_INFO = 6;
  CREDIT_REPORT_DOWNLOAD_FIELD_CONSENT_INFO = 7;
  CREDIT_REPORT_DOWNLOAD_FIELD_PROCESS_STATUS = 8;
  CREDIT_REPORT_DOWNLOAD_FIELD_PROCESS_SUB_STATUS = 9;
  CREDIT_REPORT_DOWNLOAD_FIELD_REDIRECT_DEEPLINK = 10;
  CREDIT_REPORT_DOWNLOAD_FIELD_DOWNLOADED_AT = 11;
  CREDIT_REPORT_DOWNLOAD_FIELD_COMPLETED_AT = 12;
  CREDIT_REPORT_DOWNLOAD_FIELD_CREATED_AT = 13;
  CREDIT_REPORT_DOWNLOAD_FIELD_UPDATED_AT = 14;
  CREDIT_REPORT_DOWNLOAD_FIELD_DELETED_AT = 15;
  CREDIT_REPORT_DOWNLOAD_FIELD_PROVENANCE = 16;
  CREDIT_REPORT_DOWNLOAD_FIELD_CREDIT_REPORT_ID = 17;
  CREDIT_REPORT_DOWNLOAD_FIELD_ORCH_ID = 18;
  CREDIT_REPORT_DOWNLOAD_FIELD_NEXT_ACTION = 19;
  CREDIT_REPORT_DOWNLOAD_FIELD_VENDOR_ERROR = 20;
  CREDIT_REPORT_DOWNLOAD_FIELD_MASK_DETAILS = 21;
}
