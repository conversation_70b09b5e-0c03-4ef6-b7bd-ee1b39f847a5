syntax = "proto3";

package timeline;

import "api/rpc/status.proto";
import "api/rpc/page.proto";
import "api/timeline/timeline.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/timeline";
option java_package = "com.github.epifi.gamma.api.timeline";

// Timeline service stores the relation between 2 actors who have
// some history of exchanging series of events.
//
// Pair of actors are unique across the service.
//
// piTos to be resolved via actorResolution table.
// Additionally if the payee actor is internal,
// then PIs to also be fetched dynamically using AccountPi relation
//
// It also enables fetching of chat heads.
service TimelineService {
  // Create creates a timeline
  // If the timeline already exists, it returns the existing timeline
  rpc Create (CreateRequest) returns (CreateResponse) {}

  // GetById gets timeline by id
  rpc GetById (GetByIdRequest) returns (GetByIdResponse) {}

  // GetChatHeads gets all chat heads for an actor id
  // TODO(kunal): Should this be paginated?
  rpc GetChatHeads (GetChatHeadsRequest) returns (GetChatHeadsResponse) {}

  // SetVisibility sets the secondary_actor_visibility to true,
  // and also updates the last_event_updated_at.
  rpc SetVisibility (SetVisibilityRequest) returns (SetVisibilityResponse) {
    option deprecated = true;
  }

  // GetByActorIds gets the timeline by the primary actor ID and secondary actor ID
  rpc GetByActorIds (GetByActorIdsRequest) returns (GetByActorIdsResponse) {}

  // TrustTimeline lets an actor mark second actor on the timeline as trusted.
  // This acts as a warning/alert to the actor before initiating a payment to unknown collect requests and helps
  // as a measure to protection against fraud actors. By default whenever a user receives a first
  // collect request from any actor, an alert pop-up is displayed to him/her on the timeline and only
  // after the user has trusted the second actor, he/she is able to act/approve on the collect request.
  rpc TrustTimeline(TrustTimelineRequest) returns (TrustTimelineResponse) {}

  // UpdateTimelineForActors updates the primary actor and secondary actor source and visibility of second actor for a timeline
  // corresponding to the given actors
  rpc UpdateTimelineForActors(UpdateTimelineForActorsRequest) returns (UpdateTimelineForActorsResponse) {}

  // BatchHardDeleteTimeline does hard delete of timelines from the database and publishes deletion event to
  // intimate respective stack holders.
  // The RPC works on all or none principle for all eligible entries (eligible = ids for which entry is present in the DB).
  // i.e. eligible timeline deletion and deletion event publishing is done in an atomic block
  // **NOTE**
  // The method only supports deletion on epiFi wealth related actors at the moment.
  rpc BatchHardDeleteTimeline (BatchHardDeleteTimelineRequest) returns (BatchHardDeleteTimelineResponse);

  // SoftDeleteTimelinesBySecondaryActorIds soft deletes all timelines for the given secondary actor ids.
  // Events for this delete are published to topic for downstream services to consume.
  rpc SoftDeleteTimelinesBySecondaryActorIds(SoftDeleteTimelinesBySecondaryActorIdsRequest) returns (SoftDeleteTimelinesBySecondaryActorIdsResponse);

  // GetTimelinesByPrimaryActorId filters timelines as requested and returns list of timeline
  // pagination is done on creation time of the timeline
  rpc GetTimelinesByPrimaryActorId(GetTimelinesByPrimaryActorIdRequest) returns (GetTimelinesByPrimaryActorIdResponse){}

  // GetSoftDeletedTimelineById will get only soft deleted timeline for the given Id.
  rpc GetSoftDeletedTimelineById(GetSoftDeletedTimelineByIdRequest) returns (GetSoftDeletedTimelineByIdResponse) {}

  // GetTimelinesForActor RPC fetches a list of timelines for an actor identified by a primary actor ID and multiple other actors identified by secondary actor IDs.
  rpc GetTimelinesForActor(GetTimelinesForActorRequest) returns (GetTimelinesForActorResponse) {}
}

message GetTimelinesForActorRequest {
  // Actor who initiates the creation of the timeline.
  string primary_actor_id = 1;
  // The other actor(s) who form a timeline with the primary actor.
  repeated string secondary_actor_ids = 2;
}

message GetTimelinesForActorResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  // List of timelines sorted in descending order of last_event_updated_at(latest first).
  repeated timeline.Timeline timelines = 2;
}


message GetTimelinesByPrimaryActorIdRequest{
  string actor_id = 1;
  // optional source filter
  repeated TimelineSource source_filter = 2;
  // page context for pagination
  rpc.PageContextRequest page_context = 3;
}

message GetTimelinesByPrimaryActorIdResponse{
  enum Status {
    // request was successful
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  repeated timeline.Timeline timelines = 2;
  rpc.PageContextResponse page_context = 3;
}

message CreateRequest {
  // Actor who initiates the creation of timeline
  string primary_actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // The other actor who forms a timeline
  string secondary_actor_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // Since primary will always be an internal actor, this will be derived from user
  string primary_actor_name = 3 [(validate.rules).string.min_len = 1];

  // This will derived from the PI that was added before the resolve timeline call
  string secondary_actor_name = 4 [(validate.rules).string.min_len = 1];

  // timeline resolution source for the primary actor
  timeline.TimelineSource primary_actor_source = 5;

  // timeline resolution source for the secondary actor
  timeline.TimelineSource secondary_actor_source = 6;

  // ownership of the timeline
  Ownership ownership = 7;

  // Enum to identify whether the timeline will be created for transactions created via new inbound notification or for transactions
  // getting updated via transaction backfill workflow.
  // If this is not provided by default we will assign TIMELINE_TYPE_UNSPECIFIED value to it.
  TimelineResolutionSource timeline_resolution_source = 8;
}

message CreateResponse {
  enum Status {
    OK = 0;
    // timeline already exists between 2 actors
    ALREADY_EXISTS = 6;
  }

  rpc.Status status = 1;
  timeline.Timeline timeline = 2;
}

message GetByIdRequest {
  string id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message GetByIdResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  timeline.Timeline timeline = 2;

  // in case the timeline for the timeline id passed in the request is soft deleted,
  // passing the id of the soft deleted timeline.If timeline is not deleted then it will be blank.
  string deleted_timeline_id = 3;
}

message GetChatHeadsRequest {
  string actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message GetChatHeadsResponse {
  enum Status {
    OK = 0;
  }

  rpc.Status status = 1;
  repeated timeline.Timeline timelines = 2;
}

message SetVisibilityRequest {
  // Actor who initiates the creation of timeline
  string primary_actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // The other actor who forms a timeline
  string secondary_actor_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // The time at which the event was updated last
  google.protobuf.Timestamp last_event_updated_at = 3;
}

message SetVisibilityResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
  }

  rpc.Status status = 1;
  timeline.Timeline timeline = 2;
}

message GetByActorIdsRequest {
  // Actor who initiates the creation of timeline
  string primary_actor_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  // The other actor who forms a timeline
  string secondary_actor_id = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message GetByActorIdsResponse {
  enum Status {
    OK = 0;
    NOT_FOUND = 5;
  }

  rpc.Status status = 1;
  timeline.Timeline timeline = 2;
}

message TrustTimelineRequest {
  string timeline_id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];

  string current_actor = 2 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message TrustTimelineResponse {
  enum Status {
    OK = 0;
    // invalid argument
    INVALID_ARGUMENT = 3;
    // timeline not found
    NOT_FOUND = 5;
    // The actor does not have permission to execute the specified operation.
    // One of the reasons could be timeline doesn't involve current actor
    PERMISSION_DENIED = 7;
    // pre condition to mark a timeline trusted failed
    // one of the reasons can be timeline is in invalid state
    FAILED_PRECONDITION = 9;
    // internal server error
    INTERNAL = 13;
    // timeline already trusted
    ALREADY_PROCESSED = 50;
  }

  rpc.Status status = 1;
  timeline.Timeline timeline = 2;
}

message UpdateTimelineForActorsRequest {
  // primary actor id
  string primary_actor_id = 1;

  // secondary actor id
  string secondary_actor_id = 2;

  // timeline resolution source for the primary actor
  timeline.TimelineSource primary_actor_source = 3;

  // timeline resolution source for the secondary actor
  timeline.TimelineSource secondary_actor_source = 4;

  // sets the visibtimeline/service.goility of the second actor
  bool set_visibility = 5;

  // It will be used to update last_event_updated_at field in timeline with the given value if passed.
  // Please, don't use it. It's passed for specific case to updated chat heads in case of remitter info backfill.
  // Passing this parameter impact the ordering of chat head in pay landing area.
  google.protobuf.Timestamp last_event_updated_at_time = 6;
}

message UpdateTimelineForActorsResponse {
  enum Status {
    OK = 0;
    // invalid argument
    INVALID_ARGUMENT = 3;
    // timeline not found
    NOT_FOUND = 5;
    // internal error
    INTERNAL = 13;
  }

  rpc.Status status = 1;
}

message BatchHardDeleteTimelineRequest {
  // actor against whom all the existing timeline needs to be deleted
  string primary_actor_id = 1;

  // actor ids with whom existing timeline needs to be deleted
  repeated string secondary_actor_ids = 2;
}

message BatchHardDeleteTimelineResponse {
  enum Status {
    OK = 0;
    // internal error
    INTERNAL = 13;
    // if mentioned actor identifiers are already deleted/not present in the data base.
    ALREADY_PROCESSED = 50;
  }
  // status of the request
  rpc.Status status = 1;
}

message SoftDeleteTimelinesBySecondaryActorIdsRequest {
  // Field Number 1 was being used by `limit` field which is removed now. So, marking 1 as reserved.
  reserved 1;

  // secondary actor ids with whom existing timeline needs to be deleted
  repeated string secondary_actor_ids = 2;

  // Field Number 2 was being used by `offset` field which is removed now. So, marking 3 as reserved.
  reserved 3;

  // timestamp starting from which records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp from_time = 4;

  // timestamp till records are scanned.
  // NOTE- the records are returned INCLUSIVE of the timestamp.
  google.protobuf.Timestamp to_time = 5;
}

message SoftDeleteTimelinesBySecondaryActorIdsResponse {
  enum Status {
    OK = 0;
    // internal error
    INTERNAL = 13;
    // timeline not found
    NOT_FOUND = 5;
  }
  // status of the request
  rpc.Status status = 1;
}

message GetSoftDeletedTimelineByIdRequest {
  string id = 1 [(validate.rules).string = {min_len: 4, max_len: 100}];
}

message GetSoftDeletedTimelineByIdResponse {
  enum Status {
    OK = 0;
    // if soft deleted timeline not found
    RECORD_NOT_FOUND = 5;

    INTERNAL = 13;

    INVALID_ARGUMENT = 3;
  }
  rpc.Status status = 1;

  // soft deleted timeline
  timeline.Timeline timeline = 2;
}

// TimelineType denotes if the timeline is created for new transaction parsed from inbound notifications or
// for transaction updated through transaction backfill
enum TimelineResolutionSource{
  TIMELINE_RESOLUTION_SOURCE_TYPE_UNSPECIFIED = 0;
  // If the timeline will be created for new transactions via new inbound notification
  TIMELINE_RESOLUTION_SOURCE_NEW_TRANSACTION = 1;
  // If the timeline will be created for transactions updated via transaction backfill workflow
  TIMELINE_RESOLUTION_SOURCE_BACKFILL_TRANSACTION = 2;
}
