syntax = "proto3";

package timeline;

import "api/queue/consumer_headers.proto";
import "api/timeline/timeline.proto";

option go_package = "github.com/epifi/gamma/api/timeline";
option java_package = "com.github.epifi.gamma.api.timeline";

// `TimeLineCreateOrUpdateEvent` published via sns, when
// a new timeline is created or old timeline gets updated
message TimeLineCreateOrUpdateEvent {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;

  Timeline timeline = 2;
}
