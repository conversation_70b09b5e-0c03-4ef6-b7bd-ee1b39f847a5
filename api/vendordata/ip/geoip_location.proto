syntax = "proto3";

package vendordata;

option go_package = "github.com/epifi/gamma/api/vendordata/ip";
option java_package = "com.github.epifi.gamma.api.vendordata.ip";

// https://dev.maxmind.com/geoip/docs/databases/city-and-country?lang=en#locations-files
message GeoIpLocation {
  int32 geoname_id = 1;
  string locale_code = 2;
  string continent_code = 3;
  string continent_name = 4;
  string country_iso_code = 5;
  string country_name = 6;
  string subdivision_1_iso_code = 7;
  string subdivision_1_name = 8;
  string subdivision_2_iso_code = 9;
  string subdivision_2_name = 10;
  string city_name = 11;
  int32 metro_code = 12;
  string time_zone = 14;
  bool is_in_european_union = 15;
}
