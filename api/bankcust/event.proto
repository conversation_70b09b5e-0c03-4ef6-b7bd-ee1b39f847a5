// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package bankcust;

import "api/queue/consumer_headers.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "api/bankcust/internal/types.proto";

option go_package = "github.com/epifi/gamma/api/bankcust";
option java_package = "com.github.epifi.gamma.api.bankcust";


// BankCustomerUpdateEvent will be published to notify it's listeners on any update to fields in bank customer entity.
// The field getting updated is part of the event. Consumers can GetBankCustomer using the identifiers sent in the event to
// fetch the latest state of bank customer entity.
message BankCustomerUpdateEvent {
  // common request header across all the consumer grpc services.
  // contains important information related to message retry state.
  // passed along by queue subscriber.
  queue.ConsumerRequestHeader request_header = 1;
  // Unique id to represent an event uniquely(uuid for now)
  string event_id = 2;

  // time at which the event was published
  google.protobuf.Timestamp event_timestamp = 3;

  // Identifiers to uniquely fetch a customer
  string actor_id = 4;
  vendorgateway.Vendor vendor = 5;

  // Field getting updated
  repeated bankcust.BankCustomerFieldMask updated_field_mask = 6;
}
