syntax = "proto3";

package bankcust;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/bankcust";
option java_package = "com.github.epifi.gamma.api.bankcust";

// status of the chequebook order request
enum ChequebookStatus {
  CHEQUEBOOK_STATUS_UNSPECIFIED = 0;
  // when chequebook order request is raised, but tracking details are not known
  CHEQUEBOOK_STATUS_PENDING = 1;
  // successful end to the chequebook order journey
  CHEQUEBOOK_STATUS_SHIPPED = 2;
  // terminal failure to the chequebook request
  CHEQUEBOOK_STATUS_FAILED = 3;
  // request processing is stuck and final status of chequebook is not known
  CHEQUEBOOK_STATUS_STUCK = 4;
}

message TrackingDetails {
  CourierPartner courier_partner = 1;
  string tracking_id = 2;
  google.protobuf.Timestamp ordered_at = 3;
}

// courier partners which are used by vendor for shipping chequebook
enum CourierPartner {
  COURIER_PARTNER_UNSPECIFIED = 0;
  COURIER_PARTNER_ARAMEX = 1;
  COURIER_PARTNER_DELHIVERY = 2;
  COURIER_PARTNER_EXPRESS_IT_COURIER = 3;
  COURIER_PARTNER_EXQUISITE_SOLUTIONS = 4;
  COURIER_PARTNER_FIRST_FLIGHT_COURIERS = 5;
  COURIER_PARTNER_PROFESSIONAL_COURIER = 6;
  COURIER_PARTNER_INDIA_POST_SERVICE_KOCHI = 7;
  COURIER_PARTNER_INDIA_POST_SERVICE_UDUPI = 8;
  COURIER_PARTNER_QUANTIUM_COURIER_CUSTOMER = 9;
  COURIER_PARTNER_QUANTIUM_COURIER_DOMESTIC = 10;
  COURIER_PARTNER_QUANTIUM_COURIER_FEDERAL_BANK = 11;
  COURIER_PARTNER_SPEED_AND_SAFE = 12;
}
