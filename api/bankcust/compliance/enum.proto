//go:generate gen_sql -types=AttemptSource, KYCComplianceStatus
syntax = "proto3";

package compliance;

option go_package = "github.com/epifi/gamma/api/bankcust/compliance";
option java_package = "com.github.epifi.gamma.api.bankcust.compliance";

enum KYCComplianceStatus {
  KYC_COMPLIANCE_STATUS_UNSPECIFIED = 0;
  // KYC_COMPLIANCE_STATUS_COMPLIED indicates user's kyc is verified and the user has full access to the account. No action needed from the user
  KYC_COMPLIANCE_STATUS_COMPLIED = 1;
  // KYC_COMPLIANCE_STATUS_DUE indicates user's kyc is due. The user needs to perform KYC to continue uninterrupted banking services
  KYC_COMPLIANCE_STATUS_DUE = 2;
}

enum AttemptSource {
  ATTEMPT_SOURCE_UNSPECIFIED = 0;
  // ATTEMPT_SOURCE_FILE indicates source of data is csv file
  ATTEMPT_SOURCE_FILE = 1;

  ATTEMPT_SOURCE_ACCOUNT_STATUS_ENQUIRY_API = 2;
}

enum KYCComplianceFieldMask {
  KYC_COMPLIANCE_FIELD_MASK_UNSPECIFIED = 0;
  KYC_COMPLIANCE_FIELD_MASK_KYC_COMPLIANCE_STATUS = 1;
  KYC_COMPLIANCE_FIELD_MASK_KYC_COMPLIED_AT = 2;
  KYC_COMPLIANCE_FIELD_MASK_KYC_DUE_AT = 3;
  KYC_COMPLIANCE_FIELD_MASK_USER_SMS_TRIGGERED_AT = 4;
  KYC_COMPLIANCE_FIELD_MASK_KYC_CTA_CLICKED_AT = 5;
  KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_EKYC_COMPLETED_AT = 6;
  KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_PROFILE_UPDATE_REQUEST_ID = 7;
  KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_EKYC_CLIENT_REQUEST_ID = 8;
  KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_PROFILE_UPDATE_STARTED_AT = 9;
  KYC_COMPLIANCE_FIELD_MASK_PROFILE_UPDATE_DETAILS_PROFILE = 10;
  KYC_COMPLIANCE_FIELD_MASK_COMPLIANCE_METADATA = 12;
  KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_EKYC_COMPLETED_AT = 13;
  KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_PROFILE_UPDATE_REQUEST_ID = 14;
  KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_EKYC_CLIENT_REQUEST_ID = 15;
  KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_PROFILE_UPDATE_STARTED_AT = 16;
  KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_STATUS = 17;
  KYC_COMPLIANCE_FIELD_MASK_AADHAAR_UPDATE_DETAILS_AADHAAR_UPDATE_FAILURE_REASON = 18;
}

enum NudgeType {
  NUDGE_TYPE_UNSPECIFIED = 0;
  NUDGE_TYPE_PROFILE_TOP_BANNER = 1;
  NUDGE_TYPE_TRANSACTION_FAILURE_SCREEN = 2;
}

enum Action {
  ACTION_UNSPECIFIED = 0;
  ACTION_USER_SMS_TRIGGERED_AT = 1;
  ACTION_KYC_CTA_CLICKED_AT = 2;
  ACTION_EKYC_COMPLETED = 3;
  ACTION_CONFIRM_PERIODIC_KYC_DETAILS = 4;
  ACTION_UPDATE_PERIODIC_KYC_DETAILS = 5;
}
