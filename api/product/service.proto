//go:generate gen_sql -types=ProductInfo
syntax = "proto3";

package product;

import "api/product/enums.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/product";
option java_package = "com.github.epifi.gamma.api.product";

service Product {
  // GetProductsStatus rpc to get the current status of the product. It takes in an array of product identifiers and returns the appropriate
  // status for that product
  rpc GetProductsStatus (GetProductsStatusRequest) returns (GetProductsStatusResponse);


  // GetActiveProductsByPAN rpc can be used to check if the PAN being used by the current user has already been used by
  // another user to onboard on a core product
  rpc GetActiveProductsByPAN (GetActiveProductsByPANRequest) returns (GetActiveProductsByPANResponse);

  // GetCXPriority rpc will return if a particular user is CX prioritised based on the product passed in the request
  rpc GetCXPriority (GetCXPriorityRequest) returns (GetCXPriorityResponse);
}

message GetProductsStatusRequest {
  string actor_id = 1;
  repeated ProductType product_types = 2;
}

message GetProductsStatusResponse {
  rpc.Status status = 1;
  // map of ProductIdentifier <> Product Info
  map<string, ProductInfo> product_info_map = 2;
}

message ProductInfo {
  ProductStatus product_status = 1;
  google.protobuf.Timestamp activated_at = 2;
}

message GetActiveProductsByPANRequest {
  // ActorID of the user which needs to be ignored while checking for other users
  // Ideally this would be the actor id of the current user but can be changed based on use-case
  string excluded_actor_id = 1;
  string pan = 2;
}

message GetActiveProductsByPANResponse {
  enum Status {
    // Status when other active products were found with the current PAN
    OK = 0;
    // Status when no other user is found with PAN
    USER_NOT_FOUND = 100;
    // Status when another user is found but no active products are found
    ACTIVE_PRODUCT_NOT_FOUND = 101;
  }
  rpc.Status status = 1;
  repeated ProductType active_products = 2;
}

message GetCXPriorityRequest {
  string actor_id = 1;
  repeated ProductType product_types = 2;
}

message GetCXPriorityResponse {
  rpc.Status status = 1;
  message CXPriority {
    bool is_user_prioritised = 1;
  }
  // the string key is Product enum
  map<string, CXPriority> product_priority_map = 2;
}
