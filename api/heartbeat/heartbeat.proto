syntax = "proto3";

package heartbeat;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/heartbeat";
option java_package = "com.github.epifi.gamma.api.heartbeat";

// All eligible triggers for heartbeat checks
// Any enum defined here should also define a valid trigger implementation
// The triggers are not limited to using vendor gateway APIs and can be used for any purposes
enum HeartbeatTrigger {
  HEARTBEAT_TRIGGER_UNSPECIFIED = 0;
  HEARTBEAT_TRIGGER_VENDOR_ONE_MONEY = 1;
  HEARTBEAT_TRIGGER_VENDOR_FINVU = 2;
}

// Status of the heartbeat which will be stored/returned for a trigger
// Criteria of deciding this status will be defined by the user of this service
enum HeartbeatStatus {
  HEARTBEAT_STATUS_UNSPECIFIED = 0;
  // System is up and healthy
  HEARTBEAT_STATUS_UP = 1;
  // System is down
  HEARTBEAT_STATUS_DOWN = 2;
}

// Time series data object storing status of system and timestamp of that status
message HeartbeatStat {
  HeartbeatStatus heartbeat_status = 1;
  google.protobuf.Timestamp heartbeat_timestamp = 3;
}
