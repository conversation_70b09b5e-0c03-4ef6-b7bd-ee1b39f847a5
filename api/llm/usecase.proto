// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package llm;


option go_package = "github.com/epifi/gamma/api/llm";
option java_package = "com.github.epifi.gamma.api.llm";

// UseCase represents the use case for which the LLM is being used.
// This will be used internally for any use case specific logic if required.
// For example, current setup of Vertex requires client to store cached path of the system context
// Cached system context path is stored against this enum by the client
enum UseCase {
  USE_CASE_UNSPECIFIED = 0;
  USE_CASE_NETWORTH_MAGIC_IMPORT = 1;
  USE_CASE_NETWORTH_DATA_FILE = 2;
}
