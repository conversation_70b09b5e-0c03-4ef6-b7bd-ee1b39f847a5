//go:generate gen_sql -types=SubscriptionFrequency,SubscriptionState,TxnStatus,TxnSource,EntityType,TxnType
syntax = "proto3";

package upcomingtransactions.model;

option go_package = "github.com/epifi/gamma/api/upcomingtransactions/model";
option java_package = "com.github.epifi.gamma.api.upcomingtransactions.model";


enum SubscriptionFrequency {
  SUBSCRIPTION_FREQUENCY_UNSPECIFIED = 0;
  SUBSCRIPTION_FREQUENCY_MONTHLY = 1;
  SUBSCRIPTION_FREQUENCY_WEEKLY = 2;
}


enum SubscriptionState {
  SUBSCRIPTION_STATE_UNSPECIFIED = 0;
  SUBSCRIPTION_STATE_ACTIVE = 1;
  // subscription can expired by DS
  SUBSCRIPTION_STATE_EXPIRED = 2;
  SUBSCRIPTION_STATE_DISABLED_BY_USER = 3;
}


enum TxnStatus {
  TXN_STATUS_UNSPECIFIED = 0;
  // txn executed/completed successfully
  TXN_STATUS_EXECUTED = 1;
  // txn has not been executed yet
  TXN_STATUS_NOT_EXECUTED_YET = 2;
  // User acknowledged that txn is not valid or do not want to see
  TXN_STATUS_USER_INVALIDATED = 3;
  // DS acknowledged that txn is not valid anymore
  // If DS does not return a txn again in next iteration and the txn's max_date is >= cur_date (i.e. txn is still active)
  // and it hasn't been executed yet, we mark it as DS_INVALIDATED
  TXN_STATUS_DS_INVALIDATED = 4;
}

enum TxnSource {
  TXN_SOURCE_UNSPECIFIED = 0;
  TXN_SOURCE_DS = 1;
  TXN_SOURCE_FITTT = 2;
}

enum EntityType {
  ENTITY_TYPE_UNSPECIFIED = 0;
  ENTITY_TYPE_ACTOR = 1;
  ENTITY_TYPE_MERCHANT = 2;
  ENTITY_TYPE_SD_DEPOSIT_ACCOUNT = 3;
  ENTITY_TYPE_MUTUAL_FUND = 4;
  ENTITY_TYPE_USSTOCKS = 5;
}

enum TxnType {
  TXN_TYPE_UNSPECIFIED = 0;
  TXN_TYPE_P2P = 1;
  TXN_TYPE_P2M = 2;
}
