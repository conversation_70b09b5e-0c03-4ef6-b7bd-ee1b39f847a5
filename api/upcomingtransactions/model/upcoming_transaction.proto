//go:generate gen_sql -types=TxnStatus,TxnSource,EntityType,TxnType
syntax = "proto3";

package upcomingtransactions.model;

import "api/order/payment/accounting_entry_type.proto";
import "api/upcomingtransactions/model/enums.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";


option go_package = "github.com/epifi/gamma/api/upcomingtransactions/model";
option java_package = "com.github.epifi.gamma.api.upcomingtransactions.model";


message UpcomingTransaction {
  // primary key identifier
  string id = 1;
  // computed_hash is hash of (actor_id, with_entity_id, min_amount, max_amount, min_date, max_date)
  // this field will be used to uniquely determine an upcoming transaction
  string computed_hash = 2;
  // actor id of user for whom the transaction is upcoming
  string actor_id = 3;
  // with entity_id is the other side entity of txn
  // with_entity_id is actor_id in case transaction is P2P and merchant_id in case of P2M/M2P
  string with_entity_id = 4;
  // with_entity_type defines the type of entity involved in other side of txn i.e. Actor or Merchant
  EntityType with_entity_type = 5;
  // minimum expected amount for the upcoming transaction
  google.type.Money min_amount = 6;
  // maximum expected amount for the upcoming transaction
  google.type.Money max_amount = 7;
  // min date after which upcoming transaction to expected happen
  google.protobuf.Timestamp min_date = 8;
  // max date before which upcoming transaction to expected happen
  google.protobuf.Timestamp max_date = 9;
  // if upcoming transaction is part of a subscription (e.g. OTT) we have a subscription_id
  // if not, this field will be empty
  string subscription_id = 10;
  // defines whether the upcoming transaction is debit/credit wrt the actor
  order.payment.AccountingEntryType credit_debit = 11;
  // if the upcoming transaction is part of a subscription then this field will contain the txn_id of
  // last executed txn in that subscription, otherwise it will be empty
  string last_transaction_id = 12;
  // TxnStatus determines the status of txn e.g executed,
  TxnStatus execution_status = 13;
  // source of upcoming transaction (whether provided by DS or via fittt subscription)
  TxnSource source = 14;
  // Type of txn e.g. P2P or P2M
  TxnType type = 15;

  google.protobuf.Timestamp created_at = 16;
  google.protobuf.Timestamp updated_at = 17;
  google.protobuf.Timestamp deleted_at = 18;
}


enum UpcomingTransactionFieldMask {
  UPCOMING_TRANSACTION_FIELD_MASK_UNSPECIFIED = 0;
  UPCOMING_TRANSACTION_FIELD_MASK_ID = 1;
  UPCOMING_TRANSACTION_FIELD_MASK_COMPUTED_HASH = 2;
  UPCOMING_TRANSACTION_FIELD_MASK_ACTOR_ID = 3;
  UPCOMING_TRANSACTION_FIELD_MASK_WITH_ENTITY_ID = 4;
  UPCOMING_TRANSACTION_FIELD_MASK_WITH_ENTITY_TYPE = 5;
  UPCOMING_TRANSACTION_FIELD_MASK_MIN_AMOUNT = 6;
  UPCOMING_TRANSACTION_FIELD_MASK_MAX_AMOUNT = 7;
  UPCOMING_TRANSACTION_FIELD_MASK_MIN_DATE = 8;
  UPCOMING_TRANSACTION_FIELD_MASK_MAX_DATE = 9;
  UPCOMING_TRANSACTION_FIELD_MASK_SUBSCRIPTION_ID = 10;
  UPCOMING_TRANSACTION_FIELD_MASK_CREDIT_DEBIT = 11;
  UPCOMING_TRANSACTION_FIELD_MASK_LAST_TRANSACTION_ID = 12;
  UPCOMING_TRANSACTION_FIELD_MASK_EXECUTION_STATUS = 13;
  UPCOMING_TRANSACTION_FIELD_MASK_SOURCE = 14;
  UPCOMING_TRANSACTION_FIELD_MASK_TYPE = 15;
  UPCOMING_TRANSACTION_FIELD_MASK_CREATED_AT = 16;
  UPCOMING_TRANSACTION_FIELD_MASK_UPDATED_AT = 17;
  UPCOMING_TRANSACTION_FIELD_MASK_DELETED_AT = 18;
}
