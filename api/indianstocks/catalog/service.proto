syntax = "proto3";
package api.indianstocks.catalog;

import "api/indianstocks/catalog/catalog.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/indianstocks/catalog";
option java_package = "com.github.epifi.gamma.api.indianstocks.catalog";

service IndianStocksCatalogManager {
  // GetStocks returns the stock details for the given isin numbers
  // If the isin number is not found, it will not be returned in the response
  rpc GetStocks(GetStocksRequest) returns (GetStocksResponse);
}

message GetStocksRequest {
  repeated string isin_numbers = 1;
}

message GetStocksResponse {
  rpc.Status status = 1;
  repeated IndianStock stocks = 2;
}
