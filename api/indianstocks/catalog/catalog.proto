//go:generate gen_sql -types Sector,MarketCapCategory
syntax = "proto3";
package api.indianstocks.catalog;

option go_package = "github.com/epifi/gamma/api/indianstocks/catalog";
option java_package = "com.github.epifi.gamma.api.indianstocks.catalog";


message IndianStock {
  string isin_number = 1;
  string display_name = 2;
  Sector sector = 3;
  MarketCapCategory market_cap_category = 4;
  string icon_url = 5;
}

enum Sector {
  SECTOR_UNSPECIFIED = 0;
  SECTOR_IT_SOFTWARE = 1;
  SECTOR_AEROSPACE_DEFENCE = 2;
  SECTOR_REFINERIES = 3;
  SECTOR_EDUCATION = 4;
  SECTOR_INFRASTRUCTURE_DEVELOPERS_OPERATORS = 5;
  SECTOR_COMPUTER_EDUCATION = 6;
  SECTOR_REALTY = 7;
  SECTOR_PAPER = 8;
  SECTOR_SHIPPING = 9;
  SECTOR_PACKAGING = 10;
  SECTOR_FINANCIAL_SERVICES = 11;
  SECTOR_HEALTHCARE = 12;
  SECTOR_TYRES = 13;
  SECTOR_EDIBLE_OIL = 14;
  SECTOR_REFRACTORIES = 15;
  SECTOR_RAILWAYS = 16;
  SECTOR_DIVERSIFIED = 17;
  SECTOR_PLYWOOD_BOARDS_LAMINATES = 18;
  SECTOR_LEATHER = 19;
  SECTOR_CONSTRUCTION = 20;
  SECTOR_CEMENT_PRODUCTS = 21;
  SECTOR_NON_FERROUS_METALS = 22;
  SECTOR_OIL_DRILL_ALLIED = 23;
  SECTOR_LOGISTICS = 24;
  SECTOR_POWER_GENERATION_DISTRIBUTION = 25;
  SECTOR_FMCG = 26;
  SECTOR_CERAMIC_PRODUCTS = 27;
  SECTOR_BANKS = 28;
  SECTOR_ELECTRONICS = 29;
  SECTOR_INSURANCE = 30;
  SECTOR_MISCELLANEOUS = 31;
  SECTOR_CONSUMER_DURABLES = 32;
  SECTOR_CREDIT_RATING_AGENCIES = 33;
  SECTOR_E_COMMERCE_APP_BASED_AGGREGATOR = 34;
  SECTOR_POWER_INFRASTRUCTURE = 35;
  SECTOR_STOCK_COMMODITY_BROKERS = 36;
  SECTOR_CAPITAL_GOODS_NON_ELECTRICAL_EQUIPMENT = 37;
  SECTOR_CRUDE_OIL_NATURAL_GAS = 38;
  SECTOR_TRADING = 39;
  SECTOR_PAINTS_VARNISH = 40;
  SECTOR_ENGINEERING = 41;
  SECTOR_GLASS_GLASS_PRODUCTS = 42;
  SECTOR_QUICK_SERVICE_RESTAURANT = 43;
  SECTOR_PLANTATION_PLANTATION_PRODUCTS = 44;
  SECTOR_MEDIA_PRINT_TELEVISION_RADIO = 45;
  SECTOR_TOBACCO_PRODUCTS = 46;
  SECTOR_STEEL = 47;
  SECTOR_IT_HARDWARE = 48;
  SECTOR_PLASTIC_PRODUCTS = 49;
  SECTOR_FERRO_ALLOYS = 50;
  SECTOR_ALCOHOLIC_BEVERAGES = 51;
  SECTOR_FERTILIZERS = 52;
  SECTOR_TELECOMM_EQUIPMENT_INFRA_SERVICES = 53;
  SECTOR_SHIP_BUILDING = 54;
  SECTOR_PHARMACEUTICALS = 55;
  SECTOR_CAPITAL_GOODS_ELECTRICAL_EQUIPMENT = 56;
  SECTOR_CHEMICALS = 57;
  SECTOR_PETROCHEMICALS = 58;
  SECTOR_CABLES = 59;
  SECTOR_DIAMOND_GEMS_JEWELLERY = 60;
  SECTOR_BEARINGS = 61;
  SECTOR_MINING_MINERAL_PRODUCTS = 62;
  SECTOR_AUTO_ANCILLARIES = 63;
  SECTOR_ENTERTAINMENT = 64;
  SECTOR_TELECOMM_SERVICE = 65;
  SECTOR_READYMADE_GARMENTS_APPARELLS = 66;
  SECTOR_PRINTING_STATIONERY = 67;
  SECTOR_AIR_TRANSPORT_SERVICE = 68;
  SECTOR_TEXTILES = 69;
  SECTOR_RETAIL = 70;
  SECTOR_CASTINGS_FORGINGS_FASTNERS = 71;
  SECTOR_AGRO_CHEMICALS = 72;
  SECTOR_SUGAR = 73;
  SECTOR_DRY_CELLS = 74;
  SECTOR_FINANCE = 75;
  SECTOR_CEMENT = 76;
  SECTOR_MARINE_PORT_SERVICES = 77;
  SECTOR_HOTELS_RESTAURANTS = 78;
  SECTOR_AUTOMOBILE = 79;
  SECTOR_GAS_DISTRIBUTION = 80;
  SECTOR_ERROR = 81;
}

enum MarketCapCategory {
  MARKET_CAP_CATEGORY_UNSPECIFIED = 0;
  MARKET_CAP_CATEGORY_SMALL_CAP = 1;
  MARKET_CAP_CATEGORY_MID_CAP = 2;
  MARKET_CAP_CATEGORY_LARGE_CAP = 3;
  MARKET_CAP_CATEGORY_ERROR = 4;
}
