syntax = "proto3";
package cx.issue_resolution_feedback;

import "google/protobuf/timestamp.proto";
import "api/typesv2/common/boolean.proto";
import "api/cx/issue_resolution_feedback/enums.proto";

option go_package = "github.com/epifi/gamma/api/cx/issue_resolution_feedback";
option java_package = "com.github.epifi.gamma.api.cx.issue_resolution_feedback";

// proto msg for dispute feedback
message DisputeFeedback {
  // if response is set to true than feedback is YES
  // if response is set to false than feedback is NO
  api.typesv2.common.BooleanEnum response = 1;
}

// generic feedback proto msg
message ResolutionFeedback {
  // keeping it as one of to extend it in future
  oneof feedback {
    // dispute feedback
    DisputeFeedback dispute_feedback = 1;
  }
}

// proto msg for model
message IssueResolutionFeedback {
  // row id
  string id = 1;
  // ticket id for which comms actions are being done
  int64 ticket_id = 2;
  // what type of resolution logic has to considered
  ResolutionCategory resolution_category = 3;
  // client request id if client has sent any
  string client_request_id = 4;
  // number of attempt made
  int64 number_of_attempts = 5;
  // last tried at timestamp
  google.protobuf.Timestamp last_tried_at = 6;
  // process stage: created, sent to comms, etc
  ProcessStage process_stage = 7;
  // record create at
  google.protobuf.Timestamp created_at = 8;
  // record updated at
  google.protobuf.Timestamp updated_at = 9;
}

message IssueResolutionUserResponseLog {
  // row id
  string id = 1;
  // id from issue resolution feedback
  string issue_resolution_user_feedback_id = 2;
  // feedback msg: denotes feedback received from user
  ResolutionFeedback resolution_feedback = 3;
  // record created at db timestamp
  google.protobuf.Timestamp created_at = 4;
  // record updated at db timestamp
  google.protobuf.Timestamp updated_at = 5;
}

// issue payload
message IssuePayload {
  // ticket id for which issue resolution logic has to be processed
  int64 ticket_id = 1;
  // which resolution category does issue belong to
  ResolutionCategory resolution_category = 2;
  // actor id which is mapped to the issue
  string actor_id = 3;
}

// feedback payload
message FeedbackPayload {
  // ticket id for which issue resolution logic has to be processed
  int64 ticket_id = 1;
  // feedback received from the user
  ResolutionFeedback resolution_feedback = 2;
}

// ticket update event payload
message IssueResolutionTicketUpdateEventPayload {
  // type of event: EOL, user triggerred feedback event
  IssueResolutionTicketUpdateEventType issue_resolution_ticket_update_event_type = 1;
  // feedback payload
  FeedbackPayload feedback_payload = 2;
  // resolution category
  ResolutionCategory resolution_category = 3;
}

// meta info to be sent to client particular to dispute tickets
message DisputeMeta {
  // differentiate response screen based on transaction type eg: p2p vs p2m
  TransactionType transaction_type = 4;
}
