// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package cx.agent;

import "api/cx/agent/agent.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/agent";
option java_package = "com.github.epifi.gamma.api.cx.agent";

// create a new agent
message CreateAgentRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  AgentInfo agent_info = 2;

  // whether agent to be created is required in freshdesk or not
  // TODO (hardik) : is this field really required if properties are also being passed?
  bool is_freshdesk_creation_required = 3;

  // properties specific to freshdesk
  FreshdeskUserProperties freshdesk_user_properties = 4;

  // TODO (hardik) : is this field really required if properties are also being passed?
  bool is_freshchat_creation_required = 5;

  FreshchatUserProperties freshchat_user_properties = 6;
}

// custom status to map which provisioner returned what status
message ProvisioningStatus {
  rpc.Status status = 1;
  AgentProvisioners agent_provisioner = 2;
}

message CreateAgentResponse {
  repeated ProvisioningStatus provisioning_status_list = 1;
}

// update an existing agent record for phone, access, agent type, etc
// updating email is not allowed
message UpdateAgentRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  AgentInfo agent_info = 2;
}

message UpdateAgentResponse {
  repeated ProvisioningStatus provisioning_status_list = 1;
}

message GetAgentInfoRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  // identifier of the agent
  string email_id = 2;
}

message GetAgentInfoResponse {
  rpc.Status status = 1;
  AgentInfo agent_info = 3;
}

message RemoveAgentRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // identifier of the agent
  string email_id = 2;
}

message RemoveAgentResponse {
  repeated ProvisioningStatus provisioning_status_list = 1;
}

message GetAllAgentsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetAllAgentsResponse {
  rpc.Status status = 1;

  repeated AgentInfo agent_info_list = 2;
}

service AgentService {

  // creates a new agent and provides appropriate access as per access level specified
  rpc CreateAgent(CreateAgentRequest) returns (CreateAgentResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // updates agents info and access level. Only the fields that are populated in request will be updated
  rpc UpdateAgent(UpdateAgentRequest) returns (UpdateAgentResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Provides agent info of a given email id
  rpc GetAgentInfo(GetAgentInfoRequest) returns (GetAgentInfoResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // marks agent as inactive in DB, when an agent leaves
  rpc RemoveAgent(RemoveAgentRequest) returns(RemoveAgentResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // method to fetch information of all the agents in the system
  rpc GetAllAgents(GetAllAgentsRequest) returns (GetAllAgentsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}
