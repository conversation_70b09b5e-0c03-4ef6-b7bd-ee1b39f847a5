// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package cx.agent;

import "api/casbin/casbin.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/cx/freshdesk/agent.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/agent";
option java_package = "com.github.epifi.gamma.api.cx.agent";

// Type of agent
enum AgentType {
  AGENT_TYPE_UNSPECIFIED = 0;

  // full time employees of epifi
  EMPLOYEE = 1;

  // part time hires of epifi
  CONSULTANT = 2;

  // Those agents who are using the helpdesk like freshdesk and employed by epifi
  CUSTOMER_SUPPORT = 3;
}

// To identify whether an agent is still employed or not
enum AgentStatus {
  AGENT_STATUS_UNSPECIFIED = 0;

  ACTIVE = 1;

  INACTIVE = 2;
}

message AgentInfo {
  // epifi created agent id
  // not to be passed by client
  string id = 1;

  // official registered email id of agent with epifi
  string email_id = 2 [(validate.rules).string.min_len = 1];

  // 10 digit phone number of the agent
  api.typesv2.common.PhoneNumber phone_number = 3;

  // Name of the agent
  api.typesv2.common.Name name = 4;

  // type of agent to identify whether consultant or full time employee or support agent
  AgentType agent_type = 5;

  // what access level do we need to provide to this agent
  casbin.AccessLevel access_level = 6;

  AgentStatus agent_status = 7;

  // id of agent on ozonetel
  string ozonetel_id = 8;
}

enum AgentProvisioners {
  AGENT_PROVISIONER_UNSPECIFIED = 0;

  AWS_COGNITO = 1;

  EPIFI = 2;

  FRESHDESK = 3;

  FRESHCHAT = 4;

  // DB Policy store for agents
  CASBIN = 5;
}

enum FreshdeskAgentType {
  FRESHDESK_AGENT_TYPE_UNSPECIFIED = 0;

  FULL_TIME = 1;

  OCCASIONAL = 2;
}

// attributes to be given while creating agent in freshdesk
message FreshdeskUserProperties {
  // ticket scope in freshdesk for created agent
  cx.freshdesk.TicketScope ticket_scope = 1;

  FreshdeskAgentType freshdesk_agent_type = 2;
}

message FreshchatUserProperties {

}

// To map to db column names
enum AgentFieldMask {
  AGENT_FIELD_UNSPECIFIED = 0;
  VENDOR_AGENT_ID = 2;
  VENDOR_NAME = 3;
  AGENT_TYPE = 4;
  ACCESS_LEVEL = 5;
  PHONE = 6;
  AGENT_NAME = 8;
  STATUS = 9;
  OZONETEL_ID = 10;
}
