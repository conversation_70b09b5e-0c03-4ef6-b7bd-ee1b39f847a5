syntax = "proto3";

package cx.call_routing;

import "api/cx/call_routing/enums.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/user.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/call_routing";
option java_package = "com.github.epifi.gamma.api.cx.call_routing";

service CallRouting {
  // rpc method to return ozonetel routing channel for given user
  // will be called by vendor notification service to return routing channel to ozonetel vendor
  // will return code
  // OK for success
  // INTERNAL for server errors
  rpc GetRoutingChannelForUser (GetRoutingChannelForUserRequest) returns (GetRoutingChannelForUserResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // CheckCallLangPrefAndGetLangOptions checks if user preference is set
  // and returns the list of call language options to be shown to the user
  // Returns Status:
  //     PERMISSION DENIED - if this flow is not available for the user
  //     OK with selected_user_prefs populated - if the user had already set call language preference
  //     OK without selected_user_prefs - if the user preference is not set. Here user will be taken to set preference flow
  // Will be used by the frontend
  rpc CheckCallLangPrefAndGetLangOptions (CheckCallLangPrefAndGetLangOptionsRequest) returns (CheckCallLangPrefAndGetLangOptionsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message GetRoutingChannelForUserRequest {
  // mandatory
  api.typesv2.common.PhoneNumber phone_number = 1 [(validate.rules).message.required = true];
  string monitor_ucid = 2;
}

message GetRoutingChannelForUserResponse {
  // will return code
  // OK for success
  // INTERNAL for server errors
  rpc.Status status = 1;
  // will return unspecified in case of rpc error
  // will return appropriate routing channel in other cases
  RoutingChannel routing_channel = 2;
  // call language preference of the user
  repeated api.typesv2.Language language_preference_list = 3;
  // denotes priority of a call within given routing channel
  // high priority value signifies higher the priority a user receives
  // in given routing channel to decrease the overall wait time for that user
  // this surpasses anyone having lower priority and still waiting in same routing channel
  int32 priority = 4;
  // denotes the id of the recording to be played if there is a need to do so
  // for example : user with high risk score, failed screener stage etc
  // the mapping of recording id to the actual recording played is stored at ozonetel's end
  RecordingIdentifier recording_id = 5;
  // denotes the version to be used
  // version 0 means no IVR and just routing channel/priority based logic
  // version 1 means users may have to answer an IVR system before connecting to an agent
  int32 version = 6;
}

message CheckCallLangPrefAndGetLangOptionsRequest {
  string actor_id = 1;
}

message CheckCallLangPrefAndGetLangOptionsResponse {
  // Status:
  //     PERMISSION DENIED - if this flow is not available for the user
  //     OK with selected_user_prefs populated - if the user had already set call language preference
  //     OK without selected_user_prefs - if the user preference is not set. Here user will be taken to set preference flow
  //     INTERNAL - for server errors
  rpc.Status status = 1;
  // List of languages the user can choose as a preference
  repeated api.typesv2.Language preference_options = 2;
  // List of languages the user can suggest
  repeated api.typesv2.Language suggestion_options = 3;
  // User preferred language already selected
  // This will be nil if user has not selected preference earlier
  repeated api.typesv2.Language selected_user_prefs = 4;
}
