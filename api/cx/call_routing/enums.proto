syntax = "proto3";

package cx.call_routing;

option go_package = "github.com/epifi/gamma/api/cx/call_routing";
option java_package = "com.github.epifi.gamma.api.cx.call_routing";

// routing channel enums represents different routing channel available in our IVR routing
// user will be assigned to one of the routing channel based on the stage he is in and other user properties as well
enum RoutingChannel {
  ROUTING_CHANNEL_UNSPECIFIED = 0;
  // users who are current fi account holder will be routed through this channel
  ROUTING_CHANNEL_ACCOUNT_HOLDERS = 1;
  // users who are currently onboarding will be routed through this channel
  ROUTING_CHANNEL_CURRENTLY_ONBOARDING = 2;
  // all the other unregistered users will be routed through this channel
  ROUTING_CHANNEL_UNREGISTERED = 3;
  // user who are current active in salary program
  ROUTING_CHANNEL_SALARY_PROGRAM_USERS = 4;
  // channel for users which were blocked by us and have account balance above 10k RS
  ROUTING_CHANNEL_BLOCKED_USER_BALANCE_ABOVE_10K = 5;
  // channel for users which were blocked by us and have account balance above 1k RS
  ROUTING_CHANNEL_BLOCKED_USER_BALANCE_ABOVE_1K = 6;
  // channel for users which were blocked by us and have account balance above 100 RS
  ROUTING_CHANNEL_BLOCKED_USER_BALANCE_ABOVE_100 = 7;
  // channel for users which were blocked by us and have account balance unknow or between 0 and 100 Rs
  ROUTING_CHANNEL_BLOCKED_USER_DEFAULT = 8;
  // deprecated channel
  ROUTING_CHANNEL_BLOCKED_USER_BLACKLISTED = 9 [deprecated = true];
  // channel for users which were blocked by us and were also blocked after the manual re-verfication
  ROUTING_CHANNEL_BLOCKED_USER_LOW_PRIORITY = 10;
  // channel for users whos accounts have been frozen because their ckyc type was "O"
  ROUTING_CHANNEL_CKYC_O_FLAG_ACCOUNT_FREEZED_USERS = 11;
  // channel for users holding a credit card
  ROUTING_CHANNEL_CREDIT_CARD_USERS = 12;
  // channel for users having an active loan via Fi app
  ROUTING_CHANNEL_ACTIVE_LOAN_USERS = 13;
}

enum IdentifierType {
  IDENTIFIER_TYPE_UNSPECIFIED = 0;

  IDENTIFIER_TYPE_ACTOR_ID = 1;
}

enum RecordingIdentifier {
  RECORDING_IDENTIFIER_UNSPECIFIED = 0;
  // recording identifier for users with high risk score
  RECORDING_IDENTIFIER_HIGH_RISK_SCORE = 1;
  // recording identifier for users who are rejected by screener
  RECORDING_IDENTIFIER_SCREENER_REJECT = 2;
  // recording identifier, where we play a message to user prompting them to report an issue via Fi app
  RECORDING_IDENTIFIER_REDIRECT_TO_ISSUE_REPORTING_FLOW = 3;
  // recording identifier where we play a message to user, that our team will call them back, and their issue is acknowledged
  RECORDING_IDENTIFIER_CALLBACK_REQUEST = 4;
}

// type of event that is emitted based on the routing decisions related to a call
// for example : user is greeted with a pre-recorded message or routed to routing-channel, priority etc
enum CallRoutingEventType {
  CALL_ROUTING_EVENT_TYPE_UNSPECIFIED = 0;
  // event type that will be emitted whenever a pre-recorded message is played to the user
  CALL_ROUTING_EVENT_TYPE_PRE_RECORDED_MESSAGE = 1;
}
