// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";
package cx.priority_routing;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/cx/priority_routing";
option java_package = "com.github.epifi.gamma.api.cx.priority_routing";

// UserPriorityDataRedisObject store the redis response
// msg defines attributes for user priority data content that is fetched from db(cx-redis) for a actor_id
message UserPriorityDataRedisObject {
    float saving_avg_bal_last_28_days = 1;
    float fd_avg_bal_last_28_days = 2;
    float smart_avg_bal_last_28_days = 3;
    float account_balance = 4;
    float balance_percentile = 5;
    float current_deposit_balance = 6;
    string vkyc_status = 7;
    string open_date_ist = 8;    
}


// UserPriorityData proto msg to be used by priority-rule-engine
message UserPriorityData {
    // Average Balance in Savings Account over last 28 days
    float saving_avg_bal_last_28_days = 1;
    // Average Balance in Fix Deposit over last 28 days
    float fd_avg_bal_last_28_days = 2;
    // Average Balance in Smart Deposit over last 28 days
    float smart_avg_bal_last_28_days = 3;
    // Current Savings Account Balance
    float account_balance = 4;
    // saving balance percentile of a user
    float balance_percentile = 5;
    // sum total of fixed and smart deposit of a user
    float current_deposit_balance = 6;
    // video kyc status
    string vkyc_status = 7;
    // Account Creation Date
    google.protobuf.Timestamp open_date_ist = 8;    
}
