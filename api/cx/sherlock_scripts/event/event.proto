syntax = "proto3";

package cx.sherlock_scripts.event;

import "api/queue/consumer_headers.proto";
import "api/cx/sherlock_scripts/script.proto";

option go_package = "github.com/epifi/gamma/api/cx/sherlock_scripts/event";
option java_package = "com.github.epifi.gamma.api.sherlock_scripts.event";

// UpdateSherlockScriptsBatch contains a batch of sherlock scripts which need to be indexed
message UpdateSherlockScriptsBatch {
  queue.ConsumerRequestHeader request_header = 1;
  // list of sherlock scripts to update
  repeated  sherlock_scripts.SherlockScript sherlock_scripts = 2;
}
