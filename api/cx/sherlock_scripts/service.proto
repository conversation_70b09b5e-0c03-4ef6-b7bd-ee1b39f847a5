syntax = "proto3";
package cx.sherlock_scripts;

import "api/cx/method_options.proto";
import "api/cx/sherlock_scripts/message.proto";
import "api/rpc/status.proto";
import "api/cx/sherlock_scripts/script.proto";
import "api/cx/header.proto";
import "api/typesv2/webui/table.proto";

option go_package = "github.com/epifi/gamma/api/cx/sherlock_scripts";
option java_package = "com.github.epifi.gamma.api.cx.sherlock_scripts";

service SherlockScripts {
  // GetSearchResults is used to fetch search results based on the user query
  rpc GetSearchResults(GetSearchResultsRequest) returns (GetSearchResultsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Get SearchResultsMeta fetches result count, results per product category etc
  // GetSearchResult cannot be utilized for this purpose as it is a paginated RPC
  rpc GetSearchResultsMeta(GetSearchResultsMetaRequest) returns (GetSearchResultsMetaResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc GetSherlockScript(GetSherlockScriptRequest) returns (GetSherlockScriptResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc GetSherlockScriptsInBulk(GetBulkSherlockScriptRequest) returns(GetBulkSherlockScriptResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message GetSearchResultsRequest {
  // text query based on which scripts needs to be fetched
  string query = 1;
  // product category for which articles are needed
  string product_category = 2;
  // product category details for which articles are needed
  string product_category_details = 3;
  // subcategory for which articles are needed
  string sub_category = 4;
  // contains parameters needed to perform pagination
  PaginationParams pagination_params = 5;
}

message GetSearchResultsResponse {
  rpc.Status status = 1;
  api.typesv2.webui.Table search_results = 2;
  PaginationParams pagination_params = 3;
}

message PaginationParams {
  uint64 from = 1;
  uint64 size = 2;
}

message GetSearchResultsMetaRequest {
  // text query based on which scripts needs to be fetched
  string query = 1;
  // product category for which articles are needed
  string product_category = 2;
  // product category details for which articles are needed
  string product_category_details = 3;
  // subcategory for which articles are needed
  string sub_category = 4;
}

message GetSearchResultsMetaResponse {
  rpc.Status status = 1;
  // contains the overall count of search results and the count per l1, l2 and l3
  SherlockScriptMetaSearchResult sherlock_script_meta_search_result = 2;
}

message GetSherlockScriptRequest {
  cx.Header header = 1;
  string script_id = 2;
}
message GetSherlockScriptResponse {
  rpc.Status status = 1;
  SherlockScript sherlock_script = 2;
}

message GetBulkSherlockScriptRequest{
  cx.Header header = 1;
  string start_time = 2;
  string end_time = 3;
  bool is_created_at_filter = 4;
  bool is_updated_at_filter = 5;
  PaginationParams request_page_token = 6;
}
message GetBulkSherlockScriptResponse{
  rpc.Status status = 1;
  repeated SherlockScript sherlock_scripts = 2;
}
