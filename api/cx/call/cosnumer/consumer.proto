syntax = "proto3";
package cx.call.consumer;

import "api/queue/consumer_headers.proto";
import "api/cx/call/message.proto";

option go_package = "github.com/epifi/gamma/api/cx/call/consumer";
option java_package = "com.github.epifi.gamma.api.cx.call.consumer";

message AddOrUpdateOzonetelCallDetailsRequest {
  // queue request header
  queue.ConsumerRequestHeader request_header = 1;
  // ozonetel call details
  cx.call.CallDetails call_details = 2;
}

message AddOrUpdateOzonetelCallDetailsResponse {
  // queue response header
  queue.ConsumerResponseHeader response_header = 1;
}

service OzonetelEventConsumer {
  // consumer rpc which consumes from ozonetel call details queue
  // if call details event type is screenpop, it will add record to database
  // if call details event type is push to url, it will update existing record in db and publish message to freshdesk update ticket queue
  rpc AddOrUpdateOzonetelCallDetails(AddOrUpdateOzonetelCallDetailsRequest) returns (AddOrUpdateOzonetelCallDetailsResponse) {}
}
