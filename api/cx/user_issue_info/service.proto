syntax = "proto3";
package cx.user_issue_info;

import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/user_issue_info";
option java_package = "com.github.epifi.gamma.api.cx.user_issue_info";


service UserIssueInfoService {
  // GetUserIssue gives user issue related info to help the agents
  rpc GetUserIssueInfo(GetUserIssueRequest) returns (GetUserIssueResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // SetUserIssue add user issue related info through dev action
  rpc BulkSetUserIssueInfo(BulkSetUserIssueRequest) returns (BulkSetUserIssueResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message GetUserIssueRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetUserIssueResponse {
  rpc.Status status = 1;

  repeated DataValue user_issue_data = 2;
}

message BulkSetUserIssueRequest {
  repeated UserIssueInfo user_issue_info_list = 1;
}

message BulkSetUserIssueResponse {
  rpc.Status status = 1;
}

message UserIssueInfo {
  string actor_id = 1;
  RewardsIssueInfo rewards_issue_info = 2;
}

message RewardsIssueInfo {
  string redemption_state = 1;
  string user_issue =2;
  string cx_script =3;
}

message DataValue {
  string label = 1;
  string value = 2;
}
