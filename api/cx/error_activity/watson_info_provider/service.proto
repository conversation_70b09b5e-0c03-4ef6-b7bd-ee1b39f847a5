syntax = "proto3";

package cx.error_activity.watson_info_provider;

import "api/cx/method_options.proto";
import "api/cx/watson/watson_client.proto";

option go_package = "github.com/epifi/gamma/api/cx/error_activity/watson_info_provider";
option java_package = "com.github.epifi.gamma.api.cx.error_activity.watson_info_provider";

// WatsonInfoProvider acts as client for Watson
// this service will be called whenever <PERSON> need to make some decision for an incident (ex. creating ticket, sending comms, resolving etc.)
service WatsonInfoProvider {
  rpc IsIncidentValid (.cx.watson.IsIncidentValidRequest) returns (.cx.watson.IsIncidentValidResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc GetTicketDetails (.cx.watson.GetTicketDetailsRequest) returns (.cx.watson.GetTicketDetailsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc GetCommsDetails (.cx.watson.GetCommsDetailsRequest) returns (.cx.watson.GetCommsDetailsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}
