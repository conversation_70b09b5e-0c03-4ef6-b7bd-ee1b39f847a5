syntax = "proto3";

package cx.issue_config;

import "api/cx/issue_config/config_payloads.proto";
import "api/cx/issue_config/enums.proto";
import "api/cx/manual_ticket_stage_wise_comms/message.proto";
import "api/cx/watson/watson.proto";
import "api/cx/watson/watson_client.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/common/boolean.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/issue_config";
option java_package = "com.github.epifi.gamma.api.cx.issue_config";

// IssueConfig signifies configurable data for issues faced by customers
// issue_category_id, config_type, and config_version act as an identifier, and config_payload is the actual data
message IssueConfig {
  // row id in the database
  string id = 1;
  // id representing issue_category in db
  string issue_category_id = 2;
  // determines the type of config_payload populated
  ConfigType config_type = 3;
  // actual config data
  ConfigPayload config_payload = 4;
  // version of config, to help keep track of previous versions
  int64 config_version = 5;
  // timestamp at which this record was created in db
  google.protobuf.Timestamp created_at = 6;
  // timestamp at which this record was last updated in db
  google.protobuf.Timestamp updated_at = 7;
  // email id of the sherlock user who updated the config value
  string updated_by = 8;
  // email id of the sherlock user who approved the config change
  string approved_by = 9;
  // boolean which represents whether the config version is latest or not
  bool is_latest_version = 10;
}

// WatsonConfig determines the lifecycle of watson incident for given IssueCategory
message WatsonConfig {
  // feature flag to stop processing incidents
  api.typesv2.common.BooleanEnum is_processing_enabled = 1;
  // flag to determine whether ticket needs to be created or not
  api.typesv2.common.BooleanEnum is_ticket_creation_enabled = 2;
  // flag to determine whether comms needs to be sent after incident creation
  api.typesv2.common.BooleanEnum is_comms_enabled = 3;
  // ticket_creation_delay will allow client to delay the ticket creation for an incident for certain time period
  // this is done because the incident might be resolved in some time and there is no need to create ticket
  // this is kept as string, as it stores the duration like "2h" which is then parsed and used appropriately
  string ticket_creation_delay = 4;
  // comms_delay will allow client to delay the comms send to user for an incident
  // this is done because the incident might be resolved in some time and there is no need to send comms
  // this is kept as string, as it stores the duration like "2h" which is then parsed and used appropriately
  string comms_delay = 5;
  // ticket statuses for which comms must be triggered along with the respective configs.
  map<string, WatsonTicketStatusCommsConfig> ticket_status_comms_config_map = 6;
  // time period after which the incident should be auto-closed
  string auto_closure_period = 7;
}

// WatsonTicketStatusCommsConfig represents the config used to send comms on a given ticket status
message WatsonTicketStatusCommsConfig {
  // time interval specifying the duration for triggering Comms again on this particular status
  string comms_interval = 1;
  // The maximum number of comms which can be sent on this status. Currently capped to have a maximum of 3 comms
  int32 max_num_of_comms = 2 [(validate.rules).int32.lte = 3];
}

// IncidentStageBasedCommsDetails represents what comms to send to user on various stages of incident
// currently we are only supporting comms on creation and resolution
message IncidentStageBasedCommsDetails {
  repeated watson.CommsDetail creation_comms = 1;
  repeated watson.CommsDetail resolution_comms = 2;
  repeated watson.CommsDetail auto_closure_comms = 3;
}

message UserFriendlyCategoryText {
  // user-friendly text of the product category corresponding to the issue category id
  string product_category = 1;
  // user-friendly text of the product category details corresponding to the issue category id
  string product_category_details = 2;
  // user-friendly text of the sub-category corresponding to the issue category id
  string sub_category = 3;
  // short description corresponding to the user-friendly category text
  string short_description = 4;
  // product category for which the user friendly text is defined
  string original_product_category = 5;
  // product category details for which the user friendly text is defined
  string original_product_category_details = 6;
  // subcategory for which the user friendly text is defined
  string original_sub_category = 7;
}

// ConfigPayload represents config data for different types of config
message ConfigPayload {
  oneof config {
    // represents all the details required to manage Watson incident life-cycle
    WatsonConfig watson_config = 1;
    // represents ticket details to be populated in ticket created by Watson
    watson.TicketDetails watson_ticket_details = 2;
    // represents what comms to send to user, for various incident updates (creation, resolution, etc.)
    IncidentStageBasedCommsDetails incident_stage_based_comms_details = 3;
    // represent what comms will be sent to user, for various statuses of a manually created ticket
    manual_ticket_stage_wise_comms.ManualTicketStageBasedCommsDetails manual_ticket_stage_based_comms_details = 4;
    // config to represent ticket's SLA
    TicketSlaConfig ticket_sla_config = 5;
    // flag that helps identify whether the issue is first contact resolution or not
    api.typesv2.common.BooleanEnum is_fcr = 6;
    // details on how CSAT survey needs to happen after issue resolution
    IssueResolutionCsatSurveyConfig issue_resolution_csat_survey_config = 7;
    // represents various details that needs to be displayed to user, when they have a ticket with given issue category
    InAppTicketDetailsConfig in_app_ticket_details_config = 8;
    // represents fields to be populated in monorail ticket when issue is sent to product team
    MonorailConfig monorail_config = 9;
    // represents various reference material which can be used by agents to resolve an issue
    AgentReferenceMaterialConfig agent_reference_material_config = 10;
    // represents priority of the issue
    IssuePriority issue_priority = 11;
    // deeplink which lands to a screen where user's issue can be resolved
    // deprecated in favour of app_resolution_deeplink_v2
    frontend.deeplink.Deeplink app_resolution_deeplink = 12 [deprecated = true];
    AppResolutionDeeplink app_resolution_deeplink_v2 = 13;
    // text corresponding to L1, L2 and L3 that can be shown to users
    UserFriendlyCategoryText user_friendly_category_text = 14;
    // related faq articles corresponding to L1, L2 and L3
    RelatedFaqArticles related_faq_articles = 15;
    // flag to indicate if the issue category is deprecated or not
    api.typesv2.common.BooleanEnum is_deprecated = 16;
    // flag to indicate if the issue can be answered by LLM or not
    api.typesv2.common.BooleanEnum can_llm_resolve = 17;
  }
}

// IssueConfigFilter represents field on which filters can be applied while displaying IssueConfig
message IssueConfigFilter {
  string category = 1;
  // only populate if category (L1) is present
  string category_detail = 2;
  // only populated if category details (L2) is present
  string sub_category = 3;
  ConfigType config_type = 4;
}
