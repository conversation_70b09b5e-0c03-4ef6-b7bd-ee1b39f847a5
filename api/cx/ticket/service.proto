syntax = "proto3";

package cx.ticket;

import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/cx/ticket/attach_entity.proto";
import "api/cx/ticket/enums.proto";
import "api/cx/ticket/ticket.proto";
import "api/rpc/page.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/webui/detail_grid.proto";
import "api/typesv2/webui/info_component.proto";
import "api/vendorgateway/cx/freshdesk/ticket.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/ticket";
option java_package = "com.github.epifi.gamma.api.cx.ticket";

service ticket {
  rpc AttachEntity (AttachEntityRequest) returns (AttachEntityResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // returns list of tickets linked to the current ticket
  rpc GetRelatedTickets (GetRelatedTicketsRequest) returns (GetRelatedTicketsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc MergeTickets (MergeTicketsRequest) returns (MergeTicketsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetCallRecording gets the call recording of a particular recording id(for a call ticket)
  // returns the recording in chunks
  // returns internal error if recording not found in s3 bucket
  rpc GetCallRecording (GetCallRecordingRequest) returns (stream GetCallRecordingResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetCallTranscript gets the transcript file for a particular recording id(for a call ticket)
  // returns the file in chunks
  // returns internal error if transcript not found in s3 bucket
  rpc GetCallTranscript (GetCallTranscriptRequest) returns (stream GetCallTranscriptResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to fetch support tickets stored in cx db
  // will return not found if no ticket is found with given conditions
  // will return invalid argument if mandatory parameteres are not passed or values are invalid
  // all method options are set to false since this rpc won't be called in agent flows
  rpc GetSupportTickets (GetSupportTicketsRequest) returns (GetSupportTicketsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // rpc to update ticket details in bulk
  // will return invalid argument if given csv file or checker email is invalid
  // will return ok if job is added successfully in db and events are published to queue
  // will return internal for any server errors
  // enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
  rpc BulkUpdateTickets (BulkUpdateTicketsRequest) returns (BulkUpdateTicketsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // rpc to fetch list of all bulk ticket jobs
  // will return invalid argument if mandatory paramertes are missing in request
  // will return ok for success
  // will return internal for any server errors
  // will return not found if no job is found with given conditions
  // enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
  rpc GetAllBulkTicketJobs (GetAllBulkTicketJobsRequest) returns (GetAllBulkTicketJobsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // rpc to fetch list of all failures logs for a given job
  // will return invalid argument if mandatory paramertes are missing in request
  // will return ok for success
  // will return internal for any server errors
  // will return not found if no job or logs are found for given job id
  // enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
  rpc GetJobFailureLogs (GetJobFailureLogsRequest) returns (GetJobFailureLogsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // rpc to kill the given job, this will ensure no more tickets in queue are processed
  // will return invalid argument if mandatory paramertes are missing in request
  // will return ok for success
  // will return internal for any server errors
  // will return not found if no job or logs are found for given job id
  // enrichment, ticket validation method options are set to false since this rpc won't be called in agent flows
  rpc KillJobProcessing (KillJobProcessingRequest) returns (KillJobProcessingResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to retrieve freshdesk tickets from db
  // request accepts cx header, ticket filters and page context request
  // response contains rpc status code, list of tickets and page context response
  // OK if successful
  // InvalidArg if mandatory params are missing
  // NotFound if data not found
  // ISE for any other errors
  rpc GetSupportTicketsForSherlock (GetSupportTicketsForSherlockRequest) returns (GetSupportTicketsForSherlockResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to retrieve support tickets from db to be shown to the users in app
  // request accepts actor id, ticket filters and page context request
  // response contains rpc status code, list of tickets and page context response
  // OK if successful
  // InvalidArg if mandatory params are missing
  // NotFound if data not found
  // ISE for any other errors
  rpc GetSupportTicketsForApp (GetSupportTicketsForAppRequest) returns (GetSupportTicketsForAppResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to create ticket details transformation entry in the db (used from dev action)
  // request accepts ticket details transformations with required fields populated
  // response contains rpc status code, transformations with generated ids
  // OK if successful
  // InvalidArg if mandatory params are missing
  // AlreadyExists if duplicate entry for (product_category, product_category_details, subcategory, transformation_type) as key
  // ISE for any other errors
  rpc CreateTicketDetailsTransformations (CreateTicketDetailsTransformationsRequest) returns (CreateTicketDetailsTransformationsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to update ticket details transformation entry in the db (used from dev action)
  // request accepts transformation (with id mandatory) and field mask
  // response contains rpc status code
  // OK if successful
  // InvalidArg if mandatory params are missing
  // NotFound if data not found
  // ISE for any other errors
  rpc UpdateTicketDetailsTransformation (UpdateTicketDetailsTransformationRequest) returns (UpdateTicketDetailsTransformationResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to delete ticket details transformation entry in the db (used from dev action)
  // request accepts row id list
  // response contains rpc status code number of rows deleted
  // OK if successful
  // InvalidArg if mandatory params are missing
  // NotFound if data not found
  // ISE for any other errors
  rpc DeleteTicketDetailsTransformations (DeleteTicketDetailsTransformationsRequest) returns (DeleteTicketDetailsTransformationsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to get freshdesk ticket categories i.e product category, product category details and subcategory
  // values in string format populated in a nested manner
  // request only requires cx header
  // response contains status along with freshdesk ticket categories
  // OK if successful
  // ISE for any other errors
  rpc GetFreshdeskTicketCategories (GetFreshdeskTicketCategoriesRequest) returns (GetFreshdeskTicketCategoriesResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to retrieve ticket info like ticket category details as well as additional info like expected resolution time, sla, note, guru link etc
  // additional details are populated in the best effort manner
  // request accepts ticket id, which is taken as populated in cx header
  // ticket id is mandatory
  // OK if successful
  // Invalid argument if ticket id is not present or invalid details are passed for update
  // ISE for any other errors
  rpc GetTicketInfo (GetTicketInfoRequest) returns (GetTicketInfoResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to update ticket info, currently only supports product category, product category details and subcategory
  // request accepts ticket id, which is taken as populated in cx header
  // ticket id is mandatory
  // OK if successful
  // Invalid argument if ticket or ticket id is not present
  // ISE for any other errors
  rpc UpdateTicketInfo (UpdateTicketInfoRequest) returns (UpdateTicketInfoResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to publish update ticket event to cx update ticket queue
  // request accepts cx ticket, where ticket id is mandatory field
  // only non-empty fills are updated, empty fields are ignored
  // OK if successful
  // Invalid argument if ticket or ticket id is not present
  // ISE for any other errors
  rpc UpdateTicketAsync (UpdateTicketAsyncRequest) returns (UpdateTicketAsyncResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to asynchronously add a private note to ticket
  // This will publish private note event to a queue which allows to throttle the requests to avoid vendor rate limits
  // OK if successfully published to queue
  // Invalid argument if ticket id or body is not present
  // ISE for any other errors
  rpc AddPrivateNoteAsync (AddPrivateNoteAsyncRequest) returns (AddPrivateNoteAsyncResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // A wrapper RPC to create a freshdesk ticket from given CX ticket using VG CreateTicketRaw.
  // Request accepts cx ticket. Uses issue category Id if present in the ticket.
  // The fields subject, description, status, priority are mandatory
  // Other than that one of the following 5 fields are mandatory for creating ticket
  // requester_id, email, facebook_id, phone, twitter_id, unique_external_id. Refer ticket proto for more details about each field
  // In case of only phone(i.e. other 4 fields are null), name is also mandatory
  // default value for source is 2 if not sent with request
  rpc CreateTicket (CreateTicketRequest) returns (CreateTicketResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // rpc to retrieve support ticket from db for given ticket id to be shown to the users in app
  // request accepts id of ticket whose details needs to be fetched
  // response contains rpc status code, ticket details
  // OK if successful
  // InvalidArg if mandatory params are missing
  // NotFound if data not found
  // ISE for any other errors
  rpc GetSupportTicketByIdForApp (GetSupportTicketByIdForAppRequest) returns (GetSupportTicketByIdForAppResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // RPC to fetch all the tickets which are merged into given ticket
  // Merged tickets are also called secondary tickets which are merged into a primary ticket
  // Request requires a single field which is id of ticket for which we want to fetch merged tickets
  // Response contains list of ids representing tickets which are merged into ticket provided in request
  rpc GetMergedTickets (GetMergedTicketsRequest) returns (GetMergedTicketsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetTicketDetailsForSherlock fetches ticket details for sherlock
  rpc GetTicketDetailsForSherlock (GetTicketDetailsForSherlockRequest) returns (GetTicketDetailsForSherlockResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetTicketDetailsForSherlock fetches agent instruction for a ticket which the agent has to do for the ticket
  rpc GetAgentInstructionForTicket (GetAgentInstructionForTicketRequest) returns (GetAgentInstructionForTicketResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // CreateTicketAsync RPC creates support ticket in async manner
  // Request accepts cx ticket, if issue category id is provided L1, L2, L3 will be populated based on it
  // The fields subject, description, status, priority are mandatory
  // Other than that one of the following 5 fields are mandatory for creating ticket
  // requester_id, email, facebook_id, phone, twitter_id, unique_external_id. Refer ticket proto for more details about each field
  // In case of only phone(i.e. other 4 fields are null), name is also mandatory
  // clients can subscribe to sns topic: cx-ticket-create-event to get notified once ticket creation is completed
  rpc CreateTicketAsync (CreateTicketAsyncRequest) returns (CreateTicketAsyncResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // SubmitCsatFeedback RPC is used to record a csat response
  // it accepts request identifier token, csat score both are mandatory parameter
  rpc SubmitCsatFeedback (SubmitCsatFeedbackRequest) returns (SubmitCsatFeedbackResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetCategoryTransformation RPC fetches the category transformation details for a given ticket
  // It accepts ticket details
  rpc GetCategoryTransformation (GetCategoryTransformationsRequest) returns (GetCategoryTransformationsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // FetchLatestResolvedTicketPendingCSATByUserID fetches the most recent resolved ticket
  // for the given user that is still awaiting CSAT feedback.
  rpc FetchLatestResolvedTicketIdForCSAT (FetchLatestResolvedTicketIdForCSATRequest) returns (FetchLatestResolvedTicketIdForCSATResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message FetchLatestResolvedTicketIdForCSATRequest{
  string actor_id = 1;
}

message FetchLatestResolvedTicketIdForCSATResponse{
  rpc.Status status = 1;
  int64 ticket_id = 2;
}

message GetCategoryTransformationsRequest {
  cx.Header header = 1;
  cx.ticket.Ticket ticket = 2;
}

message GetCategoryTransformationsResponse {
  rpc.Status status = 1;
  // Title fetched via ticket details transformations
  string title = 2;
  // Description fetched via ticket details transformations
  string description = 3;
}

message SubmitCsatFeedbackRequest {
  // request identifier token will be provided by CX ticket service while triggering CSAT survey
  string request_identifier_token = 1;
  // csat score given by user
  int32 score = 2;
  // optional feedback that user can provide
  string feedback = 3;
}

message SubmitCsatFeedbackResponse {
  rpc.Status status = 1;
}

message GetTicketDetailsForSherlockRequest {
  cx.Header header = 1;
}

message GetTicketDetailsForSherlockResponse {
  rpc.Status status = 1;
  // Figma: https://www.figma.com/design/mPO3o3O1axCViWDGWLOn6o/%E2%98%8E%EF%B8%8F-Sherlock-%E2%80%A2%C2%A0Workfile?node-id=5102-43493&t=x9pUkUamjNYAlkUw-0
  repeated api.typesv2.webui.DetailView detail_views = 2;
  repeated api.typesv2.webui.InfoComponentView info_component_views = 3 [deprecated = true];
}

message GetAgentInstructionForTicketRequest {
  cx.Header header = 1;
  AgentInstructionType prev_instruction_type = 2;
}

message GetAgentInstructionForTicketResponse {
  rpc.Status status = 1;
  // Figma: https://www.figma.com/design/mPO3o3O1axCViWDGWLOn6o/%E2%98%8E%EF%B8%8F-Sherlock-%E2%80%A2%C2%A0Workfile?node-id=5102-43493&t=x9pUkUamjNYAlkUw-0
  AgentInstructionType instruction_type = 2;

  oneof instruction {
    // final agent instruction is to be populated when instruction type is FINAL INSTRUCTION
    // otherwise agent instruction is to be populated
    FinalAgentInstruction final_agent_instruction = 3;
    api.typesv2.webui.InfoComponentView agent_instruction = 4;
  }
  message FinalAgentInstruction {
    string title = 1;
    repeated Notes notes = 2;
    message Notes {
      string question = 1;
      string answer = 2;
    }
  }
}

message GetFreshdeskTicketCategoriesRequest {
  cx.Header header = 1;
}

message GetFreshdeskTicketCategoriesResponse {
  rpc.Status status = 1;
  // Nested message containing product category, product category details and subcategory list
  TicketCategories ticket_categories = 2;
}

message GetTicketInfoRequest {
  cx.Header header = 1;
}

message GetTicketInfoResponse {
  rpc.Status status = 1;
  // product category
  string product_category = 2;
  // product category details
  string product_category_detail = 3;
  // subcategory
  string subcategory = 4;
  // sla mapped to (product category, product category details, subcategory) in ticket_details_transformations table
  string sla = 5;
  // expected resolution time as determined by sla config from support_tickets table
  google.protobuf.Timestamp expected_resolution_time = 6;
  // escalation teams mapped to (product category, product category details, subcategory) in ticket_details_transformations table
  repeated EscalationTeam escalation_teams = 7;
  // common note mapped to (product category, product category details, subcategory) in ticket_details_transformations table
  string note = 8;
  // guru link mapped to (product category, product category details, subcategory) in ticket_details_transformations table
  string guru_link = 9;
  // is fcr mapped to (product category, product category details, subcategory) in ticket_details_transformations table
  api.typesv2.common.BooleanEnum is_fcr = 10;
}

message UpdateTicketInfoRequest {
  cx.Header header = 1;
  // product category in string format as it exists on freshdesk
  string product_category = 2;
  // product category details in string format as it exists on freshdesk
  string product_category_details = 3;
  // subcategory in string format as it exists on freshdesk
  string subcategory = 4;
}

message UpdateTicketInfoResponse {
  rpc.Status status = 1;
}

message TicketCategories {
  repeated ProductCategoryChoice product_category_choices = 1;
}

message ProductCategoryChoice {
  string product_category = 1;
  repeated ProductCategoryDetailsChoice product_category_details_choices = 2;
}

message ProductCategoryDetailsChoice {
  string product_category_details = 1;
  repeated SubcategoryChoice subcategory_choices = 2;
}

message SubcategoryChoice {
  string subcategory = 1;
}

message AttachEntityRequest {
  // agent email, access token and ticket id is mandatory in header
  cx.Header header = 1;

  EntityType entity_type = 2;

  string entity_id = 3 [deprecated = true];

  // list of AttachEntityMeta which contains entity info which has to be attached to the ticket
  repeated AttachEntityMeta attach_entity_meta_list = 4 [deprecated = true];

  // list of AttachEntityMeta which contains entity info which has to be attached to the ticket
  // as per current string validations there is a no way to add a validation if its a valid protojson
  // pattern based regex validation can be added. will add once we have a clarity on how this looks
  repeated string attach_entity_meta_list_v2 = 5;
}

message AttachEntityResponse {
  rpc.Status status = 1;
}

message GetRelatedTicketsRequest {
  // agent email, access token and ticket id is mandatory in header
  cx.Header header = 1;
}

message GetRelatedTicketsResponse {
  rpc.Status status = 1;

  repeated cx.freshdesk.Ticket tickets = 2;
}

message MergeTicketsRequest {
  // agent email, access token and ticket id is mandatory in header
  cx.Header header = 1;

  // id of ticket in which other tickets needs be merged
  int64 primary_ticket_id = 2;

  // list of ticket id's which needs to be merged into the primary ticket
  repeated int64 secondary_ticket_ids = 3;

  string primary_ticket_note = 4;

  string secondary_ticket_note = 5;
}

message MergeTicketsResponse {
  rpc.Status status = 1;
}

message GetCallRecordingRequest {
  // agent email, access token and ticket id is mandatory in header
  cx.Header header = 1;

  string recording_id = 2;
}

message GetCallRecordingResponse {
  rpc.Status status = 1;

  // chunk of call recording
  bytes chunk = 2;
}

message GetCallTranscriptRequest {
  // agent email, access token and ticket id is mandatory in header
  cx.Header header = 1;

  string recording_id = 2;
}

message GetCallTranscriptResponse {
  rpc.Status status = 1;

  // chunk of call recording
  bytes chunk = 2;
}

message GetSupportTicketsRequest {
  // optional
  // will single ticket with given id if passed
  int64 ticket_id = 1;
  // list of ticket filters that needs to be applied
  cx.ticket.TicketFilters ticket_filters = 2;
  // max page size allowed is 50
  // if page size is not passed default page size of 30 will be used
  rpc.PageContextRequest page_context_request = 3;
}

message GetSupportTicketsResponse {
  // will return
  // OK for success
  // NOT_FOUND if no ticket is found with given conditions
  rpc.Status status = 1;

  repeated cx.ticket.Ticket tickets = 2;

  rpc.PageContextResponse page_context_response = 3;
}

message BulkUpdateTicketsRequest {
  // agent email, access token are mandatory in header
  cx.Header header = 1;
  // csv file containing details of tickets to be updated
  bytes update_ticket_csv = 2;
  // email id of the agent who verified the file for update
  string checker_email = 3;
  // description of the job
  string description = 4;
}

message BulkUpdateTicketsResponse {
  rpc.Status status = 1;
  // job id for bulk update ticket case
  int64 job_id = 2;
}

message GetAllBulkTicketJobsRequest {
  // agent email, access token are mandatory in header
  cx.Header header = 1;

  BulkTicketJobFilters filters = 2;
  // max page size allowed is 50
  // if page size is not passed default page size of 30 will be used
  rpc.PageContextRequest page_context = 3;
}

message GetAllBulkTicketJobsResponse {
  rpc.Status status = 1;

  repeated BulkTicketJobDetails job_list = 2;

  rpc.PageContextResponse page_context_response = 3;
}

message GetJobFailureLogsRequest {
  // agent email, access token are mandatory in header
  cx.Header header = 1;

  int64 job_id = 2;
}

message GetJobFailureLogsResponse {
  rpc.Status status = 1;
  // list of ticket failures
  repeated TicketFailureLog failure_list = 2;
}

message KillJobProcessingRequest {
  // agent email, access token are mandatory in header
  cx.Header header = 1;

  // id of the job to be killed
  int64 job_id = 2;
}

message KillJobProcessingResponse {
  rpc.Status status = 1;
}

message ContactDetails {
  oneof contact_info {
    string email_id = 1;
    api.typesv2.common.PhoneNumber phone_number = 2;
  }
}

message GetSupportTicketsForSherlockRequest {
  // header is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  // specifies from which source to fetch tickets details from
  // if ticket filters is passed than fetches from db
  // if contact details is passed than fetches directly from freshdesk
  oneof fetch_filter {
    // list of ticket filters that needs to be applied
    cx.ticket.TicketFilters ticket_filters = 2;

    // need to specify what contact needs to be passed and will be queried on freshdesk accordingly
    ContactDetails contact_details = 3;
  }

  // max page size allowed is 50
  // if page size is not passed default page size of 30 will be used
  rpc.PageContextRequest page_context_request = 4;

}

message GetSupportTicketsForSherlockResponse {
  // rpc status
  rpc.Status status = 1;

  // list of tickets
  repeated cx.ticket.Ticket tickets = 2;

  // pagination request field
  rpc.PageContextResponse page_context_response = 3;
}

message GetSupportTicketsForAppRequest {
  // actor_id is mandatory
  string actor_id = 1 [(validate.rules).string.min_len = 1];

  // list of ticket filters that needs to be applied
  TicketFiltersForUser ticket_filters = 2;

  // max page size allowed is 20
  // if page size is not passed default page size of 10 will be used
  rpc.PageContextRequest page_context_request = 3;
  // flag to specify whether cache data is required
  // as latest ticket of user is being shown in Home, and can have a lot of hits it's better to cache the results
  bool should_use_cache = 4;
}

message GetSupportTicketsForAppResponse {
  // rpc status
  rpc.Status status = 1;

  // list of tickets
  // The status of first ticket is used to decide whether to inform user that the latest ticket details still being updated
  // This is decided based on list of mandatory params to be populated before a ticket can be shown
  // If the status is set to UPDATING_DETAILS, latest-ticket-still-updating card must be shown to user
  repeated TicketDetailsForUser tickets = 2;

  // isLatestTicketDetailsStillUpdating flag is used to decide whether to inform user that the latest ticket details still being updated
  // This is decided based on list of mandatory params to be populated before a ticket can be shown
  // If this flag is set to TRUE, latest-ticket-still-updating card must be shown to user
  api.typesv2.common.BooleanEnum isLatestTicketDetailsStillUpdating = 3;

  // pagination request field
  rpc.PageContextResponse page_context_response = 4;
}

message CreateTicketDetailsTransformationsRequest {
  // details of the transformation to be created
  repeated TicketDetailsTransformation transformations_list = 1;
}

message CreateTicketDetailsTransformationsResponse {
  // rpc status
  rpc.Status status = 1;
  // created transformation with Id
  repeated TicketDetailsTransformation transformations_list = 2;
}

message UpdateTicketDetailsTransformationRequest {
  // Id is mandatory in the transformation
  TicketDetailsTransformation transformation = 1;
  // update mask
  repeated TicketDetailsTransformationFieldMask update_mask = 2;
}

message UpdateTicketDetailsTransformationResponse {
  // rpc status
  rpc.Status status = 1;
}

message DeleteTicketDetailsTransformationsRequest {
  // Delete will be performed by applying AND on the following filters if exists
  // At least one of the filters must be passed to avoid accidental deletes
  repeated string row_id_list = 1;
  repeated TicketTransformationType transformation_type_list = 2;
  repeated ProductCategory product_category_list = 3;
}

message DeleteTicketDetailsTransformationsResponse {
  // rpc status
  rpc.Status status = 1;
  // number of records deleted
  int64 deleted_count = 2;
}

message UpdateTicketAsyncRequest {
  // cx ticket with updated fields
  cx.ticket.Ticket ticket = 1;
}

message UpdateTicketAsyncResponse {
  // rpc status
  rpc.Status status = 1;
}

message AddPrivateNoteAsyncRequest {
  // mandatory: Id of the ticket to which the private note is to be added
  int64 ticket_id = 1;
  // mandatory: body of note
  string body = 2;
  // list of emails to notify
  repeated string notify_emails = 3;
  // id of agent who is adding the note
  int64 agent_id = 4;
}

message AddPrivateNoteAsyncResponse {
  // rpc status
  rpc.Status status = 1;
}

message CreateTicketRequest {
  Ticket ticket = 1;
  // in case the caller wants the ticket creation to be async
  // for high traffic flow, we might want to do this due to the vendor rate limits we have on creation API
  // also async ensures the intermittent failures are handled by retrying
  // if opted for async ticket creation ticket id won't be shared in response
  // deprecated in favour of CreateTicketAsync RPC
  bool is_async_creation_required = 2 [deprecated = true];
}

message CreateTicketResponse {
  rpc.Status status = 1;

  Ticket ticket = 2;
}

message GetSupportTicketByIdForAppRequest {
  int64 ticket_id = 1;
  string actor_id = 2;
}

message GetSupportTicketByIdForAppResponse {
  rpc.Status status = 1;

  // ticket details for the given ticket_id
  TicketDetailsForUser ticket = 2;
}

message GetMergedTicketsRequest {
  // id of ticket for which we want to fetch merged tickets
  int64 ticket_id = 1 [(validate.rules).int64.gte = 1];
}

message GetMergedTicketsResponse {
  enum Status {
    // successfully returned ticket ids of merged tickets
    OK = 0;
    // if there are no tickets merged
    RECORD_NOT_FOUND = 5;
    // system faced internal errors while processing the request
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // ids of ticket which are merged into ticket provided in required
  // note: this list only contains id of merged tickets so the primary ticket (the one in request) won't be present in it
  repeated int64 merged_ticket_ids = 2;
}

message CreateTicketAsyncRequest {
  // cx ticket with updated fields
  cx.ticket.Ticket ticket = 1;
  // identifier to be used by clients for listening to the updates on ticket creation
  ClientRequestInfo client_request_info = 2;
}

message CreateTicketAsyncResponse {
  rpc.Status status = 1;
}
