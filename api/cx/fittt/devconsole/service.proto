// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package cx.fittt.devconsole;

import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "google/protobuf/timestamp.proto";
import "api/fittt/devconsole/sports.proto";

option go_package = "github.com/epifi/gamma/api/cx/fittt/devconsole;cxdevconsolepb";
option java_package = "com.github.epifi.gamma.api.cx.fittt.devconsole";

// Service supports FIT Developer APIs to create and manage rules.
service CxFitDeveloperConsoleService {
  // RPC to get list of sports that are supported in FIT.
  rpc GetSportsList(GetSportsListRequest) returns(GetSportsListResponse){
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // RPC to get list of ongoing and upcoming tournaments for the given sport.
  // Tournament message returned from this rpc will not have teams, players, rules populated.
  // Use GetTournament with tournament id to get all the details.
  rpc GetTournaments(GetTournamentsRequest) returns(GetTournamentsResponse){
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // RPC returns tournament details with players, teams and rules.
  rpc GetTournament(GetTournamentRequest) returns(GetTournamentResponse){
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // RPC to configure new tournament or update existing tournament with new configuration.
  // For existing tournament, RPC make changes to them based on the diff from new and existing configuration
  rpc ConfigureTournaments(ConfigureTournamentsRequest) returns(ConfigureTournamentsResponse){
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // RPC returns sports events that can be scheduled for a tournament.
  // TODO(sakthi) - Now, RPC doesn't validate existing schedules and returns all available schedules.
  // Change this contract and BE support if required in future.
  rpc GetSportsEventSchedules(GetSportsEventSchedulesRequest) returns(GetSportsEventSchedulesResponse){
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // RPC to setup triggers to execute rules for sports events.
  rpc ScheduleSportsEventTriggers(ScheduleSportsEventTriggersRequest) returns(ScheduleSportsEventTriggersResponse){
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}


message GetSportsListRequest {}


message GetSportsListResponse {
  rpc.Status status = 1;
  repeated api.fittt.devconsole.SportType sport_types = 2;
}

message GetTournamentsRequest {
  api.fittt.devconsole.SportType sport_type = 1;
}


message GetTournamentsResponse {
  rpc.Status status = 1;
  repeated api.fittt.devconsole.Tournament tournaments = 2;
}

message GetTournamentRequest {
  api.fittt.devconsole.SportType sport_type = 1;
  string tournament_id = 2;
}

message GetTournamentResponse {
  rpc.Status status = 1;
  api.fittt.devconsole.Tournament tournament = 2;
}

message ConfigureTournamentsRequest {
  api.fittt.devconsole.Tournament tournament = 1;
}

message ConfigureTournamentsResponse {
  rpc.Status status = 1;
}

message  GetSportsEventSchedulesRequest {
  api.fittt.devconsole.SportType sport_type = 1;
  string tournament_id = 2;
}

message GetSportsEventSchedulesResponse {
  rpc.Status status = 1;
  repeated SportsEventSchedule schedules = 2;
}


message ScheduleSportsEventTriggersRequest {
  repeated SportsEventSchedule schedules = 1;
}

message ScheduleSportsEventTriggersResponse {
  rpc.Status status = 1;
}


message SportsEventSchedule {
  api.fittt.devconsole.SportType sport_type = 1;
  string tournament_id = 2;
  string id = 3;
  // optional - will be empty if the start is not available
  google.protobuf.Timestamp expected_start = 4;
  // optional - will be empty if the end is not predictable or available
  google.protobuf.Timestamp expected_end = 5;
  // optional - will be empty if the expected_start is not available
  google.protobuf.Timestamp schedule_execution_at = 6;
}
