syntax = "proto3";

package cx.risk_ops;

import "api/cx/developer/db_state/enums.proto";
import "api/typesv2/webui/media.proto";
import "api/typesv2/webui/table.proto";

option go_package = "github.com/epifi/gamma/api/cx/risk_ops";
option java_package = "com.github.epifi.gamma.api.cx.risk_ops";

message ReviewSection {
  UIOptions ui_options = 1;
  Data data = 2;
}

message UIOptions {
  int32 position = 1;

  bool is_collapsible = 2;
  // Review section name
  string header = 3;
}

message Data {
  repeated string dev_actions = 1;

  repeated DataValue info_data = 2;

  repeated api.typesv2.webui.MediaUnit media_unit = 3;

  api.typesv2.webui.Table table = 4;
}

message DataValue {
  string label = 1 [deprecated = true];

  string value = 2 [deprecated = true];

  TextElement label_element = 3;

  TextElement value_element = 4;
}

message TextElement {
  string text = 1;
  // styling properties to be applied for the cell
  api.typesv2.webui.Style style = 2;
}

// Product contains all product offerings by Fi.
enum Product {
  PRODUCT_UNSPECIFIED = 0;
  PRODUCT_SAVINGS_ACCOUNT_FEDERAL = 1;
  PRODUCT_CREDIT_CARD_FEDERAL = 2;
  PRODUCT_FI_LITE_LOANS_LIQUILOANS = 3;
}

// FormElement can be used to represent a single form.
message FormElement {
  // Header for the form
  FormHeader header = 1;

  repeated FormSection sections = 2;
}

// Header element of a form.
message FormHeader {
  repeated DataValue data_values = 1;
}

// Form element can be divided in sections.
// A single section can contain question and response element.
message FormSection {
  QuestionElement question_element = 1;
  // Submitted responses for the question, will be empty is not submitted yet.
  ResponseElement response_element = 2;
}

message ResponseElement {
  oneof element {
    TextElements text_elements = 1;

    File file = 2;
  }
}

message File {
  // Pre signed s3 url.
  string url = 1;
  // Content type e.g. csv, pdf, jpeg etc.
  string type = 2;
}

message QuestionElement {
  TextElement text_element = 1;
}

message TextElements {
  repeated TextElement elements = 1;
}
message FormField {
  // name or key or identifier of the field
  // UI will pass this back to backend as identifier for field
  string name = 1;

  // label to be shown on UI for the field
  string label = 2;

  // data type of field
  // UI will show appropriate input type for the given field based on this property
  developer.db_state.ParameterDataType type = 3;

  // applicable only in case of type DROPDOWN or MULTI_SELECT_DROPDOWN
  // list of options to be shown in dropdown for the parameter mapped against
  // conditional parameters for selected choice.
  map<string, FormFields> options = 4;

  // this field indicates whether the field is optional or not in the data request
  // using enum for this because bool defaults to false in go which could cause unexpected behaviour
  developer.db_state.ParameterOption parameter_option = 5;
}

message FormFields {
  repeated FormField fields = 1;
}
