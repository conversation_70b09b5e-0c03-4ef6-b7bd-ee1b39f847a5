syntax = "proto3";

package cx.chowkidar.consumer;

import "api/cx/chowkidar/chowkidar.proto";
import "api/cx/method_options.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/cx/chowkidar/consumer";
option java_package = "com.github.epifi.gamma.api.cx.chowkidar.consumer";

service ChowkidarConsumer {
  // consumer rpc which consumes alerts from chowkidar alert queue
  // if alert is of firing type, it will create a sherlock banner if not present, and add record in DB
  // if alert is of resolve type, it will remove the banner if it exists, and update the DB
  // it handles de-duplication of alerts so that multiple banners aren't created for same type of alerts
  rpc ProcessAlerts (ProcessAlertsRequest) returns (ProcessAlertsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message ProcessAlertsRequest {
  queue.ConsumerRequestHeader request_header = 1;
  // list of alerts sharing the same status
  repeated ChowkidarAlert alerts = 2;
}

message ProcessAlertsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
