// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package cx;

import "api/casbin/casbin.proto";
import "api/cx/enums.proto";
import "api/typesv2/actor.proto";
import "api/typesv2/common/phone_number.proto";
import "api/user/user.proto";
import "api/vendorgateway/cx/freshdesk/ticket.proto";
import "google/protobuf/timestamp.proto";
import "google/type/postal_address.proto";

option go_package = "github.com/epifi/gamma/api/cx";
option java_package = "com.github.epifi.gamma.api.cx";

message Header {
  // email of the agent loggedIn in sherlock
  string agent_email = 1;

  // access token of the agent
  string access_token = 2;

  // ticket id for which the information is being accessed
  // should be sent only if ticket check is required for the method (this will be decided by method_options)
  int64 ticket_id = 3;

  // user identifier
  // should be used only if user is calling from different number
  // agent has to manually enter user identifier on sherlock UI
  oneof identifier {
    string email_id = 4;

    api.typesv2.common.PhoneNumber phone_number = 5;
  }

  // ticket details
  // this is shouldn't be send as part of request
  // this object will populated by ticket validator interceptor to be used in all the methods
  // DISCLAIMER: the ticket populated in header is cached on cx side, it shouldn't be used in flows where latest ticket details are needed/critical.
  cx.freshdesk.Ticket ticket = 8;

  // this is shouldn't be send as part of request
  // this user object will populated by enricher interceptor to be used in all the methods
  user.User user = 9;

  // this is shouldn't be send as part of request
  // this user object will populated by enricher interceptor to be used in all the methods
  api.typesv2.Actor actor = 10;

  // this shouldn't be send as part of request
  // this will be populated by interceptor after reading the value from method options
  cx.InformationLevel information_level = 11;

  // access level of the user
  casbin.AccessLevel access_level = 12;

  // monorail id of the issue for which data is being accessed
  // this should be passed in case of dev tool rpc's
  int64 monorail_id = 13;

  // this shouldn't be passed as part of request
  // this will be used in customer auth flows to pass this info
  CustomerDetails customer_details = 14;

  // this should be populated as part of request to inform backend which auth version has to be used
  // if not populated or V1 is specified than V1 single role implementation will be used
  // If V2 is specified than use multi role implementation
  cx.AuthVersion auth_version = 15;

  // this should be populated as part of request if customer auth flow is called
  // anywhere outside regular ticket id flow
  // depending upon type specified, relevant actions are taken by the server
  cx.CustomerAuthIdentifierType customer_auth_identifier_type = 16;

  // this field should be populated as part of request if CustomerAuthEntryIdentifierType is not UNSPECIFIED
  // this field will be used by server to maintain auth session information
  string customer_auth_identifier_value = 17;

  // this field should be set to true from the client when requesting auth for insensitive data (CATEGORY FOUR AUTH)
  bool is_insensitive_auth_requested = 18;

  DeploymentSource deployment_source = 19;
}

message CustomerDetails {
  map<string, google.type.PostalAddress> addresses = 1;

  user.User user = 2;
}

// We have incorporated token based pagination to make this RPC forward and backward compatible.
// This will allow us to implement pagination in backend without the need to make any change in client.This contains data
// relevant to the server in order to send the next page.
message PageToken {
  // created timestamp of the last event on the page
  google.protobuf.Timestamp last_event_timestamp = 1;

  // An offset lets the caller control the number of records that needs to be skipped
  // starting from start timestamp.
  // e.g. we can have 10 events starting with the timestamp start_timestamp. If offset is
  // set to 5 then first 5 records from the qualifying set are removed.
  int32 event_offset = 2;
}

// page context to set offset, and limit in the request
// 1st request will have *limit* flag to specify number of elements from backend
// backend will send `PageContext` -- setting before_token and after_token
// next paginated request will send the same PageContext which will have 2 tokens and 2 flags
// to identify `next` and `before` token to paginate forward and backward
// read more on this - "https://hackernoon.com/guys-were-doing-pagination-wrong-f6c18a91b232"
message PageContextResponse {
  // token to paginate backward in the list
  string before_token = 1;
  // if items are present before before_token
  bool has_before = 2;
  // token to paginate forward in the list
  string after_token = 3;
  // if items are present after after_token
  bool has_after = 4;
}

message PageContextRequest {
  oneof token {
    string before_token = 1;
    string after_token = 2;
  }
  // page size
  uint32 page_size = 3;
}
