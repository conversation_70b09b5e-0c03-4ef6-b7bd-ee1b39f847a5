// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb

syntax = "proto3";

package cx.consumer;

import "api/aws/s3/s3.proto";
import "api/cx/call/message.proto";
import "api/cx/consumer/enums.proto";
import "api/cx/issue_resolution_feedback/messages.proto";
import "api/cx/sprinklr/message.proto";
import "api/cx/ticket/ticket.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/common/phone_number.proto";
import "api/vendorgateway/cx/freshdesk/ticket.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/cx/consumer";
option java_package = "com.github.epifi.gamma.api.cx.consumer";


message FreshdeskUpdateEventDetails {
  string id = 1;

  int64 freshdesk_id = 2;

  EventType event_type = 3;

  MessageState message_state = 4;

  google.protobuf.Timestamp created_at = 5;

  google.protobuf.Timestamp updated_at = 6;
}

message ProcessTicketEventRequest {
  queue.ConsumerRequestHeader consumer_request_header = 1;

  int64 ticket_id = 2;

  string reference_id = 3;

  TicketEvent ticket_event = 4;

  // id of the record which was created before pushing message to queue
  string message_id = 5;

  // complete ticket object in request to be passed if update/create on all fields is needed
  cx.freshdesk.Ticket ticket = 6;

  api.typesv2.common.PhoneNumber phone_number = 7;

  string email = 8;
  // Bank customer Id to be populated in the ticket.
  string customer_id = 9;
}

message ProcessTicketEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessContactEventRequest {
  queue.ConsumerRequestHeader consumer_request_header = 1;

  int64 contact_id = 2;

  ContactEvent contact_event = 3;

  // id of the record which was created before pushing message to queue
  string message_id = 4;
}

message ProcessContactEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message UpdateTicketPayload {
  // what type of ticket update it is: call, dispute, chat, etc
  UpdateTicketRequestType update_ticket_request_type = 1;

  // queue payload
  oneof details_payload {
    BulkRawTicketUpdatePayload bulk_raw_ticket_update_payload = 2;
    // raw ticket object with fields to be updated in freshdesk
    // ozonetel call details to be updated on freshdesk
    cx.call.CallDetails call_details = 3;
    // issue resolution feedback payload
    cx.issue_resolution_feedback.IssueResolutionTicketUpdateEventPayload issue_resolution_ticket_update_event_payload = 4;
    // payload to update expected resolution date determined by sla config
    ExpectedResolutionDateUpdatePayload expected_resolution_date_update_payload = 5;
    // payload to update ticket based on info provided on sherlock
    SherlockTicketInfoUpdatePayload sherlock_ticket_info_update_payload = 6;
    // sprinklr payload for ticket update
    cx.sprinklr.SprinklrPayload sprinklr_payload = 7;
    // dispute payload for ticket update
    DisputeDetailsPayload dispute_details_payload = 8;
    // payload to update any fields in cx ticket
    CxTicketUpdatePayload cx_ticket_update_payload = 9;

    // raw ticket update payload to update any fields in Freshdesk
    RawTicketUpdatePayload raw_ticket_update_payload = 10;
  }
}

message SherlockTicketInfoUpdatePayload {
  // details of ticket to be updated in form of raw ticket
  // we are using raw ticket here to keep this update extendable to future without adding any new fields
  cx.freshdesk.TicketRaw ticket = 1;
}

message ExpectedResolutionDateUpdatePayload {
  // id of ticket to be updated
  int64 ticket_id = 1;
  // expected resolution date to be updated
  google.protobuf.Timestamp expected_resolution_date = 2;
}

message DisputeDetailsPayload {
  // id of ticket to be updated
  int64 ticket_id = 1;
  // internal id to uniquely identify a raised dispute
  string dispute_id = 2;
}

message CreateTicketPayload {
  // create_ticket_request_type indicates the type of event for which ticket creation will happen
  CreateTicketRequestType create_ticket_request_type = 1;

  // queue payload
  oneof details_payload {
    // sprinklr payload for ticket creation
    cx.sprinklr.SprinklrPayload sprinklr_payload = 2;
    // ticket creation for DMP correspondence flow
    DMPCorrespondenceCreateTicketPayload dmp_correspondence_payload = 3;
    // Payload to add a private note
    // Adding this to create ticket queue because Freshdesk rate limit is shared here.
    // Freshdesk has a separate rate limit for POST type requests
    // and PUT type requests. UpdateTickets falls under PUT and CreateTicket, AddPrivateNote fall under POST
    // Ref: https://docs.google.com/document/d/1li3Bvz1FTr69d2T0OZCIMSJnBUh5a8ttEkXZJazQVFk/edit#heading=h.gdvocvm14wl8, https://epifi.slack.com/archives/C03R4JFP3EJ/p1688540411847719
    AddPrivateNotePayload add_private_note_payload = 4;
    // ticket creation with help of CX ticket payload
    ticket.Ticket cx_ticket_payload = 5;
  }
  ticket.ClientRequestInfo client_request_info = 6;
}

// create ticket payload for DMP correspondence flow
message DMPCorrespondenceCreateTicketPayload {
  string disputeId = 1;
  // freskdesk ticket with required details populated
  cx.freshdesk.Ticket ticket = 2;
  // Text which will be added as private note to the ticket
  string private_note = 3;
}

message BulkRawTicketUpdatePayload {
  // job id for the bulk ticket update job
  int64 job_id = 1;
  // id of ticket to be updated
  int64 ticket_id = 2;
  // details of ticket to be updated
  cx.freshdesk.TicketRaw ticket = 3;

  string private_note = 4;
}

message RawTicketUpdatePayload {
  // details of ticket to be updated
  cx.freshdesk.TicketRaw ticket = 1;
}

message CxTicketUpdatePayload {
  // cx ticket with updated fields
  cx.ticket.Ticket ticket = 1;
}

// Payload for adding a private note to a ticket.
message AddPrivateNotePayload {
  // mandatory: Id of the ticket to which the private note is to be added
  int64 ticket_id = 1;
  // mandatory: body of note
  string body = 2;
  // list of emails to notify
  repeated string notify_emails = 3;
  // id of agent who is adding the note
  int64 agent_id = 4;
}

message UpdateTicketEventRequest {
  // queue request header
  queue.ConsumerRequestHeader request_header = 1;

  // update payload: signifying update type and details
  UpdateTicketPayload update_ticket_payload = 2;
}

message UpdateTicketEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message CreateTicketEventRequest {
  // queue request header
  queue.ConsumerRequestHeader request_header = 1;

  // create ticket payload : signifying create ticket type and details
  CreateTicketPayload create_ticket_payload = 2;
}

message CreateTicketEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessS3EventRequest {
  queue.ConsumerRequestHeader request_header = 1;
  repeated aws.s3.Record records = 2 [json_name = "Records"];
}

message ProcessS3EventResponse {
  // the queue subscriber code depends on this header to take further action on the queue message.
  // i.e. removing message to the queue or increasing timeouts
  queue.ConsumerResponseHeader response_header = 1;
}

message EscalationUpdatePayload {
  EscalationRequestType update_ticket_request_type = 1;
  // Ref: https://docs.google.com/document/d/1jgVGMQ27pXERji_FBlu13eI0IYOo2ob2/edit
  // IODisposalStatus
  string i_o_disposal_status = 2 [json_name="IODisposalStatus"];
  // IOComments
  string i_o_comments = 3 [json_name="IOComments"];
  // IODisposalRemarks
  string i_o_disposal_remarks = 4 [json_name="IODisposalRemarks"];
  // StatusCd
  string status_cd = 5 [json_name="StatusCd"];
  // FintechSRNO
  string fintech_s_r_n_o = 6 [json_name="FintechSRNO"];
}

message ProcessFederalEscalationEventRequest{
  // queue request header
  queue.ConsumerRequestHeader request_header = 1;

  // escalation update payload: signifying update type and details
  EscalationUpdatePayload escalation_update_payload = 2;
}

message ProcessFederalEscalationEventResponse{
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessFederalEscalationCreationEventRequest{
  // queue request header
  queue.ConsumerRequestHeader request_header = 1;

  // escalation creation payload: signifying creation type and details
  EscalationCreationPayload escalation_creation_payload = 2;
}

// EscalationCreationRequest represents the request payload for creating a service request
message EscalationCreationRequest {
  // Title of the service request (max 240 characters)
  string title = 1;

  // Problem description (max 1000 characters)
  string problem_description = 2;

  // Category code from the predefined list
  string category_code = 3;

  // SR Type (e.g., FB_COMPLAINT)
  string sr_type = 4;

  // Whether the user is an existing customer
  string existing_customer = 5;

  // Type of customer (e.g., FB_INDIVIDUAL)
  string customer_type = 6;

  // Federal Bank Customer ID (max 80 characters)
  string cif_id = 7;

  // Channel code email (14 digits)
  string channel_code_email = 8;

  // Escalation status
  string escalation_status = 9;

  // Queue ID (numeric)
  int64 queue_id = 10;

  // L1 decision
  string l1_decision = 11;

  // L1 updated by
  string l1_updated_by = 12;

  // L1 updated time in format YYYY-MM-DDTHH:MM:SS
  string l1_updated_time = 13;

  // Application reference ID (max 80 characters)
  string application_ref_id = 14;

  // Channel type code
  string channel_type = 15;

  // Complaint acceptance status
  string complaint_status = 16;

  // Product name
  string product_name = 17;

  // L1 recommendation (max 1000 characters)
  string l1_recommendation = 18;

  // Branch SOL ID (4 digits)
  string branch_sol = 19;

  // Optional attachment file
  bytes attachment = 20;

  // mandatory ticket id for which the escalation is being created
  int64 ticket_id = 21;

  // user identifier for whom the escalation is being created
  string actor_id = 22;

  // s3 path of attachment
  string attachment_s3_path = 23;
}

message EscalationCreationPayload{
  // Ticket request type : Can be ESCALATION_REQUEST_TYPE_CREATE, ESCALATION_REQUEST_TYPE_REOPEN
  EscalationRequestType create_ticket_request_type = 1;

  EscalationCreationRequest escalation_creation_request = 2;
}

message ProcessFederalEscalationCreationEventResponse{
  queue.ConsumerResponseHeader response_header = 1;
}


service FreshdeskEventConsumer {
  // Consumer RPC which consumes messages from freshdesk ticket events queue
  // This will try to update ticket in freshdesk based on the information passed in request
  // Depending on the current use cases we have to update 1) requester details in one case
  // 2) custom field in another case 3) update ticket
  // IMPORTANT : Method options are not needed here since the consumer service is registered to queue not server
  rpc ProcessTicketEvent (ProcessTicketEventRequest) returns (ProcessTicketEventResponse) {}

  // Consumer RPC which consumes messages from freshdesk contacts events queue
  // This will try to update contact in freshdesk based on the information passed in request
  // Depending on the current use cases we have to 1) soft delete a contact
  // IMPORTANT : Method options are not needed here since the consumer service is registered to queue not server
  rpc ProcessContactEvent (ProcessContactEventRequest) returns (ProcessContactEventResponse) {}

  // consumer rpc which consumes the messages from freshdesk update ticket events queue
  // depending upon request type, backend will select appropriate processor and update the ticket as per processor's logic
  // we should use this common consumer for all update ticket use cases to have better control over number of updates we are processing
  // and can also turn off the consumer temporarily in case of any vendor issues
  rpc UpdateTicketEvent (UpdateTicketEventRequest) returns (UpdateTicketEventResponse) {}

  // consumer rpc which consumes the messages from freshdesk create ticket events queue
  // depending upon request type, backend will select appropriate processor and create the ticket as per processor's logic
  // we should use this common consumer for all create ticket use cases to have better control over number of updates we are processing
  // and can also turn off the consumer temporarily in case of any vendor issues
  rpc CreateTicketEvent (CreateTicketEventRequest) returns (CreateTicketEventResponse) {}

  // ProcessS3Event consumes s3 events published from any (configurable) s3 bucket
  // Request will provide us filePath of the newly created output file, and bucketName
  // To subscribe this consumer to any s3 bucket's event queue:"cx-s3-event-queue" must be added in s3-event queue of that bucket
  rpc ProcessS3Event (ProcessS3EventRequest) returns (ProcessS3EventResponse) {}

  // ProcessFederalEscalationEvent consumes messages from federal escalation event queue via Vendor notification service
  rpc ProcessFederalEscalationEvent (ProcessFederalEscalationEventRequest) returns (ProcessFederalEscalationEventResponse) {}

  // ProcessFederalEscalationCreationEvent consumes messages from federal escalation creation event queue via sherlock dev action
  rpc ProcessFederalEscalationCreationEvent (ProcessFederalEscalationCreationEventRequest) returns (ProcessFederalEscalationCreationEventResponse) {}
}
