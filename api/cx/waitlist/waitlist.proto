syntax = "proto3";
package cx.waitlist;

import "api/typesv2/common/phone_number.proto";

option go_package = "github.com/epifi/gamma/api/cx/waitlist";
option java_package = "com.github.epifi.gamma.api.cx.waitlist";

// Freelance flow user information.
message FreelanceData {
  string profession = 1;
  string work_url = 2;
  string work_email = 3;
  string comm_email = 4;
  string user_id = 5;
  api.typesv2.common.PhoneNumber phone_number = 6;
  // current approval status of the user
  // possible values:
  // FREELANCER_STATUS_UNSPECIFIED = 0;
  // FREELANCER_STATUS_ACCEPTED = 1;
  // FREELANCER_STATUS_REJECTED = 2;
  // FREELANCER_STATUS_ON_HOLD = 3;
  string approval_status = 7;
  string reason = 8;
}
