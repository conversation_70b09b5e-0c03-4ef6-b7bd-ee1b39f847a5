syntax = "proto3";

package cx;

option go_package = "github.com/epifi/gamma/api/cx";
option java_package = "com.github.epifi.gamma.api.cx";

// the information levels don't have any hierarchy between them
// each information level will have a set of rules associated with them which needs to be passed to access rpc's behind that level
enum InformationLevel {
  INFORMATION_ACCESS_LEVEL_UNSPECIFIED = 0;

  // this will contain sensitive information of user
  // this level will mostly contain information screens which don't have any reversible actions on them
  MODERATELY_SENSITIVE = 1;

  // this will contain highly sensitive information/actions
  // permanent sensitive actions like block card will be behind this auth level
  HIGHLY_SENSITIVE = 2;

  // this will contain restricted set of actions which are reversible
  // for ex suspend card, freeze account, raise dispute etc
  RESTRICTED_REVERSIBLE = 3;

  // To denote that information does not belong to any category
  // no customer auth check will be done for this information level
  INSENSITIVE = 4;

  // this will contain less sensitive information of user
  // this level will mostly contain all the screens which contains restricted reversible actions
  LESS_SENSITIVE = 5;
}

// AuthVersion enum to identify which implementation to use
// single role agent access or multi role sherlock user access
// If AuthVersion is unspecified or V1 than single role implementation will be used
// If V2 is specified than multi-role access management will be used
enum AuthVersion {
  // unspecified, use single role implementation
  AUTH_VERSION_UNSPECIFIED = 0;
  // V1, use single role implementation
  AUTH_VERSION_V1 = 1;
  // V2, use multi-role implementation
  AUTH_VERSION_V2 = 2;
}

// CustomerAuthIdentifierType enum to identify calling identifier for customer auth flow
// If unspecified, than servers understands that it is being called in ticket id flow
// if OZONETEL_MONITOR_UCID than it is being called in Sherlock call details flow
enum CustomerAuthIdentifierType {
  // unspecified, if uses regular ticket id flow
  CUSTOMER_AUTH_IDENTIFIER_TYPE_UNSPECIFIED = 0;
  // ticket id identifier, client to send this in header so server start populate the new field
  CUSTOMER_AUTH_IDENTIFIER_TYPE_FRESHDESK_TICKET_ID = 1;
  // uses ozonetel monitor ucid as auth entry identifier
  CUSTOMER_AUTH_IDENTIFIER_TYPE_OZONETEL_MONITOR_UCID = 2;
  // uses customer phone number to enrich actor id
  CUSTOMER_AUTH_IDENTIFIER_TYPE_PHONE_NUMBER = 3;
  // uses customer email id to enrich actor id
  CUSTOMER_AUTH_IDENTIFIER_TYPE_EMAIL_ID = 4;
}

// Sherlock is deployed at different sources due to new use cases
// This enum is used to identify the deployment source of the request
// This distinction allows us to choose the right authentication as per source, e.g, user poolId for cognito client is chosen as per source and used to authenticate access token
enum DeploymentSource {
  DEPLOYMENT_SOURCE_UNSPECIFIED = 0;
  // Existing inhouse hosting
  DEPLOYMENT_SOURCE_INHOUSE = 1;
  // Sherlock is hosted at federal for IFT portal due to compliance
  // https://docs.google.com/document/d/1hp000lpzfNLUm_kke6KDbfWq5mqO5nUDc73mT5FuSg0/edit?tab=t.0
  DEPLOYMENT_SOURCE_FEDERAL = 2;
}
