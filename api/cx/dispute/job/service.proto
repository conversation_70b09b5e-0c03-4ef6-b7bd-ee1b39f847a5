syntax = "proto3";
package cx.dispute.job;

import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "google/protobuf/empty.proto";

option go_package = "github.com/epifi/gamma/api/cx/dispute/job";
option java_package = "com.github.epifi.gamma.api.cx.dispute.job";

service DisputeProcessingJob {
  // generic rpc to raise dispute via SFTP and freshdesk
  // throws invalid argument error if mandatory params are not passed
  rpc ProcessDisputesJob(google.protobuf.Empty) returns (rpc.Status) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // generic rpc to raise dispute via SFTP and freshdesk
  // throws invalid argument error if mandatory params are not passed
  rpc ProcessReverseUpdateJob(google.protobuf.Empty) returns (rpc.Status) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}
