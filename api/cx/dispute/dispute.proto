syntax = "proto3";
package cx.dispute;

import "api/cx/dispute/enums.proto";
import "api/cx/dispute/tree.proto";
import "api/queue/consumer_headers.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "api/typesv2/common/boolean.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/dispute";
option java_package = "com.github.epifi.gamma.api.cx.dispute";

// fields which can be updated in the disputes table
enum DisputeFieldMask {
  DISPUTE_FIELD_MASK_UNSPECIFIED = 0;

  DISPUTE_STATE = 1;

  TRANSACTION_STATUS = 2;

  QUESTIONNAIRE_RESULT = 3;

  TICKET_ID = 4;

  ESCALATION_MODE = 5;

  ESCALATION_TIME = 6;

  REVERSE_PROCESSING_ATTEMPT_COUNT = 7;

  ESCALATION_INFO = 8;
}

message DisputeData {
  string id = 1;
  int64 ticket_id = 2;
  string actor_id = 3 [(validate.rules).string.min_len = 1];
  dispute.DisputeState dispute_state = 4;
  dispute.EscalationMode escalation_mode = 5;
  google.protobuf.Timestamp escalation_time = 6;
  string internal_transaction_id = 7 [(validate.rules).string.min_len = 1];
  dispute.Source source = 8;
  dispute.Channel channel = 9;
  dispute.QuestionnaireResult questionnaire_result = 10;
  dispute.DisputeType dispute_type = 11;
  google.protobuf.Timestamp created_at = 12 [(validate.rules).timestamp.required = true];
  google.protobuf.Timestamp updated_at = 13;
  dispute.TransactionStatus transaction_status = 14;
  DisputeEscalationInfo escalation_info = 15;
  Receiver receiver = 16;
  // field to denote any past status related to dispute creation/escalation/processing/resolution
  // this status is not directly tied with dispute state enum
  dispute.PreviousAttemptStatus previous_attempt_status = 17;
  // this field is updated by reverse processing dispute job
  // this count represents number of retry in ProcessReverseUpdate job of this dispute
  int32 reverse_processing_attempt_count = 18;
}

message DisputeEvent {
  DisputeData dispute_data = 1;

  google.protobuf.Timestamp event_publish_timestamp = 2 [(validate.rules).timestamp.required = true];

  queue.ConsumerRequestHeader consumer_request_header = 3;
}

message DisputeConfig {
  int64 id = 1;
  cx.dispute.Channel channel = 2;
  cx.dispute.Receiver receiver = 3;
  cx.dispute.DisputeType dispute_type = 4;
  cx.dispute.TransactionStatus transaction_status = 5;
  cx.dispute.EscalationMode escalation_mode = 6;
  int32 cool_off_days = 7;
  google.protobuf.Timestamp created_at = 8;
  google.protobuf.Timestamp updated_at = 9;
  google.protobuf.Timestamp deleted_at = 10;
  DisputeConfigVersion config_version = 11;
  string cool_off_duration = 12;
  // decision tree root node id configured against the combination
  string node_id = 13;
  // answer which has to be tied against the node
  string answer = 14;
  // field to denote any past status related to dispute creation/escalation/processing/resolution
  // this status is not directly tied with dispute state enum
  cx.dispute.PreviousAttemptStatus previous_attempt_status = 15;
  // provenance: specifies where txn was done: on-app, off-app, ECOM, POS, etc
  cx.dispute.Provenance provenance = 16;
}

message DisputeMessage {
  string id = 1;
  int64 ticket_id = 2;
  string actor_id = 3;
  cx.dispute.Source source = 4;
  cx.dispute.Channel channel = 5;
  cx.dispute.Receiver receiver = 6;
  cx.dispute.DisputeType dispute_type = 7;
  cx.dispute.TransactionStatus transaction_status = 8;
  cx.dispute.EscalationMode escalation_mode = 9;
  string internal_transaction_id = 10;
  google.protobuf.Timestamp created_at = 11;
  google.protobuf.Timestamp updated_at = 12;
  google.protobuf.Timestamp escalation_time = 13;
  repeated DisputeTicketLog dispute_ticket_logs = 14;
  cx.dispute.DisputeState dispute_state = 15;
  int32 reverse_processing_attempt_count = 16;
  DisputeEscalationInfo escalation_info = 17;
}

message DisputeTicketLog {
  int64 id = 1;
  int64 ticket_id = 2;
  string dispute_id = 3;
  cx.dispute.DisputeTicketState ticket_state = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message DisputeNotificationLog {
  string id = 1;
  string message_id = 2;
  string dispute_id = 3;
  cx.dispute.DisputeNotificationStatus notification_status = 4;
  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
}

message DisputeEscalationInfo {
  UDIRInfo udir_info = 1;
  // Federal bank escalation info received from DMP API
  FederalBankEscalationInfo federal_bank_escalation_info = 2;
}

// proto msg describing all possible filters which can be applied on dispute records
// filters are evaluated as in logical AND fashion on DB
// max record which can be fetched is of 90 days, which is controlled by mandatory date filters, to not hammer db
message DisputeFilters {
  //optional, channel list: UPI, NEFT, etc
  repeated dispute.Channel channel = 1;

  //optional, dispute type list: AUTHORIZED, UNAUTHORIZED, etc
  repeated dispute.DisputeType dispute_type = 2;

  //optional, dispute state list: CREATED, RESOLVED, MANUAL_INTERVENTION, etc
  repeated dispute.DisputeState dispute_state = 3;

  //mandatory, Lower limit of the date to be appiled on created_at >= from_date
  google.protobuf.Timestamp from_date = 4;

  //mandatory, Upper limit of the date to be applied on created_at <= to_date
  google.protobuf.Timestamp to_date = 5;

  //optional, escalation mode list: FRESHDESK, UDIR, etc
  repeated EscalationMode escalation_mode = 6;
}

message UDIRInfo {
  // unique reference number for the complaint/dispute issued from vendor side
  string customer_ref_number = 1;

  // remarks for the complaint adjustment
  string complaint_remarks = 2;

  // action taken on complaint till now.
  UDIRComplaintResponseAction complaint_response_action = 3;

  // reason for action on complaint. It provide the info regarding specific action reason.
  string complaint_response_reason = 4;

  // settlement amount
  google.type.Money sett_amount = 5;

  // original amount
  google.type.Money org_amount = 6;

  // timestamp of complaint action
  google.protobuf.Timestamp complaint_action_timestamp = 7;

  // reference id for complaint action
  string complaint_action_ref_id = 8;

  // complaint dispute state mapping received from pay API's response
  UDIRComplaintDisputeState complaint_dispute_state = 9;
}

// FederalBankEscalationInfo message to be used in DisputeEscalationInfo message
// the goal of this message is to associate dispute case number to internal dispute id in DisputeData message
// this message should not be used outside the above mentioned scope
// please refer FederalDmpDisputeDetails proto message for broader details
message FederalBankEscalationInfo {
  // case number received from Federal bank DMP API
  string dispute_case_number = 1;
}

// Defines the dispute stage to be shown in app
// A stage is nothing but description of an step of the dispute resolution
// along with the status
message DisputeStage {
  enum Status {
    // Default value for the status enum.
    STATUS_UNSPECIFIED = 0;
    // dispute stage is yet to be started
    PENDING = 1;
    // dispute stage has started but yet to be finished
    IN_PROGRESS = 2;
    // dispute stage has finished successfully
    SUCCESS = 3;
    // dispute stage execution has failed
    FAILED = 4;
  }
  Status status = 1;
  // Detailed description of the status in a human understandable language. The
  // client may choose to display this string as-is to the user.
  string localized_description = 2;
  // time of execution of the dispute stage to be displayed to the user.
  // depending on the details available it may be or may not be populated
  google.protobuf.Timestamp execution_time = 3;
  // Text giving more info about a particular stage
  // this will be detailed remarks received from npci for a dispute action
  string detailed_description = 4;
  // field to indicate order of the stage
  // stage with lower value should be shown first in list of dispute stages
  int32 order = 5;
}

// proto msg describing exhaustive set of details of correspondence against a raised dispute
message DisputeCorrespondenceDetails {
  // db record id
  string id = 1;
  // internal id to uniquely identify a raised dispute
  string dispute_id = 2;
  // organisation, ex: Fi, Federal Bank
  Organisation organisation = 3;
  // correspondence text , 1 <= number of characters in correspondence text <= 1500
  string correspondence_text = 4;
  // timestamp at which this row was created in DB
  google.protobuf.Timestamp created_at = 5;
  // timestamp at which this row was last updated
  google.protobuf.Timestamp updated_at = 6;
  // agent email, mail id of agent who sends the correspondence
  string agent_email = 7;
  // denotes if escalation was done by a Fi care agent on given dispute via correspondence
  api.typesv2.common.BooleanEnum is_escalation = 8;
}

// proto msg describing exhaustive set of details of document against a raised dispute
message DisputeDocumentDetails {
  // db record id
  string id = 1;
  // internal id to uniquely identify a raised dispute
  string dispute_id = 2;
  // path where the document is uploaded in s3
  string s3_file_path = 3;
  // email of the agent who uploads the document
  string agent_email = 4;
  // created at
  google.protobuf.Timestamp created_at = 5;
  // updated at
  google.protobuf.Timestamp updated_at = 6;
}

// proto msg describing exhaustive set of details of Federal's DMP system dispute response against a raised dispute
message FederalDmpDisputeDetails {
  // db record id
  string id = 1;
  // internal id to uniquely identify a raised dispute
  string dispute_id = 2;
  // dispute case number uniquely identifies a raised dispute in Federal's DMP system
  string dispute_case_number = 3;
  // this field is used to store raw field values in response to DMP requests
  FederalDmpRawDisputeEvent federal_dmp_raw_dispute_event = 4;
  // created at
  google.protobuf.Timestamp created_at = 5;
  // updated at
  google.protobuf.Timestamp updated_at = 6;
  // work flow status of transaction on which dispute is raised
  // ex: OPEN, CHARGEBACK_REJECTED.
  WorkFlowStatus work_flow_status = 7;
  // comment on current work flow
  string work_flow_comments = 8;
  // order of the workflow status against a transaction on which dispute is raised.
  int32 status_order = 9;
  // transaction number
  string transaction_number = 10;
  // Name of the department that updated the status of transaction on which dispute is raised
  string department = 11;
  // Name of the user that updated the status of transaction on which dispute is raised
  string user_name = 12;
}

// this field is used to store raw values received in response of DMP requests, these are stored for debugging purpose.
// help in debugging in future if against a fields DMP system sends some wrong value whose equivalent enum is not present in our system.
// todo: change this field name in vg-integration proto
message FederalDmpRawDisputeEvent {
  string dispute_status = 1;
  string dispute_case_number = 2;
  string transaction_number = 3;
  string date = 4;
  string work_flow_status = 5;
  string work_flow_comments = 6;
  string user_name = 7;
  string department = 8;
  string status_order = 9;
  string channel = 10;
}

message CorrespondenceFilters {
  // mandatory field to uniquely identify record in disputes table
  string dispute_id = 1;
  // fetch correspondence from given timestamp
  // mandatory
  google.protobuf.Timestamp from_time = 2;
  // fetch correspondence to given timestamp
  // mandatory
  google.protobuf.Timestamp to_time = 3;
  // populate this field if we require only escalated correspondence records
  // optional
  api.typesv2.common.BooleanEnum is_escalated_correspondence = 4;
}

// DisputeIdToTicketIdMapping proto for db table
message DisputeIdToTicketIdMapping {
  // primary identifier for DisputeIdToTicketIdMapping
  string id = 1;
  // id of FreshDesk ticket
  int64 ticket_id = 2;
  // internal id to uniquely identify a raised dispute
  string dispute_id = 3;
  // created at
  google.protobuf.Timestamp created_at = 4;
  // updated at
  google.protobuf.Timestamp updated_at = 5;
}

message File {
  // contain content type of the file
  // ex: csv, text, jpeg
  FileContentType file_content_type = 1;
  // contains file in bytes format
  bytes file_content = 2;
  // name of the file with file extension
  // example: statement.pdf
  string file_name = 3;
}

message S3FileDetails {
  //  pre-signed url, it will expires within a day. means client won't able to download the file from pre signed url after 1 day
  string s3_pre_signed_url = 1;
  // path where the document is uploaded in s3
  string s3_file_path = 2;
}
