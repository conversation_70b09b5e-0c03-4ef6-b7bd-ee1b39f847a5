syntax = "proto3";

package cx.watson;

option go_package = "github.com/epifi/gamma/api/cx/watson";
option java_package = "com.github.epifi.gamma.api.cx.watson";


// EventType is the type of the event being sent to the Watson service
enum EventType {
  EVENT_TYPE_INCIDENT_UNSPECIFIED = 0;
  // for reporting an incident
  EVENT_TYPE_INCIDENT_REPORT = 1;
  // for resolving an incident
  EVENT_TYPE_INCIDENT_RESOLUTION = 2;
}

enum IncidentState {
  INCIDENT_STATE_UNSPECIFIED = 0;
  INCIDENT_STATE_LOGGED_IN_DB = 1;
  // Incident is no longer valid to be processed for the next set of actions e.g. because of delay in consumption
  INCIDENT_STATE_INVALID = 2;
  // Incident is dropped for the next set of actions because of some reason e.g. Incident type rate limit breach.
  INCIDENT_STATE_DROPPED = 3;
  // A Freshdesk ticket is created for an incident
  INCIDENT_STATE_TICKET_CREATED = 4;
  // Communication has been sent to the user regarding identification/creation of an issue in Watson
  INCIDENT_STATE_INCIDENT_CREATION_COMMS_SENT = 5;
  // Incident is resolved, if incident contains ticket that is too resolved
  INCIDENT_STATE_RESOLVED = 6;
  // Incident is resolved, and comms are sent to the user after the resolution is done
  INCIDENT_STATE_INCIDENT_RESOLUTION_COMMS_SENT = 7;
  // Incident will be auto-closed if resolution event is not received in expected time defined by auto-closure-period
  INCIDENT_STATE_AUTO_CLOSED = 8;
}

enum CommsType {
  COMMS_TYPE_UNSPECIFIED = 0;
  COMMS_TYPE_INCIDENT_CREATION = 1;
  // Indicates that the incident was auto-resolved upon the incident resolution event sent by the client service
  // Manually resolved cases do not fall under this type.
  // NOTE: If a ticket is manually resolved, the comms type will be COMMS_TYPE_TICKET_STATUS_CHANGE with status as RESOLVED
  COMMS_TYPE_INCIDENT_RESOLUTION = 2;
  // Indicates that the status of the associated ticket has changed.
  // This comms type will entail metadata which includes details on current status and
  // the serial number for the comms (NOTE: maximum of 3 comms can be configured on a given status with periodic intervals)
  // NOTE: A special case is STATUS_RESOLVED. Watson-Auto-resolved tickets do not fall under this type. For such cases the Comms Type will be COMMS_TYPE_INCIDENT_RESOLUTION
  COMMS_TYPE_TICKET_STATUS_CHANGE = 3;
  // Indicates that the status of the associated manual ticket has changed
  // this comms type will include details on current status and the serial number for the comms sent
  // which will be sent upto a maximum configured limit
  COMMS_TYPE_MANUAL_TICKET_STATUS_CHANGE = 4;
  // Indicates that the sla to resolve of the associated manual ticket has breached
  // this comms type will include details on current status and the serial number for the comms sent
  // which will be sent upto a maximum configured limit
  COMMS_TYPE_MANUAL_TICKET_SLA_BREACHED = 5;
  COMMS_TYPE_INCIDENT_AUTO_CLOSURE = 6;
}

enum IncidentMask {
  INCIDENT_MASK_UNSPECIFIED = 0;
  INCIDENT_MASK_INCIDENT_DATA = 1;
  INCIDENT_MASK_INCIDENT_STATE = 2;
  INCIDENT_MASK_INCIDENT_CATEGORY_ID = 3;
}

enum TicketDetailMask {
  TICKET_DETAIL_MASK_UNSPECIFIED = 0;
  TICKET_DETAIL_MASK_TICKET_STATUS = 1;
  TICKET_DETAIL_MASK_RESOLUTION_TYPE = 2;
}

// NotificationType helps us determine which notification template is to be used while sending comms
enum NotificationType {
  NOTIFICATION_TYPE_UNSPECIFIED = 0;

  // When the ticket is created for incident
  NOTIFICATION_TYPE_INCIDENT_CREATION = 1;

  // When ticket is not created
  NOTIFICATION_TYPE_INCIDENT_CREATION_SHORT = 2;

  // Custom notification type for min-kyc expiry balance refund related incidents
  NOTIFICATION_TYPE_MIN_KYC_EXPIRY_BALANCE_REFUND_INCIDENT_CREATION = 3;
}

// GetTicketDetailsActionType signifies what action Watson will perform after the current GetTicketDetails call
enum GetTicketDetailsActionType {
  GET_TICKET_DETAILS_ACTION_TYPE_UNSPECIFIED = 0;
  GET_TICKET_DETAILS_ACTION_TYPE_CREATE_TICKET_FOR_INCIDENT = 1;
  GET_TICKET_DETAILS_ACTION_TYPE_UPDATE_TICKET_FOR_INCIDENT_RESOLUTION = 2;
}

// TicketEventType defines the ticket events which are of relevance to the Watson system
enum TicketEventType {
  TICKET_EVENT_TYPE_UNSPECIFIED = 0;
  // The status of the ticket has changed
  TICKET_EVENT_TYPE_STATUS_CHANGE = 1;
  // New ticket has been created manually
  TICKET_EVENT_TYPE_MANUAL_TICKET_CREATION = 2;
  // Existing manual ticket has been updated manually
  // this includes sending comms on ticket resolution
  TICKET_EVENT_TYPE_MANUAL_TICKET_UPDATE = 3;
}
