//go:generate gen_queue_pb
syntax = "proto3";

package cx.watson.consumer;

import "api/cx/ticket/ticket.proto";
import "api/cx/watson/enums.proto";
import "api/cx/watson/watson.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/cx/watson/consumer";
option java_package = "com.github.epifi.gamma.api.cx.watson.consumer";

service WatsonConsumerService {
  // Consumer RPC for the queue: cx-watson-incident-reporting-queue
  // Processing the incident event of type report which was published into queue by IngestEvent RPC.
  rpc ProcessReportIncidentEvent (ProcessReportIncidentEventRequest) returns (ProcessReportIncidentEventResponse) {}

  // Consumer RPC for the queue: cx-watson-incident-resolution-queue
  // Processing the incident event of type resolve which was published into queue by IngestEvent RPC.
  rpc ProcessResolveIncidentEvent (ProcessResolveIncidentEventRequest) returns (ProcessResolveIncidentEventResponse) {}

  // Consumer RPC for the queue: cx-watson-ticket-event-queue
  // Processes the ticket events published into queue for ingestion into Watson. eg: signaling the corresponding workflow etc.
  rpc ProcessTicketEventForWatson (ProcessTicketEventForWatsonRequest) returns (ProcessTicketEventForWatsonResponse) {}

  // Consumer RPC for the queue: cx-watson-create-ticket-event-queue
  // Processes the event published into the queue after successful ticket creation
  // This consumer helps Watson update its internal mapping with newly created ticket
  rpc ProcessCreateTicketEvent (ticket.CreateTicketEvent) returns (ProcessCreateTicketEventResponse) {}
}

message ProcessReportIncidentEventRequest {
  queue.ConsumerRequestHeader request_header = 1;
  // Incident that was published in the IngestEvent method
  Incident incident = 2;
}

message ProcessReportIncidentEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessResolveIncidentEventRequest {
  queue.ConsumerRequestHeader request_header = 1;
  // Incident that was published in the IngestEvent method
  Incident incident = 2;
}

message ProcessResolveIncidentEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessTicketEventForWatsonRequest {
  queue.ConsumerRequestHeader request_header = 1;
  // represent the type of the ticket event being ingested into Watson
  TicketEventType ticket_event_type = 2;
  // represents the payload required to ingest the event into Watson
  TicketEventPayload ticket_event_payload = 3;
}

message ProcessTicketEventForWatsonResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

message ProcessCreateTicketEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
