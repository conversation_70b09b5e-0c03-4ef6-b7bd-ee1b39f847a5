syntax = "proto3";

package cx.watson;

import "api/comms/email.proto";
import "api/comms/notification.proto";
import "api/comms/sms.proto";
import "api/comms/whatsapp.proto";
import "api/cx/watson/enums.proto";
import "api/cx/watson/watson.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/cx/watson";
option java_package = "com.github.epifi.gamma.api.cx.watson";

// Request for IsIncidentValid method to be implemented by a Watson client service
message IsIncidentValidRequest {
  IncidentDetailsForClient incident = 1;
}

// Response of IsIncidentValid method to be implemented by a Watson client service
message IsIncidentValidResponse {
  rpc.Status status = 1;
  bool is_incident_valid = 2;
}

// Request for GetTicketDetails method to be implemented by a Watson client service
message GetTicketDetailsRequest {
  IncidentDetailsForClient incident = 1;
  // GetTicketDetailsActionType signifies what action <PERSON> will perform after the current GetTicketDetails call
  GetTicketDetailsActionType action_type = 2;
}

// Response of GetTicketDetails method to be implemented by a Watson client service
message GetTicketDetailsResponse {
  rpc.Status status = 1;
  TicketDetails ticket_details = 2;
}

// Request for IsIncidentResolved method to be implemented by a Watson client service
message IsIncidentResolvedRequest {
  IncidentDetailsForClient incident = 1;
}

// Response of IsIncidentResolved method to be implemented by a Watson client service
message IsIncidentResolvedResponse {
  rpc.Status status = 1;
  bool is_incident_resolved = 2;
}

// Request for GetCommsDetails method to be implemented by a Watson client service
message GetCommsDetailsRequest {
  IncidentDetailsForClient incident = 1;
  // The metadata which may be used by the client to decide what comms to send. It may be dependent on the Comms Type
  CommsMetadataForClient comms_metadata = 2;
  CommsType comms_type = 3;
}

// CommsDetail contains the message sent by client for sending custom comms
message CommsDetail {
  oneof detail {
    comms.SMSMessage sms = 1;
    comms.EmailMessage email = 2;
    comms.NotificationMessage notification = 3;
    comms.WhatsappMessage whatsapp = 4;
  }
}

// Response of GetCommsDetails method to be implemented by a Watson client service
message GetCommsDetailsResponse {
  rpc.Status status = 1;
  repeated CommsDetail comms_details = 2;
}
