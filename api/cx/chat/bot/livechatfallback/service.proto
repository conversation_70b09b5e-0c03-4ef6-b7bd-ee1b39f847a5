syntax = "proto3";

package cx.chat.bot.livechatfallback;

import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/cx/chat/bot/livechatfallback/message.proto";
import "api/cx/chat/bot/livechatfallback/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/cx/chat/bot/livechatfallback";
option java_package = "com.github.epifi.gamma.api.cx.chat.bot.livechatfallback";

// service to receive requests from the Senseforth chat bot for live agent fallback
service LiveChatFallback {
  // API for Senseforth chat bot to initiate a conversation for live agent fallback
  rpc InitiateConversation(InitiateConversationRequest) returns (InitiateConversationResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // API for Senseforth chat bot service to send the user message for fallback to conversation with agent
  rpc PushUserMessage(PushUserMessageRequest) returns (PushUserMessageResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // API for Senseforth chat bot service to notify the user about agent's response when the user is not on the chat window
  rpc NotifyUser(NotifyUserRequest) returns (NotifyUserResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message InitiateConversationRequest {
  // the EpiFi actor_id of the user
  string actor_id = 1;
  // The list of message objects with only message_parts and message_type populated
  repeated Message messages = 2;
}

message InitiateConversationResponse {
  // OK for success
  // Internal for other server errors
  rpc.Status status = 1;
  // Unique identifier of conversation.
  // Will be used by Epifi to send Agent's response to Senseforth chat bot
  string conversation_id = 2;
  // The message objects will be populated with message_id and created_time
  repeated Message messages = 3;
}

message PushUserMessageRequest {
  // the EpiFi actor_id of the user
  string actor_id = 1;
  // conversation_id value that is an attribute of the conversation object returned in response to a successful request to create the conversation.
  string conversation_id = 2;
  // The message object with only message_parts and message_type populated
  Message message = 3;
}

message PushUserMessageResponse {
  // OK for success
  // Internal for other server errors
  rpc.Status status = 1;
  // Unique identifier of conversation.
  // Will be used by Epifi to send Agent's response to Senseforth chat bot
  string conversation_id = 2;
  // The message object will be populated with message_id and created_time
  Message message = 3;
}

message NotifyUserRequest {
  // the EpiFi actor_id of the user (will be used to identify the user to which notification must be sent)
  string actor_id = 1;
  // possible values: conversation_assignment, agent_reply, conversation_resolution
  LiveChatEventType event_type = 2;
  // time of the event in string format
  google.protobuf.Timestamp event_time = 3;
  // data relevant to this event_type
  oneof data {
    // for conversation_assignment event
    ConversationAssignmentEventData conversation_assignment = 4;
    // for agent_reply event
    AgentReplyEventData agent_reply = 5;
    // for conversation_resolution event
    ConversationResolutionEventData conversation_resolution = 6;
  }
}

message NotifyUserResponse {
  // OK for success
  // Internal for other server errors
  rpc.Status status = 1;
}
