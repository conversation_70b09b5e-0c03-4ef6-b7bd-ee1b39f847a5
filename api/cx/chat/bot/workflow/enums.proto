syntax = "proto3";

package cx.chat.bot.workflow;

option go_package = "github.com/epifi/gamma/api/cx/chat/bot/workflow";
option java_package = "com.github.epifi.gamma.api.cx.chat.bot.workflow";

// WorkflowEntity enum denotes which entity has to be fetched from the backend db
enum WorkflowEntity {
  // unspecified
  WORK_FLOW_ENTITY_UNSPECIFIED = 0;
  // faq entity
  WORK_FLOW_ENTITY_FAQ = 1;
  // for relevant user details
  WORKFLOW_ENTITY_USER_DETAILS = 2;
  // for fetching user transactions list
  WORKFLOW_ENTITY_TXN_LIST = 3;
  // for fetching details of a single transaction
  WORKFLOW_ENTITY_TXN_DETAILS = 4;
  // for fetching credit card state
  WORKFLOW_ENTITY_CREDIT_CARD_STATE = 5;
  // for fetching details for debit card tracking
  WORKFLOW_ENTITY_DEBIT_CARD_TRACKING = 6;
  // for fetching credit card txn list
  WORKFLOW_ENTITY_CREDIT_CARD_TXN_LIST = 7;
  // for fetching employment data
  WORKFLOW_ENTITY_EMPLOYMENT_DATA = 8;
  // for fetching screener attempts data
  WORKFLOW_ENTITY_SCREENER_ATTEMPTS = 9;
  // for fetching dispute details for a transaction
  WORKFLOW_ENTITY_DISPUTE_DETAILS = 10;
  // for fetching liveness details
  WORKFLOW_ENTITY_LIVENESS = 11;
  // for fetch reward offers for users
  WORKFLOW_ENTITY_FETCH_REWARD_OFFERS_FOR_USER = 12;
  // for check the eligibility for salary program amazon voucher
  WORKFLOW_ENTITY_CHECK_SALARY_PROGRAM_AMAZON_VOUCHER_ELIGIBILITY = 13;
  // for fetching last n charges debited for an actor
  WORKFLOW_ENTITY_FETCH_CHARGES_FOR_ACTOR = 14;
  // for displaying reason for the given transaction
  WORKFLOW_ENTITY_DISPLAY_TXN_REASON_FOR_ACTOR = 15;
  // for fetching last n failed transactions for an actor
  WORKFLOW_ENTITY_FETCH_FAILED_TRANSACTIONS_FOR_ACTOR = 16;
  // for fetching salary program registration stage status for actor
  WORKFLOW_ENTITY_FETCH_SALARY_PROGRAM_REGISTRATION_DETAILS = 17;
  // for fetch reward event details for reward offer and acotr
  WORKFLOW_ENTITY_FETCH_REWARDS_EVENT_DETAILS = 18;
  // to check the reward for event chosen by user such as txn,fitt,cctxn,referrals
  WORKFLOW_ENTITY_FETCH_REWARD_FOR_EVENT = 19;
  // to refresh balance for user and display the refreshed balance
  WORKFLOW_ENTITY_REFRESH_BALANCE = 20;
  // to check if there is any predefined message template for the user
  WORKFLOW_ENTITY_PREDEFINED_MESSAGE_TEMPLATE = 21;
  // this will fetch transactions for the user
  WORKFLOW_ENTITY_FETCH_USER_TRANSACTIONS = 22;
}

// WorkflowAction enum denotes what execution has to be executed at backend
enum WorkflowAction {
  // unspecified
  WORKFLOW_ACTION_UNSPECIFIED = 0;
  // create a CX support ticket
  WORKFLOW_ACTION_CREATE_TICKET = 1;
}
