syntax = "proto3";

package cx.chat.bot.workflow;

import "api/cx/data_collector/transaction/transactions.proto";
import "api/typesv2/common/boolean.proto";
import "google/type/money.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/cx/chat/bot/workflow";
option java_package = "com.github.epifi.gamma.api.cx.chat.bot.workflow";


// TransactionsData: data returned for User transactions entity
message TxnListData {
  repeated TransactionInfo txn_list = 2;
}

// TransactionsParameters is the input params required to fetch the required user transactions
message TxnListParameters {
  string actor_id = 1;
  // Optional: PaymentProtocol filter to filter order with transactions for specific payment protocol
  // If not passed result with all payment protocols will be returned
  // Payment protocol suggested by decision engine for the transaction.
  repeated string payment_protocols = 2;
  // Optional: OrderProvenance filter to filter order with transactions for specific provenance
  // If not passed result with all provenance types will be returned
  // An order can be created in the system from different entry points.
  // e.g. APP, ATM, POS, etc.
  repeated string provenances = 3;
  // Optional: Transaction Type to filter transactions based on type of issue user is facing with his/her transactions
  // e.g. Failed, Successful, ATM withdrawals, e-NACH charges
  TransactionType txn_type = 4;
}

// TxnDetailsData: data returned for transaction details entity
message TxnDetailsData {
  TransactionInfo txn_info = 2;
}

// TxnDetailsParameters is the input params required to fetch details of a single transaction
message TxnDetailsParameters {
  string actor_id = 1;
  // The display string corresponding txn selected by the user
  // The API call for Txn list entity would have returned this display strings as a list
  // We should extract txn Id from this display string to identify the transaction
  string txn_display_string = 2;
}

message TransactionInfo {
  // internal transaction id, primary key of transactions table
  string txn_id = 1;
  // Unique Transaction Reference for Online Txns like NEFT/IMPS/UPI.
  // A customer gets this id on initiating a transaction and can refer to the same
  // in case of any query regarding his/her transaction.
  // A utr is same across all the parties of a transaction.
  // For NEFT/RTGS/INTRA fund transfer protocols this refers to UTR
  // For IMPS/UPI/CARD fund transfer protocol this refers to RRN
  string utr = 2;
  // Transaction type for example debit or credit
  data_collector.transaction.TransactionType txn_type = 3;
  // amount involved in the transaction
  google.type.Money amount = 4;
  // The status of the transaction
  string txn_status = 5;
  // provenance is the payment mode or order channel
  // e.g. APP, ATM, POS, etc.
  string provenance = 6;
  // Payment protocol used for the transaction.
  // e.g. INTRA_BANK, NEFT, IMPS, UPI etc.
  string payment_protocol = 7;
  // payment instrument details
  data_collector.transaction.PIDetails from_pi_details = 8;
  data_collector.transaction.PIDetails to_pi_details = 9;
  // name of the merchant involved
  // Will be empty if no merchant involved
  string merchant_name = 10;
  // timestamp referring to moment when entry was created in the DB.
  google.protobuf.Timestamp created_at = 11;
  // indicates if the transaction is within TAT
  api.typesv2.common.BooleanEnum is_within_tat = 12;
  // timestamp referring to the moment when a user raised a dispute against a transaction
  // nil/empty timestamp denotes the transaction is not disputed
  google.protobuf.Timestamp disputed_at = 13;
  // The format in which the transaction is to be displayed in chatbot as the txn list to the user
  string display_string = 14;
  // deeplink to the screen which raises a dispute for given transaction
  string raise_dispute_deeplink = 15;
  // describes whether the given transactions is P2P or P2M
  OrderType order_type = 16;
}

// TransactionType determines which type of transactions are to be fetched
enum TransactionType {
  TRANSACTION_TYPE_UNSPECIFIED = 0;
  TRANSACTION_TYPE_SUCCESSFUL = 1;
  TRANSACTION_TYPE_FAILURE = 2;
  TRANSACTION_TYPE_ENACH_CHARGES = 3;
  TRANSACTION_TYPE_ATM_WITHDRAWALS = 4;
  TRANSACTION_TYPE_ALL = 5;
  TRANSACTION_TYPE_CHARGES = 6;
  // type for transactions where we want only those transactions which have been successfully debited from the user account
  TRANSACTION_TYPE_DEBIT_SUCCESSFUL = 7;
  // type for transactions where we want only those transactions which have been successfully credited in the user account
  TRANSACTION_TYPE_CREDIT_SUCCESSFUL = 8;
}

enum OrderType {
  ORDER_TYPE_UNSPECIFIED = 0;
  ORDER_TYPE_P2P = 1;
  ORDER_TYPE_P2M = 2;
}

// ChargesListForActorParams contains parameters needed to be passed to chatbot workflow inorder to extract charges debited for an actor.
message ChargesListForActorParams{
  string session_actor_id = 1;
}

// Its the data returned for charges debited for an actor.
// Since, charges are also a type of transaction which is debited from actor account to a specific vendor account, We are returning
// list of transactions for the charges
message ChargesListForActor {
  repeated cx.chat.bot.workflow.TransactionInfo charges_info_list = 1;
}

// DisplayTxnReasonData: data returned for cx.chat.bot.workflow.WORKFLOW_ENTITY_DISPLAY_TXN_REASON_FOR_ACTOR
message DisplayTxnReasonData {
  string reason_display_string = 1;
}

// DisplayTxnReasonParameters is the input params required to fetch reasons for a single transaction
message DisplayTxnReasonParameters {
  string session_actor_id = 1;
  // The display string corresponding txn selected by the user
  // The API call for Txn list entity would have returned this display strings as a list
  // We should extract txn Id from this display string to identify the transaction
  // Example for display string: "CreatedAt" | "TxnId" | "TxnType" | "Amount" | "PaymentProtocol" | "TxnStatus" | "UserEntityName"
  // Separator "|" can change according to the requirement
  string txn_display_string = 2;
}


// FailedTxnsDataParameters contains parameters needed to be passed to chatbot workflow inorder to extract failed txns for an actor.
message FailedTxnsDataParameters {
  string session_actor_id = 1;
}

// Its the data returned for failed txns for an actor.
message FailedTxnsData {
  repeated cx.chat.bot.workflow.TransactionInfo failed_txns_list = 1;
}
