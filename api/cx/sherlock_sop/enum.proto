syntax = "proto3";

package cx.sherlock_sop;

option go_package = "github.com/epifi/gamma/api/cx/sherlock_sop";
option java_package = "com.github.epifi.gamma.api.cx.sherlock_sop";

// StepComponentType represents the type of step in an SOP

enum StepComponentType {
  STEP_COMPONENT_TYPE_UNSPECIFIED = 0;

  // represent steps in an SOP where questions will be asked
  STEP_COMPONENT_TYPE_QUESTION = 1;

  // represent steps in an SOP where picture guide will be shown
  STEP_COMPONENT_TYPE_PICTURE_GUIDE = 2;

  // represent steps in an SOP which are final steps
  STEP_COMPONENT_TYPE_FINAL_STEP = 3;

  // represent steps in an SOP which are used to serve user info
  STEP_COMPONENT_TYPE_INFO_DISPLAY = 4;

  // represent steps in an SOP where file upload will be needed
  STEP_COMPONENT_TYPE_FILE_UPLOAD = 5;

  // represent steps in an SOP where action by user is needed
  STEP_COMPONENT_TYPE_USER_ACTION = 6;
}

// AnswerType helps <PERSON> understand how to display possible answer types for
// step component. These are defined on CMS and CX contains the logic to parse it
// and present this on sherlock. This at the moment is being used for Questions

enum AnswerType{
  ANSWER_TYPE_UNSPECIFIED = 0;

  // For questions where answers can be presented as radio buttons
  ANSWER_TYPE_RADIO_BUTTON = 1;

  // For questions where answers can be presented as check box
  ANSWER_TYPE_CHECKBOX = 2;

  // For questions where answers can be presented as drop down
  ANSWER_TYPE_DROPDOWN = 3;

  // For questions where answers can be freeform text in nature
  ANSWER_TYPE_FREEFORM = 4;
}


// NextStepConditionType helps people navigate to the next step
// a next step can be of any type: simple, label and complex
enum NextStepConditionType {
  NEXT_STEP_CONDITION_TYPE_UNSPECIFIED = 0;

  // for simple: we just get the node id and traverse to the next node
  NEXT_STEP_CONDITION_TYPE_SIMPLE = 1;

  // for label: we get a key value mapping where key represents condition
  // and value represents the node to which the successful condition evaluation would lead to
  NEXT_STEP_CONDITION_TYPE_LABEL = 2;

  // for complex: these are API calls that needs to be made to understand the decisions to be taken
  // to move to the next step. for example: if we need to perform app version check.
  NEXT_STEP_CONDITION_TYPE_COMPLEX = 3;
}

// MaskingType helps the Sherlock identify if the response value needs some type of masking before displaying to the agent
enum MaskingType {
  MASKING_TYPE_UNSPECIFIED = 0;
  // represents that masking is to be done for a email
  MASKING_TYPE_EMAIL = 1;

  // represents that masking is done starting from the last character
  MASKING_TYPE_TRAILING = 2;

  // represents that masking is done starting from the first character
  MASKING_TYPE_LEADING = 3;

  // represents that masking is done to the full value
  MASKING_TYPE_FULL = 4;

  // represents that masking is done to alternate characters
  MASKING_TYPE_ALTERNATE = 5;
}

enum Operator {
  OPERATOR_UNSPECIFIED = 0;
  OPERATOR_EQUALS = 1;
  OPERATOR_NOT_EQUALS = 2;
  OPERATOR_LESSER_THAN = 3;
  OPERATOR_GREATER_THAN = 4;
  OPERATOR_LESSER_THAN_OR_EQUAL_TO = 5;
  OPERATOR_GREATER_THAN_OR_EQUAL_TO = 6;
  OPERATOR_CONTAINS = 7;
  OPERATOR_BEGINS_WITH = 8;
  OPERATOR_ENDS_WITH = 9;
  OPERATOR_DOES_NOT_CONTAIN = 10;
  OPERATOR_DOES_NOT_BEGIN_WITH = 11;
  OPERATOR_DOES_NOT_END_WITH = 12;
  OPERATOR_IS_NULL = 13;
  OPERATOR_IS_NOT_NULL = 14;
  OPERATOR_IN = 15;
  OPERATOR_NOT_IN = 16;
  OPERATOR_BETWEEN = 17;
  OPERATOR_NOT_BETWEEN = 18;
}

enum Combinator {
  COMBINATOR_UNSPECIFIED = 0;
  COMBINATOR_AND = 1;
  COMBINATOR_OR = 2;
}

enum SearchResultType {
  SEARCH_RESULT_TYPE_UNSPECIFIED = 0;
  SEARCH_RESULT_TYPE_SOP = 1;
  SEARCH_RESULT_TYPE_SCRIPT = 2;
}
