syntax = "proto3";

package cx.sherlock_sop.event;

import "api/queue/consumer_headers.proto";
import "api/cx/sherlock_sop/message.proto";

option go_package = "github.com/epifi/gamma/api/cx/sherlock_sop/event";
option java_package = "com.github.epifi.gamma.api.sherlock_sop.event";

// UpdateSherlockSopBatch contains a batch of sherlock SOPs which need to be indexed
message UpdateSherlockSopBatch {
  queue.ConsumerRequestHeader request_header = 1;
  // list of sherlock scripts to update
  repeated sherlock_sop.SherlockSopFinalStepDetail sherlock_sop_final_step_details = 2;
}
