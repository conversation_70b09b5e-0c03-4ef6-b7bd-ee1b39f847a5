syntax = "proto3";

package cx.sherlock_sop;

option go_package = "github.com/epifi/gamma/api/cx/sherlock_sop";
option java_package = "com.github.epifi.gamma.api.cx.sherlock_sop";

import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/cx/sherlock_sop/sop.proto";
import "api/cx/sherlock_sop/message.proto";
import "api/cx/header.proto";
import "api/typesv2/webui/table.proto";
import "api/cx/sherlock_sop/enum.proto";
import "validate/validate.proto";
import "api/typesv2/webui/detail_grid.proto";


// SherlockSopService exposes all RPCs to facilitate actions related to SOP
service SherlockSopService {

  // GetSherlockSop fetches a SOP from strapi by slug id
  // Given this is an SOP, we will need ticket validation and enrichment here
  // audit logging is not needed for the RPC
  rpc GetSherlockSop(GetSherlockSopRequest) returns (GetSherlockSopResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetNextSopStep fetches a SOP step from strapi using slug id and node id of the step
  // we will need ticket validation and enrichment here
  // audit logging is not needed for the RPC
  rpc GetNextSopStep(GetNextSopStepRequest) returns (GetNextSopStepResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }


  // AddUserResponseForSopStep exposes RPC to persist user response for an SOP to backend.
  // It also contains the logic for publishing the update to a ticket
  rpc AddUserResponseForSopStep(AddUserResponseForSopStepRequest) returns (AddUserResponseForSopStepResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // FetchUserResponseForSopStep exposes RPC to fetch all sops and their responses for a particular ticket.
  // Currently this is not being considered to be V0.
  // TODO: Integration testing with Web, deprio for V0
  rpc FetchUserResponseForSopStep(FetchUserResponseForSopStepRequest) returns (FetchUserResponseForSopStepResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // GetSearchResults is used to fetch search results based on the user query
  rpc GetSearchResults(GetSearchResultsRequest) returns (GetSearchResultsResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // Get SearchResultsMeta fetches result count, results per product category etc
  // GetSearchResult cannot be utilized for this purpose as it is a paginated RPC
  rpc GetSearchResultsMeta(GetSearchResultsMetaRequest) returns (GetSearchResultsMetaResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc GetSherlockSopFinalStepDetailInBulk(GetSherlockSopFinalStepDetailInBulkRequest) returns (GetSherlockSopFinalStepDetailInBulkResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  rpc GetCustomerDetails (GetCustomerDetailsRequest) returns (GetCustomerDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }
}

message GetSherlockSopRequest {
  // slug id to be passed to CMS to fetch the SOP
  string sop_id = 1;
}
message GetSherlockSopResponse {
  rpc.Status status = 1;
  SherlockSop sop = 2;
}

message GetNextSopStepRequest {
  cx.Header header = 1;

  // slug id to be passed to CMS to fetch the SOP
  string sop_id = 2;

  // node id of the step which we want to fetch
  string next_step_id = 3;
}

message GetNextSopStepResponse {
  rpc.Status status = 1;
  SherlockSopStep sherlock_sop_step = 2;
}

message SherlockSopStepUserResponse {
  StepComponentType step_type = 1;
  oneof response {
    QuestionResponse question_response = 2;
    FinalStepResponse final_step_response = 3;
    PictureGuideStepResponse picture_guide_response = 4;
    InfoDisplayStepResponse info_display_response = 5;
  }
}

message AddUserResponseForSopStepRequest {
  cx.Header header = 1;

  // slug id of the sop whose response we are processing
  string sop_id = 2;

  // responses for each of the step involved in the SOP
  repeated SherlockSopStepUserResponse user_response = 3;
}

message AddUserResponseForSopStepResponse {
  rpc.Status status = 1;
}

message FetchUserResponseForSopStepRequest {

  // contains ticketId in header which is used to fetch all responses
  cx.Header header = 1;
}

message SopUserResponse {

  // slug id for which we need to fetch a user response
  string sop_id = 1;

  // response for each step. for example: yes/no for a question with radio button
  // and yes/no as the possible answers.
  repeated SherlockSopStepUserResponse user_response = 2;
}

message FetchUserResponseForSopStepResponse {

  rpc.Status status = 1;

  // the sop and their responses
  // there can be many such SOPs attached to a ticket
  // hence we need an list of such enitites
  repeated SopUserResponse sop_responses = 2;
}

message GetSearchResultsRequest {
  // text query based on which results needs to be fetched
  string query = 1;
  // product category for which results are needed
  string product_category = 2;
  // product category details for which results are needed
  string product_category_details = 3;
  // subcategory for which results are needed
  string sub_category = 4;
  // contains parameters needed to perform pagination
  PaginationParams pagination_params = 5;
  // specifies whether to fetch sops, scripts or all results from elastic query.
  // empty array or null values fetches all results.
  repeated SearchResultType search_result_types = 6;
}

message GetSearchResultsResponse {
  rpc.Status status = 1;
  api.typesv2.webui.Table search_results = 2;
  PaginationParams pagination_params = 3;
}

message GetSearchResultsMetaRequest {
  // text query based on which results needs to be fetched
  string query = 1;
  // product category for which results are needed
  string product_category = 2;
  // product category details for which results are needed
  string product_category_details = 3;
  // subcategory for which results are needed
  string sub_category = 4;
  // specifies whether to fetch sops, scripts or all results from elastic query.
  // empty array or null values fetches all results.
  repeated SearchResultType search_result_types = 5;
}

message GetSearchResultsMetaResponse {
  rpc.Status status = 1;
  // contains the overall count of search results and the count per l1, l2 and l3
  SherlockSopMetaSearchResult sherlock_sop_meta_search_result = 2;
}

message PaginationParams {
  uint64 from = 1;
  uint64 size = 2;
}

message GetSherlockSopFinalStepDetailInBulkRequest {
  PaginationParams pagination_params = 1;
}

message GetSherlockSopFinalStepDetailInBulkResponse {
  rpc.Status status = 1;
  repeated SherlockSopFinalStepDetail sherlock_sop_final_step_details = 2;
  PaginationParams pagination_params = 3;
}

message GetCustomerDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetCustomerDetailsResponse {
  rpc.Status status = 1;
  repeated api.typesv2.webui.DetailView segmented_tabs = 2;
}
