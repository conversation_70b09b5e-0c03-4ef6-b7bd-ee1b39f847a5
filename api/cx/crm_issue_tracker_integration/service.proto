syntax = "proto3";
package cx.crm_issue_tracker_integration;

import "api/cx/crm_issue_tracker_integration/enum.proto";
import "api/cx/crm_issue_tracker_integration/message.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration";
option java_package = "com.github.epifi.gamma.api.cx.crm_issue_tracker_integration";

// Service to integrate the CRM tool used by Fi care with the Issue tracker tools used by Fi Tech team
service CrmIssueTrackerIntegration {
  // RPC to create a ticket on Issue Tracker tool based on the ticket created on CRM tool
  rpc CreateIssueTrackerTicket(CreateIssueTrackerTicketRequest) returns (CreateIssueTrackerTicketResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // RPC to update a ticket on Issue Tracker tool based on the updates to a ticket created on CRM tool
  rpc UpdateIssueTrackerTicket(UpdateIssueTrackerTicketRequest) returns (UpdateIssueTrackerTicketResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // RPC to update a ticket on CRM tool based on the updates to a ticket created on Issue Tracker tool
  rpc UpdateCrmTicket(UpdateCrmTicketRequest) returns (UpdateCrmTicketResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // RPC to append a CRM ticket (which has been identified as a duplicate issue) to a ticket already created on Issue Tracker tool
  rpc AppendCrmTicketToIssueTrackerTicket(AppendCrmTicketToIssueTrackerTicketRequest) returns (AppendCrmTicketToIssueTrackerTicketResponse){
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message CreateIssueTrackerTicketRequest{
  // CRM tool on which the ticket was created
  CrmTool crm_tool = 1;
  // The payload of the ticket created on CRM tool
  CrmTicket crm_ticket = 2;
  // Issue tracker tool on which a new ticket is to be created
  IssueTrackerTool issue_tracker_tool = 3;
}

message CreateIssueTrackerTicketResponse{
  rpc.Status status = 1;
  // The payload of the ticket created on the issue tracker tool
  IssueTrackerTicket issue_tracker_ticket = 2;
}

message UpdateIssueTrackerTicketRequest{
  // CRM tool on which the ticket was updated
  CrmTool crm_tool = 1;
  // The payload of the ticket updated on CRM tool
  CrmTicket crm_ticket = 2;
  // Issue tracker tool on which the ticket is to be updated (based on the mapping already created)
  IssueTrackerTool issue_tracker_tool = 3;
  // Comment or note to be added to the issue tracker ticket
  // Optional: If this field is populated, a comment/note will be added to the Issue tracker ticket
  // Deprecated in favour of notes added inside the CrmTicket object
  string note = 4 [deprecated = true];
}

message UpdateIssueTrackerTicketResponse {
  rpc.Status status = 1;
}

message UpdateCrmTicketRequest{
  // Issue tracker tool on which the ticket was updated
  IssueTrackerTool issue_tracker_tool = 1;
  // The payload of the ticket updated on Issue tracker tool
  IssueTrackerTicket issue_tracker_ticket = 2;
  // CRM tool on which the ticket is to be updated (based on the mapping already created)
  CrmTool crm_tool = 3;
  // Comment or note to be added to the issue tracker ticket
  // Optional: If this field is populated, a note will be added to the CRM ticket
  string note = 4;
  // To check if any ticket attributes are updated or it is just adding a comment
  // Useful to avoid API call, since FD has separate API calls for ticket attribute update and adding a private note
  bool isTicketAttributeUpdate = 5;
}

message UpdateCrmTicketResponse{
  rpc.Status status = 1;
}

message AppendCrmTicketToIssueTrackerTicketRequest{
  // CRM tool to which the duplicate ticket belongs to
  CrmTool crm_tool = 1;
  // The payload of the CRM ticket to be appended
  // This payload should also contain the Issue Tracker ticket ID to which it has to be appended
  CrmTicket crm_ticket = 2;
  // Issue tracker tool to which the CRM ticket has to be appended
  IssueTrackerTool issue_tracker_tool = 3;
}

message AppendCrmTicketToIssueTrackerTicketResponse {
  rpc.Status status = 1;
}
