//go:generate gen_queue_pb
syntax = "proto3";

package cx.crm_issue_tracker_integration.consumer;

import "api/queue/consumer_headers.proto";
import "api/cx/crm_issue_tracker_integration/consumer/events.proto";

option go_package = "github.com/epifi/gamma/api/cx/crm_issue_tracker_integration/consumer";
option java_package = "com.github.epifi.gamma.api.cx.crm_issue_tracker_integration.consumer";

service CrmIssueTrackerIntegrationConsumer {
  // Consumer RPC for the queue: cx-crm-issue-tracker-integration-queue
  // Processes different types of events published
  rpc ProcessCrmIssueTrackerIntegrationEvent (ProcessCrmIssueTrackerIntegrationEventRequest) returns (ProcessCrmIssueTrackerIntegrationEventResponse) {}
}

message ProcessCrmIssueTrackerIntegrationEventRequest {
  queue.ConsumerRequestHeader request_header = 1;
  EventType event_type = 2;
  oneof event_payload {
    FreshdeskTicketChangePayload freshdesk_ticket_change_payload = 3;
    FreshdeskConversationPayload freshdesk_conversation_payload = 4;
    MonorailIssueUpdatesCommentsPayload monorail_issue_updates_comments_payload = 5;
  }
}

message ProcessCrmIssueTrackerIntegrationEventResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
