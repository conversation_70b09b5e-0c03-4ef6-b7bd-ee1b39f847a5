syntax = "proto3";
package cx.sherlock_feedback;

import "api/cx/sherlock_feedback/sherlock_feedback.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/rpc/page.proto";
import "api/typesv2/common/boolean.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/sherlock_feedback";
option java_package = "com.github.epifi.gamma.api.cx.sherlock_feedback";

// SherlockFeedback service to record, retrieve the agent feedback
service SherlockFeedback {
  // rpc to record the agent feedback
  // request accepts mandatory cx header and feedback details
  // response contains rpc status
  // OK if call is successful
  // InvalidArg if mandatory param is missing
  // ISE for any other errors
  rpc SubmitFeedback(SubmitFeedbackRequest) returns (SubmitFeedbackResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // rpc to get the agent feedbacks from db
  // request accepts mandatory cx header, page context request and record filter fields
  // records can filter on feedback category, from date, to date, etc
  // feedback category, from date and to date is mandatory filter field,
  // max request page size is 20
  // response contains rpc status, sherlock feedbacks and page context response
  // rpc status
  // OK with result populated
  // Invalid Argument if any mandatory req field is missing
  // NotFound is call records are not found
  // ISE for any other errors
  rpc GetFeedback(GetFeedbackRequest) returns (GetFeedbackResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message SubmitFeedbackRequest {
  // header is mandatory
  // retrieve agent email from header
  cx.Header header = 1 [(validate.rules).message.required = true];

  // mandatory: sherlock feedback details
  SherlockFeedbackDetails sherlock_feedback_details = 2 [(validate.rules).message.required = true];

  // agent want feedback to be anonymous
  // hash agent mail
  api.typesv2.common.BooleanEnum is_feedback_anonymous = 4;
}

message SubmitFeedbackResponse {
  // mandatory rpc status
  rpc.Status status = 1;
}

message GetFeedbackRequest {
  // header is mandatory
  // retrieve actor id from header
  cx.Header header = 1 [(validate.rules).message.required = true];

  // Mandatory feedback record filters proto msg
  SherlockFeedbackFilters filters = 2 [(validate.rules).message.required = true];

  // Mandatory pagination request field
  // Max page size is 20
  rpc.PageContextRequest page_context_request = 3 [(validate.rules).message.required = true];
}

message GetFeedbackResponse {
  // mandatory rpc status
  rpc.Status status = 1;

  //  list of agent feedbacks
  repeated SherlockFeedbackDetails sherlock_feedback_details = 2;

  // page context response
  rpc.PageContextResponse page_context_response = 3;
}
