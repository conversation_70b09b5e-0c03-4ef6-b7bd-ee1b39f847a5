syntax = "proto3";
package cx.sherlock_feedback;

import "google/protobuf/timestamp.proto";
import "api/typesv2/common/boolean.proto";
import "api/cx/sherlock_feedback/enums.proto";

option go_package = "github.com/epifi/gamma/api/cx/sherlock_feedback";
option java_package = "com.github.epifi.gamma.api.cx.sherlock_feedback";

// proto msg describing exhaustive set of details of feedback submitted by agent
message SherlockFeedbackDetails {
  // db record id
  string id = 1;
  // describe the type of feedback
  FeedbackCategory feedback_category = 2;
  //  actual feedback
  string feedback_message = 3;
  // feedback meta data has one to one mapping with feedback category.
  FeedbackMetaData  feedback_meta_data = 4;
  // do not have to populate this in request
  // agent who submits the feedback
  string agent_email = 5;
  //  what type of feedback identifier it is: ticket_id
  FeedbackIdentifierType feedback_identifier_type = 6;
  // have one to one mapping with feedback_identifier_type
  // contain the value of a particular identifier type
  string feedback_identifier_value = 7;
  // the time at which we got feedback and recorded into the db
  google.protobuf.Timestamp created_at = 8;
  // last time when we updated this feedback in db
  google.protobuf.Timestamp updated_at = 9;
}


// proto msg describing exhaustive set of secondary details of feedback submitted by agent
message FeedbackMetaData {
  // describes the frequency of issue reported by agent
  FeedbackFrequency frequency = 3;
  // describes if agent highlighted current feedback/issue before
  api.typesv2.common.BooleanEnum is_highlighted_before = 4;
  // tell if the current feedback/issue is urgent or not
  api.typesv2.common.BooleanEnum is_urgent = 5;
}

// Proto message for backend to not expose model in DAO methods
message SherlockFeedbackMetaData {
  // db record id
  string id = 1;
  // feedback id
  string feedback_id = 2;
  // describes the frequency of issue reported by agent
  FeedbackFrequency frequency = 3;
  // describes if agent highlighted current feedback/issue before
  api.typesv2.common.BooleanEnum is_highlighted_before = 4;
  // tell if the current feedback/issue is urgent or not
  api.typesv2.common.BooleanEnum is_urgent = 5;
  // the time at which we got feedback meta data and recorded into the db
  google.protobuf.Timestamp created_at = 6;
  // last time when we updated this feedback meta data in db
  google.protobuf.Timestamp updated_at = 7;
}

message SherlockFeedbackFilters {
  // Mandatory: describe the type of feedback given by agent
  FeedbackCategory feedback_category = 1;
  // Mandatory: will get all feedbacks who,s createdAt >= from_date
  google.protobuf.Timestamp from_date = 2;
  // Mandatory: will get all feedbacks who,s createdAt <= to_date
  google.protobuf.Timestamp to_date = 3;
  // agent mail who submits the feedback
  string agent_email = 4;
  // feedback meta data has one to one mapping with feedback category.
  FeedbackMetaData  feedback_meta_data = 5;
  //  what type of feedback identifier it is: ticket_id
  FeedbackIdentifierType feedback_identifier_type = 6;
  // have one to one mapping with feedback_identifier_type
  // contain the value of a particular identifier type
  string feedback_identifier_value = 7;
}
