syntax = "proto3";
package cx.call_ivr;

import "api/cx/call_ivr/enums.proto";
import "api/cx/call_ivr/message.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/phone_number.proto";

option go_package = "github.com/epifi/gamma/api/cx/call_ivr";
option java_package = "com.github.epifi.gamma.api.cx.call_ivr";

service Ivr {
  // InitiateIvr is supposed to be called by vendor after receiving version = 1 in the response of GetRoutingChannel RPC of CX CallRouting service,
  // vendor is supposed to call this RPC to get the details of initial IVR type, question and IVR state that the user will be served with
  rpc InitiateIvr(InitiateIvrRequest) returns (InitiateIvrResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // ProcessIvrUserInput is used to provide the next combination of IVR details
  // that will be served to the user based on the various parameters and user input provided for the previous question in the request
  rpc ProcessIvrUserInput (ProcessIvrUserInputRequest) returns (ProcessIvrUserInputResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // GetIvrState will be used by the vendor to poll for latest ivr state, question id to be served to a user
  // In case there is no state available, this RPC is supposed to return not found
  // For example : In case of Auth IVR, if the user is yet to click on auth notification/email then not found will be returned
  // Otherwise based on user action, authenticated or non-authenticated based IVR question can be provided
  rpc GetIvrState (GetIvrStateRequest) returns (GetIvrStateResponse) {
    option (cx.auth_required) = false;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}

message InitiateIvrRequest {
  // monitor ucid of the call for which IVR is to initiated
  string monitor_ucid = 1;
  // phone number of the user for which IVR is to be initiated
  api.typesv2.common.PhoneNumber phone_number = 2;
}

message InitiateIvrResponse {
  rpc.Status status = 1;
  // info regarding the current IVR question like question id, IVR type, IVR state etc
  IvrStateInfo ivr_state_info = 2;
}

message ProcessIvrUserInputRequest {
  // phone number of the user that provided IVR input
  api.typesv2.common.PhoneNumber phone_number = 1;
  // monitor ucid of the call for which response was provided
  string monitor_ucid = 2;
  // IVR type for which response was provided
  IvrType current_ivr_type = 3;
  // IVR question id for which response was provided
  int32 current_ivr_question_id = 4;
  // Response provided by the user
  string current_ivr_question_response = 5;
}

message ProcessIvrUserInputResponse {
  rpc.Status status = 1;
  // info regarding the current IVR question like question id, IVR type, IVR state etc
  IvrStateInfo ivr_state_info = 2;
}

message GetIvrStateRequest {
  // phone number of the user for which IVR state is needed
  api.typesv2.common.PhoneNumber phone_number = 1;
  // monitor ucid of the call for which IVR state is needed
  string monitor_ucid = 2;
  // current IVR type that the user is answering
  IvrType current_ivr_type = 3;
  // current question id
  int32 current_ivr_question_id = 4;
}

message GetIvrStateResponse {
  rpc.Status status = 1;
  // info regarding the current IVR question like question id, IVR type, IVR state etc
  IvrStateInfo ivr_state_info = 2;
}
