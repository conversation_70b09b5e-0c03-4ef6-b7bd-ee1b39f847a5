syntax = "proto3";

package cx.data_collector.wealth_onboarding;

import "api/typesv2/address.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/common/name.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/wealth_onboarding";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.wealth_onboarding";

message CustomerProfileDetails {
  api.typesv2.common.Name name = 1;
  api.typesv2.Gender gender = 2;
  string phone_number = 3;
  string email = 4;
  message BankDetails {
    string account_number = 1;
    string ifsc_code = 2;
    string account_type = 3;
    string bank_name = 4;
    string branch_name = 5;
    string bank_city = 6;
  }
  BankDetails bank_details = 5;
  repeated api.typesv2.AddressWithType address_with_type = 6;
}
