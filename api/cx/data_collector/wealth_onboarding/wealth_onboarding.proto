syntax = "proto3";

package cx.data_collector.wealth_onboarding;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/wealth_onboarding";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.wealth_onboarding";

message StageDetails {
  // Status of the stage
  string status = 1;
  // Order of execution
  uint32 order = 2;
  // Description
  string description = 3;
  // Updated time
  google.protobuf.Timestamp updated_at = 4;
  // json with additional meta data
  string meta_data = 5;
}

message CurrentStageTroubleShootingDetails {
  // The stage the user is stuck on
  string stage = 1;
  // stage status
  string status = 2;
  // time at which the state was last updated
  google.protobuf.Timestamp updated_at = 3;

  // 3 levels of root cause analysis for display
  string L1 = 4;
  string L2 = 5;
  string L3 = 6;

  // troubleshooting advice if available
  string advice = 7;

  // json blob contain additional details
  string additional_details = 8;
}

message OnboardingDetails {
  // overall status
  string status = 1;
  // current stage details and troubleshooting advice
  CurrentStageTroubleShootingDetails current_stage_trouble_shooting_details = 2;
  // details of each stage
  map<string, StageDetails> stage_details_mapping = 3;
}

// OnboardingType determines the phase of onboarding in wealth
enum OnboardingType {
  ONBOARDING_TYPE_UNSPECIFIED = 0;
  ONBOARDING_TYPE_WEALTH = 1;
  ONBOARDING_TYPE_PRE_INVESTMENT = 2;
}
