syntax = "proto3";

package cx.data_collector.wealth_onboarding;

import "api/cx/customer_auth/customer_auth.proto";
import "api/cx/data_collector/wealth_onboarding/wealth_onboarding.proto";
import "api/cx/data_collector/wealth_onboarding/profile.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/boolean.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/wealth_onboarding";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.wealth_onboarding";

service WealthOnboarding {
  // RPC to fetch wealth onboarding details
  // Request accepts cx header and onboarding type (wealth/pre-investment)
  // Response contains current stage status and troubleshooting details and details of all the previous stages
  // If authorization is required, response contains sherlock deeplink to auth page
  // If successful, status will be OK with onboarding details populated
  // If actorId is empty, status will be Invalid Argument
  // If onboarding details are not found for actorId, status will be Record Not Found
  // Status will be Internal Server Error for other issues
  rpc GetOnboardingDetails(GetOnboardingDetailsRequest) returns (GetOnboardingDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // RPC to fetch Investment ready status
  // Request accepts cx header
  // Response contains bool representing whether investment ready or not
  // If authorization is required, response contains sherlock deeplink to auth page
  // If successful, status will be OK with Investment status
  // If actorId is empty, status will be Invalid Argument
  // If onboarding details are not found for actorId, status will be Record Not Found
  // Status will be Internal Server Error for other issues
  rpc GetIsInvestmentReady(GetIsInvestmentReadyRequest) returns (GetIsInvestmentReadyResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
  // RPC to fetch customer details
  // Request accepts cx header
  // Response contains all the customer details required for profile on sherlock
  // If authorization is required, response contains sherlock deeplink to auth page
  // If successful, status will be OK with customer details populated
  // If actorId is empty, status will be Invalid Argument
  // If onboarding details are not found for actorId, status will be Record Not Found
  // Status will be Internal Server Error for other issues
  rpc GetCustomerProfileDetails(GetCustomerProfileDetailsRequest) returns (GetCustomerProfileDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.information_level_for_rpc) = "LESS_SENSITIVE";
  }
}

message GetOnboardingDetailsRequest {
  // ticket id is mandatory in header
  cx.Header header = 1 [(validate.rules).message.required = true];
  // onboarding type
  OnboardingType onboarding_type = 2;
}

message GetOnboardingDetailsResponse {
  rpc.Status status = 1;

  // onboarding details
  OnboardingDetails onboarding_details = 2;

  customer_auth.SherlockDeepLink sherlock_deep_link = 3;
}

message GetIsInvestmentReadyRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetIsInvestmentReadyResponse {
  rpc.Status status = 1;
  api.typesv2.common.BooleanEnum IsInvestmentReady = 2;
  customer_auth.SherlockDeepLink sherlock_deep_link = 3;
}

message GetCustomerProfileDetailsRequest {
  // ticket id is mandatory in header
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetCustomerProfileDetailsResponse {
  rpc.Status status = 1;

  // customer profile details
  wealth_onboarding.CustomerProfileDetails customer_details = 2;

  customer_auth.SherlockDeepLink sherlock_deep_link = 3;
}
