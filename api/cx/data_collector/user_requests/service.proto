syntax = "proto3";

package cx.data_collector.user_requests;

import "api/cx/customer_auth/customer_auth.proto";
import "api/cx/data_collector/user_requests/account_statements.proto";
import "api/cx/data_collector/user_requests/enums.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/rpc/status.proto";
import "api/typesv2/webui/inputs.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/user_requests";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.user_requests";

// A service to cater to the requests made by customers(/users) through sherlock
service UserRequests {
  // RPC to get details which needs to be populated on UI for the Account Statement form on the Sherlock User Requests tab
  // Ref: https://whimsical.com/account-statement-cta-agent-view-sherlock-7doXd7zSkiyvpqqqMSSPXF
  // Using information level as HIGHLY_SENSITIVE even for the form details to keep in sync with SendAccountStatement because,
  // there is no point in showing the form before authenticating customer to the required level for sending account statement.
  rpc GetAccountStatementFormDetails (GetAccountStatementFormDetailsRequest) returns (GetAccountStatementFormDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }
  // RPC to send account statement to the user.
  // Takes account type as the input, and sends statement for this account to the user's registered email Id
  // Returns RPC Status OK, if the statement is sent successfully. Otherwise respective error status
  // The Sherlock UI is expected to
  //      1. Clear the form if Status is OK.
  //      2. Give retry option to the Agent if the Status is not OK.
  rpc SendAccountStatement (SendAccountStatementRequest) returns (SendAccountStatementResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "MODERATELY_SENSITIVE";
  }

  // GetAgentPrompts RPC returns a list of prompts agent can send to users
  // ticket id is required in request header
  // response contains list of strings which represents type of prompts
  rpc GetAgentPrompts (GetAgentPromptsRequest) returns (GetAgentPromptsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "LESS_SENSITIVE";
  }

  // SendPrompt RPC sends a prompt notification to the user for selected type
  // Request requires prompt type selected by agent, and ticketId populated in header
  // Response contains status code and a display message which should be presented to agent
  rpc SendPrompt (SendPromptRequest) returns (SendPromptResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "LESS_SENSITIVE";
  }
}

message GetAccountStatementFormDetailsRequest {
  // ticket_id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
}

message GetAccountStatementFormDetailsResponse {
  rpc.Status status = 1;
  // Deeplink to redirect the agent to customer auth flow, if the required customer auth is not satisfied
  customer_auth.SherlockDeepLink sherlock_deep_link = 2;
  // list of account types to be displayed in dropdown
  repeated string account_types = 3;
  // Map of available accounts to be shown on the Sherlock UI
  // key will be string representing account type, from the above list
  // value is account details for that type
  // The agent will be able to choose the account type from dropdown but won't be able to edit account details
  map<string, Account> account_type_to_details_map = 4;
  // The DatePicker input for start time field on the UI
  // The agent is allowed to change this field,
  api.typesv2.webui.DateTimePicker start_time = 5;
  // The DatePicker input for end time field on the UI
  // The agent is allowed to change this field,
  api.typesv2.webui.DateTimePicker end_time = 6;
}

message SendAccountStatementRequest {
  // ticket_id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
  // [MANDATORY] The type of the account for which the statement is to be sent to the customer's registered email Id
  AccountType account_type = 2;
  // [MANDATORY] The start date/time for the Account statement
  google.protobuf.Timestamp start_time = 3;
  // [MANDATORY] The end date/time for the Account statement
  google.protobuf.Timestamp end_time = 4;
  // [MANDATORY] The reason for sending account statement as populated by the Agent on Sherlock UI
  string reason = 5;
}

message SendAccountStatementResponse {
  // The Sherlock UI is expected to
  //      1. Reset the form if Status is OK (to avoid clicking twice).
  //      2. Give retry option to the Agent if the Status is not OK.
  rpc.Status status = 1;
  // The message to be displayed to the Agent on sherlock UI. It can be success message or error message
  string status_message = 2;
  // Deeplink to redirect the agent to customer auth flow, if the required customer auth is not satisfied
  customer_auth.SherlockDeepLink sherlock_deep_link = 3;
}

message GetAgentPromptsRequest {
  // ticket_id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
}
message GetAgentPromptsResponse {
  rpc.Status status = 1;
  // list representing all type of prompts agent can send
  repeated string agent_prompts = 2;
  // description to be shown when a prompt is selected by the agent
  // prompt value acts as a key to get the corresponding description
  map<string, string> prompt_description = 3;
}

message SendPromptRequest {
  // ticket_id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];
  // prompt type which is selected by agent, this is mandatory field
  // this field is kept as string and not an enum
  // because we sent agent understandable string to Sherlock, and we expect the same here
  string prompt_type = 2;
}
message SendPromptResponse {
  rpc.Status status = 1;
  // message to be displayed to agents to give more understanding about the response, and next steps
  // Few example of display messages:
  // Notification send successfully please wait for few seconds
  // Error while sending notification please retry
  string message = 2;
}
