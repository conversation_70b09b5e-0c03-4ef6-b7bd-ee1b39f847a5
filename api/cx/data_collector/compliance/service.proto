syntax = "proto3";
package cx.data_collector.compliance;

import "api/bankcust/compliance/service.proto";
import "api/cx/header.proto";
import "api/cx/method_options.proto";
import "api/cx/customer_auth/customer_auth.proto";
import "api/rpc/status.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/compliance";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.compliance";

service Compliance {
  // RPC to fetch periodic kyc details for a user
  // actor id is mandatory in request
  // INTERNAL SERVER ERROR : any error in system eg. service not reachable, etc
  // INVALID ARGUMENT ERROR : if request does not contain mandatory fields
  rpc GetPeriodicKYCDetails (GetPeriodicKYCDetailsRequest) returns (GetPeriodicKYCDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = true;
    option (cx.ticket_validation_required) = true;
    option (cx.is_enrichment_required) = true;
    option (cx.information_level_for_rpc) = "LESS_SENSITIVE";
  }
}

message GetPeriodicKYCDetailsRequest {
  // agent email and access token are mandatory
  // ticket id is mandatory
  cx.Header header = 1 [(validate.rules).message.required = true];

  string actor_id = 2;
}

message GetPeriodicKYCDetailsResponse {
  rpc.Status status = 1;
  repeated .compliance.PeriodicKYCDetail periodic_k_y_c_details = 2;
  customer_auth.SherlockDeepLink sherlock_deep_link = 3;
}
