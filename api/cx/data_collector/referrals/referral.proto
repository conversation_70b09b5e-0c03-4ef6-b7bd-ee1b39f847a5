syntax = "proto3";

package cx.data_collector.referrals;

import "api/rewards/reward.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/phone_number.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/referrals";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.referrals";

message ReferralDetails {
  // finite code ID
  string finite_code_id = 1;
  // name of the referrer who gave the finite code to the referee
  api.typesv2.common.Name referrer_name = 2;
  // channel of the finite code
  string finite_code_channel = 3;
  // type of the finite code
  string finite_code_type = 4;
  // id of the finite code claim record in case the user onboarded using finite code
  string finite_code_claim_id = 5;

  // description of qualifying event for user to receive the referral reward
  // Ex: Add Rs. 1000 in 7 days
  string qualifying_event_description = 6;

  // boolean to indicate if user has passed the qualifying event and is eligible for rewards
  api.typesv2.common.BooleanEnum is_qualifying_event_passed = 7;

  // actual finite code used while onboarding
  string finite_code = 8;
}

message RewardDetails {
  // indicated if user earned an referral reward or not
  api.typesv2.common.BooleanEnum reward_earned = 1;
  // current status of reward
  string reward_state = 2;

  google.protobuf.Timestamp created_at = 3;

  string reward_type = 4;

  oneof option {
    .rewards.Cash cash = 5;
    .rewards.FiCoins fi_coins = 6;
    .rewards.LuckyDraw lucky_draw = 7;
    .rewards.SmartDeposit smart_deposit = 8;
    .rewards.GiftHamper gift_hamper = 9;
  }
}

message Referee {
  // Title of the item
  string referee_name = 1;
  // Subtitle of the item
  string referee_onboarding_status = 2;

  string actor_id = 3;

  string finite_code_used = 4;
}

message ReferrerDetails {
  api.typesv2.common.BooleanEnum is_eligible_for_referral = 1;

  // list of active finite codes for the referrer.
  // this will be nil in case the referrer is not eligible for referral program.
  repeated string finite_codes = 2;

  int64 total_claim_count = 3;
}

message ReferrerRewardSummary {
  // total cash reward amount earned
  google.type.Money total_cash_reward_earned = 2;
  // total sid reward amount earned
  google.type.Money total_sid_reward_earned = 3;
  // total fi coins earned
  int32 total_fi_coins_earned = 4;
  // total cash amount that is in_processing state.
  // i.e claimed but haven't been credited yet.
  google.type.Money total_in_processing_cash_reward_amount = 5;
  // total sid amount that is in_processing state.
  // i.e claimed but haven't been credited yet.
  google.type.Money total_in_processing_sid_reward_amount = 6;
  // total fi coins that are in_processing
  // i.e claimed but haven't been credited yet.
  int32 total_in_processing_fi_coins = 7;

  // count of rewards
  int32 total_count_of_rewards = 8;

  // count of cash rewards
  int32 total_count_of_cash_rewards = 9;
  // count of sd rewards
  int32 total_count_of_sid_rewards = 10;
  // count of fi coin rewards
  int32 total_count_of_fi_coin_rewards = 11;
}

message RefereeDetails {
  api.typesv2.common.PhoneNumber phone_number = 1;

  // description of qualifying event for user to receive the referral reward
  // Ex: Add Rs. 1000 in 7 days
  string qualifying_event_description = 2;

  // boolean to indicate if user has passed the qualifying event and is eligible for rewards
  api.typesv2.common.BooleanEnum is_qualifying_event_passed = 3;

  string current_onboarding_stage = 4;

  RewardDetails reward_details = 5;

  // finite code claim id
  string finite_code_claim_id = 10;
}
