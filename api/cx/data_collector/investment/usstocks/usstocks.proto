syntax = "proto3";

package cx.data_collector.investment.usstocks;

import "api/typesv2/common/image.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/investment/usstocks";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.investment.usstocks";


// InvestedAsset denotes the details of invested fund for a particular asset
message InvestedAsset {
  // eg. google, amazon
  string asset_name = 1;
  // eg. open/closed
  string asset_state = 2;
  // eg. ETF/Stock
  string asset_type = 3;
  string auto_invest_details = 4;
  google.protobuf.Timestamp first_transaction_at = 5;
  google.protobuf.Timestamp last_transaction_at = 6;
  // represent quantity of stock that user can not sell
  double sell_lock_units = 7;
}

message OrderDetails {
  // eg. order_id/nta_id
  string activity_id = 1;
  // eg. google
  string asset_name = 2;
  google.protobuf.Timestamp created_at = 3;
  google.protobuf.Timestamp updated_at = 4;
  // eg. $100.45
  google.type.Money value_of_activity = 5;
  // hold units of particular asset by user, eg. 143.13
  double units = 6;
  // eg. buy/sell/auto_invest_buy
  string activity_type = 7;
  string current_status = 8;
  // forex rate
  google.type.Money fx_rate = 9 [deprecated = true];
  string cbs_number = 10 [deprecated = true];
  string fi_external_txn_number = 11 [deprecated = true];
  // GST(tax) for the foreign fund transfer
  google.type.Money gst_paid = 12 [deprecated = true];
  // TCS(tax) for the foreign fund transfer, this is the amount charged by govt
  google.type.Money tcs_paid = 13 [deprecated = true];
  // is_sell_lock represent if any sell_lock_units is present or not
  bool is_sell_lock = 14 [deprecated = true];
  // represent auto payment or sip by user
  string auto_invest_details = 15;
}

message AccountStageInfo {
  string current_stage = 1;
  string current_status = 2;
  google.protobuf.Timestamp updated_at = 3;
  string description = 4;
  string meta_information = 5;
}

message OrderStepDetails {
  google.protobuf.Timestamp timestamp = 1;
  string timeline_step = 2;
  string step_status = 3;
  string internal_status = 4;
  string failure_reason = 5;
  google.protobuf.Timestamp eta = 6;
}

enum UsStocksActivityType {
  US_STOCKS_ACTIVITY_TYPE_UNSPECIFIED = 0;
  US_STOCKS_ACTIVITY_TYPE_AUTO_INVEST_BUY = 1;
  US_STOCKS_ACTIVITY_TYPE_BUY = 2;
  US_STOCKS_ACTIVITY_TYPE_SELL = 3;
  US_STOCKS_ACTIVITY_TYPE_DIVIDEND_OR_REG_FEE = 4;
}

enum ReviewPayloadType {
  REVIEW_PAYLOAD_TYPE_UNSPECIFIED = 0;
  REVIEW_PAYLOAD_TYPE_PAN_IMAGE = 1;
}

// data sent to sherlock for the agent to view
message ReviewElement {
  // primary id of the review item in manual review table to be passed along when agent approves or rejects
  string review_id = 1;
  oneof payload {
    PanReviewElement pan_review_element = 2;
  }
}

// basic information to be displayed in the list view of multiple review items
message PanReviewElement {
  string actor_id = 1;
  // review item creation timestamp
  google.protobuf.Timestamp created_at = 2;
}

// detailed information to be displayed while reviewing individual item
message PanReviewDetails {
  // primary id of the review item in manual review table to be passed along when agent approves or rejects
  string review_id = 1;
  string name = 2;
  api.typesv2.common.Image pan_image = 3;
  api.typesv2.common.Image liveness_image = 4;
  google.type.Date dob = 5;
  string pan_number = 6;
  float face_match_score = 7;
  double ocr_score = 8;
}

message PanReviewActionDetails {
  ReviewAction review_action = 1;
  ReviewReason review_reason = 2;
  string reviewer_email = 3;
}

// action taken by reviewing agent
enum ReviewAction {
  REVIEW_ACTION_UNSPECIFIED = 0;
  REVIEW_ACTION_APPROVED = 1;
  REVIEW_ACTION_REJECTED = 2;
}

// reason for approval or rejection of the review item
enum ReviewReason {
  REVIEW_REASON_UNSPECIFIED = 0;
  REVIEW_REASON_ALL_DETAILS_MATCHING = 1;
  REVIEW_REASON_NOT_PAN_IMAGE = 2;
  REVIEW_REASON_IMAGE_NOT_CLEAR = 3;
  REVIEW_REASON_DOB_INCORRECT = 4;
  REVIEW_REASON_PAN_NUMBER_INCORRECT = 5;
  REVIEW_REASON_USER_PHOTO_NOT_CLEAR_ON_PAN = 6;
  REVIEW_REASON_USER_PHOTO_ON_PAN_NOT_MATCHING = 7;
}
