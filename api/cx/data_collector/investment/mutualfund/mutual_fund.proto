syntax = "proto3";
package cx.data_collector.investment.mutualfund;

import "google/protobuf/timestamp.proto";
import "api/cx/data_collector/investment/mutualfund/enums.proto";

option go_package = "github.com/epifi/gamma/api/cx/data_collector/investment/mutualfund";
option java_package = "com.github.epifi.gamma.api.cx.data_collector.investment.mutualfund";


// InvestedFund denotes the details of the mutual fund a user has invested in via various transaction modes supported.
message InvestedFund {
  // name of mutual fund
  string mutual_fund_name = 1;
  // asset class of a mutual fund (Equity fund/debt fund/cash fund/hybrid fund)
  AssetClass asset_class = 2;
  // comma seperated fit rule subscription statements
  // eg. Invest 500 daily, Invest 2500 weekly on Fri
  string auto_invest_details = 3;
  // last date on which the order was placed for this fund
  google.protobuf.Timestamp last_transaction_date = 4;
  // if FI is allowing investment in the fund
  MutualFundInternalStatus fund_status = 5;
  // mutual fund id is a unique identifier for a mutual fund
  string mutual_fund_id = 6;
  // first date on which the order was placed for this fund
  google.protobuf.Timestamp first_transaction_date = 7;
}


// FundOrder denotes the details of given orders placed by the user.
message FundOrder{
  google.protobuf.Timestamp created_at = 1;
  google.protobuf.Timestamp updated_at = 2;
  double units = 3;
  string folio_number = 4;
  TransactionType transaction_type = 5;
  OrderStatus activity_status = 6;
  string utr_number = 7;
  // to be used to display as order_id
  string external_order_id = 8;
  string vendor_order_id = 9;
  // order_id will not be displayed to user
  // order_id will be used for communication with back-end
  string order_id = 10;
  string amount = 11;
  string rta_confirmed_amount = 12;
  // estimated date for order completion for pending orders
  // an order is considered as completed if it is in confirmed_by_rta, settled or failure
  // we will send date in "Date Month Year" format. for eg: 02 January 2006
  string estimated_completion_date = 13;
}


message OrderDetails {
  repeated OrderStatusUpdate order_timeline = 1;
}

// OrderStatusUpdate denotes the order state and the timestamp when order reached that status
message OrderStatusUpdate {
  google.protobuf.Timestamp timestamp = 1;
  OrderStatus order_status = 2 [deprecated = true];
  // displayed_status is state of order shown to the user on mobile application
  // Investment Request ,Payment received ,Fund House Processing ,Unit Allocation... etc
  string timeline_step = 3;
  // success, pending, failed
  string step_status = 5;
  // corresponding to order state machine of mf orders
  string internal_status = 6;
  // failure reason for order
  string order_failure_reason = 4;
}
