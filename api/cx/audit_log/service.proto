syntax = "proto3";
package cx.audit_log;

import "api/cx/method_options.proto";
import "api/cx/audit_log/audit_log.proto";
import "api/rpc/status.proto";
import "api/cx/header.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/cx/audit_log";
option java_package = "com.github.epifi.gamma.api.cx.audit_log";

message GetAuditLogsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];

  // these are set of filter that could be applied to fetch audit logs
  // you can set multiple filters to get result for combination of those attribute values
  // to get audit logs of a specific agent
  string agent_email = 2;

  // to get audit logs of a specific ticket
  int64 ticket_id = 3;

  // to get audit logs for a specific object
  Object object = 4;

  // to get audit logs for a specific action
  Action action = 5;

  // to get audit logs with given access_status
  AccessStatus access_status = 6;

  // We can have forward compatibility when afterPageToken is passed and backward compatibility when before token is
  // passed.
  oneof token {
    // before token is to be passed if client wants to fetch events for the previous page.
    cx.PageToken before_page_token = 7;

    // after token is to be passed if client wants to fetch events for the next page.
    cx.PageToken after_page_token = 8;
  }
  // to get audit logs for a specific range of ticket id
  // this range cannot be greater than 50
  int64 from_ticket_id = 9;
  int64 to_ticket_id = 10;

  // number of logs required, cannot be greater than 50 and by default 10 is used
  int64 response_limit = 11;
}

message GetAuditLogsResponse {
  rpc.Status status = 1;

  repeated AuditLog audit_logs_list = 2;

  // before token is to be passed if client wants to fetch events for the previous page.
  cx.PageToken before_page_token = 3;

  // after token is to be passed if client wants to fetch events for the next page.
  cx.PageToken after_page_token = 4;

  int64 limit = 5;
}

message GetAuditLogDetailsRequest {
  cx.Header header = 1 [(validate.rules).message.required = true];
  // audit log id
  string id = 2;
}

message GetAuditLogDetailsResponse {
  rpc.Status status = 1;

  AuditLog audit_log = 2;
}

service AuditLogs {

  // rpc to get list of audit logs in cx
  // will return paginated data
  // returns a list of audit logs
  // returns total count of audit logs in system
  // total count will be used for pagination
  // you can pass request parameters in request to filter the audit logs
  // returns empty list if no matching audit logs are present
  // the max number of records in the response will be equal to the limit given in the request
  rpc GetAuditLogs(GetAuditLogsRequest) returns (GetAuditLogsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }

  // returns audit log details for the given id
  // returns error if audit log not found with given id
  rpc GetAuditLogDetails(GetAuditLogDetailsRequest) returns (GetAuditLogDetailsResponse) {
    option (cx.auth_required) = true;
    option (cx.logging_required) = false;
    option (cx.ticket_validation_required) = false;
    option (cx.is_enrichment_required) = false;
    option (cx.information_level_for_rpc) = "INSENSITIVE";
  }
}
