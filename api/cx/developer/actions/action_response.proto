syntax = "proto3";
package cx.developer.actions;

import "api/cx/developer/actions/enums.proto";
import "api/cx/escalations/messages.proto";

option go_package = "github.com/epifi/gamma/api/cx/developer/actions";
option java_package = "com.github.epifi.gamma.api.cx.developer.actions";

message ActionResponse {
  // returned response type
  ActionResponseType action_response_type = 1;

  oneof action_response {
    // response will be shown as raw string on UI as result
    string string_resp = 2;
    // response json will be beautified and shown on UI
    // will show error if this field is not a valid json
    string json_resp = 3;
    // response type for this action_res should be Table
    // will show the response in html table on UI
    HtmlTable html_table = 4;
    // response type for this action_res should be DOWNLOAD_URL
    // will directly download the file from the given URL on action completion
    string download_url = 5;
  }
}

message HtmlTable {
  TableRow col_names = 1;
  repeated TableRow rows = 2;
  int32 total_cols = 3;
  int32 total_rows = 4;
}

message TableRow {
  repeated string values = 1;
}

message EscalationEntityResponse{
  repeated escalations.Escalation escalations = 1;
}

message EscalationUpdateEntityResponse{
  repeated escalations.EscalationUpdate escalation_updates = 1;
}

message EscalationAttachmentResponse {
  repeated escalations.EscalationAttachment attachments = 1;
}
