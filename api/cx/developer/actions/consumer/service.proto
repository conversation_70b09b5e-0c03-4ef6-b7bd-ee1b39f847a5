syntax = "proto3";
package cx.developer.actions.consumer;

import "api/queue/consumer_headers.proto";
import "api/cx/developer/actions/consumer/enums.proto";

option go_package = "github.com/epifi/gamma/api/cx/developer/actions/consumer";
option java_package = "com.github.epifi.gamma.api.cx.developer.actions.consumer";

service DevActionsConsumer {
  // Consumer RPC which consumes messages from dev action event queue
  // This will expect event type and event meta to be passed in request
  //  
  // IMPORTANT : Method options are not needed here since the consumer service is registered to queue not server
  rpc ProcessDelayedAction(ProcessDelayedActionRequest) returns (ProcessDelayedActionResponse) {}
}

message ProcessDelayedActionRequest {
  queue.ConsumerRequestHeader consumer_request_header = 1;

  DevActionEventType event_type = 2;

  oneof event_meta {
    FreshdeskTicketContactUpdateMeta freshdesk_ticket_contact_update_meta = 3;
  }
}

message FreshdeskTicketContactUpdateMeta {
  int64 ticket_id = 1;
}

message ProcessDelayedActionResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
