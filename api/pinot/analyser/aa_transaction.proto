syntax = "proto3";
package analyser;

import "api/accounts/account_type.proto";
import "api/order/payment/accounting_entry_type.proto";
import "api/order/payment/payment_protocol.proto";
import "api/payment_instruments/payment_instrument.proto";
import "api/categorizer/enums.proto";
import "api/typesv2/actor.proto";
import "api/typesv2/bank.proto";


option go_package = "github.com/epifi/gamma/api/pinot/analyser";
option java_package = "com.github.epifi.gamma.api.pinot.analyser";

message AATransaction {
  // unique identifier for AA transactions
  string transaction_id = 1 [json_name = "transaction_id"];

  // actor who has given consent for AA.
  string actor_id = 2 [json_name = "actor_id"];

  // payment instrument of actor
  string pi = 3 [json_name = "pi"];

  // payment instrument used by actor
  paymentinstrument.PaymentInstrumentType pi_type = 4 [json_name = "pi_type"];

  // connected account bank involved in the transaction
  api.typesv2.Bank bank = 5 [json_name = "bank"];

  // account id of the actor involved in the transaction
  string account_id = 6 [json_name = "account_id"];

  // Type of the account eg. savings, current etc.
  accounts.Type account_type = 7 [json_name = "account_type"];

  // other actor with which transaction was done.
  string with_actor_id = 8 [json_name = "with_actor_id"];

  // payment instrument used by with_actor
  string with_pi = 9 [json_name = "with_pi"];

  // with_pi instrument type
  paymentinstrument.PaymentInstrumentType with_pi_type = 10 [json_name = "with_pi_type"];

  // merchant name if with_actor is a merchant
  string with_merchant_name = 11 [json_name = "with_merchant_name"];

  // merchant id if with_actor is a merchant
  string with_merchant_id = 12 [json_name = "with_merchant_id"];

  // amount involved in the transaction
  // amount will be represented in lower denominations e.g., INR 23.23 will be represented as 2323
  int64 amount = 13 [json_name = "amount"];

  // Transactions can be carried out through different means such as DEBIT_CARD, UPI, etc.
  order.payment.PaymentProtocol payment_protocol = 14 [json_name = "payment_protocol"];

  // type of the transaction like credit/debit.
  // for CREDIT txns, with_actor is paying and actor is beneficiary
  // for DEBIT txns, actor is paying and with_actor is beneficiary
  order.payment.AccountingEntryType transaction_type = 15 [json_name = "transaction_type"];

  // ontology ids of the transaction from ds categoriser
  repeated string ontology_ids = 16 [json_name = "ontology_ids"];

  repeated categorizer.DisplayCategory display_categories = 17 [json_name = "display_categories"];

  // internal transaction id sent by connected account service
  string aa_txn_id = 18 [json_name = "aa_txn_id"];

  // timestamp referring to moment when amount was debited at bank's end.
  int64 executed_at_unix = 19 [json_name = "executed_at_unix"];

  // 12 am IST timestamp on the IST date of the transaction
  int64 executed_at_date_unix_ist = 20 [json_name = "executed_at_date_unix_ist"];

  // time of creation of the transaction
  int64 created_at_unix = 21 [json_name = "created_at_unix"];

  // machine timestamp when this record was enriched
  int64 view_updated_at_unix = 22 [json_name = "view_updated_at_unix"];

  // l0 ontologies of the transaction from ds categoriser
  // It allows filtering over broad category groups such as Spends, Investments etc
  repeated categorizer.L0 l0_ontologies = 23 [json_name = "l0_ontologies"];

  // with actor type (entity on other side of transaction)
  api.typesv2.Actor.Type with_actor_type = 25 [json_name = "with_actor_type"];

  // if the with_actor_type is MERCHANT/EXTERNAL_MERCHANT then with_derived_entity_id is to_merchant_id (entity_id) else if
  // with_actor_type is USER/EXTERNAL_USER then its value is to_actor_id
  string with_derived_entity_id = 27 [json_name = "with_derived_entity_id"];
}
