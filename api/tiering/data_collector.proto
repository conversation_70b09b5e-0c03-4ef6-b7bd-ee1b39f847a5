syntax = "proto3";

package tiering;

import "api/kyc/kyc.proto";
import "api/salaryprogram/activation_history.proto";
import "api/salaryprogram/enums/salary_band.proto";
import "api/tiering/enums/enums.proto";
import "api/tiering/internal/actor_tier_info.proto";
import "api/typesv2/common/boolean.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/tiering";
option java_package = "com.github.epifi.gamma.api.tiering";

// For all new QC, include data response type
// And also a new value to be added in QualifyingCriteriaType enum
message CollectedData {
  oneof data {
    BalanceData balance_data = 1;
    KycData kyc_data = 2;
    SalaryData salary_data = 3;
    BaseTierData base_tier_data = 4;
    UsStocksData us_stocks_data = 5;
    DepositsData deposits_data = 6;
    TrialData trial_data = 7;
  }
}

// Data collected from savings service
message BalanceData {
  // current balance of user
  google.type.Money available_balance = 1;
  // Ledger balance of the account (Opening balance on the given day)
  google.type.Money ledger_balance = 2;
  // Timestamp for which the given balance was calculated
  google.protobuf.Timestamp balance_at = 3;
}

// Data collected from kyc service
message KycData {
  // KYC done by user can either be min kyc or full kyc.
  kyc.KYCLevel kyc_level = 1;
  kyc.KycProvider kyc_provider = 2;
  // To identify the type of KYC in progress
  kyc.KycType kyc_type = 3;
  // This field is used by caller to evaluate the next action for the client.
  kyc.KycStatus kyc_status = 4;
}

// Data collected from salary program service
message SalaryData {
  // boolean field to indicate if the user is salaried or not
  api.typesv2.common.BooleanEnum is_salaried = 1 [deprecated = true];
  // min amount required for salary txn detection.
  google.type.Money min_req_amount_for_salary = 2 [deprecated = true];
  // salary activation type of the user
  salaryprogram.SalaryActivationType salary_activation_type = 3;
  // salary band of the user
  // deprecated since b2c salary program is sunset, use b2b_salary_band for b2b users
  salaryprogram.enums.SalaryBand salary_band = 4 [deprecated = true];
  // flag to indicate if the user is a b2b user
  bool b2b_salary_program_verification_status = 5;
  // b2b salary band of the user
  salaryprogram.enums.B2BSalaryBand b2b_salary_band = 6;
}

message BaseTierData {
  // base tier of the user
  enums.Tier actor_base_tier = 1;
}

message UsStocksData {
  // amount added in us stocks in last 30 days
  google.type.Money wallet_add_funds_value_in_l30d = 1;
}

message DepositsData {
  // total SD and FD amount in user's account
  google.type.Money total_deposits = 1;
}

message SegmentData {
  bool should_exclude_salary_b2c_criteria = 1;
  bool should_exclude_aa_salary_criteria = 2;
}

message TrialData {
  TrialDetails trial_details = 1;
}

message GatherDataResponse {
  BalanceData balance_data = 1;
  KycData kyc_data = 2;
  SalaryData salary_data = 3;
  BaseTierData base_tier_data = 4;
  UsStocksData us_stocks_data = 5;
  DepositsData deposits_data = 6;
  SegmentData segment_data = 7;
  TrialData trial_data = 8;
}

// Deprecated: in favor of EvaluatorMeta
message TierOptionType {
  enums.Tier tier = 1;
  enums.CriteriaOptionType criteria_option_type = 2;
}
