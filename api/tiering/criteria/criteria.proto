syntax = "proto3";

package tiering.criteria;

import "api/kyc/kyc.proto";
import "api/salaryprogram/activation_history.proto";
import "api/salaryprogram/enums/salary_band.proto";
import "api/tiering/enums/enums.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/tiering/criteria";
option java_package = "com.github.epifi.gamma.api.tiering.criteria";

// qualifying criteria for a tier to satisfy
message QualifyingCriteria {
  oneof criteria {
    Balance balance = 1;
    Salary salary = 2;
    Kyc kyc = 3;
    BalanceV2 balance_v2 = 4;
    BaseTier base_tier = 5;
    UsStocksSip us_stocks_sip = 6;
    Deposits deposits = 7;
    Salary aa_salary = 8;
    BalanceTrial balance_trial = 9;
  }
}

message UsStocksSip {
  google.type.Money min_wallet_add_funds = 1;
}

message Deposits {
  google.type.Money min_deposits_amount = 1;
}

// Balance Qualifying Criterion
message Balance {
  // minimum savings account balance needed to qualify
  google.type.Money min_balance = 1;
  // maximum savings account balance needed to qualify
  google.type.Money max_balance = 2;
}

message BalanceV2 {
  google.type.Money min_balance_for_upgrade = 1;
  google.type.Money min_balance_to_prevent_downgrade = 2;
}

// Salary Qualifying Criterion
message Salary {
  // minimum amount of salary needed to qualify
  google.type.Money min_salary = 1 [deprecated = true];
  // specifies which salary activation type needed to qualify for salary tier
  salaryprogram.SalaryActivationType salary_activation_type = 2;
  // specifies which salary band type needed for corresponding AA salary tier type
  salaryprogram.enums.SalaryBand salary_band = 3;
  bool b2b_salary_program_verification_status = 4;
  // specifies which salary band type needed for corresponding B2B salary tier type
  salaryprogram.enums.B2BSalaryBand b2b_salary_band = 5;
}

// KYC Qualifying Criterion
message Kyc {
  // whether FULL_KYC needed or not to qualify
  kyc.KYCLevel kyc_level = 1;
}

message BaseTier {
  enums.Tier base_tier = 1;
}

message BalanceTrial {
  // reduced balance threshold for being in trial period
  google.type.Money min_balance_for_trial = 1;
}

message MovementDetails {
  // Tier to which movement is being considered
  // In case this is same as current tier, it contains retention details
  enums.Tier tier_name = 1;
  // Set of options - on meeting even one of which, movement(/retention) will occur
  repeated Option options = 2;
}

message Option {
  // Identifier for option derived based on action types
  enums.CriteriaOptionType criteria_option_type = 1;
  // Set of actions - on meeting all of which, an option will be satisfied
  repeated Action actions = 3;
}

message Action {
  // Action details related to a particular action
  // Can be one of Kyc info, balance info, etc.
  tiering.criteria.QualifyingCriteria action_details = 1;
}
