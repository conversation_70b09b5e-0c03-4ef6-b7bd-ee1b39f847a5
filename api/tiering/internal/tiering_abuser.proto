//go:generate gen_sql -types=TierInfo
syntax = "proto3";

package tiering;

import "api/tiering/enums/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/tiering";
option java_package = "com.github.epifi.gamma.api.tiering";

// Represents a single tier abuse entry
message TierAbuseEntry {
  // The tier that was abused
  enums.Tier tier = 1;
  // The criteria that was abused (e.g., "MONTHLY_BALANCE_CRITERIA", "KYC_CRITERIA")
  string tier_criteria = 2;
}

// TierInfo contains information related to the tier abuse, for e.g. tier and criteria.
message TierInfo {
  // List of tier abuse entries
  repeated TierAbuseEntry entries = 1;
}

// Table to store information about users who have abused tiering criteria in specific months.
// This allows the system to track and prevent repeat abusers from gaming the tiering system.
message TieringAbuser {
  // Auto generated uuid
  string id = 1;
  // ID of the user who abused tiering criteria
  string actor_id = 2;
  // Month and year when the abuse occurred, stored as timestamp for easy extraction
  google.protobuf.Timestamp month = 3;
  // Tier abuse information for this user in this month
  TierInfo tier_info = 4;
  // Timestamp when the record was created
  google.protobuf.Timestamp created_at = 5;
  // Timestamp when the record was last updated
  google.protobuf.Timestamp updated_at = 6;
  // Timestamp when the record was soft deleted, NULL for active records
  google.protobuf.Timestamp deleted_at = 7;
}

// TieringAbuserFieldMask is the enum representation of all the tiering abuser fields.
// Meant to be used as field mask
enum TieringAbuserFieldMask {
  TIERING_ABUSER_FIELD_MASK_UNSPECIFIED = 0;
  TIERING_ABUSER_FIELD_MASK_ID = 1;
  TIERING_ABUSER_FIELD_MASK_ACTOR_ID = 2;
  TIERING_ABUSER_FIELD_MASK_MONTH = 3;
  TIERING_ABUSER_FIELD_MASK_TIER_INFO = 4;
  TIERING_ABUSER_FIELD_MASK_CREATED_AT = 5;
  TIERING_ABUSER_FIELD_MASK_UPDATED_AT = 6;
  TIERING_ABUSER_FIELD_MASK_DELETED_AT = 7;
}
