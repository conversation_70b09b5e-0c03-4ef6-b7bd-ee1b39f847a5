//go:generate gen_sql -types=EligibilityDetails,EvaluatorMeta
syntax = "proto3";

package tiering;

import "api/tiering/enums/enums.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/tiering";
option java_package = "com.github.epifi.gamma.api.tiering";

message EligibleTierMovement {
  // Auto generated uuid
  string id = 1;
  // Foreign key to tiering_criteria (id)
  string criteria_reference_id = 2;
  // tier movement from
  enums.Tier from_tier = 3;
  // tier movement to
  enums.Tier to_tier = 4;
  // actor_id for which movement happened
  string actor_id = 5;
  // Enum to represent tier movement type
  enums.TierMovementType movement_type = 6;
  // timestamp at which movement happened
  google.protobuf.Timestamp movement_timestamp = 7;
  // Enum to represent eligible tier movement status
  enums.EligibleTierMovementStatus status = 8;
  // Timestamp at which criteria created
  google.protobuf.Timestamp created_at = 9;
  // Timestamp at which criteria updated
  google.protobuf.Timestamp updated_at = 10;
  // Timestamp of deletion of record
  google.protobuf.Timestamp deleted_at = 11;
  // Eligibility details to store grace period, cool off period, etc.
  EligibilityDetails details = 12;
  // Enum to represent criteria option type
  // eg: BALANCE_AND_KYC, SALARY_AND_KYC etc
  enums.CriteriaOptionType criteria_option_type = 13;
  // Meta data for the evaluator
  EvaluatorMeta evaluator_meta = 14;
}

message EvaluatorMeta {
  enums.Tier evaluated_tier = 1;
  // list of all criteria satisfied by the actor
  repeated enums.CriteriaOptionType satisfied_criteria_option_types = 2;
  // highest priority criteria satisfied by the actor
  enums.CriteriaOptionType high_priority_criteria_option_type = 3;
}

message EligibilityDetails {
  oneof Details {
    CooloffDetails cooloff_details = 1;
    GraceDetails grace_details = 2;
  }
  // This will be populated if any ETM gets invalidated and new EMT gets created based on the old ETM
  // For example: Cx agent takes an action to override grace/cool off
  // Since old ETM marked as MOC, this will act as ref to old ETM
  // Can be empty if ETM is not related to any other ETMs
  // Note that this ref_id won't have a hard constraint check and to be only used by analytics/POC purposes
  string etm_ref_id = 3;
  // A list of reference IDs related to criteria changes that triggered the creation of this EligibleTierMovement record.
  repeated string criteria_change_ref_ids = 4;
}

// Details for cool off
// Stores window size and movements done in the window size due to which the actor is in cool off
// Ex - If an actor has done 5 movements in 60 days,
// window_size_in_days : 60 (depreacted), movements_done : 5
// update window_size : stores window size (duration)
message CooloffDetails {
  // Deprecated, do not use
  int64 window_size_in_days = 1 [deprecated = true];
  int64 movements_done = 2;
  double window_size = 3;
}

// Details for grace period
// Stores no. of days allotted in the grace period and the position in the present grace ladder allotted
// Ex - If an actor is allotted 7 days grace period and the ladder is [10,7,3,1],
// period_in_days : 7 (deprecated), grace_ladder_position : 2
// period : stores grace period (duration)
message GraceDetails {
  // Deprecated, do not use
  int64 period_in_days = 1 [deprecated = true];
  int64 grace_ladder_position = 2;
  double period = 3;
  double silent_grace_period = 4;
  // flag to indicate whether grace notification is sent or not
  bool is_notification_sent = 5;
  // flag to indicate if this is an eligible movement of a rewards abuser
  bool is_a_rewards_abuser_eligible_movement = 6;
}

// EligibleTierMovementFieldMask is the enum representation of all the eligible tier movements fields.
// Meant to be used as field mask
enum EligibleTierMovementFieldMask {
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_UNSPECIFIED = 0;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_ID = 1;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_CRITERIA_REFERENCE_ID = 2;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_FROM_TIER = 3;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_TO_TIER = 4;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_ACTOR_ID = 5;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_MOVEMENT_TYPE = 6;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_MOVEMENT_TIMESTAMP = 7;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_STATUS = 8;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_CREATED_AT = 9;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_UPDATED_AT = 10;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_DELETED_AT = 11;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_DETAILS = 12;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_CRITERIA_OPTION_TYPE = 13;
  ELIGIBLE_TIER_MOVEMENT_FIELD_MASK_EVALUATOR_META = 14;
}
