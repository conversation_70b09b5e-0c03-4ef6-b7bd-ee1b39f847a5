// protolint:disable MAX_LINE_LENGTH

syntax = "proto3";

package docs;

import "api/rpc/status.proto";
import "api/docs/templates.proto";

option go_package = "github.com/epifi/gamma/api/docs";
option java_package = "com.github.epifi.gamma.api.docs";

// Docs service provides rpc to create different types of docs file ex. PDF file etc.
service Docs {
  // GeneratePdf RPC will generate pdf file for requested template and data.
  // It will return aws url of the file with expiry time if provided.
  // Requested file will be password protected if user password is provided in request
  // otherwise file will not protected by password.
  rpc GeneratePdf(GeneratePdfRequest) returns (GeneratePdfResponse) {};
  // By use of GeneratePdfWithStream rpc we can generate pdf for more then 4MB data
  // here we send request into streaming format
  rpc GeneratePdfWithStream(stream GeneratePdfWithStreamRequest) returns (GeneratePdfResponse) {};
}

message GeneratePdfRequest {
  // Template to use for building pdf file.
  docs.PDFTemplate pdf_template = 1;

  // data for building template. It should be json format bytes of data.
  // If there is no data require for PDF rendering pass bytes of curly braces string i.e. []bytes("{}")
  bytes data = 2;

  // owner password to protect file from change, copy etc.
  // User will require owner password to make any change in file.
  // If left blank no owner password will be set.
  // Alphanumeric with space not allowed.
  string owner_password = 3;

  // password to protect file to view.
  // If not provided (i.e. blank string is passed), file will not be password protected.
  // Alphanumeric with space not allowed.
  string user_password = 4;

  // time in second after which aws url in response will expire.
  // if no expiry time is provided (i.e. expiry_time = 0) then default expiry time of one week will be set.
  int64 expiry_time_in_seconds = 5;

  // File will be created using this name. This should be unique name for each file generated
  // otherwise it will fail.
  string file_name = 6;

  // File name prefix will be used to categorised files. All file will get created in s3 bucket with this prefix.
  // Ex. for prefix 'statement' -> file will be created with 'statement/fileName.pdf'
  // Based on prefix different deletion policy will be applied.
  // For example: all file with prefix 'statement/somefile.pdf' will get automatically
  // deleted after one day to adhere compliance.
  docs.FileNamePrefix file_name_prefix = 7;
}

message GeneratePdfResponse {
  enum Status {
    OK = 0;

    // Invalid argument passed in the request.
    // i.e. data passed in request to generate pdf is not as per required by the template.
    INVALID_ARGUMENT = 3;

    // internal error
    INTERNAL = 13;
  }
  rpc.Status status = 1;

  // aws url of generated file.
  string file_url = 2;
}

enum FileNamePrefix {
  FILE_NAME_PREFIX_UNSPECIFIED = 0;
  // file prefix for statement files
  STATEMENT = 1;
  // file prefix for wealth kra form files
  KRA_FORM = 2;
  // file prefix for mutual funds nft files.
  MUTUAL_FUNDS_NFT = 3;
  // file prefix for federal bank A2 Form files
  FEDERAL_BANK_A2_FORM = 4;
  // file prefix for savings account summary files
  SAVINGS_ACCOUNT_SUMMARY = 5;
}

message GeneratePdfWithStreamRequest {
  bytes file_chunk = 1;
}
