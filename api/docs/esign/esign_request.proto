//go:generate gen_sql -types=EsignRequest
syntax = "proto3";

package docs.esign;

import "api/docs/esign/enums.proto";
import "api/typesv2/esign/enums.proto";
import "api/typesv2/esign/templates.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/docs/esign";
option java_package = "com.github.epifi.gamma.api.docs.esign";

// Table to keep a track of any esign related requests
message EsignRequest {
  string id = 1;
  string actor_id = 2;
  string vendor_document_id = 3; // Document Id returned by vendor
  string client_request_id = 4; // Id generated from Client, sent to Esign
  EsignRequestClient client = 5; // Client/Service/Caller which called Esign
  string irn = 6; // Internal Reference Number, generated and sent from Esign to Vendor
  string sign_url = 7; // document url to send to vendor
  EsignRequestStatus status = 8;
  EsignRequestVendor vendor = 9;
  api.typesv2.esign.TemplateType template_type = 10;
  api.typesv2.esign.TemplateOptions template_option = 11;
  google.protobuf.Timestamp signed_at = 12;
  google.protobuf.Timestamp expiry_at = 13;
  google.protobuf.Timestamp created_at = 14;
  google.protobuf.Timestamp updated_at = 15;
  google.protobuf.Timestamp deleted_at = 16;
}
