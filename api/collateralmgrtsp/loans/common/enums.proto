//go:generate gen_sql -types=LoanProgram,LoanOfferType
syntax = "proto3";

package collateralmgrtsp.loans.common;

option go_package = "github.com/epifi/gamma/api/collateralmgrtsp/loans/common";
option java_package = "com.github.epifi.gamma.api.collateralmgrtsp.loans.common";

enum LoanProgram {
  LOAN_PROGRAM_UNSPECIFIED = 0;
  // Loan against mutual funds
  LOAN_PROGRAM_LAMF = 1;
}

enum LoanOfferType {
  LOAN_OFFER_TYPE_UNSPECIFIED = 0;
  LOAN_OFFER_TYPE_SOFT_OFFER = 1;
  LOAN_OFFER_TYPE_FINAL_OFFER = 2;
}
