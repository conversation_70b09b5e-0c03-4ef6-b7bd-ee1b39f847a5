//go:generate gen_sql -types=Loan<PERSON>ffer,OfferProcessingInfo,RangeData,MutualFundCollateralDetails,AmountRange,IntRange
syntax = "proto3";

package collateralmgrtsp.loans.common;

import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/collateralmgrtsp/loans/common";
option java_package = "com.github.epifi.gamma.api.collateralmgrtsp.loans.common";

message LoanOffer {
  AmountRange principle = 1;
  IntRange tenure_in_months = 2;
  OfferProcessingInfo processing_info = 3;
  oneof collateral_details {
    MutualFundCollateralDetails mutual_funds = 4;
  }
}

message OfferProcessingInfo {
  double gst = 1;
  repeated RangeData interest_rate = 2;
  repeated RangeData processing_fee = 3;
}


message RangeData {
  AmountRange principle_range = 1;
  oneof value {
    double percentage = 2;
    google.type.Money amount = 3;
  }
  oneof max_value {
    google.type.Money max_amount = 4;
  }
}

message MutualFundCollateralDetails {
  google.type.Money current_balance = 1;
  repeated AllowedFundDetails allowed_funds = 2;
  repeated RejectedFundDetails rejected_funds = 3;
  message AllowedFundDetails {
    string isin = 1;
    double units = 2;
    double nav = 3;
    double market_value = 4;
    double credit_allowed = 5;
    double ltv = 6;
  }
  message RejectedFundDetails {
    string isin = 1;
    double units = 2;
  }
}

message AmountRange {
  google.type.Money min = 1;
  google.type.Money max = 2;
}

message IntRange {
  int64 min = 1;
  int64 max = 2;
}


