syntax = "proto3";

package collateralmgrtsp.loans.loanapplication;

import "api/collateralmgrtsp/common/request_header.proto";
import "api/collateralmgrtsp/loans/common/enums.proto";
import "api/collateralmgrtsp/loans/common/loan_offer.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/collateralmgrtsp/loans/loanapplication";
option java_package = "com.github.epifi.gamma.api.collateralmgrtsp.loans.loanapplication";

service LoanApplication {
  rpc GenerateLoanOffer(GenerateLoanOfferRequest) returns (GenerateLoanOfferResponse) {};
}

message GenerateLoanOfferRequest {
  collateralmgrtsp.common.RequestHeader header = 1;
  string customer_id = 2;
  common.LoanProgram loan_program = 3;
  common.LoanOfferType offer_type = 4;
  // list of assets to be kept as collateral
  repeated string asset_ids = 5;
}

message GenerateLoanOfferResponse {
  rpc.Status status = 1;
  string offer_id = 2;
  common.LoanOffer offer = 3;
}
