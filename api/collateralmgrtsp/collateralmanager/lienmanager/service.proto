syntax = "proto3";

package collateralmgrtsp.collateralmanager.lienmanager;

import "api/collateralmgrtsp/common/request_header.proto";
import "api/collateralmgrtsp/common/enums.proto";
import "api/collateralmgrtsp/collateralmanager/common/enums.proto";
import "api/collateralmgrtsp/collateralmanager/model/collateral.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/collateralmgrtsp/collateralmanager/lienmanager";
option java_package = "com.github.epifi.gamma.api.collateralmgrtsp.collateralmanager.lienmanager";

service LienManager {
  /*
  InitiateMarkLienOnCollateral initiates lien marking process on user's assets. It is a batch rpc, clients can place multiple
  requests for multiple assets in a single call.

  Mutual Funds:
  We do basic request sanity at our end and then call MF Central's validateLien api to validate the request.
  MarkLien process for all the valid requests are registered and response is returned to the caller.

  Constraints:
  1. All the requests should belong to the same user.
  2. AssetId should be distinct for all the requests part of a single batch.
  */
  rpc InitiateMarkLienOnCollateral(InitiateMarkLienOnCollateralRequest) returns (InitiateMarkLienOnCollateralResponse) {};
  // InitiateRevokeLienOnCollateral takes multiple collaterals of a user for revoking lien placed on them. All collaterals
  // passed should be of the same type.
  rpc InitiateRevokeLienOnCollateral(InitiateRevokeLienOnCollateralRequest) returns (InitiateRevokeLienOnCollateralResponse) {};
  // InitiateInvokeLienOnCollateral takes multiple collaterals of a user for revoking lien placed on them. All collaterals
  // passed should be of the same type.
  rpc InitiateInvokeLienOnCollateral(InitiateInvokeLienOnCollateralRequest) returns (InitiateInvokeLienOnCollateralResponse) {};
  // GetProcessStatus returns status of a process for external clients.
  rpc GetProcessStatus(GetProcessStatusRequest) returns (GetProcessStatusResponse) {};
  // [INTERNAL] GetCollateralDetails returns details of collaterals for internal clients
  rpc GetCollateralDetails(GetCollateralDetailsRequest) returns (GetCollateralDetailsResponse) {};
  // InitiateVerificationForLienMarking starts verification process for lien marking
  // For Mutual Funds, this means calling MF Central to send OTP to user's phone/email
  rpc InitiateVerificationForMarkLien(InitiateVerificationForMarkLienRequest) returns (InitiateVerificationForMarkLienResponse) {};
  // SubmitVerificationDetailsForLienMarking submits details required for verifying the user.
  // For Mutual Funds, we call MF central to submit the otp entered by the user.
  rpc SubmitVerificationDetailsForLienMarking(SubmitVerificationDetailsForLienMarkingRequest) returns (SubmitVerificationDetailsForLienMarkingResponse) {};
}

message InitiateMarkLienOnCollateralRequest {
  collateralmgrtsp.common.RequestHeader header = 1;
  string customer_id = 2;
  repeated InitiateMarkLienRequest requests = 3;
}

message InitiateMarkLienRequest {
  string request_id = 1;
  string asset_id = 2;
  collateralmgrtsp.common.AssetType asset_type = 3;
  oneof details {
    InitiateMutualFundsMarkLienRequestDetails mutual_funds = 4;
  }
}

message InitiateMutualFundsMarkLienRequestDetails {
}

message InitiateMarkLienOnCollateralResponse {
  rpc.Status status = 1;
  repeated InitiateMarkLienResponse responses = 2;
}

message InitiateMarkLienResponse {
  string request_id = 1;
  oneof details {
    InitiateMutualFundsMarkLienResponseDetails mutual_funds = 2;
  }
}

message InitiateMutualFundsMarkLienResponseDetails {
}

message InitiateRevokeLienOnCollateralRequest {
  collateralmgrtsp.common.RequestHeader header = 1;
  string customer_id = 2;
  string request_id = 3;
  collateralmgrtsp.common.AssetType asset_type = 4;
  repeated InitiateRevokeLienRequest requests = 5;
}

message InitiateRevokeLienRequest {
  string collateral_id = 1;
  oneof details {
    InitiateMutualFundsRevokeLienRequestDetails mutual_funds = 2;
  }
}

message InitiateMutualFundsRevokeLienRequestDetails {
}


message InitiateRevokeLienOnCollateralResponse {
  rpc.Status status = 1;
  repeated InitiateRevokeLienResponse responses = 2;
}

message InitiateRevokeLienResponse {
  string collateral_id = 1;
  oneof details {
    InitiateMutualFundsRevokeLienResponseDetails mutual_funds = 2;
  }
}

message InitiateMutualFundsRevokeLienResponseDetails {
}


message InitiateInvokeLienOnCollateralRequest {
  collateralmgrtsp.common.RequestHeader header = 1;
  string customer_id = 2;
  string request_id = 3;
  collateralmgrtsp.common.AssetType asset_type = 4;
  repeated InitiateRevokeLienRequest requests = 5;
}

message InitiateInvokeLienRequest {
  string collateral_id = 1;
  oneof details {
    InitiateMutualFundsInvokeLienRequestDetails mutual_funds = 2;
  }
}

message InitiateMutualFundsInvokeLienRequestDetails {
}

message InitiateInvokeLienOnCollateralResponse {
  rpc.Status status = 1;
}

message InitiateInvokeLienResponse {
  string collateral_id = 1;
  oneof details {
    InitiateMutualFundsInvokeLienResponseDetails mutual_funds = 2;
  }
}

message InitiateMutualFundsInvokeLienResponseDetails {
}

message GetProcessStatusRequest {
  collateralmgrtsp.common.RequestHeader header = 1;
  string request_id = 2;
}

message GetProcessStatusResponse {
  rpc.Status status = 1;
  common.ProcessExternalStatus process_status = 2;
}

message GetCollateralDetailsRequest {
  collateralmgrtsp.common.RequestHeader header = 1;
  oneof identifier {
    CollateralIdList collateral_ids = 2;
    string customer_id = 3;
    string pool_id = 4;
  }
}

message CollateralIdList {
  repeated string collateral_ids = 1;
}

message GetCollateralDetailsResponse {
  rpc.Status status = 1;
  repeated model.Collateral collateral_details = 2;
}

message InitiateVerificationForMarkLienRequest {
  collateralmgrtsp.common.RequestHeader header = 1;
  string request_id = 2;
}

message InitiateVerificationForMarkLienResponse {
  rpc.Status status = 1;
}

message SubmitVerificationDetailsForLienMarkingRequest {
  collateralmgrtsp.common.RequestHeader header = 1;
  string request_id = 2;
  oneof details {
    MutualFundsMarkLienVerificationDetails mutual_funds = 3;
  }
}

message MutualFundsMarkLienVerificationDetails {
  string otp = 1;
}

message SubmitVerificationDetailsForLienMarkingResponse {
  rpc.Status status = 1;
}
