//go:generate gen_sql -types=<PERSON>N<PERSON>,ProcessStatus,ProcessSubStatus,ProcessStageName,ProcessStageName,StageStatus,StageSubStatus,ProcessExternalStatus
syntax = "proto3";

package collateralmgrtsp.collateralmanager.common;

option go_package = "github.com/epifi/gamma/api/collateralmgrtsp/collateralmanager/common";
option java_package = "com.github.epifi.gamma.api.collateralmgrtsp.collateralmanager.common";


enum ProcessName {
  PROCESS_NAME_UNSPECIFIED = 0;
}
enum ProcessStatus {
  PROCESS_STATUS_UNSPECIFIED = 0;
  PROCESS_STATUS_CREATED = 1;
  PROCESS_STATUS_INITIATED = 2;
  PROCESS_STATUS_COMPLETED = 3;
  PROCESS_STATUS_FAILED = 4;
  PROCESS_STATUS_VERIFIED = 5;
}

enum ProcessSubStatus {
  PROCESS_SUB_STATUS_UNSPECIFIED = 0;
}

enum ProcessStageName {
  PROCESS_STAGE_NAME_UNSPECIFIED = 0;
}

enum StageStatus {
  STAGE_STATUS_UNSPECIFIED = 0;
}

enum StageSubStatus {
  STAGE_SUB_STATUS_UNSPECIFIED = 0;
}

// Generic Status values for processes that can be sent to client
enum ProcessExternalStatus {
  PROCESS_EXTERNAL_STATUS_UNSPECIFIED = 0;
  PROCESS_EXTERNAL_STATUS_SUCCESS = 1;
  PROCESS_EXTERNAL_STATUS_IN_PROGRESS = 2;
  PROCESS_EXTERNAL_STATUS_FAILED = 3;
}
