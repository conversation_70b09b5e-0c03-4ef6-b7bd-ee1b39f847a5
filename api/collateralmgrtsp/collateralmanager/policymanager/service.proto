syntax = "proto3";

package collateralmgrtsp.collateralmanager.policymanager;

import "api/collateralmgrtsp/common/enums.proto";
import "api/collateralmgrtsp/common/request_header.proto";
import "api/rpc/status.proto";
import "api/typesv2/decimal.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/collateralmgrtsp/collateralmanager/policymanager";
option java_package = "com.github.epifi.gamma.api.collateralmgrtsp.collateralmanager.policymanager";

service PolicyManager {
  rpc GetCollateralEligibilityDetails(GetCollateralEligibilityDetailsRequest)
    returns (GetCollateralEligibilityDetailsResponse) {};
}

message GetCollateralEligibilityDetailsRequest {
  collateralmgrtsp.common.RequestHeader header = 1;
  collateralmgrtsp.common.AssetType asset_type = 2;
  EligibilityRequestDetails details = 3;
}

message EligibilityRequestDetails {
  oneof details {
    MfEligibilityRequestDetails mutual_funds = 3;
  }
}

message MfEligibilityRequestDetails {
  repeated MfIsinEligibilityRequestDetails isin_details = 1;
}

message MfIsinEligibilityRequestDetails {
  string isin = 1;
  api.typesv2.Decimal units = 2;
  string asset_type = 3;
}

message GetCollateralEligibilityDetailsResponse {
  rpc.Status status = 1;
  EligibilityDetails details = 2;
}

message EligibilityDetails {
  oneof details {
    MfEligibilityDetails mutual_funds = 3;
  }
}

message MfEligibilityDetails {
  repeated MfIsinEligibilityDetails isin_details = 1;
}

message MfIsinEligibilityDetails {
  string isin = 1;
  bool eligible = 2;
  api.typesv2.Decimal units = 3;
  google.type.Money nav = 5;
  google.type.Money market_value = 6;
  LtvAndAmount desired_level = 7;
  LtvAndAmount max_allowed_level = 8;
  LtvAndAmount immediate_invocation_level = 9;
}

message LtvAndAmount {
  api.typesv2.Decimal ltv = 1;
  google.type.Money amount = 2;
}
