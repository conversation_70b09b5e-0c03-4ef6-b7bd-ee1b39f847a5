syntax = "proto3";

package api.goals;

import "google/type/money.proto";
import "google/protobuf/timestamp.proto";
import "google/protobuf/duration.proto";
import "api/rpc/status.proto";
import "api/goals/goal.proto";
import "api/goals/investment_instrument.proto";

option go_package = "github.com/epifi/gamma/api/goals";
option java_package = "com.github.epifi.gamma.api.goals";

// defines the GRPC service to perform various operations for goals
service Goals {
  // CreateGoal creates a goal defined by the user for a given target amount and target date.
  // this rpc is called by frontend service
  rpc CreateGoal(CreateGoalRequest) returns (CreateGoalResponse) {}
  // GetGoalById returns the goal given goal id
  rpc GetGoalById(GetGoalByIdRequest) returns (GetGoalByIdResponse) {}
  // GetGoalsByIds returns list of goals for given list of goal ids
  rpc GetGoalsByIds(GetGoalsByIdsRequest) returns (GetGoalsByIdsResponse) {}
  // UpdateGoal updates a goal
  // For now only target_amount is mutable
  rpc UpdateGoal(UpdateGoalRequest) returns (UpdateGoalResponse) {}
  // GetGoalAmountConstraints gives the constraints for the goal amount.
  // It gives min, max and default amount which can be used to render goal amount slider in goal creation and goal update flows.
  rpc GetGoalAmountConstraints(GetGoalAmountConstraintsRequest) returns (GetGoalAmountConstraintsResponse) {}
}

message CreateGoalRequest {
  string name = 1;
  google.type.Money target_amount = 2;
  google.protobuf.Timestamp target_date = 3;
  string actor_id = 4;
  GoalProvenance provenance = 5;
}

message CreateGoalResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // newly created goal
  Goal goal = 2;
}

message GetGoalByIdRequest {
  // goal id
  string id = 1;
}

message GetGoalByIdResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // id of the created goal
  Goal goal = 2;
}

message GetGoalsByIdsRequest {
  // goal ids list
  repeated string ids = 1;
}

message GetGoalsByIdsResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // list of goals
  repeated Goal goals = 2;
}

message UpdateGoalRequest {
  // identifier for goal
  string id = 1;
  // target amount to update
  google.type.Money target_amount = 2;
}

message UpdateGoalResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // updated goal
  Goal goal = 2;
}

message GetGoalAmountConstraintsRequest {
  InvestmentInstrumentType investment_instrument_type = 1;
  // investment_instrument_amount is creation amount during creation flow, current amount in update flow
  google.type.Money investment_instrument_amount = 2;
  // investment_instrument_duration is total duration during creation flow, remaining duration in update flow
  google.protobuf.Duration investment_instrument_duration = 3;
  GoalProvenance provenance = 4;
}

message GetGoalAmountConstraintsResponse {
  enum Status {
    OK = 0;
    // internal server error
    INTERNAL = 13;
  }
  rpc.Status status = 1;
  // min goal amount possible
  google.type.Money min_amount = 2;
  // max goal amount possible
  google.type.Money max_amount = 3;
  // default goal amount to set if goal amount is not choose by the user
  // also used for default pointer position in goal amount slider
  google.type.Money default_amount = 4;
}

