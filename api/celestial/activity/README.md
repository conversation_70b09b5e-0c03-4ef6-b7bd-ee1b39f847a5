# Activity

An activity is a fault prone business logic of a workflow.
The purpose of an Activity is to execute a single, well-defined action (either short or long running), such as calling another service, transcoding a media file, or sending an email.

It is possible to implement an Activity fully asynchronously by completing it from a different process.

* An Activity can be implemented as a synchronous method or fully asynchronously involving multiple processes.
* An Activity can be retried indefinitely according to the provided exponential retry policy.
* If for any reason an Activity is not completed within the specified timeout, an error is reported to the Workflow, which decides how to handle it. The duration of an Activity has no limit.


## Activity Types
Based on the number of workflow request processing we can split the activities into following types:
### Scalar Activity
Processes a single workflow request at a time.
### Batch Activity
Deals with processing of more than one workflow requests.
These are part of batch stage workflows.
***
Based on the nature of request processing we can split the activities into following types:
### Sync Activity
A sync activity deals with one request processing and is synchronous in nature.
DB updates, synchronous API call, etc. are some examples of sync activities

### Async Request Activity
This activity also deals with one workflow request processing but processing happens asynchronously via some external system or via some other process. This activity only submits the processing request to the underlying system.
Most vendor calls (federal) come under asynchronous category.

### Async Enquiry Activity
This activity does status enquiry for the async request initiated using `Async Request Activity`

The way we generally handle asynchronous requests is using two-step calls. I.e. `Async Request Activity`, followed by an `Async Enquiry Activity`
***
We can further define subtypes within sync activities to provide additional features out of the box from celestial like:
### Get Notification Template
This activity is used to send communication in the middle of a workflow processing.
The activity asks for SMS/Notification templates in response from the domain services.

### Send Notification
This activity takes care of sending the communications to the users post `Get Notification Template` fetches templates from the domain service.
