syntax = "proto3";

package celestial.workflow;

option go_package = "github.com/epifi/be-common/api/celestial/workflow";
option java_package = "com.github.epifi.be-common.api.celestial.workflow";

// A Workflow Type is a name that maps to a Workflow Definition.
// Deprecated in favour of TypeEnum
enum Type {
  option deprecated = true;

  TYPE_UNSPECIFIED = 0;

  // B2C Fund Transfer workflow is responsible for transferring funds from a business account to the end customer account.
  B2C_FUND_TRANSFER = 1;

  // Type for creating a recurring payment
  CREATE_RECURRING_PAYMENT_VIA_PAYER = 2;

  // Execute Recurring Payment workflow - without auth
  EXECUTE_RECURRING_PAYMENT_WITHOUT_AUTH = 3;

  // Execute Recurring Payment workflow - with auth
  EXECUTE_RECURRING_PAYMENT_WITH_AUTH = 4;

  //Type for modifying a recurring payment
  MODIFY_RECURRING_PAYMENT = 5;

  //Type to revoke a recurring payment
  REVOKE_RECURRING_PAYMENT = 6;

  PRE_APPROVED_LOAN_APPLICATION = 7;

  // Type to link a new upi account
  LINK_UPI_ACCOUNT = 8;

  // to ask user for feedback on cx issue resolution
  CX_ISSUE_RESOLUTION_FEEDBACK = 9;

  // Type to delink a upi account
  DELINK_UPI_ACCOUNT = 10;

  // Type to freeze an account
  APPLY_TOTAL_FREEZE_ON_ACCOUNT = 11;

  // Type to unfreeze an account
  APPLY_TOTAL_UNFREEZE_ON_ACCOUNT = 12;

  PERFORM_CARD_ONBOARDING = 13;

  // Type for credit freeze account
  APPLY_CREDIT_FREEZE_ON_ACCOUNT = 14;

  // Freeze or unfreeze user's card
  FREEZE_UNFREEZE_CARD = 15;

  // Type to do liveness summary
  EXECUTE_AUTH_LIVENESS_SUMMARY = 16;

  // Type to buy us stocks
  BUY_US_STOCKS = 17;

  // Type to sell us stocks
  SELL_US_STOCKS = 18;

  // Type to open us-stock broking account
  OPEN_US_BROKER_ACCOUNT = 19;

  // type to do international fund transfer
  INTERNATIONAL_FUND_TRANSFER = 20;

  // Type to extract document from Wealth on-boarding docket
  WEALTH_ONBOARDING_DOCUMENT_EXTRACTION = 21;

  // type to re-issue a card
  PROCESS_REISSUE_CARD = 22;

  // Type to make a pre-payment to a loan account
  PRE_APPROVED_LOAN_PRE_PAY = 23;

  // Set credit card limits
  SET_CARD_LIMITS = 24;

  // Type to download credit report
  DOWNLOAD_CREDIT_REPORT = 25;

  // Type to execute auth mechanisms
  EXECUTE_AUTH_WORKFLOW = 26;

  // Set credit card usage
  SET_CARD_USAGE = 27;

  // type to unlock the masked details for a card
  PROCESS_VIEW_CARD_DETAILS = 28;

  // Type for ordering a physical debit card with charges workflow
  ORDER_PHYSICAL_CARD_WITH_CHARGES = 29;

  // Type for fund transfer workflow
  FUND_TRANSFER = 30;

  // Type to activate credit card
  ACTIVATE_CREDIT_CARD = 31;

  // Type to pre-close a loan account
  PRE_CLOSE_LOAN_ACCOUNT = 32;

  // Type to process dispute
  PROCESS_DISPUTE = 33;

  // type to send physical credit card to customer
  ISSUE_PHYSICAL_CREDIT_CARD = 34;

  //Type to generate credit card Bill
  PERFORM_BILL_GENERATION = 35;

  // type to link upi number to a vpa
  LINK_UPI_NUMBER = 36;

  // Type to provision chequebook
  PROVISION_CHEQUEBOOK = 37;

  // Type to generate the files after swift is success
  GENERATE_SWIFT_REPORTS = 38;

  // Reset credit card ATM pin
  RESET_CARD_PIN = 39;

  // type to delink upi number from a vpa
  DELINK_UPI_NUMBER = 40;

  //Type to perform credit card payments in app
  PERFORM_CREDIT_CARD_PAYMENT = 41;

  // Type to process card transaction
  PROCESS_CARD_TRANSACTION = 42;

  // Type to orchestrate action against case under risk
  RISK_PROCESS_REVIEW_ACTION = 43;

  // workflow to orchestrate tasks post successful US broker account creation.
  USS_POST_ACCOUNT_CREATION = 44;

  // Type to orchestrate full freeze action against case
  RISK_FULL_FREEZE = 45;

  // Type to orchestrate full unfreeze action against case
  RISK_UNFREEZE = 46;

  // Type to orchestrate credit freeze action against case
  RISK_CREDIT_FREEZE = 47;

  // Type to orchestrate pass user action against case
  RISK_PASS_USER = 48;

  // type to orchestrate alert processing flow
  RISK_PROCESS_ALERT = 49;

  // type to orchestrate case creation flow for alert
  RISK_UPSERT_CASE = 50;

  // Type to orchestrate Jump transaction's maturity
  P2P_INVESTMENT_TRANSACTION_MATURITY = 51;

  LOAN_APPLICATION = 52;

  // to orchestrate incident lifecycle of Watson service
  CX_MANAGE_WATSON_INCIDENT_LIFECYCLE = 53;

  // Type to export cc statement documents
  EXPORT_CREDIT_CARD_STATEMENT = 54;

  // type for credit freeze bank action V2
  RISK_BANK_ACTION_CREDIT_FREEZE = 55;

  // type for full freeze bank action V2
  RISK_BANK_ACTION_FULL_FREEZE = 56;

  // type for unfreeze bank action V2
  RISK_BANK_ACTION_UNFREEZE = 57;

  // type for stage wise comms for manually created tickets
  CX_MANAGE_MANUAL_TICKET_INCIDENT_LIFECYCLE = 58;

  // type for debit freeze bank action V2
  RISK_BANK_ACTION_DEBIT_FREEZE = 59;

  // Type to orchestrate debit freeze action against case
  RISK_DEBIT_FREEZE = 60;

  // Type to apply lien action against case
  RISK_BANK_ACTION_APPLY_LIEN = 61;

  // Type to apply lien action against case
  APPLY_LIEN = 62;
}

message TypeEnum {
  // human-readable string representation for the workflow type
  string type_value = 1;
}
