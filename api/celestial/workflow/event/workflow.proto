//go:generate gen_queue_pb
syntax = "proto3";

package celestial.workflow.event;

import "api/queue/consumer_headers.proto";
import "api/celestial/workflow_req.proto";
import "api/celestial/workflow_history.proto";

option go_package = "github.com/epifi/be-common/api/celestial/workflow/event";
option java_package = "com.github.epifi.be-common.api.celestial.workflow.event";

// WorkflowUpdate is the message to be published to notify external systems about workflow state update
message WorkflowUpdate {
  queue.ConsumerRequestHeader request_header = 1;
  // workflow request object
  celestial.WorkflowRequest workflow_request = 2;
  // workflow history associated with workflow request
  repeated celestial.WorkflowHistory workflow_history_list = 3;
}
