// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package wealthonboarding;

import "api/frontend/deeplink/deeplink.proto";
import "api/typesv2/address.proto";
import "api/typesv2/document_proof.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/income_slab.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/nationality.proto";
import "api/typesv2/nominee.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/politically_exposed_status.proto";
import "api/typesv2/residential_status.proto";
import "api/wealthonboarding/data.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/wealthonboarding";
option java_package = "com.github.epifi.gamma.api.wealthonboarding";

message PreInvestmentDetail {
  api.typesv2.common.Name customer_name = 1;
  google.type.Date dob = 2;
  api.typesv2.AddressType address_type = 3;
  api.typesv2.PostalAddress address = 4;
  api.typesv2.Nationality nationality = 5;
  api.typesv2.DocumentProof pan = 6;
  // TODO(ismail): add bank account details once it is integrated
  // TODO(ismail): the employment data is fetched from the employment service, we need to handle cases of waitlisted user as well
  wealthonboarding.EmploymentData employment_data = 7;
  api.typesv2.IncomeSlab income_slab = 8 [deprecated = true]; // use income instead
  api.typesv2.PoliticallyExposedStatus politically_exposed_status = 9;
  string customer_ip_address = 10;
  api.typesv2.ResidentialStatus tax_residential_status = 11;
  string email_id = 12;
  api.typesv2.Gender gender = 13;
  api.typesv2.common.PhoneNumber mobile_no = 14;

  // nominees of user retrieved from EPIFI TECH
  api.typesv2.Nominee nominee = 15 [deprecated = true];

  bool customer_consent = 16;
  BankDetails bank_details = 17;
  api.typesv2.DocumentProof signature = 18;
  int32 income = 19;
  NomineeDeclarationDetails nominee_declaration_details = 20;
}

message PreInvestmentDetailsV2 {
  PreInvestmentDetail investment_details = 1;
  FailureType failure_type = 2;
  FailureReason failure_reason = 3;
  // deeplink for the user to perform the step if there is any missing data that needs to be collected
  frontend.deeplink.Deeplink next_step = 4;
}

// OnboardingEligibility enum represents whether a user can onboard on wealth or not
// Some users can have their onboarding status as MANUAL_INTERVENTION or FUTURE_SCOPE
// for such users we are marking their eligibility as NO
enum OnboardingEligibility {
  ONBOARDING_ELIGIBILITY_UNSPECIFIED = 0;
  ONBOARDING_ELIGIBILITY_YES = 1;
  ONBOARDING_ELIGIBILITY_NO = 2;
}

// FailureType denotes the type of failure in getting the wealth onboarding data for a user.
enum FailureType {
  FAILURE_TYPE_UNSPECIFIED = 0;
  // indicates that the user data cannot be provided by wealth without manual intervention as it is a permanent failure
  FAILURE_TYPE_NOT_RETRYABLE = 1;
  // indicates that the user data has a temporary failure and retry can be done
  FAILURE_TYPE_RETRYABLE = 2;
}

// FailureReason denotes the reason of failure in getting the wealth onboarding data for a user
enum FailureReason {
  FAILURE_REASON_UNSPECIFIED = 0;
  // indicates that user's DOB is not same in EPIFI_WEALTH database and KRA vendor
  FAILURE_REASON_DOB_MISMATCH = 1;
  // user's docket with Aadhaar as proof of address is not yet validated by KRA
  FAILURE_REASON_AADHAAR_POA_NOT_VALIDATED = 2;
}
