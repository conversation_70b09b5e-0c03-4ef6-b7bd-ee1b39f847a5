syntax = "proto3";

package wealthonboarding.cx;

import "api/typesv2/address.proto";
import "api/typesv2/gender.proto";
import "api/typesv2/common/name.proto";
import "api/wealthonboarding/data.proto";

option go_package = "github.com/epifi/gamma/api/wealthonboarding/cx";
option java_package = "com.github.epifi.gamma.api.wealthonboarding.cx";

message CustomerProfileDetails {
  api.typesv2.common.Name name = 1;
  api.typesv2.Gender gender = 2;
  string phone_number = 3;
  string email = 4;
  BankDetails bank_details = 5;
  repeated api.typesv2.AddressWithType address_with_type = 6;
}
