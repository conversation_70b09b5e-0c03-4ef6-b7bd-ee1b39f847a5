// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package wealthonboarding;

import "api/typesv2/address.proto";
import "api/typesv2/document_proof.proto";
import "api/vendors/wealth/cvlkra.proto";
import "api/wealthonboarding/ckyc_data.proto";
import "api/wealthonboarding/data.proto";
import "api/wealthonboarding/digilocker_data.proto";
import "api/wealthonboarding/enums.proto";
import "api/wealthonboarding/manual_review.proto";
import "api/wealthonboarding/ocr_document_proof.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/wealthonboarding";
option java_package = "com.github.epifi.gamma.api.wealthonboarding";

// data captured during onboarding
message OnboardingMetadata {
  api.typesv2.DocumentProof pan_details = 1;
  // Deprecated: use pan_details instead as poi will always be pan
  api.typesv2.DocumentProof poi_details = 2;
  api.typesv2.DocumentProof poa_details = 3;
  PersonalDetails personal_details = 4;
  FaceMatchData face_match_data = 5;
  NsdlData nsdl_data = 6;
  KraData kra_data = 7;
  CkycData ckyc_data = 8;
  NameMatchInfo name_match_info = 9;
  DocketInfo kra_docket_info = 10;
  DocketInfo agreement_docket_info = 11;
  CustomerProvidedData customer_provided_data = 12;
  LivenessData liveness_data = 13;
  bool is_fresh_kra = 14;
  string customer_ip_address = 15;
  EmploymentData employment_data = 16;
  UploadKraDocData upload_kra_doc_data = 17;
  DownloadedKraDocData downloaded_kra_doc_data = 18;
  BankDetails bank_details = 19;
  OcrDocumentProof poa_with_ocr = 20;
  ManualReviewAttempts manual_review_attempts = 21;
  DigilockerData digilocker_data = 22;
  int32 pan_validate_attempts_count = 23;
  bool is_investment_risk_survey_complete = 24;
}

message OnboardingDetails {
  string id = 1;
  string actor_id = 2;
  OnboardingMetadata metadata = 10;
  OnboardingStatus status = 12;
  OnboardingStep current_step = 14;
  google.protobuf.Timestamp created_at = 16;
  google.protobuf.Timestamp updated_at = 18;
  google.protobuf.Timestamp completed_at = 20;
  OnboardingType onboarding_type = 22;
  WealthFlow current_wealth_flow = 23;
  AgentProvidedData agent_provided_data = 24;
}

// FieldMask for helping db updated in onboardings table
enum OnboardingDetailsFieldMask {
  ONBOARDING_DETAILS_FIELD_MASK_UNSPECIFIED = 0;
  ONBOARDING_DETAILS_FIELD_MASK_METADATA = 1;
  ONBOARDING_DETAILS_FIELD_MASK_STATUS = 2;
  ONBOARDING_DETAILS_FIELD_MASK_CURRENT_STEP = 3;
  ONBOARDING_DETAILS_FIELD_MASK_COMPLETED_AT = 4;
  ONBOARDING_DETAILS_FIELD_MASK_CURRENT_WEALTH_FLOW = 5;
  ONBOARDING_DETAILS_FIELD_MASK_AGENT_PROVIDED_DATA = 6;
}

// represents the data that needs to be collected from user by CX agents
// use case - when KYC application status is on Hold because of issue in any of the images we submitted,
// we need to collect the proper image from user and upload the same in CVL portal to get it approved
enum UserInputPendingData {
  USER_INPUT_PENDING_DATA_UNSPECIFIED = 0;
  NEED_PAN_IMAGE = 1;
  NEED_AADHAAR_IMAGE = 2;
  NEED_PASSPORT_IMAGE = 3;
  NEED_DRIVING_LICENSE_IMAGE = 4;
  NEED_SIGNATURE_IMAGE = 5;
  PENDING_DATA_COLLECTED = 6;
}

// AgentProvidedData is used for 2 use cases
// 1. Data collection for re-creating the docket in case of corrections requested from KRA
// 2. For storing pending data to be collected from the users to help agents keep track of what is pending from user
message AgentProvidedData {
  // types of data collected from the users
  api.typesv2.PostalAddress address = 1;
  api.typesv2.DocumentProof poa = 2;
  api.typesv2.DocumentProof pan = 3;
  api.typesv2.DocumentProof user_image = 4;
  api.typesv2.DocumentProof signature = 5;
  // pending data that needs to be collected
  repeated UserInputPendingData user_input_pending_data = 6;
  // KYC status of the user based on which agent is providing this data
  // can be HOLD or REJECTED, since agent intervention is only needed in these cases
  // In case of HOLD, we just need to correct the data in the docket and ask user to esign
  // In case of REJECTED, agent enters the data in text form in CVL portal and then we need to correct the data in the docket and ask user to do esign
  // this is needed so that corrected docket is uploaded in the corresponding folder (HOLD vs regular) in CVL SFTP
  vendors.wealth.KraStatus kyc_status = 7;
  // timestamp at which agent uploaded the documents
  google.protobuf.Timestamp provided_at = 8;
}
