syntax = "proto3";

package wealthonboarding;

import "api/wealthonboarding/onboarding.proto";
import "api/wealthonboarding/onboarding_step.proto";

option go_package = "github.com/epifi/gamma/api/wealthonboarding";
option java_package = "com.github.epifi.gamma.api.wealthonboarding";

message OnboardingSummary {
  OnboardingDetails onboarding_details = 1;
  OnboardingStepDetails current_step_details = 2;
}
