syntax = "proto3";

package wealthonboarding.ckyc;

import "api/wealthonboarding/ckyc_data.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/wealthonboarding/ckyc";
option java_package = "com.github.epifi.gamma.api.wealthonboarding.ckyc";

// CkycData stores the raw response of the CKYC apis in db
message CkycData {
    string ckyc_no = 1;
    // date when kyc was first recorded
    google.type.Date kyc_date = 2;
    // date when kyc record was last updated
    google.type.Date updated_date = 3;
    wealthonboarding.CkycAccountType account_type = 4;
    CkycPayload ckyc_payload = 5;
    string actor_id = 6;
    CkycDataType ckyc_data_type = 7;
}

// indicates the type of data stored
enum CkycDataType {
    CKYC_DATA_TYPE_UNSPECIFIED = 0;
    CKYC_DATA_TYPE_SEARCH = 1;
    CKYC_DATA_TYPE_DOWNLOAD = 2;
}

message CkycPayload {
    // CkycPayload could be either search_data or download_data
    oneof CkycData {
        wealthonboarding.CkycSearchData ckyc_search_data = 5;
        wealthonboarding.CkycDownloadData ckyc_download_data = 6;
    }
}
