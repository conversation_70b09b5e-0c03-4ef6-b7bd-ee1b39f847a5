syntax = "proto3";

package aml;

import "api/aml/data.proto";
import "api/typesv2/common/ownership.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/aml";
option java_package = "com.github.epifi.gamma.api.aml";

message ScreeningAttempt {
  // primary key
  string id = 1;
  // actor id
  string actor_id = 2;
  // client request id which is passed by clients
  string client_request_id = 3;
  AmlProduct product = 4;
  vendorgateway.Vendor vendor = 5;
  CustomerDetails customer_details = 6;
  // status of the screening attempt, whether the attempt is raised successfully or not
  AmlScreeningStatus status = 7;
  // result of the screening, whether a match is found or not
  AmlMatch result = 8;
  // rejection message if the request is rejected
  string rejection_message = 9;
  // rejection code if the request is rejected
  RejectionCode rejection_code = 10;
  // the time when last screening was done
  google.protobuf.Timestamp last_screening_attempted_at = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;

  // Owner of the screening attempt
  // Required for adhering to data segregation compliance requirements of each regulatory body.
  api.typesv2.common.Owner owner = 14;
}

enum ScreeningAttemptFieldMask {
  SCREENING_ATTEMPT_FIELD_MASK_UNSPECIFIED = 0;
  SCREENING_ATTEMPT_FIELD_MASK_CUSTOMER_DETAILS = 1;
  SCREENING_ATTEMPT_FIELD_MASK_ATTEMPT_STATUS = 2;
  SCREENING_ATTEMPT_FIELD_MASK_RESULT = 3;
  SCREENING_ATTEMPT_FIELD_MASK_LAST_SCREENING_ATTEMPTED_AT = 4;
}
