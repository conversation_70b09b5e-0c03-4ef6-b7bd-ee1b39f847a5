// protolint:disable MAX_LINE_LENGTH
//go:generate gen_queue_pb
syntax = "proto3";

package aml;

import "api/aml/data.proto";
import "api/queue/consumer_headers.proto";
import "api/typesv2/common/ownership.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/aml";
option java_package = "com.github.epifi.gamma.api.aml";

service Consumer {
  // A vendor callback consumer which is triggered when a case reviewer agent takes decision on a case created in the vendor portal
  // Processes the vendor request and updates the case details table with the data received
  rpc ProcessCallbackForDecisionsOnCase (ProcessCallbackForDecisionsOnCaseRequest) returns (ProcessCallbackForDecisionsOnCaseResponse) {};
  // gets the ids of aml_screening_attempts for which file has to be generated using client_request_id from aml_attempt_id_client_id_mapper table
  // gets the details required to generated the requested files from aml_screening_attempts table
  // generates and uploads the files requested in s3
  // which then has to be pushed to the windows server where TSS vendor application is hosted
  rpc GenerateFiles (GenerateFilesRequest) returns (GenerateFilesResponse) {};
}

message GenerateFilesRequest {
  // standard header to be added to all requests
  queue.ConsumerRequestHeader request_header = 1;
  string client_request_id = 2 [(validate.rules).string.min_len = 1];
  FileType file_type = 3;
  // if this flag is set to true then user will go through fresh screening
  bool is_fresh_screening_required = 4;
}

message GenerateFilesResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

// This event will be published from VN server when received from vendor
message ProcessCallbackForDecisionsOnCaseRequest {
  // standard header to be added to all requests
  queue.ConsumerRequestHeader request_header = 1;
  // Generated by vendor
  string vendor_request_id = 2;
  repeated DecisionDetails decision_details = 3;
  vendorgateway.Vendor vendor_name = 4;

  // Owner of the screening attempt
  // Required for adhering to data segregation compliance requirements of each regulatory body.
  api.typesv2.common.Owner owner = 5;
}

// Details of the decision taken and metadata regarding the case
message DecisionDetails {
  // unique identifier for transaction
  string transaction_id = 1;
  // unique vendor identifier for user
  string record_identifier = 2;
  // unique vendor identifier for case
  string vendor_case_id = 3;
  // url for case
  string vendor_case_url = 4;
  // decision taken by epifi ops agent on the case(Accept/Decline)
  CaseDecision case_decision = 5;
  // politically exposed person. Possible values - PEP, Not a PEP, Related to PEP
  PepType pep_type = 6;
  // comma separated values of different categories of pep like bureaucrat, civil servant etc
  string pep_classification = 7;
  string adverse_media = 8;
  // comma separated values of categorization of adverse media based on the type of crime, ex. bank fraud, corruption etc.
  string adverse_media_classification = 9;
  // watchlist categories for which match is found
  repeated AmlWatchlistCategory watchlist_categories = 10;
  // remarks by the epifi ops agent
  string final_remarks = 11;
  google.protobuf.Timestamp approved_on = 12;
  string approved_by = 13;
}

message ProcessCallbackForDecisionsOnCaseResponse {
  queue.ConsumerResponseHeader response_header = 1;
}
