//go:generate gen_sql -types=AmlProduct,CustomerDetails,AmlScreeningStatus,AmlMatch,RejectionCode,CaseDecision,PepType,FileType,FileGenerationStatus
syntax = "proto3";

package aml;

import "api/typesv2/common/address.proto";
import "api/typesv2/common/boolean.proto";
import "api/typesv2/common/document_proof.proto";
import "api/typesv2/common/employment_type.proto";
import "api/typesv2/common/gender.proto";
import "api/typesv2/common/income_slab.proto";
import "api/typesv2/common/marital_status.proto";
import "api/typesv2/common/name.proto";
import "api/typesv2/common/nationality.proto";
import "api/typesv2/common/phone_number.proto";
import "api/typesv2/common/politically_exposed_status.proto";
import "google/type/date.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/aml";
option java_package = "com.github.epifi.gamma.api.aml";

message CustomerDetails {
  // name of the customer - Mandatory
  api.typesv2.common.Name name = 1 [(validate.rules).message.required = true];
  // father name - NOT Mandatory
  api.typesv2.common.Name father_name = 2;
  // mother name - NOT Mandatory
  api.typesv2.common.Name mother_name = 3;
  // gender - NOT Mandatory
  api.typesv2.common.Gender gender = 4;
  // marital status - NOT Mandatory
  api.typesv2.common.MaritalStatus marital_status = 5;
  // income slab - NOT Mandatory
  api.typesv2.common.IncomeSlab income_slab = 6;
  // pan number - NOT Mandatory
  string pan_number = 7;
  // nationality - Mandatory
  api.typesv2.common.Nationality nationality = 8 [(validate.rules).enum = {not_in: [0]}];
  // passport id number - NOT Mandatory
  string passport_number = 9;
  // passport expiry date - Mandatory if passport provided
  google.type.Date passport_expiry_date = 10;
  // driving license id number - NOT Mandatory
  string driving_license_number = 11;
  // passport expiry date - Mandatory if driving license provided
  google.type.Date driving_license_expiry_date = 12;
  // voter id number - NOT Mandatory
  string voter_id = 13;
  // document type of proof of address provided - NOT Mandatory
  api.typesv2.common.DocumentProofType poa_type = 14;
  // phone number - NOT Mandatory
  api.typesv2.common.PhoneNumber phone_number = 15;
  // email - NOT Mandatory
  string email = 16;
  // date of birth - NOT Mandatory
  google.type.Date date_of_birth = 17;
  // permanent address - NOT Mandatory
  api.typesv2.common.PostalAddress permanent_address = 18;
  // correspondence address - NOT Mandatory
  api.typesv2.common.PostalAddress correspondence_address = 19;
  // politically exposed status - NOT Mandatory
  api.typesv2.common.PoliticallyExposedStatus politically_exposed_status = 20;
  // employment type - NOT Mandatory
  api.typesv2.common.EmploymentType employment_type = 21;
}

// The entity which is requesting the screening, screening rules can be configured based on this
enum AmlEntity {
  AML_ENTITY_UNSPECIFIED = 0;
  AML_ENTITY_WEALTH = 1;
  AML_ENTITY_TECH = 2;
}

// The product for which screening is requested, screening rules can be configured based on this
enum AmlProduct {
  AML_PRODUCT_UNSPECIFIED = 0;
  AML_PRODUCT_US_STOCKS = 1;
  AML_PRODUCT_MUTUAL_FUNDS = 2;
  AML_PRODUCT_SAVINGS_ACCOUNT = 3;
  AML_PRODUCT_LOANS = 4;
}

// New customer will raise a new screening request to vendor
// Update customer will update the customer details in vendor's system and new screening request will not be made
// After updating customer details, customer will be screened with updated details going forward
enum AmlScreeningMode {
  AML_SCREENING_MODE_UNSPECIFIED = 0;
  AML_SCREENING_MODE_NEW_CUSTOMER = 1;
  AML_SCREENING_MODE_UPDATE_CUSTOMER = 2;
}

// MatchData has the details of the match to be sent to the calling service
message MatchData {
  // watchlist category against which match is found
  AmlWatchlistCategory matching_watchlist_category = 1 [deprecated = true];
  // actual watchlist name for which match is found
  string matching_watchlist = 2 [deprecated = true];
  // name of the customer in the watchlist record
  string matching_record_name = 3;
  // parameter for which the match is found
  AmlParameter matching_parameter = 4;
  // applicable when fuzzy matching is enabled
  double matching_percentage = 5;
}

enum AmlWatchlistCategory {
  AML_WATCHLIST_CATEGORY_UNSPECIFIED = 0;
  // national and international sanction lists
  AML_WATCHLIST_CATEGORY_SANCTIONS = 1;
  // law enforcement public domain data
  AML_WATCHLIST_CATEGORY_LAW_ENFORCEMENT = 2;
  // regulatory enforcement public domain data
  AML_WATCHLIST_CATEGORY_REGULATORY_ENFORCEMENT = 3;
  // Politically exposed persons list
  AML_WATCHLIST_CATEGORY_PEP = 4;
  // Adverse media coverage
  AML_WATCHLIST_CATEGORY_ADVERSE_MEDIA = 5;
  // Any other watchlist
  AML_WATCHLIST_CATEGORY_OTHERS = 6;
}

// represents the status of the screening attempt
enum AmlScreeningStatus {
  AML_SCREENING_STATUS_UNSPECIFIED = 0;
  // screening attempt raised successfully
  AML_SCREENING_STATUS_INITIATED = 1;
  // screening attempt failed
  AML_SCREENING_STATUS_FAILED = 2;
  // screening is completed
  AML_SCREENING_STATUS_SUCCESS = 3;
}

// This describes the parameter match based on which case is created
enum AmlParameter {
  AML_PARAMETER_UNSPECIFIED = 0;
  // PAN number matching
  AML_PARAMETER_PAN = 1;
  // Passport number matching
  AML_PARAMETER_PASSPORT = 2;
  // Driving license number matching
  AML_PARAMETER_DRIVING_LICENSE = 3;
  AML_PARAMETER_NAME = 4 [deprecated = true];
  // Exact name matching in any order
  // e.g. Jolly Joseph and Joseph Jolly
  AML_PARAMETER_EXACT_NAME = 5;
  // Name matching with spaces removed
  // e.g. Jolly Joseph and JollyJoseph
  AML_PARAMETER_BINDING_NAME = 6;
  // Fuzzy name matching using a matching algorithm
  // a matching percentage is also returned for this case
  AML_PARAMETER_FUZZY_NAME = 7;
  // First name matching
  // e.g. Jolly Joseph and Jolly Kent
  AML_PARAMETER_INITIAL_NAME = 8;
  // Vowels in the name matching
  // e.g. Jolly Joseph and Jolly Yosef
  AML_PARAMETER_VOWEL_NAME = 9;
  // Alias name matching
  AML_PARAMETER_ALIAS_NAME = 10;
  // DIN number matching
  AML_PARAMETER_DIN = 11;
  // Date of birth fuzzy matching
  AML_PARAMETER_FUZZY_DOB = 12;
  // State in address matching
  AML_PARAMETER_STATE = 13;
  // Country in address matching
  AML_PARAMETER_COUNTRY = 14;
  // City in address matching
  AML_PARAMETER_CITY = 15;
}

// Enum represents if a match is found or not after screening is done by vendor
enum AmlMatch {
  AML_MATCH_UNSPECIFIED = 0;
  // Match found in screening
  AML_MATCH_FOUND = 1;
  // Match not found in screening
  AML_MATCH_NOT_FOUND = 2;
  // Error in creating screening attempt
  AML_MATCH_ERROR = 3;
}

enum MatchType {
  MATCH_TYPE_UNSPECIFIED = 0;
  MATCH_TYPE_CONFIRM = 1;
  MATCH_TYPE_PROBABLE = 2;
}

// MatchDetails represents the details of the single match sent by vendor and stored in our DB
message MatchDetails {
  // case id identifies a case created for a user at vendor
  // deprecated since case id is already present at case level
  string case_id = 1 [deprecated = true];
  // match id identifies an individual match within the case created for a user at vendor
  string match_id = 2;
  // watchlist category for which the match is found
  AmlWatchlistCategory watchlist_category = 3;
  // comma separated watchlist keywords for which the match is found
  string watchlist = 4;
  // rule/algorithm with which alert is generated
  string matching_rule = 5;
  // watchlist record id of the matching record
  string watchlist_record_id = 6;
  // vendor id for the matching watchlist record
  string vendor_watchlist_record_id = 7;
  // name present in the matching watchlist record
  string watchlist_record_name = 8;
  // available only in cases where fuzzy matching is enabled
  double matching_percentage = 9;
  // whether the watchlist record is active
  api.typesv2.common.BooleanEnum is_active = 10;
  // the parameter match based on which case is created
  AmlParameter matching_parameter = 11;
  MatchType match_type = 12;
  // aliases present in the match
  string watchlist_aliases = 13;
  // any notices issued by governments or agencies like interpol, fbi etc
  string notice_details = 14;
  // address details present in the watchlist
  string watchlist_address = 15;
  // associated countries present in the watchlist
  string watchlist_associated_countries = 16;
  // dates of birth present in the watchlist
  string watchlist_dob = 17;
  // any media articles or government website links containing information about the person
  string external_source_links = 18;
  // identity numbers present in the watchlist
  string watchlist_identity_numbers = 19;
  // any other information present in the watchlist
  string additional_info = 20;
}

enum RejectionCode {
  REJECTION_CODE_UNSPECIFIED = 0;
  REJECTION_CODE_CORRESPONDENCE_ADDRESS_1_LENGTH_EXCEEDED = 1;
  REJECTION_CODE_CORRESPONDENCE_ADDRESS_2_LENGTH_EXCEEDED = 2;
  REJECTION_CODE_CORRESPONDENCE_ADDRESS_3_LENGTH_EXCEEDED = 3;
  REJECTION_CODE_CORRESPONDENCE_ADDRESS_LOCALITY_LENGTH_EXCEEDED = 4;
  REJECTION_CODE_CORRESPONDENCE_ADDRESS_POSTAL_CODE_LENGTH_EXCEEDED = 5;
  REJECTION_CODE_CORRESPONDENCE_ADDRESS_ADMINISTRATIVE_AREA_LENGTH_EXCEEDED = 6;
  REJECTION_CODE_PERMANENT_ADDRESS_1_LENGTH_EXCEEDED = 7;
  REJECTION_CODE_PERMANENT_ADDRESS_2_LENGTH_EXCEEDED = 8;
  REJECTION_CODE_PERMANENT_ADDRESS_3_LENGTH_EXCEEDED = 9;
  REJECTION_CODE_PERMANENT_ADDRESS_LOCALITY_LENGTH_EXCEEDED = 10;
  REJECTION_CODE_PERMANENT_ADDRESS_POSTAL_CODE_LENGTH_EXCEEDED = 11;
  REJECTION_CODE_PERMANENT_ADDRESS_ADMINISTRATIVE_AREA_LENGTH_EXCEEDED = 12;
  REJECTION_CODE_FIRST_NAME_CONTAINS_INVALID_CHARACTERS = 13;
  REJECTION_CODE_FIRST_NAME_MISSING = 14;
  REJECTION_CODE_FIRST_NAME_LENGTH_EXCEEDED = 15;
  REJECTION_CODE_LAST_NAME_CONTAINS_INVALID_CHARACTERS = 16;
  REJECTION_CODE_LAST_NAME_LENGTH_EXCEEDED = 17;
  REJECTION_CODE_MIDDLE_NAME_CONTAINS_INVALID_CHARACTERS = 18;
  REJECTION_CODE_MIDDLE_NAME_LENGTH_EXCEEDED = 19;
  REJECTION_CODE_INVALID_PAN_FORMAT = 20;
  REJECTION_CODE_PASSPORT_LENGTH_EXCEEDED = 21;
  REJECTION_CODE_INVALID_DOB_FORMAT = 22;
  REJECTION_CODE_PARENT_COMPANY_NOT_RECOGNIZED = 23;
  REJECTION_CODE_PARENT_COMPANY_LENGTH_EXCEEDED = 24;
  REJECTION_CODE_NATIONALITY_COUNTRY_NOT_RECOGNIZED = 25;
  REJECTION_CODE_PERMANENT_ADDRESS_COUNTRY_CODE_LENGTH_EXCEEDED = 26;
  REJECTION_CODE_PERMANENT_ADDRESS_COUNTRY_NOT_RECOGNIZED = 27;
  REJECTION_CODE_CORRESPONDENCE_ADDRESS_COUNTRY_NOT_RECOGNIZED = 28;
  REJECTION_CODE_PRODUCT_NOT_RECOGNIZED = 29;
  REJECTION_CODE_PRODUCT_LENGTH_EXCEEDED = 30;
  REJECTION_CODE_RECORD_IDENTIFIER_MISSING = 31;
  REJECTION_CODE_RECORD_IDENTIFIER_LENGTH_EXCEEDED = 32;
  REJECTION_CODE_REQUEST_ID_MISSING = 33;
  REJECTION_CODE_REQUEST_ID_LENGTH_EXCEEDED = 34;
  REJECTION_CODE_REQUEST_ID_DUPLICATE = 35;
  REJECTION_CODE_SCREENING_CATEGORY_NOT_RECOGNIZED = 36;
  REJECTION_CODE_SCREENING_CATEGORY_LENGTH_EXCEEDED = 37;
  REJECTION_CODE_SYSTEM_NAME_NOT_RECOGNIZED = 38;
  REJECTION_CODE_SYSTEM_NAME_LENGTH_EXCEEDED = 39;
  REJECTION_CODE_CUSTOMER_CATEGORY_NOT_RECOGNIZED = 40;
  REJECTION_CODE_API_TOKEN_NOT_RECOGNIZED = 41;
  REJECTION_CODE_API_TOKEN_MISSING = 42;
  REJECTION_CODE_API_TOKEN_INVALID = 43;
  REJECTION_CODE_GENDER_NOT_RECOGNIZED = 44;
  REJECTION_CODE_CITIZENSHIP_NOT_RECOGNIZED = 45;
  REJECTION_CODE_OCCUPATION_NOT_RECOGNIZED = 46;
  REJECTION_CODE_UNKNOWN = 100;
}

// Decision taken by epifi agent on an AML case in vendor application
enum CaseDecision {
  CASE_DECISION_UNSPECIFIED = 0;
  // User approved for onboarding
  CASE_DECISION_APPROVED = 1;
  // User rejected for onboarding
  CASE_DECISION_REJECTED = 2;
}

// Politically exposed status of the user provided by agent
enum PepType {
  PEP_TYPE_UNSPECIFIED = 0;
  // Politically exposed person
  PEP_TYPE_PEP = 1;
  // Not Politically exposed person
  PEP_TYPE_NOT_PEP = 2;
  // Related to Politically exposed person
  PEP_TYPE_RELATED_TO_PEP = 3;
}

// represents the type of file to be generated
enum FileType {
  FILE_TYPE_UNSPECIFIED = 0;
  // FL1 - file in which customer details are present
  // FL43 - file in which customer product details are present
  // For any user, record must be present in both of these files, so they are tightly coupled and a single enum is used
  FILE_TYPE_FL1_FL43 = 1;
}

// file generation status for a specific actor
enum FileGenerationStatus {
  FILE_GENERATION_STATUS_UNSPECIFIED = 0;
  // file generation is successful for actor
  FILE_GENERATION_STATUS_SUCCESS = 1;
  // file generation failed for this actor
  FILE_GENERATION_STATUS_FAILURE = 2;
  // file generation can be retried for this actor
  FILE_GENERATION_STATUS_RETRY_NEEDED = 3;
  // file generation is pending for this actor
  FILE_GENERATION_STATUS_PENDING = 4;
  // file generation retried for this record
  FILE_GENERATION_STATUS_RETRY_DONE = 5;
}

// Purpose is used to decide which type of screening to perform
enum Purpose {
  PURPOSE_UNSPECIFIED = 0;
  // To screen a user with the given details once
  PURPOSE_INITIAL_SCREENING = 1;
  // To keep screening the user not just now, but also in future if his details match
  // any new entries in the watchlists as the watchlists keep getting updated periodically
  PURPOSE_CONTINUOUS_SCREENING = 2;
}
