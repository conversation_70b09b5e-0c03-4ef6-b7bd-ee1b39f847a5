// protolint:disable MAX_LINE_LENGTH
syntax = "proto3";

package p2pinvestment;

option go_package = "github.com/epifi/gamma/api/p2pinvestment";
option java_package = "com.github.epifi.gamma.api.p2pinvestment";

//go:generate gen_sql -types=InvestorStatus,InvestorSubStatus,Vendor,InvestmentTransactionType,InvestmentTransactionStatus,InvestmentTransactionSubStatus,ActivityType,ActivityStatus,ActivityStageStatus,ActivityStage,IneligibilityReason,SchemeIneligibilityReason,,MaturityActionStatus,MaturityActionSubStatus,MaturityActionType,SchemeStatus,DocumentUploadStatus,MaturityConsentType
enum InvestorStatus {
  INVESTOR_STATUS_UNSPECIFIED = 0;
  // when investor creation at vendor is not yet triggerred
  INVESTOR_STATUS_CREATION_PENDING = 1;
  // when investor creation at vendor and our side is in progress
  INVESTOR_STATUS_CREATION_IN_PROGRESS = 2;
  INVESTOR_STATUS_CREATION_FAILED = 3;
  INVESTOR_STATUS_CREATED = 4;
  // if vendor call failed
  INVESTOR_STATUS_CREATION_TRANSIENT_FAILED = 5;
  // if investor account is closed
  INVESTOR_STATUS_ACCOUNT_CLOSED = 6;
}

enum InvestorSubStatus {
  INVESTOR_SUB_STATUS_UNSPECIFIED = 0;
  // if user is already investing through other partner with our vendor partner
  INVESTOR_SUB_STATUS_ALREADY_CREATED_BY_OTHER_PARTNER = 1;
  // in case of document fetched by vendor by identifier passed by us is insufficient or incorrect
  INVESTOR_SUB_STATUS_DOCUMENT_VERIFICATION_FAILED = 2;
  // deprecated in favour of INVESTOR_SUB_STATUS_AGREEMENT_UPLOADED
  INVESTOR_SUB_STATUS_DOCUMENT_UPLOADED = 3;
  // investor agreement generation failed
  INVESTOR_SUB_STATUS_AGREEMENT_GENERATION_FAILED = 4;
  // investor agreement uploaded failed to vendor
  INVESTOR_SUB_STATUS_AGREEMENT_UPLOAD_FAILED = 5;
  // investor agreement uploaded successfully
  INVESTOR_SUB_STATUS_AGREEMENT_UPLOADED = 6;
  // eligibility failed for user
  INVESTOR_SUB_STATUS_ELIGIBILITY_FAILED = 7;
  // when user asks LL to directly delete the account
  INVESTOR_SUB_STATUS_ACCOUNT_CLOSED_AT_VENDOR = 8;
}

enum Vendor {
  VENDOR_UNSPECIFIED = 0;
  VENDOR_LIQUILOANS = 1;
}

enum InvestmentTransactionType {
  INVESTMENT_TRANSACTION_TYPE_UNSPECIFIED = 0;
  INVESTMENT_TRANSACTION_TYPE_INVESTMENT = 1;
  INVESTMENT_TRANSACTION_TYPE_WITHDRAWAL = 2;
  // REINVESTMENT represent investment transaction is created after user has reinvested his money on maturity
  // Deprecated: Use 'INVESTMENT_TRANSACTION_TYPE_REINVESTMENT' instead
  INVESTMENT_TRANSACTION_TYPE_MATURITY_REINVESTMENT_SAME_SCHEME = 3 [deprecated = true];
  // MATURITY_PAYOUT represents If the user cancel the reinvestment than complete money will be transfer to investor account
  INVESTMENT_TRANSACTION_TYPE_MATURITY_PAYOUT = 4;
  // MATURITY_INTEREST_PAYOUT only interest has been sent to investor account
  // this can happen when investor breach the limit of 10 lac and we only reinvest the principal amount than the interest will be paid out to investor
  INVESTMENT_TRANSACTION_TYPE_MATURITY_INTEREST_PAYOUT = 5;
  // MATURITY_WITHDRAWAL represents if the user does not take any action on an investment on maturity we will place a withdrawal request for that investor
  INVESTMENT_TRANSACTION_TYPE_MATURITY_WITHDRAWAL = 6;
  // REINVESTMENT transaction represent transactions that are reinvested as part of maturity of an investment transaction
  INVESTMENT_TRANSACTION_TYPE_MATURITY_REINVESTMENT = 7;
}

enum InvestmentTransactionStatus {
  INVESTMENT_TRANSACTION_STATUS_UNSPECIFIED = 0;
  // Transaction created in our system, not initiated with the investment vendor or payment partner bank.
  // For investments, this would mean transaction not initiated with partner bank
  // For withdrawal, this would mean transaction not initiated with investment vendor
  CREATED = 1;
  // Transaction initiated with either vendor or partner bank.
  // In case of investment this would mean initiated with partner bank.
  // In case of withdrawal this would mean initiated with investment vendor.
  INITIATED = 2;
  // Vendor or partner bank returned in progress status.
  IN_PROGRESS = 3;
  // Transaction approved at either the vendor or partner bank's end.
  // For Investment, this would mean txn is a success at partner bank.
  // For withdrawal, this would mean txn is a success at investment vendor
  APPROVED = 4;
  // Transaction approved at either vendor or partner bank, and settlement across the other party is in progress
  // For Investment, this would mean txn settlement started at vendor
  // For withdrawal, this would mean txn settlement/confirmation started at partner bank
  SETTLEMENT = 5;
  // Investment or withdrawal is a Success.
  // This is a terminal state.
  // For Investment, this would mean investment settled with the vendor, i.e. the vendor got the money.
  // For withdrawal, this would mean transaction settled with partner bank, i.e. the partner bank got the money.
  SUCCESS = 6;
  // Investment or withdrawal failed.
  // This is a terminal state.
  FAILED = 7;
  // Either the partner bank or investment vendor returned an UNKNOWN status
  UNKNOWN = 8;
  // This represents the user has again requested to renew investment with some different parameter than the existing through which this transaction was created
  // We need to mark the previous investment as inactive when new transaction is created
  INACTIVE = 9;
  // Transaction is first created with this, and if the user actually attempts the payment then it will be moved to created. If user abandons the payment before entering the pin, order would remain in this state
  // Usage - We don't need to show the transactions which the user have not performed any payment.
  CREATION_ON_HOLD = 10;
}

enum InvestmentTransactionSubStatus {
  INVESTMENT_TRANSACTION_SUB_STATUS_UNSPECIFIED = 0;
  // Investment is successful, all steps completed
  SUCCESS_INVESTMENT_COMPLETE = 1;
  // Withdrawal is successful, all steps completed
  SUCCESS_WITHDRAWAL_COMPLETE = 2;
  // Payment for investment is approved by partner bank
  APPROVED_INVESTMENT_PAYMENT = 3;
  // Withdrawal request is approved by investment vendor
  APPROVED_WITHDRAWAL = 4;
  // Settlement process yet to being at either partner bank or venodr
  SETTLEMENT_IN_PROGRESS = 5;
  // Transaction is in settlement status and is approved to be settled
  SETTLEMENT_APPROVED = 6;
  // Transaction is in settlement status and is executed
  SETTLEMENT_EXECUTED = 7;
  // Failed payment at partner bank for investment
  FAILED_PAYMENT = 8;
  // Failed withdrawal at investment vendor
  FAILED_WITHDRAWAL = 9;
  // Failed withdrawal at investment vendor
  // Due to 'Please withdraw full amount, which is' error
  FAILED_WITHDRAWAL_DUE_TO_WITHDRAW_FULL_AMOUNT = 14;
  // Failed settlement either at partner bank or investment vendor
  // For investment, this would would failed to settle txn at investment vendor
  // For withdrawal, this would mean failed to settle txn at partner bank
  FAILED_SETTLEMENT = 10;
  // Partner bank returned an unknown status while making payment for investment
  UNKNOWN_PAYMENT = 11;
  // Investment vendor returned an unknown status for withdrawal.
  UNKNOWN_WITHDRAWAL = 12;
  // Either the partner bank or the investment vendor returned an UNKNOWN status response.
  // For investment, this would would unknown status returned by investment vendor
  // For withdrawal, this would mean  unknown status returned by partner bank
  UNKNOWN_SETTLEMENT = 13;

  // 14
  // this sub status represents that investment has been successfully matured
  INVESTMENT_MATURED = 15;
  // we have not received any credit notification from bank yet
  // but as the eta breached we mark the transaction success and wait for the credit notification async
  CREDIT_NOTIFICATION_PENDING = 16;
  // this sub status represents that investment which should have been paid out has been reinvested into flexi scheme
  // this can happen due to error at vendor, or due to us missing to upload the maturity consent
  MATURITY_PAYOUT_TRANSACTION_REINVESTED_INTO_FLEXI = 17;
  // this sub status represents that investment which should have been reinvested in some scheme has been reinvested
  // into a different flexi scheme.
  // this can happen due to error at vendor, or due to us missing to upload the maturity consent.
  // we just fail the transaction and ask the user to place withdrawal at their side
  REINVESTMENT_TRANSACTION_REINVESTED_INTO_FLEXI_UNEXPECTEDLY = 18;
}

enum ActivityType {
  ACTIVITY_TYPE_UNSPECIFIED = 0;
  ACTIVITY_TYPE_INVESTMENT = 1;
  ACTIVITY_TYPE_WITHDRAWAL = 2;
  // to show maturity auto credit to customer's account
  ACTIVITY_TYPE_MATURITY = 3;
  ACTIVITY_TYPE_MATURITY_PAYOUT = 4;
  ACTIVITY_TYPE_INTEREST_PAYOUT = 5;
  ACTIVITY_TYPE_REINVESTMENT = 6;
  ACTIVITY_TYPE_MATURITY_WITHDRAWAL = 7;
}

enum ActivityStatus {
  ACTIVITY_STATUS_UNSPECIFIED = 0;
  // status for activities which will be processed in future like: maturity
  ACTIVITY_STATUS_PENDING = 1;
  ACTIVITY_STATUS_PROCESSING = 2;
  ACTIVITY_STATUS_SUCCESS = 3;
  ACTIVITY_STATUS_FAILED = 4;
  ACTIVITY_STATUS_PAYMENT_PENDING = 5;
  ACTIVITY_STATUS_PROCESSING_DELAYED = 6;
  ACTIVITY_STATUS_REQUESTED = 7;
  ACTIVITY_STATUS_CANCELLED = 8;
}

enum ActivityStageStatus {
  ACTIVITY_STAGE_STATUS_UNSPECIFIED = 0;
  ACTIVITY_STAGE_STATUS_PENDING = 1;
  ACTIVITY_STAGE_STATUS_PROCESSING = 2;
  ACTIVITY_STAGE_STATUS_SUCCESS = 3;
  ACTIVITY_STAGE_STATUS_FAILED = 4;
  // used to show when waiting for payment confirmation for investment
  ACTIVITY_STAGE_STATUS_PAYMENT_PENDING = 5;
  // used to show when eta has been crossed for activity stage
  ACTIVITY_STAGE_STATUS_PROCESSING_DELAYED = 6;
  ACTIVITY_STAGE_STATUS_REQUESTED = 7;
  ACTIVITY_STAGE_STATUS_CANCELLED = 8;
}

enum ActivityStage {
  ACTIVITY_STAGE_UNSPECIFIED = 0;
  ACTIVITY_STAGE_PAYMENT = 1;
  ACTIVITY_STAGE_INVESTMENT = 2;
  ACTIVITY_STAGE_WITHDRAW = 3;
  // this will be used to show that vendor has approved the withdrawal or not
  ACTIVITY_STAGE_WITHDRAW_CONFIRMATION = 4;
  // this will be used to show money deposit by vendor to customer's account
  ACTIVITY_STAGE_WITHDRAW_PAYMENT = 5;
  // this will be used to show the action taken by investor is in success state
  ACTIVITY_STAGE_INVESTMENT_RENEWAL_ACTION = 6;
  ACTIVITY_STAGE_INVESTMENT_RENEWAL = 7;
  ACTIVITY_STAGE_NON_RENEWAL_OF_INVESTMENT = 8;
  ACTIVITY_STAGE_INVESTMENT_AMOUNT_PAID = 9;
  ACTIVITY_STAGE_INTEREST_PAID = 10;
}

enum IneligibilityReason {
  INELIGIBILITY_REASON_UNSPECIFIED = 0;
  BALANCE = 1;
  FULL_KYC = 2;
  CKYC_STATUS = 3;
  EXISTING_INVESTOR = 4;
  CKYC_INCOMPLETE_DATA = 5;
}

enum SchemeIneligibilityReason {
  SCHEME_INELIGIBILITY_REASON_UNSPECIFIED = 0;
  SCHEME_INELIGIBILITY_REASON_BALANCE = 1;
  SCHEME_INELIGIBILITY_REASON_FULL_KYC = 2;
  SCHEME_INELIGIBILITY_REASON_TIERING = 3;
  SCHEME_INELIGIBILITY_REASON_MIN_INVESTMENT_ALLOWED_GREATER_THAN_AVAILABLE_LIMIT = 4;
}


enum DynamicInventoryFieldMask {
  DYNAMIC_INVENTORY_FIELD_MASK_UNSPECIFIED = 0;
  DYNAMIC_INVENTORY_FIELD_MASK_INVESTMENT_TRANSACTION_ID = 1;
  DYNAMIC_INVENTORY_FIELD_MASK_EXPIRY_DATE = 2;
  DYNAMIC_INVENTORY_FIELD_MASK_DETAILS = 3;
}

enum OpsFileType {
  OPS_FILE_TYPE_UNSPECIFIED = 0;
  OPS_FILE_TYPE_BUY_RECON = 1;
  OPS_FILE_TYPE_SELL_RECON = 2;
  OPS_FILE_TYPE_DEBUG_RECON = 3;
  // Includes all orders irrespective of status. Ignores 'Inactive' orders
  // Should only be used for very less number of days.
  // We should not rely on this file for debugging issues, and should always add that support in the 'DEBUG' file
  // Adding this for figuring out irregularities pointed out by our vendor and what is present in our systems
  OPS_FILE_TYPE_COMPREHENSIVE_RECON = 4;
  // Recon file for executing maturity transactions
  // Includes pending transactions for following types
  //  - INVESTMENT_TRANSACTION_TYPE_MATURITY_REINVESTMENT_SAME_SCHEME
  //  - INVESTMENT_TRANSACTION_TYPE_MATURITY_PAYOUT
  //  - INVESTMENT_TRANSACTION_TYPE_MATURITY_WITHDRAWAL
  OPS_FILE_TYPE_MATURITY_RECON = 5;
}

enum WithdrawalMethod {
  WITHDRAWAL_METHOD_UNSPECIFIED = 0;
  // if the user select penalty free amount in the enter amount withdrawal screen
  WITHDRAWAL_METHOD_PENALTY_FREE = 1;
}
enum MaturityActionStatus {
  MATURITY_ACTION_STATUS_UNSPECIFIED = 0;
  // CREATED status represents that user has intent to raise renew investment request in a specific scheme
  // User has shown intent to take renewal action. OTP screen has been shown to user
  // https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=11726%3A15473&t=JD1hwkhuEsOjnqfn-0
  MATURITY_ACTION_STATUS_CREATED = 1;
  // IN_PROGRESS status represents that Request to generate OTP have been placed to the vendor
  MATURITY_ACTION_STATUS_IN_PROGRESS = 2;
  // COMPLETED status represents that last request has been successfully raised with vendor to renew the investment
  //OTP has been verified by the vendor, and renewal action is registered
  MATURITY_ACTION_STATUS_COMPLETED = 3;
}

enum MaturityActionSubStatus {
  MATURITY_ACTION_SUB_STATUS_UNSPECIFIED = 0;
  // OTP_GENERATION_FAILED Unknown Failure in generate otp call. Probably 'service unavailable'
  MATURITY_ACTION_SUB_STATUS_OTP_GENERATION_FAILED = 1;
  // OTP_GENERATION_FAILED_INCORRECT_LINK_TOKEN the link token passed in request payload is not correct
  MATURITY_ACTION_SUB_STATUS_OTP_GENERATION_FAILED_INCORRECT_LINK_TOKEN = 2;
  // OTP_VERIFICATION_FAILED Unknown Failure in verify otp call. Probably 'service unavailable'
  MATURITY_ACTION_SUB_STATUS_OTP_VERIFICATION_FAILED = 3;
  // OTP_VERIFICATION_FAILED_INCORRECT_OTP otp verification failed as otp enter by investor is not correct
  MATURITY_ACTION_SUB_STATUS_OTP_VERIFICATION_FAILED_INCORRECT_OTP = 4;
  // OTP_VERIFICATION_FAILED_INCORRECT_OTP otp verification failed as link token passed in request payload is not correct
  MATURITY_ACTION_SUB_STATUS_OTP_VERIFICATION_FAILED_INCORRECT_LINK_TOKEN = 5;
  // OTP_VERIFICATION_FAILED_OTP_EXPIRED otp verification failed as otp enter by investor has expired
  MATURITY_ACTION_SUB_STATUS_OTP_VERIFICATION_FAILED_OTP_EXPIRED = 6;
  // REQUEST_RAISED_SUCCESS OTP has been verified by the vendor, and renewal action is registered
  MATURITY_ACTION_SUB_STATUS_REQUEST_RAISED_SUCCESS = 7;
}

enum MaturityActionFieldMask {
  MATURITY_ACTION_FIELD_MASK_UNSPECIFIED = 0;
  MATURITY_ACTION_FIELD_MASK_STATUS = 1;
  MATURITY_ACTION_FIELD_MASK_SUB_STATUS = 2;
}

enum MaturityActionType {
  MATURITY_ACTION_TYPE_UNSPECIFIED = 0;
  MATURITY_ACTION_TYPE_PRINCIPAL_AND_INTEREST_REINVESTMENT = 1;
  MATURITY_ACTION_TYPE_PRINCIPAL_REINVESTMENT_AND_INTEREST_REDEMPTION = 2;
  MATURITY_ACTION_TYPE_PRINCIPAL_AND_INTEREST_REDEMPTION = 3;
}

enum OtpStatus {
  OTP_STATUS_UNSPECIFIED = 0;
  // Success
  OK = 1;
  // Client specified an invalid argument
  INVALID_ARGUMENT = 2;
  // Internal error
  INTERNAL = 3;
  // Input token is expired and cannot use it
  // Generate new token
  TOKEN_EXPIRY = 4;
  // Otp is not correct
  OTP_INCORRECT = 5;
  // Otp ast attempt
  OTP_INCORRECT_LAST_ATTEMPT = 6;
  // Too many incorrect attempts on the token
  // Generate new token
  OTP_VERIFY_LIMIT_EXCEEDED = 7;
}

enum MaturityTransactionStatus {
  // investor has not taken any action for the maturity transaction
  MATURITY_TRANSACTION_STATUS_NO_MATURITY_ACTION_TAKEN = 0;
  // investor has successfully raise request for renew/cancel investment
  MATURITY_TRANSACTION_STATUS_REQUESTED = 1;
  // investor has taken another action on this maturity transaction
  MATURITY_TRANSACTION_STATUS_CANCELLED = 2;
}

enum SchemeStatus {
  //scheme is not specified as it is active or not
  SCHEME_STATUS_UNSPECIFIED = 0;
  // scheme is active for the investment to the users
  SCHEME_STATUS_ACTIVE = 1;
  // scheme is not active for the investment to the users
  SCHEME_STATUS_INACTIVE = 2;
}

// represents the status of document upload to vendor
enum DocumentUploadStatus {
  DOCUMENT_UPLOAD_STATUS_UNSPECIFIED = 0;
  // successfully uploaded
  DOCUMENT_UPLOAD_STATUS_UPLOADED = 1;
}

enum MaturityConsentFieldMask {
  MATURITY_CONSENT_FIELD_MASK_UNSPECIFIED = 0;
  MATURITY_CONSENT_FIELD_MASK_DOCUMENT_UPLOAD_STATUS = 1;
  MATURITY_CONSENT_FIELD_MASK_DOCUMENT_DETAILS = 2;
  MATURITY_CONSENT_FIELD_MASK_VENDOR_UPDATE_DETAILS = 3;
}

enum MaturityConsentType {
  MATURITY_CONSENT_TYPE_UNSPECIFIED = 0;
  // renew the investment perpetually after every maturity for a transaction
  MATURITY_CONSENT_TYPE_PERPETUAL_RENEWAL = 1;
  // renew the investment only one time after maturity, for the next maturity we need to take consent again
  MATURITY_CONSENT_TYPE_ONE_TIME_RENEWAL = 2;
  // after maturity, credit the amount to user's bank account
  MATURITY_CONSENT_TYPE_PAYOUT = 3;
}

// status of maturity action update on vendor side
enum MaturityActionVendorUpdateStatus {
  MATURITY_ACTION_VENDOR_UPDATE_STATUS_UNSPECIFIED = 0;
  MATURITY_ACTION_VENDOR_UPDATE_STATUS_UPDATED = 1;
}

enum CkycStatus {
  CKYC_STATUS_UNSPECIFIED = 0;
  // ckyc data exists for the user and is complete
  CKYC_STATUS_SUCCESS = 1;
  // ckyc record not found
  CKYC_STATUS_RECORD_NOT_FOUND = 2;
  // ckyc data is incomplete
  CKYC_STATUS_INCOMPLETE_DATA = 3;
}
