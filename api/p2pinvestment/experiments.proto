syntax = "proto3";

package p2pinvestment;

option go_package = "github.com/epifi/gamma/api/p2pinvestment";
option java_package = "com.github.epifi.gamma.api.p2pinvestment";

enum JumpEligibilityVariant {
  JUMP_ELIGIBILITY_VARIANT_UNSPECIFIED = 0;

  // variant for which different eligibility criteria
  // would be shown to different user groups
  JUMP_ELIGIBILITY_VARIANT_DEFAULT = 1;
  JUMP_ELIGIBILITY_VARIANT_A_BUCKET = 2;
  JUMP_ELIGIBILITY_VARIANT_B_BUCKET = 3;
}
