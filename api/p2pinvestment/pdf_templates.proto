syntax = "proto3";

package p2pinvestment;


option go_package = "github.com/epifi/gamma/api/p2pinvestment";
option java_package = "com.github.epifi.gamma.api.p2pinvestment";

// https://drive.google.com/file/d/1J6Ry7ng0n_dU1YM-hojxYv1Nj2x0t6q7/view
message MaturityConsentPdfParams {
  string letter_date = 1 [json_name = "letterDate"];
  string investor_name = 2 [json_name = "investorName"];
  string investor_id = 3 [json_name = "investorId"];
  string investment_date = 4 [json_name = "investmentDate"];
  string invested_amount = 5 [json_name = "investedAmount"];
  string scheme_id = 6 [json_name = "schemeId"];
  string indicated_yield = 7 [json_name = "indicatedYield"];
  string maturity_date = 8 [json_name = "maturityDate"];
  // possible values:
  // 1) PERPETUAL_CONSENT_FOR_PRINCIPAL_AND_INTEREST_REINVESTMENT
  // 2) PERPETUAL_CONSENT_TO_REDEEM_INTEREST_AND_REINVEST_PRINCIPAL
  // 3) ONE_TIME_CONSENT_FOR_PRINCIPAL_AND_INTEREST_REINVESTMENT
  // 4) ONE_TIME_CONSENT_TO_REDEEM_INTEREST_AND_REINVEST_PRINCIPAL
  // 5) REDEEM_BOTH_PRINCIPAL_AND_INTEREST
  string maturity_action = 9 [json_name = "maturityAction"];
  string letter_timestamp = 10 [json_name = "letterTimestamp"];
  string letter_ip_address = 11 [json_name = "letterIpAddress"];
  string re_investment_scheme_ids = 12 [json_name = "reInvestmentSchemeIds"];
}
