syntax = "proto3";

package api.webfe.deeplink_screen_option.secretanalyser;

import "api/typesv2/deeplink_screen_option/insights/secrets/component/secret_components.proto";
import "api/webfe/deeplink_screen_option/message.proto";
import "api/typesv2/deeplink_screen_option/header.proto";

option go_package = "github.com/epifi/gamma/api/webfe/deeplink_screen_option/secretanalyser";
option java_package = "com.github.epifi.gamma.api.webfe.deeplink_screen_option.secretanalyser";

// Helps in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message WebSecretAnalyserScreenOptions {
  // common header for all screen options
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;
  // common content for all screen options
  CommonOptions content = 2;
  // name of the secret analyser
  string secret_name = 3;
  // animation details for the loading screen
  typesv2.deeplink_screen_option.insights.secrets.component.SecretsAnimationDetails loading_animation_details = 4;
}
