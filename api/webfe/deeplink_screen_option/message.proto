syntax = "proto3";

package api.webfe.deeplink_screen_option;

option go_package = "github.com/epifi/gamma/api/webfe/deeplink_screen_option";
option java_package = "com.github.epifi.gamma.api.webfe.deeplink_screen_option";

// figma: https://www.figma.com/design/PcfTQa0Yf2FKy7OP7SvYXW/%F0%9F%A4%91-New-Lending-Workfile?node-id=8041-27152&t=ujMktj5IZfPyqjyR-4
message CommonOptions {
  // text to be shown on the screen
  string text = 1;
  // sub text to be shown on the screen
  string sub_text = 2;
  // image url to be shown on the screen
  string image_url = 3;
  // boolean value to show end to end encrypted text on the bottom of the screen
  bool show_end_to_end_encrypted_text = 4;
  // current step of the flow
  int32 current_step = 5;
  // progress of the current step in percentage in the flow
  int32 progress = 6;
  // boolean value to show header on the screen
  bool has_header = 7;
  // mobile image url to be shown on the screen. If this is not provided, then the image_url will be shown
  string mob_image_url = 8;
  // text to be shown on the button. If this is not provided, then the button will not be shown
  string button_text = 9;
  // icon to be shown on the button.
  string button_icon = 10;
  // html string to be shown on the bottom of the screen above the button.
  string bottom_text = 11;
}
