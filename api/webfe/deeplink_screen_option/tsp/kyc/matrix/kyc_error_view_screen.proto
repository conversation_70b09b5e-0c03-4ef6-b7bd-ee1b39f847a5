syntax = "proto3";

package api.webfe.deeplink_screen_option.tsp.kyc.matrix;

import "api/typesv2/deeplink_screen_option/header.proto";
import "api/typesv2/ui/icon_text_component.proto";
import "api/typesv2/common/visual_element.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/webfe/tsp/kyc/enums.proto";

option go_package = "github.com/epifi/gamma/api/webfe/deeplink_screen_option/tsp/kyc/matrix";
option java_package = "com.github.epifi.gamma.api.webfe.deeplink_screen_option.tsp.kyc.matrix";

// Help in packing and unpacking the proto object into java class of type Any.
// For reference : https://docs.google.com/document/d/1ehJb9518k5CJI30RT6hIDiw3VJwsbmTLYXNMcEJdujU/edit#
option java_multiple_files = true;

message KycErrorViewScreenOptions {
  message KycTypeCta {
    .webfe.tsp.kyc.KycType kyc_type = 1; // Type of KYC verification (e.g. "digilocker", "ckyc")
    frontend.deeplink.Cta cta = 2; // CTA for the KYC type
  }
  typesv2.deeplink_screen_option.ScreenOptionHeader header = 1;

  // Error Image
  typesv2.common.VisualElement header_image = 2;

  // Title - Ex : "Try again with a different method"
  typesv2.ui.IconTextComponent title = 3;

  // Subtitle - Ex : "DigiLocker verification failed due to technical issues"
  typesv2.ui.IconTextComponent sub_title = 4;

  // It will take to particular kyc type, can be ckyc, Digilocker
  KycTypeCta primary_cta = 5;
  // It will redirect to the multi kyc option scren
  frontend.deeplink.Cta secondary_cta = 6;

  // Background color for the screen
  string bg_color = 7;
}

