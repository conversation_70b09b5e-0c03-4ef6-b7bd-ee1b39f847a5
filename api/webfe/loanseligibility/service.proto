syntax = "proto3";

package webfe.loanseligibility;

import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/frontend/deeplink/deeplink.proto";
import "api/rpc/method_options.proto";
import "api/typesv2/money.proto";
import "api/typesv2/employment_type.proto";
import "api/typesv2/common/name.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/webfe/loanseligibility";
option java_package = "com.github.epifi.gamma.api.webfe.loanseligibility";

service LoansEligibility {
  // GetFiPromotionDetails returns the promotion details for the user
  rpc GetFiPromotionDetails (GetFiPromotionDetailsRequest) returns (GetFiPromotionDetailsResponse) {
    option (rpc.auth_required) = false;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetLoansLandingPageDetails returns details required to render the loans landing page
  rpc GetLoansLandingPageDetails (GetLoansLandingPageDetailsRequest) returns (GetLoansLandingPageDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // GetLoansNextEligibilityStep returns the next step the user needs to complete for loan eligibility
  rpc GetLoansNextEligibilityStep (GetLoansNextEligibilityStepRequest) returns (GetLoansNextEligibilityStepResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }

  // CollectUserDetails gathers basic and employment information from the user for loan eligibility
  rpc CollectUserDetails (CollectUserDetailsRequest) returns (CollectUserDetailsResponse) {
    option (rpc.auth_required) = true;
    option (rpc.device_registration_required) = false;
    option (rpc.savings_account_required) = false;
  }
}

message GetFiPromotionDetailsRequest {
  frontend.header.RequestHeader req = 1;
  // Query string to fetch the promotion details (e.g., "?utm=123&source=google")
  string query_string = 2;
}

message GetFiPromotionDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;

  message PromotionCard {
    // Title of the promotion card
    string title = 1;
    // Description of the promotion card
    string description = 2;
    // URL of the promotion image
    string image_url = 3;
  }

  message PromotionDetails {
    // Title of the promotion
    string title = 1;
    // Description of the promotion in stringified HTML
    string description = 2;
    // List of promotion cards
    repeated PromotionCard cards = 3;
  }

  // Contains details of the promotion
  PromotionDetails promotion_details = 2;
}

message GetLoansLandingPageDetailsRequest {
  frontend.header.RequestHeader req = 1;
  // Query string to track the source and flow (e.g., "?utm=123&source=google")
  string query_string = 2;
}

message GetLoansLandingPageDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Deeplink to navigate to the loans landing page with specific screen options
  frontend.deeplink.Deeplink deeplink = 2;
}

message GetLoansNextEligibilityStepRequest {
  frontend.header.RequestHeader req = 1;
  // Query string to track the source and flow (e.g., "?utm=123&source=google")
  string query_string = 2;
  // ID of the loan request for which the next eligibility step is to be fetched, if applicable
  // Get this from the screen options or the previous RPC response
  string loan_request_id = 3;
}

message GetLoansNextEligibilityStepResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Deeplink to navigate to the next eligibility step screen
  frontend.deeplink.Deeplink deeplink = 2;
}

message CollectUserDetailsRequest {

  message BasicUserDetails {
    // Name of the user as per PAN card
    api.typesv2.common.Name name = 1;
    // Date of birth of the user in the format "YYYY-MM-DD"
    google.type.Date dob = 2;
    // PAN number of the user
    string pan = 3;
  }

  message EmploymentDetails {
    // Employment type of the user (e.g., salaried, self-employed)
    api.typesv2.EmploymentType employment_type = 1;
    // Monthly income of the user
    api.typesv2.Money monthly_income = 2;
    // PIN code of the user's current address
    string pincode = 3;
  }

  message PreEligibilityConsent {
    repeated string consent_ids = 1;
  }

  frontend.header.RequestHeader req = 1;
  // Loan Request ID for the ongoing pre-eligibility process.
  // This ties the submitted data to a specific evaluation flow.
  string request_id = 2;

  // Either basic user details or employment details must be provided
  oneof details {
    BasicUserDetails basic_user_details = 3;
    EmploymentDetails employment_details = 4;
    PreEligibilityConsent pre_eligibility_consent = 5;
  }

  // Indicates the originating screen from which this RPC was invoked, for tracking purposes
  frontend.deeplink.Screen deeplink_screen = 6;
}

message CollectUserDetailsResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Deeplink to navigate to the screen corresponding to the next step after collecting user details
  frontend.deeplink.Deeplink deeplink = 2;
}
