syntax = "proto3";

package webfe.travel;

import "api/frontend/header/request.proto";
import "api/frontend/header/response.proto";
import "api/rpc/method_options.proto";

import "api/webfe/travel/enums.proto";
import "api/webfe/travel/message.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/webfe/travel";
option java_package = "com.github.epifi.gamma.api.webfe.travel";

service TravelBudget {
    // get the available travel destinations
    rpc GetTravelDestinations (GetTravelDestinationsRequest) returns (GetTravelDestinationsResponse) {
      option (rpc.auth_required) = false;
      option (rpc.device_registration_required) = false;
      option (rpc.savings_account_required) = false;
    }

    // get the travel expense budget for a given country
    rpc GetTravelExpense (GetTravelExpenseRequest) returns (GetTravelExpenseResponse) {
      option (rpc.auth_required) = false;
      option (rpc.device_registration_required) = false;
      option (rpc.savings_account_required) = false;
    }

    // get the international ATM withdrawal limits
    rpc GetInternationalATMWithdrawalLimits (GetInternationalATMWithdrawalLimitsRequest) returns (GetInternationalATMWithdrawalLimitsResponse) {
      option (rpc.auth_required) = false;
      option (rpc.device_registration_required) = false;
      option (rpc.savings_account_required) = false;
    }

    // get the forex exchange rate
    rpc GetForexExchangeRate (GetForexExchangeRateRequest) returns (GetForexExchangeRateResponse) {
      option (rpc.auth_required) = false;
      option (rpc.device_registration_required) = false;
      option (rpc.savings_account_required) = false;
    }
}

message GetTravelDestinationsRequest {
    frontend.header.RequestHeader req = 1;
}

message GetTravelDestinationsResponse {
    // define the TravelDestinations message
    message TravelDestinations {
        // list of countries as destinations
        repeated Country destinations = 1;
        // list of popular destinations
        repeated PopularDestinations popular_destinations = 2;
    }

    frontend.header.ResponseHeader resp_header = 1;
    TravelDestinations travel_destinations = 2;
}

message GetTravelExpenseRequest {
    message ExpenseData {
        // country identifier
        string country = 1;
        // start date of the travel
        google.protobuf.Timestamp start_date = 2;
        // end date of the travel
        google.protobuf.Timestamp end_date = 3;
        // number of people traveling
        int32 people_count = 4;
        // travel style (e.g., economic, luxury)
        TravelStyle travel_style = 5;
    }
    frontend.header.RequestHeader req = 1;
    // expense data for the travel
    ExpenseData expense_data = 2;
}

message GetTravelExpenseResponse {

    message TravelExpenseBudget {
        // list of daily expenses
        repeated DailyExpense daily_expenses = 1;
        // list of travel offers
        repeated TravelOffer travel_offers = 2;
        // selected country information
        Country country = 3;
        // estimated expense amount per day
        double unit_total_estimate_expense_amount = 4;
        // currency conversion rate for the destination against the indian currency
        double currency_conversion_rate = 5;
        // number of travel days
        int32 travel_days = 6;
        // native currency of the destination
        string destination_native_currency = 7;
        // image of the country
        Image country_image = 8;
        // start date of the travel
        google.protobuf.Timestamp start_date = 9;
        // end date of the travel
        google.protobuf.Timestamp end_date = 10;
        // number of people traveling
        int32 people_count = 11;
        // deep link to redirect to app to configure spend limit
        Cta cta = 12;
        // travel style (e.g., economic, luxury)
        TravelStyle travel_style = 13;
        // total savings amount using DC
        double total_savings_amount = 14;
        // zero markup savings amount using DC
        double zero_markup_savings_amount = 15;
        // card offers savings amount using DC
        double card_offers_savings_amount = 16;
        // symbol of the native currency
        string native_currency_symbol = 17;
    }

    frontend.header.ResponseHeader resp_header = 1;
    TravelExpenseBudget expense_budget = 2;
}

message GetInternationalATMWithdrawalLimitsRequest {
  frontend.header.RequestHeader req = 1;
}

message GetInternationalATMWithdrawalLimitsResponse {

  message ATMLimit {
    // Country name
    string country_name = 1;
    // Country flag as emoji
    string country_flag = 2;
    // ATM withdrawal limit
    google.type.Money atm_limit = 3;
  }

  frontend.header.ResponseHeader resp_header = 1;
  // Atm withdrawal limit for different countries
  repeated ATMLimit atm_limits = 2;
}

message GetForexExchangeRateRequest {
  frontend.header.RequestHeader req = 1;
  // Country code of the country from which the currency is to be converted
  string from_country_code = 2;
  // Country code of the country to which the currency is to be converted
  string to_country_code = 3;
  // Amount to be converted
  google.type.Money amount = 4;
}

message GetForexExchangeRateResponse {
  frontend.header.ResponseHeader resp_header = 1;
  // Forex converted amount
  google.type.Money converted_amount = 2;
  // Forex savings amount
  google.type.Money forex_savings_amount = 3;
  // Card offers amount
  google.type.Money card_offers_amount = 4;
}
