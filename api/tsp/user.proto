syntax = "proto3";

package tsp;

option go_package = "github.com/epifi/gamma/api/tsp";
option java_package = "com.github.epifi.gamma.api/tsp";

import "validate/validate.proto";

// The user will contain all info of cutomers registered at tsp it will provide the either some unique identifer of customer or some
// ref identifer which will be used to get the unique identifier.
message User {
    oneof id {
        // in case we already know the actor id as registered at tsp.
        string actor_id = 1 [(validate.rules).string.min_len = 1];
        // In case we don't have the actor id we will send some customer ref id which was passed while creating actor at tsp's end.
        string customer_ref_id = 2 [(validate.rules).string.min_len = 1];
    }
}
