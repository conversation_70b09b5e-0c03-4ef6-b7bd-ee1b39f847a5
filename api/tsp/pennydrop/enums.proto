syntax = "proto3";

package tsp.pennydrop;

option go_package = "github.com/epifi/gamma/api/tsp/pennydrop";
option java_package = "com.github.epifi.gamma.api.tsp.pennydrop";

enum PennyDropStatus {
    PENNY_DROP_STATUS_UNSPECIFIED = 0;
    PENNY_DROP_STATUS_IN_PROGRESS = 1;
    PENNY_DROP_STATUS_SUCCESSFUL = 2;
    PENNY_DROP_STATUS_FAILED = 3;
    // pending status will be used in cases where penny drop is taking longer than expected,
    // and we need to redirect to business unit's next action post retry attempts are exhausted.
    PENNY_DROP_STATUS_PENDING = 4;
    // this will be sent in cases where penny drop is still not initiated.
    PENNY_DROP_STATUS_RECORD_NOT_FOUND = 5;
}
