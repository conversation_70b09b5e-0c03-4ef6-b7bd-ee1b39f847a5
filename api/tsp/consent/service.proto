syntax = "proto3";

package tsp.consent;

option go_package = "github.com/epifi/gamma/api/tsp/consent";
option java_package = "com.github.epifi.gamma.api.tsp.consent";

import "api/tsp/user.proto";
import "api/typesv2/common/device.proto";
import "api/tsp/consent/enums.proto";
import "google/protobuf/timestamp.proto";
import "api/rpc/status.proto";

service Consent{
    // Records multiple consents for the actor with timestamp
    rpc RecordConsents (RecordConsentsRequest) returns (RecordConsentsResponse);

    // Fetches the the actor's consent details by client_req_id
    rpc FetchConsentByReqId (FetchConsentByReqIdRequest) returns (FetchConsentByReqIdResponse);
}

message RecordConsentsRequest{
    repeated ConsentReqInfo consents = 1;
    // User for which consent has to be recorded.
    User user = 2;
    // Device fingerprint from where actor has given the consent
    api.typesv2.common.Device device = 3;
}

message ConsentReqInfo{
    // Type of Consents that the actor has provided
    ConsentType consent_type = 1;


    // expiration time of the consent. for non-expiring consents this field should be passed as nil.
    google.protobuf.Timestamp expires_at = 2;

    // Request ID sent by client
    string client_req_id = 3;
}


message RecordConsentsResponse{
    rpc.Status status = 1;

    // list of consent ids along with its consent type
    repeated ConsentResponseInfo consent_responses = 2;
}

message ConsentResponseInfo{
    // Type of Consent that the actor has provided
    ConsentType consent_type = 1;
    string consent_id = 2;
}

message FetchConsentByReqIdRequest{
    string client_req_id = 1;
}

message FetchConsentByReqIdResponse{
    rpc.Status status = 1;
    // Timestamp at which consent has been given by the actor
    google.protobuf.Timestamp created_at = 2;
    // expiration time of the consent. for non-expiring consents this field will be nil.
    google.protobuf.Timestamp expires_at = 3;
    ConsentType consent_type = 4;
}
