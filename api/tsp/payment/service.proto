syntax = "proto3";

package tsp.payment;

import "api/rpc/status.proto";
import "api/tsp/bank_account_details.proto";
import "api/tsp/payment/enums.proto";
import "api/tsp/user.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "validate/validate.proto";

option go_package = "github.com/epifi/gamma/api/tsp/payment";
option java_package = "com.github.epifi.gamma.api.tsp.payment";

// This service is used for making B2B fund transfer.
service Payment {
  // Initiates the fund transfer from the payer to payee account.
  rpc InitiateFundTransfer (InitiateFundTransferRequest) returns (InitiateFundTransferResponse) {};
  // Fetches the fund transfer details for the given payment request.
  rpc GetFundTransferDetails (GetFundTransferDetailsRequest) returns (GetFundTransferDetailsResponse) {}
}

message InitiateFundTransferRequest {
  // payer details to uniquely identify it.
  User payer = 1;
  // payee details to uniquely identify it.
  User payee = 2;
  // Amount of money involved in the transaction
  google.type.Money amount = 3 [(validate.rules).message.required = true];
  // bank account details from which asset need to be debited
  BankAccountDetails payer_bank_account_details = 4;
  // bank account details to which asset needs to be credited
  BankAccountDetails payee_bank_account_details = 5;
  // Will be used to get payment details.
  string client_request_id = 6;
}

message InitiateFundTransferResponse {
  enum Status {
    // fund transfer request registered successfully
    OK = 0;
    // fund transfer request already exists for the given client request id
    ALREADY_EXISTS = 6;
    // internal server error while registering fund transfer request
    // the client should regenerate a fresh request to make a payment in this case
    INTERNAL = 13;
  }
  rpc.Status status = 1;
}


message GetFundTransferDetailsRequest {
  oneof identifier {
    string external_id = 1;
    string client_req_id = 2;
  }
}

message GetFundTransferDetailsResponse {
  enum Status {
    OK = 0;
  }
  rpc.Status status = 1;
  PaymentDetails payment_details = 2;
}

message PaymentDetails {
  // actor who initiated the order
  string from_actor_id = 1;
  // receiving entity of the order or the service provider
  string to_actor_id = 2;
  PaymentStatus status = 3;
  // Final payment amount. (Incl. of all taxes and service charges)
  google.type.Money amount = 4;
  // payment creation timestamp
  google.protobuf.Timestamp created_at = 5;
  string external_id = 6;
  string client_req_id = 7;
  // The order UTR number for the payment
  string utr = 8;
  // the protocol with which payment was made
  PaymentProtocol payment_protocol = 9;
  // timestamp at which the actual money transfer happened
  google.protobuf.Timestamp txn_timestamp = 10;
}
