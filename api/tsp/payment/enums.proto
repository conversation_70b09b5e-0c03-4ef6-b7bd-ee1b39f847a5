syntax = "proto3";

package tsp.payment;

option go_package = "github.com/epifi/gamma/api/tsp/payment";
option java_package = "com.github.epifi.gamma.api.tsp.payment";

enum PaymentStatus {
  PAYMENT_STATUS_UNSPECIFIED = 0;

  // Entry created in the system.
  PAYMENT_STATUS_CREATED = 1;

  // Attempting payment for the order.
  PAYMENT_STATUS_IN_PROGRESS = 2;

  // Payment is successful.
  PAYMENT_STATUS_SUCCESS = 3;

  // Payment attempt failed for the order.
  PAYMENT_STATUS_FAILED = 4;

  // The payment for order is reversed.
  // For eg. in case of fund transfer the order can be reversed after a successful debit
  // in case the credit fails
  // An order can move into PAYMENT_REVERSED state even from PAYMENT_FAILED/PAID state.
  PAYMENT_STATUS_REVERSED = 5;

  // Payment attempt is stuck and needs some manual intervention.
  PAYMENT_STATUS_MANUAL_INTERVENTION = 6;
}

enum PaymentProtocol {
  PAYMENT_PROTOCOL_UNSPECIFIED = 0;
  INTRA_BANK = 1;
  NEFT = 2;
  IMPS = 3;
  RTGS = 4;
  UPI = 5;
  ENACH = 6;
}
