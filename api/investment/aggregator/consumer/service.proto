//go:generate gen_queue_pb
syntax = "proto3";

package api.investment.aggregator.consumer;

import "api/investment/instrument.proto";
import "api/queue/consumer_headers.proto";
import "api/investment/mutualfund/external/mutual_fund_external_request.proto";
import "api/investment/mutualfund/order/order.proto";
import "api/p2pinvestment/enums.proto";
import "api/p2pinvestment/scheme.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/investment/aggregator/consumer";
option java_package = "com.github.epifi.gamma.api.investment.aggregator.consumer";

// Defines the GRPC services to process sqs messages for investment aggregator
service InvestmentAggregatorConsumer {
  // ProcessInvestmentEvent rpc helps to publish events when ever any action is taken on
  // the investment instrument by a user. This rpc would convert an instrument update event to an investment update event
  // and pushes to a common SNS topic from where different clients can consume these events.
  // For eg: MutualFundInvested instrument event can be converted to 2 or more investment update events, like
  //  MutualFundInvestmentDone and AnyInvestmentDone
  rpc ProcessInvestmentEvent(ProcessInvestmentEventRequest) returns (ProcessInvestmentEventResponse);
}


message ProcessInvestmentEventRequest {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerRequestHeader request_header = 1;
  investment.InvestmentInstrumentType instrument_type = 2;
  // These field numbers were being used by fields that are deprecated and removed now. Hence marking these as reserved.
  reserved 3, 4, 5;
  oneof instrument_data {
    // Events that represents that there is an update in mutual funds external order data.
    MutualFundExternalInstrumentData mf_external_instrument_data = 6;

    SDInvestmentData sd_investment_data = 7;
    SDSubscriptionData sd_subscription_data = 8;
    // Represents template level aggregated data for an SD for an actor.
    SDTemplateAggregatedData sd_template_aggregated_data = 9;

    FDInvestmentData fd_investment_data = 10;

    MFInvestmentData mf_investment_data = 11;
    MFSubscriptionData mf_subscription_data = 12;

    P2PInvestmentData p2p_investment_data = 13;

    USStocksInvestmentData us_stocks_investment_data = 14;

    JumpMaturityActionData jump_maturity_action_data = 15;
  }
}

message ProcessInvestmentEventResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}


message MutualFundExternalInstrumentData{
  string actor_id = 1;
  mutualfund.external.Provenance provenance = 2;
  // MF external orders import request external id
  string import_request_external_id = 3;
  // updated_at of external order import request while pushing the event
  google.protobuf.Timestamp updated_at = 4;
}

message SDInvestmentData {
  string actor_id = 1;
  string deposit_id = 2;
  SDInstrumentInvestmentTye type = 5;
}

enum SDInstrumentInvestmentTye {
  SDInvestmentTye_UNSPECIFIED = 0;
  SDInvestmentTye_CREATE_NEW_ACCOUNT = 1;
  SDInvestmentTye_ADD_FUNDS_TO_EXISTING_ACCOUNT = 2;
}

message SDSubscriptionData {
  string actor_id = 1;
  string rule_id = 2;
}

message SDTemplateAggregatedData {
  string template_id = 1;
}

message FDInvestmentData {
  string actor_id = 1;
  string deposit_id = 2;
}

message MFInvestmentData {
  string actor_id = 1;
  string order_id = 2;
  mutualfund.order.OrderStatus order_status = 3;
}

message MFSubscriptionData {
  string actor_id = 1;
  string rule_id = 2;
}

message P2PInvestmentData {
  string actor_id = 1;
  string investment_transaction_id = 2;
  p2pinvestment.InvestmentTransactionStatus status = 3;
  google.type.Money amount = 4;
  string scheme = 5;

}

message USStocksInvestmentData {
  string actor_id = 1;
  string order_id = 2;
}

message MutualFundInstrumentData {
  string order_id = 1[deprecated = true];
  string actor_id = 2;
  oneof id {
    string mf_order_id = 3;
    string fit_rule_id = 4;
  }
}

enum InvestmentState {
  InvestmentState_UNSPECIFIED = 0;
  // Used to represent cases when a user has subscribed for recurring investments via services like FIT.
  InvestmentState_INVESTMENT_SUBSCRIBED = 1;
  // Used to represent cases when a user as started an Investment.
  // For eg: For mutual funds, this represents the creation of an order.
  InvestmentState_INVESTMENT_PENDING = 3;
  // Used to represent if a user was able to successfully do an investment.
  // For eg: For mutual funds, this could mean the order moving to a successful terminal state.
  InvestmentState_INVESTMENT_DONE = 2;
}

message SDInstrumentData {
  string actor_id = 1;
  oneof id {
    string deposit_id = 2;
    string template_id = 3;
    string fit_rule_id = 4;
  }
}

message FDInstrumentData {
  string actor_id = 1;
  oneof id {
    string deposit_id = 2;
    string template_id = 3;
  }
}

// Maturity action is the action taken by the user to modify what happens with the investment once it matures
message JumpMaturityActionData {
  string actor_id = 1;
  // original investment transaction id which is going to mature
  string original_transaction_id = 2;
  p2pinvestment.SchemeName scheme = 3;
  p2pinvestment.MaturityActionType maturity_action_type = 4;
}
