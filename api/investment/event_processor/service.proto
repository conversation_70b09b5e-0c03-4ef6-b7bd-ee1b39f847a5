//go:generate gen_queue_pb
syntax = "proto3";

package api.investment.event_processor;

import "api/investment/event_processor/user_journey.proto";
import "api/queue/consumer_headers.proto";
import "api/rpc/status.proto";
import "api/typesv2/common/ownership.proto";

option go_package = "github.com/epifi/gamma/api/investment/event_processor";
option java_package = "com.github.epifi.gamma.api.investment.event_processor";

// EventProcessor is used to process client and backend events to perform tasks like driving user engagement to avoid drop-offs via notifications
service EventProcessor {
  // MapEventsToUserJourneys receives events from different services, maps them to a user journeys and forwards them to consumers for further processing
  // Only events that are required to be processed should be sent to this RPC
  rpc MapEventsToUserJourneys(MapEventsToUserJourneysRequest) returns (MapEventsToUserJourneysResponse);

  // SendUserJourneyNotifications sends notifications scheduled by MapEventsToUserJourneys
  rpc SendUserJourneyNotifications(SendUserJourneyNotificationsRequest) returns (SendUserJourneyNotificationsResponse);
}

message MapEventsToUserJourneysRequest {
  string actor_id = 1;

  // name of the event
  // e.g. USSDetailsPageLoaded when the US stocks details page is loaded
  // Only certain events are supported, for the complete list refer InvestmentEvent
  InvestmentEvent investment_event = 2;

  // Map of all event properties and their values
  // properties can be mutual fund id, stock id, etc.
  map<string, string> event_properties = 3;

  // Ownership is used to decide the database for storing/retrieving user journey records
  api.typesv2.common.Ownership ownership = 4;
}

message MapEventsToUserJourneysResponse {
  enum Status {
    OK = 0;

    // Internal Server Error
    INTERNAL = 13;

    // Event is skipped from processing
    EVENT_SKIPPED = 101;
  }
  rpc.Status status = 1;
}

message SendUserJourneyNotificationsRequest {
  queue.ConsumerRequestHeader request_header = 1;

  string actor_id = 2;

  JourneyType journey_type = 3;

  // Ownership is used to decide the database for storing/retrieving user journey records
  api.typesv2.common.Ownership ownership = 4;
}

message SendUserJourneyNotificationsResponse {
  queue.ConsumerResponseHeader response_header = 1;
}

enum InvestmentEvent {
  INVESTMENT_EVENT_UNSPECIFIED = 0;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3395%3A14745&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_LANDING_SCREEN_LOADED = 1;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3388%3A11427&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_SEARCH_LANDING_SCREEN_LOADED = 2;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3388%3A12328&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_SEARCH_RESULTS_SCREEN_LOADED = 3;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=3939%3A18319&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_COLLECTION_SCREEN_LOADED = 4;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1196%3A5224&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_STOCK_DETAILS_SCREEN_LOADED = 5;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A3551&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_AVAILABLE_DOCS_SCREEN_LOADED = 6;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A3578&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_AVAILABLE_DOCS_SUBMITTED = 7;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=335%3A5524&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_PAN_UPLOAD_SCREEN_LOADED = 8;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2341&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_DISCLOSURES_SCREEN_LOADED = 9;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A2727&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_EMPLOYMENT_DETAILS_SCREEN_LOADED = 10;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=861%3A3894&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_INVESTMENT_PLAN_SCREEN_LOADED = 11;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=330%3A3222&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_TNC_SCREEN_LOADED = 12;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=4003%3A17821&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_BUY_BUTTON_CLICKED = 13;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1013%3A4586&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_INPUT_BUY_AMOUNT_SCREEN_LOADED = 14;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1013%3A4594&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_INPUT_BUY_AMOUNT_SCREEN_ACTION_TAKEN = 15;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1013%3A4462&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_BUY_SUMMARY_SCREEN_LOADED = 16;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=1013%3A4466&t=ddpi8YlwxbsGBouc-4
  INVESTMENT_EVENT_US_STOCKS_SWIPED_TO_BUY = 17;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?node-id=7003-23039&t=82o9R78Ea8m45FSz-4
  INVESTMENT_EVENT_US_STOCKS_SOURCE_OF_FUNDS_SCREEN_LOADED = 18;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-11562&t=asVkojb0z1mzwJ0m-0
  INVESTMENT_EVENT_STATE_JUMP_INTRO_SCREEN_VISITED = 19;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-11260&t=jh8XP2y7YDJqPKex-0
  INVESTMENT_EVENT_STATE_JUMP_CHOOSE_PLAN_SCREEN_VISITED = 20;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  INVESTMENT_EVENT_STATE_JUMP_INVEST_SCREEN_VISITED = 21;

  // ref: https://www.figma.com/file/O54XOGtRfQoewjucrrmbvF/%F0%9F%9A%80-Jump-P2P-%2F-FFF?node-id=8590-12974&t=trzAI6RBrX7ajmfT-0
  INVESTMENT_EVENT_STATE_JUMP_SWIPED_TO_INVEST = 22;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=20995-3972&mode=dev
  INVESTMENT_EVENT_USS_CREATE_RISK_PROFILE_VISITED = 23;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21011-26228&mode=dev
  INVESTMENT_EVENT_USS_COLLECT_RISK_LEVEL_VISITED = 24;

  // ref: https://www.figma.com/file/7fVdk00uRcBEghRYg6Wk9m/%F0%9F%9A%80-US-stocks-%2F-FFF?type=design&node-id=21375-12544&mode=dev
  INVESTMENT_EVENT_USS_RISK_DISCLOSURE_VISITED = 25;

}
