syntax = "proto3";

package api.investment.mutualfund.reconciliation;

import "api/vendorgateway/vendor.proto";
import "api/rpc/status.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/reconciliation";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund.reconciliation";


service ReconciliationService {
  // this rpc takes in a file and uploads them in the required s3 bucket.
  // s3 path will be RTA/ReconciliationFile/ReconciliationFile/FileName
  rpc ReconciliationFileUpload(ReconciliationFileUploadRequest) returns (ReconciliationFileUploadResponse){}
  // this rpc takes in an S3 path for a reverse feed file and generates a report of the details of mismatch if any.
  rpc ReconcileOrdersFromReverseFeedFile(ReconcileOrdersFromReverseFeedFileRequest) returns (ReconcileOrdersFromReverseFeedFileResponse) {}
  // this rpc takes in an S3 path for a reverse feed file and an nav data file. It returns a report with details of
  // orders for which nav was wrongly allocated. This rpc doesn't update any data in the database.
  rpc ReconcileNavData(ReconcileNavDataRequest) returns (ReconcileNavDataResponse) {}
  // this rpc takes in an S3 path for a folio update report generated by ReconcileOrdersFromReverseFeedFile.
  // Each row in the report consists of order related data. This data is updated in mf_orders table and is used
  // to reconcile folios.
  rpc ProcessOrderReconciliationReport(ProcessOrderReconciliationReportRequest) returns (ProcessOrderReconciliationReportResponse) {}
}

message ReconciliationFileUploadRequest {
  vendorgateway.Vendor rta = 1;
  ReconciliationType reconciliation_type = 2;
  string file_name = 3;
  bytes file_content = 4;
}

message ReconciliationFileUploadResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  string file_path = 2;
}

message ReconcileOrdersFromReverseFeedFileRequest{
  vendorgateway.Vendor rta = 1;
  string file_path = 2;
}

message ReconcileOrdersFromReverseFeedFileResponse{
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  string report_url = 3;
}

message ReconcileNavDataRequest {
  vendorgateway.Vendor rta = 1;
  // revers_feed_file_path is needed for getting the nav value allocated for the orders.
  string revers_feed_file_path = 2;
  // nav_data_file_path is needed for getting the actual nav value for different funds.
  string nav_data_file_path = 3;
}

message ReconcileNavDataResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }
  // rpc response status
  rpc.Status status = 1;
  repeated NavReconciliationDetails nav_reconciliation_details= 2;
}

message NavReconciliationDetails {
  string vendor_order_id = 1;
  string scheme_code = 2;
  string allocation_date = 3;
  double allocated_nav = 4;
  double actual_nav = 5;
  string remarks = 6;
}

enum ReconciliationType {
  RECONCILIATION_UNSPECIFIED = 0;
  // ORDER_RECONCILIATION is correcting order related parameters like unit and amount if there is any difference in the
  // latest data available with the rta and fi. If any order has this different, the respective folio will also be corrected.
  ORDER_RECONCILIATION = 1;
  // NAV_RECONCILIATION checks if the nav allocated for each order for a given date with the actual nav data
  NAV_RECONCILIATION = 2;
  // FOLIO_RECONCILIATION_FROM_REPORT reconciles order from a report that gets generated as part of ReconcileOrdersFromReverseFeedFile.
  FOLIO_RECONCILIATION_FROM_REPORT = 3;
}

message ProcessOrderReconciliationReportRequest {
  vendorgateway.Vendor rta = 1;
  // report generated by the ReconcileOrdersFromReverseFeedFile
  string report_file_path = 2;
}

message ProcessOrderReconciliationReportResponse {
  enum Status {
    OK = 0;
    // System faced internal errors while processing the request
    INTERNAL = 13;
  }

  // rpc response status
  rpc.Status status = 1;
}
