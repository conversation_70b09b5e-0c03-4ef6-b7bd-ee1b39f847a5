syntax = "proto3";

package api.investment.mutualfund.payment_handler.consumer;

import "api/order/order.proto";
import "api/queue/consumer_headers.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/payment_handler/consumer";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund.payment_handler/consumer";

// Defines the GRPC service to process order updates for investment orders
service PaymentUpdateEventProcessor {
  // UpdatePaymentStatusFromOMS would consume the orderUpdate events for Investment payment orders
  // and update the corresponding payment status
  rpc UpdatePaymentStatusFromOMS(order.OrderUpdate) returns (UpdatePaymentStatusFromOMSResponse);
}

message UpdatePaymentStatusFromOMSResponse {
  // A set of all the common attributes to be contained in a queue consumer response
  queue.ConsumerResponseHeader response_header = 1;
}
