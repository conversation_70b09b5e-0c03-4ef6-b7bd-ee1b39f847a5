syntax = "proto3";

package api.investment.mutualfund.payment_handler.standing_instructions;

import "google/type/money.proto";
import "api/investment/mutualfund/payment_handler/standing_instructions/service.proto";
import "api/vendorgateway/vendor.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/payment_handler/standing_instructions";

/*
  This entity represents a standing instruction that is created from a payment instrument owned by a user to a payment
  instrument owned by an AMC.
*/
message StandingInstruction {
  string id = 1;
  string from_actor_id = 2;
  string to_actor_id = 3;
  string from_payment_instrument_id = 4;
  string to_payment_instrument_id = 5;
  google.type.Money maximum_amount =6;
  string standing_instruction_id = 7;
  string transaction_id = 8;
  string recurring_payment_id = 9;
  standing_instructions.SIState state = 10;
  vendorgateway.Vendor partner_bank = 11;
  google.protobuf.Timestamp created_at = 12;
  google.protobuf.Timestamp updated_at = 13;
  google.protobuf.Timestamp deleted_at = 14;
}
