syntax = "proto3";

package api.investment.mutualfund.payment_handler;

import "google/type/money.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/payment_handler";

enum PaymentStatus {
  // PAYMENT_STATUS_UNSPECIFIED denotes that a payment entry is created in mf_payments table. Downstream call to pay services has not been made yet.
  PAYMENT_STATUS_UNSPECIFIED = 0;
  // PAYMENT_STATUS_PENDING denotes that downstream call to pay services has been done and payment is in pending state. This is applicable for async payments.
  PAYMENT_STATUS_PENDING = 1;
  // PAYMENT_STATUS_SUCCESSFUL denotes that payment is successful
  PAYMENT_STATUS_SUCCESSFUL = 2;
  // PAYMENT_STATUS_FAILED denotes that payment has failed.
  PAYMENT_STATUS_FAILED = 3;
}

enum PaymentMode {
  PAYMENT_MODE_UNSPECIFIED = 0;
  // Payment mode for enabling pre-authorised auto debits via Standing Instructions
  PAYMENT_MODE_SI = 1;
  // Payment mode for enabling lump sum payments via OMS's P2P_FUND_TRANSFER workflow
  P2P_TRANSFER = 2;
}

/*
go:generate gen_sql -types=Payment,PaymentMode,PaymentStatus,Metadata
This represents a payment made for an order
 */
message Payment {
  string id = 1;
  string order_id = 2;
  // Stores the execution identifier for the payment that is generated by the downstream service.
  // If the downstream service doesn't generate an identifier, then this will be blank.
  // For eg: In case of one time investments this would be the OMS order id
  string payment_id = 3;
  PaymentMode payment_mode = 4;
  string attempt_id = 5;
  PaymentStatus payment_status = 6;
  bool is_active = 7;
  google.type.Money amount = 8;
  Metadata metadata = 9;
  google.protobuf.Timestamp created_at = 10;
  google.protobuf.Timestamp updated_at = 11;
  google.protobuf.Timestamp deleted_at = 12;
  // payment protocol present in the transaction
  string txn_protocol = 13;
  // latest payment failure code present in the transaction. Only present if the payment was ever in 'FAILED' status
  string txn_failure_code = 14;
  // latest known utr number for this payment.
  // The authoritative source for utr should always be the transaction corresponding to this payment.
  // We are storing this at our end to simplify debugging and certain look up operations since this mostly remains static
  string utr_number = 15;
  // time stamp at which the payment transaction completed.
  google.protobuf.Timestamp txn_completed_at = 16;
}

/*
  Keeping it empty now. Can add values as needed later.
*/
message Metadata {
  // This is a unique identifier created by the downstream payment service
  string payment_execution_order_id = 1[deprecated=true];
}
