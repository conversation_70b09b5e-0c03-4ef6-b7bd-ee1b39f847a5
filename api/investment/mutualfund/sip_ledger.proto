syntax = "proto3";

package api.investment.mutualfund;

import "google/protobuf/timestamp.proto";
import "google/type/money.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund";

/*
go:generate gen_sql -types=SIPGranularity,SIPStatus
SIP ledger is responsible for mapping each actor to their respective SIPs.
For more details: https://docs.google.com/document/d/1wXrrQdUqeWGcOROXlkBqp1ilLexL2OWHaXxkno-ItnQ/edit#heading=h.idskzxou4vl2
*/
message SIPLedger {
  // Primary identifier to SIP Ledger database model.
  string id = 1;

  // Actor associated with the SIP
  string actor_id = 2;

  // Primary identifier to the mutual fund data model.
  string mutual_fund_id = 3;

  // Unique SIP registration number which would be generated whenever a new SIP has to be registered.
  // This is shared to the vendor in the forward feed file along with the installment number
  string sip_registration_number = 4;

  // Identifier for corresponding fit subscription mapped to this SIP.
  string fit_subscription_id = 5;

  // Amount to be invested for each installment in this SIP
  google.type.Money sip_amount = 6;

  // Granularity of the SIP
  SIPGranularity sip_granularity = 7;

  // Status of the SIP
  SIPStatus sip_status = 8;

  // Start date of the SIP
  google.protobuf.Timestamp start_date = 9;

  // End date of the SIP, by default we would be setting SIP for 10 years
  google.protobuf.Timestamp end_date = 10;

  // Total number of installments that can be made for this SIP
  int32 total_installments = 11;

  // Successful number of installments submitted to the vendor for this SIP
  int32 successful_installments = 12;

  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 13;
  google.protobuf.Timestamp updated_at = 14;
  google.protobuf.Timestamp deleted_at = 15;
}

enum SIPGranularity {
  SIP_GRANULARITY_UNSPECIFIED = 0;
  SIP_GRANULARITY_DAILY = 1;
  SIP_GRANULARITY_WEEKLY = 2;
  SIP_GRANULARITY_MONTHLY = 3;
}

enum SIPStatus {
  SIP_STATUS_UNSPECIFIED = 0;
  // Denotes the SIP is active and can be used for placing orders
  SIP_STATUS_ACTIVE = 1;
  // Denotes that the SIP is temporarily paused and cannot be used for placing orders
  // SIP can be moved to active from this state
  SIP_STATUS_INACTIVE = 2;
  // Denotes that the SIP is permanently cancelled and cannot be used for placing orders
  // We mark SIP cancelled only after Vendor confirmation of the same
  SIP_STATUS_CANCELLED = 3;
}

// SIPLedgerMask denotes the column field masks used during update
enum SIPLedgerMask {
  SIP_LEDGER_MASK_UNSPECIFIED = 0;
  SIP_AMOUNT = 1;
  SIP_GRANULARITY = 2;
  SIP_STATUS = 3;
  SIP_START_DATE = 4;
  SIP_END_DATE = 5;
  SIP_TOTAL_INSTALLMENTS = 6;
  SIP_SUCCESSFUL_INSTALLMENTS = 7;
  SIP_CREATED_AT = 8;
}
