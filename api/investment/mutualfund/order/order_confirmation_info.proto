syntax = "proto3";

package api.investment.mutualfund.order;

import "api/vendorgateway/vendor.proto";
import "api/investment/mutualfund/order/order.proto";
import "google/protobuf/timestamp.proto";
import "google/type/money.proto";
import "api/investment/mutualfund/mutual_fund.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/order";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund.order";

message OrderConfirmationInfo {
  string  id = 1;
  string order_id = 2;
  string rta_transaction_number = 3;
  vendorgateway.Vendor rta = 4;
  order.OrderConfirmationMetadata order_confirmation_metadata = 5;
  PaymentConfirmationMetadata payment_confirmation_metadata = 6;

  google.protobuf.Timestamp created_at = 7;
  google.protobuf.Timestamp updated_at = 8;
  google.protobuf.Timestamp deleted_at = 9;
}

message PaymentConfirmationMetadata {
  // trade_date is the nav date for which the transaction happened
  google.protobuf.Timestamp trade_date = 1;
  // order amount confirmed by the RTA
  google.type.Money amount = 2;
  // broker_dealer_code is an identifier given by the rta.
  string broker_dealer_code = 3;
  // dispatch_mode represents the payment mode like NEFT/RTGS/Electronic/Cheque.
  string dispatch_mode = 4;

  // mailed_date, dispatch_status and dispatch_reference are applicable if payment mode is cheque
  google.protobuf.Timestamp mailed_date = 5;
  string dispatch_status=6;
  string dispatch_reference = 7;

  // payout_description is a text that describes some remarks about the transaction.
  string payout_description = 8;

  // represents the payment instrument details
  string instrument_number = 9;
  google.protobuf.Timestamp instrument_date = 10;
  string instrument_bank = 11;
  string instrument_branch = 12;
  string min = 13;
  string payee_account_number = 14;
}

message OrderRejectionMetaData {
  // rejected_at represents whether rejection happened at BackOffice or FrontOffice
  string rejected_at = 1;
  string rejection_reason = 2;
  mutualfund.Amc amc = 3;
  string scheme_code = 4;
  string folio_id = 5;
}

enum OrderConfirmationInfoFieldMASK {
  ORDER_CONFIRMATION_INFO_MASK_UNSPECIFIED = 0;
  ORDER_CONFIRMATION_METADATA = 1;
  PAYMENT_CONFIRMATION_METADATA = 2;
}
