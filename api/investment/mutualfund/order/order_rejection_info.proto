syntax = "proto3";

package api.investment.mutualfund.order;

import "google/protobuf/timestamp.proto";

option go_package = "github.com/epifi/gamma/api/investment/mutualfund/order";
option java_package = "com.github.epifi.gamma.api.investment.mutualfund.order";

message OrderRejectionInfo {
  string  id = 1;
  string order_id = 2;
  string rejection_reason = 3;
  // Whether the order was rejected at front office or back office
  string rejected_at = 4;

  google.protobuf.Timestamp created_at = 5;
  google.protobuf.Timestamp updated_at = 6;
  google.protobuf.Timestamp deleted_at = 7;

}

enum OrderRejectionInfoFieldMask {
  ORDER_REJECTION_INFO_MASK_UNSPECIFIED = 0;
}
