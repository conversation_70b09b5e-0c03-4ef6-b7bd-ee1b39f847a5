syntax = "proto3";

package api.alfred;

option go_package = "github.com/epifi/gamma/api/alfred";
option java_package = "com.github.epifi.gamma.api.alfred";

// RequestBundleType can be used to filter out specific bundles to show via the GetRequestChoice fe rpc
// Request bundle is a fe construct, keeping the type here in BE so that client do not use this unintentionally
enum RequestBundleType {
  REQUEST_BUNDLE_TYPE_UNSPECIFIED = 0;
  REQUEST_BUNDLE_TYPE_CHEQUEBOOK = 1;
  REQUEST_BUNDLE_TYPE_ELSS_TAX_STATEMENTS = 2;
  REQUEST_BUNDLE_TYPE_PROFILE_UPDATE = 3;
  REQUEST_BUNDLE_TYPE_UPDATE_SIGNATURE = 4;
  REQUEST_BUNDLE_TYPE_USS_TAX_DOCUMENTS = 5;
  REQUEST_BUNDLE_TYPE_USS_DOCUMENTS = 6;
}

message RequestChoiceFilter {
  // list of request_bundle_type to be considered for generating request choices page result
  repeated RequestBundleType bundle_types = 1;
}
