syntax = "proto3";

package alfred;

import "api/typesv2/money.proto";
import "google/protobuf/timestamp.proto";
import "google/type/date.proto";

option go_package = "github.com/epifi/gamma/api/alfred";
option java_package = "com.github.epifi.gamma.api.alfred";

enum RequestType {
  REQUEST_TYPE_UNSPECIFIED = 0;

  REQUEST_TYPE_PROFILE_DOB_CHANGE = 100;
  REQUEST_TYPE_PROFILE_COMMUNICATION_ADDRESS_CHANGE = 101;

  REQUEST_TYPE_ORDER_CHEQUEBOOK = 200;
  REQUEST_TYPE_DOWNLOAD_DIGITAL_CANCELLED_CHEQUEBOOK = 2;
  REQUEST_TYPE_CHEQUEBOOK = 202;

  REQUEST_TYPE_TAX_STATEMENT_ELSS = 300;

  REQUEST_TYPE_SAVINGS_ACC_SIGNATURE_CHANGE = 400;

  REQUEST_TYPE_USS_TAX_DOCUMENTS_TIME_RANGE_SELECTION = 401;

  REQUEST_TYPE_SEND_USS_TAX_DOCUMENTS = 402;
  REQUEST_TYPE_USS_DOCUMENTS_CALENDER_YEAR_SELECTION = 403;
  REQUEST_TYPE_USS_DOCUMENTS_MONTH_SELECTION = 404;
  REQUEST_TYPE_SEND_USS_MONTHLY_STATEMENTS = 405;
}

// CategoryType is an identifier to be sent in AlfredSummaryRequestOptions
// Category type is mapped to one or more requestTypes in GetFilteredRequestSummaries API to get request histories
// eg. REQUEST_TYPE_ORDER_CHEQUEBOOK, REQUEST_TYPE_CANCELLED_CHEQUEBOOK will corresponds to CATEGORY_TYPE_CHEQUEBOOK
enum CategoryType {
  CATEGORY_TYPE_UNSPECIFIED = 0;
  CATEGORY_TYPE_CHEQUEBOOK = 1;
  CATEGORY_TYPE_ADDRESS_UPDATE = 2;
}

enum Stage {
  STAGE_UNSPECIFIED = 0;
  // STAGE_ELIGIBILITY_CHECKS = 1;
  // STAGE_USER_INPUTS = 2;
  // STAGE_PROCESSING = 3;
}

enum Status {
  STATUS_UNSPECIFIED = 0;
  STATUS_IN_PROGRESS = 1;
  STATUS_SUCCESS = 2;
  STATUS_FAILED = 3;
  STATUS_STUCK = 4;
  STATUS_CREATED = 5;
}

enum Reason {
  REASON_UNSPECIFIED = 0;
}

message StageStatus {
  Stage stage = 1;
  Status status = 2;
  Reason reason = 3;
}

message Details {
  oneof Metadata {
    ChequebookMetadata ChequebookMetadata = 1;
    TaxStatementElssMetadata tax_statement_elss_metadata = 2;
    ProfileUpdateMetadata profile_update_metadata = 3;
  }
  oneof UserInput {
    ProfileUpdateUserInput profile_update_user_input = 4;
  }
}

message ProfileUpdateUserInput {
  // User input date of birth
  google.type.Date dob = 1;
  // dob last captured timestamp used in handling retry logic for matching user input DOB with KYC data
  google.protobuf.Timestamp dob_last_updated_at = 2;
  // counter to track number of attempts made by user to input DOB which matches with KYC data
  // counter resets based on a timer logic on backend
  int32 dob_attempt_number = 3;
}

message ChequebookMetadata {
  google.protobuf.Timestamp ordered_at = 1;
  string couriered_via = 2;
  string tracking_id = 3;
  api.typesv2.Money charges = 4;
  uint32 cheque_leaves_count = 5;
  string tracking_url = 6;
}

message TaxStatementElssMetadata {
  google.type.Date start_date = 1;
  google.type.Date end_date = 2;
}
message ProfileUpdateMetadata {
  enum FailureReason {
    FAILURE_REASON_UNSPECIFIED = 0;
    FAILURE_REASON_NO_USER_CONSENT = 1;
    FAILURE_REASON_VENDOR_ISSUE = 2;
    // based on previous failure attempts
    FAILURE_REASON_FIELD_UPDATE_NOT_ALLOWED = 3;
    FAILURE_REASON_DOB_MAX_ATTEMPTS = 4;
    FAILURE_REASON_ADDRESS_MAX_ATTEMPTS = 5;
    FAILURE_REASON_FIELD_PHOTO_MISMATCH = 6;
    FAILURE_REASON_FIELD_NAME_MISMATCH = 7;
    FAILURE_REASON_NO_FIELD_TO_UPDATE = 8;
    FAILURE_REASON_KYC_EXPIRED = 9;
    // KYC DOB mismatch with user given DOB
    FAILURE_REASON_DOB_MISMATCH = 10;
    // DOB in user data is same with KYC DOB in DOB change request
    FAILURE_REASON_NO_CHANGE_IN_DOB = 11;
    FAILURE_REASON_MAX_FM_ATTEMPTS = 12;
    FAILURE_REASON_ANOTHER_REQUEST_IN_PROGRESS = 13;
  }
  FailureReason failure_reason = 1;
  // Fields which are getting updated
  repeated ProfileField profile_fields = 2;
  string kyc_attempt_id = 3;
  // Facematch attempt ID
  string fm_attempt_id = 4;
  // Consent request ID
  string consent_request_id = 5;
  // Facematch attempt number
  int32 fm_attempt_number = 6;
  google.protobuf.Timestamp vendor_request_started_at = 9;
}

enum ProfileField {
  PROFILE_FIELD_UNSPECIFIED = 0;
  PROFILE_FIELD_NAME = 1;
  PROFILE_FIELD_PHOTO = 2;
  PROFILE_FIELD_DOB = 3;
  PROFILE_FIELD_COMMUNICATION_ADDRESS = 4;
}

enum ProfileUpdateStage {
  PROFILE_UPDATE_STAGE_UNSPECIFIED = 0;
  PROFILE_UPDATE_STAGE_GET_KYC = 1;
  PROFILE_UPDATE_STAGE_UPDATE_AT_BANK = 2;
  PROFILE_UPDATE_STAGE_UPDATE_COMPLETE = 3;
}

message ServiceRequest {
  string id = 1;
  // identifier for the user raising the request
  string actor_id = 2;
  // type of request raised by the user
  RequestType request_type = 3;
  // overall status on the request raised
  Status status = 4;
  // details associated with request processing, including stage wise statuses
  Details details = 5;

  // Standard timestamp fields
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
  int64 deleted_at_unix = 8;
}

enum ServiceRequestFieldMask {
  SERVICE_REQUEST_FIELD_MASK_UNSPECIFIED = 0;
  SERVICE_REQUEST_FIELD_MASK_STATUS = 1;
  SERVICE_REQUEST_FIELD_MASK_DETAILS = 2;
  SERVICE_REQUEST_FIELD_MASK_METADATA = 3;
  SERVICE_REQUEST_FIELD_MASK_DELETED_AT_UNIX = 4;
  SERVICE_REQUEST_FIELD_MASK_USER_INPUT = 5;
}

enum SortOrder {
  SORT_ORDER_UNSPECIFIED = 0;
  SORT_ORDER_DESC = 1;
  SORT_ORDER_ASC = 2;
}

message Filters {
  string actor_id = 1;
  repeated RequestType request_types = 2;
  // status of service request
  repeated Status status_list = 3;
}
