/**
 * SchemaGenerator: Proto Json Schema Generator
 * @params target output
 * target: target directory for the compiled json files /api/cx
 * output: output file path for the schema json file /grpcMethod.json
 **/

const fs = require('fs');
const path = require('path');
const glob = require('glob');

const finalJson = {};

const rootDir = process.argv[2];
const outputFile = process.argv[3];

function getProtoFiles(dir) {
	return new Promise((resolve, reject) => {
		glob(`${dir}/**/service.json`, (err, files) => {
			if (err) {
				reject(err);
			} else {
				resolve(files);
			}
		});
	});
}

function readJSON(filePath) {
	return JSON.parse(fs.readFileSync(filePath, 'utf8'));
}

function writeJSON(filePath, data) {
	const dir = path.dirname(filePath); // Get the directory path
	fs.mkdirSync(dir, { recursive: true }); // Ensure the directory exists
	fs.writeFileSync(filePath, JSON.stringify(data, null, 2), 'utf8'); // Write the JSON data to the file
}

(async () => {
	try {
		const protoFiles = await getProtoFiles(rootDir);
		protoFiles.forEach(protoFile => {
			const json = readJSON(protoFile);
			const services = json.services.reduce((acc, service) => {
				const methods = service.methods.reduce((methodAcc, method) => {
					methodAcc[method.name] = {
						input: {
							name: method.input_type,
						},
						output: {
							name: method.output_type,
						},
						options: method.options,
					};
					return methodAcc;
				}, {});

				acc[service.name] = methods;
				return acc;
			}, {});
			const parts = protoFile.split('/');
			const extractedPath = parts.slice(3, parts.length - 1).join('/');
			finalJson[json.package] = { ...services, protoPath: extractedPath };
		});
		writeJSON(outputFile, finalJson);
		console.log('All files processed.');
	} catch (err) {
		console.error('Error:', err);
	}
})();
