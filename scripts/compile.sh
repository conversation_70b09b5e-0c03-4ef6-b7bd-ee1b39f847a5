#!/bin/bash
set -e

compile_protos_go() {
	target=$2
#   TODO(sakthi) identify dependency graph [files importing modified files] and compile only the required protos
#	if [ ! -z "$PR_NUMBER" ]; then
#    	filepath=$(gh pr diff $PR_NUMBER --name-only | grep -e ".*\.proto")
#	el
	if [[ "$target" == *.proto ]]; then
		filepath=./api/${target}
	else
	  	filepath=$(find ./api/${target}/ -iname "*.proto")
	fi
	echo "Compiling protos..."
	set +e # ignore exit code of protoc command and handle it through status check and validate warnings
	result=$(protoc \
		-I . \
		-I ${GO_PROTO_OUTPUT_ROOT} \
		-I ${GOPATH}/pkg/mod/ \
		-I ${ENVOY_PROTO_VALIDATE_PATH} \
		-I ${GOOGLE_API_PROTO_PATH} \
		--go-grpc_out=require_unimplemented_servers=false:${GO_PROTO_OUTPUT_ROOT} \
		--go_out=${GO_PROTO_OUTPUT_ROOT} \
		--validate_out="lang=go:${GO_PROTO_OUTPUT_ROOT}" \
		--grpc-gateway_out=logtostderr=true:${GO_PROTO_OUTPUT_ROOT} \
		$filepath 2>&1)
	status=$?
	set -e
	echo "$result"
	if [ $status -ne 0 ]; then
		echo "Error: Compilation failed"
		exit 1
	fi

	unused_imports=$(echo "$result" | grep -E "\.proto:[0-9]+:[0-9]+: warning: Import .* is unused" || true)
	if [ ! -z "$unused_imports" ]; then
		echo "Error: Unused imports found in the proto files. Please remove them."
		exit 1
	fi
	echo "Compilation successful"
}

generate_service_mocks_go() {
	target=$2
	if [[ "$target" == *"/"* ]]; then
	   target_service=$(echo "$target" | cut -d '/' -f1)
	   rest_path=$(echo "$target" | cut -d '/' -f2-)
	else
	   target_service="$target"
	   rest_path=""
	fi
    # TODO(shafi) find the correct folder path from go options in proto file
	case "$target_service" in
	"inappreferrals")
		target_service="inappreferral"
		;;
	"payment_instruments")
		target_service="paymentinstrument"
		;;
	esac

	if [ -z "$rest_path" ]; then
	target="${target_service}"
	else
	target="${target_service}/${rest_path}"
	fi

	#find the repo name based on go_package in the target directory
	REPO_NAME=$(grep -r -m 1 "option go_package = \"github.com/epifi/" ./api/${target} | head -n 1 | awk '{print $4}' | cut -d '/' -f 3)

	API_PATH="${GO_PROTO_OUTPUT_ROOT}/github.com/epifi/${REPO_NAME}/api"
	target_dirs="${API_PATH}/${target}"

	if [ ! -z "$PR_NUMBER" ]; then
		REPO_PATH=$(dirname API_PATH)
        target_dirs=$(gh pr diff $PR_NUMBER --name-only | xargs -n 1 dirname | uniq | xargs -n 1 -I % find % -maxdepth 1 -name '*.proto' | xargs -n 1 dirname | uniq | xargs -n 1 -I % echo ${REPO_PATH}/%/ | tr '\n' ' ')
	elif [ ! -d "${API_PATH}/${target}" ]; then
	  if [[ "$target" == *.proto ]]; then
        target_dirs=${API_PATH}"/`dirname ${target}`"
      else
		echo "Error: Directory ${API_PATH}/${target} does not exist." >&2
		exit 1
	  fi
	elif [ "${SERVICE_TARGET}" == "/" ]; then
		target_dirs=${API_PATH}
	fi
	echo "Generating mocks of services in ${API_PATH}${SERVICE_TARGET} for target dirs: $target_dirs"
	for target_dir in $(echo $target_dirs);
	do
	# not using the dirs list directly in the find command as it reports "too long" error for large number of dirs
		find "$target_dir" -iname "*_grpc.pb.go" | while read -r line;
		do
			directory_path="`dirname "$line"`"
			file_name="`basename "$line"`"
			dest_file_name="`echo "$file_name" | cut -d '.' -f 1`_mocks.go"
            path_after_api="$(echo "${directory_path}" | sed "s?${API_PATH}/??g" | sed "s?^/??")"
			package_name="mocks"
			cd "${API_PATH}" && cd .. && mockgen -source="api/${path_after_api}/${file_name}" -destination="${directory_path}/mocks/${dest_file_name}" -package="${package_name}"
		done
	done
}

case $1 in
compile_protos_go)
	compile_protos_go $@
	;;
generate_service_mocks_go)
	generate_service_mocks_go $@
	;;
esac
